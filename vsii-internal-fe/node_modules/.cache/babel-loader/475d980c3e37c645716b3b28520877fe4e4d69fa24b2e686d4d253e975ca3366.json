{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/ExpiredAccount.tsx\";\nimport { Card, CardContent, CardMedia, Grid, Typography } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { FormattedMessage } from 'react-intl';\nimport imageBackground from 'assets/images/maintenance/img-error-bg.svg';\nimport imagePurple from 'assets/images/maintenance/img-error-purple.svg';\nimport imageBlue from 'assets/images/maintenance/img-error-blue.svg';\nimport imageText from 'assets/images/maintenance/img-error-text.svg';\nimport { gridSpacing } from 'store/constant';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CardMediaWrapper = styled('div')({\n  maxWidth: 720,\n  margin: '0 auto',\n  position: 'relative'\n});\n_c = CardMediaWrapper;\nconst ErrorWrapper = styled('div')({\n  maxWidth: 350,\n  margin: '0 auto',\n  textAlign: 'center'\n});\n_c2 = ErrorWrapper;\nconst ErrorCard = styled(Card)({\n  minHeight: '100vh',\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center'\n});\n_c3 = ErrorCard;\nconst CardMediaBlock = styled('img')({\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  width: '100%',\n  animation: '3s bounce ease-in-out infinite'\n});\n_c4 = CardMediaBlock;\nconst CardMediaBlue = styled('img')({\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  width: '100%',\n  animation: '15s wings ease-in-out infinite'\n});\n_c5 = CardMediaBlue;\nconst CardMediaPurple = styled('img')({\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  width: '100%',\n  animation: '12s wings ease-in-out infinite'\n});\n_c6 = CardMediaPurple;\nconst ExpiredAccount = () => {\n  return /*#__PURE__*/_jsxDEV(ErrorCard, {\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        justifyContent: \"center\",\n        spacing: gridSpacing,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(CardMediaWrapper, {\n            children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n              component: \"img\",\n              image: imageBackground,\n              title: \"Slider5 image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(CardMediaBlock, {\n              src: imageText,\n              title: \"Slider 1 image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(CardMediaBlue, {\n              src: imageBlue,\n              title: \"Slider 2 image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(CardMediaPurple, {\n              src: imagePurple,\n              title: \"Slider 3 image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(ErrorWrapper, {\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: gridSpacing,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h1\",\n                  component: \"div\",\n                  children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n                    id: \"something-is-wrong\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 72,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n                    id: \"error-expired-account-screen-centent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 77,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 76,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 9\n  }, this);\n};\n_c7 = ExpiredAccount;\nexport default ExpiredAccount;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"CardMediaWrapper\");\n$RefreshReg$(_c2, \"ErrorWrapper\");\n$RefreshReg$(_c3, \"ErrorCard\");\n$RefreshReg$(_c4, \"CardMediaBlock\");\n$RefreshReg$(_c5, \"CardMediaBlue\");\n$RefreshReg$(_c6, \"CardMediaPurple\");\n$RefreshReg$(_c7, \"ExpiredAccount\");", "map": {"version": 3, "names": ["Card", "<PERSON><PERSON><PERSON><PERSON>", "CardMedia", "Grid", "Typography", "styled", "FormattedMessage", "imageBackground", "imagePurple", "imageBlue", "imageText", "gridSpacing", "jsxDEV", "_jsxDEV", "CardMediaWrapper", "max<PERSON><PERSON><PERSON>", "margin", "position", "_c", "ErrorWrapper", "textAlign", "_c2", "ErrorCard", "minHeight", "display", "alignItems", "justifyContent", "_c3", "CardMediaBlock", "top", "left", "width", "animation", "_c4", "CardMediaBlue", "_c5", "CardMediaPurple", "_c6", "ExpiredAccount", "children", "container", "spacing", "item", "xs", "component", "image", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "variant", "id", "_c7", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/ExpiredAccount.tsx"], "sourcesContent": ["import { Card, CardContent, CardMedia, Grid, Typography } from '@mui/material';\nimport { styled } from '@mui/material/styles';\nimport { FormattedMessage } from 'react-intl';\n\nimport imageBackground from 'assets/images/maintenance/img-error-bg.svg';\nimport imagePurple from 'assets/images/maintenance/img-error-purple.svg';\nimport imageBlue from 'assets/images/maintenance/img-error-blue.svg';\nimport imageText from 'assets/images/maintenance/img-error-text.svg';\nimport { gridSpacing } from 'store/constant';\n\nconst CardMediaWrapper = styled('div')({\n    maxWidth: 720,\n    margin: '0 auto',\n    position: 'relative'\n});\n\nconst ErrorWrapper = styled('div')({\n    maxWidth: 350,\n    margin: '0 auto',\n    textAlign: 'center'\n});\n\nconst ErrorCard = styled(Card)({\n    minHeight: '100vh',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n});\n\nconst CardMediaBlock = styled('img')({\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    animation: '3s bounce ease-in-out infinite'\n});\n\nconst CardMediaBlue = styled('img')({\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    animation: '15s wings ease-in-out infinite'\n});\n\nconst CardMediaPurple = styled('img')({\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    animation: '12s wings ease-in-out infinite'\n});\n\nconst ExpiredAccount = () => {\n    return (\n        <ErrorCard>\n            <CardContent>\n                <Grid container justifyContent=\"center\" spacing={gridSpacing}>\n                    <Grid item xs={12}>\n                        <CardMediaWrapper>\n                            <CardMedia component=\"img\" image={imageBackground} title=\"Slider5 image\" />\n                            <CardMediaBlock src={imageText} title=\"Slider 1 image\" />\n                            <CardMediaBlue src={imageBlue} title=\"Slider 2 image\" />\n                            <CardMediaPurple src={imagePurple} title=\"Slider 3 image\" />\n                        </CardMediaWrapper>\n                    </Grid>\n                    <Grid item xs={12}>\n                        <ErrorWrapper>\n                            <Grid container spacing={gridSpacing}>\n                                <Grid item xs={12}>\n                                    <Typography variant=\"h1\" component=\"div\">\n                                        <FormattedMessage id=\"something-is-wrong\" />\n                                    </Typography>\n                                </Grid>\n                                <Grid item xs={12}>\n                                    <Typography variant=\"body2\">\n                                        <FormattedMessage id=\"error-expired-account-screen-centent\" />\n                                    </Typography>\n                                </Grid>\n                            </Grid>\n                        </ErrorWrapper>\n                    </Grid>\n                </Grid>\n            </CardContent>\n        </ErrorCard>\n    );\n};\n\nexport default ExpiredAccount;\n"], "mappings": ";AAAA,SAASA,IAAI,EAAEC,WAAW,EAAEC,SAAS,EAAEC,IAAI,EAAEC,UAAU,QAAQ,eAAe;AAC9E,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,gBAAgB,QAAQ,YAAY;AAE7C,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,WAAW,MAAM,gDAAgD;AACxE,OAAOC,SAAS,MAAM,8CAA8C;AACpE,OAAOC,SAAS,MAAM,8CAA8C;AACpE,SAASC,WAAW,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,gBAAgB,GAAGT,MAAM,CAAC,KAAK,CAAC,CAAC;EACnCU,QAAQ,EAAE,GAAG;EACbC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE;AACd,CAAC,CAAC;AAACC,EAAA,GAJGJ,gBAAgB;AAMtB,MAAMK,YAAY,GAAGd,MAAM,CAAC,KAAK,CAAC,CAAC;EAC/BU,QAAQ,EAAE,GAAG;EACbC,MAAM,EAAE,QAAQ;EAChBI,SAAS,EAAE;AACf,CAAC,CAAC;AAACC,GAAA,GAJGF,YAAY;AAMlB,MAAMG,SAAS,GAAGjB,MAAM,CAACL,IAAI,CAAC,CAAC;EAC3BuB,SAAS,EAAE,OAAO;EAClBC,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBC,cAAc,EAAE;AACpB,CAAC,CAAC;AAACC,GAAA,GALGL,SAAS;AAOf,MAAMM,cAAc,GAAGvB,MAAM,CAAC,KAAK,CAAC,CAAC;EACjCY,QAAQ,EAAE,UAAU;EACpBY,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,MAAM;EACbC,SAAS,EAAE;AACf,CAAC,CAAC;AAACC,GAAA,GANGL,cAAc;AAQpB,MAAMM,aAAa,GAAG7B,MAAM,CAAC,KAAK,CAAC,CAAC;EAChCY,QAAQ,EAAE,UAAU;EACpBY,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,MAAM;EACbC,SAAS,EAAE;AACf,CAAC,CAAC;AAACG,GAAA,GANGD,aAAa;AAQnB,MAAME,eAAe,GAAG/B,MAAM,CAAC,KAAK,CAAC,CAAC;EAClCY,QAAQ,EAAE,UAAU;EACpBY,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,MAAM;EACbC,SAAS,EAAE;AACf,CAAC,CAAC;AAACK,GAAA,GANGD,eAAe;AAQrB,MAAME,cAAc,GAAGA,CAAA,KAAM;EACzB,oBACIzB,OAAA,CAACS,SAAS;IAAAiB,QAAA,eACN1B,OAAA,CAACZ,WAAW;MAAAsC,QAAA,eACR1B,OAAA,CAACV,IAAI;QAACqC,SAAS;QAACd,cAAc,EAAC,QAAQ;QAACe,OAAO,EAAE9B,WAAY;QAAA4B,QAAA,gBACzD1B,OAAA,CAACV,IAAI;UAACuC,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAJ,QAAA,eACd1B,OAAA,CAACC,gBAAgB;YAAAyB,QAAA,gBACb1B,OAAA,CAACX,SAAS;cAAC0C,SAAS,EAAC,KAAK;cAACC,KAAK,EAAEtC,eAAgB;cAACuC,KAAK,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3ErC,OAAA,CAACe,cAAc;cAACuB,GAAG,EAAEzC,SAAU;cAACoC,KAAK,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzDrC,OAAA,CAACqB,aAAa;cAACiB,GAAG,EAAE1C,SAAU;cAACqC,KAAK,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxDrC,OAAA,CAACuB,eAAe;cAACe,GAAG,EAAE3C,WAAY;cAACsC,KAAK,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACPrC,OAAA,CAACV,IAAI;UAACuC,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAJ,QAAA,eACd1B,OAAA,CAACM,YAAY;YAAAoB,QAAA,eACT1B,OAAA,CAACV,IAAI;cAACqC,SAAS;cAACC,OAAO,EAAE9B,WAAY;cAAA4B,QAAA,gBACjC1B,OAAA,CAACV,IAAI;gBAACuC,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAJ,QAAA,eACd1B,OAAA,CAACT,UAAU;kBAACgD,OAAO,EAAC,IAAI;kBAACR,SAAS,EAAC,KAAK;kBAAAL,QAAA,eACpC1B,OAAA,CAACP,gBAAgB;oBAAC+C,EAAE,EAAC;kBAAoB;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACPrC,OAAA,CAACV,IAAI;gBAACuC,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAJ,QAAA,eACd1B,OAAA,CAACT,UAAU;kBAACgD,OAAO,EAAC,OAAO;kBAAAb,QAAA,eACvB1B,OAAA,CAACP,gBAAgB;oBAAC+C,EAAE,EAAC;kBAAsC;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEpB,CAAC;AAACI,GAAA,GAjCIhB,cAAc;AAmCpB,eAAeA,cAAc;AAAC,IAAApB,EAAA,EAAAG,GAAA,EAAAM,GAAA,EAAAM,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAiB,GAAA;AAAAC,YAAA,CAAArC,EAAA;AAAAqC,YAAA,CAAAlC,GAAA;AAAAkC,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAApB,GAAA;AAAAoB,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}