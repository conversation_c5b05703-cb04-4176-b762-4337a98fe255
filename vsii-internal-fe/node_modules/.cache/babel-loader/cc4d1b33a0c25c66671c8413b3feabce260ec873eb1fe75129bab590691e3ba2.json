{"ast": null, "code": "import { isString, isNumber } from '@motionone/utils';\nconst namedEdges = {\n  start: 0,\n  center: 0.5,\n  end: 1\n};\nfunction resolveEdge(edge, length) {\n  let inset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n  let delta = 0;\n  /**\n   * If we have this edge defined as a preset, replace the definition\n   * with the numerical value.\n   */\n  if (namedEdges[edge] !== undefined) {\n    edge = namedEdges[edge];\n  }\n  /**\n   * Handle unit values\n   */\n  if (isString(edge)) {\n    const asNumber = parseFloat(edge);\n    if (edge.endsWith(\"px\")) {\n      delta = asNumber;\n    } else if (edge.endsWith(\"%\")) {\n      edge = asNumber / 100;\n    } else if (edge.endsWith(\"vw\")) {\n      delta = asNumber / 100 * document.documentElement.clientWidth;\n    } else if (edge.endsWith(\"vh\")) {\n      delta = asNumber / 100 * document.documentElement.clientHeight;\n    } else {\n      edge = asNumber;\n    }\n  }\n  /**\n   * If the edge is defined as a number, handle as a progress value.\n   */\n  if (isNumber(edge)) {\n    delta = length * edge;\n  }\n  return inset + delta;\n}\nexport { namedEdges, resolveEdge };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}