{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"value\", \"components\", \"componentsProps\", \"component\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useSlotProps } from '../utils';\nimport composeClasses from '../composeClasses';\nimport { getTabPanelUnstyledUtilityClass } from './tabPanelUnstyledClasses';\nimport useTabPanel from './useTabPanel';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    hidden\n  } = ownerState;\n  const slots = {\n    root: ['root', hidden && 'hidden']\n  };\n  return composeClasses(slots, getTabPanelUnstyledUtilityClass, {});\n};\n/**\n *\n * Demos:\n *\n * - [Unstyled tabs](https://mui.com/base/react-tabs/)\n *\n * API:\n *\n * - [TabPanelUnstyled API](https://mui.com/base/api/tab-panel-unstyled/)\n */\n\nconst TabPanelUnstyled = /*#__PURE__*/React.forwardRef(function TabPanelUnstyled(props, ref) {\n  var _ref;\n  const {\n      children,\n      components = {},\n      componentsProps = {},\n      component\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    hidden,\n    getRootProps\n  } = useTabPanel(props);\n  const ownerState = _extends({}, props, {\n    hidden\n  });\n  const classes = useUtilityClasses(ownerState);\n  const TabPanelRoot = (_ref = component != null ? component : components.Root) != null ? _ref : 'div';\n  const tabPanelRootProps = useSlotProps({\n    elementType: TabPanelRoot,\n    getSlotProps: getRootProps,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      role: 'tabpanel',\n      ref\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(TabPanelRoot, _extends({}, tabPanelRootProps, {\n    children: !hidden && children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TabPanelUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the TabPanel.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The props used for each slot inside the TabPanel.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The value of the TabPanel. It will be shown when the Tab with the corresponding value is selected.\n   */\n  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired\n} : void 0;\nexport default TabPanelUnstyled;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}