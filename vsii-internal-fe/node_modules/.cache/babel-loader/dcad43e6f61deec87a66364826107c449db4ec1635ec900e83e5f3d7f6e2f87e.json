{"ast": null, "code": "import React,{memo,useEffect,useState}from'react';// project imports\nimport MainCard from'components/cards/MainCard';import{Table,TableFooter}from'components/extended/Table';import{TabPanel}from'components/extended/Tabs';import Api from'constants/Api';import{SEARCH_PARAM_KEY,TEXT_CONFIG_SCREEN,paginationParamDefault,paginationResponseDefault,saleMonitorBiddingPackageTabs}from'constants/Common';import{PERMISSIONS}from'constants/Permission';import{TabCustom,TableToolbar}from'containers';import{useAppDispatch}from'app/hooks';import{AddOrEditBiddingReport,BiddingReportSearch,BiddingReportTBody,BiddingReportThead,BiddingTrackingSearch,BiddingTrackingTBody,BiddingTrackingThead,Synchronize}from'containers/sales';import{FilterCollapse}from'containers/search';import{useSearchParams}from'react-router-dom';import sendRequest from'services/ApiService';import{openSnackbar}from'store/slice/snackbarSlice';import{checkAllowedPermission,checkAllowedTab}from'utils/authorization';import{downloadDocument,getSearchParam,transformObject}from'utils/common';import{addOrEditBiddingReportFormDefault,monitorBiddingPackagesFilterConfig}from'./Config';import pick from'lodash/pick';import{Box}from'@mui/material';// ==============================|| Monitor Bidding Packages ||============================== //\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const MonitorBiddingPackages=/*#__PURE__*/memo(()=>{// URL Params\nconst[searchParams,setSearchParams]=useSearchParams();const keyParams=[SEARCH_PARAM_KEY.tab,SEARCH_PARAM_KEY.page,SEARCH_PARAM_KEY.size,// ====== tab 0 - Bidding tracking ======\nSEARCH_PARAM_KEY.type,SEARCH_PARAM_KEY.packageName,SEARCH_PARAM_KEY.address,// ====== tab 1 - Bidding Report ======\nSEARCH_PARAM_KEY.type,SEARCH_PARAM_KEY.biddingPackagesName,SEARCH_PARAM_KEY.status,SEARCH_PARAM_KEY.address];const params=getSearchParam(keyParams,searchParams);transformObject(params);// delete unnecessary key value\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst{fullname,...cloneParams}=params;// Hooks, State, Variable\nconst defaultConditions={...monitorBiddingPackagesFilterConfig,...cloneParams};const{monitorBiddingPackages}=TEXT_CONFIG_SCREEN.salesReport;const dispatch=useAppDispatch();const[open,setOpen]=useState(false);const[openSynchronize,setOpenSynchronize]=useState(false);const[isEdit,setIsEdit]=useState(false);const[loading,setLoading]=useState(false);const[synchronizeLoading,setSynchronizeLoading]=useState(false);const[biddingTracking,setBiddingTracking]=useState([]);const[biddingReport,setBiddingReport]=useState([]);const[detailBiddingReport,setDetailBiddingReport]=useState(addOrEditBiddingReportFormDefault);const[conditions,setConditions]=useState(defaultConditions);const[formReset,setFormReset]=useState(defaultConditions);const[tabValue,setTabValue]=useState(checkAllowedTab(saleMonitorBiddingPackageTabs,params.tab)[0]);const[paginationResponse,setPaginationResponse]=useState({...paginationResponseDefault,pageNumber:params.page?params.page:paginationResponseDefault.pageNumber,pageSize:params.size?params.size:paginationResponseDefault.pageSize});const[addOrEditLoading,setAddOrEditLoading]=useState(false);const{monitorBiddingPackage}=PERMISSIONS.sale;// Function\nconst getDataTable=async tabNumber=>{setLoading(true);const response=await sendRequest(tabValue===0?Api.monitor_bidding.getBiddingReport:Api.monitor_bidding.getBiddingTracking,{...conditions,page:conditions.page+1});if(response){const{status,result}=response;if(status){const{content,pagination}=result;tabValue===0?setBiddingReport(content):setBiddingTracking(content);setPaginationResponse({...paginationResponse,totalElement:pagination.totalElement});setLoading(false);}else{setDataEmpty();}return;}else{setDataEmpty();}};const setDataEmpty=()=>{setBiddingTracking([]);setBiddingReport([]);setLoading(false);};// Event\nconst handleChangeTab=(event,newTabValue)=>{setTabValue(newTabValue);setConditions({...monitorBiddingPackagesFilterConfig});setFormReset({...monitorBiddingPackagesFilterConfig});setSearchParams({tab:newTabValue});};const handleChangePage=(event,newPage)=>{setConditions({...conditions,page:newPage});setSearchParams({...params,page:newPage});};const handleChangeRowsPerPage=event=>{setConditions({...conditions,page:paginationParamDefault.page,size:parseInt(event.target.value,10)});setSearchParams({...params,page:paginationParamDefault.page,size:parseInt(event.target.value,10)});};const handleOpenDialog=item=>{setIsEdit(item?true:false);setDetailBiddingReport(item?{...item}:addOrEditBiddingReportFormDefault);setOpen(true);};const handleCloseDialog=()=>{setOpen(false);};//Call API Bidding Report\n// TODO\nconst postAddOrEditBiddingReport=async(value,idHexString)=>{try{setAddOrEditLoading(true);const response=await sendRequest(isEdit?Api.monitor_bidding.updateBiddingReportpost(idHexString):Api.monitor_bidding.createOrUpdateBiddingReportpost,value);if(response.status){const message=isEdit?'update-success':'add-success';dispatch(openSnackbar({open:true,message,variant:'alert',alert:{color:'success'}}));getDataTable(tabValue);setOpen(false);}else{dispatch(openSnackbar({open:true,message:response.result.content,variant:'alert',alert:{color:'error'}}));}}finally{setAddOrEditLoading(false);}};const postSynchronize=async token=>{try{setSynchronizeLoading(true);const response=await sendRequest(Api.monitor_bidding.postSynchronize(token));if(response!==null&&response!==void 0&&response.status){dispatch(openSnackbar({open:true,message:'Synchronize successfully!',variant:'alert',alert:{color:'success'}}));setOpenSynchronize(false);getDataTable();}else{var _response$result,_response$result$cont;dispatch(openSnackbar({open:true,message:(response===null||response===void 0?void 0:(_response$result=response.result)===null||_response$result===void 0?void 0:(_response$result$cont=_response$result.content)===null||_response$result$cont===void 0?void 0:_response$result$cont.message)||'Synchronize failed!',variant:'alert',alert:{color:'error'}}));}}finally{setSynchronizeLoading(false);}};const hanldeAddBiddingReport=value=>{postAddOrEditBiddingReport(value);};const hanldeEditBiddingReport=(value,idHexString)=>{postAddOrEditBiddingReport(value,idHexString);};const handleExportDocument=async params=>{const response=await sendRequest(Api.monitor_bidding.getDownload,params);if(response){downloadDocument('MONITOR_BIDDING_REPORT.xlsx',response);}};const handleSynchronize=()=>{setOpenSynchronize(true);};const handlePostSynchronize=token=>{postSynchronize(token);};const handleCloseSynchronize=()=>{setOpenSynchronize(false);};// Handle submit\nconst handleBiddingTrackingSearch=value=>{const biddingTrackingSearch={...value,tab:tabValue};transformObject(biddingTrackingSearch);setSearchParams(biddingTrackingSearch);setConditions({...conditions,...value,page:paginationParamDefault.page});};const handleBiddingReportSearch=value=>{const biddingReportSearch={...value,tab:tabValue};transformObject(biddingReportSearch);setSearchParams(biddingReportSearch);setConditions({...conditions,...value,page:paginationParamDefault.page});};// Effect\nuseEffect(()=>{getDataTable(tabValue);// eslint-disable-next-line react-hooks/exhaustive-deps\n},[tabValue,conditions]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(TabCustom,{value:tabValue,handleChange:handleChangeTab,tabs:saleMonitorBiddingPackageTabs}),/*#__PURE__*/_jsxs(FilterCollapse,{handleSynchronize:checkAllowedPermission(monitorBiddingPackage.synchronizePackagesBiddingTracking)&&tabValue===0?undefined:handleSynchronize,handleExport:checkAllowedPermission(monitorBiddingPackage.downloadPackagesBiddingtracking)?()=>handleExportDocument(pick(conditions,['address','biddingPackagesName','status','type'])):undefined,syncLabel:monitorBiddingPackages+'tracking-synchronize',downloadLabel:monitorBiddingPackages+'tracking-download',children:[/*#__PURE__*/_jsx(TabPanel,{value:tabValue,index:0,children:/*#__PURE__*/_jsx(BiddingReportSearch,{formReset:formReset,handleSearch:handleBiddingReportSearch})}),/*#__PURE__*/_jsx(TabPanel,{value:tabValue,index:1,children:/*#__PURE__*/_jsx(BiddingTrackingSearch,{formReset:formReset,handleSearch:handleBiddingTrackingSearch})})]}),/*#__PURE__*/_jsxs(MainCard,{children:[/*#__PURE__*/_jsxs(TabPanel,{value:tabValue,index:0,children:[checkAllowedPermission(monitorBiddingPackage.add)&&/*#__PURE__*/_jsx(TableToolbar,{handleOpen:handleOpenDialog,handleRefreshData:getDataTable}),/*#__PURE__*/_jsx(Box,{sx:{width:'100%',overflowX:'auto'},children:/*#__PURE__*/_jsx(Table,{heads:/*#__PURE__*/_jsx(BiddingReportThead,{}),isLoading:loading,data:biddingReport,sx:{minWidth:'1310px'},children:/*#__PURE__*/_jsx(BiddingReportTBody,{pageNumber:conditions.page,pageSize:conditions.size,biddingReport:biddingReport,handleOpen:handleOpenDialog})})})]}),/*#__PURE__*/_jsx(TabPanel,{value:tabValue,index:1,children:/*#__PURE__*/_jsx(Box,{sx:{width:'100%',overflowX:'auto'},children:/*#__PURE__*/_jsx(Table,{heads:/*#__PURE__*/_jsx(BiddingTrackingThead,{}),isLoading:loading,data:biddingTracking,sx:{minWidth:'1310px'},children:/*#__PURE__*/_jsx(BiddingTrackingTBody,{pageNumber:conditions.page,pageSize:conditions.size,biddingTracking:biddingTracking})})})})]}),!loading&&tabValue<2&&/*#__PURE__*/_jsx(TableFooter,{pagination:{total:paginationResponse.totalElement,page:conditions.page,size:conditions.size},onPageChange:handleChangePage,onRowsPerPageChange:handleChangeRowsPerPage}),/*#__PURE__*/_jsx(AddOrEditBiddingReport,{open:open,handleClose:handleCloseDialog,isEdit:isEdit,loading:addOrEditLoading,data:detailBiddingReport,hanldeAdd:hanldeAddBiddingReport,hanldeEdit:hanldeEditBiddingReport}),/*#__PURE__*/_jsx(Synchronize,{open:openSynchronize,loading:synchronizeLoading,handleClose:handleCloseSynchronize,handleSynchronize:handlePostSynchronize})]});});export default MonitorBiddingPackages;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}