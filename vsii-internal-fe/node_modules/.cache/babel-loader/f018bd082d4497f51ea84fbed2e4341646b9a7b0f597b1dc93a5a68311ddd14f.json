{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/AddOrEditBidding.tsx\",\n  _s = $RefreshSig$();\n/* eslint-disable prettier/prettier */\n/* eslint-disable react-hooks/exhaustive-deps */\nimport { useEffect, useState } from 'react';\n\n// react-hoook-form\nimport { useFieldArray, useForm } from 'react-hook-form';\n\n// material-ui\nimport { LoadingButton } from '@mui/lab';\nimport { Button, DialogActions, Grid, Stack } from '@mui/material';\n\n// third party\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { useAppDispatch } from 'app/hooks';\nimport { FormProvider } from 'components/extended/Form';\nimport Modal from 'components/extended/Modal';\nimport { TabPanel } from 'components/extended/Tabs';\nimport { CONTRACT_TYPE_SALE_REPORT, E_BIDDING_STATUS, E_SALES_BIDDING_TYPE_SET_HCINFO, FIELD_BY_TAB_BIDDING, SERVICE_TYPE_STATUS, addOrEditBiddingTabs } from 'constants/Common';\nimport { TabCustom } from 'containers';\nimport { addOrEditBiddingConfig, addOrEditBiddingSchema } from 'pages/sales/Config';\nimport { openConfirm } from 'store/slice/confirmSlice';\nimport { openSnackbar } from 'store/slice/snackbarSlice';\nimport { getTabValueByFieldError, isEmpty } from 'utils/common';\nimport { dateFormat } from 'utils/date';\nimport AddOrEditBiddingFinancialInfo from './AddOrEditBiddingFinancialInfo';\nimport AddOrEditBiddingHcInfo from './AddOrEditBiddingHcInfo';\nimport AddOrEditBiddingOtherInfo from './AddOrEditBiddingOtherInfo';\nimport AddOrEditBiddingProjectInfo from './AddOrEditBiddingProjectInfo';\nimport TMOrFCBidding from './TMOrFCBidding';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AddOrEditBidding = props => {\n  _s();\n  // Hooks, State, Variable, Props\n  const dispatch = useAppDispatch();\n  const {\n    open,\n    handleClose,\n    isEdit,\n    currencyAndExchangeRateDefault,\n    monthlyBillableDay,\n    loading,\n    postAddOrEditBidding,\n    detailBidding,\n    year,\n    exchangeRateUSDpercentVND\n  } = props;\n  const [tabValue, setTabValue] = useState(0);\n  const [openNestedModal, setOpenNestedModal] = useState(false);\n  const [contact, setContact] = useState(null);\n  // TODO: DELETE ANY\n  const [hcInfoMonth, setHcInfoMonth] = useState(null);\n  const [fixCostHcInfo, setFixCostHcInfo] = useState(null);\n  const monthlyHCList = monthlyBillableDay.map(item => {\n    return {\n      year: item.year,\n      month: item.month,\n      billableDay: item.workingDays,\n      hcMonthly: 0,\n      billable: 0\n    };\n  });\n  const defaultValues = {\n    ...addOrEditBiddingConfig,\n    hcInfo: {\n      ...addOrEditBiddingConfig.hcInfo,\n      monthlyHCList\n    },\n    financialInfo: {\n      ...addOrEditBiddingConfig.financialInfo,\n      currency: (currencyAndExchangeRateDefault === null || currencyAndExchangeRateDefault === void 0 ? void 0 : currencyAndExchangeRateDefault.currency) || '',\n      exchangeRate: currencyAndExchangeRateDefault === null || currencyAndExchangeRateDefault === void 0 ? void 0 : currencyAndExchangeRateDefault.exchangeRate\n    }\n  };\n\n  // useForm\n  const methods = useForm({\n    defaultValues,\n    mode: 'all',\n    resolver: yupResolver(addOrEditBiddingSchema)\n  });\n  const {\n    errors\n  } = methods.formState;\n\n  // useFieldArray\n  const {\n    fields: fieldsMonthlyHCList\n  } = useFieldArray({\n    name: 'hcInfo.monthlyHCList',\n    control: methods.control\n  });\n  const handleSetHCInfo = (hcListResponse, type) => {\n    const monthlyHCListDefaultValue = methods.watch('hcInfo.monthlyHCList');\n    const monthlyHCListConvert = hcListResponse.map(x => {\n      const monthlyHCDefaultValue = monthlyHCListDefaultValue.find(y => x.year === y.year && x.month === y.month);\n      return {\n        ...x,\n        billable: type === E_SALES_BIDDING_TYPE_SET_HCINFO.ESTIMATE ? +x.billable : monthlyHCDefaultValue ? +(monthlyHCDefaultValue === null || monthlyHCDefaultValue === void 0 ? void 0 : monthlyHCDefaultValue.billable) : 0,\n        hcMonthly: monthlyHCDefaultValue ? +monthlyHCDefaultValue.hcMonthly : x.hcMonthly\n      };\n    });\n    methods.setValue('hcInfo.monthlyHCList', monthlyHCListConvert);\n  };\n  const focusErrors = () => {\n    const tabNumber = getTabValueByFieldError(errors, FIELD_BY_TAB_BIDDING);\n    setTabValue(tabNumber);\n  };\n\n  // ================= Event =================\n  // Handle change tab\n  const handleChangeTab = (event, value) => {\n    setTabValue(value);\n  };\n  const handleChangeUserContact = userSelected => {\n    setContact(userSelected ? {\n      idHexString: userSelected.idHexString,\n      firstName: userSelected.firstName,\n      lastName: userSelected.lastName\n    } : null);\n  };\n  const handleOpenTMOrFCHCInfo = () => {\n    setOpenNestedModal(true);\n  };\n  const handleCloseTMOrFCHCInfo = () => {\n    setOpenNestedModal(false);\n  };\n\n  // ================= Submit =================\n  const handleSubmit = values => {\n    // project Redmine\n    const projectRedmine = values.project.projectRedmineId;\n    // contractDurationFrom\n    const contractDurationFrom = values.project.contractDurationFrom;\n    // contract type\n    const contractType = values.project.contractType;\n    // hcInfo base contractType\n    const hcInfoMonthPayload = contractType === CONTRACT_TYPE_SALE_REPORT.TM ? {\n      hcInfoMonth\n    } : {\n      fixCostHcInfo\n    };\n    // payload\n    const payload = {\n      ...values,\n      project: {\n        ...values.project,\n        contractDueDate: dateFormat(values.project.contractDueDate),\n        contractDurationFrom: dateFormat(contractDurationFrom),\n        contractDurationTo: dateFormat(values.project.contractDurationTo),\n        projectRedmineId: projectRedmine === null || projectRedmine === void 0 ? void 0 : projectRedmine.value,\n        projectRedmineName: projectRedmine === null || projectRedmine === void 0 ? void 0 : projectRedmine.label\n      },\n      financialInfo: {\n        ...values.financialInfo,\n        year: isEdit ? year : new Date(contractDurationFrom).getFullYear()\n      },\n      hcInfo: {\n        ...values.hcInfo,\n        ...hcInfoMonthPayload\n      },\n      otherInfo: {\n        ...values.otherInfo,\n        contact\n      }\n    };\n    if (isEdit) {\n      // check contract type and service type\n      if (values.project.serviceType === SERVICE_TYPE_STATUS.PRODUCT && values.project.contractType === CONTRACT_TYPE_SALE_REPORT.FIXED_COST) {\n        dispatch(openSnackbar({\n          open: true,\n          message: 'edit-contract-type-service-type',\n          variant: 'alert',\n          alert: {\n            color: 'error'\n          }\n        }));\n        return;\n      }\n      // confirm submit if isEdit && status = Contract\n      const status = values.project.status;\n      if (status === E_BIDDING_STATUS.CONTRACT && (detailBidding === null || detailBidding === void 0 ? void 0 : detailBidding.project.status) !== E_BIDDING_STATUS.CONTRACT) {\n        dispatch(openConfirm({\n          open: true,\n          title: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 32\n          }, this),\n          content: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: \"bidding-confirm-edit-contract\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 34\n          }, this),\n          handleConfirm: () => postAddOrEditBidding(payload)\n        }));\n        return;\n      }\n      postAddOrEditBidding(payload);\n      return;\n    }\n    postAddOrEditBidding(payload);\n  };\n\n  // ================= Effect =================\n  useEffect(() => {\n    !isEmpty(errors) && focusErrors();\n  }, [errors]);\n  useEffect(() => {\n    if (isEdit && detailBidding) {\n      const {\n        project,\n        otherInfo,\n        hcInfo\n      } = detailBidding;\n      setContact({\n        idHexString: otherInfo.contact.idHexString,\n        firstName: otherInfo.contact.firstName,\n        lastName: otherInfo.contact.lastName\n      });\n      methods.reset({\n        ...detailBidding,\n        project: {\n          ...project,\n          projectRedmineId: project !== null && project !== void 0 && project.projectRedmineId ? {\n            value: project === null || project === void 0 ? void 0 : project.projectRedmineId,\n            label: project === null || project === void 0 ? void 0 : project.projectRedmineName\n          } : null\n        },\n        otherInfo: {\n          ...otherInfo,\n          contact: otherInfo !== null && otherInfo !== void 0 && otherInfo.contact.idHexString ? {\n            value: otherInfo.contact.idHexString,\n            label: `${otherInfo.contact.firstName} ${otherInfo.contact.lastName}`\n          } : null\n        }\n      });\n      (hcInfo === null || hcInfo === void 0 ? void 0 : hcInfo.hcInfoMonth) && setHcInfoMonth(hcInfo.hcInfoMonth);\n      (hcInfo === null || hcInfo === void 0 ? void 0 : hcInfo.fixCostHcInfo) && setFixCostHcInfo(hcInfo.fixCostHcInfo);\n    }\n  }, [detailBidding]);\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    isOpen: open,\n    title: isEdit ? 'all-sale-pipeline-edit-bidding-packages' : 'all-sale-pipeline-add-bidding-packages',\n    onClose: handleClose,\n    keepMounted: false,\n    maxWidth: \"lg\",\n    children: [/*#__PURE__*/_jsxDEV(TabCustom, {\n      value: tabValue,\n      handleChange: handleChangeTab,\n      tabs: addOrEditBiddingTabs\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(FormProvider, {\n      formReturn: methods,\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 0,\n        children: /*#__PURE__*/_jsxDEV(AddOrEditBiddingProjectInfo, {\n          isEdit: isEdit,\n          status: detailBidding === null || detailBidding === void 0 ? void 0 : detailBidding.project.status,\n          handleSetHCInfo: handleSetHCInfo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 1,\n        children: /*#__PURE__*/_jsxDEV(AddOrEditBiddingFinancialInfo, {\n          year: year,\n          exchangeRateUSDpercentVND: exchangeRateUSDpercentVND,\n          status: detailBidding === null || detailBidding === void 0 ? void 0 : detailBidding.project.status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 2,\n        children: /*#__PURE__*/_jsxDEV(AddOrEditBiddingHcInfo, {\n          fieldsMonthlyHCList: fieldsMonthlyHCList,\n          handleOpenTMOrFCHCInfo: handleOpenTMOrFCHCInfo,\n          status: detailBidding === null || detailBidding === void 0 ? void 0 : detailBidding.project.status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 3,\n        children: /*#__PURE__*/_jsxDEV(AddOrEditBiddingOtherInfo, {\n          handleChangeUserContact: handleChangeUserContact,\n          status: detailBidding === null || detailBidding === void 0 ? void 0 : detailBidding.project.status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            justifyContent: \"flex-end\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              color: \"error\",\n              onClick: handleClose,\n              disabled: loading,\n              children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n                id: \"cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(LoadingButton, {\n              loading: loading,\n              variant: \"contained\",\n              type: \"submit\",\n              disabled: (detailBidding === null || detailBidding === void 0 ? void 0 : detailBidding.project.status) === E_BIDDING_STATUS.CONTRACT,\n              children: isEdit ? /*#__PURE__*/_jsxDEV(FormattedMessage, {\n                id: \"submit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 43\n              }, this) : /*#__PURE__*/_jsxDEV(FormattedMessage, {\n                id: \"add\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 78\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 13\n    }, this), openNestedModal && /*#__PURE__*/_jsxDEV(TMOrFCBidding, {\n      open: openNestedModal,\n      handleClose: handleCloseTMOrFCHCInfo,\n      handleSetHCInfo: handleSetHCInfo,\n      hcInfoMonth: hcInfoMonth,\n      setHcInfoMonth: setHcInfoMonth,\n      fixCostHcInfo: fixCostHcInfo,\n      setFixCostHcInfo: setFixCostHcInfo,\n      exchangeRate: methods.getValues('financialInfo.exchangeRate'),\n      contractType: methods.getValues('project.contractType'),\n      sizeVND: methods.getValues('financialInfo.sizeVND'),\n      from: methods.getValues('project.contractDurationFrom'),\n      to: methods.getValues('project.contractDurationTo'),\n      status: detailBidding === null || detailBidding === void 0 ? void 0 : detailBidding.project.status\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 271,\n    columnNumber: 9\n  }, this);\n};\n_s(AddOrEditBidding, \"mawaFQNTIlxMei3g23CrLi8dHh8=\", false, function () {\n  return [useAppDispatch, useForm, useFieldArray];\n});\n_c = AddOrEditBidding;\nexport default AddOrEditBidding;\nvar _c;\n$RefreshReg$(_c, \"AddOrEditBidding\");", "map": {"version": 3, "names": ["useEffect", "useState", "useFieldArray", "useForm", "LoadingButton", "<PERSON><PERSON>", "DialogActions", "Grid", "<PERSON><PERSON>", "yupResolver", "FormattedMessage", "useAppDispatch", "FormProvider", "Modal", "TabPanel", "CONTRACT_TYPE_SALE_REPORT", "E_BIDDING_STATUS", "E_SALES_BIDDING_TYPE_SET_HCINFO", "FIELD_BY_TAB_BIDDING", "SERVICE_TYPE_STATUS", "addOrEditBiddingTabs", "TabCustom", "addOrEditBiddingConfig", "addOrEditBiddingSchema", "openConfirm", "openSnackbar", "getTabValueByFieldError", "isEmpty", "dateFormat", "AddOrEditBiddingFinancialInfo", "AddOrEditBiddingHcInfo", "AddOrEditBiddingOtherInfo", "AddOrEditBiddingProjectInfo", "TMOrFCBidding", "jsxDEV", "_jsxDEV", "AddOrEditBidding", "props", "_s", "dispatch", "open", "handleClose", "isEdit", "currencyAndExchangeRateDefault", "monthlyBillableDay", "loading", "postAddOrEditBidding", "detailBidding", "year", "exchangeRateUSDpercentVND", "tabValue", "setTabValue", "openNestedModal", "setOpenNestedModal", "contact", "setContact", "hcInfoMonth", "setHcInfoMonth", "fixCostHcInfo", "setFixCostHcInfo", "monthlyHCList", "map", "item", "month", "billableDay", "workingDays", "hcMonthly", "billable", "defaultValues", "hcInfo", "financialInfo", "currency", "exchangeRate", "methods", "mode", "resolver", "errors", "formState", "fields", "fieldsMonthlyHCList", "name", "control", "handleSetHCInfo", "hcListResponse", "type", "monthlyHCListDefaultValue", "watch", "monthlyHCListConvert", "x", "monthlyHCDefaultValue", "find", "y", "ESTIMATE", "setValue", "focusErrors", "tabNumber", "handleChangeTab", "event", "value", "handleChangeUserContact", "userSelected", "idHexString", "firstName", "lastName", "handleOpenTMOrFCHCInfo", "handleCloseTMOrFCHCInfo", "handleSubmit", "values", "projectRedmine", "project", "projectRedmineId", "contractDurationFrom", "contractType", "hcInfoMonthPayload", "TM", "payload", "contractDueDate", "contractDurationTo", "projectRedmineName", "label", "Date", "getFullYear", "otherInfo", "serviceType", "PRODUCT", "FIXED_COST", "message", "variant", "alert", "color", "status", "CONTRACT", "title", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "handleConfirm", "reset", "isOpen", "onClose", "keepMounted", "max<PERSON><PERSON><PERSON>", "children", "handleChange", "tabs", "formReturn", "onSubmit", "index", "xs", "direction", "spacing", "justifyContent", "onClick", "disabled", "getV<PERSON>ues", "sizeVND", "from", "to", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/AddOrEditBidding.tsx"], "sourcesContent": ["/* eslint-disable prettier/prettier */\n/* eslint-disable react-hooks/exhaustive-deps */\nimport { SyntheticEvent, useEffect, useState } from 'react';\n\n// react-hoook-form\nimport { useFieldArray, useForm } from 'react-hook-form';\n\n// material-ui\nimport { LoadingButton } from '@mui/lab';\nimport { Button, DialogActions, Grid, Stack } from '@mui/material';\n\n// third party\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { useAppDispatch } from 'app/hooks';\nimport { FormProvider } from 'components/extended/Form';\nimport Modal from 'components/extended/Modal';\nimport { TabPanel } from 'components/extended/Tabs';\nimport {\n    CONTRACT_TYPE_SALE_REPORT,\n    E_BIDDING_STATUS,\n    E_SALES_BIDDING_TYPE_SET_HCINFO,\n    FIELD_BY_TAB_BIDDING,\n    SERVICE_TYPE_STATUS,\n    addOrEditBiddingTabs\n} from 'constants/Common';\nimport { TabCustom } from 'containers';\nimport { addOrEditBiddingConfig, addOrEditBiddingSchema } from 'pages/sales/Config';\nimport { openConfirm } from 'store/slice/confirmSlice';\nimport { openSnackbar } from 'store/slice/snackbarSlice';\nimport { ICurrencyBidding, IDetailBidding, IHCMonthlyList } from 'types';\nimport { getTabValueByFieldError, isEmpty } from 'utils/common';\nimport { dateFormat } from 'utils/date';\nimport AddOrEditBiddingFinancialInfo from './AddOrEditBiddingFinancialInfo';\nimport AddOrEditBiddingHcInfo from './AddOrEditBiddingHcInfo';\nimport AddOrEditBiddingOtherInfo from './AddOrEditBiddingOtherInfo';\nimport AddOrEditBiddingProjectInfo from './AddOrEditBiddingProjectInfo';\nimport TMOrFCBidding from './TMOrFCBidding';\nimport { IMember } from 'types/member';\n\ninterface IAddOrEditBiddingProps {\n    open: boolean;\n    year?: number;\n    isEdit: boolean;\n    handleClose: () => void;\n    loading: boolean;\n    detailBidding?: IDetailBidding | null;\n    currencyAndExchangeRateDefault: ICurrencyBidding;\n    exchangeRateUSDpercentVND: number;\n    // TODO:\n    postAddOrEditBidding: (payload: any) => void;\n    monthlyBillableDay: any;\n}\n\nconst AddOrEditBidding = (props: IAddOrEditBiddingProps) => {\n    // Hooks, State, Variable, Props\n    const dispatch = useAppDispatch();\n    const {\n        open,\n        handleClose,\n        isEdit,\n        currencyAndExchangeRateDefault,\n        monthlyBillableDay,\n        loading,\n        postAddOrEditBidding,\n        detailBidding,\n        year,\n        exchangeRateUSDpercentVND\n    } = props;\n    const [tabValue, setTabValue] = useState(0);\n    const [openNestedModal, setOpenNestedModal] = useState<boolean>(false);\n    const [contact, setContact] = useState<{ idHexString: string; firstName: string; lastName: string } | null>(null);\n    // TODO: DELETE ANY\n    const [hcInfoMonth, setHcInfoMonth] = useState<any>(null);\n    const [fixCostHcInfo, setFixCostHcInfo] = useState<any>(null);\n\n    const monthlyHCList = monthlyBillableDay.map((item: any) => {\n        return {\n            year: item.year,\n            month: item.month,\n            billableDay: item.workingDays,\n            hcMonthly: 0,\n            billable: 0\n        };\n    });\n    const defaultValues = {\n        ...addOrEditBiddingConfig,\n        hcInfo: {\n            ...addOrEditBiddingConfig.hcInfo,\n            monthlyHCList\n        },\n        financialInfo: {\n            ...addOrEditBiddingConfig.financialInfo,\n            currency: currencyAndExchangeRateDefault?.currency || '',\n            exchangeRate: currencyAndExchangeRateDefault?.exchangeRate\n        }\n    };\n\n    // useForm\n    const methods = useForm({\n        defaultValues,\n        mode: 'all',\n        resolver: yupResolver(addOrEditBiddingSchema)\n    });\n    const { errors } = methods.formState;\n\n    // useFieldArray\n    const { fields: fieldsMonthlyHCList } = useFieldArray({\n        name: 'hcInfo.monthlyHCList',\n        control: methods.control\n    } as any);\n\n    const handleSetHCInfo = (hcListResponse: IHCMonthlyList[], type?: string) => {\n        const monthlyHCListDefaultValue = methods.watch('hcInfo.monthlyHCList');\n        const monthlyHCListConvert = hcListResponse.map((x: IHCMonthlyList) => {\n            const monthlyHCDefaultValue = monthlyHCListDefaultValue.find((y: IHCMonthlyList) => x.year === y.year && x.month === y.month);\n            return {\n                ...x,\n                billable:\n                    type === E_SALES_BIDDING_TYPE_SET_HCINFO.ESTIMATE\n                        ? +x.billable\n                        : monthlyHCDefaultValue\n                        ? +monthlyHCDefaultValue?.billable\n                        : 0,\n                hcMonthly: monthlyHCDefaultValue ? +monthlyHCDefaultValue.hcMonthly : x.hcMonthly\n            };\n        });\n        methods.setValue('hcInfo.monthlyHCList', monthlyHCListConvert);\n    };\n\n    const focusErrors = () => {\n        const tabNumber = getTabValueByFieldError(errors, FIELD_BY_TAB_BIDDING);\n        setTabValue(tabNumber);\n    };\n\n    // ================= Event =================\n    // Handle change tab\n    const handleChangeTab = (event: SyntheticEvent, value: number) => {\n        setTabValue(value);\n    };\n\n    const handleChangeUserContact = (userSelected: IMember) => {\n        setContact(\n            userSelected\n                ? {\n                      idHexString: userSelected.idHexString!,\n                      firstName: userSelected.firstName,\n                      lastName: userSelected.lastName\n                  }\n                : null\n        );\n    };\n\n    const handleOpenTMOrFCHCInfo = () => {\n        setOpenNestedModal(true);\n    };\n\n    const handleCloseTMOrFCHCInfo = () => {\n        setOpenNestedModal(false);\n    };\n\n    // ================= Submit =================\n    const handleSubmit = (values: any) => {\n        // project Redmine\n        const projectRedmine = values.project.projectRedmineId;\n        // contractDurationFrom\n        const contractDurationFrom = values.project.contractDurationFrom;\n        // contract type\n        const contractType = values.project.contractType;\n        // hcInfo base contractType\n        const hcInfoMonthPayload = contractType === CONTRACT_TYPE_SALE_REPORT.TM ? { hcInfoMonth } : { fixCostHcInfo };\n        // payload\n        const payload = {\n            ...values,\n            project: {\n                ...values.project,\n                contractDueDate: dateFormat(values.project.contractDueDate),\n                contractDurationFrom: dateFormat(contractDurationFrom),\n                contractDurationTo: dateFormat(values.project.contractDurationTo),\n                projectRedmineId: projectRedmine?.value,\n                projectRedmineName: projectRedmine?.label\n            },\n            financialInfo: {\n                ...values.financialInfo,\n                year: isEdit ? year : new Date(contractDurationFrom).getFullYear()\n            },\n            hcInfo: { ...values.hcInfo, ...hcInfoMonthPayload },\n            otherInfo: {\n                ...values.otherInfo,\n                contact\n            }\n        };\n\n        if (isEdit) {\n            // check contract type and service type\n            if (\n                values.project.serviceType === SERVICE_TYPE_STATUS.PRODUCT &&\n                values.project.contractType === CONTRACT_TYPE_SALE_REPORT.FIXED_COST\n            ) {\n                dispatch(\n                    openSnackbar({\n                        open: true,\n                        message: 'edit-contract-type-service-type',\n                        variant: 'alert',\n                        alert: { color: 'error' }\n                    })\n                );\n                return;\n            }\n            // confirm submit if isEdit && status = Contract\n            const status = values.project.status;\n            if (status === E_BIDDING_STATUS.CONTRACT && detailBidding?.project.status !== E_BIDDING_STATUS.CONTRACT) {\n                dispatch(\n                    openConfirm({\n                        open: true,\n                        title: <FormattedMessage id=\"warning\" />,\n                        content: <FormattedMessage id=\"bidding-confirm-edit-contract\" />,\n                        handleConfirm: () => postAddOrEditBidding(payload)\n                    })\n                );\n                return;\n            }\n            postAddOrEditBidding(payload);\n            return;\n        }\n        postAddOrEditBidding(payload);\n    };\n\n    // ================= Effect =================\n    useEffect(() => {\n        !isEmpty(errors) && focusErrors();\n    }, [errors]);\n\n    useEffect(() => {\n        if (isEdit && detailBidding) {\n            const { project, otherInfo, hcInfo } = detailBidding;\n            setContact({\n                idHexString: otherInfo.contact.idHexString,\n                firstName: otherInfo.contact.firstName,\n                lastName: otherInfo.contact.lastName\n            });\n            methods.reset({\n                ...detailBidding,\n                project: {\n                    ...project,\n                    projectRedmineId: project?.projectRedmineId\n                        ? {\n                              value: project?.projectRedmineId,\n                              label: project?.projectRedmineName\n                          }\n                        : null\n                },\n                otherInfo: {\n                    ...otherInfo,\n                    contact: otherInfo?.contact.idHexString\n                        ? {\n                              value: otherInfo.contact.idHexString,\n                              label: `${otherInfo.contact.firstName} ${otherInfo.contact.lastName}`\n                          }\n                        : null\n                }\n            } as any);\n            hcInfo?.hcInfoMonth && setHcInfoMonth(hcInfo.hcInfoMonth);\n            hcInfo?.fixCostHcInfo && setFixCostHcInfo(hcInfo.fixCostHcInfo);\n        }\n    }, [detailBidding]);\n\n    return (\n        <Modal\n            isOpen={open}\n            title={isEdit ? 'all-sale-pipeline-edit-bidding-packages' : 'all-sale-pipeline-add-bidding-packages'}\n            onClose={handleClose}\n            keepMounted={false}\n            maxWidth=\"lg\"\n        >\n            <TabCustom value={tabValue} handleChange={handleChangeTab} tabs={addOrEditBiddingTabs} />\n            <FormProvider formReturn={methods} onSubmit={handleSubmit}>\n                {/* Project Info */}\n                <TabPanel value={tabValue} index={0}>\n                    <AddOrEditBiddingProjectInfo isEdit={isEdit} status={detailBidding?.project.status} handleSetHCInfo={handleSetHCInfo} />\n                </TabPanel>\n                {/* Financial Info */}\n                <TabPanel value={tabValue} index={1}>\n                    <AddOrEditBiddingFinancialInfo\n                        year={year}\n                        exchangeRateUSDpercentVND={exchangeRateUSDpercentVND}\n                        status={detailBidding?.project.status}\n                    />\n                </TabPanel>\n                {/* HC Info */}\n                <TabPanel value={tabValue} index={2}>\n                    <AddOrEditBiddingHcInfo\n                        fieldsMonthlyHCList={fieldsMonthlyHCList}\n                        handleOpenTMOrFCHCInfo={handleOpenTMOrFCHCInfo}\n                        status={detailBidding?.project.status}\n                    />\n                </TabPanel>\n                {/* Other Info */}\n                <TabPanel value={tabValue} index={3}>\n                    <AddOrEditBiddingOtherInfo handleChangeUserContact={handleChangeUserContact} status={detailBidding?.project.status} />\n                </TabPanel>\n                {/* Cancel | Submit */}\n                <Grid item xs={12}>\n                    <DialogActions>\n                        <Stack direction=\"row\" spacing={1} justifyContent=\"flex-end\">\n                            <Button color=\"error\" onClick={handleClose} disabled={loading}>\n                                <FormattedMessage id=\"cancel\" />\n                            </Button>\n                            <LoadingButton\n                                loading={loading}\n                                variant=\"contained\"\n                                type=\"submit\"\n                                disabled={detailBidding?.project.status === E_BIDDING_STATUS.CONTRACT}\n                            >\n                                {isEdit ? <FormattedMessage id=\"submit\" /> : <FormattedMessage id=\"add\" />}\n                            </LoadingButton>\n                        </Stack>\n                    </DialogActions>\n                </Grid>\n            </FormProvider>\n\n            {/* TM or Fix Cost Modal HC Info */}\n            {openNestedModal && (\n                <TMOrFCBidding\n                    open={openNestedModal}\n                    handleClose={handleCloseTMOrFCHCInfo}\n                    handleSetHCInfo={handleSetHCInfo}\n                    hcInfoMonth={hcInfoMonth}\n                    setHcInfoMonth={setHcInfoMonth}\n                    fixCostHcInfo={fixCostHcInfo}\n                    setFixCostHcInfo={setFixCostHcInfo}\n                    exchangeRate={methods.getValues('financialInfo.exchangeRate')}\n                    contractType={methods.getValues('project.contractType')}\n                    sizeVND={methods.getValues('financialInfo.sizeVND')}\n                    from={methods.getValues('project.contractDurationFrom')}\n                    to={methods.getValues('project.contractDurationTo')}\n                    status={detailBidding?.project.status}\n                />\n            )}\n        </Modal>\n    );\n};\n\nexport default AddOrEditBidding;\n"], "mappings": ";;AAAA;AACA;AACA,SAAyBA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;;AAE3D;AACA,SAASC,aAAa,EAAEC,OAAO,QAAQ,iBAAiB;;AAExD;AACA,SAASC,aAAa,QAAQ,UAAU;AACxC,SAASC,MAAM,EAAEC,aAAa,EAAEC,IAAI,EAAEC,KAAK,QAAQ,eAAe;;AAElE;AACA,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,cAAc,QAAQ,WAAW;AAC1C,SAASC,YAAY,QAAQ,0BAA0B;AACvD,OAAOC,KAAK,MAAM,2BAA2B;AAC7C,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SACIC,yBAAyB,EACzBC,gBAAgB,EAChBC,+BAA+B,EAC/BC,oBAAoB,EACpBC,mBAAmB,EACnBC,oBAAoB,QACjB,kBAAkB;AACzB,SAASC,SAAS,QAAQ,YAAY;AACtC,SAASC,sBAAsB,EAAEC,sBAAsB,QAAQ,oBAAoB;AACnF,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,YAAY,QAAQ,2BAA2B;AAExD,SAASC,uBAAuB,EAAEC,OAAO,QAAQ,cAAc;AAC/D,SAASC,UAAU,QAAQ,YAAY;AACvC,OAAOC,6BAA6B,MAAM,iCAAiC;AAC3E,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,yBAAyB,MAAM,6BAA6B;AACnE,OAAOC,2BAA2B,MAAM,+BAA+B;AACvE,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAiB5C,MAAMC,gBAAgB,GAAIC,KAA6B,IAAK;EAAAC,EAAA;EACxD;EACA,MAAMC,QAAQ,GAAG5B,cAAc,CAAC,CAAC;EACjC,MAAM;IACF6B,IAAI;IACJC,WAAW;IACXC,MAAM;IACNC,8BAA8B;IAC9BC,kBAAkB;IAClBC,OAAO;IACPC,oBAAoB;IACpBC,aAAa;IACbC,IAAI;IACJC;EACJ,CAAC,GAAGZ,KAAK;EACT,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACmD,eAAe,EAAEC,kBAAkB,CAAC,GAAGpD,QAAQ,CAAU,KAAK,CAAC;EACtE,MAAM,CAACqD,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAsE,IAAI,CAAC;EACjH;EACA,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAM,IAAI,CAAC;EACzD,MAAM,CAACyD,aAAa,EAAEC,gBAAgB,CAAC,GAAG1D,QAAQ,CAAM,IAAI,CAAC;EAE7D,MAAM2D,aAAa,GAAGhB,kBAAkB,CAACiB,GAAG,CAAEC,IAAS,IAAK;IACxD,OAAO;MACHd,IAAI,EAAEc,IAAI,CAACd,IAAI;MACfe,KAAK,EAAED,IAAI,CAACC,KAAK;MACjBC,WAAW,EAAEF,IAAI,CAACG,WAAW;MAC7BC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;IACd,CAAC;EACL,CAAC,CAAC;EACF,MAAMC,aAAa,GAAG;IAClB,GAAG9C,sBAAsB;IACzB+C,MAAM,EAAE;MACJ,GAAG/C,sBAAsB,CAAC+C,MAAM;MAChCT;IACJ,CAAC;IACDU,aAAa,EAAE;MACX,GAAGhD,sBAAsB,CAACgD,aAAa;MACvCC,QAAQ,EAAE,CAAA5B,8BAA8B,aAA9BA,8BAA8B,uBAA9BA,8BAA8B,CAAE4B,QAAQ,KAAI,EAAE;MACxDC,YAAY,EAAE7B,8BAA8B,aAA9BA,8BAA8B,uBAA9BA,8BAA8B,CAAE6B;IAClD;EACJ,CAAC;;EAED;EACA,MAAMC,OAAO,GAAGtE,OAAO,CAAC;IACpBiE,aAAa;IACbM,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAElE,WAAW,CAACc,sBAAsB;EAChD,CAAC,CAAC;EACF,MAAM;IAAEqD;EAAO,CAAC,GAAGH,OAAO,CAACI,SAAS;;EAEpC;EACA,MAAM;IAAEC,MAAM,EAAEC;EAAoB,CAAC,GAAG7E,aAAa,CAAC;IAClD8E,IAAI,EAAE,sBAAsB;IAC5BC,OAAO,EAAER,OAAO,CAACQ;EACrB,CAAQ,CAAC;EAET,MAAMC,eAAe,GAAGA,CAACC,cAAgC,EAAEC,IAAa,KAAK;IACzE,MAAMC,yBAAyB,GAAGZ,OAAO,CAACa,KAAK,CAAC,sBAAsB,CAAC;IACvE,MAAMC,oBAAoB,GAAGJ,cAAc,CAACtB,GAAG,CAAE2B,CAAiB,IAAK;MACnE,MAAMC,qBAAqB,GAAGJ,yBAAyB,CAACK,IAAI,CAAEC,CAAiB,IAAKH,CAAC,CAACxC,IAAI,KAAK2C,CAAC,CAAC3C,IAAI,IAAIwC,CAAC,CAACzB,KAAK,KAAK4B,CAAC,CAAC5B,KAAK,CAAC;MAC7H,OAAO;QACH,GAAGyB,CAAC;QACJrB,QAAQ,EACJiB,IAAI,KAAKnE,+BAA+B,CAAC2E,QAAQ,GAC3C,CAACJ,CAAC,CAACrB,QAAQ,GACXsB,qBAAqB,GACrB,EAACA,qBAAqB,aAArBA,qBAAqB,uBAArBA,qBAAqB,CAAEtB,QAAQ,IAChC,CAAC;QACXD,SAAS,EAAEuB,qBAAqB,GAAG,CAACA,qBAAqB,CAACvB,SAAS,GAAGsB,CAAC,CAACtB;MAC5E,CAAC;IACL,CAAC,CAAC;IACFO,OAAO,CAACoB,QAAQ,CAAC,sBAAsB,EAAEN,oBAAoB,CAAC;EAClE,CAAC;EAED,MAAMO,WAAW,GAAGA,CAAA,KAAM;IACtB,MAAMC,SAAS,GAAGrE,uBAAuB,CAACkD,MAAM,EAAE1D,oBAAoB,CAAC;IACvEiC,WAAW,CAAC4C,SAAS,CAAC;EAC1B,CAAC;;EAED;EACA;EACA,MAAMC,eAAe,GAAGA,CAACC,KAAqB,EAAEC,KAAa,KAAK;IAC9D/C,WAAW,CAAC+C,KAAK,CAAC;EACtB,CAAC;EAED,MAAMC,uBAAuB,GAAIC,YAAqB,IAAK;IACvD7C,UAAU,CACN6C,YAAY,GACN;MACIC,WAAW,EAAED,YAAY,CAACC,WAAY;MACtCC,SAAS,EAAEF,YAAY,CAACE,SAAS;MACjCC,QAAQ,EAAEH,YAAY,CAACG;IAC3B,CAAC,GACD,IACV,CAAC;EACL,CAAC;EAED,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACjCnD,kBAAkB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMoD,uBAAuB,GAAGA,CAAA,KAAM;IAClCpD,kBAAkB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMqD,YAAY,GAAIC,MAAW,IAAK;IAClC;IACA,MAAMC,cAAc,GAAGD,MAAM,CAACE,OAAO,CAACC,gBAAgB;IACtD;IACA,MAAMC,oBAAoB,GAAGJ,MAAM,CAACE,OAAO,CAACE,oBAAoB;IAChE;IACA,MAAMC,YAAY,GAAGL,MAAM,CAACE,OAAO,CAACG,YAAY;IAChD;IACA,MAAMC,kBAAkB,GAAGD,YAAY,KAAKjG,yBAAyB,CAACmG,EAAE,GAAG;MAAE1D;IAAY,CAAC,GAAG;MAAEE;IAAc,CAAC;IAC9G;IACA,MAAMyD,OAAO,GAAG;MACZ,GAAGR,MAAM;MACTE,OAAO,EAAE;QACL,GAAGF,MAAM,CAACE,OAAO;QACjBO,eAAe,EAAExF,UAAU,CAAC+E,MAAM,CAACE,OAAO,CAACO,eAAe,CAAC;QAC3DL,oBAAoB,EAAEnF,UAAU,CAACmF,oBAAoB,CAAC;QACtDM,kBAAkB,EAAEzF,UAAU,CAAC+E,MAAM,CAACE,OAAO,CAACQ,kBAAkB,CAAC;QACjEP,gBAAgB,EAAEF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEV,KAAK;QACvCoB,kBAAkB,EAAEV,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEW;MACxC,CAAC;MACDjD,aAAa,EAAE;QACX,GAAGqC,MAAM,CAACrC,aAAa;QACvBtB,IAAI,EAAEN,MAAM,GAAGM,IAAI,GAAG,IAAIwE,IAAI,CAACT,oBAAoB,CAAC,CAACU,WAAW,CAAC;MACrE,CAAC;MACDpD,MAAM,EAAE;QAAE,GAAGsC,MAAM,CAACtC,MAAM;QAAE,GAAG4C;MAAmB,CAAC;MACnDS,SAAS,EAAE;QACP,GAAGf,MAAM,CAACe,SAAS;QACnBpE;MACJ;IACJ,CAAC;IAED,IAAIZ,MAAM,EAAE;MACR;MACA,IACIiE,MAAM,CAACE,OAAO,CAACc,WAAW,KAAKxG,mBAAmB,CAACyG,OAAO,IAC1DjB,MAAM,CAACE,OAAO,CAACG,YAAY,KAAKjG,yBAAyB,CAAC8G,UAAU,EACtE;QACEtF,QAAQ,CACJd,YAAY,CAAC;UACTe,IAAI,EAAE,IAAI;UACVsF,OAAO,EAAE,iCAAiC;UAC1CC,OAAO,EAAE,OAAO;UAChBC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAQ;QAC5B,CAAC,CACL,CAAC;QACD;MACJ;MACA;MACA,MAAMC,MAAM,GAAGvB,MAAM,CAACE,OAAO,CAACqB,MAAM;MACpC,IAAIA,MAAM,KAAKlH,gBAAgB,CAACmH,QAAQ,IAAI,CAAApF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE8D,OAAO,CAACqB,MAAM,MAAKlH,gBAAgB,CAACmH,QAAQ,EAAE;QACrG5F,QAAQ,CACJf,WAAW,CAAC;UACRgB,IAAI,EAAE,IAAI;UACV4F,KAAK,eAAEjG,OAAA,CAACzB,gBAAgB;YAAC2H,EAAE,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UACxCC,OAAO,eAAEvG,OAAA,CAACzB,gBAAgB;YAAC2H,EAAE,EAAC;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UAChEE,aAAa,EAAEA,CAAA,KAAM7F,oBAAoB,CAACqE,OAAO;QACrD,CAAC,CACL,CAAC;QACD;MACJ;MACArE,oBAAoB,CAACqE,OAAO,CAAC;MAC7B;IACJ;IACArE,oBAAoB,CAACqE,OAAO,CAAC;EACjC,CAAC;;EAED;EACAnH,SAAS,CAAC,MAAM;IACZ,CAAC2B,OAAO,CAACiD,MAAM,CAAC,IAAIkB,WAAW,CAAC,CAAC;EACrC,CAAC,EAAE,CAAClB,MAAM,CAAC,CAAC;EAEZ5E,SAAS,CAAC,MAAM;IACZ,IAAI0C,MAAM,IAAIK,aAAa,EAAE;MACzB,MAAM;QAAE8D,OAAO;QAAEa,SAAS;QAAErD;MAAO,CAAC,GAAGtB,aAAa;MACpDQ,UAAU,CAAC;QACP8C,WAAW,EAAEqB,SAAS,CAACpE,OAAO,CAAC+C,WAAW;QAC1CC,SAAS,EAAEoB,SAAS,CAACpE,OAAO,CAACgD,SAAS;QACtCC,QAAQ,EAAEmB,SAAS,CAACpE,OAAO,CAACiD;MAChC,CAAC,CAAC;MACF9B,OAAO,CAACmE,KAAK,CAAC;QACV,GAAG7F,aAAa;QAChB8D,OAAO,EAAE;UACL,GAAGA,OAAO;UACVC,gBAAgB,EAAED,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEC,gBAAgB,GACrC;YACIZ,KAAK,EAAEW,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,gBAAgB;YAChCS,KAAK,EAAEV,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAES;UACpB,CAAC,GACD;QACV,CAAC;QACDI,SAAS,EAAE;UACP,GAAGA,SAAS;UACZpE,OAAO,EAAEoE,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEpE,OAAO,CAAC+C,WAAW,GACjC;YACIH,KAAK,EAAEwB,SAAS,CAACpE,OAAO,CAAC+C,WAAW;YACpCkB,KAAK,EAAE,GAAGG,SAAS,CAACpE,OAAO,CAACgD,SAAS,IAAIoB,SAAS,CAACpE,OAAO,CAACiD,QAAQ;UACvE,CAAC,GACD;QACV;MACJ,CAAQ,CAAC;MACT,CAAAlC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEb,WAAW,KAAIC,cAAc,CAACY,MAAM,CAACb,WAAW,CAAC;MACzD,CAAAa,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEX,aAAa,KAAIC,gBAAgB,CAACU,MAAM,CAACX,aAAa,CAAC;IACnE;EACJ,CAAC,EAAE,CAACX,aAAa,CAAC,CAAC;EAEnB,oBACIZ,OAAA,CAACtB,KAAK;IACFgI,MAAM,EAAErG,IAAK;IACb4F,KAAK,EAAE1F,MAAM,GAAG,yCAAyC,GAAG,wCAAyC;IACrGoG,OAAO,EAAErG,WAAY;IACrBsG,WAAW,EAAE,KAAM;IACnBC,QAAQ,EAAC,IAAI;IAAAC,QAAA,gBAEb9G,OAAA,CAACd,SAAS;MAAC6E,KAAK,EAAEhD,QAAS;MAACgG,YAAY,EAAElD,eAAgB;MAACmD,IAAI,EAAE/H;IAAqB;MAAAkH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzFtG,OAAA,CAACvB,YAAY;MAACwI,UAAU,EAAE3E,OAAQ;MAAC4E,QAAQ,EAAE3C,YAAa;MAAAuC,QAAA,gBAEtD9G,OAAA,CAACrB,QAAQ;QAACoF,KAAK,EAAEhD,QAAS;QAACoG,KAAK,EAAE,CAAE;QAAAL,QAAA,eAChC9G,OAAA,CAACH,2BAA2B;UAACU,MAAM,EAAEA,MAAO;UAACwF,MAAM,EAAEnF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE8D,OAAO,CAACqB,MAAO;UAAChD,eAAe,EAAEA;QAAgB;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClH,CAAC,eAEXtG,OAAA,CAACrB,QAAQ;QAACoF,KAAK,EAAEhD,QAAS;QAACoG,KAAK,EAAE,CAAE;QAAAL,QAAA,eAChC9G,OAAA,CAACN,6BAA6B;UAC1BmB,IAAI,EAAEA,IAAK;UACXC,yBAAyB,EAAEA,yBAA0B;UACrDiF,MAAM,EAAEnF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE8D,OAAO,CAACqB;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAEXtG,OAAA,CAACrB,QAAQ;QAACoF,KAAK,EAAEhD,QAAS;QAACoG,KAAK,EAAE,CAAE;QAAAL,QAAA,eAChC9G,OAAA,CAACL,sBAAsB;UACnBiD,mBAAmB,EAAEA,mBAAoB;UACzCyB,sBAAsB,EAAEA,sBAAuB;UAC/C0B,MAAM,EAAEnF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE8D,OAAO,CAACqB;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAEXtG,OAAA,CAACrB,QAAQ;QAACoF,KAAK,EAAEhD,QAAS;QAACoG,KAAK,EAAE,CAAE;QAAAL,QAAA,eAChC9G,OAAA,CAACJ,yBAAyB;UAACoE,uBAAuB,EAAEA,uBAAwB;UAAC+B,MAAM,EAAEnF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE8D,OAAO,CAACqB;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChH,CAAC,eAEXtG,OAAA,CAAC5B,IAAI;QAACuD,IAAI;QAACyF,EAAE,EAAE,EAAG;QAAAN,QAAA,eACd9G,OAAA,CAAC7B,aAAa;UAAA2I,QAAA,eACV9G,OAAA,CAAC3B,KAAK;YAACgJ,SAAS,EAAC,KAAK;YAACC,OAAO,EAAE,CAAE;YAACC,cAAc,EAAC,UAAU;YAAAT,QAAA,gBACxD9G,OAAA,CAAC9B,MAAM;cAAC4H,KAAK,EAAC,OAAO;cAAC0B,OAAO,EAAElH,WAAY;cAACmH,QAAQ,EAAE/G,OAAQ;cAAAoG,QAAA,eAC1D9G,OAAA,CAACzB,gBAAgB;gBAAC2H,EAAE,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACTtG,OAAA,CAAC/B,aAAa;cACVyC,OAAO,EAAEA,OAAQ;cACjBkF,OAAO,EAAC,WAAW;cACnB3C,IAAI,EAAC,QAAQ;cACbwE,QAAQ,EAAE,CAAA7G,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE8D,OAAO,CAACqB,MAAM,MAAKlH,gBAAgB,CAACmH,QAAS;cAAAc,QAAA,EAErEvG,MAAM,gBAAGP,OAAA,CAACzB,gBAAgB;gBAAC2H,EAAE,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGtG,OAAA,CAACzB,gBAAgB;gBAAC2H,EAAE,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EAGdrF,eAAe,iBACZjB,OAAA,CAACF,aAAa;MACVO,IAAI,EAAEY,eAAgB;MACtBX,WAAW,EAAEgE,uBAAwB;MACrCvB,eAAe,EAAEA,eAAgB;MACjC1B,WAAW,EAAEA,WAAY;MACzBC,cAAc,EAAEA,cAAe;MAC/BC,aAAa,EAAEA,aAAc;MAC7BC,gBAAgB,EAAEA,gBAAiB;MACnCa,YAAY,EAAEC,OAAO,CAACoF,SAAS,CAAC,4BAA4B,CAAE;MAC9D7C,YAAY,EAAEvC,OAAO,CAACoF,SAAS,CAAC,sBAAsB,CAAE;MACxDC,OAAO,EAAErF,OAAO,CAACoF,SAAS,CAAC,uBAAuB,CAAE;MACpDE,IAAI,EAAEtF,OAAO,CAACoF,SAAS,CAAC,8BAA8B,CAAE;MACxDG,EAAE,EAAEvF,OAAO,CAACoF,SAAS,CAAC,4BAA4B,CAAE;MACpD3B,MAAM,EAAEnF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE8D,OAAO,CAACqB;IAAO;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB,CAAC;AAACnG,EAAA,CA/RIF,gBAAgB;EAAA,QAEDzB,cAAc,EA2CfR,OAAO,EAQiBD,aAAa;AAAA;AAAA+J,EAAA,GArDnD7H,gBAAgB;AAiStB,eAAeA,gBAAgB;AAAC,IAAA6H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}