{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isWithinInterval\n * @category Interval Helpers\n * @summary Is the given date within the interval?\n *\n * @description\n * Is the given date within the interval? (Including start and end.)\n *\n * @param {Date|Number} date - the date to check\n * @param {Interval} interval - the interval to check\n * @returns {Boolean} the date is within the interval\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // For the date within the interval:\n * isWithinInterval(new Date(2014, 0, 3), {\n *   start: new Date(2014, 0, 1),\n *   end: new Date(2014, 0, 7)\n * })\n * //=> true\n *\n * @example\n * // For the date outside of the interval:\n * isWithinInterval(new Date(2014, 0, 10), {\n *   start: new Date(2014, 0, 1),\n *   end: new Date(2014, 0, 7)\n * })\n * //=> false\n *\n * @example\n * // For date equal to interval start:\n * isWithinInterval(date, { start, end: date }) // => true\n *\n * @example\n * // For date equal to interval end:\n * isWithinInterval(date, { start: date, end }) // => true\n */\nexport default function isWithinInterval(dirtyDate, interval) {\n  requiredArgs(2, arguments);\n  var time = toDate(dirtyDate).getTime();\n  var startTime = toDate(interval.start).getTime();\n  var endTime = toDate(interval.end).getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(startTime <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  return time >= startTime && time <= endTime;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}