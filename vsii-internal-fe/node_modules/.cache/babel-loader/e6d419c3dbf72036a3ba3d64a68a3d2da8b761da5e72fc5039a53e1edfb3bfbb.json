{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"component\", \"componentsProps\", \"max\", \"renderSurplus\", \"slotProps\", \"spacing\", \"total\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { isFragment } from 'react-is';\nimport clsx from 'clsx';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Avatar, { avatarClasses } from '../Avatar';\nimport avatarGroupClasses, { getAvatarGroupUtilityClass } from './avatarGroupClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst SPACINGS = {\n  small: -16,\n  medium: null\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    avatar: ['avatar']\n  };\n  return composeClasses(slots, getAvatarGroupUtilityClass, classes);\n};\nconst AvatarGroupRoot = styled('div', {\n  name: 'MuiAvatarGroup',\n  slot: 'Root',\n  overridesResolver: (props, styles) => _extends({\n    [\"& .\".concat(avatarGroupClasses.avatar)]: styles.avatar\n  }, styles.root)\n})(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  const marginValue = ownerState.spacing && SPACINGS[ownerState.spacing] !== undefined ? SPACINGS[ownerState.spacing] : -ownerState.spacing;\n  return {\n    [\"& .\".concat(avatarClasses.root)]: {\n      border: \"2px solid \".concat((theme.vars || theme).palette.background.default),\n      boxSizing: 'content-box',\n      marginLeft: marginValue != null ? marginValue : -8,\n      '&:last-child': {\n        marginLeft: 0\n      }\n    },\n    display: 'flex',\n    flexDirection: 'row-reverse'\n  };\n});\nconst AvatarGroup = /*#__PURE__*/React.forwardRef(function AvatarGroup(inProps, ref) {\n  var _slotProps$additional;\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAvatarGroup'\n  });\n  const {\n      children: childrenProp,\n      className,\n      component = 'div',\n      componentsProps = {},\n      max = 5,\n      renderSurplus,\n      slotProps = {},\n      spacing = 'medium',\n      total,\n      variant = 'circular'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  let clampedMax = max < 2 ? 2 : max;\n  const ownerState = _extends({}, props, {\n    max,\n    spacing,\n    component,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const children = React.Children.toArray(childrenProp).filter(child => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The AvatarGroup component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    return /*#__PURE__*/React.isValidElement(child);\n  });\n  const totalAvatars = total || children.length;\n  if (totalAvatars === clampedMax) {\n    clampedMax += 1;\n  }\n  clampedMax = Math.min(totalAvatars + 1, clampedMax);\n  const maxAvatars = Math.min(children.length, clampedMax - 1);\n  const extraAvatars = Math.max(totalAvatars - clampedMax, totalAvatars - maxAvatars, 0);\n  const extraAvatarsElement = renderSurplus ? renderSurplus(extraAvatars) : \"+\".concat(extraAvatars);\n  const additionalAvatarSlotProps = (_slotProps$additional = slotProps.additionalAvatar) != null ? _slotProps$additional : componentsProps.additionalAvatar;\n  return /*#__PURE__*/_jsxs(AvatarGroupRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other, {\n    children: [extraAvatars ? /*#__PURE__*/_jsx(Avatar, _extends({\n      variant: variant\n    }, additionalAvatarSlotProps, {\n      className: clsx(classes.avatar, additionalAvatarSlotProps == null ? void 0 : additionalAvatarSlotProps.className),\n      children: extraAvatarsElement\n    })) : null, children.slice(0, maxAvatars).reverse().map(child => {\n      return /*#__PURE__*/React.cloneElement(child, {\n        className: clsx(child.props.className, classes.avatar),\n        variant: child.props.variant || variant\n      });\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? AvatarGroup.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The avatars to stack.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    additionalAvatar: PropTypes.object\n  }),\n  /**\n   * Max avatars to show before +x.\n   * @default 5\n   */\n  max: chainPropTypes(PropTypes.number, props => {\n    if (props.max < 2) {\n      return new Error(['MUI: The prop `max` should be equal to 2 or above.', 'A value below is clamped to 2.'].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * custom renderer of extraAvatars\n   * @param {number} surplus number of extra avatars\n   * @returns {React.ReactNode} custom element to display\n   */\n  renderSurplus: PropTypes.func,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `componentsProps` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    additionalAvatar: PropTypes.object\n  }),\n  /**\n   * Spacing between avatars.\n   * @default 'medium'\n   */\n  spacing: PropTypes.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.number]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The total number of avatars. Used for calculating the number of extra avatars.\n   * @default children.length\n   */\n  total: PropTypes.number,\n  /**\n   * The variant to use.\n   * @default 'circular'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['circular', 'rounded', 'square']), PropTypes.string])\n} : void 0;\nexport default AvatarGroup;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}