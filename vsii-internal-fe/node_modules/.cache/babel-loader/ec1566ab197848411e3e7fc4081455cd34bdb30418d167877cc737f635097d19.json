{"ast": null, "code": "// cheap lodash replacements\n/**\n * drop-in replacement for _.get\n * @param obj\n * @param path\n * @param defaultValue\n */export function get(obj, path, defaultValue) {\n  return path.split('.').reduce((a, c) => a && a[c] ? a[c] : defaultValue || null, obj);\n}\n/**\n * drop-in replacement for _.without\n */\nexport function without(items, item) {\n  return items.filter(i => i !== item);\n}\n/**\n * drop-in replacement for _.isString\n * @param input\n */\nexport function isString(input) {\n  return typeof input === 'string';\n}\n/**\n * drop-in replacement for _.isString\n * @param input\n */\nexport function isObject(input) {\n  return typeof input === 'object';\n}\n/**\n * replacement for _.xor\n * @param itemsA\n * @param itemsB\n */\nexport function xor(itemsA, itemsB) {\n  const map = new Map();\n  const insertItem = item => {\n    map.set(item, map.has(item) ? map.get(item) + 1 : 1);\n  };\n  itemsA.forEach(insertItem);\n  itemsB.forEach(insertItem);\n  const result = [];\n  map.forEach((count, key) => {\n    if (count === 1) {\n      result.push(key);\n    }\n  });\n  return result;\n}\n/**\n * replacement for _.intersection\n * @param itemsA\n * @param itemsB\n */\nexport function intersection(itemsA, itemsB) {\n  return itemsA.filter(t => itemsB.indexOf(t) > -1);\n}\n\n//# sourceMappingURL=js_utils.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}