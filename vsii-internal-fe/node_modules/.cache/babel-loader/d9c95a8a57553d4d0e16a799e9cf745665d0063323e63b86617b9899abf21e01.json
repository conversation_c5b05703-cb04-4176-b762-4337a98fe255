{"ast": null, "code": "// project imports\nimport{Select}from'components/extended/Form';import Api from'constants/Api';import{DEFAULT_VALUE_OPTION,DEFAULT_VALUE_OPTION_SELECT}from'constants/Common';import{useEffect,useState}from'react';import sendRequest from'services/ApiService';import{searchFormConfig}from'./Config';// third party\nimport{FormattedMessage}from'react-intl';import{jsx as _jsx}from\"react/jsx-runtime\";const Title=props=>{const{isShowAll,isShowLabel,name,disabled,isShowTitleName,label}=props;const[titles,setTitles]=useState(isShowAll?[DEFAULT_VALUE_OPTION]:[DEFAULT_VALUE_OPTION_SELECT]);async function getAllTitle(){const response=await sendRequest(Api.master.getAllTitle);if(response!==null&&response!==void 0&&response.status){const{result}=response;result.content.forEach(title=>{let option={value:title.titleCode,label:isShowTitleName?\"[\".concat(title.titleCode,\"] - \").concat(title.titleName):title.titleCode};setTitles(titles=>[...titles,option]);});}else return;}useEffect(()=>{getAllTitle();// eslint-disable-next-line react-hooks/exhaustive-deps\n},[]);return/*#__PURE__*/_jsx(Select,{selects:titles,name:!name?searchFormConfig.titleCode.name:name,disabled:disabled,label:!isShowLabel&&/*#__PURE__*/_jsx(FormattedMessage,{id:label||searchFormConfig.titleCode.label})});};Title.defaultProps={isShowAll:true,isShowTitleName:true};export default Title;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}