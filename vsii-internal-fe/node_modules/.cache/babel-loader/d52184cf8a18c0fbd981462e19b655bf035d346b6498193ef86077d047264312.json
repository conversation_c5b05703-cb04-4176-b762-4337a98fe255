{"ast": null, "code": "import { useMemo } from 'react';\nexport function useConnectDragSource(connector) {\n  return useMemo(() => connector.hooks.dragSource(), [connector]);\n}\nexport function useConnectDragPreview(connector) {\n  return useMemo(() => connector.hooks.dragPreview(), [connector]);\n}\n\n//# sourceMappingURL=connectors.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}