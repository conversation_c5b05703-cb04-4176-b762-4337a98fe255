{"ast": null, "code": "// material-ui\nimport{Table<PERSON>ell,TableHead,TableRow}from'@mui/material';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{FormattedMessage}from'react-intl';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const MonthlyEffortMemberThead=()=>{const{effortbymember}=TEXT_CONFIG_SCREEN.monthlyEffort;return/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:effortbymember+'no'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:effortbymember+'members'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:effortbymember+'member-code'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:effortbymember+'level'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:effortbymember+'department'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:effortbymember+'effort-in-month'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:effortbymember+'difference-hours'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:effortbymember+'projects'})})]})});};export default MonthlyEffortMemberThead;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}