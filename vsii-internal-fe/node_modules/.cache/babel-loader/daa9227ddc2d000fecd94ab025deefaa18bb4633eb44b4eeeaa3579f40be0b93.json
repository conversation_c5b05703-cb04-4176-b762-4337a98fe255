{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/EmailType.tsx\";\nimport { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { searchFormConfig } from './Config';\nimport { Select } from 'components/extended/Form';\nimport { EMAIL_TYPE_OPTIONS, DEFAULT_VALUE_OPTION_SELECT } from 'constants/Common';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EmailType = props => {\n  const {\n    required,\n    handleChange,\n    disabled\n  } = props;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Select, {\n      isMultipleLanguage: true,\n      required: required,\n      disabled: disabled,\n      handleChange: handleChange,\n      selects: [DEFAULT_VALUE_OPTION_SELECT, ...EMAIL_TYPE_OPTIONS],\n      name: searchFormConfig.emailType.name,\n      label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n        id: searchFormConfig.emailType.label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 24\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_c = EmailType;\nexport default EmailType;\nvar _c;\n$RefreshReg$(_c, \"EmailType\");", "map": {"version": 3, "names": ["FormattedMessage", "searchFormConfig", "Select", "EMAIL_TYPE_OPTIONS", "DEFAULT_VALUE_OPTION_SELECT", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EmailType", "props", "required", "handleChange", "disabled", "children", "isMultipleLanguage", "selects", "name", "emailType", "label", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/EmailType.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { searchFormConfig } from './Config';\nimport { Select } from 'components/extended/Form';\nimport { EMAIL_TYPE_OPTIONS, DEFAULT_VALUE_OPTION_SELECT } from 'constants/Common';\nimport { SelectChangeEvent } from '@mui/material';\n\ninterface IEmailTypeProps {\n    required?: boolean;\n    handleChange?: (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => void;\n    disabled?: boolean;\n}\n\nconst EmailType = (props: IEmailTypeProps) => {\n    const { required, handleChange, disabled } = props;\n    return (\n        <>\n            <Select\n                isMultipleLanguage\n                required={required}\n                disabled={disabled}\n                handleChange={handleChange}\n                selects={[DEFAULT_VALUE_OPTION_SELECT, ...EMAIL_TYPE_OPTIONS]}\n                name={searchFormConfig.emailType.name}\n                label={<FormattedMessage id={searchFormConfig.emailType.label} />}\n            />\n        </>\n    );\n};\n\nexport default EmailType;\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,gBAAgB,QAAQ,UAAU;AAC3C,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,kBAAkB,EAAEC,2BAA2B,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASnF,MAAMC,SAAS,GAAIC,KAAsB,IAAK;EAC1C,MAAM;IAAEC,QAAQ;IAAEC,YAAY;IAAEC;EAAS,CAAC,GAAGH,KAAK;EAClD,oBACIJ,OAAA,CAAAE,SAAA;IAAAM,QAAA,eACIR,OAAA,CAACJ,MAAM;MACHa,kBAAkB;MAClBJ,QAAQ,EAAEA,QAAS;MACnBE,QAAQ,EAAEA,QAAS;MACnBD,YAAY,EAAEA,YAAa;MAC3BI,OAAO,EAAE,CAACZ,2BAA2B,EAAE,GAAGD,kBAAkB,CAAE;MAC9Dc,IAAI,EAAEhB,gBAAgB,CAACiB,SAAS,CAACD,IAAK;MACtCE,KAAK,eAAEb,OAAA,CAACN,gBAAgB;QAACoB,EAAE,EAAEnB,gBAAgB,CAACiB,SAAS,CAACC;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE;EAAC,gBACJ,CAAC;AAEX,CAAC;AAACC,EAAA,GAfIhB,SAAS;AAiBf,eAAeA,SAAS;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}