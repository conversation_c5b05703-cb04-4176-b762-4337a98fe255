{"ast": null, "code": "import{useEffect,useState}from'react';import{Grid,Typography}from'@mui/material';import ErrorIcon from'@mui/icons-material/Error';import{FormattedMessage}from'react-intl';import{costAndEffortMonitoringSelector,getCostMonitoringProjectOptionByFixCost}from'store/slice/costAndEffortMonitoringSlice';import{costMonitoringFilterConfig,costMonitoringFilterSchema}from'pages/cost-monitoring/Config';import{TEXT_CONFIG_SCREEN,TEXT_INPUT_COLOR_EFFORT_INCURRED}from'constants/Common';import{Autocomplete,Label}from'components/extended/Form';import{searchFormConfig}from'containers/search/Config';import ColorNoteTooltip from'components/ColorNoteTooltip';import{useAppDispatch,useAppSelector}from'app/hooks';import{SearchForm,Years,Months}from'../search';import{Button}from'components';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const MonthlyCostMonitoringSearch=_ref=>{let{formReset,months,fixCost,handleChangeYear,handleSearch}=_ref;const{projectOptions}=useAppSelector(costAndEffortMonitoringSelector);const[month,setMonth]=useState('');const dispatch=useAppDispatch();const currentMonth=months.find(month=>month.value===costMonitoringFilterConfig.month);const{monthlyMonitoring}=TEXT_CONFIG_SCREEN.costAndEffortMonitoring;const handleChangeMonth=value=>{const getMonth=months.find(month=>month.value===value);if(getMonth){setMonth(getMonth.label);}};useEffect(()=>{dispatch(getCostMonitoringProjectOptionByFixCost({type:'month',value:month?month:currentMonth===null||currentMonth===void 0?void 0:currentMonth.label,fixCost,color:true}));},[month,dispatch,fixCost,currentMonth]);return/*#__PURE__*/_jsx(SearchForm,{defaultValues:costMonitoringFilterConfig,formSchema:costMonitoringFilterSchema,handleSubmit:handleSearch,formReset:formReset,children:/*#__PURE__*/_jsxs(Grid,{container:true,alignItems:\"center\",spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:3,children:/*#__PURE__*/_jsx(Years,{handleChangeYear:handleChangeYear,ignoreDefault:true,label:monthlyMonitoring+'year'})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:3,children:/*#__PURE__*/_jsx(Months,{months:months,onChange:handleChangeMonth,isFilter:true,isShow13MonthSalary:true,year:formReset.year,label:monthlyMonitoring+'month'})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:3,children:/*#__PURE__*/_jsx(Autocomplete,{options:projectOptions,name:searchFormConfig.project.name,label:/*#__PURE__*/_jsxs(Typography,{display:\"flex\",gap:0.5,children:[/*#__PURE__*/_jsx(FormattedMessage,{id:monthlyMonitoring+'project'}),/*#__PURE__*/_jsx(ColorNoteTooltip,{notes:TEXT_INPUT_COLOR_EFFORT_INCURRED,children:/*#__PURE__*/_jsx(ErrorIcon,{sx:{fontSize:15}})})]}),groupBy:option=>option.typeCode,isDefaultAll:true,isDisableClearable:true})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,lg:3,children:[/*#__PURE__*/_jsx(Label,{label:\"\\xA0\"}),/*#__PURE__*/_jsx(Button,{type:\"submit\",size:\"medium\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:monthlyMonitoring+'search'}),variant:\"contained\"})]})]})});};export default MonthlyCostMonitoringSearch;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}