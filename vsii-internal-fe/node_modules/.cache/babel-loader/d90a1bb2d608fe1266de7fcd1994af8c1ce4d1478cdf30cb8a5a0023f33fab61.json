{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/icons/SystemConfig.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SystemConfig = () => {\n  return /*#__PURE__*/_jsxDEV(\"svg\", {\n    width: 20,\n    height: 20,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z\",\n      stroke: \"#292D32\",\n      strokeWidth: \"1.5\",\n      strokeMiterlimit: 10,\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M2 12.88V11.12C2 10.08 2.85 9.22 3.9 9.22C5.71 9.22 6.45 7.94 5.54 6.37C5.02 5.47 5.33 4.3 6.24 3.78L7.97 2.79C8.76 2.32 9.78 2.6 10.25 3.39L10.36 3.58C11.26 5.15 12.74 5.15 13.65 3.58L13.76 3.39C14.23 2.6 15.25 2.32 16.04 2.79L17.77 3.78C18.68 4.3 18.99 5.47 18.47 6.37C17.56 7.94 18.3 9.22 20.11 9.22C21.15 9.22 22.01 10.07 22.01 11.12V12.88C22.01 13.92 21.16 14.78 20.11 14.78C18.3 14.78 17.56 16.06 18.47 17.63C18.99 18.54 18.68 19.7 17.77 20.22L16.04 21.21C15.25 21.68 14.23 21.4 13.76 20.61L13.65 20.42C12.75 18.85 11.27 18.85 10.36 20.42L10.25 20.61C9.78 21.4 8.76 21.68 7.97 21.21L6.24 20.22C5.33 19.7 5.02 18.53 5.54 17.63C6.45 16.06 5.71 14.78 3.9 14.78C2.85 14.78 2 13.92 2 12.88Z\",\n      stroke: \"#292D32\",\n      strokeWidth: \"1.5\",\n      strokeMiterlimit: 10,\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 9\n  }, this);\n};\n_c = SystemConfig;\nexport default SystemConfig;\nvar _c;\n$RefreshReg$(_c, \"SystemConfig\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "SystemConfig", "width", "height", "viewBox", "fill", "xmlns", "children", "d", "stroke", "strokeWidth", "strokeMiterlimit", "strokeLinecap", "strokeLinejoin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/icons/SystemConfig.tsx"], "sourcesContent": ["import React from 'react';\n\nconst SystemConfig = () => {\n    return (\n        <svg width={20} height={20} viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path\n                d=\"M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z\"\n                stroke=\"#292D32\"\n                strokeWidth=\"1.5\"\n                strokeMiterlimit={10}\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n            <path\n                d=\"M2 12.88V11.12C2 10.08 2.85 9.22 3.9 9.22C5.71 9.22 6.45 7.94 5.54 6.37C5.02 5.47 5.33 4.3 6.24 3.78L7.97 2.79C8.76 2.32 9.78 2.6 10.25 3.39L10.36 3.58C11.26 5.15 12.74 5.15 13.65 3.58L13.76 3.39C14.23 2.6 15.25 2.32 16.04 2.79L17.77 3.78C18.68 4.3 18.99 5.47 18.47 6.37C17.56 7.94 18.3 9.22 20.11 9.22C21.15 9.22 22.01 10.07 22.01 11.12V12.88C22.01 13.92 21.16 14.78 20.11 14.78C18.3 14.78 17.56 16.06 18.47 17.63C18.99 18.54 18.68 19.7 17.77 20.22L16.04 21.21C15.25 21.68 14.23 21.4 13.76 20.61L13.65 20.42C12.75 18.85 11.27 18.85 10.36 20.42L10.25 20.61C9.78 21.4 8.76 21.68 7.97 21.21L6.24 20.22C5.33 19.7 5.02 18.53 5.54 17.63C6.45 16.06 5.71 14.78 3.9 14.78C2.85 14.78 2 13.92 2 12.88Z\"\n                stroke=\"#292D32\"\n                strokeWidth=\"1.5\"\n                strokeMiterlimit={10}\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n        </svg>\n    );\n};\n\nexport default SystemConfig;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACvB,oBACID,OAAA;IAAKE,KAAK,EAAE,EAAG;IAACC,MAAM,EAAE,EAAG;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,MAAM;IAACC,KAAK,EAAC,4BAA4B;IAAAC,QAAA,gBAC1FP,OAAA;MACIQ,CAAC,EAAC,mHAAmH;MACrHC,MAAM,EAAC,SAAS;MAChBC,WAAW,EAAC,KAAK;MACjBC,gBAAgB,EAAE,EAAG;MACrBC,aAAa,EAAC,OAAO;MACrBC,cAAc,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eACFjB,OAAA;MACIQ,CAAC,EAAC,qrBAAqrB;MACvrBC,MAAM,EAAC,SAAS;MAChBC,WAAW,EAAC,KAAK;MACjBC,gBAAgB,EAAE,EAAG;MACrBC,aAAa,EAAC,OAAO;MACrBC,cAAc,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACC,EAAA,GArBIjB,YAAY;AAuBlB,eAAeA,YAAY;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}