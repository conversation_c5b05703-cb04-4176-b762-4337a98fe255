{"ast": null, "code": "var castPath = require('./_castPath'),\n  last = require('./last'),\n  parent = require('./_parent'),\n  toKey = require('./_toKey');\n\n/**\n * The base implementation of `_.unset`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The property path to unset.\n * @returns {boolean} Returns `true` if the property is deleted, else `false`.\n */\nfunction baseUnset(object, path) {\n  path = castPath(path, object);\n  object = parent(object, path);\n  return object == null || delete object[toKey(last(path))];\n}\nmodule.exports = baseUnset;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}