{"ast": null, "code": "import React,{useEffect,useState}from'react';// project import\nimport{ManageRankTBody,ManageRankThead}from'containers/administration';import EditRank from'containers/administration/EditRank';import{Table,TableFooter}from'components/extended/Table';import MainCard from'components/cards/MainCard';import Api from'constants/Api';import{SEARCH_PARAM_KEY,paginationParamDefault}from'constants/Common';import sendRequest from'services/ApiService';import{rankValueDefault}from'./Config';import{openSnackbar}from'store/slice/snackbarSlice';import{useAppDispatch,useAppSelector}from'app/hooks';import{getSearchParam,transformObject}from'utils/common';import{getAllRank,rankSelector}from'store/slice/rankSlice';// third party\nimport{useSearchParams}from'react-router-dom';// ==============================|| Manage Rank ||============================== //\n/**\n *  page\n *  size\n */import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const Rank=()=>{var _pagination$totalElem;// URL Params\nconst[searchParams,setSearchParams]=useSearchParams();const keyParams=[SEARCH_PARAM_KEY.page,SEARCH_PARAM_KEY.size];const params=getSearchParam(keyParams,searchParams);transformObject(params);// Hooks, State, Variable\nconst dispatch=useAppDispatch();const[open,setOpen]=useState(false);const[editLoading,setEditLoading]=useState(false);const[conditions,setConditions]=useState({...paginationParamDefault,...params});const[rank,setRank]=useState(rankValueDefault);const{loading,pagination,rank:ranks}=useAppSelector(rankSelector);const postEditRank=async valueRank=>{setEditLoading(true);const response=await sendRequest(Api.rank.postUpdateRank,valueRank);if(response){dispatch(openSnackbar({open:true,message:'update-success',variant:'alert',alert:{color:'success'}}));setEditLoading(false);dispatch(getAllRank({...conditions,page:conditions.page+1}));setRank(rankValueDefault);setOpen(false);}else{setEditLoading(false);}};// Event\nconst handleChangePage=(event,newPage)=>{setConditions({...conditions,page:newPage});setSearchParams({...params,page:newPage});};const handleChangeRowsPerPage=event=>{setConditions({...conditions,page:paginationParamDefault.page,size:parseInt(event.target.value,10)});setSearchParams({...params,page:paginationParamDefault.page,size:parseInt(event.target.value,10)});};// Handle submit\nconst handleOpenDialog=item=>{setRank(item);setOpen(true);};const handleCloseDialog=()=>{setOpen(false);setRank(rankValueDefault);};const handleEditRank=rankEdit=>{postEditRank(rankEdit);};useEffect(()=>{dispatch(getAllRank({...conditions,page:conditions.page+1}));},[dispatch,conditions]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(MainCard,{children:/*#__PURE__*/_jsx(Table,{heads:/*#__PURE__*/_jsx(ManageRankThead,{}),isLoading:loading[getAllRank.typePrefix],data:ranks,children:/*#__PURE__*/_jsx(ManageRankTBody,{pageNumber:conditions.page,pageSize:conditions.size,ranks:ranks,handleOpen:handleOpenDialog})})}),!loading[getAllRank.typePrefix]&&/*#__PURE__*/_jsx(TableFooter,{pagination:{total:(_pagination$totalElem=pagination===null||pagination===void 0?void 0:pagination.totalElement)!==null&&_pagination$totalElem!==void 0?_pagination$totalElem:0,page:conditions.page,size:conditions.size},onPageChange:handleChangePage,onRowsPerPageChange:handleChangeRowsPerPage}),open&&/*#__PURE__*/_jsx(EditRank,{open:open,loading:editLoading,rank:rank,handleClose:handleCloseDialog,handleEditRank:handleEditRank})]});};export default Rank;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}