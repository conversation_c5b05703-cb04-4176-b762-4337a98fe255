{"ast": null, "code": "import React from'react';// project imports\nimport{Select}from'components/extended/Form';import{DEFAULT_VALUE_OPTION,DEFAULT_VALUE_OPTION_SELECT,SALE_PIPELINE_STATUS}from'constants/Common';import{searchFormConfig}from'./Config';// third party\nimport{FormattedMessage}from'react-intl';import{jsx as _jsx}from\"react/jsx-runtime\";const SalePipelineStatus=props=>{const{name,required,isShowAll,disabled,handleChangeStatus,label}=props;return/*#__PURE__*/_jsx(Select,{isMultipleLanguage:true,disabled:disabled,required:required,selects:isShowAll?[DEFAULT_VALUE_OPTION,...SALE_PIPELINE_STATUS]:[DEFAULT_VALUE_OPTION_SELECT,...SALE_PIPELINE_STATUS],name:name,label:/*#__PURE__*/_jsx(FormattedMessage,{id:label?label:searchFormConfig.salePipelineStatus.label}),handleChange:handleChangeStatus});};SalePipelineStatus.defaultProps={name:searchFormConfig.salePipelineStatus.name};export default SalePipelineStatus;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}