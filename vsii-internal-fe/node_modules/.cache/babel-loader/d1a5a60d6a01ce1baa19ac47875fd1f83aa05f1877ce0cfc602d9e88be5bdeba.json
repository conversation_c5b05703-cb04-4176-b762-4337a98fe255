{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/layout/MenuList/NavItem.tsx\",\n  _s = $RefreshSig$();\nimport { forwardRef, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\n\n// material-ui\nimport { Avatar, ButtonBase, Chip, ListItemButton, ListItemIcon, ListItemText, Typography, useMediaQuery } from '@mui/material';\nimport { useTheme } from '@mui/material/styles';\n\n// project imports\nimport { activeID, activeItem, openDrawer } from 'store/slice/menuSlice';\nimport { useAppSelector, useAppDispatch } from 'app/hooks';\nimport useConfig from 'hooks/useConfig';\n\n// assets\nimport { closeDeniedPermission, openDeniedPermission } from 'store/slice/deniedPermissionSlice';\nimport FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { SEARCH_TIMESTAMP } from 'constants/Config';\n\n// ==============================|| SIDEBAR MENU LIST ITEMS ||============================== //\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst NavItem = ({\n  item,\n  level,\n  parentId\n}) => {\n  _s();\n  const theme = useTheme();\n  const matchesSM = useMediaQuery(theme.breakpoints.down('lg'));\n  const dispatch = useAppDispatch();\n  const {\n    pathname\n  } = useLocation();\n  const {\n    borderRadius\n  } = useConfig();\n  const {\n    selectedItem,\n    drawerOpen\n  } = useAppSelector(state => state.menu);\n  const isSelected = selectedItem.findIndex(id => id === item.id) > -1;\n  const Icon = item === null || item === void 0 ? void 0 : item.icon;\n  const itemIcon = item !== null && item !== void 0 && item.icon ? /*#__PURE__*/_jsxDEV(Icon, {\n    stroke: 1.5,\n    size: drawerOpen ? '20px' : '24px',\n    style: {\n      color: isSelected ? theme.palette.secondary.main : theme.palette.text.primary\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 9\n  }, this) : /*#__PURE__*/_jsxDEV(FiberManualRecordIcon, {\n    sx: {\n      color: isSelected ? theme.palette.secondary.main : theme.palette.text.primary,\n      width: selectedItem.findIndex(id => id === (item === null || item === void 0 ? void 0 : item.id)) > -1 ? 8 : 6,\n      height: selectedItem.findIndex(id => id === (item === null || item === void 0 ? void 0 : item.id)) > -1 ? 8 : 6\n    },\n    fontSize: level > 0 ? 'inherit' : 'medium'\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 9\n  }, this);\n  let itemTarget = '_self';\n  if (item.target) {\n    itemTarget = '_blank';\n  }\n  // giữ lại thời gian khi chuyển màn hình ( áp dụng với các màn con trong 1 báo cáo)\n  const getSegment = (url, index) => {\n    const path = url.split('?')[0].split('/');\n    return path[index];\n  };\n  const setUrl = url => {\n    const currentUrlSegment = getSegment(window.location.href, 3);\n    const itemUrlSegment = getSegment(url, 1);\n    const timeSearch = localStorage.getItem(SEARCH_TIMESTAMP);\n    if (currentUrlSegment !== itemUrlSegment) {\n      return url;\n    } else {\n      if (timeSearch) {\n        return `${url}${timeSearch}`;\n      } else {\n        return url;\n      }\n    }\n  };\n  const checkAndRemoveLocalStorage = url => {\n    const currentUrlSegment = getSegment(window.location.href, 3);\n    const itemUrlSegment = getSegment(url, 1);\n    if (currentUrlSegment !== itemUrlSegment) {\n      localStorage.removeItem(SEARCH_TIMESTAMP);\n    }\n  };\n  let listItemProps = {\n    component: /*#__PURE__*/forwardRef((props, ref) => /*#__PURE__*/_jsxDEV(Link, {\n      ref: ref,\n      ...props,\n      to: item.defaultUrl ? item.url : setUrl(item.url),\n      onClick: e => {\n        checkAndRemoveLocalStorage(item.url);\n        if (item.access && !checkAllowedPermission(item.access[0])) {\n          dispatch(openDeniedPermission());\n        } else {\n          dispatch(closeDeniedPermission());\n        }\n      },\n      target: itemTarget\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 13\n    }, this))\n  };\n  if (item !== null && item !== void 0 && item.external) {\n    listItemProps = {\n      component: 'a',\n      href: item.url,\n      target: itemTarget\n    };\n  }\n  const itemHandler = id => {\n    dispatch(activeItem([id]));\n    if (matchesSM) dispatch(openDrawer(false));\n    dispatch(activeID(parentId));\n  };\n\n  // active menu item on page load\n  useEffect(() => {\n    const currentIndex = document.location.pathname.toString().split('/').findIndex(id => id === item.id);\n    if (currentIndex > -1) {\n      dispatch(activeItem([item.id]));\n    }\n    // eslint-disable-next-line\n  }, [pathname]);\n  const textColor = theme.palette.mode === 'dark' ? 'grey.400' : 'text.primary';\n  const iconSelectedColor = theme.palette.mode === 'dark' && drawerOpen ? 'text.primary' : 'secondary.main';\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n      ...listItemProps,\n      disabled: item.disabled,\n      disableRipple: !drawerOpen,\n      sx: {\n        zIndex: 1201,\n        borderRadius: `${borderRadius}px`,\n        mb: 0.5,\n        pl: drawerOpen ? `${level * 24}px` : 1.25,\n        ...(drawerOpen && level === 1 && theme.palette.mode !== 'dark' && {\n          '&:hover': {\n            background: theme.palette.secondary.light\n          },\n          '&.Mui-selected': {\n            background: theme.palette.secondary.light,\n            color: iconSelectedColor,\n            '&:hover': {\n              color: iconSelectedColor,\n              background: theme.palette.secondary.light\n            }\n          }\n        }),\n        ...((!drawerOpen || level !== 1) && {\n          py: level === 1 ? 0 : 1,\n          '&:hover': {\n            bgcolor: 'transparent'\n          },\n          '&.Mui-selected': {\n            '&:hover': {\n              bgcolor: 'transparent'\n            },\n            bgcolor: 'transparent'\n          }\n        })\n      },\n      selected: isSelected,\n      onClick: () => itemHandler(item.id),\n      children: [/*#__PURE__*/_jsxDEV(ButtonBase, {\n        sx: {\n          borderRadius: `${borderRadius}px`\n        },\n        disableRipple: drawerOpen,\n        children: /*#__PURE__*/_jsxDEV(ListItemIcon, {\n          sx: {\n            minWidth: level === 1 ? 30 : 18,\n            color: isSelected ? iconSelectedColor : textColor,\n            ...(!drawerOpen && level === 1 && {\n              borderRadius: `${borderRadius}px`,\n              width: 46,\n              height: 46,\n              alignItems: 'center',\n              justifyContent: 'center',\n              '&:hover': {\n                bgcolor: theme.palette.mode === 'dark' ? theme.palette.secondary.main + 25 : 'secondary.light'\n              },\n              ...(isSelected && {\n                bgcolor: theme.palette.mode === 'dark' ? theme.palette.secondary.main + 25 : 'secondary.light',\n                '&:hover': {\n                  bgcolor: theme.palette.mode === 'dark' ? theme.palette.secondary.main + 30 : 'secondary.light'\n                }\n              })\n            })\n          },\n          children: itemIcon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 17\n      }, this), (drawerOpen || !drawerOpen && level !== 1) && /*#__PURE__*/_jsxDEV(ListItemText, {\n        primary: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: isSelected ? 'h5' : 'body1',\n          color: \"inherit\",\n          sx: {\n            whiteSpace: 'break-spaces'\n          },\n          children: item.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 29\n        }, this),\n        secondary: item.caption && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          sx: {\n            ...theme.typography.subMenuCaption\n          },\n          display: \"block\",\n          gutterBottom: true,\n          children: item.caption\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 33\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 21\n      }, this), drawerOpen && item.chip && /*#__PURE__*/_jsxDEV(Chip, {\n        color: item.chip.color,\n        variant: item.chip.variant,\n        size: item.chip.size,\n        label: item.chip.label,\n        avatar: item.chip.avatar && /*#__PURE__*/_jsxDEV(Avatar, {\n          children: item.chip.avatar\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s(NavItem, \"HrvZ3XNXbRpAFBCOR/ycee4zuMs=\", false, function () {\n  return [useTheme, useMediaQuery, useAppDispatch, useLocation, useConfig, useAppSelector];\n});\n_c = NavItem;\nexport default NavItem;\nvar _c;\n$RefreshReg$(_c, \"NavItem\");", "map": {"version": 3, "names": ["forwardRef", "useEffect", "Link", "useLocation", "Avatar", "ButtonBase", "Chip", "ListItemButton", "ListItemIcon", "ListItemText", "Typography", "useMediaQuery", "useTheme", "activeID", "activeItem", "openDrawer", "useAppSelector", "useAppDispatch", "useConfig", "closeDeniedPermission", "openDeniedPermission", "FiberManualRecordIcon", "checkAllowedPermission", "SEARCH_TIMESTAMP", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "NavItem", "item", "level", "parentId", "_s", "theme", "matchesSM", "breakpoints", "down", "dispatch", "pathname", "borderRadius", "selectedItem", "drawerOpen", "state", "menu", "isSelected", "findIndex", "id", "Icon", "icon", "itemIcon", "stroke", "size", "style", "color", "palette", "secondary", "main", "text", "primary", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "width", "height", "fontSize", "itemTarget", "target", "getSegment", "url", "index", "path", "split", "setUrl", "currentUrlSegment", "window", "location", "href", "itemUrlSegment", "timeSearch", "localStorage", "getItem", "checkAndRemoveLocalStorage", "removeItem", "listItemProps", "component", "props", "ref", "to", "defaultUrl", "onClick", "e", "access", "external", "itemHandler", "currentIndex", "document", "toString", "textColor", "mode", "iconSelectedColor", "children", "disabled", "disable<PERSON><PERSON><PERSON>", "zIndex", "mb", "pl", "background", "light", "py", "bgcolor", "selected", "min<PERSON><PERSON><PERSON>", "alignItems", "justifyContent", "variant", "whiteSpace", "title", "caption", "typography", "subMenuCaption", "display", "gutterBottom", "chip", "label", "avatar", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/layout/MenuList/NavItem.tsx"], "sourcesContent": ["import { ForwardRefExoticComponent, RefAttributes, forwardRef, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\n\n// material-ui\nimport { Avatar, ButtonBase, Chip, ListItemButton, ListItemIcon, ListItemText, Typography, useMediaQuery } from '@mui/material';\nimport { useTheme } from '@mui/material/styles';\n\n// project imports\nimport { activeID, activeItem, openDrawer } from 'store/slice/menuSlice';\nimport { useAppSelector, useAppDispatch } from 'app/hooks';\nimport useConfig from 'hooks/useConfig';\n\n// assets\nimport { closeDeniedPermission, openDeniedPermission } from 'store/slice/deniedPermissionSlice';\nimport FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { SEARCH_TIMESTAMP } from 'constants/Config';\nimport { LinkTarget, NavItemType } from 'types';\n\n// ==============================|| SIDEBAR MENU LIST ITEMS ||============================== //\n\ninterface NavItemProps {\n    item: NavItemType;\n    level: number;\n    parentId: string;\n}\n\nconst NavItem = ({ item, level, parentId }: NavItemProps) => {\n    const theme = useTheme();\n    const matchesSM = useMediaQuery(theme.breakpoints.down('lg'));\n\n    const dispatch = useAppDispatch();\n    const { pathname } = useLocation();\n    const { borderRadius } = useConfig();\n\n    const { selectedItem, drawerOpen } = useAppSelector((state) => state.menu);\n    const isSelected = selectedItem.findIndex((id) => id === item.id) > -1;\n\n    const Icon = item?.icon!;\n    const itemIcon = item?.icon ? (\n        <Icon\n            stroke={1.5}\n            size={drawerOpen ? '20px' : '24px'}\n            style={{ color: isSelected ? theme.palette.secondary.main : theme.palette.text.primary }}\n        />\n    ) : (\n        <FiberManualRecordIcon\n            sx={{\n                color: isSelected ? theme.palette.secondary.main : theme.palette.text.primary,\n                width: selectedItem.findIndex((id) => id === item?.id) > -1 ? 8 : 6,\n                height: selectedItem.findIndex((id) => id === item?.id) > -1 ? 8 : 6\n            }}\n            fontSize={level > 0 ? 'inherit' : 'medium'}\n        />\n    );\n\n    let itemTarget: LinkTarget = '_self';\n    if (item.target) {\n        itemTarget = '_blank';\n    }\n    // giữ lại thời gian khi chuyển màn hình ( áp dụng với các màn con trong 1 báo cáo)\n    const getSegment = (url: string, index: number) => {\n        const path = url.split('?')[0].split('/');\n        return path[index];\n    };\n    const setUrl = (url: any) => {\n        const currentUrlSegment = getSegment(window.location.href, 3);\n        const itemUrlSegment = getSegment(url, 1);\n        const timeSearch = localStorage.getItem(SEARCH_TIMESTAMP);\n        if (currentUrlSegment !== itemUrlSegment) {\n            return url;\n        } else {\n            if (timeSearch) {\n                return `${url}${timeSearch}`;\n            } else {\n                return url;\n            }\n        }\n    };\n    const checkAndRemoveLocalStorage = (url: string) => {\n        const currentUrlSegment = getSegment(window.location.href, 3);\n        const itemUrlSegment = getSegment(url, 1);\n\n        if (currentUrlSegment !== itemUrlSegment) {\n            localStorage.removeItem(SEARCH_TIMESTAMP);\n        }\n    };\n\n    let listItemProps: {\n        component: ForwardRefExoticComponent<RefAttributes<HTMLAnchorElement>> | string;\n        href?: string;\n        target?: LinkTarget;\n    } = {\n        component: forwardRef((props, ref) => (\n            <Link\n                ref={ref}\n                {...props}\n                to={item.defaultUrl ? item.url : setUrl(item.url!)}\n                onClick={(e) => {\n                    checkAndRemoveLocalStorage(item.url!);\n                    if (item.access && !checkAllowedPermission(item.access[0])) {\n                        dispatch(openDeniedPermission());\n                    } else {\n                        dispatch(closeDeniedPermission());\n                    }\n                }}\n                target={itemTarget}\n            />\n        ))\n    };\n    if (item?.external) {\n        listItemProps = { component: 'a', href: item.url, target: itemTarget };\n    }\n\n    const itemHandler = (id: string) => {\n        dispatch(activeItem([id]));\n        if (matchesSM) dispatch(openDrawer(false));\n        dispatch(activeID(parentId));\n    };\n\n    // active menu item on page load\n    useEffect(() => {\n        const currentIndex = document.location.pathname\n            .toString()\n            .split('/')\n            .findIndex((id) => id === item.id);\n        if (currentIndex > -1) {\n            dispatch(activeItem([item.id]));\n        }\n        // eslint-disable-next-line\n    }, [pathname]);\n\n    const textColor = theme.palette.mode === 'dark' ? 'grey.400' : 'text.primary';\n    const iconSelectedColor = theme.palette.mode === 'dark' && drawerOpen ? 'text.primary' : 'secondary.main';\n\n    return (\n        <>\n            <ListItemButton\n                {...listItemProps}\n                disabled={item.disabled}\n                disableRipple={!drawerOpen}\n                sx={{\n                    zIndex: 1201,\n                    borderRadius: `${borderRadius}px`,\n                    mb: 0.5,\n                    pl: drawerOpen ? `${level * 24}px` : 1.25,\n                    ...(drawerOpen &&\n                        level === 1 &&\n                        theme.palette.mode !== 'dark' && {\n                            '&:hover': {\n                                background: theme.palette.secondary.light\n                            },\n                            '&.Mui-selected': {\n                                background: theme.palette.secondary.light,\n                                color: iconSelectedColor,\n                                '&:hover': {\n                                    color: iconSelectedColor,\n                                    background: theme.palette.secondary.light\n                                }\n                            }\n                        }),\n                    ...((!drawerOpen || level !== 1) && {\n                        py: level === 1 ? 0 : 1,\n                        '&:hover': {\n                            bgcolor: 'transparent'\n                        },\n                        '&.Mui-selected': {\n                            '&:hover': {\n                                bgcolor: 'transparent'\n                            },\n                            bgcolor: 'transparent'\n                        }\n                    })\n                }}\n                selected={isSelected}\n                onClick={() => itemHandler(item.id!)}\n            >\n                <ButtonBase sx={{ borderRadius: `${borderRadius}px` }} disableRipple={drawerOpen}>\n                    <ListItemIcon\n                        sx={{\n                            minWidth: level === 1 ? 30 : 18,\n                            color: isSelected ? iconSelectedColor : textColor,\n                            ...(!drawerOpen &&\n                                level === 1 && {\n                                    borderRadius: `${borderRadius}px`,\n                                    width: 46,\n                                    height: 46,\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    '&:hover': {\n                                        bgcolor: theme.palette.mode === 'dark' ? theme.palette.secondary.main + 25 : 'secondary.light'\n                                    },\n                                    ...(isSelected && {\n                                        bgcolor: theme.palette.mode === 'dark' ? theme.palette.secondary.main + 25 : 'secondary.light',\n                                        '&:hover': {\n                                            bgcolor: theme.palette.mode === 'dark' ? theme.palette.secondary.main + 30 : 'secondary.light'\n                                        }\n                                    })\n                                })\n                        }}\n                    >\n                        {itemIcon}\n                    </ListItemIcon>\n                </ButtonBase>\n\n                {(drawerOpen || (!drawerOpen && level !== 1)) && (\n                    <ListItemText\n                        primary={\n                            <Typography variant={isSelected ? 'h5' : 'body1'} color=\"inherit\" sx={{ whiteSpace: 'break-spaces' }}>\n                                {item.title}\n                            </Typography>\n                        }\n                        secondary={\n                            item.caption && (\n                                <Typography variant=\"caption\" sx={{ ...theme.typography.subMenuCaption }} display=\"block\" gutterBottom>\n                                    {item.caption}\n                                </Typography>\n                            )\n                        }\n                    />\n                )}\n\n                {drawerOpen && item.chip && (\n                    <Chip\n                        color={item.chip.color}\n                        variant={item.chip.variant}\n                        size={item.chip.size}\n                        label={item.chip.label}\n                        avatar={item.chip.avatar && <Avatar>{item.chip.avatar}</Avatar>}\n                    />\n                )}\n            </ListItemButton>\n        </>\n    );\n};\n\nexport default NavItem;\n"], "mappings": ";;AAAA,SAAmDA,UAAU,EAAEC,SAAS,QAAQ,OAAO;AACvF,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;;AAEpD;AACA,SAASC,MAAM,EAAEC,UAAU,EAAEC,IAAI,EAAEC,cAAc,EAAEC,YAAY,EAAEC,YAAY,EAAEC,UAAU,EAAEC,aAAa,QAAQ,eAAe;AAC/H,SAASC,QAAQ,QAAQ,sBAAsB;;AAE/C;AACA,SAASC,QAAQ,EAAEC,UAAU,EAAEC,UAAU,QAAQ,uBAAuB;AACxE,SAASC,cAAc,EAAEC,cAAc,QAAQ,WAAW;AAC1D,OAAOC,SAAS,MAAM,iBAAiB;;AAEvC;AACA,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,mCAAmC;AAC/F,OAAOC,qBAAqB,MAAM,uCAAuC;AACzE,SAASC,sBAAsB,QAAQ,qBAAqB;AAC5D,SAASC,gBAAgB,QAAQ,kBAAkB;;AAGnD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQA,MAAMC,OAAO,GAAGA,CAAC;EAAEC,IAAI;EAAEC,KAAK;EAAEC;AAAuB,CAAC,KAAK;EAAAC,EAAA;EACzD,MAAMC,KAAK,GAAGrB,QAAQ,CAAC,CAAC;EACxB,MAAMsB,SAAS,GAAGvB,aAAa,CAACsB,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAE7D,MAAMC,QAAQ,GAAGpB,cAAc,CAAC,CAAC;EACjC,MAAM;IAAEqB;EAAS,CAAC,GAAGnC,WAAW,CAAC,CAAC;EAClC,MAAM;IAAEoC;EAAa,CAAC,GAAGrB,SAAS,CAAC,CAAC;EAEpC,MAAM;IAAEsB,YAAY;IAAEC;EAAW,CAAC,GAAGzB,cAAc,CAAE0B,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC;EAC1E,MAAMC,UAAU,GAAGJ,YAAY,CAACK,SAAS,CAAEC,EAAE,IAAKA,EAAE,KAAKjB,IAAI,CAACiB,EAAE,CAAC,GAAG,CAAC,CAAC;EAEtE,MAAMC,IAAI,GAAGlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,IAAK;EACxB,MAAMC,QAAQ,GAAGpB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEmB,IAAI,gBACvBvB,OAAA,CAACsB,IAAI;IACDG,MAAM,EAAE,GAAI;IACZC,IAAI,EAAEV,UAAU,GAAG,MAAM,GAAG,MAAO;IACnCW,KAAK,EAAE;MAAEC,KAAK,EAAET,UAAU,GAAGX,KAAK,CAACqB,OAAO,CAACC,SAAS,CAACC,IAAI,GAAGvB,KAAK,CAACqB,OAAO,CAACG,IAAI,CAACC;IAAQ;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5F,CAAC,gBAEFrC,OAAA,CAACJ,qBAAqB;IAClB0C,EAAE,EAAE;MACAV,KAAK,EAAET,UAAU,GAAGX,KAAK,CAACqB,OAAO,CAACC,SAAS,CAACC,IAAI,GAAGvB,KAAK,CAACqB,OAAO,CAACG,IAAI,CAACC,OAAO;MAC7EM,KAAK,EAAExB,YAAY,CAACK,SAAS,CAAEC,EAAE,IAAKA,EAAE,MAAKjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,EAAE,EAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;MACnEmB,MAAM,EAAEzB,YAAY,CAACK,SAAS,CAAEC,EAAE,IAAKA,EAAE,MAAKjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,EAAE,EAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG;IACvE,CAAE;IACFoB,QAAQ,EAAEpC,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG;EAAS;IAAA6B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9C,CACJ;EAED,IAAIK,UAAsB,GAAG,OAAO;EACpC,IAAItC,IAAI,CAACuC,MAAM,EAAE;IACbD,UAAU,GAAG,QAAQ;EACzB;EACA;EACA,MAAME,UAAU,GAAGA,CAACC,GAAW,EAAEC,KAAa,KAAK;IAC/C,MAAMC,IAAI,GAAGF,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC;IACzC,OAAOD,IAAI,CAACD,KAAK,CAAC;EACtB,CAAC;EACD,MAAMG,MAAM,GAAIJ,GAAQ,IAAK;IACzB,MAAMK,iBAAiB,GAAGN,UAAU,CAACO,MAAM,CAACC,QAAQ,CAACC,IAAI,EAAE,CAAC,CAAC;IAC7D,MAAMC,cAAc,GAAGV,UAAU,CAACC,GAAG,EAAE,CAAC,CAAC;IACzC,MAAMU,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC3D,gBAAgB,CAAC;IACzD,IAAIoD,iBAAiB,KAAKI,cAAc,EAAE;MACtC,OAAOT,GAAG;IACd,CAAC,MAAM;MACH,IAAIU,UAAU,EAAE;QACZ,OAAO,GAAGV,GAAG,GAAGU,UAAU,EAAE;MAChC,CAAC,MAAM;QACH,OAAOV,GAAG;MACd;IACJ;EACJ,CAAC;EACD,MAAMa,0BAA0B,GAAIb,GAAW,IAAK;IAChD,MAAMK,iBAAiB,GAAGN,UAAU,CAACO,MAAM,CAACC,QAAQ,CAACC,IAAI,EAAE,CAAC,CAAC;IAC7D,MAAMC,cAAc,GAAGV,UAAU,CAACC,GAAG,EAAE,CAAC,CAAC;IAEzC,IAAIK,iBAAiB,KAAKI,cAAc,EAAE;MACtCE,YAAY,CAACG,UAAU,CAAC7D,gBAAgB,CAAC;IAC7C;EACJ,CAAC;EAED,IAAI8D,aAIH,GAAG;IACAC,SAAS,eAAEtF,UAAU,CAAC,CAACuF,KAAK,EAAEC,GAAG,kBAC7B/D,OAAA,CAACvB,IAAI;MACDsF,GAAG,EAAEA,GAAI;MAAA,GACLD,KAAK;MACTE,EAAE,EAAE5D,IAAI,CAAC6D,UAAU,GAAG7D,IAAI,CAACyC,GAAG,GAAGI,MAAM,CAAC7C,IAAI,CAACyC,GAAI,CAAE;MACnDqB,OAAO,EAAGC,CAAC,IAAK;QACZT,0BAA0B,CAACtD,IAAI,CAACyC,GAAI,CAAC;QACrC,IAAIzC,IAAI,CAACgE,MAAM,IAAI,CAACvE,sBAAsB,CAACO,IAAI,CAACgE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;UACxDxD,QAAQ,CAACjB,oBAAoB,CAAC,CAAC,CAAC;QACpC,CAAC,MAAM;UACHiB,QAAQ,CAAClB,qBAAqB,CAAC,CAAC,CAAC;QACrC;MACJ,CAAE;MACFiD,MAAM,EAAED;IAAW;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CACJ;EACL,CAAC;EACD,IAAIjC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEiE,QAAQ,EAAE;IAChBT,aAAa,GAAG;MAAEC,SAAS,EAAE,GAAG;MAAER,IAAI,EAAEjD,IAAI,CAACyC,GAAG;MAAEF,MAAM,EAAED;IAAW,CAAC;EAC1E;EAEA,MAAM4B,WAAW,GAAIjD,EAAU,IAAK;IAChCT,QAAQ,CAACvB,UAAU,CAAC,CAACgC,EAAE,CAAC,CAAC,CAAC;IAC1B,IAAIZ,SAAS,EAAEG,QAAQ,CAACtB,UAAU,CAAC,KAAK,CAAC,CAAC;IAC1CsB,QAAQ,CAACxB,QAAQ,CAACkB,QAAQ,CAAC,CAAC;EAChC,CAAC;;EAED;EACA9B,SAAS,CAAC,MAAM;IACZ,MAAM+F,YAAY,GAAGC,QAAQ,CAACpB,QAAQ,CAACvC,QAAQ,CAC1C4D,QAAQ,CAAC,CAAC,CACVzB,KAAK,CAAC,GAAG,CAAC,CACV5B,SAAS,CAAEC,EAAE,IAAKA,EAAE,KAAKjB,IAAI,CAACiB,EAAE,CAAC;IACtC,IAAIkD,YAAY,GAAG,CAAC,CAAC,EAAE;MACnB3D,QAAQ,CAACvB,UAAU,CAAC,CAACe,IAAI,CAACiB,EAAE,CAAC,CAAC,CAAC;IACnC;IACA;EACJ,CAAC,EAAE,CAACR,QAAQ,CAAC,CAAC;EAEd,MAAM6D,SAAS,GAAGlE,KAAK,CAACqB,OAAO,CAAC8C,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG,cAAc;EAC7E,MAAMC,iBAAiB,GAAGpE,KAAK,CAACqB,OAAO,CAAC8C,IAAI,KAAK,MAAM,IAAI3D,UAAU,GAAG,cAAc,GAAG,gBAAgB;EAEzG,oBACIhB,OAAA,CAAAE,SAAA;IAAA2E,QAAA,eACI7E,OAAA,CAAClB,cAAc;MAAA,GACP8E,aAAa;MACjBkB,QAAQ,EAAE1E,IAAI,CAAC0E,QAAS;MACxBC,aAAa,EAAE,CAAC/D,UAAW;MAC3BsB,EAAE,EAAE;QACA0C,MAAM,EAAE,IAAI;QACZlE,YAAY,EAAE,GAAGA,YAAY,IAAI;QACjCmE,EAAE,EAAE,GAAG;QACPC,EAAE,EAAElE,UAAU,GAAG,GAAGX,KAAK,GAAG,EAAE,IAAI,GAAG,IAAI;QACzC,IAAIW,UAAU,IACVX,KAAK,KAAK,CAAC,IACXG,KAAK,CAACqB,OAAO,CAAC8C,IAAI,KAAK,MAAM,IAAI;UAC7B,SAAS,EAAE;YACPQ,UAAU,EAAE3E,KAAK,CAACqB,OAAO,CAACC,SAAS,CAACsD;UACxC,CAAC;UACD,gBAAgB,EAAE;YACdD,UAAU,EAAE3E,KAAK,CAACqB,OAAO,CAACC,SAAS,CAACsD,KAAK;YACzCxD,KAAK,EAAEgD,iBAAiB;YACxB,SAAS,EAAE;cACPhD,KAAK,EAAEgD,iBAAiB;cACxBO,UAAU,EAAE3E,KAAK,CAACqB,OAAO,CAACC,SAAS,CAACsD;YACxC;UACJ;QACJ,CAAC,CAAC;QACN,IAAI,CAAC,CAACpE,UAAU,IAAIX,KAAK,KAAK,CAAC,KAAK;UAChCgF,EAAE,EAAEhF,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;UACvB,SAAS,EAAE;YACPiF,OAAO,EAAE;UACb,CAAC;UACD,gBAAgB,EAAE;YACd,SAAS,EAAE;cACPA,OAAO,EAAE;YACb,CAAC;YACDA,OAAO,EAAE;UACb;QACJ,CAAC;MACL,CAAE;MACFC,QAAQ,EAAEpE,UAAW;MACrB+C,OAAO,EAAEA,CAAA,KAAMI,WAAW,CAAClE,IAAI,CAACiB,EAAG,CAAE;MAAAwD,QAAA,gBAErC7E,OAAA,CAACpB,UAAU;QAAC0D,EAAE,EAAE;UAAExB,YAAY,EAAE,GAAGA,YAAY;QAAK,CAAE;QAACiE,aAAa,EAAE/D,UAAW;QAAA6D,QAAA,eAC7E7E,OAAA,CAACjB,YAAY;UACTuD,EAAE,EAAE;YACAkD,QAAQ,EAAEnF,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE;YAC/BuB,KAAK,EAAET,UAAU,GAAGyD,iBAAiB,GAAGF,SAAS;YACjD,IAAI,CAAC1D,UAAU,IACXX,KAAK,KAAK,CAAC,IAAI;cACXS,YAAY,EAAE,GAAGA,YAAY,IAAI;cACjCyB,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACViD,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxB,SAAS,EAAE;gBACPJ,OAAO,EAAE9E,KAAK,CAACqB,OAAO,CAAC8C,IAAI,KAAK,MAAM,GAAGnE,KAAK,CAACqB,OAAO,CAACC,SAAS,CAACC,IAAI,GAAG,EAAE,GAAG;cACjF,CAAC;cACD,IAAIZ,UAAU,IAAI;gBACdmE,OAAO,EAAE9E,KAAK,CAACqB,OAAO,CAAC8C,IAAI,KAAK,MAAM,GAAGnE,KAAK,CAACqB,OAAO,CAACC,SAAS,CAACC,IAAI,GAAG,EAAE,GAAG,iBAAiB;gBAC9F,SAAS,EAAE;kBACPuD,OAAO,EAAE9E,KAAK,CAACqB,OAAO,CAAC8C,IAAI,KAAK,MAAM,GAAGnE,KAAK,CAACqB,OAAO,CAACC,SAAS,CAACC,IAAI,GAAG,EAAE,GAAG;gBACjF;cACJ,CAAC;YACL,CAAC;UACT,CAAE;UAAA8C,QAAA,EAEDrD;QAAQ;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,EAEZ,CAACrB,UAAU,IAAK,CAACA,UAAU,IAAIX,KAAK,KAAK,CAAE,kBACxCL,OAAA,CAAChB,YAAY;QACTiD,OAAO,eACHjC,OAAA,CAACf,UAAU;UAAC0G,OAAO,EAAExE,UAAU,GAAG,IAAI,GAAG,OAAQ;UAACS,KAAK,EAAC,SAAS;UAACU,EAAE,EAAE;YAAEsD,UAAU,EAAE;UAAe,CAAE;UAAAf,QAAA,EAChGzE,IAAI,CAACyF;QAAK;UAAA3D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACf;QACDP,SAAS,EACL1B,IAAI,CAAC0F,OAAO,iBACR9F,OAAA,CAACf,UAAU;UAAC0G,OAAO,EAAC,SAAS;UAACrD,EAAE,EAAE;YAAE,GAAG9B,KAAK,CAACuF,UAAU,CAACC;UAAe,CAAE;UAACC,OAAO,EAAC,OAAO;UAACC,YAAY;UAAArB,QAAA,EACjGzE,IAAI,CAAC0F;QAAO;UAAA5D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAEnB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACJ,EAEArB,UAAU,IAAIZ,IAAI,CAAC+F,IAAI,iBACpBnG,OAAA,CAACnB,IAAI;QACD+C,KAAK,EAAExB,IAAI,CAAC+F,IAAI,CAACvE,KAAM;QACvB+D,OAAO,EAAEvF,IAAI,CAAC+F,IAAI,CAACR,OAAQ;QAC3BjE,IAAI,EAAEtB,IAAI,CAAC+F,IAAI,CAACzE,IAAK;QACrB0E,KAAK,EAAEhG,IAAI,CAAC+F,IAAI,CAACC,KAAM;QACvBC,MAAM,EAAEjG,IAAI,CAAC+F,IAAI,CAACE,MAAM,iBAAIrG,OAAA,CAACrB,MAAM;UAAAkG,QAAA,EAAEzE,IAAI,CAAC+F,IAAI,CAACE;QAAM;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CACJ;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW;EAAC,gBACnB,CAAC;AAEX,CAAC;AAAC9B,EAAA,CA/MIJ,OAAO;EAAA,QACKhB,QAAQ,EACJD,aAAa,EAEdM,cAAc,EACVd,WAAW,EACPe,SAAS,EAEGF,cAAc;AAAA;AAAA+G,EAAA,GARjDnG,OAAO;AAiNb,eAAeA,OAAO;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}