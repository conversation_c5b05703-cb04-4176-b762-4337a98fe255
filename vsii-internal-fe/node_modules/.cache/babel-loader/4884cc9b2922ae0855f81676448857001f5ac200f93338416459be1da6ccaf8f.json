{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport SpeedDialIcon from '@mui/material/SpeedDialIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n/**\n * @ignore - do not document.\n */\n\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedSpeedDialIcon(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The SpeedDialIcon component was moved from the lab to the core.', '', \"You should use `import { SpeedDialIcon } from '@mui/material'`\", \"or `import SpeedDialIcon from '@mui/material/SpeedDialIcon'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(SpeedDialIcon, _extends({\n    ref: ref\n  }, props));\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}