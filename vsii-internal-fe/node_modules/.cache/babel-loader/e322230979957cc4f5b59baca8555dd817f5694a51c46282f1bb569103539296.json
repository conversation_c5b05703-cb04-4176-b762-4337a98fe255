{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/monthly-effort/MonthlyEffortProjectReportThead.tsx\",\n  _s = $RefreshSig$();\nimport { useCallback, useState } from 'react';\n\n// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme } from '@mui/material/styles';\nimport { FormattedMessage } from 'react-intl';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction MonthlyEffortProjectReportThead({\n  currentYear,\n  projectLength\n}) {\n  _s();\n  const [projectCell, setProjectCell] = useState(null);\n  const [deptIdCell, setDeptIdCell] = useState(null);\n  const theme = useTheme();\n  const {\n    project_report\n  } = TEXT_CONFIG_SCREEN.generalReport;\n  const projectCellRef = useCallback(domNode => {\n    if (projectLength > 0 && domNode) {\n      setProjectCell(domNode.getBoundingClientRect());\n    }\n  }, [projectLength]);\n  const deptIdRef = useCallback(domNode => {\n    if (domNode) {\n      setDeptIdCell(domNode.getBoundingClientRect());\n    }\n  }, []);\n  const matches = useMediaQuery(theme.breakpoints.up('md'));\n  return /*#__PURE__*/_jsxDEV(TableHead, {\n    children: /*#__PURE__*/_jsxDEV(TableRow, {\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        ref: projectCellRef,\n        sx: {\n          left: !!matches ? 0 : 'unset',\n          zIndex: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: project_report + 'no'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"left\",\n        ref: deptIdRef,\n        sx: {\n          left: !!matches ? projectLength > 0 ? projectCell === null || projectCell === void 0 ? void 0 : projectCell.width : 'unset' : 'unset',\n          minWidth: '200px',\n          zIndex: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: project_report + 'project-name'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          left: !!matches ? projectLength > 0 && (projectCell === null || projectCell === void 0 ? void 0 : projectCell.width) + (deptIdCell === null || deptIdCell === void 0 ? void 0 : deptIdCell.width) : 'unset',\n          zIndex: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: project_report + 'project-manager'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: project_report + 'project-implementation-phase'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        rowSpan: 3,\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: project_report + 'project-progress-assessment'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        rowSpan: 3,\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: project_report + 'dept'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        rowSpan: 3,\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: project_report + 'project-type'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        rowSpan: 2,\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: project_report + 'start-date'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        rowSpan: 2,\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: project_report + 'end-date'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        rowSpan: 2,\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: project_report + 'user-update'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        rowSpan: 2,\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: project_report + 'last-update'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        rowSpan: 2,\n        align: \"center\",\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: project_report + 'actions'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 9\n  }, this);\n}\n_s(MonthlyEffortProjectReportThead, \"LyDo70oh9aMcMhlcN6XkfKWBhhY=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c = MonthlyEffortProjectReportThead;\nexport default MonthlyEffortProjectReportThead;\nvar _c;\n$RefreshReg$(_c, \"MonthlyEffortProjectReportThead\");", "map": {"version": 3, "names": ["useCallback", "useState", "TableCell", "TableHead", "TableRow", "useMediaQuery", "useTheme", "FormattedMessage", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "MonthlyEffortProjectReportThead", "currentYear", "projectLength", "_s", "projectCell", "setProjectCell", "deptIdCell", "setDeptIdCell", "theme", "project_report", "general<PERSON><PERSON><PERSON>", "projectCellRef", "domNode", "getBoundingClientRect", "deptIdRef", "matches", "breakpoints", "up", "children", "ref", "sx", "left", "zIndex", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "align", "width", "min<PERSON><PERSON><PERSON>", "rowSpan", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/monthly-effort/MonthlyEffortProjectReportThead.tsx"], "sourcesContent": ["import { useCallback, useState } from 'react';\r\n\r\n// material-ui\r\nimport { TableCell, TableHead, TableRow } from '@mui/material';\r\nimport useMediaQuery from '@mui/material/useMediaQuery';\r\nimport { useTheme } from '@mui/material/styles';\r\nimport { FormattedMessage } from 'react-intl';\r\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\r\n\r\ninterface IMonthlyEffortProjectReportTheadProps {\r\n    currentYear: number;\r\n    projectLength: number;\r\n}\r\n\r\nfunction MonthlyEffortProjectReportThead({ currentYear, projectLength }: IMonthlyEffortProjectReportTheadProps) {\r\n    const [projectCell, setProjectCell] = useState<any>(null);\r\n    const [deptIdCell, setDeptIdCell] = useState<any>(null);\r\n    const theme = useTheme();\r\n\r\n    const { project_report } = TEXT_CONFIG_SCREEN.generalReport;\r\n\r\n    const projectCellRef = useCallback(\r\n        (domNode: any) => {\r\n            if (projectLength > 0 && domNode) {\r\n                setProjectCell(domNode.getBoundingClientRect());\r\n            }\r\n        },\r\n        [projectLength]\r\n    );\r\n\r\n    const deptIdRef = useCallback((domNode: any) => {\r\n        if (domNode) {\r\n            setDeptIdCell(domNode.getBoundingClientRect());\r\n        }\r\n    }, []);\r\n\r\n    const matches = useMediaQuery(theme.breakpoints.up('md'));\r\n    return (\r\n        <TableHead>\r\n            <TableRow>\r\n                <TableCell ref={projectCellRef} sx={{ left: !!matches ? 0 : 'unset', zIndex: 3 }}>\r\n                    <FormattedMessage id={project_report + 'no'} />\r\n                </TableCell>\r\n                <TableCell\r\n                    align=\"left\"\r\n                    ref={deptIdRef}\r\n                    sx={{ left: !!matches ? (projectLength > 0 ? projectCell?.width : 'unset') : 'unset', minWidth: '200px', zIndex: 3 }}\r\n                >\r\n                    <FormattedMessage id={project_report + 'project-name'} />\r\n                </TableCell>\r\n                <TableCell sx={{ left: !!matches ? projectLength > 0 && projectCell?.width + deptIdCell?.width : 'unset', zIndex: 3 }}>\r\n                    <FormattedMessage id={project_report + 'project-manager'} />\r\n                </TableCell>\r\n                <TableCell>\r\n                    <FormattedMessage id={project_report + 'project-implementation-phase'} />\r\n                </TableCell>\r\n                <TableCell rowSpan={3}>\r\n                    <FormattedMessage id={project_report + 'project-progress-assessment'} />\r\n                </TableCell>\r\n                <TableCell rowSpan={3}>\r\n                    <FormattedMessage id={project_report + 'dept'} />\r\n                </TableCell>\r\n                <TableCell rowSpan={3}>\r\n                    <FormattedMessage id={project_report + 'project-type'} />\r\n                </TableCell>\r\n                <TableCell rowSpan={2}>\r\n                    <FormattedMessage id={project_report + 'start-date'} />\r\n                </TableCell>\r\n                <TableCell rowSpan={2}>\r\n                    <FormattedMessage id={project_report + 'end-date'} />\r\n                </TableCell>\r\n\r\n                <TableCell rowSpan={2}>\r\n                    <FormattedMessage id={project_report + 'user-update'} />\r\n                </TableCell>\r\n                <TableCell rowSpan={2}>\r\n                    <FormattedMessage id={project_report + 'last-update'} />\r\n                </TableCell>\r\n                <TableCell rowSpan={2} align=\"center\">\r\n                    <FormattedMessage id={project_report + 'actions'} />\r\n                </TableCell>\r\n            </TableRow>\r\n        </TableHead>\r\n    );\r\n}\r\nexport default MonthlyEffortProjectReportThead;\r\n"], "mappings": ";;AAAA,SAASA,WAAW,EAAEC,QAAQ,QAAQ,OAAO;;AAE7C;AACA,SAASC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC9D,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOtD,SAASC,+BAA+BA,CAAC;EAAEC,WAAW;EAAEC;AAAqD,CAAC,EAAE;EAAAC,EAAA;EAC5G,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAM,IAAI,CAAC;EACzD,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAM,IAAI,CAAC;EACvD,MAAMkB,KAAK,GAAGb,QAAQ,CAAC,CAAC;EAExB,MAAM;IAAEc;EAAe,CAAC,GAAGZ,kBAAkB,CAACa,aAAa;EAE3D,MAAMC,cAAc,GAAGtB,WAAW,CAC7BuB,OAAY,IAAK;IACd,IAAIV,aAAa,GAAG,CAAC,IAAIU,OAAO,EAAE;MAC9BP,cAAc,CAACO,OAAO,CAACC,qBAAqB,CAAC,CAAC,CAAC;IACnD;EACJ,CAAC,EACD,CAACX,aAAa,CAClB,CAAC;EAED,MAAMY,SAAS,GAAGzB,WAAW,CAAEuB,OAAY,IAAK;IAC5C,IAAIA,OAAO,EAAE;MACTL,aAAa,CAACK,OAAO,CAACC,qBAAqB,CAAC,CAAC,CAAC;IAClD;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,OAAO,GAAGrB,aAAa,CAACc,KAAK,CAACQ,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,CAAC;EACzD,oBACIlB,OAAA,CAACP,SAAS;IAAA0B,QAAA,eACNnB,OAAA,CAACN,QAAQ;MAAAyB,QAAA,gBACLnB,OAAA,CAACR,SAAS;QAAC4B,GAAG,EAAER,cAAe;QAACS,EAAE,EAAE;UAAEC,IAAI,EAAE,CAAC,CAACN,OAAO,GAAG,CAAC,GAAG,OAAO;UAAEO,MAAM,EAAE;QAAE,CAAE;QAAAJ,QAAA,eAC7EnB,OAAA,CAACH,gBAAgB;UAAC2B,EAAE,EAAEd,cAAc,GAAG;QAAK;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACZ5B,OAAA,CAACR,SAAS;QACNqC,KAAK,EAAC,MAAM;QACZT,GAAG,EAAEL,SAAU;QACfM,EAAE,EAAE;UAAEC,IAAI,EAAE,CAAC,CAACN,OAAO,GAAIb,aAAa,GAAG,CAAC,GAAGE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEyB,KAAK,GAAG,OAAO,GAAI,OAAO;UAAEC,QAAQ,EAAE,OAAO;UAAER,MAAM,EAAE;QAAE,CAAE;QAAAJ,QAAA,eAErHnB,OAAA,CAACH,gBAAgB;UAAC2B,EAAE,EAAEd,cAAc,GAAG;QAAe;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,eACZ5B,OAAA,CAACR,SAAS;QAAC6B,EAAE,EAAE;UAAEC,IAAI,EAAE,CAAC,CAACN,OAAO,GAAGb,aAAa,GAAG,CAAC,IAAI,CAAAE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEyB,KAAK,KAAGvB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEuB,KAAK,IAAG,OAAO;UAAEP,MAAM,EAAE;QAAE,CAAE;QAAAJ,QAAA,eAClHnB,OAAA,CAACH,gBAAgB;UAAC2B,EAAE,EAAEd,cAAc,GAAG;QAAkB;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eACZ5B,OAAA,CAACR,SAAS;QAAA2B,QAAA,eACNnB,OAAA,CAACH,gBAAgB;UAAC2B,EAAE,EAAEd,cAAc,GAAG;QAA+B;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACZ5B,OAAA,CAACR,SAAS;QAACwC,OAAO,EAAE,CAAE;QAAAb,QAAA,eAClBnB,OAAA,CAACH,gBAAgB;UAAC2B,EAAE,EAAEd,cAAc,GAAG;QAA8B;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,eACZ5B,OAAA,CAACR,SAAS;QAACwC,OAAO,EAAE,CAAE;QAAAb,QAAA,eAClBnB,OAAA,CAACH,gBAAgB;UAAC2B,EAAE,EAAEd,cAAc,GAAG;QAAO;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACZ5B,OAAA,CAACR,SAAS;QAACwC,OAAO,EAAE,CAAE;QAAAb,QAAA,eAClBnB,OAAA,CAACH,gBAAgB;UAAC2B,EAAE,EAAEd,cAAc,GAAG;QAAe;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,eACZ5B,OAAA,CAACR,SAAS;QAACwC,OAAO,EAAE,CAAE;QAAAb,QAAA,eAClBnB,OAAA,CAACH,gBAAgB;UAAC2B,EAAE,EAAEd,cAAc,GAAG;QAAa;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACZ5B,OAAA,CAACR,SAAS;QAACwC,OAAO,EAAE,CAAE;QAAAb,QAAA,eAClBnB,OAAA,CAACH,gBAAgB;UAAC2B,EAAE,EAAEd,cAAc,GAAG;QAAW;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eAEZ5B,OAAA,CAACR,SAAS;QAACwC,OAAO,EAAE,CAAE;QAAAb,QAAA,eAClBnB,OAAA,CAACH,gBAAgB;UAAC2B,EAAE,EAAEd,cAAc,GAAG;QAAc;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eACZ5B,OAAA,CAACR,SAAS;QAACwC,OAAO,EAAE,CAAE;QAAAb,QAAA,eAClBnB,OAAA,CAACH,gBAAgB;UAAC2B,EAAE,EAAEd,cAAc,GAAG;QAAc;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eACZ5B,OAAA,CAACR,SAAS;QAACwC,OAAO,EAAE,CAAE;QAACH,KAAK,EAAC,QAAQ;QAAAV,QAAA,eACjCnB,OAAA,CAACH,gBAAgB;UAAC2B,EAAE,EAAEd,cAAc,GAAG;QAAU;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEpB;AAACxB,EAAA,CAtEQH,+BAA+B;EAAA,QAGtBL,QAAQ,EAmBND,aAAa;AAAA;AAAAsC,EAAA,GAtBxBhC,+BAA+B;AAuExC,eAAeA,+BAA+B;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}