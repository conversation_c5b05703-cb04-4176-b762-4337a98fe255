{"ast": null, "code": "/* eslint-disable */\nexport const REGEX_CONSTANTS = {\n  REGEX_PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[^a-zA-Z\\d])\\S{8,24}$/,\n  REGEX_EMAIL: /^\\w+([\\.-]?\\w+)*@\\w+([\\.-]?\\w+)*(\\.\\w{2,3})+$/,\n  REGEX_NUMBER: /^\\d+$/,\n  REGEX_NUMBER_AND_DECIMAL: /^[0-9]\\d*(\\.\\d+)?$/,\n  REGEX_PHONE_NUMBER: /^((\\+([1-9]{1,2})?[-. ]?(\\(?([0-9]{3})\\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})))|(((0[1-9]{1}){1})[0-9-. ]{8,12}))$/g,\n  REGEX_SPECIAL_CHARACTERS: /^[_a-zA-Z0-9\\s\\d]*$/,\n  REGEX_SPECIAL_CHARACTERS_NOT_SPACE: /^[_a-zA-Z0-9\\d]*$/,\n  REGEX_NO_NUMBER: /^[^\\d]*$/,\n  REGEX_NAME: /^[a-zA-Z0-9().,&:;@_ -]+$/,\n  REGEX_NAME_CODE: /^[a-zA-Z0-9\\s\\d]*([!@#$%^&*()_ -+=`~\\[\\]{}\\|;:'\",<.>?/][a-zA-Z0-9\\s\\d]*)?$/,\n  REGEX_NAME_FILE: /\\.xlsx$/\n};", "map": {"version": 3, "names": ["REGEX_CONSTANTS", "REGEX_PASSWORD", "REGEX_EMAIL", "REGEX_NUMBER", "REGEX_NUMBER_AND_DECIMAL", "REGEX_PHONE_NUMBER", "REGEX_SPECIAL_CHARACTERS", "REGEX_SPECIAL_CHARACTERS_NOT_SPACE", "REGEX_NO_NUMBER", "REGEX_NAME", "REGEX_NAME_CODE", "REGEX_NAME_FILE"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/constants/Validation.ts"], "sourcesContent": ["/* eslint-disable */\nexport const REGEX_CONSTANTS = {\n    REGEX_PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[^a-zA-Z\\d])\\S{8,24}$/,\n    REGEX_EMAIL: /^\\w+([\\.-]?\\w+)*@\\w+([\\.-]?\\w+)*(\\.\\w{2,3})+$/,\n    REGEX_NUMBER: /^\\d+$/,\n    REGEX_NUMBER_AND_DECIMAL: /^[0-9]\\d*(\\.\\d+)?$/,\n    REGEX_PHONE_NUMBER: /^((\\+([1-9]{1,2})?[-. ]?(\\(?([0-9]{3})\\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})))|(((0[1-9]{1}){1})[0-9-. ]{8,12}))$/g,\n    REGEX_SPECIAL_CHARACTERS: /^[_a-zA-Z0-9\\s\\d]*$/,\n    REGEX_SPECIAL_CHARACTERS_NOT_SPACE: /^[_a-zA-Z0-9\\d]*$/,\n    REGEX_NO_NUMBER: /^[^\\d]*$/,\n    REGEX_NAME: /^[a-zA-Z0-9().,&:;@_ -]+$/,\n    REGEX_NAME_CODE: /^[a-zA-Z0-9\\s\\d]*([!@#$%^&*()_ -+=`~\\[\\]{}\\|;:'\",<.>?/][a-zA-Z0-9\\s\\d]*)?$/,\n    REGEX_NAME_FILE: /\\.xlsx$/\n};\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,eAAe,GAAG;EAC3BC,cAAc,EAAE,2DAA2D;EAC3EC,WAAW,EAAE,+CAA+C;EAC5DC,YAAY,EAAE,OAAO;EACrBC,wBAAwB,EAAE,oBAAoB;EAC9CC,kBAAkB,EAAE,iHAAiH;EACrIC,wBAAwB,EAAE,qBAAqB;EAC/CC,kCAAkC,EAAE,mBAAmB;EACvDC,eAAe,EAAE,UAAU;EAC3BC,UAAU,EAAE,2BAA2B;EACvCC,eAAe,EAAE,4EAA4E;EAC7FC,eAAe,EAAE;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}