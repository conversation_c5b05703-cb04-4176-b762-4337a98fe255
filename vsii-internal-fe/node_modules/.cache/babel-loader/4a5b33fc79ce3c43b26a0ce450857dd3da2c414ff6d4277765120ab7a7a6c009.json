{"ast": null, "code": "// material-ui\nimport{TableCell,TableHead,TableRow}from'@mui/material';// third party\nimport{FormattedMessage}from'react-intl';// project imports\nimport{PERMISSIONS}from'constants/Permission';import{checkAllowedPermission}from'utils/authorization';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const ManageLeavesTHead=()=>{const{manageLeaves}=PERMISSIONS.workingCalendar;const{manage_leaves}=TEXT_CONFIG_SCREEN.workingCalendar;return/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_leaves+'no'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_leaves+'members'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_leaves+'approver'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_leaves+'dept'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_leaves+'leaves-type'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_leaves+'from-date'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_leaves+'to-date'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_leaves+'status'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_leaves+'approved-date'})}),checkAllowedPermission(manageLeaves.approve)||checkAllowedPermission(manageLeaves.edit)?/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_leaves+'actions'})}):/*#__PURE__*/_jsx(_Fragment,{})]})});};export default ManageLeavesTHead;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}