{"ast": null, "code": "'use strict';\n\n// do not edit .js files directly - edit src/index.jst\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;) if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n    for (i = length; i-- !== 0;) if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n      if (!equal(a[key], b[key])) return false;\n    }\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a !== a && b !== b;\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}