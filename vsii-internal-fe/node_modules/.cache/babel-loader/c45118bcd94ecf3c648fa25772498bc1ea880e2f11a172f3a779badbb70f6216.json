{"ast": null, "code": "export const ROUTER = {\n  home: {\n    index: '/'\n  },\n  authentication: {\n    login: 'login',\n    confirmEmail: 'confirm-email',\n    register: 'register',\n    forgot: 'forgot',\n    forgotPassword: 'forgot-password'\n  },\n  administration: {\n    manage_user: 'manage-user',\n    manage_project: 'manage-project',\n    manage_holiday: 'manage-holiday',\n    manage_special_hours: 'manage-special-hours',\n    manage_group: 'manage-group',\n    system_config: 'system-config',\n    manage_rank: 'manage-rank',\n    email_config: 'email-config',\n    cv_config: 'cv-config',\n    exchange_rate_config: 'exchange-rate-config',\n    manage_department: 'manage-department',\n    project_type_config: 'project-type-config',\n    title_config: 'title-config',\n    non_billables_config: 'non-billables-config',\n    flexible_reporting: {\n      index: 'flexible-reporting',\n      column_config: 'column-config',\n      text_config: 'text-config',\n      flexible_reporting_config: 'flexible-reporting-config'\n    }\n  },\n  workingCalendar: {\n    register_working_calendar: 'register-working-calendar',\n    manage_leave_days: 'manage-leave-days',\n    manage_leaves: 'manage-leaves',\n    manage_ot: 'manage-ot-requests',\n    manage_resignation: 'manage-resignation',\n    manage_leave_requests: 'manage-leave-requests'\n  },\n  reports: {\n    weekly_effort: 'weekly-effort',\n    monthly_effort: {\n      index: 'monthly-effort',\n      summary: 'summary',\n      project: 'project',\n      department_member: 'department-member'\n    },\n    general_report: {\n      index: 'general-report',\n      product: 'product-report',\n      project_report: 'project-report',\n      orm_report: 'orm-report'\n    },\n    non_billable_monitoring: {\n      index: 'non-billable-monitoring',\n      non_billable_by_member: 'non-billable-by-member',\n      non_billable_cost_by_week: 'nonbill-ratio-chart'\n    },\n    resources_in_project: 'resources-in-project',\n    cost_monitoring: {\n      index: 'cost-monitoring',\n      weekly_cost: 'weekly-monitoring',\n      monthly_cost: 'monthly-monitoring'\n    },\n    monthly_project_cost: {\n      index: 'monthly-project-cost',\n      summary: 'summary',\n      detail_report_by_month: 'detail-report-by-month',\n      monthly_cost_data: 'monthly-cost-data'\n    },\n    sales: {\n      index: 'sales',\n      monthly_production_performance: 'monthly-production-performance',\n      sales_lead: 'sales-lead',\n      monitor_bidding_package: 'monitor-bidding-package',\n      project_reference: 'project-reference',\n      sales_pipeline: {\n        index: 'sales-pipeline',\n        summary: 'summary',\n        on_going: 'on-going',\n        bidding: 'all',\n        budgeting_plan: 'budgeting-plan'\n      }\n    },\n    skills_manage: {\n      index: 'manage-skills',\n      skills_update: 'skills-update',\n      skills_report: 'skills-report',\n      cv: 'cv'\n    }\n  }\n};", "map": {"version": 3, "names": ["ROUTER", "home", "index", "authentication", "login", "confirmEmail", "register", "forgot", "forgotPassword", "administration", "manage_user", "manage_project", "manage_holiday", "manage_special_hours", "manage_group", "system_config", "manage_rank", "email_config", "cv_config", "exchange_rate_config", "manage_department", "project_type_config", "title_config", "non_billables_config", "flexible_reporting", "column_config", "text_config", "flexible_reporting_config", "workingCalendar", "register_working_calendar", "manage_leave_days", "manage_leaves", "manage_ot", "manage_resignation", "manage_leave_requests", "reports", "weekly_effort", "monthly_effort", "summary", "project", "department_member", "general_report", "product", "project_report", "orm_report", "non_billable_monitoring", "non_billable_by_member", "non_billable_cost_by_week", "resources_in_project", "cost_monitoring", "weekly_cost", "monthly_cost", "monthly_project_cost", "detail_report_by_month", "monthly_cost_data", "sales", "monthly_production_performance", "sales_lead", "monitor_bidding_package", "project_reference", "sales_pipeline", "on_going", "bidding", "budgeting_plan", "skills_manage", "skills_update", "skills_report", "cv"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/constants/Routers.ts"], "sourcesContent": ["export const ROUTER = {\n    home: {\n        index: '/'\n    },\n    authentication: {\n        login: 'login',\n        confirmEmail: 'confirm-email',\n        register: 'register',\n        forgot: 'forgot',\n        forgotPassword: 'forgot-password'\n    },\n    administration: {\n        manage_user: 'manage-user',\n        manage_project: 'manage-project',\n        manage_holiday: 'manage-holiday',\n        manage_special_hours: 'manage-special-hours',\n        manage_group: 'manage-group',\n        system_config: 'system-config',\n        manage_rank: 'manage-rank',\n        email_config: 'email-config',\n        cv_config: 'cv-config',\n        exchange_rate_config: 'exchange-rate-config',\n        manage_department: 'manage-department',\n        project_type_config: 'project-type-config',\n        title_config: 'title-config',\n        non_billables_config: 'non-billables-config',\n        flexible_reporting: {\n            index: 'flexible-reporting',\n            column_config: 'column-config',\n            text_config: 'text-config',\n            flexible_reporting_config: 'flexible-reporting-config'\n        }\n    },\n    workingCalendar: {\n        register_working_calendar: 'register-working-calendar',\n        manage_leave_days: 'manage-leave-days',\n        manage_leaves: 'manage-leaves',\n        manage_ot: 'manage-ot-requests',\n        manage_resignation: 'manage-resignation',\n        manage_leave_requests: 'manage-leave-requests'\n    },\n    reports: {\n        weekly_effort: 'weekly-effort',\n        monthly_effort: {\n            index: 'monthly-effort',\n            summary: 'summary',\n            project: 'project',\n            department_member: 'department-member'\n        },\n        general_report: {\n            index: 'general-report',\n            product: 'product-report',\n            project_report: 'project-report',\n            orm_report: 'orm-report'\n        },\n\n        non_billable_monitoring: {\n            index: 'non-billable-monitoring',\n            non_billable_by_member: 'non-billable-by-member',\n            non_billable_cost_by_week: 'nonbill-ratio-chart'\n        },\n        resources_in_project: 'resources-in-project',\n        cost_monitoring: {\n            index: 'cost-monitoring',\n            weekly_cost: 'weekly-monitoring',\n            monthly_cost: 'monthly-monitoring'\n        },\n        monthly_project_cost: {\n            index: 'monthly-project-cost',\n            summary: 'summary',\n            detail_report_by_month: 'detail-report-by-month',\n            monthly_cost_data: 'monthly-cost-data'\n        },\n        sales: {\n            index: 'sales',\n            monthly_production_performance: 'monthly-production-performance',\n            sales_lead: 'sales-lead',\n            monitor_bidding_package: 'monitor-bidding-package',\n            project_reference: 'project-reference',\n            sales_pipeline: {\n                index: 'sales-pipeline',\n                summary: 'summary',\n                on_going: 'on-going',\n                bidding: 'all',\n                budgeting_plan: 'budgeting-plan'\n            }\n        },\n        skills_manage: {\n            index: 'manage-skills',\n            skills_update: 'skills-update',\n            skills_report: 'skills-report',\n            cv: 'cv'\n        }\n    }\n};\n"], "mappings": "AAAA,OAAO,MAAMA,MAAM,GAAG;EAClBC,IAAI,EAAE;IACFC,KAAK,EAAE;EACX,CAAC;EACDC,cAAc,EAAE;IACZC,KAAK,EAAE,OAAO;IACdC,YAAY,EAAE,eAAe;IAC7BC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,QAAQ;IAChBC,cAAc,EAAE;EACpB,CAAC;EACDC,cAAc,EAAE;IACZC,WAAW,EAAE,aAAa;IAC1BC,cAAc,EAAE,gBAAgB;IAChCC,cAAc,EAAE,gBAAgB;IAChCC,oBAAoB,EAAE,sBAAsB;IAC5CC,YAAY,EAAE,cAAc;IAC5BC,aAAa,EAAE,eAAe;IAC9BC,WAAW,EAAE,aAAa;IAC1BC,YAAY,EAAE,cAAc;IAC5BC,SAAS,EAAE,WAAW;IACtBC,oBAAoB,EAAE,sBAAsB;IAC5CC,iBAAiB,EAAE,mBAAmB;IACtCC,mBAAmB,EAAE,qBAAqB;IAC1CC,YAAY,EAAE,cAAc;IAC5BC,oBAAoB,EAAE,sBAAsB;IAC5CC,kBAAkB,EAAE;MAChBtB,KAAK,EAAE,oBAAoB;MAC3BuB,aAAa,EAAE,eAAe;MAC9BC,WAAW,EAAE,aAAa;MAC1BC,yBAAyB,EAAE;IAC/B;EACJ,CAAC;EACDC,eAAe,EAAE;IACbC,yBAAyB,EAAE,2BAA2B;IACtDC,iBAAiB,EAAE,mBAAmB;IACtCC,aAAa,EAAE,eAAe;IAC9BC,SAAS,EAAE,oBAAoB;IAC/BC,kBAAkB,EAAE,oBAAoB;IACxCC,qBAAqB,EAAE;EAC3B,CAAC;EACDC,OAAO,EAAE;IACLC,aAAa,EAAE,eAAe;IAC9BC,cAAc,EAAE;MACZnC,KAAK,EAAE,gBAAgB;MACvBoC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,SAAS;MAClBC,iBAAiB,EAAE;IACvB,CAAC;IACDC,cAAc,EAAE;MACZvC,KAAK,EAAE,gBAAgB;MACvBwC,OAAO,EAAE,gBAAgB;MACzBC,cAAc,EAAE,gBAAgB;MAChCC,UAAU,EAAE;IAChB,CAAC;IAEDC,uBAAuB,EAAE;MACrB3C,KAAK,EAAE,yBAAyB;MAChC4C,sBAAsB,EAAE,wBAAwB;MAChDC,yBAAyB,EAAE;IAC/B,CAAC;IACDC,oBAAoB,EAAE,sBAAsB;IAC5CC,eAAe,EAAE;MACb/C,KAAK,EAAE,iBAAiB;MACxBgD,WAAW,EAAE,mBAAmB;MAChCC,YAAY,EAAE;IAClB,CAAC;IACDC,oBAAoB,EAAE;MAClBlD,KAAK,EAAE,sBAAsB;MAC7BoC,OAAO,EAAE,SAAS;MAClBe,sBAAsB,EAAE,wBAAwB;MAChDC,iBAAiB,EAAE;IACvB,CAAC;IACDC,KAAK,EAAE;MACHrD,KAAK,EAAE,OAAO;MACdsD,8BAA8B,EAAE,gCAAgC;MAChEC,UAAU,EAAE,YAAY;MACxBC,uBAAuB,EAAE,yBAAyB;MAClDC,iBAAiB,EAAE,mBAAmB;MACtCC,cAAc,EAAE;QACZ1D,KAAK,EAAE,gBAAgB;QACvBoC,OAAO,EAAE,SAAS;QAClBuB,QAAQ,EAAE,UAAU;QACpBC,OAAO,EAAE,KAAK;QACdC,cAAc,EAAE;MACpB;IACJ,CAAC;IACDC,aAAa,EAAE;MACX9D,KAAK,EAAE,eAAe;MACtB+D,aAAa,EAAE,eAAe;MAC9BC,aAAa,EAAE,eAAe;MAC9BC,EAAE,EAAE;IACR;EACJ;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}