{"ast": null, "code": "import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getFormControlUnstyledUtilityClass(slot) {\n  return generateUtilityClass('BaseFormControl', slot);\n}\nconst formControlUnstyledClasses = generateUtilityClasses('BaseFormControl', ['root', 'disabled', 'error', 'filled', 'focused', 'required']);\nexport default formControlUnstyledClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}