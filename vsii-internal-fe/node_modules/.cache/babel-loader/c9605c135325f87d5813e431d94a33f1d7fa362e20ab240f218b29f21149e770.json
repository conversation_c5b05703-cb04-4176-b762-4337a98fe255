{"ast": null, "code": "// material-ui\nimport{TableCell,TableHead,TableRow,useMediaQuery,useTheme}from'@mui/material';// project imports\nimport{PERMISSIONS}from'constants/Permission';import{checkAllowedPermission}from'utils/authorization';// third party\nimport{FormattedMessage}from'react-intl';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const BiddingThead=()=>{const{biddingPermission}=PERMISSIONS.sale.salePipeline;const theme=useTheme();const matches=useMediaQuery(theme.breakpoints.up('md'));const{salesReport}=TEXT_CONFIG_SCREEN;return/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[checkAllowedPermission(biddingPermission.edit)?/*#__PURE__*/_jsx(TableCell,{align:\"center\",sx:{position:'sticky',left:0,zIndex:3,backgroundColor:'white'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-action'})}):/*#__PURE__*/_jsx(_Fragment,{}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-no'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-contract-type'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-service-type'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-project-name'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-customer'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-probability'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"revenue\"})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-status'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-size-vnd'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-size-usd'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-management-revenue-allocated'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-accountant-revenue-allocatedVND'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-license-fee'})}),/*#__PURE__*/_jsx(TableCell,{sx:{position:'sticky',right:!!matches?0:'unset'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-time-duration'})})]})});};export default BiddingThead;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}