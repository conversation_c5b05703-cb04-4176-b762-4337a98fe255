{"ast": null, "code": ";\n(function (root, factory) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var WordArray = C_lib.WordArray;\n    var C_enc = C.enc;\n\n    /**\n     * UTF-16 BE encoding strategy.\n     */\n    var Utf16BE = C_enc.Utf16 = C_enc.Utf16BE = {\n      /**\n       * Converts a word array to a UTF-16 BE string.\n       *\n       * @param {WordArray} wordArray The word array.\n       *\n       * @return {string} The UTF-16 BE string.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var utf16String = CryptoJS.enc.Utf16.stringify(wordArray);\n       */\n      stringify: function (wordArray) {\n        // Shortcuts\n        var words = wordArray.words;\n        var sigBytes = wordArray.sigBytes;\n\n        // Convert\n        var utf16Chars = [];\n        for (var i = 0; i < sigBytes; i += 2) {\n          var codePoint = words[i >>> 2] >>> 16 - i % 4 * 8 & 0xffff;\n          utf16Chars.push(String.fromCharCode(codePoint));\n        }\n        return utf16Chars.join('');\n      },\n      /**\n       * Converts a UTF-16 BE string to a word array.\n       *\n       * @param {string} utf16Str The UTF-16 BE string.\n       *\n       * @return {WordArray} The word array.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var wordArray = CryptoJS.enc.Utf16.parse(utf16String);\n       */\n      parse: function (utf16Str) {\n        // Shortcut\n        var utf16StrLength = utf16Str.length;\n\n        // Convert\n        var words = [];\n        for (var i = 0; i < utf16StrLength; i++) {\n          words[i >>> 1] |= utf16Str.charCodeAt(i) << 16 - i % 2 * 16;\n        }\n        return WordArray.create(words, utf16StrLength * 2);\n      }\n    };\n\n    /**\n     * UTF-16 LE encoding strategy.\n     */\n    C_enc.Utf16LE = {\n      /**\n       * Converts a word array to a UTF-16 LE string.\n       *\n       * @param {WordArray} wordArray The word array.\n       *\n       * @return {string} The UTF-16 LE string.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var utf16Str = CryptoJS.enc.Utf16LE.stringify(wordArray);\n       */\n      stringify: function (wordArray) {\n        // Shortcuts\n        var words = wordArray.words;\n        var sigBytes = wordArray.sigBytes;\n\n        // Convert\n        var utf16Chars = [];\n        for (var i = 0; i < sigBytes; i += 2) {\n          var codePoint = swapEndian(words[i >>> 2] >>> 16 - i % 4 * 8 & 0xffff);\n          utf16Chars.push(String.fromCharCode(codePoint));\n        }\n        return utf16Chars.join('');\n      },\n      /**\n       * Converts a UTF-16 LE string to a word array.\n       *\n       * @param {string} utf16Str The UTF-16 LE string.\n       *\n       * @return {WordArray} The word array.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var wordArray = CryptoJS.enc.Utf16LE.parse(utf16Str);\n       */\n      parse: function (utf16Str) {\n        // Shortcut\n        var utf16StrLength = utf16Str.length;\n\n        // Convert\n        var words = [];\n        for (var i = 0; i < utf16StrLength; i++) {\n          words[i >>> 1] |= swapEndian(utf16Str.charCodeAt(i) << 16 - i % 2 * 16);\n        }\n        return WordArray.create(words, utf16StrLength * 2);\n      }\n    };\n    function swapEndian(word) {\n      return word << 8 & 0xff00ff00 | word >>> 8 & 0x00ff00ff;\n    }\n  })();\n  return CryptoJS.enc.Utf16;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}