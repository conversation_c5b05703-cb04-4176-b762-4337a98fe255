{"ast": null, "code": "const motionEvent = (name, target) => new CustomEvent(name, {\n  detail: {\n    target\n  }\n});\nfunction dispatchPointerEvent(element, name, event) {\n  element.dispatchEvent(new CustomEvent(name, {\n    detail: {\n      originalEvent: event\n    }\n  }));\n}\nfunction dispatchViewEvent(element, name, entry) {\n  element.dispatchEvent(new CustomEvent(name, {\n    detail: {\n      originalEntry: entry\n    }\n  }));\n}\nexport { dispatchPointerEvent, dispatchViewEvent, motionEvent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}