{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/product-report/BacklogThead.tsx\";\n// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BacklogThead = () => {\n  return /*#__PURE__*/_jsxDEV(TableHead, {\n    sx: {\n      position: 'sticky',\n      top: '0',\n      zIndex: '99'\n    },\n    children: /*#__PURE__*/_jsxDEV(TableRow, {\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          width: '2%',\n          px: '3px'\n        },\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: \"product-report.modal.rightSide.table.no\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          width: '35%',\n          px: '3px'\n        },\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: \"product-report.modal.rightSide.table.requirement\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          width: '35%',\n          px: '3px'\n        },\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: \"product-report.modal.rightSide.table.sprint\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          width: '14%',\n          px: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: \"product-report.modal.rightSide.table.status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          width: '14%',\n          px: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: \"product-report.modal.rightSide.table.totalEffort\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 9\n  }, this);\n};\n_c = BacklogThead;\nexport default BacklogThead;\nvar _c;\n$RefreshReg$(_c, \"BacklogThead\");", "map": {"version": 3, "names": ["TableCell", "TableHead", "TableRow", "FormattedMessage", "jsxDEV", "_jsxDEV", "BacklogThead", "sx", "position", "top", "zIndex", "children", "width", "px", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/product-report/BacklogThead.tsx"], "sourcesContent": ["// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\n\nconst BacklogThead = () => {\n    return (\n        <TableHead\n            sx={{\n                position: 'sticky',\n                top: '0',\n                zIndex: '99'\n            }}\n        >\n            <TableRow>\n                <TableCell sx={{ width: '2%', px: '3px' }}>\n                    <FormattedMessage id=\"product-report.modal.rightSide.table.no\" />\n                </TableCell>\n                <TableCell sx={{ width: '35%', px: '3px' }}>\n                    <FormattedMessage id=\"product-report.modal.rightSide.table.requirement\" />\n                </TableCell>\n                <TableCell sx={{ width: '35%', px: '3px' }}>\n                    <FormattedMessage id=\"product-report.modal.rightSide.table.sprint\" />\n                </TableCell>\n                <TableCell sx={{ width: '14%', px: 0 }}>\n                    <FormattedMessage id=\"product-report.modal.rightSide.table.status\" />\n                </TableCell>\n                <TableCell sx={{ width: '14%', px: 0 }}>\n                    <FormattedMessage id=\"product-report.modal.rightSide.table.totalEffort\" />\n                </TableCell>\n            </TableRow>\n        </TableHead>\n    );\n};\n\nexport default BacklogThead;\n"], "mappings": ";AAAA;AACA,SAASA,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;;AAE9D;AACA,SAASC,gBAAgB,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACvB,oBACID,OAAA,CAACJ,SAAS;IACNM,EAAE,EAAE;MACAC,QAAQ,EAAE,QAAQ;MAClBC,GAAG,EAAE,GAAG;MACRC,MAAM,EAAE;IACZ,CAAE;IAAAC,QAAA,eAEFN,OAAA,CAACH,QAAQ;MAAAS,QAAA,gBACLN,OAAA,CAACL,SAAS;QAACO,EAAE,EAAE;UAAEK,KAAK,EAAE,IAAI;UAAEC,EAAE,EAAE;QAAM,CAAE;QAAAF,QAAA,eACtCN,OAAA,CAACF,gBAAgB;UAACW,EAAE,EAAC;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACZb,OAAA,CAACL,SAAS;QAACO,EAAE,EAAE;UAAEK,KAAK,EAAE,KAAK;UAAEC,EAAE,EAAE;QAAM,CAAE;QAAAF,QAAA,eACvCN,OAAA,CAACF,gBAAgB;UAACW,EAAE,EAAC;QAAkD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eACZb,OAAA,CAACL,SAAS;QAACO,EAAE,EAAE;UAAEK,KAAK,EAAE,KAAK;UAAEC,EAAE,EAAE;QAAM,CAAE;QAAAF,QAAA,eACvCN,OAAA,CAACF,gBAAgB;UAACW,EAAE,EAAC;QAA6C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACZb,OAAA,CAACL,SAAS;QAACO,EAAE,EAAE;UAAEK,KAAK,EAAE,KAAK;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,eACnCN,OAAA,CAACF,gBAAgB;UAACW,EAAE,EAAC;QAA6C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACZb,OAAA,CAACL,SAAS;QAACO,EAAE,EAAE;UAAEK,KAAK,EAAE,KAAK;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,eACnCN,OAAA,CAACF,gBAAgB;UAACW,EAAE,EAAC;QAAkD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEpB,CAAC;AAACC,EAAA,GA5BIb,YAAY;AA8BlB,eAAeA,YAAY;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}