{"ast": null, "code": "import { getPickersLocalization } from './utils/getPickersLocalization';\nconst views = {\n  hours: '小时',\n  minutes: '分钟',\n  seconds: '秒'\n};\nconst zhCNPickers = {\n  // Calendar navigation\n  previousMonth: '上个月',\n  nextMonth: '下个月',\n  // View navigation\n  openPreviousView: '前一个视图',\n  openNextView: '下一个视图',\n  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? '年视图已打开，切换为日历视图' : '日历视图已打开，切换为年视图',\n  // inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `text input view is open, go to ${viewType} view` : `${viewType} view is open, go to text input view`,\n  // DateRange placeholders\n  start: '开始',\n  end: '结束',\n  // Action bar\n  cancelButtonLabel: '取消',\n  clearButtonLabel: '清除',\n  okButtonLabel: '确认',\n  todayButtonLabel: '今天',\n  // Toolbar titles\n  // datePickerDefaultToolbarTitle: 'Select date',\n  // dateTimePickerDefaultToolbarTitle: 'Select date & time',\n  // timePickerDefaultToolbarTitle: 'Select time',\n  // dateRangePickerDefaultToolbarTitle: 'Select date range',\n  // Clock labels\n  clockLabelText: (view, time, adapter) => \"Select \".concat(views[view], \". \").concat(time === null ? '未选择时间' : \"\\u5DF2\\u9009\\u62E9\".concat(adapter.format(time, 'fullTime'))),\n  hoursClockNumberText: hours => \"\".concat(hours, \"\\u5C0F\\u65F6\"),\n  minutesClockNumberText: minutes => \"\".concat(minutes, \"\\u5206\\u949F\"),\n  secondsClockNumberText: seconds => \"\".concat(seconds, \"\\u79D2\"),\n  // Open picker labels\n  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? \"\\u9009\\u62E9\\u65E5\\u671F\\uFF0C\\u5DF2\\u9009\\u62E9\".concat(utils.format(utils.date(rawValue), 'fullDate')) : '选择日期',\n  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? \"\\u9009\\u62E9\\u65F6\\u95F4\\uFF0C\\u5DF2\\u9009\\u62E9\".concat(utils.format(utils.date(rawValue), 'fullTime')) : '选择时间',\n  // Table labels\n  timeTableLabel: '选择时间',\n  dateTableLabel: '选择日期'\n};\nexport const zhCN = getPickersLocalization(zhCNPickers);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}