{"ast": null, "code": "import { invariant } from 'hey-listen';\nimport { stopAnimation, animateVisualElement } from '../../render/utils/animation.mjs';\nimport { setValues } from '../../render/utils/setters.mjs';\n\n/**\n * @public\n */\nfunction animationControls() {\n  /**\n   * Track whether the host component has mounted.\n   */\n  let hasMounted = false;\n  /**\n   * Pending animations that are started before a component is mounted.\n   * TODO: Remove this as animations should only run in effects\n   */\n  const pendingAnimations = [];\n  /**\n   * A collection of linked component animation controls.\n   */\n  const subscribers = new Set();\n  const controls = {\n    subscribe(visualElement) {\n      subscribers.add(visualElement);\n      return () => void subscribers.delete(visualElement);\n    },\n    start(definition, transitionOverride) {\n      /**\n       * TODO: We only perform this hasMounted check because in Framer we used to\n       * encourage the ability to start an animation within the render phase. This\n       * isn't behaviour concurrent-safe so when we make Framer concurrent-safe\n       * we can ditch this.\n       */\n      if (hasMounted) {\n        const animations = [];\n        subscribers.forEach(visualElement => {\n          animations.push(animateVisualElement(visualElement, definition, {\n            transitionOverride\n          }));\n        });\n        return Promise.all(animations);\n      } else {\n        return new Promise(resolve => {\n          pendingAnimations.push({\n            animation: [definition, transitionOverride],\n            resolve\n          });\n        });\n      }\n    },\n    set(definition) {\n      invariant(hasMounted, \"controls.set() should only be called after a component has mounted. Consider calling within a useEffect hook.\");\n      return subscribers.forEach(visualElement => {\n        setValues(visualElement, definition);\n      });\n    },\n    stop() {\n      subscribers.forEach(visualElement => {\n        stopAnimation(visualElement);\n      });\n    },\n    mount() {\n      hasMounted = true;\n      pendingAnimations.forEach(_ref => {\n        let {\n          animation,\n          resolve\n        } = _ref;\n        controls.start(...animation).then(resolve);\n      });\n      return () => {\n        hasMounted = false;\n        controls.stop();\n      };\n    }\n  };\n  return controls;\n}\nexport { animationControls };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}