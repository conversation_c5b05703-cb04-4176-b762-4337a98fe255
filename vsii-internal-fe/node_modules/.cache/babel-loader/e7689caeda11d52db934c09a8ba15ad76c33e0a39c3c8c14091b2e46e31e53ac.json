{"ast": null, "code": "import{forwardRef}from'react';// material-ui\nimport{But<PERSON>,Stack,TableBody,TableCell,TableRow}from'@mui/material';// project imports\nimport{DATE_FORMAT,PUBLIC_URL}from'constants/Common';import InputTable from'./InputTable';// third party\nimport moment from'moment';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SignatureTBody=/*#__PURE__*/forwardRef((props,ref)=>{const{signaturePreview,handleChangeSignature,handleRemoveSignature,date}=props;const toDate=moment(date,DATE_FORMAT.DDMMYYYY).toDate();const lastDateFormat=date?moment(toDate).format(DATE_FORMAT.DoMMMYY):moment(new Date()).format(DATE_FORMAT.DoMMMYY);return/*#__PURE__*/_jsx(TableBody,{children:/*#__PURE__*/_jsx(TableRow,{children:/*#__PURE__*/_jsxs(TableCell,{children:[\"I certify that the information declared above is true to the best of my knowledge and belief.\",/*#__PURE__*/_jsx(\"br\",{}),\"Ha Noi, \",lastDateFormat,/*#__PURE__*/_jsx(\"br\",{}),\"Signatue\",/*#__PURE__*/_jsxs(Stack,{alignItems:\"left\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"img\",{src:signaturePreview?signaturePreview:\"\".concat(PUBLIC_URL,\"/no-image.jpg\"),alt:\"avatar\",style:{width:'150px',height:'100px',objectFit:'cover'}})}),/*#__PURE__*/_jsxs(Stack,{direction:\"row\",spacing:1,children:[/*#__PURE__*/_jsx(Button,{color:\"error\",onClick:handleRemoveSignature,children:\"Remove\"}),/*#__PURE__*/_jsx(Button,{component:\"label\",variant:\"contained\",htmlFor:\"signature\",children:\"Attach file\"}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",id:\"signature\",hidden:true,onChange:handleChangeSignature,accept:\"image/jpeg\",ref:ref})]}),/*#__PURE__*/_jsx(InputTable,{name:\"personalDetail.fullname\",placeholder:\"Fill fullname\",disabled:true})]})]})})});});export default SignatureTBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}