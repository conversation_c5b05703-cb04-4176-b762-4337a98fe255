{"ast": null, "code": "import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const EmailConfig=()=>{return/*#__PURE__*/_jsxs(\"svg\",{width:20,height:20,viewBox:\"0 0 24 24\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M12 20.5H7C4 20.5 2 19 2 15.5V8.5C2 5 4 3.5 7 3.5H17C20 3.5 22 5 22 8.5V11.5\",stroke:\"#292D32\",strokeWidth:\"1.5\",strokeMiterlimit:10,strokeLinecap:\"round\",strokeLinejoin:\"round\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M17 9L13.87 11.5C12.84 12.32 11.15 12.32 10.12 11.5L7 9\",stroke:\"#292D32\",strokeWidth:\"1.5\",strokeMiterlimit:10,strokeLinecap:\"round\",strokeLinejoin:\"round\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M19 19C19.5523 19 20 18.5523 20 18C20 17.4477 19.5523 17 19 17C18.4477 17 18 17.4477 18 18C18 18.5523 18.4477 19 19 19Z\",stroke:\"#292D32\",strokeWidth:\"1.5\",strokeMiterlimit:10,strokeLinecap:\"round\",strokeLinejoin:\"round\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M15 18.3729V17.6271C15 17.1863 15.3398 16.8219 15.7596 16.8219C16.4833 16.8219 16.7791 16.2795 16.4153 15.6141C16.2074 15.2327 16.3313 14.7369 16.6952 14.5165L17.3868 14.097C17.7026 13.8978 18.1104 14.0165 18.2984 14.3513L18.3423 14.4318C18.7021 15.0971 19.2939 15.0971 19.6577 14.4318L19.7016 14.3513C19.8896 14.0165 20.2974 13.8978 20.6132 14.097L21.3048 14.5165C21.6687 14.7369 21.7926 15.2327 21.5847 15.6141C21.2209 16.2795 21.5167 16.8219 22.2404 16.8219C22.6562 16.8219 23 17.1821 23 17.6271V18.3729C23 18.8137 22.6602 19.1781 22.2404 19.1781C21.5167 19.1781 21.2209 19.7205 21.5847 20.3859C21.7926 20.7715 21.6687 21.2631 21.3048 21.4835L20.6132 21.903C20.2974 22.1022 19.8896 21.9835 19.7016 21.6487L19.6577 21.5682C19.2979 20.9029 18.7061 20.9029 18.3423 21.5682L18.2984 21.6487C18.1104 21.9835 17.7026 22.1022 17.3868 21.903L16.6952 21.4835C16.3313 21.2631 16.2074 20.7673 16.4153 20.3859C16.7791 19.7205 16.4833 19.1781 15.7596 19.1781C15.3398 19.1781 15 18.8137 15 18.3729Z\",stroke:\"#292D32\",strokeWidth:\"1.5\",strokeMiterlimit:10,strokeLinecap:\"round\",strokeLinejoin:\"round\"})]});};export default EmailConfig;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}