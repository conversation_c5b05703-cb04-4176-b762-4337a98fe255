{"ast": null, "code": "// material-ui\nimport{TableCell,TableHead,TableRow}from'@mui/material';import{FormattedMessage}from'react-intl';import useMediaQuery from'@mui/material/useMediaQuery';import{useTheme}from'@mui/material/styles';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DetailReportByMonthThead=()=>{const theme=useTheme();const matches=useMediaQuery(theme.breakpoints.up('md'));const{monthlyProjectCost}=TEXT_CONFIG_SCREEN;return/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{sx:{'& .MuiTableCell-root':{textAlign:'center',fontWeight:'700'}},children:[/*#__PURE__*/_jsx(TableCell,{sx:{textAlign:'left !important',left:!!matches?0:'unset',zIndex:3},children:/*#__PURE__*/_jsx(FormattedMessage,{id:monthlyProjectCost.detailReportByMonth+'project'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:monthlyProjectCost.detailReportByMonth+'department'})}),/*#__PURE__*/_jsx(TableCell,{sx:{textAlign:'left !important'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:monthlyProjectCost.detailReportByMonth+'project-type'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:monthlyProjectCost.detailReportByMonth+'total-effort-md'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:monthlyProjectCost.detailReportByMonth+'overhead-allocated-amt'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:monthlyProjectCost.detailReportByMonth+'salary-cost'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:monthlyProjectCost.detailReportByMonth+'total-cost'})})]})});};export default DetailReportByMonthThead;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}