{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/icons/Crown.tsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Crown = () => {\n  return /*#__PURE__*/_jsxDEV(\"svg\", {\n    fill: \"#f99e2185\",\n    height: \"80px\",\n    width: \"80px\",\n    version: \"1.1\",\n    id: \"Capa_1\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\",\n    viewBox: \"0 0 267.5 267.5\",\n    xmlSpace: \"preserve\",\n    children: /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M256.975,100.34c0.041,0.736-0.013,1.485-0.198,2.229l-16.5,66c-0.832,3.325-3.812,5.663-7.238,5.681l-99,0.5\\r c-0.013,0-0.025,0-0.038,0H35c-3.444,0-6.445-2.346-7.277-5.688l-16.5-66.25c-0.19-0.764-0.245-1.534-0.197-2.289\\r C4.643,98.512,0,92.539,0,85.5c0-8.685,7.065-15.75,15.75-15.75S31.5,76.815,31.5,85.5c0,4.891-2.241,9.267-5.75,12.158\\r l20.658,20.814c5.221,5.261,12.466,8.277,19.878,8.277c8.764,0,17.12-4.162,22.382-11.135l33.95-44.984\\r C119.766,67.78,118,63.842,118,59.5c0-8.685,7.065-15.75,15.75-15.75s15.75,7.065,15.75,15.75c0,4.212-1.672,8.035-4.375,10.864\\r c0.009,0.012,0.02,0.022,0.029,0.035l33.704,45.108c5.26,7.04,13.646,11.243,22.435,11.243c7.48,0,14.514-2.913,19.803-8.203\\r l20.788-20.788C238.301,94.869,236,90.451,236,85.5c0-8.685,7.065-15.75,15.75-15.75s15.75,7.065,15.75,15.75\\r C267.5,92.351,263.095,98.178,256.975,100.34z M238.667,198.25c0-4.142-3.358-7.5-7.5-7.5h-194c-4.142,0-7.5,3.358-7.5,7.5v18\\r c0,4.142,3.358,7.5,7.5,7.5h194c4.142,0,7.5-3.358,7.5-7.5V198.25z\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 9\n  }, this);\n};\n_c = Crown;\nexport default Crown;\nvar _c;\n$RefreshReg$(_c, \"Crown\");", "map": {"version": 3, "names": ["Crown", "_jsxDEV", "fill", "height", "width", "version", "id", "xmlns", "xmlnsXlink", "viewBox", "xmlSpace", "children", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/icons/Crown.tsx"], "sourcesContent": ["const Crown = () => {\r\n    return (\r\n        <svg\r\n            fill=\"#f99e2185\"\r\n            height=\"80px\"\r\n            width=\"80px\"\r\n            version=\"1.1\"\r\n            id=\"Capa_1\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            xmlnsXlink=\"http://www.w3.org/1999/xlink\"\r\n            viewBox=\"0 0 267.5 267.5\"\r\n            xmlSpace=\"preserve\"\r\n        >\r\n            <path\r\n                d=\"M256.975,100.34c0.041,0.736-0.013,1.485-0.198,2.229l-16.5,66c-0.832,3.325-3.812,5.663-7.238,5.681l-99,0.5\r\n\t\t\t\tc-0.013,0-0.025,0-0.038,0H35c-3.444,0-6.445-2.346-7.277-5.688l-16.5-66.25c-0.19-0.764-0.245-1.534-0.197-2.289\r\n\t\t\t\tC4.643,98.512,0,92.539,0,85.5c0-8.685,7.065-15.75,15.75-15.75S31.5,76.815,31.5,85.5c0,4.891-2.241,9.267-5.75,12.158\r\n\t\t\t\tl20.658,20.814c5.221,5.261,12.466,8.277,19.878,8.277c8.764,0,17.12-4.162,22.382-11.135l33.95-44.984\r\n\t\t\t\tC119.766,67.78,118,63.842,118,59.5c0-8.685,7.065-15.75,15.75-15.75s15.75,7.065,15.75,15.75c0,4.212-1.672,8.035-4.375,10.864\r\n\t\t\t\tc0.009,0.012,0.02,0.022,0.029,0.035l33.704,45.108c5.26,7.04,13.646,11.243,22.435,11.243c7.48,0,14.514-2.913,19.803-8.203\r\n\t\t\t\tl20.788-20.788C238.301,94.869,236,90.451,236,85.5c0-8.685,7.065-15.75,15.75-15.75s15.75,7.065,15.75,15.75\r\n\t\t\t\tC267.5,92.351,263.095,98.178,256.975,100.34z M238.667,198.25c0-4.142-3.358-7.5-7.5-7.5h-194c-4.142,0-7.5,3.358-7.5,7.5v18\r\n\t\t\t\tc0,4.142,3.358,7.5,7.5,7.5h194c4.142,0,7.5-3.358,7.5-7.5V198.25z\"\r\n            />\r\n        </svg>\r\n    );\r\n};\r\n\r\nexport default Crown;\r\n"], "mappings": ";;AAAA,MAAMA,KAAK,GAAGA,CAAA,KAAM;EAChB,oBACIC,OAAA;IACIC,IAAI,EAAC,WAAW;IAChBC,MAAM,EAAC,MAAM;IACbC,KAAK,EAAC,MAAM;IACZC,OAAO,EAAC,KAAK;IACbC,EAAE,EAAC,QAAQ;IACXC,KAAK,EAAC,4BAA4B;IAClCC,UAAU,EAAC,8BAA8B;IACzCC,OAAO,EAAC,iBAAiB;IACzBC,QAAQ,EAAC,UAAU;IAAAC,QAAA,eAEnBV,OAAA;MACIW,CAAC,EAAC;IAQmD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACC,EAAA,GA1BIjB,KAAK;AA4BX,eAAeA,KAAK;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}