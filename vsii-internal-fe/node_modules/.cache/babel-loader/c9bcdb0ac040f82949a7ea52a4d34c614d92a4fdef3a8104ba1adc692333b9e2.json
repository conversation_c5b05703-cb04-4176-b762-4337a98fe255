{"ast": null, "code": "// material-ui\nimport{TableCell,TableHead,TableRow}from'@mui/material';import{TEXT_CONFIG_SCREEN}from'constants/Common';// third party\nimport{FormattedMessage}from'react-intl';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SupplierCheckingThead=()=>{const{salesReport}=TEXT_CONFIG_SCREEN;return/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesLead+'supplier-no'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesLead+'supplier-supplier-name'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesLead+'supplier-technology'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesLead+'supplier-quantity'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesLead+'supplier-from-date'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesLead+'supplier-to-date'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesLead+'supplier-unit-price'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesLead+'supplier-pic-user-name'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesLead+'supplier-work-type'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesLead+'supplier-note'})}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesLead+'supplier-action'})})]})});};export default SupplierCheckingThead;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}