{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport Skeleton from '@mui/material/Skeleton';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/material';\nimport { DAY_SIZE, DAY_MARGIN } from '../internals/constants/dimensions';\nimport { getCalendarPickerSkeletonUtilityClass } from './calendarPickerSkeletonClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    week: ['week'],\n    daySkeleton: ['daySkeleton']\n  };\n  return composeClasses(slots, getCalendarPickerSkeletonUtilityClass, classes);\n};\nconst CalendarPickerSkeletonRoot = styled('div', {\n  name: 'MuiCalendarPickerSkeleton',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  alignSelf: 'start'\n});\nconst CalendarPickerSkeletonWeek = styled('div', {\n  name: 'MuiCalendarPickerSkeleton',\n  slot: 'Week',\n  overridesResolver: (props, styles) => styles.week\n})({\n  margin: \"\".concat(DAY_MARGIN, \"px 0\"),\n  display: 'flex',\n  justifyContent: 'center'\n});\nconst CalendarPickerSkeletonDay = styled(Skeleton, {\n  name: 'MuiCalendarPickerSkeleton',\n  slot: 'DaySkeleton',\n  overridesResolver: (props, styles) => styles.daySkeleton\n})(_ref => {\n  let {\n    ownerState\n  } = _ref;\n  return _extends({\n    margin: \"0 \".concat(DAY_MARGIN, \"px\")\n  }, ownerState.day === 0 && {\n    visibility: 'hidden'\n  });\n});\nCalendarPickerSkeletonDay.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  ownerState: PropTypes.shape({\n    day: PropTypes.number.isRequired\n  }).isRequired\n};\nconst monthMap = [[0, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 1, 1, 1], [1, 1, 1, 1, 0, 0, 0]];\n/**\n *\n * Demos:\n *\n * - [Date Picker](https://mui.com/x/react-date-pickers/date-picker/)\n *\n * API:\n *\n * - [CalendarPickerSkeleton API](https://mui.com/x/api/date-pickers/calendar-picker-skeleton/)\n */\n\nfunction CalendarPickerSkeleton(inProps) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiCalendarPickerSkeleton'\n  });\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const classes = useUtilityClasses(other);\n  return /*#__PURE__*/_jsx(CalendarPickerSkeletonRoot, _extends({\n    className: clsx(classes.root, className)\n  }, other, {\n    children: monthMap.map((week, index) => /*#__PURE__*/_jsx(CalendarPickerSkeletonWeek, {\n      className: classes.week,\n      children: week.map((day, index2) => /*#__PURE__*/_jsx(CalendarPickerSkeletonDay, {\n        variant: \"circular\",\n        width: DAY_SIZE,\n        height: DAY_SIZE,\n        className: classes.daySkeleton,\n        ownerState: {\n          day\n        }\n      }, index2))\n    }, index))\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? CalendarPickerSkeleton.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { CalendarPickerSkeleton };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}