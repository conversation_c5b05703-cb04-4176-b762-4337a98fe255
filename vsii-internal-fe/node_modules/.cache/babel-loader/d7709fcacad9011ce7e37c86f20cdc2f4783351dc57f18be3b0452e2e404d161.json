{"ast": null, "code": "/**\n * https://tc39.es/ecma402/#sec-getoptionsobject\n * @param options\n * @returns\n */\nexport function GetOptionsObject(options) {\n  if (typeof options === 'undefined') {\n    return Object.create(null);\n  }\n  if (typeof options === 'object') {\n    return options;\n  }\n  throw new TypeError('Options must be an object');\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}