{"ast": null, "code": "// material-ui\nimport{Table<PERSON>ell,TableHead,TableRow,useMediaQuery,useTheme}from'@mui/material';// project imports\nimport{PERMISSIONS}from'constants/Permission';import{checkAllowedPermission}from'utils/authorization';// third party\nimport{FormattedMessage}from'react-intl';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const OnGoingThead=()=>{const{onGoingPermission}=PERMISSIONS.sale.salePipeline;const theme=useTheme();const matches=useMediaQuery(theme.breakpoints.up('md'));const{salesReport}=TEXT_CONFIG_SCREEN;return/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[checkAllowedPermission(onGoingPermission.delete)||checkAllowedPermission(onGoingPermission.edit)?/*#__PURE__*/_jsx(TableCell,{align:\"center\",sx:{position:'sticky',left:0,zIndex:3,backgroundColor:'white'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-action'})}):/*#__PURE__*/_jsx(_Fragment,{}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-no'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-contract-type'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-service-type'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-project-name'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-probability'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-sale-pipeline-status'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"revenue\"})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-size-vnd'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-size-usd'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-management-revenue-allocated'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-accountant-revenue-allocatedVND'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-license-fee'})}),/*#__PURE__*/_jsx(TableCell,{sx:{position:'sticky',right:!!matches?0:'unset'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-time-duration'})})]})});};export default OnGoingThead;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}