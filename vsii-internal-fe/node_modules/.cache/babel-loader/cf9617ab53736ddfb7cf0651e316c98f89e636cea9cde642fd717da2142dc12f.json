{"ast": null, "code": "/**\n * https://tc39.es/ecma402/#sec-isvalidtimezonename\n * @param tz\n * @param implDetails implementation details\n */\nexport function IsValidTimeZoneName(tz, _a) {\n  var zoneNamesFromData = _a.zoneNamesFromData,\n    uppercaseLinks = _a.uppercaseLinks;\n  var uppercasedTz = tz.toUpperCase();\n  var zoneNames = new Set();\n  var linkNames = new Set();\n  zoneNamesFromData.map(function (z) {\n    return z.toUpperCase();\n  }).forEach(function (z) {\n    return zoneNames.add(z);\n  });\n  Object.keys(uppercaseLinks).forEach(function (linkName) {\n    linkNames.add(linkName.toUpperCase());\n    zoneNames.add(uppercaseLinks[linkName].toUpperCase());\n  });\n  return zoneNames.has(uppercasedTz) || linkNames.has(uppercasedTz);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}