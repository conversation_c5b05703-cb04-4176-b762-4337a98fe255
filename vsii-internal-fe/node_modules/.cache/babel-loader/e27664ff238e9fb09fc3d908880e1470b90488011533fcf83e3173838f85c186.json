{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/BiddingReportSearch.tsx\";\nimport { FormattedMessage } from 'react-intl';\n\n// material-ui\nimport { Grid } from '@mui/material';\n\n// project import\nimport { biddingPackageShemcha, monitorBiddingPackagesFilterConfig } from 'pages/sales/Config';\nimport { Address, BiddingPackageName, SearchForm, StatusBiddingReport, Type } from 'containers/search';\nimport { Label } from 'components/extended/Form';\nimport { Button } from 'components';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BiddingReportSearch = props => {\n  const {\n    formReset,\n    handleSearch\n  } = props;\n  const {\n    salesReport\n  } = TEXT_CONFIG_SCREEN;\n  return /*#__PURE__*/_jsxDEV(SearchForm, {\n    defaultValues: monitorBiddingPackagesFilterConfig,\n    formSchema: biddingPackageShemcha,\n    handleSubmit: handleSearch,\n    formReset: formReset,\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      alignItems: \"center\",\n      spacing: 2,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2.5,\n        children: /*#__PURE__*/_jsxDEV(Type, {\n          label: salesReport.monitorBiddingPackages + 'report-type'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2.5,\n        children: /*#__PURE__*/_jsxDEV(BiddingPackageName, {\n          label: salesReport.monitorBiddingPackages + 'report-bidding-package-name'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2.5,\n        children: /*#__PURE__*/_jsxDEV(Address, {\n          label: salesReport.monitorBiddingPackages + 'report-address'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2,\n        children: /*#__PURE__*/_jsxDEV(StatusBiddingReport, {\n          label: salesReport.monitorBiddingPackages + 'report-status'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2.5,\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          label: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          size: \"medium\",\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: salesReport.monitorBiddingPackages + 'report-search'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 35\n          }, this),\n          variant: \"contained\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 9\n  }, this);\n};\n_c = BiddingReportSearch;\nexport default BiddingReportSearch;\nvar _c;\n$RefreshReg$(_c, \"BiddingReportSearch\");", "map": {"version": 3, "names": ["FormattedMessage", "Grid", "biddingPackageShemcha", "monitorBiddingPackagesFilterConfig", "Address", "BiddingPackageName", "SearchForm", "StatusBiddingReport", "Type", "Label", "<PERSON><PERSON>", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "BiddingReportSearch", "props", "formReset", "handleSearch", "salesReport", "defaultValues", "formSchema", "handleSubmit", "children", "container", "alignItems", "spacing", "item", "xs", "lg", "label", "monitorBiddingPackages", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "size", "id", "variant", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/BiddingReportSearch.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\r\n\r\n// material-ui\r\nimport { Grid } from '@mui/material';\r\n\r\n// project import\r\nimport { IMonitorBiddingPackagesFilterConfig, biddingPackageShemcha, monitorBiddingPackagesFilterConfig } from 'pages/sales/Config';\r\nimport { Address, BiddingPackageName, SearchForm, StatusBiddingReport, Type } from 'containers/search';\r\nimport { Label } from 'components/extended/Form';\r\nimport { Button } from 'components';\r\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\r\n\r\ninterface IBiddingReportSearchProps {\r\n    formReset: IMonitorBiddingPackagesFilterConfig;\r\n    handleSearch: (value: any) => void;\r\n}\r\n\r\nconst BiddingReportSearch = (props: IBiddingReportSearchProps) => {\r\n    const { formReset, handleSearch } = props;\r\n\r\n    const { salesReport } = TEXT_CONFIG_SCREEN;\r\n\r\n    return (\r\n        <SearchForm\r\n            defaultValues={monitorBiddingPackagesFilterConfig}\r\n            formSchema={biddingPackageShemcha}\r\n            handleSubmit={handleSearch}\r\n            formReset={formReset}\r\n        >\r\n            <Grid container alignItems=\"center\" spacing={2}>\r\n                <Grid item xs={12} lg={2.5}>\r\n                    <Type label={salesReport.monitorBiddingPackages + 'report-type'} />\r\n                </Grid>\r\n                <Grid item xs={12} lg={2.5}>\r\n                    <BiddingPackageName label={salesReport.monitorBiddingPackages + 'report-bidding-package-name'} />\r\n                </Grid>\r\n                <Grid item xs={12} lg={2.5}>\r\n                    <Address label={salesReport.monitorBiddingPackages + 'report-address'} />\r\n                </Grid>\r\n                <Grid item xs={12} lg={2}>\r\n                    <StatusBiddingReport label={salesReport.monitorBiddingPackages + 'report-status'} />\r\n                </Grid>\r\n                <Grid item xs={12} lg={2.5}>\r\n                    {/* not done */}\r\n                    <Label label=\"&nbsp;\" />\r\n                    <Button\r\n                        type=\"submit\"\r\n                        size=\"medium\"\r\n                        children={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-search'} />}\r\n                        variant=\"contained\"\r\n                    />\r\n                </Grid>\r\n            </Grid>\r\n        </SearchForm>\r\n    );\r\n};\r\n\r\nexport default BiddingReportSearch;\r\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,IAAI,QAAQ,eAAe;;AAEpC;AACA,SAA8CC,qBAAqB,EAAEC,kCAAkC,QAAQ,oBAAoB;AACnI,SAASC,OAAO,EAAEC,kBAAkB,EAAEC,UAAU,EAAEC,mBAAmB,EAAEC,IAAI,QAAQ,mBAAmB;AACtG,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOtD,MAAMC,mBAAmB,GAAIC,KAAgC,IAAK;EAC9D,MAAM;IAAEC,SAAS;IAAEC;EAAa,CAAC,GAAGF,KAAK;EAEzC,MAAM;IAAEG;EAAY,CAAC,GAAGP,kBAAkB;EAE1C,oBACIE,OAAA,CAACP,UAAU;IACPa,aAAa,EAAEhB,kCAAmC;IAClDiB,UAAU,EAAElB,qBAAsB;IAClCmB,YAAY,EAAEJ,YAAa;IAC3BD,SAAS,EAAEA,SAAU;IAAAM,QAAA,eAErBT,OAAA,CAACZ,IAAI;MAACsB,SAAS;MAACC,UAAU,EAAC,QAAQ;MAACC,OAAO,EAAE,CAAE;MAAAH,QAAA,gBAC3CT,OAAA,CAACZ,IAAI;QAACyB,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,GAAI;QAAAN,QAAA,eACvBT,OAAA,CAACL,IAAI;UAACqB,KAAK,EAAEX,WAAW,CAACY,sBAAsB,GAAG;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,eACPrB,OAAA,CAACZ,IAAI;QAACyB,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,GAAI;QAAAN,QAAA,eACvBT,OAAA,CAACR,kBAAkB;UAACwB,KAAK,EAAEX,WAAW,CAACY,sBAAsB,GAAG;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC,eACPrB,OAAA,CAACZ,IAAI;QAACyB,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,GAAI;QAAAN,QAAA,eACvBT,OAAA,CAACT,OAAO;UAACyB,KAAK,EAAEX,WAAW,CAACY,sBAAsB,GAAG;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eACPrB,OAAA,CAACZ,IAAI;QAACyB,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,eACrBT,OAAA,CAACN,mBAAmB;UAACsB,KAAK,EAAEX,WAAW,CAACY,sBAAsB,GAAG;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF,CAAC,eACPrB,OAAA,CAACZ,IAAI;QAACyB,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,GAAI;QAAAN,QAAA,gBAEvBT,OAAA,CAACJ,KAAK;UAACoB,KAAK,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxBrB,OAAA,CAACH,MAAM;UACHyB,IAAI,EAAC,QAAQ;UACbC,IAAI,EAAC,QAAQ;UACbd,QAAQ,eAAET,OAAA,CAACb,gBAAgB;YAACqC,EAAE,EAAEnB,WAAW,CAACY,sBAAsB,GAAG;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzFI,OAAO,EAAC;QAAW;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAErB,CAAC;AAACK,EAAA,GAtCIzB,mBAAmB;AAwCzB,eAAeA,mBAAmB;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}