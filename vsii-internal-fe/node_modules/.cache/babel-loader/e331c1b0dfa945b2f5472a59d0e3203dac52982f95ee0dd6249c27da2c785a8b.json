{"ast": null, "code": "import { createMotionComponent } from '../../motion/index.mjs';\nimport { createMotionProxy } from './motion-proxy.mjs';\nimport { createDomMotionConfig } from './utils/create-config.mjs';\nimport { gestureAnimations } from '../../motion/features/gestures.mjs';\nimport { animations } from '../../motion/features/animations.mjs';\nimport { drag } from '../../motion/features/drag.mjs';\nimport { createDomVisualElement } from './create-visual-element.mjs';\nimport { layoutFeatures } from '../../motion/features/layout/index.mjs';\nimport { HTMLProjectionNode } from '../../projection/node/HTMLProjectionNode.mjs';\nconst featureBundle = {\n  ...animations,\n  ...gestureAnimations,\n  ...drag,\n  ...layoutFeatures\n};\n/**\n * HTML & SVG components, optimised for use with gestures and animation. These can be used as\n * drop-in replacements for any HTML & SVG component, all CSS & SVG properties are supported.\n *\n * @public\n */\nconst motion = /*@__PURE__*/createMotionProxy((Component, config) => createDomMotionConfig(Component, config, featureBundle, createDomVisualElement, HTMLProjectionNode));\n/**\n * Create a DOM `motion` component with the provided string. This is primarily intended\n * as a full alternative to `motion` for consumers who have to support environments that don't\n * support `Proxy`.\n *\n * ```javascript\n * import { createDomMotionComponent } from \"framer-motion\"\n *\n * const motion = {\n *   div: createDomMotionComponent('div')\n * }\n * ```\n *\n * @public\n */\nfunction createDomMotionComponent(key) {\n  return createMotionComponent(createDomMotionConfig(key, {\n    forwardMotionProps: false\n  }, featureBundle, createDomVisualElement, HTMLProjectionNode));\n}\nexport { createDomMotionComponent, motion };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}