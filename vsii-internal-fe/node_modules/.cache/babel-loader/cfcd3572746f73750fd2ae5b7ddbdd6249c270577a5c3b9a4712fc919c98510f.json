{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"onChange\", \"maxRows\", \"minRows\", \"style\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { flushSync } from 'react-dom';\nimport { unstable_debounce as debounce, unstable_useForkRef as useForkRef, unstable_useEnhancedEffect as useEnhancedEffect, unstable_ownerWindow as ownerWindow } from '@mui/utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction getStyleValue(computedStyle, property) {\n  return parseInt(computedStyle[property], 10) || 0;\n}\nconst styles = {\n  shadow: {\n    // Visibility needed to hide the extra text area on iPads\n    visibility: 'hidden',\n    // Remove from the content flow\n    position: 'absolute',\n    // Ignore the scrollbar width\n    overflow: 'hidden',\n    height: 0,\n    top: 0,\n    left: 0,\n    // Create a new layer, increase the isolation of the computed values\n    transform: 'translateZ(0)'\n  }\n};\nfunction isEmpty(obj) {\n  return obj === undefined || obj === null || Object.keys(obj).length === 0;\n}\nconst TextareaAutosize = /*#__PURE__*/React.forwardRef(function TextareaAutosize(props, ref) {\n  const {\n      onChange,\n      maxRows,\n      minRows = 1,\n      style,\n      value\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    current: isControlled\n  } = React.useRef(value != null);\n  const inputRef = React.useRef(null);\n  const handleRef = useForkRef(ref, inputRef);\n  const shadowRef = React.useRef(null);\n  const renders = React.useRef(0);\n  const [state, setState] = React.useState({});\n  const getUpdatedState = React.useCallback(() => {\n    const input = inputRef.current;\n    const containerWindow = ownerWindow(input);\n    const computedStyle = containerWindow.getComputedStyle(input); // If input's width is shrunk and it's not visible, don't sync height.\n\n    if (computedStyle.width === '0px') {\n      return {};\n    }\n    const inputShallow = shadowRef.current;\n    inputShallow.style.width = computedStyle.width;\n    inputShallow.value = input.value || props.placeholder || 'x';\n    if (inputShallow.value.slice(-1) === '\\n') {\n      // Certain fonts which overflow the line height will cause the textarea\n      // to report a different scrollHeight depending on whether the last line\n      // is empty. Make it non-empty to avoid this issue.\n      inputShallow.value += ' ';\n    }\n    const boxSizing = computedStyle['box-sizing'];\n    const padding = getStyleValue(computedStyle, 'padding-bottom') + getStyleValue(computedStyle, 'padding-top');\n    const border = getStyleValue(computedStyle, 'border-bottom-width') + getStyleValue(computedStyle, 'border-top-width'); // The height of the inner content\n\n    const innerHeight = inputShallow.scrollHeight; // Measure height of a textarea with a single row\n\n    inputShallow.value = 'x';\n    const singleRowHeight = inputShallow.scrollHeight; // The height of the outer content\n\n    let outerHeight = innerHeight;\n    if (minRows) {\n      outerHeight = Math.max(Number(minRows) * singleRowHeight, outerHeight);\n    }\n    if (maxRows) {\n      outerHeight = Math.min(Number(maxRows) * singleRowHeight, outerHeight);\n    }\n    outerHeight = Math.max(outerHeight, singleRowHeight); // Take the box sizing into account for applying this value as a style.\n\n    const outerHeightStyle = outerHeight + (boxSizing === 'border-box' ? padding + border : 0);\n    const overflow = Math.abs(outerHeight - innerHeight) <= 1;\n    return {\n      outerHeightStyle,\n      overflow\n    };\n  }, [maxRows, minRows, props.placeholder]);\n  const updateState = (prevState, newState) => {\n    const {\n      outerHeightStyle,\n      overflow\n    } = newState; // Need a large enough difference to update the height.\n    // This prevents infinite rendering loop.\n\n    if (renders.current < 20 && (outerHeightStyle > 0 && Math.abs((prevState.outerHeightStyle || 0) - outerHeightStyle) > 1 || prevState.overflow !== overflow)) {\n      renders.current += 1;\n      return {\n        overflow,\n        outerHeightStyle\n      };\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (renders.current === 20) {\n        console.error(['MUI: Too many re-renders. The layout is unstable.', 'TextareaAutosize limits the number of renders to prevent an infinite loop.'].join('\\n'));\n      }\n    }\n    return prevState;\n  };\n  const syncHeight = React.useCallback(() => {\n    const newState = getUpdatedState();\n    if (isEmpty(newState)) {\n      return;\n    }\n    setState(prevState => {\n      return updateState(prevState, newState);\n    });\n  }, [getUpdatedState]);\n  const syncHeightWithFlushSycn = () => {\n    const newState = getUpdatedState();\n    if (isEmpty(newState)) {\n      return;\n    } // In React 18, state updates in a ResizeObserver's callback are happening after the paint which causes flickering\n    // when doing some visual updates in it. Using flushSync ensures that the dom will be painted after the states updates happen\n    // Related issue - https://github.com/facebook/react/issues/24331\n\n    flushSync(() => {\n      setState(prevState => {\n        return updateState(prevState, newState);\n      });\n    });\n  };\n  React.useEffect(() => {\n    const handleResize = debounce(() => {\n      renders.current = 0; // If the TextareaAutosize component is replaced by Suspense with a fallback, the last\n      // ResizeObserver's handler that runs because of the change in the layout is trying to\n      // access a dom node that is no longer there (as the fallback component is being shown instead).\n      // See https://github.com/mui/material-ui/issues/32640\n      // TODO: Add tests that will ensure the component is not failing when\n      // replaced by Suspense with a fallback, once React is updated to version 18\n\n      if (inputRef.current) {\n        syncHeightWithFlushSycn();\n      }\n    });\n    const containerWindow = ownerWindow(inputRef.current);\n    containerWindow.addEventListener('resize', handleResize);\n    let resizeObserver;\n    if (typeof ResizeObserver !== 'undefined') {\n      resizeObserver = new ResizeObserver(handleResize);\n      resizeObserver.observe(inputRef.current);\n    }\n    return () => {\n      handleResize.clear();\n      containerWindow.removeEventListener('resize', handleResize);\n      if (resizeObserver) {\n        resizeObserver.disconnect();\n      }\n    };\n  });\n  useEnhancedEffect(() => {\n    syncHeight();\n  });\n  React.useEffect(() => {\n    renders.current = 0;\n  }, [value]);\n  const handleChange = event => {\n    renders.current = 0;\n    if (!isControlled) {\n      syncHeight();\n    }\n    if (onChange) {\n      onChange(event);\n    }\n  };\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(\"textarea\", _extends({\n      value: value,\n      onChange: handleChange,\n      ref: handleRef // Apply the rows prop to get a \"correct\" first SSR paint\n      ,\n\n      rows: minRows,\n      style: _extends({\n        height: state.outerHeightStyle,\n        // Need a large enough difference to allow scrolling.\n        // This prevents infinite rendering loop.\n        overflow: state.overflow ? 'hidden' : null\n      }, style)\n    }, other)), /*#__PURE__*/_jsx(\"textarea\", {\n      \"aria-hidden\": true,\n      className: props.className,\n      readOnly: true,\n      ref: shadowRef,\n      tabIndex: -1,\n      style: _extends({}, styles.shadow, style, {\n        padding: 0\n      })\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TextareaAutosize.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Maximum number of rows to display.\n   */\n  maxRows: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * Minimum number of rows to display.\n   * @default 1\n   */\n  minRows: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * @ignore\n   */\n  onChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  placeholder: PropTypes.string,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * @ignore\n   */\n  value: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string), PropTypes.number, PropTypes.string])\n} : void 0;\nexport default TextareaAutosize;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}