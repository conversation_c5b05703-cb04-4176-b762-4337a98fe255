{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"expandIcon\", \"focusVisibleClassName\", \"onClick\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '../zero-styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport ButtonBase from '../ButtonBase';\nimport AccordionContext from '../Accordion/AccordionContext';\nimport accordionSummaryClasses, { getAccordionSummaryUtilityClass } from './accordionSummaryClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    expanded,\n    disabled,\n    disableGutters\n  } = ownerState;\n  const slots = {\n    root: ['root', expanded && 'expanded', disabled && 'disabled', !disableGutters && 'gutters'],\n    focusVisible: ['focusVisible'],\n    content: ['content', expanded && 'expanded', !disableGutters && 'contentGutters'],\n    expandIconWrapper: ['expandIconWrapper', expanded && 'expanded']\n  };\n  return composeClasses(slots, getAccordionSummaryUtilityClass, classes);\n};\nconst AccordionSummaryRoot = styled(ButtonBase, {\n  name: 'MuiAccordionSummary',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref => {\n  let {\n    theme\n  } = _ref;\n  const transition = {\n    duration: theme.transitions.duration.shortest\n  };\n  return {\n    display: 'flex',\n    minHeight: 48,\n    padding: theme.spacing(0, 2),\n    transition: theme.transitions.create(['min-height', 'background-color'], transition),\n    [\"&.\".concat(accordionSummaryClasses.focusVisible)]: {\n      backgroundColor: (theme.vars || theme).palette.action.focus\n    },\n    [\"&.\".concat(accordionSummaryClasses.disabled)]: {\n      opacity: (theme.vars || theme).palette.action.disabledOpacity\n    },\n    [\"&:hover:not(.\".concat(accordionSummaryClasses.disabled, \")\")]: {\n      cursor: 'pointer'\n    },\n    variants: [{\n      props: props => !props.disableGutters,\n      style: {\n        [\"&.\".concat(accordionSummaryClasses.expanded)]: {\n          minHeight: 64\n        }\n      }\n    }]\n  };\n});\nconst AccordionSummaryContent = styled('div', {\n  name: 'MuiAccordionSummary',\n  slot: 'Content',\n  overridesResolver: (props, styles) => styles.content\n})(_ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    display: 'flex',\n    flexGrow: 1,\n    margin: '12px 0',\n    variants: [{\n      props: props => !props.disableGutters,\n      style: {\n        transition: theme.transitions.create(['margin'], {\n          duration: theme.transitions.duration.shortest\n        }),\n        [\"&.\".concat(accordionSummaryClasses.expanded)]: {\n          margin: '20px 0'\n        }\n      }\n    }]\n  };\n});\nconst AccordionSummaryExpandIconWrapper = styled('div', {\n  name: 'MuiAccordionSummary',\n  slot: 'ExpandIconWrapper',\n  overridesResolver: (props, styles) => styles.expandIconWrapper\n})(_ref3 => {\n  let {\n    theme\n  } = _ref3;\n  return {\n    display: 'flex',\n    color: (theme.vars || theme).palette.action.active,\n    transform: 'rotate(0deg)',\n    transition: theme.transitions.create('transform', {\n      duration: theme.transitions.duration.shortest\n    }),\n    [\"&.\".concat(accordionSummaryClasses.expanded)]: {\n      transform: 'rotate(180deg)'\n    }\n  };\n});\nconst AccordionSummary = /*#__PURE__*/React.forwardRef(function AccordionSummary(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAccordionSummary'\n  });\n  const {\n      children,\n      className,\n      expandIcon,\n      focusVisibleClassName,\n      onClick\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    disabled = false,\n    disableGutters,\n    expanded,\n    toggle\n  } = React.useContext(AccordionContext);\n  const handleChange = event => {\n    if (toggle) {\n      toggle(event);\n    }\n    if (onClick) {\n      onClick(event);\n    }\n  };\n  const ownerState = _extends({}, props, {\n    expanded,\n    disabled,\n    disableGutters\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(AccordionSummaryRoot, _extends({\n    focusRipple: false,\n    disableRipple: true,\n    disabled: disabled,\n    component: \"div\",\n    \"aria-expanded\": expanded,\n    className: clsx(classes.root, className),\n    focusVisibleClassName: clsx(classes.focusVisible, focusVisibleClassName),\n    onClick: handleChange,\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: [/*#__PURE__*/_jsx(AccordionSummaryContent, {\n      className: classes.content,\n      ownerState: ownerState,\n      children: children\n    }), expandIcon && /*#__PURE__*/_jsx(AccordionSummaryExpandIconWrapper, {\n      className: classes.expandIconWrapper,\n      ownerState: ownerState,\n      children: expandIcon\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? AccordionSummary.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The icon to display as the expand indicator.\n   */\n  expandIcon: PropTypes.node,\n  /**\n   * This prop can help identify which element has keyboard focus.\n   * The class name will be applied when the element gains the focus through keyboard interaction.\n   * It's a polyfill for the [CSS :focus-visible selector](https://drafts.csswg.org/selectors-4/#the-focus-visible-pseudo).\n   * The rationale for using this feature [is explained here](https://github.com/WICG/focus-visible/blob/HEAD/explainer.md).\n   * A [polyfill can be used](https://github.com/WICG/focus-visible) to apply a `focus-visible` class to other components\n   * if needed.\n   */\n  focusVisibleClassName: PropTypes.string,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default AccordionSummary;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}