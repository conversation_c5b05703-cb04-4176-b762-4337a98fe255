{"ast": null, "code": "/* eslint-disable prettier/prettier */// material-ui\nimport{IconButton,Stack,TableBody,TableCell,TableRow,Tooltip,Typography}from'@mui/material';import{FormattedMessage}from'react-intl';// project imports\nimport{dateFormat}from'utils/date';import{EApproveStatus,GROUP_ID_APPROVER}from'constants/Common';import{PERMISSIONS}from'constants/Permission';import{checkAllowedPermission}from'utils/authorization';// assets\nimport DownloadOutlinedIcon from'@mui/icons-material/DownloadOutlined';import EditTwoToneIcon from'@mui/icons-material/EditTwoTone';import DeleteOutlineOutlinedIcon from'@mui/icons-material/DeleteOutlineOutlined';import{useAppSelector}from'app/hooks';import{authSelector}from'store/slice/authSlice';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ManageOTTBody=props=>{var _userInfo$role;const{pageNumber,pageSize,otList,handleEdit,handleDelete}=props;const{manageOt}=PERMISSIONS.workingCalendar;const{userInfo}=useAppSelector(authSelector);const userGroup=userInfo===null||userInfo===void 0?void 0:(_userInfo$role=userInfo.role)===null||_userInfo$role===void 0?void 0:_userInfo$role.map(item=>item.groupId);return/*#__PURE__*/_jsx(TableBody,{children:otList.map((item,key)=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:pageSize*pageNumber+key+1}),/*#__PURE__*/_jsx(TableCell,{children:item.memberName}),/*#__PURE__*/_jsx(TableCell,{children:item.approveName}),/*#__PURE__*/_jsx(TableCell,{children:item.dept}),/*#__PURE__*/_jsx(TableCell,{children:!item.overTimeType?'-':/*#__PURE__*/_jsx(Tooltip,{title:item.overTimeType.includes(',')?/*#__PURE__*/_jsx(Stack,{direction:\"column\",spacing:0.5,children:item.overTimeType.split(',').map((type,index)=>/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:type.trim()})},index))}):null,placement:\"top\",arrow:true,children:/*#__PURE__*/_jsx(Typography,{children:item.overTimeType.includes(',')?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FormattedMessage,{id:item.overTimeType.split(',')[0].trim()}),/*#__PURE__*/_jsxs(Typography,{component:\"span\",color:\"textSecondary\",children:[\"+\",item.overTimeType.split(',').length-1]})]}):/*#__PURE__*/_jsx(FormattedMessage,{id:item.overTimeType})})})}),/*#__PURE__*/_jsx(TableCell,{children:dateFormat(item.fromDate)}),/*#__PURE__*/_jsx(TableCell,{children:dateFormat(item.toDate)}),/*#__PURE__*/_jsx(TableCell,{children:!item.status?'-':/*#__PURE__*/_jsx(Typography,{color:item.status===EApproveStatus.APPROVED?'#3163D4':item.status===EApproveStatus.DECLINED?'#A10000':item.status===EApproveStatus.AWAITING_QLTT||item.status===EApproveStatus.AWAITING_QLKT||item.status===EApproveStatus.AWAITING_HR?'#616161':'textPrimary',children:/*#__PURE__*/_jsx(FormattedMessage,{id:item.status})})}),/*#__PURE__*/_jsx(TableCell,{children:item.approvedDate&&dateFormat(item.approvedDate)}),/*#__PURE__*/_jsx(TableCell,{sx:{whiteSpace:'nowrap',overflow:'hidden',textOverflow:'ellipsis'},children:/*#__PURE__*/_jsxs(Stack,{direction:\"row\",justifyContent:\"center\",alignItems:\"center\",children:[/*#__PURE__*/_jsx(Tooltip,{placement:\"top\",title:/*#__PURE__*/_jsx(FormattedMessage,{id:\"edit\"}),onClick:()=>handleEdit(item),children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"edit\",size:\"small\",children:/*#__PURE__*/_jsx(EditTwoToneIcon,{sx:{fontSize:'1.1rem'}})})}),checkAllowedPermission(manageOt.approve)&&userGroup&&userGroup.includes(GROUP_ID_APPROVER.HR)&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Tooltip,{placement:\"top\",title:/*#__PURE__*/_jsx(FormattedMessage,{id:\"download\"}),onClick:()=>handleEdit(item),children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"download\",size:\"small\",children:/*#__PURE__*/_jsx(DownloadOutlinedIcon,{sx:{fontSize:'1.1rem'}})})}),checkAllowedPermission(manageOt.delete)&&/*#__PURE__*/_jsx(Tooltip,{placement:\"top\",title:/*#__PURE__*/_jsx(FormattedMessage,{id:\"delete\"}),onClick:()=>handleDelete(item.id),children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"delete\",size:\"small\",children:/*#__PURE__*/_jsx(DeleteOutlineOutlinedIcon,{sx:{fontSize:'1.1rem'}})})})]})]})})]},key))});};export default ManageOTTBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}