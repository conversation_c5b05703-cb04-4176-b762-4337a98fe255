{"ast": null, "code": "// third-party\nimport Chart from'react-apexcharts';import{FormattedMessage,useIntl}from'react-intl';// material-ui\nimport{useTheme}from'@mui/material/styles';import{Grid,TableContainer,Table,TableHead,TableRow,TableCell,TableBody}from'@mui/material';// project imports\nimport MainCard from'components/cards/MainCard';import SkeletonSummaryCard from'components/cards/Skeleton/SummaryCard';import{gridSpacing}from'store/constant';import{formatPrice}from'utils/common';import{nonBillCostByWeekChartOption}from'pages/non-billable-monitoring/Config';// =========================|| NonBill Cost By Week Card ||========================= //\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const NonBillCostByWeekCard=_ref=>{let{data,isLoading,year}=_ref;const theme=useTheme();const intl=useIntl();const newData=[...data.nonbillByWeekList].reverse();const labels=newData&&newData.map(item=>item.week.substring(0,7));const colors=['#4272df'];const series=[{name:\"\".concat(intl.formatMessage({id:'budget-by-week'})),data:newData.map(value=>value.budgetByWeek)}];return/*#__PURE__*/_jsx(_Fragment,{children:isLoading?/*#__PURE__*/_jsx(SkeletonSummaryCard,{}):/*#__PURE__*/_jsx(MainCard,{sx:{marginBottom:theme.spacing(gridSpacing)},title:/*#__PURE__*/_jsx(FormattedMessage,{id:\"NBM-ratio\"}),children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:5,children:/*#__PURE__*/_jsx(TableContainer,{sx:{maxHeight:300},children:/*#__PURE__*/_jsxs(Table,{\"aria-label\":\"simple table\",size:\"small\",stickyHeader:true,children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"weeks\"})}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"budget-by-week\"})})]})}),/*#__PURE__*/_jsx(TableBody,{children:data.nonbillByWeekList.map((item,key)=>{return/*#__PURE__*/_jsxs(TableRow,{sx:{'&:last-child td, &:last-child th':{border:0}},children:[/*#__PURE__*/_jsx(TableCell,{children:\"\".concat(item.week)}),/*#__PURE__*/_jsx(TableCell,{align:\"right\",children:formatPrice(item.budgetByWeek)})]},key);})})]})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:7,children:/*#__PURE__*/_jsx(Chart,{options:nonBillCostByWeekChartOption(labels,colors),series:series,type:\"bar\",height:450})})]})})});};export default NonBillCostByWeekCard;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}