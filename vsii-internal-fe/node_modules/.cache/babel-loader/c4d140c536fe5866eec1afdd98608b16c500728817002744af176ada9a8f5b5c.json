{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/utils/route-guard/NotFound.tsx\";\nimport { FormattedMessage } from 'react-intl';\nimport { Link } from 'react-router-dom';\n\n// material-ui\nimport { styled } from '@mui/material/styles';\nimport { Button, Card, CardContent, CardMedia, Grid, Typography } from '@mui/material';\n\n// project imports\nimport { DASHBOARD_PATH } from 'constants/Config';\nimport AnimateButton from 'components/extended/AnimateButton';\nimport { gridSpacing } from 'store/constant';\n\n// assets\nimport HomeTwoToneIcon from '@mui/icons-material/HomeTwoTone';\nimport imageBackground from 'assets/images/maintenance/img-error-bg.svg';\nimport imageBlue from 'assets/images/maintenance/img-error-blue.svg';\nimport imageText from 'assets/images/maintenance/img-error-text.svg';\nimport imagePurple from 'assets/images/maintenance/img-error-purple.svg';\n\n// styles\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CardMediaWrapper = styled('div')({\n  maxWidth: 720,\n  margin: '0 auto',\n  position: 'relative'\n});\n_c = CardMediaWrapper;\nconst ErrorWrapper = styled('div')({\n  maxWidth: 350,\n  margin: '0 auto',\n  textAlign: 'center'\n});\n_c2 = ErrorWrapper;\nconst ErrorCard = styled(Card)({\n  minHeight: '100vh',\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center'\n});\n_c3 = ErrorCard;\nconst CardMediaBlock = styled('img')({\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  width: '100%',\n  animation: '3s bounce ease-in-out infinite'\n});\n_c4 = CardMediaBlock;\nconst CardMediaBlue = styled('img')({\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  width: '100%',\n  animation: '15s wings ease-in-out infinite'\n});\n_c5 = CardMediaBlue;\nconst CardMediaPurple = styled('img')({\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  width: '100%',\n  animation: '12s wings ease-in-out infinite'\n});\n\n// ==============================|| ERROR PAGE ||============================== //\n_c6 = CardMediaPurple;\nconst NotFound = () => {\n  return /*#__PURE__*/_jsxDEV(ErrorCard, {\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        justifyContent: \"center\",\n        spacing: gridSpacing,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(CardMediaWrapper, {\n            children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n              component: \"img\",\n              image: imageBackground,\n              title: \"Slider5 image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(CardMediaBlock, {\n              src: imageText,\n              title: \"Slider 1 image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(CardMediaBlue, {\n              src: imageBlue,\n              title: \"Slider 2 image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(CardMediaPurple, {\n              src: imagePurple,\n              title: \"Slider 3 image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(ErrorWrapper, {\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: gridSpacing,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h1\",\n                  component: \"div\",\n                  children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n                    id: \"something-is-wrong\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 85,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n                    id: \"error-not-found-screen-centent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 90,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(AnimateButton, {\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"contained\",\n                    size: \"large\",\n                    component: Link,\n                    to: DASHBOARD_PATH,\n                    children: [/*#__PURE__*/_jsxDEV(HomeTwoToneIcon, {\n                      sx: {\n                        fontSize: '1.3rem',\n                        mr: 0.75\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 96,\n                      columnNumber: 45\n                    }, this), \" Home\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 95,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 94,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 9\n  }, this);\n};\n_c7 = NotFound;\nexport default NotFound;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"CardMediaWrapper\");\n$RefreshReg$(_c2, \"ErrorWrapper\");\n$RefreshReg$(_c3, \"ErrorCard\");\n$RefreshReg$(_c4, \"CardMediaBlock\");\n$RefreshReg$(_c5, \"CardMediaBlue\");\n$RefreshReg$(_c6, \"CardMediaPurple\");\n$RefreshReg$(_c7, \"NotFound\");", "map": {"version": 3, "names": ["FormattedMessage", "Link", "styled", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardMedia", "Grid", "Typography", "DASHBOARD_PATH", "AnimateButton", "gridSpacing", "HomeTwoToneIcon", "imageBackground", "imageBlue", "imageText", "imagePurple", "jsxDEV", "_jsxDEV", "CardMediaWrapper", "max<PERSON><PERSON><PERSON>", "margin", "position", "_c", "ErrorWrapper", "textAlign", "_c2", "ErrorCard", "minHeight", "display", "alignItems", "justifyContent", "_c3", "CardMediaBlock", "top", "left", "width", "animation", "_c4", "CardMediaBlue", "_c5", "CardMediaPurple", "_c6", "NotFound", "children", "container", "spacing", "item", "xs", "component", "image", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "variant", "id", "size", "to", "sx", "fontSize", "mr", "_c7", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/utils/route-guard/NotFound.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\nimport { Link } from 'react-router-dom';\n\n// material-ui\nimport { styled } from '@mui/material/styles';\nimport { <PERSON><PERSON>, Card, CardContent, CardMedia, Grid, Typography } from '@mui/material';\n\n// project imports\nimport { DASHBOARD_PATH } from 'constants/Config';\nimport AnimateButton from 'components/extended/AnimateButton';\nimport { gridSpacing } from 'store/constant';\n\n// assets\nimport HomeTwoToneIcon from '@mui/icons-material/HomeTwoTone';\n\nimport imageBackground from 'assets/images/maintenance/img-error-bg.svg';\nimport imageBlue from 'assets/images/maintenance/img-error-blue.svg';\nimport imageText from 'assets/images/maintenance/img-error-text.svg';\nimport imagePurple from 'assets/images/maintenance/img-error-purple.svg';\n\n// styles\nconst CardMediaWrapper = styled('div')({\n    maxWidth: 720,\n    margin: '0 auto',\n    position: 'relative'\n});\n\nconst ErrorWrapper = styled('div')({\n    maxWidth: 350,\n    margin: '0 auto',\n    textAlign: 'center'\n});\n\nconst ErrorCard = styled(Card)({\n    minHeight: '100vh',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center'\n});\n\nconst CardMediaBlock = styled('img')({\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    animation: '3s bounce ease-in-out infinite'\n});\n\nconst CardMediaBlue = styled('img')({\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    animation: '15s wings ease-in-out infinite'\n});\n\nconst CardMediaPurple = styled('img')({\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    animation: '12s wings ease-in-out infinite'\n});\n\n// ==============================|| ERROR PAGE ||============================== //\n\nconst NotFound = () => {\n    return (\n        <ErrorCard>\n            <CardContent>\n                <Grid container justifyContent=\"center\" spacing={gridSpacing}>\n                    <Grid item xs={12}>\n                        <CardMediaWrapper>\n                            <CardMedia component=\"img\" image={imageBackground} title=\"Slider5 image\" />\n                            <CardMediaBlock src={imageText} title=\"Slider 1 image\" />\n                            <CardMediaBlue src={imageBlue} title=\"Slider 2 image\" />\n                            <CardMediaPurple src={imagePurple} title=\"Slider 3 image\" />\n                        </CardMediaWrapper>\n                    </Grid>\n                    <Grid item xs={12}>\n                        <ErrorWrapper>\n                            <Grid container spacing={gridSpacing}>\n                                <Grid item xs={12}>\n                                    <Typography variant=\"h1\" component=\"div\">\n                                        <FormattedMessage id=\"something-is-wrong\" />\n                                    </Typography>\n                                </Grid>\n                                <Grid item xs={12}>\n                                    <Typography variant=\"body2\">\n                                        <FormattedMessage id=\"error-not-found-screen-centent\" />\n                                    </Typography>\n                                </Grid>\n                                <Grid item xs={12}>\n                                    <AnimateButton>\n                                        <Button variant=\"contained\" size=\"large\" component={Link} to={DASHBOARD_PATH}>\n                                            <HomeTwoToneIcon sx={{ fontSize: '1.3rem', mr: 0.75 }} /> Home\n                                        </Button>\n                                    </AnimateButton>\n                                </Grid>\n                            </Grid>\n                        </ErrorWrapper>\n                    </Grid>\n                </Grid>\n            </CardContent>\n        </ErrorCard>\n    );\n};\n\nexport default NotFound;\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,IAAI,QAAQ,kBAAkB;;AAEvC;AACA,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,MAAM,EAAEC,IAAI,EAAEC,WAAW,EAAEC,SAAS,EAAEC,IAAI,EAAEC,UAAU,QAAQ,eAAe;;AAEtF;AACA,SAASC,cAAc,QAAQ,kBAAkB;AACjD,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,SAASC,WAAW,QAAQ,gBAAgB;;AAE5C;AACA,OAAOC,eAAe,MAAM,iCAAiC;AAE7D,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,SAAS,MAAM,8CAA8C;AACpE,OAAOC,SAAS,MAAM,8CAA8C;AACpE,OAAOC,WAAW,MAAM,gDAAgD;;AAExE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,gBAAgB,GAAGjB,MAAM,CAAC,KAAK,CAAC,CAAC;EACnCkB,QAAQ,EAAE,GAAG;EACbC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE;AACd,CAAC,CAAC;AAACC,EAAA,GAJGJ,gBAAgB;AAMtB,MAAMK,YAAY,GAAGtB,MAAM,CAAC,KAAK,CAAC,CAAC;EAC/BkB,QAAQ,EAAE,GAAG;EACbC,MAAM,EAAE,QAAQ;EAChBI,SAAS,EAAE;AACf,CAAC,CAAC;AAACC,GAAA,GAJGF,YAAY;AAMlB,MAAMG,SAAS,GAAGzB,MAAM,CAACE,IAAI,CAAC,CAAC;EAC3BwB,SAAS,EAAE,OAAO;EAClBC,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBC,cAAc,EAAE;AACpB,CAAC,CAAC;AAACC,GAAA,GALGL,SAAS;AAOf,MAAMM,cAAc,GAAG/B,MAAM,CAAC,KAAK,CAAC,CAAC;EACjCoB,QAAQ,EAAE,UAAU;EACpBY,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,MAAM;EACbC,SAAS,EAAE;AACf,CAAC,CAAC;AAACC,GAAA,GANGL,cAAc;AAQpB,MAAMM,aAAa,GAAGrC,MAAM,CAAC,KAAK,CAAC,CAAC;EAChCoB,QAAQ,EAAE,UAAU;EACpBY,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,MAAM;EACbC,SAAS,EAAE;AACf,CAAC,CAAC;AAACG,GAAA,GANGD,aAAa;AAQnB,MAAME,eAAe,GAAGvC,MAAM,CAAC,KAAK,CAAC,CAAC;EAClCoB,QAAQ,EAAE,UAAU;EACpBY,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,MAAM;EACbC,SAAS,EAAE;AACf,CAAC,CAAC;;AAEF;AAAAK,GAAA,GARMD,eAAe;AAUrB,MAAME,QAAQ,GAAGA,CAAA,KAAM;EACnB,oBACIzB,OAAA,CAACS,SAAS;IAAAiB,QAAA,eACN1B,OAAA,CAACb,WAAW;MAAAuC,QAAA,eACR1B,OAAA,CAACX,IAAI;QAACsC,SAAS;QAACd,cAAc,EAAC,QAAQ;QAACe,OAAO,EAAEnC,WAAY;QAAAiC,QAAA,gBACzD1B,OAAA,CAACX,IAAI;UAACwC,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAJ,QAAA,eACd1B,OAAA,CAACC,gBAAgB;YAAAyB,QAAA,gBACb1B,OAAA,CAACZ,SAAS;cAAC2C,SAAS,EAAC,KAAK;cAACC,KAAK,EAAErC,eAAgB;cAACsC,KAAK,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3ErC,OAAA,CAACe,cAAc;cAACuB,GAAG,EAAEzC,SAAU;cAACoC,KAAK,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzDrC,OAAA,CAACqB,aAAa;cAACiB,GAAG,EAAE1C,SAAU;cAACqC,KAAK,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxDrC,OAAA,CAACuB,eAAe;cAACe,GAAG,EAAExC,WAAY;cAACmC,KAAK,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACPrC,OAAA,CAACX,IAAI;UAACwC,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAJ,QAAA,eACd1B,OAAA,CAACM,YAAY;YAAAoB,QAAA,eACT1B,OAAA,CAACX,IAAI;cAACsC,SAAS;cAACC,OAAO,EAAEnC,WAAY;cAAAiC,QAAA,gBACjC1B,OAAA,CAACX,IAAI;gBAACwC,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAJ,QAAA,eACd1B,OAAA,CAACV,UAAU;kBAACiD,OAAO,EAAC,IAAI;kBAACR,SAAS,EAAC,KAAK;kBAAAL,QAAA,eACpC1B,OAAA,CAAClB,gBAAgB;oBAAC0D,EAAE,EAAC;kBAAoB;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACPrC,OAAA,CAACX,IAAI;gBAACwC,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAJ,QAAA,eACd1B,OAAA,CAACV,UAAU;kBAACiD,OAAO,EAAC,OAAO;kBAAAb,QAAA,eACvB1B,OAAA,CAAClB,gBAAgB;oBAAC0D,EAAE,EAAC;kBAAgC;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACPrC,OAAA,CAACX,IAAI;gBAACwC,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAJ,QAAA,eACd1B,OAAA,CAACR,aAAa;kBAAAkC,QAAA,eACV1B,OAAA,CAACf,MAAM;oBAACsD,OAAO,EAAC,WAAW;oBAACE,IAAI,EAAC,OAAO;oBAACV,SAAS,EAAEhD,IAAK;oBAAC2D,EAAE,EAAEnD,cAAe;oBAAAmC,QAAA,gBACzE1B,OAAA,CAACN,eAAe;sBAACiD,EAAE,EAAE;wBAAEC,QAAQ,EAAE,QAAQ;wBAAEC,EAAE,EAAE;sBAAK;oBAAE;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,SAC7D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEpB,CAAC;AAACS,GAAA,GAxCIrB,QAAQ;AA0Cd,eAAeA,QAAQ;AAAC,IAAApB,EAAA,EAAAG,GAAA,EAAAM,GAAA,EAAAM,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAsB,GAAA;AAAAC,YAAA,CAAA1C,EAAA;AAAA0C,YAAA,CAAAvC,GAAA;AAAAuC,YAAA,CAAAjC,GAAA;AAAAiC,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAzB,GAAA;AAAAyB,YAAA,CAAAvB,GAAA;AAAAuB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}