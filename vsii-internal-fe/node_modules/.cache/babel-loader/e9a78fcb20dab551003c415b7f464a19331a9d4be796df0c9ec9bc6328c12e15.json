{"ast": null, "code": "import { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { isVariantLabel } from './is-variant-label.mjs';\nconst variantProps = [\"initial\", \"animate\", \"exit\", \"whileHover\", \"whileDrag\", \"whileTap\", \"whileFocus\", \"whileInView\"];\nfunction isControllingVariants(props) {\n  return isAnimationControls(props.animate) || variantProps.some(name => isVariantLabel(props[name]));\n}\nfunction isVariantNode(props) {\n  return Boolean(isControllingVariants(props) || props.variants);\n}\nexport { isControllingVariants, isVariantNode };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}