{"ast": null, "code": "// material-ui\nimport{<PERSON><PERSON><PERSON><PERSON>on,<PERSON>ack,TableBody,TableCell,TableRow}from'@mui/material';// projects import\nimport{DeleteTwoToneIcon}from'assets/images/icons';import{Input,NumericFormatCustom}from'components/extended/Form';import{E_BIDDING_STATUS}from'constants/Common';import{Role}from'containers/search';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const MonthlyBillableTBody=props=>{const{fieldsRateByMonth,removeRateByMonth,status}=props;return/*#__PURE__*/_jsx(TableBody,{children:fieldsRateByMonth.map((item,index)=>/*#__PURE__*/_jsxs(TableRow,{sx:{'& .MuiFormHelperText-root':{whiteSpace:'nowrap'},'& .MuiFormControl-root':{height:'50px'},'&.MuiTableRow-root':{'& td':{borderColor:'transparent','&.MuiTableCell-root:nth-of-type(2) .MuiFormHelperText-root':{position:'absolute'}}}},children:[/*#__PURE__*/_jsx(TableCell,{children:index+1}),/*#__PURE__*/_jsx(TableCell,{sx:{'& .MuiInputBase-root':{width:'110px'}},children:/*#__PURE__*/_jsx(Role,{isShowName:\"rateByMonth.\".concat(index,\".role\"),isShowLabel:true,disabled:status===E_BIDDING_STATUS.CONTRACT})}),/*#__PURE__*/_jsx(TableCell,{sx:{'& .MuiFormControl-root':{marginTop:'10px'}},children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"rateByMonth.\".concat(index,\".rate\"),disabled:status===E_BIDDING_STATUS.CONTRACT})}),/*#__PURE__*/_jsx(TableCell,{sx:{'& .MuiFormControl-root':{marginTop:'10px'}},children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},disabled:true,name:\"rateByMonth.\".concat(index,\".rateVND\")})}),/*#__PURE__*/_jsx(TableCell,{sx:{'& .MuiFormControl-root':{marginTop:'10px'}},children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"rateByMonth.\".concat(index,\".quantity\"),disabled:status===E_BIDDING_STATUS.CONTRACT})}),/*#__PURE__*/_jsx(TableCell,{sx:{display:'none','& .MuiFormControl-root':{marginTop:'10px'}},children:/*#__PURE__*/_jsx(Input,{disabled:true,textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"rateByMonth.\".concat(index,\".amount\")})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Stack,{direction:\"row\",justifyContent:\"center\",alignItems:\"center\",children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"delete\",size:\"small\",onClick:()=>removeRateByMonth(index),disabled:status===E_BIDDING_STATUS.CONTRACT,children:/*#__PURE__*/_jsx(DeleteTwoToneIcon,{sx:{fontSize:'1.1rem'}})})})})]},item.id))});};export default MonthlyBillableTBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}