{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/weekly-effort/WeeklyEffortProjectTBody.tsx\",\n  _s = $RefreshSig$();\n// materia-ui\nimport { TableBody, TableCell, TableRow } from '@mui/material';\n\n// project imports\nimport { rankSelector } from 'store/slice/rankSlice';\nimport { useAppSelector } from 'app/hooks';\nimport { formatPrice } from 'utils/common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WeeklyEffortProjectTBody = props => {\n  _s();\n  const {\n    pageNumber,\n    pageSize,\n    projects\n  } = props;\n  const {\n    rank\n  } = useAppSelector(rankSelector);\n  return /*#__PURE__*/_jsxDEV(TableBody, {\n    children: projects.map((item, key) => /*#__PURE__*/_jsxDEV(TableRow, {\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        children: pageSize * pageNumber + key + 1\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: item.projectName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: item.department\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          fontWeight: '700 !important'\n        },\n        children: parseFloat(item.totalEffort).toFixed(1)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 21\n      }, this), rank.map((r, index) => {\n        var _item$rank$find;\n        const cost = ((_item$rank$find = item.rank.find(r2 => r2.level === r.rankName)) === null || _item$rank$find === void 0 ? void 0 : _item$rank$find.cost) || 0;\n        return /*#__PURE__*/_jsxDEV(TableCell, {\n          children: formatPrice(cost)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 32\n        }, this);\n      }), /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          fontWeight: '700 !important'\n        },\n        children: formatPrice(item.totalCost)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 21\n      }, this)]\n    }, key, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 17\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 9\n  }, this);\n};\n_s(WeeklyEffortProjectTBody, \"a0laFAP5fRaNsSZtYsfiEr4uvxI=\", false, function () {\n  return [useAppSelector];\n});\n_c = WeeklyEffortProjectTBody;\nexport default WeeklyEffortProjectTBody;\nvar _c;\n$RefreshReg$(_c, \"WeeklyEffortProjectTBody\");", "map": {"version": 3, "names": ["TableBody", "TableCell", "TableRow", "rankSelector", "useAppSelector", "formatPrice", "jsxDEV", "_jsxDEV", "WeeklyEffortProjectTBody", "props", "_s", "pageNumber", "pageSize", "projects", "rank", "children", "map", "item", "key", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "projectName", "department", "sx", "fontWeight", "parseFloat", "totalEffort", "toFixed", "r", "index", "_item$rank$find", "cost", "find", "r2", "level", "rankName", "totalCost", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/weekly-effort/WeeklyEffortProjectTBody.tsx"], "sourcesContent": ["// materia-ui\nimport { TableBody, TableCell, TableRow } from '@mui/material';\n\n// project imports\nimport { rankSelector } from 'store/slice/rankSlice';\nimport { IWeeklyEffortProject } from 'types';\nimport { useAppSelector } from 'app/hooks';\nimport { formatPrice } from 'utils/common';\n\ninterface IWeeklyEffortProjectTBodyProps {\n    pageNumber: number;\n    pageSize: number;\n    projects: IWeeklyEffortProject[];\n}\n\nconst WeeklyEffortProjectTBody = (props: IWeeklyEffortProjectTBodyProps) => {\n    const { pageNumber, pageSize, projects } = props;\n    const { rank } = useAppSelector(rankSelector);\n\n    return (\n        <TableBody>\n            {projects.map((item, key) => (\n                <TableRow key={key}>\n                    <TableCell>{pageSize * pageNumber + key + 1}</TableCell>\n                    <TableCell>{item.projectName}</TableCell>\n                    <TableCell>{item.department}</TableCell>\n                    <TableCell sx={{ fontWeight: '700 !important' }}>{parseFloat(item.totalEffort).toFixed(1)}</TableCell>\n                    {rank.map((r, index) => {\n                        const cost = item.rank.find((r2) => r2.level === r.rankName)?.cost || 0;\n                        return <TableCell key={index}>{formatPrice(cost)}</TableCell>;\n                    })}\n                    <TableCell sx={{ fontWeight: '700 !important' }}>{formatPrice(item.totalCost)}</TableCell>\n                </TableRow>\n            ))}\n        </TableBody>\n    );\n};\n\nexport default WeeklyEffortProjectTBody;\n"], "mappings": ";;AAAA;AACA,SAASA,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;;AAE9D;AACA,SAASC,YAAY,QAAQ,uBAAuB;AAEpD,SAASC,cAAc,QAAQ,WAAW;AAC1C,SAASC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ3C,MAAMC,wBAAwB,GAAIC,KAAqC,IAAK;EAAAC,EAAA;EACxE,MAAM;IAAEC,UAAU;IAAEC,QAAQ;IAAEC;EAAS,CAAC,GAAGJ,KAAK;EAChD,MAAM;IAAEK;EAAK,CAAC,GAAGV,cAAc,CAACD,YAAY,CAAC;EAE7C,oBACII,OAAA,CAACP,SAAS;IAAAe,QAAA,EACLF,QAAQ,CAACG,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,kBACpBX,OAAA,CAACL,QAAQ;MAAAa,QAAA,gBACLR,OAAA,CAACN,SAAS;QAAAc,QAAA,EAAEH,QAAQ,GAAGD,UAAU,GAAGO,GAAG,GAAG;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACxDf,OAAA,CAACN,SAAS;QAAAc,QAAA,EAAEE,IAAI,CAACM;MAAW;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACzCf,OAAA,CAACN,SAAS;QAAAc,QAAA,EAAEE,IAAI,CAACO;MAAU;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACxCf,OAAA,CAACN,SAAS;QAACwB,EAAE,EAAE;UAAEC,UAAU,EAAE;QAAiB,CAAE;QAAAX,QAAA,EAAEY,UAAU,CAACV,IAAI,CAACW,WAAW,CAAC,CAACC,OAAO,CAAC,CAAC;MAAC;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EACrGR,IAAI,CAACE,GAAG,CAAC,CAACc,CAAC,EAAEC,KAAK,KAAK;QAAA,IAAAC,eAAA;QACpB,MAAMC,IAAI,GAAG,EAAAD,eAAA,GAAAf,IAAI,CAACH,IAAI,CAACoB,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAACC,KAAK,KAAKN,CAAC,CAACO,QAAQ,CAAC,cAAAL,eAAA,uBAA/CA,eAAA,CAAiDC,IAAI,KAAI,CAAC;QACvE,oBAAO1B,OAAA,CAACN,SAAS;UAAAc,QAAA,EAAcV,WAAW,CAAC4B,IAAI;QAAC,GAAzBF,KAAK;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgC,CAAC;MACjE,CAAC,CAAC,eACFf,OAAA,CAACN,SAAS;QAACwB,EAAE,EAAE;UAAEC,UAAU,EAAE;QAAiB,CAAE;QAAAX,QAAA,EAAEV,WAAW,CAACY,IAAI,CAACqB,SAAS;MAAC;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA,GAT/EJ,GAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAUR,CACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEpB,CAAC;AAACZ,EAAA,CArBIF,wBAAwB;EAAA,QAETJ,cAAc;AAAA;AAAAmC,EAAA,GAF7B/B,wBAAwB;AAuB9B,eAAeA,wBAAwB;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}