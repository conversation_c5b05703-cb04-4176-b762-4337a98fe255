{"ast": null, "code": "import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getSelectUnstyledUtilityClass(slot) {\n  return generateUtilityClass('MuiSelectUnstyled', slot);\n}\nconst selectUnstyledClasses = generateUtilityClasses('MuiSelectUnstyled', ['root', 'button', 'listbox', 'popper', 'active', 'expanded', 'disabled', 'focusVisible']);\nexport default selectUnstyledClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}