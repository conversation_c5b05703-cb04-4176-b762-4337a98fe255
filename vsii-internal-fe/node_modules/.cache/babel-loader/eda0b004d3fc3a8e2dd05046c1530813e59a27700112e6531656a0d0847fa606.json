{"ast": null, "code": "import{FormattedMessage}from'react-intl';// material-ui\nimport{TableCell,TableHead,TableRow}from'@mui/material';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ExchangeRateConfigThead=()=>{const{exchange_rate_config}=TEXT_CONFIG_SCREEN.administration;return/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:exchange_rate_config+'no'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:exchange_rate_config+'year'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:exchange_rate_config+'currency'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:exchange_rate_config+'exchange-rate'})}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:exchange_rate_config+'actions'})})]})});};export default ExchangeRateConfigThead;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}