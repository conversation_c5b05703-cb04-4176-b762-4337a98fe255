{"ast": null, "code": "import{useEffect,useState}from'react';// third party\nimport{yupResolver}from'@hookform/resolvers/yup';import{FormattedMessage}from'react-intl';// material-ui\nimport{LoadingButton}from'@mui/lab';import{Button,DialogActions,Grid}from'@mui/material';// project imports\nimport{useAppDispatch,useAppSelector}from'app/hooks';import{FormProvider,Input}from'components/extended/Form';import Modal from'components/extended/Modal';import Api from'constants/Api';import{DATE_FORMAT}from'constants/Common';import{commentFormDefault,commentFormSchema}from'pages/Config';import sendRequest from'services/ApiService';import{gridSpacing}from'store/constant';import{closeCommentDialog,conditionsSelector,openSelector,changeCommented,titleDetailSelector}from'store/slice/commentSlice';import{openSnackbar}from'store/slice/snackbarSlice';import{isEmpty}from'utils/common';import{authSelector}from'store/slice/authSlice';import{convertWeekFromToDate,dateFormat,getNumberOfWeek}from'utils/date';// ==============================|| Comment Modal ||============================== //\nimport{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const CommentDialog=()=>{const dispatch=useAppDispatch();const[comment,setComment]=useState();const{userInfo}=useAppSelector(authSelector);const open=useAppSelector(openSelector);const conditions=useAppSelector(conditionsSelector);const titleDetail=useAppSelector(titleDetailSelector);const week=conditions!==null&&conditions!==void 0&&conditions.week?getNumberOfWeek(conditions.week):'';const weekSelected=conditions!==null&&conditions!==void 0&&conditions.week?convertWeekFromToDate(conditions.week):'';const currentDate=new Date();const formattedDate=dateFormat(currentDate,DATE_FORMAT.DDMMYYYYHHmmss);const title=conditions!==null&&conditions!==void 0&&conditions.userId?'update-comment':'update-report-comment';// Functions\nconst getFindComment=async conditions=>{const request={...conditions,...weekSelected,week};const response=await sendRequest(conditions.userId?Api.comment.getFindCommentDetail:Api.comment.getFindComment,request);if(response){const{content}=response.result;setComment(content);}};const postSaveOrUpdateComment=async value=>{const isEmptyComment=isEmpty(comment);const{idHexString}=comment!==null&&comment!==void 0?comment:{};const params={...conditions,fromDate:value.fromDate,toDate:value.toDate,note:value.note,week,...weekSelected,idHexString,userName:null,[isEmptyComment?'userCreate':'userUpdate']:userInfo===null||userInfo===void 0?void 0:userInfo.userName,[isEmptyComment?'dateCreate':'lastUpdate']:formattedDate};const response=await sendRequest(conditions&&conditions.userId?Api.comment.postSaveOrUpdateCommentDetail:Api.comment.postSaveOrUpdateComment,params);if(response){const message='comment-success';dispatch(openSnackbar({open:true,message,variant:'alert',alert:{color:'success'}}));dispatch(changeCommented(true));handleClose();}};// Event\nconst handleClose=()=>{dispatch(closeCommentDialog());setComment({...comment,note:''});};const handleSubmit=value=>{postSaveOrUpdateComment(value);};// Effect\nuseEffect(()=>{open&&conditions&&getFindComment(conditions);// eslint-disable-next-line react-hooks/exhaustive-deps\n},[open,conditions]);return/*#__PURE__*/_jsx(Modal,{isOpen:open,title:title,titleDetail:titleDetail,onClose:handleClose,keepMounted:false,children:/*#__PURE__*/_jsxs(FormProvider,{form:{defaultValues:commentFormDefault,resolver:yupResolver(commentFormSchema)},formReset:comment,onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(Grid,{container:true,spacing:gridSpacing,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:12,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{multiline:true,rows:4},name:\"note\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:\"comment\"})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:12,children:/*#__PURE__*/_jsx(\"em\",{children:comment!==null&&comment!==void 0&&comment.userUpdate?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FormattedMessage,{id:\"last-update-comment\"}),\" \",/*#__PURE__*/_jsx(\"strong\",{children:comment.userUpdate}),\" -\",' ',/*#__PURE__*/_jsx(\"strong\",{children:dateFormat(comment.lastUpdate,DATE_FORMAT.HHmmssDDMMYYYY)})]}):comment!==null&&comment!==void 0&&comment.userCreate?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FormattedMessage,{id:\"last-update-comment\"}),\" \",/*#__PURE__*/_jsx(\"strong\",{children:comment.userCreate}),\" -\",' ',/*#__PURE__*/_jsx(\"strong\",{children:dateFormat(comment.dateCreate,DATE_FORMAT.HHmmssDDMMYYYY)})]}):''})})]}),/*#__PURE__*/_jsxs(DialogActions,{style:{marginTop:'20px'},children:[/*#__PURE__*/_jsx(Button,{color:\"error\",onClick:handleClose,children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"cancel\"})}),/*#__PURE__*/_jsx(LoadingButton,{variant:\"contained\",type:\"submit\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"submit\"})})]})]})});};export default CommentDialog;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}