{"ast": null, "code": "var castPath = require('./_castPath'),\n  isArguments = require('./isArguments'),\n  isArray = require('./isArray'),\n  isIndex = require('./_isIndex'),\n  isLength = require('./isLength'),\n  toKey = require('./_toKey');\n\n/**\n * Checks if `path` exists on `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array|string} path The path to check.\n * @param {Function} hasFunc The function to check properties.\n * @returns {boolean} Returns `true` if `path` exists, else `false`.\n */\nfunction hasPath(object, path, hasFunc) {\n  path = castPath(path, object);\n  var index = -1,\n    length = path.length,\n    result = false;\n  while (++index < length) {\n    var key = toKey(path[index]);\n    if (!(result = object != null && hasFunc(object, key))) {\n      break;\n    }\n    object = object[key];\n  }\n  if (result || ++index != length) {\n    return result;\n  }\n  length = object == null ? 0 : object.length;\n  return !!length && isLength(length) && isIndex(key, length) && (isArray(object) || isArguments(object));\n}\nmodule.exports = hasPath;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}