{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/ExchangeRateConfigTBody.tsx\";\n// material-ui\nimport { TableBody, TableCell, TableRow, Stack, Tooltip, IconButton } from '@mui/material';\nimport HighlightOffIcon from '@mui/icons-material/HighlightOff';\n\n// assets\nimport EditTwoToneIcon from '@mui/icons-material/EditTwoTone';\n\n//projects import\nimport { PERMISSIONS } from 'constants/Permission';\nimport { checkAllowedPermission } from 'utils/authorization';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\nimport { formatPrice } from 'utils/common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExchangeRateConfigTBody = props => {\n  const {\n    exchangeRates,\n    handleOpen,\n    handleDelete,\n    pageSize,\n    pageNumber\n  } = props;\n  const {\n    exchangeRatePermission\n  } = PERMISSIONS.admin;\n  return /*#__PURE__*/_jsxDEV(TableBody, {\n    children: exchangeRates === null || exchangeRates === void 0 ? void 0 : exchangeRates.map((exchangeRate, key) => /*#__PURE__*/_jsxDEV(TableRow, {\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        children: pageSize * pageNumber + key + 1\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: exchangeRate.year\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: exchangeRate.currency\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: formatPrice(exchangeRate.exchangeRate)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 21\n      }, this), checkAllowedPermission(exchangeRatePermission.edit) && /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          justifyContent: \"center\",\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n            placement: \"top\",\n            title: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: 'edit'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 65\n            }, this),\n            onClick: () => handleOpen(exchangeRate),\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              \"aria-label\": \"edit\",\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(EditTwoToneIcon, {\n                sx: {\n                  fontSize: '1.1rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            placement: \"top\",\n            title: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: \"delete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 44\n            }, this),\n            onClick: () => handleDelete(exchangeRate),\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              \"aria-label\": \"delete\",\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(HighlightOffIcon, {\n                sx: {\n                  fontSize: '1.1rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 25\n      }, this)]\n    }, key, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 17\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 9\n  }, this);\n};\n_c = ExchangeRateConfigTBody;\nexport default ExchangeRateConfigTBody;\nvar _c;\n$RefreshReg$(_c, \"ExchangeRateConfigTBody\");", "map": {"version": 3, "names": ["TableBody", "TableCell", "TableRow", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "IconButton", "HighlightOffIcon", "EditTwoToneIcon", "PERMISSIONS", "checkAllowedPermission", "FormattedMessage", "formatPrice", "jsxDEV", "_jsxDEV", "ExchangeRateConfigTBody", "props", "exchangeRates", "handleOpen", "handleDelete", "pageSize", "pageNumber", "exchangeRatePermission", "admin", "children", "map", "exchangeRate", "key", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "year", "currency", "edit", "direction", "justifyContent", "alignItems", "placement", "title", "id", "onClick", "size", "sx", "fontSize", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/ExchangeRateConfigTBody.tsx"], "sourcesContent": ["// material-ui\nimport { TableBody, TableCell, TableRow, Stack, Tooltip, IconButton } from '@mui/material';\nimport HighlightOffIcon from '@mui/icons-material/HighlightOff';\n\n// assets\nimport EditTwoToneIcon from '@mui/icons-material/EditTwoTone';\n\n//projects import\nimport { PERMISSIONS } from 'constants/Permission';\nimport { checkAllowedPermission } from 'utils/authorization';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\nimport { IExchangeRate } from 'types';\nimport { formatPrice } from 'utils/common';\n\ninterface IExchangeRateConfigProps {\n    exchangeRates: IExchangeRate[];\n    handleOpen: (exchange: IExchangeRate) => void;\n    handleDelete: (exchange: IExchangeRate) => void;\n    pageSize: number;\n    pageNumber: number;\n}\n\nconst ExchangeRateConfigTBody = (props: IExchangeRateConfigProps) => {\n    const { exchangeRates, handleOpen, handleDelete, pageSize, pageNumber } = props;\n    const { exchangeRatePermission } = PERMISSIONS.admin;\n\n    return (\n        <TableBody>\n            {exchangeRates?.map((exchangeRate: IExchangeRate, key: number) => (\n                <TableRow key={key}>\n                    <TableCell>{pageSize * pageNumber + key + 1}</TableCell>\n                    <TableCell>{exchangeRate.year}</TableCell>\n                    <TableCell>{exchangeRate.currency}</TableCell>\n                    <TableCell>{formatPrice(exchangeRate.exchangeRate)}</TableCell>\n                    {checkAllowedPermission(exchangeRatePermission.edit) && (\n                        <TableCell>\n                            <Stack direction=\"row\" justifyContent=\"center\" alignItems=\"center\">\n                                <Tooltip placement=\"top\" title={<FormattedMessage id={'edit'} />} onClick={() => handleOpen(exchangeRate)}>\n                                    <IconButton aria-label=\"edit\" size=\"small\">\n                                        <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />\n                                    </IconButton>\n                                </Tooltip>\n                                <Tooltip\n                                    placement=\"top\"\n                                    title={<FormattedMessage id=\"delete\" />}\n                                    onClick={() => handleDelete(exchangeRate)}\n                                >\n                                    <IconButton aria-label=\"delete\" size=\"small\">\n                                        <HighlightOffIcon sx={{ fontSize: '1.1rem' }} />\n                                    </IconButton>\n                                </Tooltip>\n                            </Stack>\n                        </TableCell>\n                    )}\n                </TableRow>\n            ))}\n        </TableBody>\n    );\n};\n\nexport default ExchangeRateConfigTBody;\n"], "mappings": ";AAAA;AACA,SAASA,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAEC,UAAU,QAAQ,eAAe;AAC1F,OAAOC,gBAAgB,MAAM,kCAAkC;;AAE/D;AACA,OAAOC,eAAe,MAAM,iCAAiC;;AAE7D;AACA,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,sBAAsB,QAAQ,qBAAqB;;AAE5D;AACA,SAASC,gBAAgB,QAAQ,YAAY;AAE7C,SAASC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAU3C,MAAMC,uBAAuB,GAAIC,KAA+B,IAAK;EACjE,MAAM;IAAEC,aAAa;IAAEC,UAAU;IAAEC,YAAY;IAAEC,QAAQ;IAAEC;EAAW,CAAC,GAAGL,KAAK;EAC/E,MAAM;IAAEM;EAAuB,CAAC,GAAGb,WAAW,CAACc,KAAK;EAEpD,oBACIT,OAAA,CAACb,SAAS;IAAAuB,QAAA,EACLP,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEQ,GAAG,CAAC,CAACC,YAA2B,EAAEC,GAAW,kBACzDb,OAAA,CAACX,QAAQ;MAAAqB,QAAA,gBACLV,OAAA,CAACZ,SAAS;QAAAsB,QAAA,EAAEJ,QAAQ,GAAGC,UAAU,GAAGM,GAAG,GAAG;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACxDjB,OAAA,CAACZ,SAAS;QAAAsB,QAAA,EAAEE,YAAY,CAACM;MAAI;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC1CjB,OAAA,CAACZ,SAAS;QAAAsB,QAAA,EAAEE,YAAY,CAACO;MAAQ;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC9CjB,OAAA,CAACZ,SAAS;QAAAsB,QAAA,EAAEZ,WAAW,CAACc,YAAY,CAACA,YAAY;MAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAC9DrB,sBAAsB,CAACY,sBAAsB,CAACY,IAAI,CAAC,iBAChDpB,OAAA,CAACZ,SAAS;QAAAsB,QAAA,eACNV,OAAA,CAACV,KAAK;UAAC+B,SAAS,EAAC,KAAK;UAACC,cAAc,EAAC,QAAQ;UAACC,UAAU,EAAC,QAAQ;UAAAb,QAAA,gBAC9DV,OAAA,CAACT,OAAO;YAACiC,SAAS,EAAC,KAAK;YAACC,KAAK,eAAEzB,OAAA,CAACH,gBAAgB;cAAC6B,EAAE,EAAE;YAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACU,OAAO,EAAEA,CAAA,KAAMvB,UAAU,CAACQ,YAAY,CAAE;YAAAF,QAAA,eACtGV,OAAA,CAACR,UAAU;cAAC,cAAW,MAAM;cAACoC,IAAI,EAAC,OAAO;cAAAlB,QAAA,eACtCV,OAAA,CAACN,eAAe;gBAACmC,EAAE,EAAE;kBAAEC,QAAQ,EAAE;gBAAS;cAAE;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACVjB,OAAA,CAACT,OAAO;YACJiC,SAAS,EAAC,KAAK;YACfC,KAAK,eAAEzB,OAAA,CAACH,gBAAgB;cAAC6B,EAAE,EAAC;YAAQ;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxCU,OAAO,EAAEA,CAAA,KAAMtB,YAAY,CAACO,YAAY,CAAE;YAAAF,QAAA,eAE1CV,OAAA,CAACR,UAAU;cAAC,cAAW,QAAQ;cAACoC,IAAI,EAAC,OAAO;cAAAlB,QAAA,eACxCV,OAAA,CAACP,gBAAgB;gBAACoC,EAAE,EAAE;kBAAEC,QAAQ,EAAE;gBAAS;cAAE;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACd;IAAA,GAxBUJ,GAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAyBR,CACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEpB,CAAC;AAACc,EAAA,GApCI9B,uBAAuB;AAsC7B,eAAeA,uBAAuB;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}