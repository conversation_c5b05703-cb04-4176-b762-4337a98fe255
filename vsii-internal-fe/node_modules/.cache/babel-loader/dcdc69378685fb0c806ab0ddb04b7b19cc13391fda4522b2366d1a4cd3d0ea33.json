{"ast": null, "code": "import ValidationError from '../ValidationError';\nconst once = cb => {\n  let fired = false;\n  return function () {\n    if (fired) return;\n    fired = true;\n    cb(...arguments);\n  };\n};\nexport default function runTests(options, cb) {\n  let {\n    endEarly,\n    tests,\n    args,\n    value,\n    errors,\n    sort,\n    path\n  } = options;\n  let callback = once(cb);\n  let count = tests.length;\n  const nestedErrors = [];\n  errors = errors ? errors : [];\n  if (!count) return errors.length ? callback(new ValidationError(errors, value, path)) : callback(null, value);\n  for (let i = 0; i < tests.length; i++) {\n    const test = tests[i];\n    test(args, function finishTestRun(err) {\n      if (err) {\n        // always return early for non validation errors\n        if (!ValidationError.isError(err)) {\n          return callback(err, value);\n        }\n        if (endEarly) {\n          err.value = value;\n          return callback(err, value);\n        }\n        nestedErrors.push(err);\n      }\n      if (--count <= 0) {\n        if (nestedErrors.length) {\n          if (sort) nestedErrors.sort(sort); //show parent errors after the nested ones: name.first, name\n\n          if (errors.length) nestedErrors.push(...errors);\n          errors = nestedErrors;\n        }\n        if (errors.length) {\n          callback(new ValidationError(errors, value, path), value);\n          return;\n        }\n        callback(null, value);\n      }\n    });\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}