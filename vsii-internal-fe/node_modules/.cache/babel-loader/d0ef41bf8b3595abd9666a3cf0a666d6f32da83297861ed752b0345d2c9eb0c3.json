{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"defaultValue\", \"children\", \"component\", \"components\", \"componentsProps\", \"disabled\", \"error\", \"onChange\", \"required\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_useControlled as useControlled } from '@mui/utils';\nimport FormControlUnstyledContext from './FormControlUnstyledContext';\nimport { getFormControlUnstyledUtilityClass } from './formControlUnstyledClasses';\nimport { useSlotProps } from '../utils';\nimport composeClasses from '../composeClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction hasValue(value) {\n  return value != null && !(Array.isArray(value) && value.length === 0) && value !== '';\n}\nfunction useUtilityClasses(ownerState) {\n  const {\n    disabled,\n    error,\n    filled,\n    focused,\n    required\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', focused && 'focused', error && 'error', filled && 'filled', required && 'required']\n  };\n  return composeClasses(slots, getFormControlUnstyledUtilityClass, {});\n}\n/**\n * Provides context such as filled/focused/error/required for form inputs.\n * Relying on the context provides high flexibility and ensures that the state always stays\n * consistent across the children of the `FormControl`.\n * This context is used by the following components:\n *\n * *   FormLabel\n * *   FormHelperText\n * *   Input\n * *   InputLabel\n *\n * You can find one composition example below and more going to [the demos](https://mui.com/material-ui/react-text-field/#components).\n *\n * ```jsx\n * <FormControl>\n *   <InputLabel htmlFor=\"my-input\">Email address</InputLabel>\n *   <Input id=\"my-input\" aria-describedby=\"my-helper-text\" />\n *   <FormHelperText id=\"my-helper-text\">We'll never share your email.</FormHelperText>\n * </FormControl>\n * ```\n *\n * ⚠️ Only one `Input` can be used within a FormControl because it create visual inconsistencies.\n * For instance, only one input can be focused at the same time, the state shouldn't be shared.\n *\n * Demos:\n *\n * - [Unstyled form control](https://mui.com/base/react-form-control/)\n *\n * API:\n *\n * - [FormControlUnstyled API](https://mui.com/base/api/form-control-unstyled/)\n */\n\nconst FormControlUnstyled = /*#__PURE__*/React.forwardRef(function FormControlUnstyled(props, ref) {\n  var _ref;\n  const {\n      defaultValue,\n      children,\n      component,\n      components = {},\n      componentsProps = {},\n      disabled = false,\n      error = false,\n      onChange,\n      required = false,\n      value: incomingValue\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const [value, setValue] = useControlled({\n    controlled: incomingValue,\n    default: defaultValue,\n    name: 'FormControl',\n    state: 'value'\n  });\n  const filled = hasValue(value);\n  const [focused, setFocused] = React.useState(false);\n  if (disabled && focused) {\n    setFocused(false);\n  }\n  const ownerState = _extends({}, props, {\n    disabled,\n    error,\n    filled,\n    focused,\n    required\n  });\n  const handleChange = event => {\n    setValue(event.target.value);\n    onChange == null ? void 0 : onChange(event);\n  };\n  const childContext = {\n    disabled,\n    error,\n    filled,\n    focused,\n    onBlur: () => {\n      setFocused(false);\n    },\n    onChange: handleChange,\n    onFocus: () => {\n      setFocused(true);\n    },\n    required,\n    value: value != null ? value : ''\n  };\n  const classes = useUtilityClasses(ownerState);\n  const renderChildren = () => {\n    if (typeof children === 'function') {\n      return children(childContext);\n    }\n    return children;\n  };\n  const Root = (_ref = component != null ? component : components.Root) != null ? _ref : 'div';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref,\n      children: renderChildren()\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(FormControlUnstyledContext.Provider, {\n    value: childContext,\n    children: /*#__PURE__*/_jsx(Root, _extends({}, rootProps))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? FormControlUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * The content of the component.\n   */\n  children: PropTypes\n  /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.node, PropTypes.func]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the FormControl.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * @ignore\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * @ignore\n   */\n  defaultValue: PropTypes.any,\n  /**\n   * If `true`, the label, input and helper text should be displayed in a disabled state.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the label is displayed in an error state.\n   * @default false\n   */\n  error: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the label will indicate that the `input` is required.\n   * @default false\n   */\n  required: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  value: PropTypes.any\n} : void 0;\nexport default FormControlUnstyled;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}