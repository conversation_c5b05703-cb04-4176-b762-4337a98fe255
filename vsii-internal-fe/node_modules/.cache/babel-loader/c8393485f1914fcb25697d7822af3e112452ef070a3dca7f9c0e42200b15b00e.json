{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Typography from '@mui/material/Typography';\nimport { styled, useTheme, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/material';\nimport clsx from 'clsx';\nimport { PickersDay } from '../PickersDay/PickersDay';\nimport { useUtils, useNow } from '../internals/hooks/useUtils';\nimport { DAY_SIZE, DAY_MARGIN } from '../internals/constants/dimensions';\nimport { PickersSlideTransition } from './PickersSlideTransition';\nimport { useIsDayDisabled } from '../internals/hooks/validation/useDateValidation';\nimport { findClosestEnabledDate } from '../internals/utils/date-utils';\nimport { getDayPickerUtilityClass } from './dayPickerClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { createElement as _createElement } from \"react\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    header: ['header'],\n    weekDayLabel: ['weekDayLabel'],\n    loadingContainer: ['loadingContainer'],\n    slideTransition: ['slideTransition'],\n    monthContainer: ['monthContainer'],\n    weekContainer: ['weekContainer']\n  };\n  return composeClasses(slots, getDayPickerUtilityClass, classes);\n};\nconst defaultDayOfWeekFormatter = day => day.charAt(0).toUpperCase();\nconst weeksContainerHeight = (DAY_SIZE + DAY_MARGIN * 2) * 6;\nconst PickersCalendarDayHeader = styled('div', {\n  name: 'MuiDayPicker',\n  slot: 'Header',\n  overridesResolver: (_, styles) => styles.header\n})({\n  display: 'flex',\n  justifyContent: 'center',\n  alignItems: 'center'\n});\nconst PickersCalendarWeekDayLabel = styled(Typography, {\n  name: 'MuiDayPicker',\n  slot: 'WeekDayLabel',\n  overridesResolver: (_, styles) => styles.weekDayLabel\n})(({\n  theme\n}) => ({\n  width: 36,\n  height: 40,\n  margin: '0 2px',\n  textAlign: 'center',\n  display: 'flex',\n  justifyContent: 'center',\n  alignItems: 'center',\n  color: theme.palette.text.secondary\n}));\nconst PickersCalendarLoadingContainer = styled('div', {\n  name: 'MuiDayPicker',\n  slot: 'LoadingContainer',\n  overridesResolver: (_, styles) => styles.loadingContainer\n})({\n  display: 'flex',\n  justifyContent: 'center',\n  alignItems: 'center',\n  minHeight: weeksContainerHeight\n});\nconst PickersCalendarSlideTransition = styled(PickersSlideTransition, {\n  name: 'MuiDayPicker',\n  slot: 'SlideTransition',\n  overridesResolver: (_, styles) => styles.slideTransition\n})({\n  minHeight: weeksContainerHeight\n});\nconst PickersCalendarWeekContainer = styled('div', {\n  name: 'MuiDayPicker',\n  slot: 'MonthContainer',\n  overridesResolver: (_, styles) => styles.monthContainer\n})({\n  overflow: 'hidden'\n});\nconst PickersCalendarWeek = styled('div', {\n  name: 'MuiDayPicker',\n  slot: 'WeekContainer',\n  overridesResolver: (_, styles) => styles.weekContainer\n})({\n  margin: `${DAY_MARGIN}px 0`,\n  display: 'flex',\n  justifyContent: 'center'\n});\n/**\n * @ignore - do not document.\n */\n\nexport function DayPicker(inProps) {\n  const now = useNow();\n  const utils = useUtils();\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiDayPicker'\n  });\n  const classes = useUtilityClasses(props);\n  const {\n    onFocusedDayChange,\n    className,\n    currentMonth,\n    selectedDays,\n    disabled,\n    disableHighlightToday,\n    focusedDay,\n    isMonthSwitchingAnimating,\n    loading,\n    onSelectedDaysChange,\n    onMonthSwitchingAnimationEnd,\n    readOnly,\n    reduceAnimations,\n    renderDay,\n    renderLoading = () => /*#__PURE__*/_jsx(\"span\", {\n      children: \"...\"\n    }),\n    showDaysOutsideCurrentMonth,\n    slideDirection,\n    TransitionProps,\n    disablePast,\n    disableFuture,\n    minDate,\n    maxDate,\n    shouldDisableDate,\n    dayOfWeekFormatter = defaultDayOfWeekFormatter,\n    hasFocus,\n    onFocusedViewChange,\n    gridLabelId\n  } = props;\n  const isDateDisabled = useIsDayDisabled({\n    shouldDisableDate,\n    minDate,\n    maxDate,\n    disablePast,\n    disableFuture\n  });\n  const [internalFocusedDay, setInternalFocusedDay] = React.useState(() => focusedDay || now);\n  const changeHasFocus = React.useCallback(newHasFocus => {\n    if (onFocusedViewChange) {\n      onFocusedViewChange(newHasFocus);\n    }\n  }, [onFocusedViewChange]);\n  const handleDaySelect = React.useCallback((day, isFinish = 'finish') => {\n    if (readOnly) {\n      return;\n    }\n    onSelectedDaysChange(day, isFinish);\n  }, [onSelectedDaysChange, readOnly]);\n  const focusDay = React.useCallback(day => {\n    if (!isDateDisabled(day)) {\n      onFocusedDayChange(day);\n      setInternalFocusedDay(day);\n      changeHasFocus(true);\n    }\n  }, [isDateDisabled, onFocusedDayChange, changeHasFocus]);\n  const theme = useTheme();\n  function handleKeyDown(event, day) {\n    switch (event.key) {\n      case 'ArrowUp':\n        focusDay(utils.addDays(day, -7));\n        event.preventDefault();\n        break;\n      case 'ArrowDown':\n        focusDay(utils.addDays(day, 7));\n        event.preventDefault();\n        break;\n      case 'ArrowLeft':\n        {\n          const newFocusedDayDefault = utils.addDays(day, theme.direction === 'ltr' ? -1 : 1);\n          const nextAvailableMonth = theme.direction === 'ltr' ? utils.getPreviousMonth(day) : utils.getNextMonth(day);\n          const closestDayToFocus = findClosestEnabledDate({\n            utils,\n            date: newFocusedDayDefault,\n            minDate: theme.direction === 'ltr' ? utils.startOfMonth(nextAvailableMonth) : newFocusedDayDefault,\n            maxDate: theme.direction === 'ltr' ? newFocusedDayDefault : utils.endOfMonth(nextAvailableMonth),\n            isDateDisabled\n          });\n          focusDay(closestDayToFocus || newFocusedDayDefault);\n          event.preventDefault();\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const newFocusedDayDefault = utils.addDays(day, theme.direction === 'ltr' ? 1 : -1);\n          const nextAvailableMonth = theme.direction === 'ltr' ? utils.getNextMonth(day) : utils.getPreviousMonth(day);\n          const closestDayToFocus = findClosestEnabledDate({\n            utils,\n            date: newFocusedDayDefault,\n            minDate: theme.direction === 'ltr' ? newFocusedDayDefault : utils.startOfMonth(nextAvailableMonth),\n            maxDate: theme.direction === 'ltr' ? utils.endOfMonth(nextAvailableMonth) : newFocusedDayDefault,\n            isDateDisabled\n          });\n          focusDay(closestDayToFocus || newFocusedDayDefault);\n          event.preventDefault();\n          break;\n        }\n      case 'Home':\n        focusDay(utils.startOfWeek(day));\n        event.preventDefault();\n        break;\n      case 'End':\n        focusDay(utils.endOfWeek(day));\n        event.preventDefault();\n        break;\n      case 'PageUp':\n        focusDay(utils.getNextMonth(day));\n        event.preventDefault();\n        break;\n      case 'PageDown':\n        focusDay(utils.getPreviousMonth(day));\n        event.preventDefault();\n        break;\n      default:\n        break;\n    }\n  }\n  function handleFocus(event, day) {\n    focusDay(day);\n  }\n  function handleBlur(event, day) {\n    if (hasFocus && utils.isSameDay(internalFocusedDay, day)) {\n      changeHasFocus(false);\n    }\n  }\n  const currentMonthNumber = utils.getMonth(currentMonth);\n  const validSelectedDays = selectedDays.filter(day => !!day).map(day => utils.startOfDay(day)); // need a new ref whenever the `key` of the transition changes: http://reactcommunity.org/react-transition-group/transition/#Transition-prop-nodeRef.\n\n  const transitionKey = currentMonthNumber; // eslint-disable-next-line react-hooks/exhaustive-deps\n\n  const slideNodeRef = React.useMemo(() => /*#__PURE__*/React.createRef(), [transitionKey]);\n  const startOfCurrentWeek = utils.startOfWeek(now);\n  const focusableDay = React.useMemo(() => {\n    const startOfMonth = utils.startOfMonth(currentMonth);\n    const endOfMonth = utils.endOfMonth(currentMonth);\n    if (isDateDisabled(internalFocusedDay) || utils.isAfterDay(internalFocusedDay, endOfMonth) || utils.isBeforeDay(internalFocusedDay, startOfMonth)) {\n      return findClosestEnabledDate({\n        utils,\n        date: internalFocusedDay,\n        minDate: startOfMonth,\n        maxDate: endOfMonth,\n        disablePast,\n        disableFuture,\n        isDateDisabled\n      });\n    }\n    return internalFocusedDay;\n  }, [currentMonth, disableFuture, disablePast, internalFocusedDay, isDateDisabled, utils]);\n  return /*#__PURE__*/_jsxs(\"div\", {\n    role: \"grid\",\n    \"aria-labelledby\": gridLabelId,\n    children: [/*#__PURE__*/_jsx(PickersCalendarDayHeader, {\n      role: \"row\",\n      className: classes.header,\n      children: utils.getWeekdays().map((day, i) => {\n        var _dayOfWeekFormatter;\n        return /*#__PURE__*/_jsx(PickersCalendarWeekDayLabel, {\n          variant: \"caption\",\n          role: \"columnheader\",\n          \"aria-label\": utils.format(utils.addDays(startOfCurrentWeek, i), 'weekday'),\n          className: classes.weekDayLabel,\n          children: (_dayOfWeekFormatter = dayOfWeekFormatter == null ? void 0 : dayOfWeekFormatter(day)) != null ? _dayOfWeekFormatter : day\n        }, day + i.toString());\n      })\n    }), loading ? /*#__PURE__*/_jsx(PickersCalendarLoadingContainer, {\n      className: classes.loadingContainer,\n      children: renderLoading()\n    }) : /*#__PURE__*/_jsx(PickersCalendarSlideTransition, _extends({\n      transKey: transitionKey,\n      onExited: onMonthSwitchingAnimationEnd,\n      reduceAnimations: reduceAnimations,\n      slideDirection: slideDirection,\n      className: clsx(className, classes.slideTransition)\n    }, TransitionProps, {\n      nodeRef: slideNodeRef,\n      children: /*#__PURE__*/_jsx(PickersCalendarWeekContainer, {\n        ref: slideNodeRef,\n        role: \"rowgroup\",\n        className: classes.monthContainer,\n        children: utils.getWeekArray(currentMonth).map(week => /*#__PURE__*/_jsx(PickersCalendarWeek, {\n          role: \"row\",\n          className: classes.weekContainer,\n          children: week.map(day => {\n            const isFocusableDay = focusableDay !== null && utils.isSameDay(day, focusableDay);\n            const isSelected = validSelectedDays.some(selectedDay => utils.isSameDay(selectedDay, day));\n            const isToday = utils.isSameDay(day, now);\n            const pickersDayProps = {\n              key: day == null ? void 0 : day.toString(),\n              day,\n              isAnimating: isMonthSwitchingAnimating,\n              disabled: disabled || isDateDisabled(day),\n              autoFocus: hasFocus && isFocusableDay,\n              today: isToday,\n              outsideCurrentMonth: utils.getMonth(day) !== currentMonthNumber,\n              selected: isSelected,\n              disableHighlightToday,\n              showDaysOutsideCurrentMonth,\n              onKeyDown: handleKeyDown,\n              onFocus: handleFocus,\n              onBlur: handleBlur,\n              onDaySelect: handleDaySelect,\n              tabIndex: isFocusableDay ? 0 : -1,\n              role: 'gridcell',\n              'aria-selected': isSelected\n            };\n            if (isToday) {\n              pickersDayProps['aria-current'] = 'date';\n            }\n            return renderDay ? renderDay(day, validSelectedDays, pickersDayProps) : /*#__PURE__*/_createElement(PickersDay, _extends({}, pickersDayProps, {\n              key: pickersDayProps.key\n            }));\n          })\n        }, `week-${week[0]}`))\n      })\n    }))]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}