{"ast": null, "code": "import{FormattedMessage}from'react-intl';// material-ui\nimport{Box,Button,Stack,useTheme}from'@mui/material';// project imports\nimport{Table}from'components/extended/Table';import{gridSpacing}from'store/constant';import{quotaUpdateHistoryDefault}from'pages/administration/Config';import QuotaUpdateHistoryThead from'./QuotaUpdateHistoryThead';import QuotaUpdateHistoryTBody from'./QuotaUpdateHistoryTBody';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const QuotaUpdateHistory=props=>{const theme=useTheme();const{quotaUpdateHistories,handleClose}=props;return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Table,{heads:/*#__PURE__*/_jsx(QuotaUpdateHistoryThead,{}),data:quotaUpdateHistories||quotaUpdateHistoryDefault,children:/*#__PURE__*/_jsx(QuotaUpdateHistoryTBody,{quotaUpdateHistories:quotaUpdateHistories||quotaUpdateHistoryDefault})}),/*#__PURE__*/_jsx(Box,{sx:{marginTop:theme.spacing(gridSpacing)},children:/*#__PURE__*/_jsx(Stack,{direction:\"row\",spacing:1,justifyContent:\"flex-end\",children:/*#__PURE__*/_jsx(Button,{color:\"error\",onClick:handleClose,children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"cancel\"})})})})]});};export default QuotaUpdateHistory;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}