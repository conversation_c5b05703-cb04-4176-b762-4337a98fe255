{"ast": null, "code": "var isArray = require('./isArray'),\n  isKey = require('./_isKey'),\n  stringToPath = require('./_stringToPath'),\n  toString = require('./toString');\n\n/**\n * Casts `value` to a path array if it's not one.\n *\n * @private\n * @param {*} value The value to inspect.\n * @param {Object} [object] The object to query keys on.\n * @returns {Array} Returns the cast property path array.\n */\nfunction castPath(value, object) {\n  if (isArray(value)) {\n    return value;\n  }\n  return isKey(value, object) ? [value] : stringToPath(toString(value));\n}\nmodule.exports = castPath;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}