{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/extended/Table/ReorderTableRow.tsx\",\n  _s = $RefreshSig$();\nimport { TableRow } from '@mui/material';\nimport React, { useRef } from 'react';\n/* eslint-disable import/no-extraneous-dependencies */\nimport { useDrag, useDrop } from 'react-dnd';\n\n// Define the draggable type\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DraggableTypes = {\n  REORDERABLE_ROW: 'reorderable_row'\n};\n\n// Define the interface for the item being dragged\n\n// Define props interface\n\nconst style = {\n  border: '1px solid gray',\n  padding: '10px',\n  marginBottom: '5px',\n  backgroundColor: 'white',\n  cursor: 'move'\n};\nconst ReorderableRow = ({\n  id,\n  children,\n  index,\n  moveRow,\n  sx\n}) => {\n  _s();\n  const ref = useRef(null);\n  const [, drop] = useDrop({\n    accept: DraggableTypes.REORDERABLE_ROW,\n    hover(item, monitor) {\n      var _ref$current;\n      if (!ref.current) {\n        return;\n      }\n      const dragIndex = item.index;\n      const hoverIndex = index;\n      if (dragIndex === hoverIndex) {\n        return;\n      }\n      const hoverBoundingRect = (_ref$current = ref.current) === null || _ref$current === void 0 ? void 0 : _ref$current.getBoundingClientRect();\n      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;\n      const clientOffset = monitor.getClientOffset();\n      if (!clientOffset) {\n        return;\n      }\n      const hoverClientY = clientOffset.y - hoverBoundingRect.top;\n      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {\n        return;\n      }\n      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {\n        return;\n      }\n      moveRow(dragIndex, hoverIndex);\n      item.index = hoverIndex;\n    }\n  });\n  const [{\n    isDragging\n  }, drag] = useDrag({\n    type: DraggableTypes.REORDERABLE_ROW,\n    item: {\n      id,\n      index,\n      type: DraggableTypes.REORDERABLE_ROW\n    },\n    collect: monitor => ({\n      isDragging: monitor.isDragging()\n    })\n  });\n  const opacity = isDragging ? 0 : 1;\n  drag(drop(ref));\n  return /*#__PURE__*/_jsxDEV(TableRow, {\n    ref: ref,\n    style: {\n      ...style,\n      opacity\n    },\n    sx: sx,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 9\n  }, this);\n};\n_s(ReorderableRow, \"SYYd1my1HJmIW9rZNaWEabF/XNs=\", false, function () {\n  return [useDrop, useDrag];\n});\n_c = ReorderableRow;\nexport default ReorderableRow;\nvar _c;\n$RefreshReg$(_c, \"ReorderableRow\");", "map": {"version": 3, "names": ["TableRow", "React", "useRef", "useDrag", "useDrop", "jsxDEV", "_jsxDEV", "DraggableTypes", "REORDERABLE_ROW", "style", "border", "padding", "marginBottom", "backgroundColor", "cursor", "ReorderableRow", "id", "children", "index", "moveRow", "sx", "_s", "ref", "drop", "accept", "hover", "item", "monitor", "_ref$current", "current", "dragIndex", "hoverIndex", "hoverBoundingRect", "getBoundingClientRect", "hoverMiddleY", "bottom", "top", "clientOffset", "getClientOffset", "hoverClientY", "y", "isDragging", "drag", "type", "collect", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/extended/Table/ReorderTableRow.tsx"], "sourcesContent": ["import { SxProps, TableRow } from '@mui/material';\nimport React, { useRef } from 'react';\n/* eslint-disable import/no-extraneous-dependencies */\nimport { useDrag, useDrop, DropTargetMonitor } from 'react-dnd';\n\n// Define the draggable type\nconst DraggableTypes = {\n    REORDERABLE_ROW: 'reorderable_row'\n};\n\n// Define the interface for the item being dragged\ninterface DragItem {\n    index: number;\n    id: string | number;\n    type: string;\n}\n\n// Define props interface\ninterface ReorderableRowProps {\n    id: string | number;\n    children: React.ReactNode;\n    index: number;\n    moveRow: (dragIndex: number, hoverIndex: number) => void;\n    sx?: SxProps;\n}\n\nconst style: React.CSSProperties = {\n    border: '1px solid gray',\n    padding: '10px',\n    marginBottom: '5px',\n    backgroundColor: 'white',\n    cursor: 'move'\n};\n\nconst ReorderableRow: React.FC<ReorderableRowProps> = ({ id, children, index, moveRow, sx }) => {\n    const ref = useRef<HTMLTableRowElement>(null);\n\n    const [, drop] = useDrop<DragItem>({\n        accept: DraggableTypes.REORDERABLE_ROW,\n        hover(item, monitor: DropTargetMonitor) {\n            if (!ref.current) {\n                return;\n            }\n            const dragIndex = item.index;\n            const hoverIndex = index;\n\n            if (dragIndex === hoverIndex) {\n                return;\n            }\n\n            const hoverBoundingRect = ref.current?.getBoundingClientRect();\n            const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;\n            const clientOffset = monitor.getClientOffset();\n\n            if (!clientOffset) {\n                return;\n            }\n\n            const hoverClientY = clientOffset.y - hoverBoundingRect.top;\n\n            if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {\n                return;\n            }\n\n            if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {\n                return;\n            }\n\n            moveRow(dragIndex, hoverIndex);\n            item.index = hoverIndex;\n        }\n    });\n\n    const [{ isDragging }, drag] = useDrag({\n        type: DraggableTypes.REORDERABLE_ROW,\n        item: { id, index, type: DraggableTypes.REORDERABLE_ROW },\n        collect: (monitor) => ({\n            isDragging: monitor.isDragging()\n        })\n    });\n\n    const opacity = isDragging ? 0 : 1;\n    drag(drop(ref));\n\n    return (\n        <TableRow ref={ref} style={{ ...style, opacity }} sx={sx}>\n            {children}\n        </TableRow>\n    );\n};\n\nexport default ReorderableRow;\n"], "mappings": ";;AAAA,SAAkBA,QAAQ,QAAQ,eAAe;AACjD,OAAOC,KAAK,IAAIC,MAAM,QAAQ,OAAO;AACrC;AACA,SAASC,OAAO,EAAEC,OAAO,QAA2B,WAAW;;AAE/D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAG;EACnBC,eAAe,EAAE;AACrB,CAAC;;AAED;;AAOA;;AASA,MAAMC,KAA0B,GAAG;EAC/BC,MAAM,EAAE,gBAAgB;EACxBC,OAAO,EAAE,MAAM;EACfC,YAAY,EAAE,KAAK;EACnBC,eAAe,EAAE,OAAO;EACxBC,MAAM,EAAE;AACZ,CAAC;AAED,MAAMC,cAA6C,GAAGA,CAAC;EAAEC,EAAE;EAAEC,QAAQ;EAAEC,KAAK;EAAEC,OAAO;EAAEC;AAAG,CAAC,KAAK;EAAAC,EAAA;EAC5F,MAAMC,GAAG,GAAGpB,MAAM,CAAsB,IAAI,CAAC;EAE7C,MAAM,GAAGqB,IAAI,CAAC,GAAGnB,OAAO,CAAW;IAC/BoB,MAAM,EAAEjB,cAAc,CAACC,eAAe;IACtCiB,KAAKA,CAACC,IAAI,EAAEC,OAA0B,EAAE;MAAA,IAAAC,YAAA;MACpC,IAAI,CAACN,GAAG,CAACO,OAAO,EAAE;QACd;MACJ;MACA,MAAMC,SAAS,GAAGJ,IAAI,CAACR,KAAK;MAC5B,MAAMa,UAAU,GAAGb,KAAK;MAExB,IAAIY,SAAS,KAAKC,UAAU,EAAE;QAC1B;MACJ;MAEA,MAAMC,iBAAiB,IAAAJ,YAAA,GAAGN,GAAG,CAACO,OAAO,cAAAD,YAAA,uBAAXA,YAAA,CAAaK,qBAAqB,CAAC,CAAC;MAC9D,MAAMC,YAAY,GAAG,CAACF,iBAAiB,CAACG,MAAM,GAAGH,iBAAiB,CAACI,GAAG,IAAI,CAAC;MAC3E,MAAMC,YAAY,GAAGV,OAAO,CAACW,eAAe,CAAC,CAAC;MAE9C,IAAI,CAACD,YAAY,EAAE;QACf;MACJ;MAEA,MAAME,YAAY,GAAGF,YAAY,CAACG,CAAC,GAAGR,iBAAiB,CAACI,GAAG;MAE3D,IAAIN,SAAS,GAAGC,UAAU,IAAIQ,YAAY,GAAGL,YAAY,EAAE;QACvD;MACJ;MAEA,IAAIJ,SAAS,GAAGC,UAAU,IAAIQ,YAAY,GAAGL,YAAY,EAAE;QACvD;MACJ;MAEAf,OAAO,CAACW,SAAS,EAAEC,UAAU,CAAC;MAC9BL,IAAI,CAACR,KAAK,GAAGa,UAAU;IAC3B;EACJ,CAAC,CAAC;EAEF,MAAM,CAAC;IAAEU;EAAW,CAAC,EAAEC,IAAI,CAAC,GAAGvC,OAAO,CAAC;IACnCwC,IAAI,EAAEpC,cAAc,CAACC,eAAe;IACpCkB,IAAI,EAAE;MAAEV,EAAE;MAAEE,KAAK;MAAEyB,IAAI,EAAEpC,cAAc,CAACC;IAAgB,CAAC;IACzDoC,OAAO,EAAGjB,OAAO,KAAM;MACnBc,UAAU,EAAEd,OAAO,CAACc,UAAU,CAAC;IACnC,CAAC;EACL,CAAC,CAAC;EAEF,MAAMI,OAAO,GAAGJ,UAAU,GAAG,CAAC,GAAG,CAAC;EAClCC,IAAI,CAACnB,IAAI,CAACD,GAAG,CAAC,CAAC;EAEf,oBACIhB,OAAA,CAACN,QAAQ;IAACsB,GAAG,EAAEA,GAAI;IAACb,KAAK,EAAE;MAAE,GAAGA,KAAK;MAAEoC;IAAQ,CAAE;IAACzB,EAAE,EAAEA,EAAG;IAAAH,QAAA,EACpDA;EAAQ;IAAA6B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEnB,CAAC;AAAC5B,EAAA,CAvDIN,cAA6C;EAAA,QAG9BX,OAAO,EAoCOD,OAAO;AAAA;AAAA+C,EAAA,GAvCpCnC,cAA6C;AAyDnD,eAAeA,cAAc;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}