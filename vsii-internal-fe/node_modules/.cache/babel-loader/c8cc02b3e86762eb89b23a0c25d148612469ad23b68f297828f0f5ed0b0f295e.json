{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/DepartmentBidding.tsx\";\n// project imports\nimport { Select } from 'components/extended/Form';\nimport { DEFAULT_VALUE_OPTION_SELECT, DEPARTMENT_BIDDING_OPTION } from 'constants/Common';\nimport { FormattedMessage } from 'react-intl';\nimport { searchFormConfig } from './Config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DepartmentBidding = props => {\n  const {\n    required,\n    name,\n    disabled,\n    label\n  } = props;\n  return /*#__PURE__*/_jsxDEV(Select, {\n    required: required,\n    disabled: disabled,\n    selects: [DEFAULT_VALUE_OPTION_SELECT, ...DEPARTMENT_BIDDING_OPTION],\n    name: name,\n    label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n      id: label || searchFormConfig.department.label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 20\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 9\n  }, this);\n};\n_c = DepartmentBidding;\nexport default DepartmentBidding;\nvar _c;\n$RefreshReg$(_c, \"DepartmentBidding\");", "map": {"version": 3, "names": ["Select", "DEFAULT_VALUE_OPTION_SELECT", "DEPARTMENT_BIDDING_OPTION", "FormattedMessage", "searchFormConfig", "jsxDEV", "_jsxDEV", "DepartmentBidding", "props", "required", "name", "disabled", "label", "selects", "id", "department", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/DepartmentBidding.tsx"], "sourcesContent": ["// project imports\nimport { Select } from 'components/extended/Form';\nimport { DEFAULT_VALUE_OPTION_SELECT, DEPARTMENT_BIDDING_OPTION } from 'constants/Common';\nimport { FormattedMessage } from 'react-intl';\nimport { searchFormConfig } from './Config';\n\ninterface IDepartmentBiddingProps {\n    name: string;\n    required?: boolean;\n    disabled?: boolean;\n    label?: string;\n}\n\nconst DepartmentBidding = (props: IDepartmentBiddingProps) => {\n    const { required, name, disabled, label } = props;\n\n    return (\n        <Select\n            required={required}\n            disabled={disabled}\n            selects={[DEFAULT_VALUE_OPTION_SELECT, ...DEPARTMENT_BIDDING_OPTION]}\n            name={name}\n            label={<FormattedMessage id={label || searchFormConfig.department.label} />}\n        />\n    );\n};\n\nexport default DepartmentBidding;\n"], "mappings": ";AAAA;AACA,SAASA,MAAM,QAAQ,0BAA0B;AACjD,SAASC,2BAA2B,EAAEC,yBAAyB,QAAQ,kBAAkB;AACzF,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,gBAAgB,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS5C,MAAMC,iBAAiB,GAAIC,KAA8B,IAAK;EAC1D,MAAM;IAAEC,QAAQ;IAAEC,IAAI;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAGJ,KAAK;EAEjD,oBACIF,OAAA,CAACN,MAAM;IACHS,QAAQ,EAAEA,QAAS;IACnBE,QAAQ,EAAEA,QAAS;IACnBE,OAAO,EAAE,CAACZ,2BAA2B,EAAE,GAAGC,yBAAyB,CAAE;IACrEQ,IAAI,EAAEA,IAAK;IACXE,KAAK,eAAEN,OAAA,CAACH,gBAAgB;MAACW,EAAE,EAAEF,KAAK,IAAIR,gBAAgB,CAACW,UAAU,CAACH;IAAM;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/E,CAAC;AAEV,CAAC;AAACC,EAAA,GAZIb,iBAAiB;AAcvB,eAAeA,iBAAiB;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}