{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/ProductionPerformance.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\n\n// project imports\nimport Api from 'constants/Api';\nimport sendRequest from 'services/ApiService';\nimport { searchFormConfig } from './Config';\nimport { Autocomplete } from 'components/extended/Form';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductionPerformance = props => {\n  _s();\n  const {\n    name,\n    disabled,\n    isDefaultAll,\n    required,\n    handleChange,\n    label,\n    requestParams\n  } = props;\n  const [projects, setProjects] = useState([]);\n  const [listProject, setListProject] = useState([]);\n  const handleChangeProject = option => {\n    const projectSelected = option ? listProject.filter(pro => pro.idHexString === option.value) : null;\n    handleChange && handleChange(projectSelected ? projectSelected[0] : null);\n  };\n  const getAllProductionPerformance = async () => {\n    const params = {\n      ...requestParams\n    };\n    const response = await sendRequest(Api.master.getProductionPerformanceAll, params);\n    if (!response) return;\n    const {\n      status,\n      result\n    } = response;\n    if (status) {\n      result.content.forEach(pro => {\n        let projectOption = {\n          value: pro.idHexString,\n          label: pro.project.projectName\n        };\n        setProjects(projects => [...projects, projectOption]);\n        setListProject(listPro => [...listPro, pro]);\n      });\n    }\n  };\n  useEffect(() => {\n    setProjects([]);\n    getAllProductionPerformance();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [requestParams]);\n  return /*#__PURE__*/_jsxDEV(Autocomplete, {\n    required: required,\n    disabled: disabled,\n    options: projects,\n    name: name,\n    label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n      id: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 20\n    }, this),\n    isDefaultAll: isDefaultAll,\n    handleChange: handleChangeProject\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 9\n  }, this);\n};\n_s(ProductionPerformance, \"uZkTg61JJknTHrbJqIKlnYQifVU=\");\n_c = ProductionPerformance;\nProductionPerformance.defaultProps = {\n  name: searchFormConfig.productionPerformance.name,\n  label: searchFormConfig.productionPerformance.label,\n  isDefaultAll: true\n};\nexport default ProductionPerformance;\nvar _c;\n$RefreshReg$(_c, \"ProductionPerformance\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Api", "sendRequest", "searchFormConfig", "Autocomplete", "FormattedMessage", "jsxDEV", "_jsxDEV", "ProductionPerformance", "props", "_s", "name", "disabled", "isDefaultAll", "required", "handleChange", "label", "requestParams", "projects", "setProjects", "listProject", "setListProject", "handleChangeProject", "option", "projectSelected", "filter", "pro", "idHexString", "value", "getAllProductionPerformance", "params", "response", "master", "getProductionPerformanceAll", "status", "result", "content", "for<PERSON>ach", "projectOption", "project", "projectName", "listPro", "options", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "defaultProps", "productionPerformance", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/ProductionPerformance.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\n\n// project imports\nimport Api from 'constants/Api';\nimport sendRequest from 'services/ApiService';\nimport { searchFormConfig } from './Config';\nimport { Autocomplete } from 'components/extended/Form';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\nimport { IOption, IProductionPerformanceItem, IProductionPerformanceList, IResponseList } from 'types';\n\ninterface IProductionPerformanceProps {\n    name: string;\n    label: string;\n    disabled?: boolean;\n    isDefaultAll?: boolean;\n    required?: boolean;\n    handleChange?: (data: any) => void;\n    requestParams?: {\n        type?: string;\n        year?: number;\n        status?: string;\n    };\n}\n\nconst ProductionPerformance = (props: IProductionPerformanceProps) => {\n    const { name, disabled, isDefaultAll, required, handleChange, label, requestParams } = props;\n    const [projects, setProjects] = useState<IOption[]>([]);\n    const [listProject, setListProject] = useState<IProductionPerformanceItem[]>([]);\n\n    const handleChangeProject = (option: IOption) => {\n        const projectSelected = option ? listProject.filter((pro) => pro.idHexString === option.value) : null;\n        handleChange && handleChange(projectSelected ? projectSelected[0] : null);\n    };\n\n    const getAllProductionPerformance = async () => {\n        const params = { ...requestParams };\n        const response: IResponseList<IProductionPerformanceList> = await sendRequest(Api.master.getProductionPerformanceAll, params);\n        if (!response) return;\n        const { status, result } = response;\n        if (status) {\n            result.content.forEach((pro) => {\n                let projectOption = {\n                    value: pro.idHexString,\n                    label: pro.project.projectName\n                };\n                setProjects((projects) => [...projects, projectOption]);\n                setListProject((listPro) => [...listPro, pro]);\n            });\n        }\n    };\n\n    useEffect(() => {\n        setProjects([]);\n        getAllProductionPerformance();\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [requestParams]);\n\n    return (\n        <Autocomplete\n            required={required}\n            disabled={disabled}\n            options={projects}\n            name={name}\n            label={<FormattedMessage id={label} />}\n            isDefaultAll={isDefaultAll}\n            handleChange={handleChangeProject}\n        />\n    );\n};\n\nProductionPerformance.defaultProps = {\n    name: searchFormConfig.productionPerformance.name,\n    label: searchFormConfig.productionPerformance.label,\n    isDefaultAll: true\n};\n\nexport default ProductionPerformance;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;;AAElD;AACA,OAAOC,GAAG,MAAM,eAAe;AAC/B,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,gBAAgB,QAAQ,UAAU;AAC3C,SAASC,YAAY,QAAQ,0BAA0B;;AAEvD;AACA,SAASC,gBAAgB,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAiB9C,MAAMC,qBAAqB,GAAIC,KAAkC,IAAK;EAAAC,EAAA;EAClE,MAAM;IAAEC,IAAI;IAAEC,QAAQ;IAAEC,YAAY;IAAEC,QAAQ;IAAEC,YAAY;IAAEC,KAAK;IAAEC;EAAc,CAAC,GAAGR,KAAK;EAC5F,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAA+B,EAAE,CAAC;EAEhF,MAAMsB,mBAAmB,GAAIC,MAAe,IAAK;IAC7C,MAAMC,eAAe,GAAGD,MAAM,GAAGH,WAAW,CAACK,MAAM,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,KAAKJ,MAAM,CAACK,KAAK,CAAC,GAAG,IAAI;IACrGb,YAAY,IAAIA,YAAY,CAACS,eAAe,GAAGA,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;EAC7E,CAAC;EAED,MAAMK,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC5C,MAAMC,MAAM,GAAG;MAAE,GAAGb;IAAc,CAAC;IACnC,MAAMc,QAAmD,GAAG,MAAM7B,WAAW,CAACD,GAAG,CAAC+B,MAAM,CAACC,2BAA2B,EAAEH,MAAM,CAAC;IAC7H,IAAI,CAACC,QAAQ,EAAE;IACf,MAAM;MAAEG,MAAM;MAAEC;IAAO,CAAC,GAAGJ,QAAQ;IACnC,IAAIG,MAAM,EAAE;MACRC,MAAM,CAACC,OAAO,CAACC,OAAO,CAAEX,GAAG,IAAK;QAC5B,IAAIY,aAAa,GAAG;UAChBV,KAAK,EAAEF,GAAG,CAACC,WAAW;UACtBX,KAAK,EAAEU,GAAG,CAACa,OAAO,CAACC;QACvB,CAAC;QACDrB,WAAW,CAAED,QAAQ,IAAK,CAAC,GAAGA,QAAQ,EAAEoB,aAAa,CAAC,CAAC;QACvDjB,cAAc,CAAEoB,OAAO,IAAK,CAAC,GAAGA,OAAO,EAAEf,GAAG,CAAC,CAAC;MAClD,CAAC,CAAC;IACN;EACJ,CAAC;EAED3B,SAAS,CAAC,MAAM;IACZoB,WAAW,CAAC,EAAE,CAAC;IACfU,2BAA2B,CAAC,CAAC;IAC7B;EACJ,CAAC,EAAE,CAACZ,aAAa,CAAC,CAAC;EAEnB,oBACIV,OAAA,CAACH,YAAY;IACTU,QAAQ,EAAEA,QAAS;IACnBF,QAAQ,EAAEA,QAAS;IACnB8B,OAAO,EAAExB,QAAS;IAClBP,IAAI,EAAEA,IAAK;IACXK,KAAK,eAAET,OAAA,CAACF,gBAAgB;MAACsC,EAAE,EAAE3B;IAAM;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAE;IACvClC,YAAY,EAAEA,YAAa;IAC3BE,YAAY,EAAEO;EAAoB;IAAAsB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrC,CAAC;AAEV,CAAC;AAACrC,EAAA,CA5CIF,qBAAqB;AAAAwC,EAAA,GAArBxC,qBAAqB;AA8C3BA,qBAAqB,CAACyC,YAAY,GAAG;EACjCtC,IAAI,EAAER,gBAAgB,CAAC+C,qBAAqB,CAACvC,IAAI;EACjDK,KAAK,EAAEb,gBAAgB,CAAC+C,qBAAqB,CAAClC,KAAK;EACnDH,YAAY,EAAE;AAClB,CAAC;AAED,eAAeL,qBAAqB;AAAC,IAAAwC,EAAA;AAAAG,YAAA,CAAAH,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}