{"ast": null, "code": "import React,{useState}from'react';import{yupResolver}from'@hookform/resolvers/yup';import{useSearchParams}from'react-router-dom';import{FormattedMessage}from'react-intl';import{useForm}from'react-hook-form';import{Grid}from'@mui/material';import{flexibleReportingConfigSchema}from'pages/administration/Config';import ReportNameConfig from'containers/search/ReportNameConfig';import{FormProvider,Label}from'components/extended/Form';import ColumnName from'containers/search/ColumnName';import{transformObject}from'utils/common';import{Button}from'components';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ColumnConfigSearch=_ref=>{let{conditions,setConditions}=_ref;const{column_config}=TEXT_CONFIG_SCREEN.administration.flexibleReport;const[reportNameId,setReportNameId]=useState(conditions.flexibleReportId);const[formReset]=useState({...conditions,flexibleColumnName:conditions.flexibleColumnId?{value:conditions.flexibleColumnId,label:conditions.flexibleColumnName}:{value:'',label:''}});const[,setSearchParams]=useSearchParams();const methods=useForm({defaultValues:conditions,resolver:yupResolver(flexibleReportingConfigSchema)});const handleSearch=value=>{var _newValue$flexibleCol,_newValue$flexibleCol2,_newValue$flexibleCol3;const newValue=transformObject({...value,page:1});setSearchParams({...newValue,flexibleColumnName:newValue.flexibleColumnName?(_newValue$flexibleCol=newValue.flexibleColumnName)===null||_newValue$flexibleCol===void 0?void 0:_newValue$flexibleCol.label:'',flexibleColumnId:newValue.flexibleColumnName?(_newValue$flexibleCol2=newValue.flexibleColumnName)===null||_newValue$flexibleCol2===void 0?void 0:_newValue$flexibleCol2.value:''});setConditions({...newValue,flexibleColumnName:(_newValue$flexibleCol3=newValue.flexibleColumnName)===null||_newValue$flexibleCol3===void 0?void 0:_newValue$flexibleCol3.label});};const handleSelectReportName=(id,isSetDefaultValue)=>{setReportNameId(id);if(isSetDefaultValue){methods.setValue('flexibleReportId',id);handleSearch(methods.getValues());}};return/*#__PURE__*/_jsx(FormProvider,{formReturn:methods,formReset:formReset,onSubmit:handleSearch,children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:2.5,children:/*#__PURE__*/_jsx(ReportNameConfig,{name:\"flexibleReportId\",isSetDefaultValue:!conditions.flexibleReportId,label:/*#__PURE__*/_jsx(FormattedMessage,{id:column_config+'report-name'}),onChange:handleSelectReportName})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:2.5,children:/*#__PURE__*/_jsx(ColumnName,{name:\"flexibleColumnName\",isShowAll:true,flexibleReportId:reportNameId,label:/*#__PURE__*/_jsx(FormattedMessage,{id:column_config+'column-name'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:5}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,lg:2,children:[/*#__PURE__*/_jsx(Label,{label:\"\\xA0\"}),/*#__PURE__*/_jsx(Button,{type:\"submit\",size:\"medium\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:column_config+'search'}),variant:\"contained\"})]})]})});};export default ColumnConfigSearch;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}