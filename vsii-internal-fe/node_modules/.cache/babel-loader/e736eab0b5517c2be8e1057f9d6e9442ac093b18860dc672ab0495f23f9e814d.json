{"ast": null, "code": "// @generated from time-data-gen.ts\n// prettier-ignore  \nexport var timeData = {\n  \"001\": [\"H\", \"h\"],\n  \"AC\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"AD\": [\"H\", \"hB\"],\n  \"AE\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"AF\": [\"H\", \"hb\", \"hB\", \"h\"],\n  \"AG\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"AI\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"AL\": [\"h\", \"H\", \"hB\"],\n  \"AM\": [\"H\", \"hB\"],\n  \"AO\": [\"H\", \"hB\"],\n  \"AR\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"AS\": [\"h\", \"H\"],\n  \"AT\": [\"H\", \"hB\"],\n  \"AU\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"AW\": [\"H\", \"hB\"],\n  \"AX\": [\"H\"],\n  \"AZ\": [\"H\", \"hB\", \"h\"],\n  \"BA\": [\"H\", \"hB\", \"h\"],\n  \"BB\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"BD\": [\"h\", \"hB\", \"H\"],\n  \"BE\": [\"H\", \"hB\"],\n  \"BF\": [\"H\", \"hB\"],\n  \"BG\": [\"H\", \"hB\", \"h\"],\n  \"BH\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"BI\": [\"H\", \"h\"],\n  \"BJ\": [\"H\", \"hB\"],\n  \"BL\": [\"H\", \"hB\"],\n  \"BM\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"BN\": [\"hb\", \"hB\", \"h\", \"H\"],\n  \"BO\": [\"H\", \"hB\", \"h\", \"hb\"],\n  \"BQ\": [\"H\"],\n  \"BR\": [\"H\", \"hB\"],\n  \"BS\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"BT\": [\"h\", \"H\"],\n  \"BW\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"BY\": [\"H\", \"h\"],\n  \"BZ\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"CA\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"CC\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"CD\": [\"hB\", \"H\"],\n  \"CF\": [\"H\", \"h\", \"hB\"],\n  \"CG\": [\"H\", \"hB\"],\n  \"CH\": [\"H\", \"hB\", \"h\"],\n  \"CI\": [\"H\", \"hB\"],\n  \"CK\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"CL\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"CM\": [\"H\", \"h\", \"hB\"],\n  \"CN\": [\"H\", \"hB\", \"hb\", \"h\"],\n  \"CO\": [\"h\", \"H\", \"hB\", \"hb\"],\n  \"CP\": [\"H\"],\n  \"CR\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"CU\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"CV\": [\"H\", \"hB\"],\n  \"CW\": [\"H\", \"hB\"],\n  \"CX\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"CY\": [\"h\", \"H\", \"hb\", \"hB\"],\n  \"CZ\": [\"H\"],\n  \"DE\": [\"H\", \"hB\"],\n  \"DG\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"DJ\": [\"h\", \"H\"],\n  \"DK\": [\"H\"],\n  \"DM\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"DO\": [\"h\", \"H\", \"hB\", \"hb\"],\n  \"DZ\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"EA\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"EC\": [\"H\", \"hB\", \"h\", \"hb\"],\n  \"EE\": [\"H\", \"hB\"],\n  \"EG\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"EH\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"ER\": [\"h\", \"H\"],\n  \"ES\": [\"H\", \"hB\", \"h\", \"hb\"],\n  \"ET\": [\"hB\", \"hb\", \"h\", \"H\"],\n  \"FI\": [\"H\"],\n  \"FJ\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"FK\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"FM\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"FO\": [\"H\", \"h\"],\n  \"FR\": [\"H\", \"hB\"],\n  \"GA\": [\"H\", \"hB\"],\n  \"GB\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"GD\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"GE\": [\"H\", \"hB\", \"h\"],\n  \"GF\": [\"H\", \"hB\"],\n  \"GG\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"GH\": [\"h\", \"H\"],\n  \"GI\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"GL\": [\"H\", \"h\"],\n  \"GM\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"GN\": [\"H\", \"hB\"],\n  \"GP\": [\"H\", \"hB\"],\n  \"GQ\": [\"H\", \"hB\", \"h\", \"hb\"],\n  \"GR\": [\"h\", \"H\", \"hb\", \"hB\"],\n  \"GT\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"GU\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"GW\": [\"H\", \"hB\"],\n  \"GY\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"HK\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"HN\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"HR\": [\"H\", \"hB\"],\n  \"HU\": [\"H\", \"h\"],\n  \"IC\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"ID\": [\"H\"],\n  \"IE\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"IL\": [\"H\", \"hB\"],\n  \"IM\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"IN\": [\"h\", \"H\"],\n  \"IO\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"IQ\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"IR\": [\"hB\", \"H\"],\n  \"IS\": [\"H\"],\n  \"IT\": [\"H\", \"hB\"],\n  \"JE\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"JM\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"JO\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"JP\": [\"H\", \"K\", \"h\"],\n  \"KE\": [\"hB\", \"hb\", \"H\", \"h\"],\n  \"KG\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"KH\": [\"hB\", \"h\", \"H\", \"hb\"],\n  \"KI\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"KM\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"KN\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"KP\": [\"h\", \"H\", \"hB\", \"hb\"],\n  \"KR\": [\"h\", \"H\", \"hB\", \"hb\"],\n  \"KW\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"KY\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"KZ\": [\"H\", \"hB\"],\n  \"LA\": [\"H\", \"hb\", \"hB\", \"h\"],\n  \"LB\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"LC\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"LI\": [\"H\", \"hB\", \"h\"],\n  \"LK\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"LR\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"LS\": [\"h\", \"H\"],\n  \"LT\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"LU\": [\"H\", \"h\", \"hB\"],\n  \"LV\": [\"H\", \"hB\", \"hb\", \"h\"],\n  \"LY\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"MA\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"MC\": [\"H\", \"hB\"],\n  \"MD\": [\"H\", \"hB\"],\n  \"ME\": [\"H\", \"hB\", \"h\"],\n  \"MF\": [\"H\", \"hB\"],\n  \"MG\": [\"H\", \"h\"],\n  \"MH\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"MK\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"ML\": [\"H\"],\n  \"MM\": [\"hB\", \"hb\", \"H\", \"h\"],\n  \"MN\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"MO\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"MP\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"MQ\": [\"H\", \"hB\"],\n  \"MR\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"MS\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"MT\": [\"H\", \"h\"],\n  \"MU\": [\"H\", \"h\"],\n  \"MV\": [\"H\", \"h\"],\n  \"MW\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"MX\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"MY\": [\"hb\", \"hB\", \"h\", \"H\"],\n  \"MZ\": [\"H\", \"hB\"],\n  \"NA\": [\"h\", \"H\", \"hB\", \"hb\"],\n  \"NC\": [\"H\", \"hB\"],\n  \"NE\": [\"H\"],\n  \"NF\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"NG\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"NI\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"NL\": [\"H\", \"hB\"],\n  \"NO\": [\"H\", \"h\"],\n  \"NP\": [\"H\", \"h\", \"hB\"],\n  \"NR\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"NU\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"NZ\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"OM\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"PA\": [\"h\", \"H\", \"hB\", \"hb\"],\n  \"PE\": [\"H\", \"hB\", \"h\", \"hb\"],\n  \"PF\": [\"H\", \"h\", \"hB\"],\n  \"PG\": [\"h\", \"H\"],\n  \"PH\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"PK\": [\"h\", \"hB\", \"H\"],\n  \"PL\": [\"H\", \"h\"],\n  \"PM\": [\"H\", \"hB\"],\n  \"PN\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"PR\": [\"h\", \"H\", \"hB\", \"hb\"],\n  \"PS\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"PT\": [\"H\", \"hB\"],\n  \"PW\": [\"h\", \"H\"],\n  \"PY\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"QA\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"RE\": [\"H\", \"hB\"],\n  \"RO\": [\"H\", \"hB\"],\n  \"RS\": [\"H\", \"hB\", \"h\"],\n  \"RU\": [\"H\"],\n  \"RW\": [\"H\", \"h\"],\n  \"SA\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"SB\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"SC\": [\"H\", \"h\", \"hB\"],\n  \"SD\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"SE\": [\"H\"],\n  \"SG\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"SH\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"SI\": [\"H\", \"hB\"],\n  \"SJ\": [\"H\"],\n  \"SK\": [\"H\"],\n  \"SL\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"SM\": [\"H\", \"h\", \"hB\"],\n  \"SN\": [\"H\", \"h\", \"hB\"],\n  \"SO\": [\"h\", \"H\"],\n  \"SR\": [\"H\", \"hB\"],\n  \"SS\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"ST\": [\"H\", \"hB\"],\n  \"SV\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"SX\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"SY\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"SZ\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"TA\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"TC\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"TD\": [\"h\", \"H\", \"hB\"],\n  \"TF\": [\"H\", \"h\", \"hB\"],\n  \"TG\": [\"H\", \"hB\"],\n  \"TH\": [\"H\", \"h\"],\n  \"TJ\": [\"H\", \"h\"],\n  \"TL\": [\"H\", \"hB\", \"hb\", \"h\"],\n  \"TM\": [\"H\", \"h\"],\n  \"TN\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"TO\": [\"h\", \"H\"],\n  \"TR\": [\"H\", \"hB\"],\n  \"TT\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"TW\": [\"hB\", \"hb\", \"h\", \"H\"],\n  \"TZ\": [\"hB\", \"hb\", \"H\", \"h\"],\n  \"UA\": [\"H\", \"hB\", \"h\"],\n  \"UG\": [\"hB\", \"hb\", \"H\", \"h\"],\n  \"UM\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"US\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"UY\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"UZ\": [\"H\", \"hB\", \"h\"],\n  \"VA\": [\"H\", \"h\", \"hB\"],\n  \"VC\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"VE\": [\"h\", \"H\", \"hB\", \"hb\"],\n  \"VG\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"VI\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"VN\": [\"H\", \"h\"],\n  \"VU\": [\"h\", \"H\"],\n  \"WF\": [\"H\", \"hB\"],\n  \"WS\": [\"h\", \"H\"],\n  \"XK\": [\"H\", \"hB\", \"h\"],\n  \"YE\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"YT\": [\"H\", \"hB\"],\n  \"ZA\": [\"H\", \"h\", \"hb\", \"hB\"],\n  \"ZM\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"ZW\": [\"H\", \"h\"],\n  \"af-ZA\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"ar-001\": [\"h\", \"hB\", \"hb\", \"H\"],\n  \"ca-ES\": [\"H\", \"h\", \"hB\"],\n  \"en-001\": [\"h\", \"hb\", \"H\", \"hB\"],\n  \"es-BO\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"es-BR\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"es-EC\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"es-ES\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"es-GQ\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"es-PE\": [\"H\", \"h\", \"hB\", \"hb\"],\n  \"fr-CA\": [\"H\", \"h\", \"hB\"],\n  \"gl-ES\": [\"H\", \"h\", \"hB\"],\n  \"gu-IN\": [\"hB\", \"hb\", \"h\", \"H\"],\n  \"hi-IN\": [\"hB\", \"h\", \"H\"],\n  \"it-CH\": [\"H\", \"h\", \"hB\"],\n  \"it-IT\": [\"H\", \"h\", \"hB\"],\n  \"kn-IN\": [\"hB\", \"h\", \"H\"],\n  \"ml-IN\": [\"hB\", \"h\", \"H\"],\n  \"mr-IN\": [\"hB\", \"hb\", \"h\", \"H\"],\n  \"pa-IN\": [\"hB\", \"hb\", \"h\", \"H\"],\n  \"ta-IN\": [\"hB\", \"h\", \"hb\", \"H\"],\n  \"te-IN\": [\"hB\", \"h\", \"H\"],\n  \"zu-ZA\": [\"H\", \"hB\", \"hb\", \"h\"]\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}