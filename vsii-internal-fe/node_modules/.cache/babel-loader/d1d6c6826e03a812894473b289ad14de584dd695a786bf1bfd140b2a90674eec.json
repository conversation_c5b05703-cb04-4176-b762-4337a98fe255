{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/TitleConfigSearch.tsx\",\n  _s = $RefreshSig$();\nimport { useSearchParams } from 'react-router-dom';\nimport { FormattedMessage } from 'react-intl';\nimport { Grid } from '@mui/material';\nimport { FormProvider, Input, Label } from 'components/extended/Form';\nimport { searchFormConfig } from 'containers/search/Config';\nimport { transformObject } from 'utils/common';\nimport { Button } from 'components';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TitleConfigSearch = ({\n  conditions,\n  setConditions\n}) => {\n  _s();\n  const [, setSearchParams] = useSearchParams();\n  const {\n    title_config\n  } = TEXT_CONFIG_SCREEN.administration;\n  const handleSearch = value => {\n    const newValue = transformObject({\n      ...value,\n      page: 1\n    });\n    setSearchParams(newValue);\n    setConditions(newValue);\n  };\n  return /*#__PURE__*/_jsxDEV(FormProvider, {\n    form: {\n      defaultValues: conditions\n    },\n    onSubmit: handleSearch,\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      justifyContent: \"space-between\",\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 3,\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          name: searchFormConfig.titleCode.name,\n          label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: title_config + 'title'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 74\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2,\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          label: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          size: \"medium\",\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: title_config + 'search'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 67\n          }, this),\n          variant: \"contained\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 9\n  }, this);\n};\n_s(TitleConfigSearch, \"z1k3ODVsVL3skfX8ceyXjWBr8cw=\", false, function () {\n  return [useSearchParams];\n});\n_c = TitleConfigSearch;\nexport default TitleConfigSearch;\nvar _c;\n$RefreshReg$(_c, \"TitleConfigSearch\");", "map": {"version": 3, "names": ["useSearchParams", "FormattedMessage", "Grid", "FormProvider", "Input", "Label", "searchFormConfig", "transformObject", "<PERSON><PERSON>", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "TitleConfigSearch", "conditions", "setConditions", "_s", "setSearchParams", "title_config", "administration", "handleSearch", "value", "newValue", "page", "form", "defaultValues", "onSubmit", "children", "container", "justifyContent", "item", "xs", "lg", "name", "titleCode", "label", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "size", "variant", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/TitleConfigSearch.tsx"], "sourcesContent": ["import { useSearchParams } from 'react-router-dom';\nimport { FormattedMessage } from 'react-intl';\nimport { Grid } from '@mui/material';\n\nimport { FormProvider, Input, Label } from 'components/extended/Form';\nimport { ITitleFilterConfig } from 'pages/administration/Config';\nimport { searchFormConfig } from 'containers/search/Config';\nimport { transformObject } from 'utils/common';\nimport { Button } from 'components';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\n\ninterface ITitleConfigSearchProps {\n    conditions: ITitleFilterConfig;\n    setConditions: React.Dispatch<React.SetStateAction<ITitleFilterConfig>>;\n}\n\nconst TitleConfigSearch = ({ conditions, setConditions }: ITitleConfigSearchProps) => {\n    const [, setSearchParams] = useSearchParams();\n\n    const { title_config } = TEXT_CONFIG_SCREEN.administration;\n    const handleSearch = (value: ITitleFilterConfig) => {\n        const newValue = transformObject({ ...value, page: 1 });\n        setSearchParams(newValue as any);\n        setConditions(newValue);\n    };\n\n    return (\n        <FormProvider\n            form={{\n                defaultValues: conditions\n            }}\n            onSubmit={handleSearch}\n        >\n            <Grid container justifyContent=\"space-between\">\n                <Grid item xs={12} lg={3}>\n                    <Input name={searchFormConfig.titleCode.name} label={<FormattedMessage id={title_config + 'title'} />} />\n                </Grid>\n                <Grid item xs={12} lg={2}>\n                    <Label label=\"&nbsp;\" />\n                    <Button type=\"submit\" size=\"medium\" children={<FormattedMessage id={title_config + 'search'} />} variant=\"contained\" />\n                </Grid>\n            </Grid>\n        </FormProvider>\n    );\n};\n\nexport default TitleConfigSearch;\n"], "mappings": ";;AAAA,SAASA,eAAe,QAAQ,kBAAkB;AAClD,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,IAAI,QAAQ,eAAe;AAEpC,SAASC,YAAY,EAAEC,KAAK,EAAEC,KAAK,QAAQ,0BAA0B;AAErE,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,eAAe,QAAQ,cAAc;AAC9C,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOtD,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAuC,CAAC,KAAK;EAAAC,EAAA;EAClF,MAAM,GAAGC,eAAe,CAAC,GAAGhB,eAAe,CAAC,CAAC;EAE7C,MAAM;IAAEiB;EAAa,CAAC,GAAGR,kBAAkB,CAACS,cAAc;EAC1D,MAAMC,YAAY,GAAIC,KAAyB,IAAK;IAChD,MAAMC,QAAQ,GAAGd,eAAe,CAAC;MAAE,GAAGa,KAAK;MAAEE,IAAI,EAAE;IAAE,CAAC,CAAC;IACvDN,eAAe,CAACK,QAAe,CAAC;IAChCP,aAAa,CAACO,QAAQ,CAAC;EAC3B,CAAC;EAED,oBACIV,OAAA,CAACR,YAAY;IACToB,IAAI,EAAE;MACFC,aAAa,EAAEX;IACnB,CAAE;IACFY,QAAQ,EAAEN,YAAa;IAAAO,QAAA,eAEvBf,OAAA,CAACT,IAAI;MAACyB,SAAS;MAACC,cAAc,EAAC,eAAe;MAAAF,QAAA,gBAC1Cf,OAAA,CAACT,IAAI;QAAC2B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACrBf,OAAA,CAACP,KAAK;UAAC4B,IAAI,EAAE1B,gBAAgB,CAAC2B,SAAS,CAACD,IAAK;UAACE,KAAK,eAAEvB,OAAA,CAACV,gBAAgB;YAACkC,EAAE,EAAElB,YAAY,GAAG;UAAQ;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvG,CAAC,eACP5B,OAAA,CAACT,IAAI;QAAC2B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,gBACrBf,OAAA,CAACN,KAAK;UAAC6B,KAAK,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxB5B,OAAA,CAACH,MAAM;UAACgC,IAAI,EAAC,QAAQ;UAACC,IAAI,EAAC,QAAQ;UAACf,QAAQ,eAAEf,OAAA,CAACV,gBAAgB;YAACkC,EAAE,EAAElB,YAAY,GAAG;UAAS;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACG,OAAO,EAAC;QAAW;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEvB,CAAC;AAACxB,EAAA,CA5BIH,iBAAiB;EAAA,QACSZ,eAAe;AAAA;AAAA2C,EAAA,GADzC/B,iBAAiB;AA8BvB,eAAeA,iBAAiB;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}