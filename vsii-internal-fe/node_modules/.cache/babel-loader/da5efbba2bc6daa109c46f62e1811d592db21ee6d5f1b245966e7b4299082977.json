{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/pages/Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport { Box, Grid, Typography } from '@mui/material';\nimport { PUBLIC_URL, TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport imageDashBoard from '../assets/images/dashboard/image-dashboard.svg';\nimport { FormattedMessage } from 'react-intl';\nimport { authSelector } from 'store/slice/authSlice';\nimport { useTheme } from '@mui/material/styles';\nimport { useAppSelector } from 'app/hooks';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DashBoard = () => {\n  _s();\n  const {\n    userInfo\n  } = useAppSelector(authSelector);\n  const theme = useTheme();\n  const {\n    dashboard\n  } = TEXT_CONFIG_SCREEN;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      background: `url(\"${PUBLIC_URL}background-dashboard.svg\") no-repeat center`,\n      [theme.breakpoints.up('lg')]: {\n        height: 'calc(100vh - 128px)'\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      alignItems: 'center',\n      className: \"dashboard__container\",\n      sx: {\n        [theme.breakpoints.down('lg')]: {\n          '& .MuiGrid-root': {\n            padding: '40px !important'\n          },\n          '& .MuiGrid-root:last-child': {\n            padding: '0 40px 40px !important',\n            '& .image__dashboard': {\n              maxWidth: '40%'\n            }\n          }\n        },\n        [theme.breakpoints.down('md')]: {\n          '& .MuiGrid-root:last-child': {\n            '& .image__dashboard': {\n              maxWidth: '50%'\n            }\n          }\n        },\n        [theme.breakpoints.down('sm')]: {\n          '& .MuiGrid-root:last-child': {\n            '& .image__dashboard': {\n              maxWidth: '75%'\n            }\n          }\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard__content--header\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h3\",\n            gutterBottom: true,\n            color: '#3163D4',\n            children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: dashboard + 'welcome'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h3\",\n            gutterBottom: true,\n            children: userInfo && `${userInfo.firstName} ${userInfo.lastName} `\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard__content--body\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h2\",\n            gutterBottom: true,\n            children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: dashboard + 'system-title'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n                id: \"accessibility\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: dashboard + 'accessibility-content'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n                id: dashboard + 'efficiency'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: dashboard + 'efficiency-content'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n                id: dashboard + 'real-time'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: dashboard + 'real-time-content'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        textAlign: 'center',\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: imageDashBoard,\n          alt: \"dashboardImage\",\n          className: \"image__dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 9\n  }, this);\n};\n_s(DashBoard, \"k5goWFTjkhHrYvsLI2kKc9jrW3c=\", false, function () {\n  return [useAppSelector, useTheme];\n});\n_c = DashBoard;\nexport default DashBoard;\nvar _c;\n$RefreshReg$(_c, \"DashBoard\");", "map": {"version": 3, "names": ["Box", "Grid", "Typography", "PUBLIC_URL", "TEXT_CONFIG_SCREEN", "imageDashBoard", "FormattedMessage", "authSelector", "useTheme", "useAppSelector", "jsxDEV", "_jsxDEV", "DashBoard", "_s", "userInfo", "theme", "dashboard", "sx", "background", "breakpoints", "up", "height", "children", "container", "alignItems", "className", "down", "padding", "max<PERSON><PERSON><PERSON>", "item", "xs", "lg", "variant", "gutterBottom", "color", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "firstName", "lastName", "textAlign", "src", "alt", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/pages/Dashboard.tsx"], "sourcesContent": ["import { Box, Grid, Typography } from '@mui/material';\nimport { PUBLIC_URL, TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport imageDashBoard from '../assets/images/dashboard/image-dashboard.svg';\nimport { FormattedMessage } from 'react-intl';\nimport { authSelector } from 'store/slice/authSlice';\nimport { useTheme } from '@mui/material/styles';\nimport { useAppSelector } from 'app/hooks';\n\nconst DashBoard = () => {\n    const { userInfo } = useAppSelector(authSelector);\n    const theme = useTheme();\n\n    const { dashboard } = TEXT_CONFIG_SCREEN;\n\n    return (\n        <Box\n            sx={{\n                background: `url(\"${PUBLIC_URL}background-dashboard.svg\") no-repeat center`,\n                [theme.breakpoints.up('lg')]: {\n                    height: 'calc(100vh - 128px)'\n                }\n            }}\n        >\n            <Grid\n                container\n                alignItems={'center'}\n                className=\"dashboard__container\"\n                sx={{\n                    [theme.breakpoints.down('lg')]: {\n                        '& .MuiGrid-root': {\n                            padding: '40px !important'\n                        },\n                        '& .MuiGrid-root:last-child': {\n                            padding: '0 40px 40px !important',\n                            '& .image__dashboard': {\n                                maxWidth: '40%'\n                            }\n                        }\n                    },\n                    [theme.breakpoints.down('md')]: {\n                        '& .MuiGrid-root:last-child': {\n                            '& .image__dashboard': {\n                                maxWidth: '50%'\n                            }\n                        }\n                    },\n                    [theme.breakpoints.down('sm')]: {\n                        '& .MuiGrid-root:last-child': {\n                            '& .image__dashboard': {\n                                maxWidth: '75%'\n                            }\n                        }\n                    }\n                }}\n            >\n                <Grid item xs={12} lg={6}>\n                    <div className=\"dashboard__content--header\">\n                        <Typography variant=\"h3\" gutterBottom color={'#3163D4'}>\n                            <FormattedMessage id={dashboard + 'welcome'} />\n                        </Typography>\n                        <Typography variant=\"h3\" gutterBottom>\n                            {userInfo && `${userInfo.firstName} ${userInfo.lastName} `}\n                        </Typography>\n                    </div>\n                    <div className=\"dashboard__content--body\">\n                        <Typography variant=\"h2\" gutterBottom>\n                            <FormattedMessage id={dashboard + 'system-title'} />\n                        </Typography>\n                        <br />\n                        <Typography variant=\"subtitle1\" gutterBottom>\n                            <strong>\n                                <FormattedMessage id=\"accessibility\" />\n                            </strong>\n                            <FormattedMessage id={dashboard + 'accessibility-content'} />\n                        </Typography>\n                        <br />\n                        <Typography variant=\"subtitle1\" gutterBottom>\n                            <strong>\n                                <FormattedMessage id={dashboard + 'efficiency'} />\n                            </strong>\n                            <FormattedMessage id={dashboard + 'efficiency-content'} />\n                        </Typography>\n                        <br />\n                        <Typography variant=\"subtitle1\" gutterBottom>\n                            <strong>\n                                <FormattedMessage id={dashboard + 'real-time'} />\n                            </strong>\n                            <FormattedMessage id={dashboard + 'real-time-content'} />\n                        </Typography>\n                    </div>\n                </Grid>\n                <Grid item xs={12} lg={6} textAlign={'center'}>\n                    <img src={imageDashBoard} alt=\"dashboardImage\" className=\"image__dashboard\" />\n                </Grid>\n            </Grid>\n        </Box>\n    );\n};\n\nexport default DashBoard;\n"], "mappings": ";;AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,UAAU,QAAQ,eAAe;AACrD,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,kBAAkB;AACjE,OAAOC,cAAc,MAAM,gDAAgD;AAC3E,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,cAAc,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM;IAAEC;EAAS,CAAC,GAAGL,cAAc,CAACF,YAAY,CAAC;EACjD,MAAMQ,KAAK,GAAGP,QAAQ,CAAC,CAAC;EAExB,MAAM;IAAEQ;EAAU,CAAC,GAAGZ,kBAAkB;EAExC,oBACIO,OAAA,CAACX,GAAG;IACAiB,EAAE,EAAE;MACAC,UAAU,EAAE,QAAQf,UAAU,6CAA6C;MAC3E,CAACY,KAAK,CAACI,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;QAC1BC,MAAM,EAAE;MACZ;IACJ,CAAE;IAAAC,QAAA,eAEFX,OAAA,CAACV,IAAI;MACDsB,SAAS;MACTC,UAAU,EAAE,QAAS;MACrBC,SAAS,EAAC,sBAAsB;MAChCR,EAAE,EAAE;QACA,CAACF,KAAK,CAACI,WAAW,CAACO,IAAI,CAAC,IAAI,CAAC,GAAG;UAC5B,iBAAiB,EAAE;YACfC,OAAO,EAAE;UACb,CAAC;UACD,4BAA4B,EAAE;YAC1BA,OAAO,EAAE,wBAAwB;YACjC,qBAAqB,EAAE;cACnBC,QAAQ,EAAE;YACd;UACJ;QACJ,CAAC;QACD,CAACb,KAAK,CAACI,WAAW,CAACO,IAAI,CAAC,IAAI,CAAC,GAAG;UAC5B,4BAA4B,EAAE;YAC1B,qBAAqB,EAAE;cACnBE,QAAQ,EAAE;YACd;UACJ;QACJ,CAAC;QACD,CAACb,KAAK,CAACI,WAAW,CAACO,IAAI,CAAC,IAAI,CAAC,GAAG;UAC5B,4BAA4B,EAAE;YAC1B,qBAAqB,EAAE;cACnBE,QAAQ,EAAE;YACd;UACJ;QACJ;MACJ,CAAE;MAAAN,QAAA,gBAEFX,OAAA,CAACV,IAAI;QAAC4B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAT,QAAA,gBACrBX,OAAA;UAAKc,SAAS,EAAC,4BAA4B;UAAAH,QAAA,gBACvCX,OAAA,CAACT,UAAU;YAAC8B,OAAO,EAAC,IAAI;YAACC,YAAY;YAACC,KAAK,EAAE,SAAU;YAAAZ,QAAA,eACnDX,OAAA,CAACL,gBAAgB;cAAC6B,EAAE,EAAEnB,SAAS,GAAG;YAAU;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACb5B,OAAA,CAACT,UAAU;YAAC8B,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAX,QAAA,EAChCR,QAAQ,IAAI,GAAGA,QAAQ,CAAC0B,SAAS,IAAI1B,QAAQ,CAAC2B,QAAQ;UAAG;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACN5B,OAAA;UAAKc,SAAS,EAAC,0BAA0B;UAAAH,QAAA,gBACrCX,OAAA,CAACT,UAAU;YAAC8B,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAX,QAAA,eACjCX,OAAA,CAACL,gBAAgB;cAAC6B,EAAE,EAAEnB,SAAS,GAAG;YAAe;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACb5B,OAAA;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN5B,OAAA,CAACT,UAAU;YAAC8B,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAX,QAAA,gBACxCX,OAAA;cAAAW,QAAA,eACIX,OAAA,CAACL,gBAAgB;gBAAC6B,EAAE,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACT5B,OAAA,CAACL,gBAAgB;cAAC6B,EAAE,EAAEnB,SAAS,GAAG;YAAwB;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACb5B,OAAA;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN5B,OAAA,CAACT,UAAU;YAAC8B,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAX,QAAA,gBACxCX,OAAA;cAAAW,QAAA,eACIX,OAAA,CAACL,gBAAgB;gBAAC6B,EAAE,EAAEnB,SAAS,GAAG;cAAa;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACT5B,OAAA,CAACL,gBAAgB;cAAC6B,EAAE,EAAEnB,SAAS,GAAG;YAAqB;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACb5B,OAAA;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN5B,OAAA,CAACT,UAAU;YAAC8B,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAX,QAAA,gBACxCX,OAAA;cAAAW,QAAA,eACIX,OAAA,CAACL,gBAAgB;gBAAC6B,EAAE,EAAEnB,SAAS,GAAG;cAAY;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACT5B,OAAA,CAACL,gBAAgB;cAAC6B,EAAE,EAAEnB,SAAS,GAAG;YAAoB;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACP5B,OAAA,CAACV,IAAI;QAAC4B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACW,SAAS,EAAE,QAAS;QAAApB,QAAA,eAC1CX,OAAA;UAAKgC,GAAG,EAAEtC,cAAe;UAACuC,GAAG,EAAC,gBAAgB;UAACnB,SAAS,EAAC;QAAkB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEd,CAAC;AAAC1B,EAAA,CAzFID,SAAS;EAAA,QACUH,cAAc,EACrBD,QAAQ;AAAA;AAAAqC,EAAA,GAFpBjC,SAAS;AA2Ff,eAAeA,SAAS;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}