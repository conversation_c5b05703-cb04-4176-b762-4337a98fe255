{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"minDate\", \"maxDate\", \"disableFuture\", \"shouldDisableDate\", \"disablePast\"];\nimport { useValidation } from './useValidation';\nimport { validateDate } from './useDateValidation';\nimport { validateTime } from './useTimeValidation';\nexport const validateDateTime = _ref => {\n  let {\n    props,\n    value,\n    adapter\n  } = _ref;\n  const {\n      minDate,\n      maxDate,\n      disableFuture,\n      shouldDisableDate,\n      disablePast\n    } = props,\n    timeValidationProps = _objectWithoutPropertiesLoose(props, _excluded);\n  const dateValidationResult = validateDate({\n    adapter,\n    value,\n    props: {\n      minDate,\n      maxDate,\n      disableFuture,\n      shouldDisableDate,\n      disablePast\n    }\n  });\n  if (dateValidationResult !== null) {\n    return dateValidationResult;\n  }\n  return validateTime({\n    adapter,\n    value,\n    props: timeValidationProps\n  });\n};\nconst isSameDateTimeError = (a, b) => a === b;\nexport function useDateTimeValidation(props) {\n  return useValidation(props, validateDateTime, isSameDateTimeError);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}