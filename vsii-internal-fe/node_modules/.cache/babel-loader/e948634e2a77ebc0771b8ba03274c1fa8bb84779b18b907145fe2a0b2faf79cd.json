{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/WorkType.tsx\";\nimport { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { searchFormConfig } from './Config';\nimport { Select } from 'components/extended/Form';\nimport { DEFAULT_VALUE_OPTION, WORK_TYPE, DEFAULT_VALUE_OPTION_SELECT } from 'constants/Common';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst WorkType = props => {\n  const {\n    isShowAll,\n    required\n  } = props;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Select, {\n      isMultipleLanguage: true,\n      required: required,\n      selects: !isShowAll ? [DEFAULT_VALUE_OPTION, ...WORK_TYPE] : [DEFAULT_VALUE_OPTION_SELECT, ...WORK_TYPE],\n      name: searchFormConfig.workType.name,\n      label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n        id: searchFormConfig.workType.label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 24\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_c = WorkType;\nWorkType.defaultProps = {\n  isShowAll: false\n};\nexport default WorkType;\nvar _c;\n$RefreshReg$(_c, \"WorkType\");", "map": {"version": 3, "names": ["FormattedMessage", "searchFormConfig", "Select", "DEFAULT_VALUE_OPTION", "WORK_TYPE", "DEFAULT_VALUE_OPTION_SELECT", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "WorkType", "props", "isShowAll", "required", "children", "isMultipleLanguage", "selects", "name", "workType", "label", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "defaultProps", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/WorkType.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { searchFormConfig } from './Config';\nimport { Select } from 'components/extended/Form';\nimport { DEFAULT_VALUE_OPTION, WORK_TYPE, DEFAULT_VALUE_OPTION_SELECT } from 'constants/Common';\n\ninterface IWorkTypeProps {\n    isShowAll: boolean;\n    required: boolean;\n}\n\nconst WorkType = (props: IWorkTypeProps) => {\n    const { isShowAll, required } = props;\n    return (\n        <>\n            <Select\n                isMultipleLanguage\n                required={required}\n                selects={!isShowAll ? [DEFAULT_VALUE_OPTION, ...WORK_TYPE] : [DEFAULT_VALUE_OPTION_SELECT, ...WORK_TYPE]}\n                name={searchFormConfig.workType.name}\n                label={<FormattedMessage id={searchFormConfig.workType.label} />}\n            />\n        </>\n    );\n};\n\nWorkType.defaultProps = {\n    isShowAll: false\n};\n\nexport default WorkType;\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,gBAAgB,QAAQ,UAAU;AAC3C,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,oBAAoB,EAAEC,SAAS,EAAEC,2BAA2B,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOhG,MAAMC,QAAQ,GAAIC,KAAqB,IAAK;EACxC,MAAM;IAAEC,SAAS;IAAEC;EAAS,CAAC,GAAGF,KAAK;EACrC,oBACIJ,OAAA,CAAAE,SAAA;IAAAK,QAAA,eACIP,OAAA,CAACL,MAAM;MACHa,kBAAkB;MAClBF,QAAQ,EAAEA,QAAS;MACnBG,OAAO,EAAE,CAACJ,SAAS,GAAG,CAACT,oBAAoB,EAAE,GAAGC,SAAS,CAAC,GAAG,CAACC,2BAA2B,EAAE,GAAGD,SAAS,CAAE;MACzGa,IAAI,EAAEhB,gBAAgB,CAACiB,QAAQ,CAACD,IAAK;MACrCE,KAAK,eAAEZ,OAAA,CAACP,gBAAgB;QAACoB,EAAE,EAAEnB,gBAAgB,CAACiB,QAAQ,CAACC;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE;EAAC,gBACJ,CAAC;AAEX,CAAC;AAACC,EAAA,GAbIf,QAAQ;AAedA,QAAQ,CAACgB,YAAY,GAAG;EACpBd,SAAS,EAAE;AACf,CAAC;AAED,eAAeF,QAAQ;AAAC,IAAAe,EAAA;AAAAE,YAAA,CAAAF,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}