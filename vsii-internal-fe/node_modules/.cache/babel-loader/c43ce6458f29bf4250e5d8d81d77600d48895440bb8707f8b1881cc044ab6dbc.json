{"ast": null, "code": "// materia-ui\nimport{<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON>ack,TableBody,TableCell,TableRow,Tooltip,Typography}from'@mui/material';// third party\nimport{FormattedMessage}from'react-intl';// project imports\nimport{PERMISSIONS}from'constants/Permission';import{checkAllowedPermission}from'utils/authorization';// assets\nimport EditTwoToneIcon from'@mui/icons-material/EditTwoTone';import HighlightOffIcon from'@mui/icons-material/HighlightOff';import{dateFormat}from'utils/date';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const RequestsCheckingTBody=props=>{const{requests,handleOpen,pageNumber,pageSize,handleOpenDeleteConfirm}=props;const{saleList}=PERMISSIONS.sale;const getTranslateStatus=status=>{switch(status){case'Not Start':return/*#__PURE__*/_jsx(FormattedMessage,{id:\"not-start\"});case'Inprogress':return/*#__PURE__*/_jsx(FormattedMessage,{id:\"inprogress\"});case'Stop':return/*#__PURE__*/_jsx(FormattedMessage,{id:\"stop\"});default:return'';}};const getTranslatePossibility=possibility=>{switch(possibility){case'High':return/*#__PURE__*/_jsx(FormattedMessage,{id:\"high\"});case'Normal':return/*#__PURE__*/_jsx(FormattedMessage,{id:\"normal\"});case'Low':return/*#__PURE__*/_jsx(FormattedMessage,{id:\"low\"});default:return'';}};return/*#__PURE__*/_jsx(TableBody,{children:requests.map((request,key)=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:pageSize*pageNumber+key+1}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Stack,{direction:\"row\",children:/*#__PURE__*/_jsx(Tooltip,{placement:\"right\",title:request.partnerName,children:/*#__PURE__*/_jsx(Typography,{className:\"tooltip-content\",children:request.partnerName})})})}),/*#__PURE__*/_jsx(TableCell,{children:dateFormat(request.receivedDate)}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Stack,{direction:\"row\",children:/*#__PURE__*/_jsx(Tooltip,{placement:\"right\",title:request.request,children:/*#__PURE__*/_jsx(Typography,{className:\"tooltip-content\",children:request.request})})})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Stack,{direction:\"row\",children:/*#__PURE__*/_jsx(Tooltip,{placement:\"right\",title:request.technology,children:/*#__PURE__*/_jsx(Typography,{className:\"tooltip-content\",children:request.technology})})})}),/*#__PURE__*/_jsx(TableCell,{children:request.quantity}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Stack,{direction:\"row\",children:/*#__PURE__*/_jsx(Tooltip,{placement:\"right\",title:request.timeline,children:/*#__PURE__*/_jsx(Typography,{className:\"tooltip-content\",children:request.timeline})})})}),/*#__PURE__*/_jsxs(TableCell,{children:[request.picFirstName,\" \",request.picLastName]}),/*#__PURE__*/_jsx(TableCell,{children:getTranslateStatus(request.status)}),/*#__PURE__*/_jsx(TableCell,{children:getTranslatePossibility(request.possibility)}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Stack,{direction:\"row\",children:/*#__PURE__*/_jsx(Tooltip,{placement:\"right\",title:request.domain,children:/*#__PURE__*/_jsx(Typography,{className:\"tooltip-content\",children:request.domain})})})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Stack,{direction:\"row\",children:/*#__PURE__*/_jsx(Tooltip,{placement:\"right\",title:request.note,children:/*#__PURE__*/_jsx(Typography,{className:\"tooltip-content\",children:request.note})})})}),checkAllowedPermission(saleList.editRequest)&&/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(Stack,{direction:\"row\",justifyContent:\"center\",alignItems:\"center\",children:[/*#__PURE__*/_jsx(Tooltip,{placement:\"top\",title:/*#__PURE__*/_jsx(FormattedMessage,{id:\"edit\"}),onClick:()=>handleOpen(request),children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"edit\",size:\"small\",children:/*#__PURE__*/_jsx(EditTwoToneIcon,{sx:{fontSize:'1.1rem'}})})}),/*#__PURE__*/_jsx(Tooltip,{placement:\"top\",title:/*#__PURE__*/_jsx(FormattedMessage,{id:\"delete\"}),onClick:()=>handleOpenDeleteConfirm(request,'delete'),children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"delete\",size:\"small\",children:/*#__PURE__*/_jsx(HighlightOffIcon,{sx:{fontSize:'1.1rem'}})})})]})})]},key))});};export default RequestsCheckingTBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}