{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/ColumnConfigSearch.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport { useSearchParams } from 'react-router-dom';\nimport { FormattedMessage } from 'react-intl';\nimport { useForm } from 'react-hook-form';\nimport { Grid } from '@mui/material';\nimport { flexibleReportingConfigSchema } from 'pages/administration/Config';\nimport ReportNameConfig from 'containers/search/ReportNameConfig';\nimport { FormProvider, Label } from 'components/extended/Form';\nimport ColumnName from 'containers/search/ColumnName';\nimport { transformObject } from 'utils/common';\nimport { Button } from 'components';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ColumnConfigSearch = ({\n  conditions,\n  setConditions\n}) => {\n  _s();\n  const {\n    column_config\n  } = TEXT_CONFIG_SCREEN.administration.flexibleReport;\n  const [reportNameId, setReportNameId] = useState(conditions.flexibleReportId);\n  const [formReset] = useState({\n    ...conditions,\n    flexibleColumnName: conditions.flexibleColumnId ? {\n      value: conditions.flexibleColumnId,\n      label: conditions.flexibleColumnName\n    } : {\n      value: '',\n      label: ''\n    }\n  });\n  const [, setSearchParams] = useSearchParams();\n  const methods = useForm({\n    defaultValues: conditions,\n    resolver: yupResolver(flexibleReportingConfigSchema)\n  });\n  const handleSearch = value => {\n    var _newValue$flexibleCol, _newValue$flexibleCol2, _newValue$flexibleCol3;\n    const newValue = transformObject({\n      ...value,\n      page: 1\n    });\n    setSearchParams({\n      ...newValue,\n      flexibleColumnName: newValue.flexibleColumnName ? (_newValue$flexibleCol = newValue.flexibleColumnName) === null || _newValue$flexibleCol === void 0 ? void 0 : _newValue$flexibleCol.label : '',\n      flexibleColumnId: newValue.flexibleColumnName ? (_newValue$flexibleCol2 = newValue.flexibleColumnName) === null || _newValue$flexibleCol2 === void 0 ? void 0 : _newValue$flexibleCol2.value : ''\n    });\n    setConditions({\n      ...newValue,\n      flexibleColumnName: (_newValue$flexibleCol3 = newValue.flexibleColumnName) === null || _newValue$flexibleCol3 === void 0 ? void 0 : _newValue$flexibleCol3.label\n    });\n  };\n  const handleSelectReportName = (id, isSetDefaultValue) => {\n    setReportNameId(id);\n    if (isSetDefaultValue) {\n      methods.setValue('flexibleReportId', id);\n      handleSearch(methods.getValues());\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(FormProvider, {\n    formReturn: methods,\n    formReset: formReset,\n    onSubmit: handleSearch,\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2.5,\n        children: /*#__PURE__*/_jsxDEV(ReportNameConfig, {\n          name: \"flexibleReportId\",\n          isSetDefaultValue: !conditions.flexibleReportId,\n          label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: column_config + 'report-name'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 32\n          }, this),\n          onChange: handleSelectReportName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2.5,\n        children: /*#__PURE__*/_jsxDEV(ColumnName, {\n          name: \"flexibleColumnName\",\n          isShowAll: true,\n          flexibleReportId: reportNameId,\n          label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: column_config + 'column-name'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 32\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 5\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2,\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          label: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          size: \"medium\",\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: column_config + 'search'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 67\n          }, this),\n          variant: \"contained\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 9\n  }, this);\n};\n_s(ColumnConfigSearch, \"9M3lIQgWle70jraqsfPKhrdp7c8=\", false, function () {\n  return [useSearchParams, useForm];\n});\n_c = ColumnConfigSearch;\nexport default ColumnConfigSearch;\nvar _c;\n$RefreshReg$(_c, \"ColumnConfigSearch\");", "map": {"version": 3, "names": ["React", "useState", "yupResolver", "useSearchParams", "FormattedMessage", "useForm", "Grid", "flexibleReportingConfigSchema", "ReportNameConfig", "FormProvider", "Label", "ColumnName", "transformObject", "<PERSON><PERSON>", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "ColumnConfigSearch", "conditions", "setConditions", "_s", "column_config", "administration", "flexibleReport", "reportNameId", "setReportNameId", "flexibleReportId", "formReset", "flexibleColumnName", "flexibleColumnId", "value", "label", "setSearchParams", "methods", "defaultValues", "resolver", "handleSearch", "_newValue$flexibleCol", "_newValue$flexibleCol2", "_newValue$flexibleCol3", "newValue", "page", "handleSelectReportName", "id", "isSetDefaultValue", "setValue", "getV<PERSON>ues", "formReturn", "onSubmit", "children", "container", "spacing", "item", "xs", "lg", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "isShowAll", "type", "size", "variant", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/ColumnConfigSearch.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { yupResolver } from '@hookform/resolvers/yup';\r\nimport { useSearchParams } from 'react-router-dom';\r\nimport { FormattedMessage } from 'react-intl';\r\nimport { useForm } from 'react-hook-form';\r\nimport { Grid } from '@mui/material';\r\n\r\nimport { flexibleReportingConfigSchema } from 'pages/administration/Config';\r\nimport ReportNameConfig from 'containers/search/ReportNameConfig';\r\nimport { ISearchColumnConfigParams } from 'types/flexible-report';\r\nimport { FormProvider, Label } from 'components/extended/Form';\r\nimport ColumnName from 'containers/search/ColumnName';\r\nimport { transformObject } from 'utils/common';\r\nimport { Button } from 'components';\r\nimport { IOption } from 'types';\r\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\r\n\r\ninterface Props {\r\n    conditions: ISearchColumnConfigParams;\r\n    setConditions: React.Dispatch<React.SetStateAction<ISearchColumnConfigParams>>;\r\n}\r\n\r\nconst ColumnConfigSearch: React.FC<Props> = ({ conditions, setConditions }) => {\r\n    const { column_config } = TEXT_CONFIG_SCREEN.administration.flexibleReport;\r\n    const [reportNameId, setReportNameId] = useState<string>(conditions.flexibleReportId);\r\n\r\n    const [formReset] = useState<ISearchColumnConfigParams>({\r\n        ...conditions,\r\n        flexibleColumnName: conditions.flexibleColumnId\r\n            ? ({ value: conditions.flexibleColumnId, label: conditions.flexibleColumnName } as IOption)\r\n            : { value: '', label: '' }\r\n    });\r\n\r\n    const [, setSearchParams] = useSearchParams();\r\n\r\n    const methods = useForm({\r\n        defaultValues: conditions,\r\n        resolver: yupResolver(flexibleReportingConfigSchema)\r\n    });\r\n\r\n    const handleSearch = (value: ISearchColumnConfigParams) => {\r\n        const newValue = transformObject({ ...value, page: 1 });\r\n        setSearchParams({\r\n            ...(newValue as any),\r\n            flexibleColumnName: newValue.flexibleColumnName ? newValue.flexibleColumnName?.label : '',\r\n            flexibleColumnId: newValue.flexibleColumnName ? newValue.flexibleColumnName?.value : ''\r\n        });\r\n        setConditions({\r\n            ...newValue,\r\n            flexibleColumnName: newValue.flexibleColumnName?.label\r\n        });\r\n    };\r\n\r\n    const handleSelectReportName = (id: string, isSetDefaultValue?: boolean) => {\r\n        setReportNameId(id);\r\n        if (isSetDefaultValue) {\r\n            methods.setValue('flexibleReportId', id);\r\n            handleSearch(methods.getValues());\r\n        }\r\n    };\r\n\r\n    return (\r\n        <FormProvider formReturn={methods} formReset={formReset} onSubmit={handleSearch}>\r\n            <Grid container spacing={2}>\r\n                <Grid item xs={12} lg={2.5}>\r\n                    <ReportNameConfig\r\n                        name=\"flexibleReportId\"\r\n                        isSetDefaultValue={!conditions.flexibleReportId}\r\n                        label={<FormattedMessage id={column_config + 'report-name'} />}\r\n                        onChange={handleSelectReportName}\r\n                    />\r\n                </Grid>\r\n                <Grid item xs={12} lg={2.5}>\r\n                    <ColumnName\r\n                        name=\"flexibleColumnName\"\r\n                        isShowAll\r\n                        flexibleReportId={reportNameId}\r\n                        label={<FormattedMessage id={column_config + 'column-name'} />}\r\n                    />\r\n                </Grid>\r\n                <Grid item xs={12} lg={5}></Grid>\r\n                <Grid item xs={12} lg={2}>\r\n                    <Label label=\"&nbsp;\" />\r\n                    <Button type=\"submit\" size=\"medium\" children={<FormattedMessage id={column_config + 'search'} />} variant=\"contained\" />\r\n                </Grid>\r\n            </Grid>\r\n        </FormProvider>\r\n    );\r\n};\r\n\r\nexport default ColumnConfigSearch;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,IAAI,QAAQ,eAAe;AAEpC,SAASC,6BAA6B,QAAQ,6BAA6B;AAC3E,OAAOC,gBAAgB,MAAM,oCAAoC;AAEjE,SAASC,YAAY,EAAEC,KAAK,QAAQ,0BAA0B;AAC9D,OAAOC,UAAU,MAAM,8BAA8B;AACrD,SAASC,eAAe,QAAQ,cAAc;AAC9C,SAASC,MAAM,QAAQ,YAAY;AAEnC,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOtD,MAAMC,kBAAmC,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAC3E,MAAM;IAAEC;EAAc,CAAC,GAAGP,kBAAkB,CAACQ,cAAc,CAACC,cAAc;EAC1E,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAASiB,UAAU,CAACQ,gBAAgB,CAAC;EAErF,MAAM,CAACC,SAAS,CAAC,GAAG1B,QAAQ,CAA4B;IACpD,GAAGiB,UAAU;IACbU,kBAAkB,EAAEV,UAAU,CAACW,gBAAgB,GACxC;MAAEC,KAAK,EAAEZ,UAAU,CAACW,gBAAgB;MAAEE,KAAK,EAAEb,UAAU,CAACU;IAAmB,CAAC,GAC7E;MAAEE,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAG;EACjC,CAAC,CAAC;EAEF,MAAM,GAAGC,eAAe,CAAC,GAAG7B,eAAe,CAAC,CAAC;EAE7C,MAAM8B,OAAO,GAAG5B,OAAO,CAAC;IACpB6B,aAAa,EAAEhB,UAAU;IACzBiB,QAAQ,EAAEjC,WAAW,CAACK,6BAA6B;EACvD,CAAC,CAAC;EAEF,MAAM6B,YAAY,GAAIN,KAAgC,IAAK;IAAA,IAAAO,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACvD,MAAMC,QAAQ,GAAG5B,eAAe,CAAC;MAAE,GAAGkB,KAAK;MAAEW,IAAI,EAAE;IAAE,CAAC,CAAC;IACvDT,eAAe,CAAC;MACZ,GAAIQ,QAAgB;MACpBZ,kBAAkB,EAAEY,QAAQ,CAACZ,kBAAkB,IAAAS,qBAAA,GAAGG,QAAQ,CAACZ,kBAAkB,cAAAS,qBAAA,uBAA3BA,qBAAA,CAA6BN,KAAK,GAAG,EAAE;MACzFF,gBAAgB,EAAEW,QAAQ,CAACZ,kBAAkB,IAAAU,sBAAA,GAAGE,QAAQ,CAACZ,kBAAkB,cAAAU,sBAAA,uBAA3BA,sBAAA,CAA6BR,KAAK,GAAG;IACzF,CAAC,CAAC;IACFX,aAAa,CAAC;MACV,GAAGqB,QAAQ;MACXZ,kBAAkB,GAAAW,sBAAA,GAAEC,QAAQ,CAACZ,kBAAkB,cAAAW,sBAAA,uBAA3BA,sBAAA,CAA6BR;IACrD,CAAC,CAAC;EACN,CAAC;EAED,MAAMW,sBAAsB,GAAGA,CAACC,EAAU,EAAEC,iBAA2B,KAAK;IACxEnB,eAAe,CAACkB,EAAE,CAAC;IACnB,IAAIC,iBAAiB,EAAE;MACnBX,OAAO,CAACY,QAAQ,CAAC,kBAAkB,EAAEF,EAAE,CAAC;MACxCP,YAAY,CAACH,OAAO,CAACa,SAAS,CAAC,CAAC,CAAC;IACrC;EACJ,CAAC;EAED,oBACI9B,OAAA,CAACP,YAAY;IAACsC,UAAU,EAAEd,OAAQ;IAACN,SAAS,EAAEA,SAAU;IAACqB,QAAQ,EAAEZ,YAAa;IAAAa,QAAA,eAC5EjC,OAAA,CAACV,IAAI;MAAC4C,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAF,QAAA,gBACvBjC,OAAA,CAACV,IAAI;QAAC8C,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,GAAI;QAAAL,QAAA,eACvBjC,OAAA,CAACR,gBAAgB;UACb+C,IAAI,EAAC,kBAAkB;UACvBX,iBAAiB,EAAE,CAAC1B,UAAU,CAACQ,gBAAiB;UAChDK,KAAK,eAAEf,OAAA,CAACZ,gBAAgB;YAACuC,EAAE,EAAEtB,aAAa,GAAG;UAAc;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC/DC,QAAQ,EAAElB;QAAuB;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACP3C,OAAA,CAACV,IAAI;QAAC8C,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,GAAI;QAAAL,QAAA,eACvBjC,OAAA,CAACL,UAAU;UACP4C,IAAI,EAAC,oBAAoB;UACzBM,SAAS;UACTnC,gBAAgB,EAAEF,YAAa;UAC/BO,KAAK,eAAEf,OAAA,CAACZ,gBAAgB;YAACuC,EAAE,EAAEtB,aAAa,GAAG;UAAc;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACP3C,OAAA,CAACV,IAAI;QAAC8C,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE;MAAE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACjC3C,OAAA,CAACV,IAAI;QAAC8C,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,gBACrBjC,OAAA,CAACN,KAAK;UAACqB,KAAK,EAAC;QAAQ;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxB3C,OAAA,CAACH,MAAM;UAACiD,IAAI,EAAC,QAAQ;UAACC,IAAI,EAAC,QAAQ;UAACd,QAAQ,eAAEjC,OAAA,CAACZ,gBAAgB;YAACuC,EAAE,EAAEtB,aAAa,GAAG;UAAS;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACK,OAAO,EAAC;QAAW;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEvB,CAAC;AAACvC,EAAA,CAlEIH,kBAAmC;EAAA,QAWTd,eAAe,EAE3BE,OAAO;AAAA;AAAA4D,EAAA,GAbrBhD,kBAAmC;AAoEzC,eAAeA,kBAAkB;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}