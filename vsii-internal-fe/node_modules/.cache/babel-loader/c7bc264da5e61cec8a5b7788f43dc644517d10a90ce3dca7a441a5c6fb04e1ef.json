{"ast": null, "code": "// redux\nimport { createAsyncThunk, createSlice } from '@reduxjs/toolkit';\n// project imports\nimport Api from 'constants/Api';\nimport { paginationResponseDefault } from 'constants/Common';\nimport sendRequest from 'services/ApiService';\nimport manualSyncAPI from 'services/others/manualSyncAPI';\n\n// interface\n\n// initialState\nconst initialState = {\n  productReport: [],\n  sprints: [],\n  productReportOption: [],\n  pagination: paginationResponseDefault,\n  loading: {}\n};\n\n// Call API\nexport const getAllProductReport = createAsyncThunk(Api.product.getReport.url, async parameters => {\n  const response = await sendRequest(Api.product.getReport, parameters);\n  return response;\n});\nexport const getReportByProjectId = createAsyncThunk('Api.product.getReportByProjectId.url', async parameters => {\n  const response = await sendRequest(Api.product.getReportByProjectId(parameters.projectId));\n  return response;\n});\nexport const syncDataProjectReport = createAsyncThunk('manualSyncAPI.getSyncProjectReport', async () => {\n  const response = await manualSyncAPI.getSyncProjectReport();\n  return response;\n});\nexport const getProjectReportOption = createAsyncThunk('Api.product.getProductReportOptions', async () => {\n  const response = await sendRequest(Api.product.getProductReportOptions);\n  const {\n    status,\n    result\n  } = response;\n  if (!response || !status) return [];\n  const arrOption = result.content.map(pro => ({\n    value: pro.projectId,\n    label: pro.projectName,\n    typeCode: pro.typeCode\n  }));\n  return arrOption;\n});\n\n// Slice & Actions\nconst productReportSlice = createSlice({\n  name: 'productReport',\n  initialState: initialState,\n  reducers: {\n    filterSprint: (state, action) => {\n      var _state$projectDetail;\n      state.sprints = ((_state$projectDetail = state.projectDetail) === null || _state$projectDetail === void 0 ? void 0 : _state$projectDetail.sprints.filter(sprint => action.payload === 'all' ? true : sprint.sprintId.toString() === action.payload)) || [];\n    },\n    setSprintSelectDefault: (state, action) => {\n      state.sprintSelectDefaut = action.payload;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(getAllProductReport.pending, state => {\n      state.loading[getAllProductReport.typePrefix] = true;\n    });\n    builder.addCase(getAllProductReport.fulfilled, (state, action) => {\n      var _action$payload, _action$payload2;\n      state.productReport = (_action$payload = action.payload) === null || _action$payload === void 0 ? void 0 : _action$payload.result.content;\n      state.pagination = ((_action$payload2 = action.payload) === null || _action$payload2 === void 0 ? void 0 : _action$payload2.result.pagination) || paginationResponseDefault;\n      state.loading[getAllProductReport.typePrefix] = false;\n    });\n    builder.addCase(getAllProductReport.rejected, state => {\n      state.loading[getAllProductReport.typePrefix] = false;\n    });\n    builder.addCase(getReportByProjectId.pending, state => {\n      state.loading[getReportByProjectId.typePrefix] = true;\n    });\n    builder.addCase(getReportByProjectId.fulfilled, (state, action) => {\n      var _action$payload3, _action$payload4, _action$payload4$resu, _action$payload4$resu2, _action$payload5, _action$payload5$resu, _action$payload5$resu2;\n      state.projectDetail = (_action$payload3 = action.payload) === null || _action$payload3 === void 0 ? void 0 : _action$payload3.result.content;\n      state.sprints = ((_action$payload4 = action.payload) === null || _action$payload4 === void 0 ? void 0 : (_action$payload4$resu = _action$payload4.result.content) === null || _action$payload4$resu === void 0 ? void 0 : (_action$payload4$resu2 = _action$payload4$resu.sprints) === null || _action$payload4$resu2 === void 0 ? void 0 : _action$payload4$resu2.filter(sprint => sprint.sprintId.toString() === state.sprintSelectDefaut)) || ((_action$payload5 = action.payload) === null || _action$payload5 === void 0 ? void 0 : (_action$payload5$resu = _action$payload5.result.content) === null || _action$payload5$resu === void 0 ? void 0 : (_action$payload5$resu2 = _action$payload5$resu.sprints) === null || _action$payload5$resu2 === void 0 ? void 0 : _action$payload5$resu2[0]) || [];\n      state.loading[getReportByProjectId.typePrefix] = false;\n    });\n    builder.addCase(getReportByProjectId.rejected, state => {\n      state.loading[getReportByProjectId.typePrefix] = false;\n    });\n    builder.addCase(syncDataProjectReport.pending, state => {\n      state.loading[syncDataProjectReport.typePrefix] = true;\n    });\n    builder.addCase(syncDataProjectReport.fulfilled, state => {\n      state.loading[syncDataProjectReport.typePrefix] = false;\n    });\n    builder.addCase(syncDataProjectReport.rejected, state => {\n      state.loading[syncDataProjectReport.typePrefix] = false;\n    });\n    builder.addCase(getProjectReportOption.pending, state => {\n      state.loading[getProjectReportOption.typePrefix] = true;\n    });\n    builder.addCase(getProjectReportOption.fulfilled, (state, action) => {\n      state.productReportOption = action.payload;\n      state.loading[getProjectReportOption.typePrefix] = false;\n    });\n    builder.addCase(getProjectReportOption.rejected, state => {\n      state.loading[getProjectReportOption.typePrefix] = false;\n    });\n  }\n});\n\n// Reducer & export\n\nexport default productReportSlice.reducer;\nexport const {\n  filterSprint,\n  setSprintSelectDefault\n} = productReportSlice.actions;\n\n// Selector & export\nexport const productReportSelector = state => state.productReport;", "map": {"version": 3, "names": ["createAsyncThunk", "createSlice", "Api", "paginationResponseDefault", "sendRequest", "manualSyncAPI", "initialState", "productReport", "sprints", "productReportOption", "pagination", "loading", "getAllProductReport", "product", "getReport", "url", "parameters", "response", "getReportByProjectId", "projectId", "syncDataProjectReport", "getSyncProjectReport", "getProjectReportOption", "getProductReportOptions", "status", "result", "arrOption", "content", "map", "pro", "value", "label", "projectName", "typeCode", "productReportSlice", "name", "reducers", "filterSprint", "state", "action", "_state$projectDetail", "projectDetail", "filter", "sprint", "payload", "sprintId", "toString", "setSprintSelectDefault", "sprintSelectDefaut", "extraReducers", "builder", "addCase", "pending", "typePrefix", "fulfilled", "_action$payload", "_action$payload2", "rejected", "_action$payload3", "_action$payload4", "_action$payload4$resu", "_action$payload4$resu2", "_action$payload5", "_action$payload5$resu", "_action$payload5$resu2", "reducer", "actions", "productReportSelector"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/store/slice/productReportSlice.ts"], "sourcesContent": ["// redux\nimport { createAsyncThunk, createSlice } from '@reduxjs/toolkit';\nimport { RootState } from 'app/store';\n\n// project imports\nimport Api from 'constants/Api';\nimport { paginationResponseDefault } from 'constants/Common';\nimport sendRequest from 'services/ApiService';\nimport manualSyncAPI, { IResponseManualSync } from 'services/others/manualSyncAPI';\nimport {\n    IPaginationResponse,\n    IProductReportDetailResponse,\n    IProductReport,\n    IResponseList,\n    IProductReportResponse,\n    IProductReportRequest,\n    IOption,\n    IProject,\n    IProjectList\n} from 'types';\n\n// interface\ninterface IProjectInitialState {\n    productReport: IProductReport[];\n    projectDetail?: IProductReportDetailResponse['content'];\n    sprints: IProductReportDetailResponse['content']['sprints'];\n    pagination: IPaginationResponse;\n    sprintSelectDefaut?: string;\n    productReportOption: IOption[];\n    loading: {\n        [key: string]: boolean;\n    };\n}\n\n// initialState\nconst initialState: IProjectInitialState = {\n    productReport: [],\n    sprints: [],\n    productReportOption: [],\n    pagination: paginationResponseDefault,\n    loading: {}\n};\n\n// Call API\nexport const getAllProductReport = createAsyncThunk<IResponseList<IProductReportResponse>, IProductReportRequest>(\n    Api.product.getReport.url,\n    async (parameters) => {\n        const response = await sendRequest(Api.product.getReport, parameters);\n\n        return response;\n    }\n);\nexport const getReportByProjectId = createAsyncThunk<IResponseList<IProductReportDetailResponse>, { projectId: number }>(\n    'Api.product.getReportByProjectId.url',\n    async (parameters) => {\n        const response = await sendRequest(Api.product.getReportByProjectId(parameters.projectId));\n\n        return response;\n    }\n);\nexport const syncDataProjectReport = createAsyncThunk<IResponseManualSync>('manualSyncAPI.getSyncProjectReport', async () => {\n    const response = await manualSyncAPI.getSyncProjectReport();\n\n    return response;\n});\n\nexport const getProjectReportOption = createAsyncThunk<IOption[]>('Api.product.getProductReportOptions', async () => {\n    const response: IResponseList<IProjectList> = await sendRequest(Api.product.getProductReportOptions);\n    const { status, result } = response;\n    if (!response || !status) return [];\n\n    const arrOption: IOption[] = result.content.map((pro: IProject) => ({\n        value: pro.projectId,\n        label: pro.projectName,\n        typeCode: pro.typeCode\n    }));\n\n    return arrOption;\n});\n\n// Slice & Actions\nconst productReportSlice = createSlice({\n    name: 'productReport',\n    initialState: initialState,\n    reducers: {\n        filterSprint: (state, action) => {\n            state.sprints =\n                state.projectDetail?.sprints.filter((sprint) =>\n                    action.payload === 'all' ? true : sprint.sprintId.toString() === action.payload\n                ) || [];\n        },\n        setSprintSelectDefault: (state, action) => {\n            state.sprintSelectDefaut = action.payload;\n        }\n    },\n    extraReducers: (builder) => {\n        builder.addCase(getAllProductReport.pending, (state) => {\n            state.loading[getAllProductReport.typePrefix] = true;\n        });\n        builder.addCase(getAllProductReport.fulfilled, (state, action) => {\n            state.productReport = action.payload?.result.content;\n            state.pagination = action.payload?.result.pagination || paginationResponseDefault;\n            state.loading[getAllProductReport.typePrefix] = false;\n        });\n        builder.addCase(getAllProductReport.rejected, (state) => {\n            state.loading[getAllProductReport.typePrefix] = false;\n        });\n        builder.addCase(getReportByProjectId.pending, (state) => {\n            state.loading[getReportByProjectId.typePrefix] = true;\n        });\n        builder.addCase(getReportByProjectId.fulfilled, (state, action) => {\n            state.projectDetail = action.payload?.result.content;\n            state.sprints =\n                action.payload?.result.content?.sprints?.filter((sprint) => sprint.sprintId.toString() === state.sprintSelectDefaut) ||\n                action.payload?.result.content?.sprints?.[0] ||\n                [];\n            state.loading[getReportByProjectId.typePrefix] = false;\n        });\n        builder.addCase(getReportByProjectId.rejected, (state) => {\n            state.loading[getReportByProjectId.typePrefix] = false;\n        });\n        builder.addCase(syncDataProjectReport.pending, (state) => {\n            state.loading[syncDataProjectReport.typePrefix] = true;\n        });\n        builder.addCase(syncDataProjectReport.fulfilled, (state) => {\n            state.loading[syncDataProjectReport.typePrefix] = false;\n        });\n        builder.addCase(syncDataProjectReport.rejected, (state) => {\n            state.loading[syncDataProjectReport.typePrefix] = false;\n        });\n        builder.addCase(getProjectReportOption.pending, (state) => {\n            state.loading[getProjectReportOption.typePrefix] = true;\n        });\n        builder.addCase(getProjectReportOption.fulfilled, (state, action) => {\n            state.productReportOption = action.payload;\n            state.loading[getProjectReportOption.typePrefix] = false;\n        });\n        builder.addCase(getProjectReportOption.rejected, (state) => {\n            state.loading[getProjectReportOption.typePrefix] = false;\n        });\n    }\n});\n\n// Reducer & export\n\nexport default productReportSlice.reducer;\n\nexport const { filterSprint, setSprintSelectDefault } = productReportSlice.actions;\n\n// Selector & export\nexport const productReportSelector = (state: RootState) => state.productReport;\n"], "mappings": "AAAA;AACA,SAASA,gBAAgB,EAAEC,WAAW,QAAQ,kBAAkB;AAGhE;AACA,OAAOC,GAAG,MAAM,eAAe;AAC/B,SAASC,yBAAyB,QAAQ,kBAAkB;AAC5D,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,aAAa,MAA+B,+BAA+B;;AAalF;;AAaA;AACA,MAAMC,YAAkC,GAAG;EACvCC,aAAa,EAAE,EAAE;EACjBC,OAAO,EAAE,EAAE;EACXC,mBAAmB,EAAE,EAAE;EACvBC,UAAU,EAAEP,yBAAyB;EACrCQ,OAAO,EAAE,CAAC;AACd,CAAC;;AAED;AACA,OAAO,MAAMC,mBAAmB,GAAGZ,gBAAgB,CAC/CE,GAAG,CAACW,OAAO,CAACC,SAAS,CAACC,GAAG,EACzB,MAAOC,UAAU,IAAK;EAClB,MAAMC,QAAQ,GAAG,MAAMb,WAAW,CAACF,GAAG,CAACW,OAAO,CAACC,SAAS,EAAEE,UAAU,CAAC;EAErE,OAAOC,QAAQ;AACnB,CACJ,CAAC;AACD,OAAO,MAAMC,oBAAoB,GAAGlB,gBAAgB,CAChD,sCAAsC,EACtC,MAAOgB,UAAU,IAAK;EAClB,MAAMC,QAAQ,GAAG,MAAMb,WAAW,CAACF,GAAG,CAACW,OAAO,CAACK,oBAAoB,CAACF,UAAU,CAACG,SAAS,CAAC,CAAC;EAE1F,OAAOF,QAAQ;AACnB,CACJ,CAAC;AACD,OAAO,MAAMG,qBAAqB,GAAGpB,gBAAgB,CAAsB,oCAAoC,EAAE,YAAY;EACzH,MAAMiB,QAAQ,GAAG,MAAMZ,aAAa,CAACgB,oBAAoB,CAAC,CAAC;EAE3D,OAAOJ,QAAQ;AACnB,CAAC,CAAC;AAEF,OAAO,MAAMK,sBAAsB,GAAGtB,gBAAgB,CAAY,qCAAqC,EAAE,YAAY;EACjH,MAAMiB,QAAqC,GAAG,MAAMb,WAAW,CAACF,GAAG,CAACW,OAAO,CAACU,uBAAuB,CAAC;EACpG,MAAM;IAAEC,MAAM;IAAEC;EAAO,CAAC,GAAGR,QAAQ;EACnC,IAAI,CAACA,QAAQ,IAAI,CAACO,MAAM,EAAE,OAAO,EAAE;EAEnC,MAAME,SAAoB,GAAGD,MAAM,CAACE,OAAO,CAACC,GAAG,CAAEC,GAAa,KAAM;IAChEC,KAAK,EAAED,GAAG,CAACV,SAAS;IACpBY,KAAK,EAAEF,GAAG,CAACG,WAAW;IACtBC,QAAQ,EAAEJ,GAAG,CAACI;EAClB,CAAC,CAAC,CAAC;EAEH,OAAOP,SAAS;AACpB,CAAC,CAAC;;AAEF;AACA,MAAMQ,kBAAkB,GAAGjC,WAAW,CAAC;EACnCkC,IAAI,EAAE,eAAe;EACrB7B,YAAY,EAAEA,YAAY;EAC1B8B,QAAQ,EAAE;IACNC,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAAA,IAAAC,oBAAA;MAC7BF,KAAK,CAAC9B,OAAO,GACT,EAAAgC,oBAAA,GAAAF,KAAK,CAACG,aAAa,cAAAD,oBAAA,uBAAnBA,oBAAA,CAAqBhC,OAAO,CAACkC,MAAM,CAAEC,MAAM,IACvCJ,MAAM,CAACK,OAAO,KAAK,KAAK,GAAG,IAAI,GAAGD,MAAM,CAACE,QAAQ,CAACC,QAAQ,CAAC,CAAC,KAAKP,MAAM,CAACK,OAC5E,CAAC,KAAI,EAAE;IACf,CAAC;IACDG,sBAAsB,EAAEA,CAACT,KAAK,EAAEC,MAAM,KAAK;MACvCD,KAAK,CAACU,kBAAkB,GAAGT,MAAM,CAACK,OAAO;IAC7C;EACJ,CAAC;EACDK,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CAACC,OAAO,CAACvC,mBAAmB,CAACwC,OAAO,EAAGd,KAAK,IAAK;MACpDA,KAAK,CAAC3B,OAAO,CAACC,mBAAmB,CAACyC,UAAU,CAAC,GAAG,IAAI;IACxD,CAAC,CAAC;IACFH,OAAO,CAACC,OAAO,CAACvC,mBAAmB,CAAC0C,SAAS,EAAE,CAAChB,KAAK,EAAEC,MAAM,KAAK;MAAA,IAAAgB,eAAA,EAAAC,gBAAA;MAC9DlB,KAAK,CAAC/B,aAAa,IAAAgD,eAAA,GAAGhB,MAAM,CAACK,OAAO,cAAAW,eAAA,uBAAdA,eAAA,CAAgB9B,MAAM,CAACE,OAAO;MACpDW,KAAK,CAAC5B,UAAU,GAAG,EAAA8C,gBAAA,GAAAjB,MAAM,CAACK,OAAO,cAAAY,gBAAA,uBAAdA,gBAAA,CAAgB/B,MAAM,CAACf,UAAU,KAAIP,yBAAyB;MACjFmC,KAAK,CAAC3B,OAAO,CAACC,mBAAmB,CAACyC,UAAU,CAAC,GAAG,KAAK;IACzD,CAAC,CAAC;IACFH,OAAO,CAACC,OAAO,CAACvC,mBAAmB,CAAC6C,QAAQ,EAAGnB,KAAK,IAAK;MACrDA,KAAK,CAAC3B,OAAO,CAACC,mBAAmB,CAACyC,UAAU,CAAC,GAAG,KAAK;IACzD,CAAC,CAAC;IACFH,OAAO,CAACC,OAAO,CAACjC,oBAAoB,CAACkC,OAAO,EAAGd,KAAK,IAAK;MACrDA,KAAK,CAAC3B,OAAO,CAACO,oBAAoB,CAACmC,UAAU,CAAC,GAAG,IAAI;IACzD,CAAC,CAAC;IACFH,OAAO,CAACC,OAAO,CAACjC,oBAAoB,CAACoC,SAAS,EAAE,CAAChB,KAAK,EAAEC,MAAM,KAAK;MAAA,IAAAmB,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MAC/D1B,KAAK,CAACG,aAAa,IAAAiB,gBAAA,GAAGnB,MAAM,CAACK,OAAO,cAAAc,gBAAA,uBAAdA,gBAAA,CAAgBjC,MAAM,CAACE,OAAO;MACpDW,KAAK,CAAC9B,OAAO,GACT,EAAAmD,gBAAA,GAAApB,MAAM,CAACK,OAAO,cAAAe,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlC,MAAM,CAACE,OAAO,cAAAiC,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAgCpD,OAAO,cAAAqD,sBAAA,uBAAvCA,sBAAA,CAAyCnB,MAAM,CAAEC,MAAM,IAAKA,MAAM,CAACE,QAAQ,CAACC,QAAQ,CAAC,CAAC,KAAKR,KAAK,CAACU,kBAAkB,CAAC,OAAAc,gBAAA,GACpHvB,MAAM,CAACK,OAAO,cAAAkB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrC,MAAM,CAACE,OAAO,cAAAoC,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAgCvD,OAAO,cAAAwD,sBAAA,uBAAvCA,sBAAA,CAA0C,CAAC,CAAC,KAC5C,EAAE;MACN1B,KAAK,CAAC3B,OAAO,CAACO,oBAAoB,CAACmC,UAAU,CAAC,GAAG,KAAK;IAC1D,CAAC,CAAC;IACFH,OAAO,CAACC,OAAO,CAACjC,oBAAoB,CAACuC,QAAQ,EAAGnB,KAAK,IAAK;MACtDA,KAAK,CAAC3B,OAAO,CAACO,oBAAoB,CAACmC,UAAU,CAAC,GAAG,KAAK;IAC1D,CAAC,CAAC;IACFH,OAAO,CAACC,OAAO,CAAC/B,qBAAqB,CAACgC,OAAO,EAAGd,KAAK,IAAK;MACtDA,KAAK,CAAC3B,OAAO,CAACS,qBAAqB,CAACiC,UAAU,CAAC,GAAG,IAAI;IAC1D,CAAC,CAAC;IACFH,OAAO,CAACC,OAAO,CAAC/B,qBAAqB,CAACkC,SAAS,EAAGhB,KAAK,IAAK;MACxDA,KAAK,CAAC3B,OAAO,CAACS,qBAAqB,CAACiC,UAAU,CAAC,GAAG,KAAK;IAC3D,CAAC,CAAC;IACFH,OAAO,CAACC,OAAO,CAAC/B,qBAAqB,CAACqC,QAAQ,EAAGnB,KAAK,IAAK;MACvDA,KAAK,CAAC3B,OAAO,CAACS,qBAAqB,CAACiC,UAAU,CAAC,GAAG,KAAK;IAC3D,CAAC,CAAC;IACFH,OAAO,CAACC,OAAO,CAAC7B,sBAAsB,CAAC8B,OAAO,EAAGd,KAAK,IAAK;MACvDA,KAAK,CAAC3B,OAAO,CAACW,sBAAsB,CAAC+B,UAAU,CAAC,GAAG,IAAI;IAC3D,CAAC,CAAC;IACFH,OAAO,CAACC,OAAO,CAAC7B,sBAAsB,CAACgC,SAAS,EAAE,CAAChB,KAAK,EAAEC,MAAM,KAAK;MACjED,KAAK,CAAC7B,mBAAmB,GAAG8B,MAAM,CAACK,OAAO;MAC1CN,KAAK,CAAC3B,OAAO,CAACW,sBAAsB,CAAC+B,UAAU,CAAC,GAAG,KAAK;IAC5D,CAAC,CAAC;IACFH,OAAO,CAACC,OAAO,CAAC7B,sBAAsB,CAACmC,QAAQ,EAAGnB,KAAK,IAAK;MACxDA,KAAK,CAAC3B,OAAO,CAACW,sBAAsB,CAAC+B,UAAU,CAAC,GAAG,KAAK;IAC5D,CAAC,CAAC;EACN;AACJ,CAAC,CAAC;;AAEF;;AAEA,eAAenB,kBAAkB,CAAC+B,OAAO;AAEzC,OAAO,MAAM;EAAE5B,YAAY;EAAEU;AAAuB,CAAC,GAAGb,kBAAkB,CAACgC,OAAO;;AAElF;AACA,OAAO,MAAMC,qBAAqB,GAAI7B,KAAgB,IAAKA,KAAK,CAAC/B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}