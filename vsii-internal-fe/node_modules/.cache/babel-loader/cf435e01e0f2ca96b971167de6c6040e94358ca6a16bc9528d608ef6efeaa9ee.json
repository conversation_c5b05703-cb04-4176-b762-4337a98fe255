{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useThemeProps } from '@mui/material/styles';\nimport { DEFAULT_LOCALE } from '../locales';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const MuiPickersAdapterContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  MuiPickersAdapterContext.displayName = 'MuiPickersAdapterContext';\n}\nlet warnedOnce = false;\n/**\n * @ignore - do not document.\n */\n\nexport function LocalizationProvider(inProps) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiLocalizationProvider'\n  });\n  const {\n    children,\n    dateAdapter: Utils,\n    dateFormats,\n    dateLibInstance,\n    locale,\n    adapterLocale,\n    localeText\n  } = props;\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnce && locale !== undefined) {\n      warnedOnce = true;\n      console.warn(\"LocalizationProvider's prop `locale` is deprecated and replaced by `adapterLocale`\");\n    }\n  }\n  const utils = React.useMemo(() => new Utils({\n    locale: adapterLocale != null ? adapterLocale : locale,\n    formats: dateFormats,\n    instance: dateLibInstance\n  }), [Utils, locale, adapterLocale, dateFormats, dateLibInstance]);\n  const defaultDates = React.useMemo(() => {\n    return {\n      minDate: utils.date('1900-01-01T00:00:00.000'),\n      maxDate: utils.date('2099-12-31T00:00:00.000')\n    };\n  }, [utils]);\n  const contextValue = React.useMemo(() => {\n    return {\n      utils,\n      defaultDates,\n      localeText: _extends({}, DEFAULT_LOCALE, localeText != null ? localeText : {})\n    };\n  }, [defaultDates, utils, localeText]);\n  return /*#__PURE__*/_jsx(MuiPickersAdapterContext.Provider, {\n    value: contextValue,\n    children: children\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? LocalizationProvider.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * Locale for the date library you are using\n   */\n  adapterLocale: PropTypes.oneOfType([PropTypes.object, PropTypes.string]),\n  children: PropTypes.node,\n  /**\n   * DateIO adapter class function\n   */\n  dateAdapter: PropTypes.func.isRequired,\n  /**\n   * Formats that are used for any child pickers\n   */\n  dateFormats: PropTypes.shape({\n    dayOfMonth: PropTypes.string,\n    fullDate: PropTypes.string,\n    fullDateTime: PropTypes.string,\n    fullDateTime12h: PropTypes.string,\n    fullDateTime24h: PropTypes.string,\n    fullDateWithWeekday: PropTypes.string,\n    fullTime: PropTypes.string,\n    fullTime12h: PropTypes.string,\n    fullTime24h: PropTypes.string,\n    hours12h: PropTypes.string,\n    hours24h: PropTypes.string,\n    keyboardDate: PropTypes.string,\n    keyboardDateTime: PropTypes.string,\n    keyboardDateTime12h: PropTypes.string,\n    keyboardDateTime24h: PropTypes.string,\n    minutes: PropTypes.string,\n    month: PropTypes.string,\n    monthAndDate: PropTypes.string,\n    monthAndYear: PropTypes.string,\n    monthShort: PropTypes.string,\n    normalDate: PropTypes.string,\n    normalDateWithWeekday: PropTypes.string,\n    seconds: PropTypes.string,\n    shortDate: PropTypes.string,\n    weekday: PropTypes.string,\n    weekdayShort: PropTypes.string,\n    year: PropTypes.string\n  }),\n  /**\n   * Date library instance you are using, if it has some global overrides\n   * ```jsx\n   * dateLibInstance={momentTimeZone}\n   * ```\n   */\n  dateLibInstance: PropTypes.any,\n  /**\n   * Locale for the date library you are using\n   * @deprecated Use `adapterLocale` instead\n   */\n  locale: PropTypes.oneOfType([PropTypes.object, PropTypes.string]),\n  /**\n   * Locale for components texts\n   */\n  localeText: PropTypes.object\n} : void 0;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}