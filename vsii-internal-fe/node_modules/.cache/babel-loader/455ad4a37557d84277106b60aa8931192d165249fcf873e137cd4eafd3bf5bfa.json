{"ast": null, "code": "import{Button,DialogActions,Grid}from'@mui/material';import{yupResolver}from'@hookform/resolvers/yup';import{FormattedMessage}from'react-intl';import{useForm}from'react-hook-form';import{LoadingButton}from'@mui/lab';import{createDepartment,departmentSelector,editDepartment,getSearchDepartment}from'store/slice/departmentSlice';import{createOrEditDepartmentSchema}from'pages/administration/Config';import{FormProvider,Input}from'components/extended/Form';import{useAppDispatch,useAppSelector}from'app/hooks';import{openSnackbar}from'store/slice/snackbarSlice';import Modal from'components/extended/Modal';import{gridSpacing}from'store/constant';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AddOrEditDepartment=props=>{const{department,open,conditions,handleClose}=props;const{manage_department}=TEXT_CONFIG_SCREEN.administration;const dispatch=useAppDispatch();const{loading}=useAppSelector(departmentSelector);// useForm\nconst methods=useForm({defaultValues:{deptId:(department===null||department===void 0?void 0:department.deptId)||'',deptName:(department===null||department===void 0?void 0:department.deptName)||''},resolver:yupResolver(createOrEditDepartmentSchema),mode:'all'});const handleSubmit=async values=>{if(department){const resultAction=await dispatch(editDepartment({...values,id:department.id}));if(editDepartment.fulfilled.match(resultAction)&&!resultAction.payload.status){var _resultAction$payload,_resultAction$payload2;dispatch(openSnackbar({open:true,message:((_resultAction$payload=resultAction.payload)===null||_resultAction$payload===void 0?void 0:(_resultAction$payload2=_resultAction$payload.result)===null||_resultAction$payload2===void 0?void 0:_resultAction$payload2.content)||'Error',variant:'alert',alert:{color:'error'}}));return;}else if(editDepartment.rejected.match(resultAction)){return;}}else{const resultAction=await dispatch(createDepartment(values));if(createDepartment.fulfilled.match(resultAction)&&!resultAction.payload.status){var _resultAction$payload3,_resultAction$payload4;dispatch(openSnackbar({open:true,message:((_resultAction$payload3=resultAction.payload)===null||_resultAction$payload3===void 0?void 0:(_resultAction$payload4=_resultAction$payload3.result)===null||_resultAction$payload4===void 0?void 0:_resultAction$payload4.content)||'Error',variant:'alert',alert:{color:'error'}}));return;}else if(createDepartment.rejected.match(resultAction)){return;}}dispatch(openSnackbar({open:true,message:department?'update-success':'add-success',variant:'alert',alert:{color:'success'}}));dispatch(getSearchDepartment(conditions));handleClose();};return/*#__PURE__*/_jsx(Modal,{isOpen:open,title:department?manage_department+'edit-department':manage_department+'add-department',onClose:handleClose,keepMounted:false,maxWidth:\"xs\",children:/*#__PURE__*/_jsxs(FormProvider,{onSubmit:handleSubmit,formReturn:methods,children:[/*#__PURE__*/_jsxs(Grid,{container:true,spacing:gridSpacing,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Input,{required:true,name:\"deptId\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_department+'department'}),disabled:!!department})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Input,{required:true,name:\"deptName\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_department+'department-name'})})})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{color:\"error\",onClick:handleClose,children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_department+'cancel'})}),/*#__PURE__*/_jsx(LoadingButton,{loading:loading[createDepartment.typePrefix]||loading[editDepartment.typePrefix],variant:\"contained\",type:\"submit\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_department+'submit'})})]})]})});};export default AddOrEditDepartment;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}