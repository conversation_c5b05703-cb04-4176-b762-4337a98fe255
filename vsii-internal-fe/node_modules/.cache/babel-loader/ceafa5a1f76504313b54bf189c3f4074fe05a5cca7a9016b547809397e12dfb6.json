{"ast": null, "code": "'use client';\n\nimport systemUseThemeProps from '@mui/system/useThemeProps';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nexport default function useThemeProps(_ref) {\n  let {\n    props,\n    name\n  } = _ref;\n  return systemUseThemeProps({\n    props,\n    name,\n    defaultTheme,\n    themeId: THEME_ID\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}