{"ast": null, "code": "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport sliderUnstyledClasses from './sliderUnstyledClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useValueLabelClasses = props => {\n  const {\n    open\n  } = props;\n  const utilityClasses = {\n    offset: clsx(open && sliderUnstyledClasses.valueLabelOpen),\n    circle: sliderUnstyledClasses.valueLabelCircle,\n    label: sliderUnstyledClasses.valueLabelLabel\n  };\n  return utilityClasses;\n};\n/**\n * @ignore - internal component.\n */\n\nexport default function SliderValueLabelUnstyled(props) {\n  const {\n    children,\n    className,\n    value,\n    theme\n  } = props;\n  const classes = useValueLabelClasses(props);\n  return /*#__PURE__*/React.cloneElement(children, {\n    className: clsx(children.props.className)\n  }, /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [children.props.children, /*#__PURE__*/_jsx(\"span\", {\n      className: clsx(classes.offset, className),\n      theme: theme,\n      \"aria-hidden\": true,\n      children: /*#__PURE__*/_jsx(\"span\", {\n        className: classes.circle,\n        children: /*#__PURE__*/_jsx(\"span\", {\n          className: classes.label,\n          children: value\n        })\n      })\n    })]\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? SliderValueLabelUnstyled.propTypes = {\n  children: PropTypes.element.isRequired,\n  className: PropTypes.string,\n  theme: PropTypes.any,\n  value: PropTypes.node\n} : void 0;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}