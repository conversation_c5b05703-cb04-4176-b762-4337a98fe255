{"ast": null, "code": "import{MAIN_FUNCTION_LIST}from'constants/Permission';import{store}from'app/store';export function userAuthorization(permissionsRequired){let isAllowFunctions=false;if(!(permissionsRequired!==null&&permissionsRequired!==void 0&&permissionsRequired.length)){return{isAllowFunctions};}const{userInfo}=store.getState().auth;const groups=(userInfo===null||userInfo===void 0?void 0:userInfo.featureList)||(userInfo===null||userInfo===void 0?void 0:userInfo.functionList)||(userInfo===null||userInfo===void 0?void 0:userInfo.groups)||[];if(groups.length){isAllowFunctions=MAIN_FUNCTION_LIST.some(mainFnc=>mainFnc.functions.some(fnc=>groups.some(fncOfUser=>fnc===fncOfUser&&permissionsRequired&&permissionsRequired.includes(fncOfUser))));}return{isAllowFunctions};}export function checkAllowedPermission(permission_key){var _store$getState$auth$;const userPermissions=((_store$getState$auth$=store.getState().auth.userInfo)===null||_store$getState$auth$===void 0?void 0:_store$getState$auth$.groups)||[];return userPermissions.includes(permission_key);}export function checkAllowedTab(tabs,tabValueParam){const tabValue=[];if(tabValueParam&&tabValueParam>Math.max(...tabs.map(tab=>tab.value||0))){return tabValue;}tabs.forEach(item=>{var _store$getState$auth$2;if(item.permission_key&&(checkAllowedPermission(item.permission_key)||!((_store$getState$auth$2=store.getState().auth.userInfo)!==null&&_store$getState$auth$2!==void 0&&_store$getState$auth$2.isLdap))){if(item.value===tabValueParam){tabValue.unshift(item.value);}else{tabValue.push(item.value);}}});return tabValue;}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}