{"ast": null, "code": "import * as React from 'react';\nimport TreeViewContext from '../TreeView/TreeViewContext';\nexport default function useTreeItem(nodeId) {\n  const {\n    focus,\n    isExpanded,\n    isExpandable,\n    isFocused,\n    isDisabled,\n    isSelected,\n    multiSelect,\n    selectNode,\n    selectRange,\n    toggleExpansion\n  } = React.useContext(TreeViewContext);\n  const expandable = isExpandable ? isExpandable(nodeId) : false;\n  const expanded = isExpanded ? isExpanded(nodeId) : false;\n  const focused = isFocused ? isFocused(nodeId) : false;\n  const disabled = isDisabled ? isDisabled(nodeId) : false;\n  const selected = isSelected ? isSelected(nodeId) : false;\n  const handleExpansion = event => {\n    if (!disabled) {\n      if (!focused) {\n        focus(event, nodeId);\n      }\n      const multiple = multiSelect && (event.shiftKey || event.ctrlKey || event.metaKey); // If already expanded and trying to toggle selection don't close\n\n      if (expandable && !(multiple && isExpanded(nodeId))) {\n        toggleExpansion(event, nodeId);\n      }\n    }\n  };\n  const handleSelection = event => {\n    if (!disabled) {\n      if (!focused) {\n        focus(event, nodeId);\n      }\n      const multiple = multiSelect && (event.shiftKey || event.ctrlKey || event.metaKey);\n      if (multiple) {\n        if (event.shiftKey) {\n          selectRange(event, {\n            end: nodeId\n          });\n        } else {\n          selectNode(event, nodeId, true);\n        }\n      } else {\n        selectNode(event, nodeId);\n      }\n    }\n  };\n  const preventSelection = event => {\n    if (event.shiftKey || event.ctrlKey || event.metaKey || disabled) {\n      // Prevent text selection\n      event.preventDefault();\n    }\n  };\n  return {\n    disabled,\n    expanded,\n    selected,\n    focused,\n    handleExpansion,\n    handleSelection,\n    preventSelection\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}