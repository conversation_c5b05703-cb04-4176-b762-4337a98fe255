{"ast": null, "code": "// Third party\nimport{yupResolver}from'@hookform/resolvers/yup';import{FormattedMessage}from'react-intl';// material-ui\nimport{Button,DialogActions,Grid,Stack}from'@mui/material';import{LoadingButton}from'@mui/lab';// project import\nimport{synchronizeFormDefault,synchronizeSchema}from'pages/sales/Config';import{FormProvider,Input}from'components/extended/Form';import Modal from'components/extended/Modal';import{gridSpacing}from'store/constant';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Synchronize=props=>{const{open,handleClose,loading,handleSynchronize}=props;const handleSubmit=value=>{handleSynchronize(value.token);};return/*#__PURE__*/_jsx(Modal,{isOpen:open,title:\"synchronize\",onClose:handleClose,keepMounted:false,maxWidth:\"xs\",children:/*#__PURE__*/_jsx(FormProvider,{form:{defaultValues:synchronizeFormDefault,resolver:yupResolver(synchronizeSchema)},onSubmit:handleSubmit,children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:gridSpacing,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Input,{name:\"token\",label:\"Token\",textFieldProps:{multiline:true,rows:5},required:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(DialogActions,{children:/*#__PURE__*/_jsxs(Stack,{direction:\"row\",spacing:1,justifyContent:\"flex-end\",children:[/*#__PURE__*/_jsx(Button,{color:\"error\",onClick:handleClose,children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"cancel\"})}),/*#__PURE__*/_jsx(LoadingButton,{loading:loading,variant:\"contained\",type:\"submit\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"submit\"})})]})})})]})})});};export default Synchronize;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}