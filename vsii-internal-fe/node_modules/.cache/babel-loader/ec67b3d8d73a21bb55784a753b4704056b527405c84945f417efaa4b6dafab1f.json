{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/pages/monthly-project-cost/MonthlyCostData.tsx\",\n  _s = $RefreshSig$();\n/* eslint-disable react-hooks/exhaustive-deps */\nimport { useCallback, useEffect, useState } from 'react';\nimport { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { MonthlyCostDataSearch, MonthlyCostDataTBody, MonthlyCostDataThead } from 'containers/monthly-project-cost';\nimport { exportDocument, getSearchParam, setLocalStorageSearchTime, transformObject } from 'utils/common';\nimport { monthlyCostDataConfig, monthlyCostDataFormDefault } from './Config';\nimport AddActualCost from 'containers/monthly-project-cost/AddActualCost';\nimport { closeConfirm, openConfirm } from 'store/slice/confirmSlice';\nimport { convertMonthFromToDate, getMonthsOfYear } from 'utils/date';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { openSnackbar } from 'store/slice/snackbarSlice';\nimport { SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { FilterCollapse } from 'containers/search';\nimport { PERMISSIONS } from 'constants/Permission';\nimport { Table } from 'components/extended/Table';\nimport MainCard from 'components/cards/MainCard';\nimport sendRequest from 'services/ApiService';\nimport { useAppDispatch } from 'app/hooks';\nimport { TableToolbar } from 'containers';\nimport Api from 'constants/Api';\n\n// third party\nimport { useSearchParams } from 'react-router-dom';\n\n// ==============================|| Monthly Project Cost - Data ||============================== //\n/**\n *  URL Params\n *  year\n *  month\n *  projectId\n *  projectName\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MonthlyCostData = () => {\n  _s();\n  const {\n    monthlyCost\n  } = TEXT_CONFIG_SCREEN.monthlyProjectCost;\n  // URL Params\n  const [searchParams, setSearchParams] = useSearchParams();\n  const keyParams = [SEARCH_PARAM_KEY.year, SEARCH_PARAM_KEY.month, SEARCH_PARAM_KEY.projectId, SEARCH_PARAM_KEY.projectName];\n  const params = getSearchParam(keyParams, searchParams);\n  transformObject(params);\n  // delete unnecessary key value\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  const {\n    projectName,\n    ...cloneParams\n  } = params;\n\n  // Hooks, State, Variable\n  const defaultConditions = {\n    ...monthlyCostDataConfig,\n    ...cloneParams,\n    projectId: params.projectId ? {\n      value: params.projectId,\n      label: params.projectName\n    } : null\n  };\n\n  //get current month\n  const getCurrentMonth = getMonthsOfYear(defaultConditions.year).filter(month => {\n    return defaultConditions.month === month.value;\n  });\n\n  // Provide a default value or handle the case where getCurrentMonth might be empty\n  const initialMonth = getCurrentMonth.length > 0 ? convertMonthFromToDate(getCurrentMonth[0].label) : null;\n  const [loading, setLoading] = useState(false);\n  const [addOrEditLoading, setAddOrEditLoading] = useState(false);\n  const [monthlyCostData, setMonthlyCostData] = useState([]);\n  const [conditions, setConditions] = useState(defaultConditions);\n  const [formReset, setFormReset] = useState(defaultConditions);\n  const [actualCost, setActualCost] = useState(monthlyCostDataFormDefault);\n  const [year, setYear] = useState(defaultConditions.year);\n  const [months, setMonths] = useState([]);\n  const [month, setMonth] = useState(initialMonth);\n  const [isChangeYear, setIsChangeYear] = useState(false);\n  const [open, setOpen] = useState(false);\n  const [isEdit, setIsEdit] = useState(false);\n  const dispatch = useAppDispatch();\n  const {\n    monthlyProjectCost\n  } = PERMISSIONS.report;\n\n  // Function\n  const getDataTable = async () => {\n    setLoading(true);\n    const response = await sendRequest(Api.monthly_project_cost.getMonthlyCost, {\n      ...conditions,\n      projectId: conditions.projectId ? conditions.projectId.value : null\n    });\n    if (response) {\n      const {\n        status,\n        result\n      } = response;\n      if (status) {\n        const {\n          content\n        } = result;\n        setMonthlyCostData(content);\n        setLoading(false);\n      } else {\n        setDataEmpty();\n      }\n      return;\n    } else {\n      setDataEmpty();\n    }\n  };\n  const postAddOrEditActualCost = async payload => {\n    setAddOrEditLoading(true);\n    const response = await sendRequest(Api.monthly_project_cost.postSaveOrUpdateActualCost, payload);\n    const {\n      status\n    } = response;\n    if (status) {\n      setAddOrEditLoading(false);\n      setOpen(false);\n      dispatch(openSnackbar({\n        open: true,\n        message: isEdit ? 'update-success' : 'add-success',\n        variant: 'alert',\n        alert: {\n          color: 'success'\n        }\n      }));\n      dispatch(closeConfirm());\n      getDataTable();\n    }\n  };\n  const deleteActualCost = async id => {\n    const response = await sendRequest(Api.monthly_project_cost.deleteActualCost, {\n      id\n    });\n    const {\n      status\n    } = response;\n    if (status) {\n      dispatch(openSnackbar({\n        open: true,\n        message: 'delete-success',\n        variant: 'alert',\n        alert: {\n          color: 'success'\n        }\n      }));\n      dispatch(closeConfirm());\n      getDataTable();\n    }\n  };\n  const setDataEmpty = () => {\n    setMonthlyCostData([]);\n    setLoading(false);\n  };\n  const getMonthInYears = useCallback(async y => {\n    const monthInYears = await getMonthsOfYear(y);\n    return monthInYears;\n  }, []);\n\n  // Event\n  const handleChangeYear = e => {\n    const {\n      value\n    } = e.target;\n    setYear(value);\n    setIsChangeYear(true);\n    setMonth(convertMonthFromToDate(getMonthsOfYear(value)[0].label));\n    setFormReset({\n      ...formReset,\n      projectId: null\n    });\n  };\n  const handleChangeMonth = value => {\n    var _getMonth$;\n    const getMonth = months.filter(month => {\n      return month.value === value;\n    });\n    if (getMonth.length > 0) setMonth(convertMonthFromToDate((_getMonth$ = getMonth[0]) === null || _getMonth$ === void 0 ? void 0 : _getMonth$.label));\n  };\n  const handleOpenDialog = item => {\n    setIsEdit(item ? true : false);\n    setActualCost(item ? {\n      ...item,\n      projectId: {\n        value: item.projectId,\n        label: item.projectName\n      }\n    } : monthlyCostDataFormDefault);\n    setOpen(true);\n  };\n  const handleCloseDialog = () => {\n    setOpen(false);\n  };\n  const handleOpenConfirm = (item, type) => {\n    dispatch(openConfirm({\n      open: true,\n      title: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n        id: \"warning\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 24\n      }, this),\n      content: type === 'delete' ? /*#__PURE__*/_jsxDEV(FormattedMessage, {\n        id: \"delete-record\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 46\n      }, this) : /*#__PURE__*/_jsxDEV(FormattedMessage, {\n        id: \"overwrite-record\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 88\n      }, this),\n      handleConfirm: () => type === 'delete' ? deleteActualCost(`${item.idHexString}`) : handleAddActualCost(item)\n    }));\n  };\n  const handleExportTemplate = () => {\n    exportDocument(Api.monthly_project_cost.getDownloadTemplateMonthlyCost.url, {\n      year: conditions.year,\n      month: conditions.month\n    });\n  };\n\n  // Handle submit\n  const handleSearch = value => {\n    const {\n      projectId\n    } = value;\n    transformObject(value);\n    setSearchParams(projectId ? {\n      ...value,\n      projectId: projectId.value,\n      projectName: projectId.label\n    } : value);\n    setConditions(value);\n    // lưu thời gian vào localStorage\n    setLocalStorageSearchTime({\n      month: value.month,\n      year: value.year\n    });\n  };\n  const handleAddActualCost = actualCostNew => {\n    postAddOrEditActualCost(actualCostNew);\n  };\n  const handleEditActualCost = actualCostEdit => {\n    postAddOrEditActualCost(actualCostEdit);\n  };\n\n  // Effect\n  useEffect(() => {\n    getDataTable();\n  }, [conditions]);\n  useEffect(() => {\n    getMonthInYears(year).then(items => {\n      setMonths(items);\n      if (items.length > 0 && isChangeYear) {\n        setFormReset({\n          ...formReset,\n          year,\n          month: items[0].value\n        });\n      }\n    });\n  }, [year]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(FilterCollapse, {\n      children: /*#__PURE__*/_jsxDEV(MonthlyCostDataSearch, {\n        formReset: formReset,\n        months: months,\n        handleChangeYear: handleChangeYear,\n        handleSearch: handleSearch,\n        handleChangeMonth: handleChangeMonth,\n        month: month\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(MainCard, {\n      children: [/*#__PURE__*/_jsxDEV(TableToolbar, {\n        handleOpen: checkAllowedPermission(monthlyProjectCost.addCost) ? handleOpenDialog : undefined,\n        handleExportTemplate: checkAllowedPermission(monthlyProjectCost.downloadTemplate) ? handleExportTemplate : undefined,\n        handleRefreshData: getDataTable,\n        isShowUpload: checkAllowedPermission(monthlyProjectCost.importTemplate),\n        addLabel: monthlyCost + 'add-new'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        heads: /*#__PURE__*/_jsxDEV(MonthlyCostDataThead, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 31\n        }, this),\n        isLoading: loading,\n        data: monthlyCostData,\n        children: /*#__PURE__*/_jsxDEV(MonthlyCostDataTBody, {\n          data: monthlyCostData,\n          handleOpen: handleOpenDialog,\n          handleOpenDelete: handleOpenConfirm\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(AddActualCost, {\n      open: open,\n      loading: addOrEditLoading,\n      isEdit: isEdit,\n      actualCost: actualCost,\n      handleClose: handleCloseDialog,\n      setActualCost: setActualCost,\n      addActualCost: handleAddActualCost,\n      editActualCost: handleEditActualCost,\n      handleOpenConfirm: handleOpenConfirm\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(MonthlyCostData, \"S9/mSF7zVdwWtNWcCBB0AVaYcco=\", false, function () {\n  return [useSearchParams, useAppDispatch];\n});\n_c = MonthlyCostData;\nexport default MonthlyCostData;\nvar _c;\n$RefreshReg$(_c, \"MonthlyCostData\");", "map": {"version": 3, "names": ["useCallback", "useEffect", "useState", "FormattedMessage", "MonthlyCostDataSearch", "MonthlyCostDataTBody", "MonthlyCostDataThead", "exportDocument", "getSearchParam", "setLocalStorageSearchTime", "transformObject", "monthlyCostDataConfig", "monthlyCostDataFormDefault", "AddActualCost", "closeConfirm", "openConfirm", "convertMonthFromToDate", "getMonthsOfYear", "checkAllowedPermission", "openSnackbar", "SEARCH_PARAM_KEY", "TEXT_CONFIG_SCREEN", "FilterCollapse", "PERMISSIONS", "Table", "MainCard", "sendRequest", "useAppDispatch", "TableToolbar", "Api", "useSearchParams", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MonthlyCostData", "_s", "monthlyCost", "monthlyProjectCost", "searchParams", "setSearchParams", "keyParams", "year", "month", "projectId", "projectName", "params", "cloneParams", "defaultConditions", "value", "label", "getCurrentMonth", "filter", "initialMonth", "length", "loading", "setLoading", "addOrEditLoading", "setAddOrEditLoading", "monthlyCostData", "setMonthlyCostData", "conditions", "setConditions", "formReset", "setFormReset", "actualCost", "setActualCost", "setYear", "months", "setMonths", "setMonth", "isChangeYear", "setIsChangeYear", "open", "<PERSON><PERSON><PERSON>", "isEdit", "setIsEdit", "dispatch", "report", "getDataTable", "response", "monthly_project_cost", "getMonthlyCost", "status", "result", "content", "setDataEmpty", "postAddOrEditActualCost", "payload", "postSaveOrUpdateActualCost", "message", "variant", "alert", "color", "deleteActualCost", "id", "getMonthInYears", "y", "monthInYears", "handleChangeYear", "e", "target", "handleChangeMonth", "_getMonth$", "getMonth", "handleOpenDialog", "item", "handleCloseDialog", "handleOpenConfirm", "type", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleConfirm", "idHexString", "handleAddActualCost", "handleExportTemplate", "getDownloadTemplateMonthlyCost", "url", "handleSearch", "actualCostNew", "handleEditActualCost", "actualCostEdit", "then", "items", "children", "handleOpen", "addCost", "undefined", "downloadTemplate", "handleRefreshData", "isShowUpload", "importTemplate", "addLabel", "heads", "isLoading", "data", "handleOpenDelete", "handleClose", "addActualCost", "editActualCost", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/pages/monthly-project-cost/MonthlyCostData.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\nimport { useCallback, useEffect, useState } from 'react';\nimport { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { MonthlyCostDataSearch, MonthlyCostDataTBody, MonthlyCostDataThead } from 'containers/monthly-project-cost';\nimport { IMonthlyCostData, IMonthlyCostDataList, IMonthlyCostDataResponse, IOption, IResponseList } from 'types';\nimport { exportDocument, getSearchParam, setLocalStorageSearchTime, transformObject } from 'utils/common';\nimport { IMonthlyCostDataConfig, monthlyCostDataConfig, monthlyCostDataFormDefault } from './Config';\nimport AddActualCost from 'containers/monthly-project-cost/AddActualCost';\nimport { closeConfirm, openConfirm } from 'store/slice/confirmSlice';\nimport { convertMonthFromToDate, getMonthsOfYear } from 'utils/date';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { openSnackbar } from 'store/slice/snackbarSlice';\nimport { SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { FilterCollapse } from 'containers/search';\nimport { PERMISSIONS } from 'constants/Permission';\nimport { Table } from 'components/extended/Table';\nimport MainCard from 'components/cards/MainCard';\nimport sendRequest from 'services/ApiService';\nimport { useAppDispatch } from 'app/hooks';\nimport { TableToolbar } from 'containers';\nimport Api from 'constants/Api';\n\n// third party\nimport { useSearchParams } from 'react-router-dom';\n\n// ==============================|| Monthly Project Cost - Data ||============================== //\n/**\n *  URL Params\n *  year\n *  month\n *  projectId\n *  projectName\n */\nconst MonthlyCostData = () => {\n    const { monthlyCost } = TEXT_CONFIG_SCREEN.monthlyProjectCost;\n    // URL Params\n    const [searchParams, setSearchParams] = useSearchParams();\n    const keyParams = [SEARCH_PARAM_KEY.year, SEARCH_PARAM_KEY.month, SEARCH_PARAM_KEY.projectId, SEARCH_PARAM_KEY.projectName];\n    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);\n    transformObject(params);\n    // delete unnecessary key value\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const { projectName, ...cloneParams }: any = params;\n\n    // Hooks, State, Variable\n    const defaultConditions = {\n        ...monthlyCostDataConfig,\n        ...cloneParams,\n        projectId: params.projectId ? { value: params.projectId, label: params.projectName } : null\n    };\n\n    //get current month\n    const getCurrentMonth = getMonthsOfYear(defaultConditions.year).filter((month) => {\n        return defaultConditions.month === month.value;\n    });\n\n    // Provide a default value or handle the case where getCurrentMonth might be empty\n    const initialMonth = getCurrentMonth.length > 0 ? convertMonthFromToDate(getCurrentMonth[0].label) : null;\n\n    const [loading, setLoading] = useState<boolean>(false);\n    const [addOrEditLoading, setAddOrEditLoading] = useState<boolean>(false);\n    const [monthlyCostData, setMonthlyCostData] = useState<IMonthlyCostData[]>([]);\n    const [conditions, setConditions] = useState<IMonthlyCostDataConfig>(defaultConditions);\n    const [formReset, setFormReset] = useState<IMonthlyCostDataConfig>(defaultConditions);\n    const [actualCost, setActualCost] = useState<IMonthlyCostData>(monthlyCostDataFormDefault);\n    const [year, setYear] = useState<number>(defaultConditions.year);\n    const [months, setMonths] = useState<IOption[]>([]);\n    const [month, setMonth] = useState(initialMonth);\n    const [isChangeYear, setIsChangeYear] = useState<boolean>(false);\n    const [open, setOpen] = useState<boolean>(false);\n    const [isEdit, setIsEdit] = useState<boolean>(false);\n    const dispatch = useAppDispatch();\n    const { monthlyProjectCost } = PERMISSIONS.report;\n\n    // Function\n    const getDataTable = async () => {\n        setLoading(true);\n        const response: IResponseList<IMonthlyCostDataList> = await sendRequest(Api.monthly_project_cost.getMonthlyCost, {\n            ...conditions,\n            projectId: conditions.projectId ? conditions.projectId.value : null\n        });\n\n        if (response) {\n            const { status, result } = response;\n            if (status) {\n                const { content } = result;\n                setMonthlyCostData(content as IMonthlyCostData[]);\n                setLoading(false);\n            } else {\n                setDataEmpty();\n            }\n            return;\n        } else {\n            setDataEmpty();\n        }\n    };\n\n    const postAddOrEditActualCost = async (payload: IMonthlyCostData) => {\n        setAddOrEditLoading(true);\n        const response: IResponseList<IMonthlyCostDataResponse> = await sendRequest(\n            Api.monthly_project_cost.postSaveOrUpdateActualCost,\n            payload\n        );\n        const { status } = response;\n        if (status) {\n            setAddOrEditLoading(false);\n            setOpen(false);\n            dispatch(\n                openSnackbar({\n                    open: true,\n                    message: isEdit ? 'update-success' : 'add-success',\n                    variant: 'alert',\n                    alert: { color: 'success' }\n                })\n            );\n            dispatch(closeConfirm());\n            getDataTable();\n        }\n    };\n\n    const deleteActualCost = async (id: string) => {\n        const response: IResponseList<IMonthlyCostDataResponse> = await sendRequest(Api.monthly_project_cost.deleteActualCost, {\n            id\n        });\n        const { status } = response;\n        if (status) {\n            dispatch(\n                openSnackbar({\n                    open: true,\n                    message: 'delete-success',\n                    variant: 'alert',\n                    alert: { color: 'success' }\n                })\n            );\n            dispatch(closeConfirm());\n            getDataTable();\n        }\n    };\n\n    const setDataEmpty = () => {\n        setMonthlyCostData([]);\n        setLoading(false);\n    };\n\n    const getMonthInYears = useCallback(async (y: number) => {\n        const monthInYears = await getMonthsOfYear(y);\n        return monthInYears;\n    }, []);\n\n    // Event\n    const handleChangeYear = (e: any) => {\n        const { value } = e.target;\n        setYear(value);\n        setIsChangeYear(true);\n        setMonth(convertMonthFromToDate(getMonthsOfYear(value)[0].label));\n        setFormReset({ ...formReset, projectId: null });\n    };\n\n    const handleChangeMonth = (value: any) => {\n        const getMonth = months.filter((month) => {\n            return month.value === value;\n        });\n        if (getMonth.length > 0) setMonth(convertMonthFromToDate(getMonth[0]?.label));\n    };\n\n    const handleOpenDialog = (item?: any) => {\n        setIsEdit(item ? true : false);\n        setActualCost(item ? { ...item, projectId: { value: item.projectId, label: item.projectName } } : monthlyCostDataFormDefault);\n        setOpen(true);\n    };\n\n    const handleCloseDialog = () => {\n        setOpen(false);\n    };\n\n    const handleOpenConfirm = (item: IMonthlyCostData, type?: string) => {\n        dispatch(\n            openConfirm({\n                open: true,\n                title: <FormattedMessage id=\"warning\" />,\n                content: type === 'delete' ? <FormattedMessage id=\"delete-record\" /> : <FormattedMessage id=\"overwrite-record\" />,\n                handleConfirm: () => (type === 'delete' ? deleteActualCost(`${item.idHexString}`) : handleAddActualCost(item))\n            })\n        );\n    };\n\n    const handleExportTemplate = () => {\n        exportDocument(Api.monthly_project_cost.getDownloadTemplateMonthlyCost.url, { year: conditions.year, month: conditions.month });\n    };\n\n    // Handle submit\n    const handleSearch = (value: any) => {\n        const { projectId } = value;\n        transformObject(value);\n        setSearchParams(projectId ? { ...value, projectId: projectId.value, projectName: projectId.label } : value);\n        setConditions(value);\n        // lưu thời gian vào localStorage\n        setLocalStorageSearchTime({ month: value.month, year: value.year });\n    };\n\n    const handleAddActualCost = (actualCostNew: IMonthlyCostData) => {\n        postAddOrEditActualCost(actualCostNew);\n    };\n\n    const handleEditActualCost = (actualCostEdit: IMonthlyCostData) => {\n        postAddOrEditActualCost(actualCostEdit);\n    };\n\n    // Effect\n    useEffect(() => {\n        getDataTable();\n    }, [conditions]);\n\n    useEffect(() => {\n        getMonthInYears(year).then((items: IOption[]) => {\n            setMonths(items);\n            if (items.length > 0 && isChangeYear) {\n                setFormReset({ ...formReset, year, month: items[0].value });\n            }\n        });\n    }, [year]);\n\n    return (\n        <>\n            {/* Search form  */}\n            <FilterCollapse>\n                <MonthlyCostDataSearch\n                    formReset={formReset}\n                    months={months}\n                    handleChangeYear={handleChangeYear}\n                    handleSearch={handleSearch}\n                    handleChangeMonth={handleChangeMonth}\n                    month={month}\n                />\n            </FilterCollapse>\n\n            {/* Table and Toolbar */}\n            <MainCard>\n                <TableToolbar\n                    handleOpen={checkAllowedPermission(monthlyProjectCost.addCost) ? handleOpenDialog : undefined}\n                    handleExportTemplate={checkAllowedPermission(monthlyProjectCost.downloadTemplate) ? handleExportTemplate : undefined}\n                    handleRefreshData={getDataTable}\n                    isShowUpload={checkAllowedPermission(monthlyProjectCost.importTemplate)}\n                    addLabel={monthlyCost + 'add-new'}\n                />\n                <Table heads={<MonthlyCostDataThead />} isLoading={loading} data={monthlyCostData}>\n                    <MonthlyCostDataTBody data={monthlyCostData} handleOpen={handleOpenDialog} handleOpenDelete={handleOpenConfirm} />\n                </Table>\n            </MainCard>\n\n            {/* Add or Edit Actual Cost Dialog */}\n            <AddActualCost\n                open={open}\n                loading={addOrEditLoading}\n                isEdit={isEdit}\n                actualCost={actualCost}\n                handleClose={handleCloseDialog}\n                setActualCost={setActualCost}\n                addActualCost={handleAddActualCost}\n                editActualCost={handleEditActualCost}\n                handleOpenConfirm={handleOpenConfirm}\n            />\n        </>\n    );\n};\n\nexport default MonthlyCostData;\n"], "mappings": ";;AAAA;AACA,SAASA,WAAW,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACxD,SAASC,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,qBAAqB,EAAEC,oBAAoB,EAAEC,oBAAoB,QAAQ,iCAAiC;AAEnH,SAASC,cAAc,EAAEC,cAAc,EAAEC,yBAAyB,EAAEC,eAAe,QAAQ,cAAc;AACzG,SAAiCC,qBAAqB,EAAEC,0BAA0B,QAAQ,UAAU;AACpG,OAAOC,aAAa,MAAM,+CAA+C;AACzE,SAASC,YAAY,EAAEC,WAAW,QAAQ,0BAA0B;AACpE,SAASC,sBAAsB,EAAEC,eAAe,QAAQ,YAAY;AACpE,SAASC,sBAAsB,QAAQ,qBAAqB;AAC5D,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,gBAAgB,EAAEC,kBAAkB,QAAQ,kBAAkB;AACvE,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,KAAK,QAAQ,2BAA2B;AACjD,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,cAAc,QAAQ,WAAW;AAC1C,SAASC,YAAY,QAAQ,YAAY;AACzC,OAAOC,GAAG,MAAM,eAAe;;AAE/B;AACA,SAASC,eAAe,QAAQ,kBAAkB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOA,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC;EAAY,CAAC,GAAGhB,kBAAkB,CAACiB,kBAAkB;EAC7D;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,eAAe,CAAC,CAAC;EACzD,MAAMW,SAAS,GAAG,CAACrB,gBAAgB,CAACsB,IAAI,EAAEtB,gBAAgB,CAACuB,KAAK,EAAEvB,gBAAgB,CAACwB,SAAS,EAAExB,gBAAgB,CAACyB,WAAW,CAAC;EAC3H,MAAMC,MAA8B,GAAGtC,cAAc,CAACiC,SAAS,EAAEF,YAAY,CAAC;EAC9E7B,eAAe,CAACoC,MAAM,CAAC;EACvB;EACA;EACA,MAAM;IAAED,WAAW;IAAE,GAAGE;EAAiB,CAAC,GAAGD,MAAM;;EAEnD;EACA,MAAME,iBAAiB,GAAG;IACtB,GAAGrC,qBAAqB;IACxB,GAAGoC,WAAW;IACdH,SAAS,EAAEE,MAAM,CAACF,SAAS,GAAG;MAAEK,KAAK,EAAEH,MAAM,CAACF,SAAS;MAAEM,KAAK,EAAEJ,MAAM,CAACD;IAAY,CAAC,GAAG;EAC3F,CAAC;;EAED;EACA,MAAMM,eAAe,GAAGlC,eAAe,CAAC+B,iBAAiB,CAACN,IAAI,CAAC,CAACU,MAAM,CAAET,KAAK,IAAK;IAC9E,OAAOK,iBAAiB,CAACL,KAAK,KAAKA,KAAK,CAACM,KAAK;EAClD,CAAC,CAAC;;EAEF;EACA,MAAMI,YAAY,GAAGF,eAAe,CAACG,MAAM,GAAG,CAAC,GAAGtC,sBAAsB,CAACmC,eAAe,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,GAAG,IAAI;EAEzG,MAAM,CAACK,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAU,KAAK,CAAC;EACtD,MAAM,CAACuD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxD,QAAQ,CAAU,KAAK,CAAC;EACxE,MAAM,CAACyD,eAAe,EAAEC,kBAAkB,CAAC,GAAG1D,QAAQ,CAAqB,EAAE,CAAC;EAC9E,MAAM,CAAC2D,UAAU,EAAEC,aAAa,CAAC,GAAG5D,QAAQ,CAAyB8C,iBAAiB,CAAC;EACvF,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAG9D,QAAQ,CAAyB8C,iBAAiB,CAAC;EACrF,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAmBU,0BAA0B,CAAC;EAC1F,MAAM,CAAC8B,IAAI,EAAEyB,OAAO,CAAC,GAAGjE,QAAQ,CAAS8C,iBAAiB,CAACN,IAAI,CAAC;EAChE,MAAM,CAAC0B,MAAM,EAAEC,SAAS,CAAC,GAAGnE,QAAQ,CAAY,EAAE,CAAC;EACnD,MAAM,CAACyC,KAAK,EAAE2B,QAAQ,CAAC,GAAGpE,QAAQ,CAACmD,YAAY,CAAC;EAChD,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGtE,QAAQ,CAAU,KAAK,CAAC;EAChE,MAAM,CAACuE,IAAI,EAAEC,OAAO,CAAC,GAAGxE,QAAQ,CAAU,KAAK,CAAC;EAChD,MAAM,CAACyE,MAAM,EAAEC,SAAS,CAAC,GAAG1E,QAAQ,CAAU,KAAK,CAAC;EACpD,MAAM2E,QAAQ,GAAGlD,cAAc,CAAC,CAAC;EACjC,MAAM;IAAEW;EAAmB,CAAC,GAAGf,WAAW,CAACuD,MAAM;;EAEjD;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7BvB,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMwB,QAA6C,GAAG,MAAMtD,WAAW,CAACG,GAAG,CAACoD,oBAAoB,CAACC,cAAc,EAAE;MAC7G,GAAGrB,UAAU;MACbjB,SAAS,EAAEiB,UAAU,CAACjB,SAAS,GAAGiB,UAAU,CAACjB,SAAS,CAACK,KAAK,GAAG;IACnE,CAAC,CAAC;IAEF,IAAI+B,QAAQ,EAAE;MACV,MAAM;QAAEG,MAAM;QAAEC;MAAO,CAAC,GAAGJ,QAAQ;MACnC,IAAIG,MAAM,EAAE;QACR,MAAM;UAAEE;QAAQ,CAAC,GAAGD,MAAM;QAC1BxB,kBAAkB,CAACyB,OAA6B,CAAC;QACjD7B,UAAU,CAAC,KAAK,CAAC;MACrB,CAAC,MAAM;QACH8B,YAAY,CAAC,CAAC;MAClB;MACA;IACJ,CAAC,MAAM;MACHA,YAAY,CAAC,CAAC;IAClB;EACJ,CAAC;EAED,MAAMC,uBAAuB,GAAG,MAAOC,OAAyB,IAAK;IACjE9B,mBAAmB,CAAC,IAAI,CAAC;IACzB,MAAMsB,QAAiD,GAAG,MAAMtD,WAAW,CACvEG,GAAG,CAACoD,oBAAoB,CAACQ,0BAA0B,EACnDD,OACJ,CAAC;IACD,MAAM;MAAEL;IAAO,CAAC,GAAGH,QAAQ;IAC3B,IAAIG,MAAM,EAAE;MACRzB,mBAAmB,CAAC,KAAK,CAAC;MAC1BgB,OAAO,CAAC,KAAK,CAAC;MACdG,QAAQ,CACJ1D,YAAY,CAAC;QACTsD,IAAI,EAAE,IAAI;QACViB,OAAO,EAAEf,MAAM,GAAG,gBAAgB,GAAG,aAAa;QAClDgB,OAAO,EAAE,OAAO;QAChBC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAC9B,CAAC,CACL,CAAC;MACDhB,QAAQ,CAAC/D,YAAY,CAAC,CAAC,CAAC;MACxBiE,YAAY,CAAC,CAAC;IAClB;EACJ,CAAC;EAED,MAAMe,gBAAgB,GAAG,MAAOC,EAAU,IAAK;IAC3C,MAAMf,QAAiD,GAAG,MAAMtD,WAAW,CAACG,GAAG,CAACoD,oBAAoB,CAACa,gBAAgB,EAAE;MACnHC;IACJ,CAAC,CAAC;IACF,MAAM;MAAEZ;IAAO,CAAC,GAAGH,QAAQ;IAC3B,IAAIG,MAAM,EAAE;MACRN,QAAQ,CACJ1D,YAAY,CAAC;QACTsD,IAAI,EAAE,IAAI;QACViB,OAAO,EAAE,gBAAgB;QACzBC,OAAO,EAAE,OAAO;QAChBC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAC9B,CAAC,CACL,CAAC;MACDhB,QAAQ,CAAC/D,YAAY,CAAC,CAAC,CAAC;MACxBiE,YAAY,CAAC,CAAC;IAClB;EACJ,CAAC;EAED,MAAMO,YAAY,GAAGA,CAAA,KAAM;IACvB1B,kBAAkB,CAAC,EAAE,CAAC;IACtBJ,UAAU,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMwC,eAAe,GAAGhG,WAAW,CAAC,MAAOiG,CAAS,IAAK;IACrD,MAAMC,YAAY,GAAG,MAAMjF,eAAe,CAACgF,CAAC,CAAC;IAC7C,OAAOC,YAAY;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,gBAAgB,GAAIC,CAAM,IAAK;IACjC,MAAM;MAAEnD;IAAM,CAAC,GAAGmD,CAAC,CAACC,MAAM;IAC1BlC,OAAO,CAAClB,KAAK,CAAC;IACduB,eAAe,CAAC,IAAI,CAAC;IACrBF,QAAQ,CAACtD,sBAAsB,CAACC,eAAe,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACjEc,YAAY,CAAC;MAAE,GAAGD,SAAS;MAAEnB,SAAS,EAAE;IAAK,CAAC,CAAC;EACnD,CAAC;EAED,MAAM0D,iBAAiB,GAAIrD,KAAU,IAAK;IAAA,IAAAsD,UAAA;IACtC,MAAMC,QAAQ,GAAGpC,MAAM,CAAChB,MAAM,CAAET,KAAK,IAAK;MACtC,OAAOA,KAAK,CAACM,KAAK,KAAKA,KAAK;IAChC,CAAC,CAAC;IACF,IAAIuD,QAAQ,CAAClD,MAAM,GAAG,CAAC,EAAEgB,QAAQ,CAACtD,sBAAsB,EAAAuF,UAAA,GAACC,QAAQ,CAAC,CAAC,CAAC,cAAAD,UAAA,uBAAXA,UAAA,CAAarD,KAAK,CAAC,CAAC;EACjF,CAAC;EAED,MAAMuD,gBAAgB,GAAIC,IAAU,IAAK;IACrC9B,SAAS,CAAC8B,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC;IAC9BxC,aAAa,CAACwC,IAAI,GAAG;MAAE,GAAGA,IAAI;MAAE9D,SAAS,EAAE;QAAEK,KAAK,EAAEyD,IAAI,CAAC9D,SAAS;QAAEM,KAAK,EAAEwD,IAAI,CAAC7D;MAAY;IAAE,CAAC,GAAGjC,0BAA0B,CAAC;IAC7H8D,OAAO,CAAC,IAAI,CAAC;EACjB,CAAC;EAED,MAAMiC,iBAAiB,GAAGA,CAAA,KAAM;IAC5BjC,OAAO,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,MAAMkC,iBAAiB,GAAGA,CAACF,IAAsB,EAAEG,IAAa,KAAK;IACjEhC,QAAQ,CACJ9D,WAAW,CAAC;MACR0D,IAAI,EAAE,IAAI;MACVqC,KAAK,eAAE9E,OAAA,CAAC7B,gBAAgB;QAAC4F,EAAE,EAAC;MAAS;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACxC7B,OAAO,EAAEwB,IAAI,KAAK,QAAQ,gBAAG7E,OAAA,CAAC7B,gBAAgB;QAAC4F,EAAE,EAAC;MAAe;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAGlF,OAAA,CAAC7B,gBAAgB;QAAC4F,EAAE,EAAC;MAAkB;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACjHC,aAAa,EAAEA,CAAA,KAAON,IAAI,KAAK,QAAQ,GAAGf,gBAAgB,CAAC,GAAGY,IAAI,CAACU,WAAW,EAAE,CAAC,GAAGC,mBAAmB,CAACX,IAAI;IAChH,CAAC,CACL,CAAC;EACL,CAAC;EAED,MAAMY,oBAAoB,GAAGA,CAAA,KAAM;IAC/B/G,cAAc,CAACsB,GAAG,CAACoD,oBAAoB,CAACsC,8BAA8B,CAACC,GAAG,EAAE;MAAE9E,IAAI,EAAEmB,UAAU,CAACnB,IAAI;MAAEC,KAAK,EAAEkB,UAAU,CAAClB;IAAM,CAAC,CAAC;EACnI,CAAC;;EAED;EACA,MAAM8E,YAAY,GAAIxE,KAAU,IAAK;IACjC,MAAM;MAAEL;IAAU,CAAC,GAAGK,KAAK;IAC3BvC,eAAe,CAACuC,KAAK,CAAC;IACtBT,eAAe,CAACI,SAAS,GAAG;MAAE,GAAGK,KAAK;MAAEL,SAAS,EAAEA,SAAS,CAACK,KAAK;MAAEJ,WAAW,EAAED,SAAS,CAACM;IAAM,CAAC,GAAGD,KAAK,CAAC;IAC3Ga,aAAa,CAACb,KAAK,CAAC;IACpB;IACAxC,yBAAyB,CAAC;MAAEkC,KAAK,EAAEM,KAAK,CAACN,KAAK;MAAED,IAAI,EAAEO,KAAK,CAACP;IAAK,CAAC,CAAC;EACvE,CAAC;EAED,MAAM2E,mBAAmB,GAAIK,aAA+B,IAAK;IAC7DnC,uBAAuB,CAACmC,aAAa,CAAC;EAC1C,CAAC;EAED,MAAMC,oBAAoB,GAAIC,cAAgC,IAAK;IAC/DrC,uBAAuB,CAACqC,cAAc,CAAC;EAC3C,CAAC;;EAED;EACA3H,SAAS,CAAC,MAAM;IACZ8E,YAAY,CAAC,CAAC;EAClB,CAAC,EAAE,CAAClB,UAAU,CAAC,CAAC;EAEhB5D,SAAS,CAAC,MAAM;IACZ+F,eAAe,CAACtD,IAAI,CAAC,CAACmF,IAAI,CAAEC,KAAgB,IAAK;MAC7CzD,SAAS,CAACyD,KAAK,CAAC;MAChB,IAAIA,KAAK,CAACxE,MAAM,GAAG,CAAC,IAAIiB,YAAY,EAAE;QAClCP,YAAY,CAAC;UAAE,GAAGD,SAAS;UAAErB,IAAI;UAAEC,KAAK,EAAEmF,KAAK,CAAC,CAAC,CAAC,CAAC7E;QAAM,CAAC,CAAC;MAC/D;IACJ,CAAC,CAAC;EACN,CAAC,EAAE,CAACP,IAAI,CAAC,CAAC;EAEV,oBACIV,OAAA,CAAAE,SAAA;IAAA6F,QAAA,gBAEI/F,OAAA,CAACV,cAAc;MAAAyG,QAAA,eACX/F,OAAA,CAAC5B,qBAAqB;QAClB2D,SAAS,EAAEA,SAAU;QACrBK,MAAM,EAAEA,MAAO;QACf+B,gBAAgB,EAAEA,gBAAiB;QACnCsB,YAAY,EAAEA,YAAa;QAC3BnB,iBAAiB,EAAEA,iBAAkB;QACrC3D,KAAK,EAAEA;MAAM;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC,eAGjBlF,OAAA,CAACP,QAAQ;MAAAsG,QAAA,gBACL/F,OAAA,CAACJ,YAAY;QACToG,UAAU,EAAE9G,sBAAsB,CAACoB,kBAAkB,CAAC2F,OAAO,CAAC,GAAGxB,gBAAgB,GAAGyB,SAAU;QAC9FZ,oBAAoB,EAAEpG,sBAAsB,CAACoB,kBAAkB,CAAC6F,gBAAgB,CAAC,GAAGb,oBAAoB,GAAGY,SAAU;QACrHE,iBAAiB,EAAErD,YAAa;QAChCsD,YAAY,EAAEnH,sBAAsB,CAACoB,kBAAkB,CAACgG,cAAc,CAAE;QACxEC,QAAQ,EAAElG,WAAW,GAAG;MAAU;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACFlF,OAAA,CAACR,KAAK;QAACgH,KAAK,eAAExG,OAAA,CAAC1B,oBAAoB;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACuB,SAAS,EAAElF,OAAQ;QAACmF,IAAI,EAAE/E,eAAgB;QAAAoE,QAAA,eAC9E/F,OAAA,CAAC3B,oBAAoB;UAACqI,IAAI,EAAE/E,eAAgB;UAACqE,UAAU,EAAEvB,gBAAiB;UAACkC,gBAAgB,EAAE/B;QAAkB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/G,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGXlF,OAAA,CAACnB,aAAa;MACV4D,IAAI,EAAEA,IAAK;MACXlB,OAAO,EAAEE,gBAAiB;MAC1BkB,MAAM,EAAEA,MAAO;MACfV,UAAU,EAAEA,UAAW;MACvB2E,WAAW,EAAEjC,iBAAkB;MAC/BzC,aAAa,EAAEA,aAAc;MAC7B2E,aAAa,EAAExB,mBAAoB;MACnCyB,cAAc,EAAEnB,oBAAqB;MACrCf,iBAAiB,EAAEA;IAAkB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAAA,eACJ,CAAC;AAEX,CAAC;AAAC9E,EAAA,CAvOID,eAAe;EAAA,QAGuBL,eAAe,EAmCtCH,cAAc;AAAA;AAAAoH,EAAA,GAtC7B5G,eAAe;AAyOrB,eAAeA,eAAe;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}