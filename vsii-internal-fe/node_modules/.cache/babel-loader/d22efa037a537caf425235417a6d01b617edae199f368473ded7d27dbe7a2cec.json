{"ast": null, "code": "import { mix } from './mix.es.js';\nimport { noopReturn } from './noop.es.js';\nimport { fillOffset, defaultOffset } from './offset.es.js';\nimport { progress } from './progress.es.js';\nimport { getEasingForSegment } from './easing.es.js';\nimport { clamp } from './clamp.es.js';\nfunction interpolate(output, input = defaultOffset(output.length), easing = noopReturn) {\n  const length = output.length;\n  /**\n   * If the input length is lower than the output we\n   * fill the input to match. This currently assumes the input\n   * is an animation progress value so is a good candidate for\n   * moving outside the function.\n   */\n  const remainder = length - input.length;\n  remainder > 0 && fillOffset(input, remainder);\n  return t => {\n    let i = 0;\n    for (; i < length - 2; i++) {\n      if (t < input[i + 1]) break;\n    }\n    let progressInRange = clamp(0, 1, progress(input[i], input[i + 1], t));\n    const segmentEasing = getEasingForSegment(easing, i);\n    progressInRange = segmentEasing(progressInRange);\n    return mix(output[i], output[i + 1], progressInRange);\n  };\n}\nexport { interpolate };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}