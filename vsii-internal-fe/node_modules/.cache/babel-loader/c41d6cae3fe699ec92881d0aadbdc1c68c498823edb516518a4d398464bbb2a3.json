{"ast": null, "code": "\"use strict\";\n\"use client\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _createSvgIcon = _interopRequireDefault(require(\"./utils/createSvgIcon\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nvar _default = exports.default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)(\"path\", {\n  d: \"M14.17 11H13V5h-2v6H9.83L12 13.17z\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/(0, _jsxRuntime.jsx)(\"path\", {\n  d: \"M5 18h14v2H5zm14-9h-4V3H9v6H5l7 7zm-8 2V5h2v6h1.17L12 13.17 9.83 11z\"\n}, \"1\")], 'FileDownloadTwoTone');", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}