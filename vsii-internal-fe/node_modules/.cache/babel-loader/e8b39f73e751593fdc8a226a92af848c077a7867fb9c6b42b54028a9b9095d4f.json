{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/monthly-project-cost/DetailReportByMonthTFooter.tsx\";\nimport { TableCell, TableRow } from '@mui/material';\nimport { formatPrice } from 'utils/common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DetailReportByMonthTFooter = ({\n  projects\n}) => {\n  const total = projects.reduce((prev, next) => ({\n    overheadAllocatedAmt: prev.overheadAllocatedAmt + (next.overheadAllocatedAmt || 0),\n    salaryCost: prev.salaryCost + (next.salaryCost || 0),\n    totalCost: prev.totalCost + (next.totalCost || 0)\n  }), {\n    overheadAllocatedAmt: 0,\n    salaryCost: 0,\n    totalCost: 0\n  });\n  return /*#__PURE__*/_jsxDEV(TableRow, {\n    sx: {\n      '& .MuiTableCell-root': {\n        fontWeight: '600',\n        color: '#000'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n      colSpan: 3,\n      children: \"\\xA0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n      align: \"center\",\n      children: \"Total\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n      align: \"center\",\n      children: formatPrice(total.overheadAllocatedAmt)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n      align: \"center\",\n      children: formatPrice(total.salaryCost)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n      align: \"center\",\n      children: formatPrice(total.totalCost)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 9\n  }, this);\n};\n_c = DetailReportByMonthTFooter;\nexport default DetailReportByMonthTFooter;\nvar _c;\n$RefreshReg$(_c, \"DetailReportByMonthTFooter\");", "map": {"version": 3, "names": ["TableCell", "TableRow", "formatPrice", "jsxDEV", "_jsxDEV", "DetailReportByMonthTFooter", "projects", "total", "reduce", "prev", "next", "overheadAllocatedAmt", "salaryCost", "totalCost", "sx", "fontWeight", "color", "children", "colSpan", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "align", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/monthly-project-cost/DetailReportByMonthTFooter.tsx"], "sourcesContent": ["import { TableCell, TableRow } from '@mui/material';\n\nimport { IDetailReportByMonth } from 'types';\nimport { formatPrice } from 'utils/common';\n\ninterface IDetailReportByMonthTFooterProps {\n    projects: IDetailReportByMonth[];\n}\n\nconst DetailReportByMonthTFooter = ({ projects }: IDetailReportByMonthTFooterProps) => {\n    const total = projects.reduce(\n        (prev, next) => ({\n            overheadAllocatedAmt: prev.overheadAllocatedAmt + (next.overheadAllocatedAmt || 0),\n            salaryCost: prev.salaryCost + (next.salaryCost || 0),\n            totalCost: prev.totalCost + (next.totalCost || 0)\n        }),\n        { overheadAllocatedAmt: 0, salaryCost: 0, totalCost: 0 }\n    );\n\n    return (\n        <TableRow sx={{ '& .MuiTableCell-root': { fontWeight: '600', color: '#000' } }}>\n            <TableCell colSpan={3}>&nbsp;</TableCell>\n            <TableCell align=\"center\">Total</TableCell>\n            <TableCell align=\"center\">{formatPrice(total.overheadAllocatedAmt)}</TableCell>\n            <TableCell align=\"center\">{formatPrice(total.salaryCost)}</TableCell>\n            <TableCell align=\"center\">{formatPrice(total.totalCost)}</TableCell>\n        </TableRow>\n    );\n};\n\nexport default DetailReportByMonthTFooter;\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAGnD,SAASC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM3C,MAAMC,0BAA0B,GAAGA,CAAC;EAAEC;AAA2C,CAAC,KAAK;EACnF,MAAMC,KAAK,GAAGD,QAAQ,CAACE,MAAM,CACzB,CAACC,IAAI,EAAEC,IAAI,MAAM;IACbC,oBAAoB,EAAEF,IAAI,CAACE,oBAAoB,IAAID,IAAI,CAACC,oBAAoB,IAAI,CAAC,CAAC;IAClFC,UAAU,EAAEH,IAAI,CAACG,UAAU,IAAIF,IAAI,CAACE,UAAU,IAAI,CAAC,CAAC;IACpDC,SAAS,EAAEJ,IAAI,CAACI,SAAS,IAAIH,IAAI,CAACG,SAAS,IAAI,CAAC;EACpD,CAAC,CAAC,EACF;IAAEF,oBAAoB,EAAE,CAAC;IAAEC,UAAU,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAE,CAC3D,CAAC;EAED,oBACIT,OAAA,CAACH,QAAQ;IAACa,EAAE,EAAE;MAAE,sBAAsB,EAAE;QAAEC,UAAU,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAO;IAAE,CAAE;IAAAC,QAAA,gBAC3Eb,OAAA,CAACJ,SAAS;MAACkB,OAAO,EAAE,CAAE;MAAAD,QAAA,EAAC;IAAM;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAW,CAAC,eACzClB,OAAA,CAACJ,SAAS;MAACuB,KAAK,EAAC,QAAQ;MAAAN,QAAA,EAAC;IAAK;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAW,CAAC,eAC3ClB,OAAA,CAACJ,SAAS;MAACuB,KAAK,EAAC,QAAQ;MAAAN,QAAA,EAAEf,WAAW,CAACK,KAAK,CAACI,oBAAoB;IAAC;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAC/ElB,OAAA,CAACJ,SAAS;MAACuB,KAAK,EAAC,QAAQ;MAAAN,QAAA,EAAEf,WAAW,CAACK,KAAK,CAACK,UAAU;IAAC;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACrElB,OAAA,CAACJ,SAAS;MAACuB,KAAK,EAAC,QAAQ;MAAAN,QAAA,EAAEf,WAAW,CAACK,KAAK,CAACM,SAAS;IAAC;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9D,CAAC;AAEnB,CAAC;AAACE,EAAA,GAnBInB,0BAA0B;AAqBhC,eAAeA,0BAA0B;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}