{"ast": null, "code": "function findIndex(arr, err) {\n  let idx = Infinity;\n  arr.some((key, ii) => {\n    var _err$path;\n    if (((_err$path = err.path) == null ? void 0 : _err$path.indexOf(key)) !== -1) {\n      idx = ii;\n      return true;\n    }\n  });\n  return idx;\n}\nexport default function sortByKeyOrder(keys) {\n  return (a, b) => {\n    return findIndex(keys, a) - findIndex(keys, b);\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}