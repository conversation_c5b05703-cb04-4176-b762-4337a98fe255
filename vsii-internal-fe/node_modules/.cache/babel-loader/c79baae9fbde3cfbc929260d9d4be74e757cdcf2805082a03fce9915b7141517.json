{"ast": null, "code": "var negativeMapping = {\n  ceil: 'zero',\n  floor: 'infinity',\n  expand: 'infinity',\n  trunc: 'zero',\n  halfCeil: 'half-zero',\n  halfFloor: 'half-infinity',\n  halfExpand: 'half-infinity',\n  halfTrunc: 'half-zero',\n  halfEven: 'half-even'\n};\nvar positiveMapping = {\n  ceil: 'infinity',\n  floor: 'zero',\n  expand: 'infinity',\n  trunc: 'zero',\n  halfCeil: 'half-infinity',\n  halfFloor: 'half-zero',\n  halfExpand: 'half-infinity',\n  halfTrunc: 'half-zero',\n  halfEven: 'half-even'\n};\nexport function GetUnsignedRoundingMode(roundingMode, isNegative) {\n  if (isNegative) {\n    return negativeMapping[roundingMode];\n  }\n  return positiveMapping[roundingMode];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}