{"ast": null, "code": "import { isBrowser } from '../utils/is-browser.mjs';\n\n// We check for event support via functions in case they've been mocked by a testing suite.\nconst supportsPointerEvents = () => isBrowser && window.onpointerdown === null;\nconst supportsTouchEvents = () => isBrowser && window.ontouchstart === null;\nconst supportsMouseEvents = () => isBrowser && window.onmousedown === null;\nexport { supportsMouseEvents, supportsPointerEvents, supportsTouchEvents };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}