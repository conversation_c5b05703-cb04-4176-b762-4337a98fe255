{"ast": null, "code": "// material-ui\nimport{TableB<PERSON>,TableCell,TableRow,Stack,Tooltip,IconButton}from'@mui/material';import HighlightOffIcon from'@mui/icons-material/HighlightOff';// assets\nimport EditTwoToneIcon from'@mui/icons-material/EditTwoTone';// project import\nimport{checkAllowedPermission}from'utils/authorization';import{PERMISSIONS}from'constants/Permission';// third party\nimport{FormattedMessage}from'react-intl';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ReferenceConfigTBody=props=>{const{references,handleOpen,pageNumber,pageSize,handleDelete}=props;const{cVConfigReferencePermission}=PERMISSIONS.admin;return/*#__PURE__*/_jsx(TableBody,{children:references===null||references===void 0?void 0:references.map((reference,key)=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:pageSize*pageNumber+key+1}),/*#__PURE__*/_jsx(TableCell,{children:reference.fullName}),/*#__PURE__*/_jsx(TableCell,{children:reference.position}),/*#__PURE__*/_jsx(TableCell,{children:reference.userUpdate}),checkAllowedPermission(cVConfigReferencePermission.edit)&&/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsxs(Stack,{direction:\"row\",justifyContent:\"center\",alignItems:\"center\",children:[/*#__PURE__*/_jsx(Tooltip,{placement:\"top\",title:/*#__PURE__*/_jsx(FormattedMessage,{id:'edit'}),onClick:()=>handleOpen(reference),children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"edit\",size:\"small\",children:/*#__PURE__*/_jsx(EditTwoToneIcon,{sx:{fontSize:'1.1rem'}})})}),/*#__PURE__*/_jsx(Tooltip,{placement:\"top\",title:/*#__PURE__*/_jsx(FormattedMessage,{id:\"delete\"}),onClick:()=>handleDelete(reference),children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"delete\",size:\"small\",children:/*#__PURE__*/_jsx(HighlightOffIcon,{sx:{fontSize:'1.1rem'}})})})]})})]},key))});};export default ReferenceConfigTBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}