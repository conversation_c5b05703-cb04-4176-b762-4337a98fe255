{"ast": null, "code": "import { useReducer, useRef, useLayoutEffect, useEffect } from 'react';\nconst useRifm = props => {\n  const [, refresh] = useReducer(c => c + 1, 0);\n  const valueRef = useRef(null);\n  const {\n    replace,\n    append\n  } = props;\n  const userValue = replace ? replace(props.format(props.value)) : props.format(props.value); // state of delete button see comments below about inputType support\n\n  const isDeleleteButtonDownRef = useRef(false);\n  const onChange = evt => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (evt.target.type === 'number') {\n        console.error('Rifm does not support input type=number, use type=tel instead.');\n        return;\n      }\n      if (evt.target.type === 'date') {\n        console.error('Rifm does not support input type=date.');\n        return;\n      }\n    }\n    const eventValue = evt.target.value;\n    valueRef.current = [eventValue,\n    // eventValue\n    evt.target,\n    // input\n    eventValue.length > userValue.length,\n    // isSizeIncreaseOperation\n    isDeleleteButtonDownRef.current,\n    // isDeleleteButtonDown\n    userValue === props.format(eventValue) // isNoOperation\n    ];\n    if (process.env.NODE_ENV !== 'production') {\n      const formattedEventValue = props.format(eventValue);\n      if (eventValue !== formattedEventValue && eventValue.toLowerCase() === formattedEventValue.toLowerCase()) {\n        console.warn('Case enforcement does not work with format. Please use replace={value => value.toLowerCase()} instead');\n      }\n    } // The main trick is to update underlying input with non formatted value (= eventValue)\n    // that allows us to calculate right cursor position after formatting (see getCursorPosition)\n    // then we format new value and call props.onChange with masked/formatted value\n    // and finally we are able to set cursor position into right place\n\n    refresh();\n  }; // React prints warn on server in non production mode about useLayoutEffect usage\n  // in both cases it's noop\n\n  if (process.env.NODE_ENV === 'production' || typeof window !== 'undefined') {\n    useLayoutEffect(() => {\n      if (valueRef.current == null) return;\n      let [eventValue, input, isSizeIncreaseOperation, isDeleleteButtonDown,\n      // No operation means that value itself hasn't been changed, BTW cursor, selection etc can be changed\n      isNoOperation] = valueRef.current;\n      valueRef.current = null; // this usually occurs on deleting special symbols like ' here 123'123.00\n      // in case of isDeleleteButtonDown cursor should move differently vs backspace\n\n      const deleteWasNoOp = isDeleleteButtonDown && isNoOperation;\n      const valueAfterSelectionStart = eventValue.slice(input.selectionStart);\n      const acceptedCharIndexAfterDelete = valueAfterSelectionStart.search(props.accept || /\\d/g);\n      const charsToSkipAfterDelete = acceptedCharIndexAfterDelete !== -1 ? acceptedCharIndexAfterDelete : 0; // Create string from only accepted symbols\n\n      const clean = str => (str.match(props.accept || /\\d/g) || []).join('');\n      const valueBeforeSelectionStart = clean(eventValue.substr(0, input.selectionStart)); // trying to find cursor position in formatted value having knowledge about valueBeforeSelectionStart\n      // This works because we assume that format doesn't change the order of accepted symbols.\n      // Imagine we have formatter which adds ' symbol between numbers, and by default we refuse all non numeric symbols\n      // for example we had input = 1'2|'4 (| means cursor position) then user entered '3' symbol\n      // inputValue = 1'23'|4 so valueBeforeSelectionStart = 123 and formatted value = 1'2'3'4\n      // calling getCursorPosition(\"1'2'3'4\") will give us position after 3, 1'2'3|'4\n      // so for formatting just this function to determine cursor position after formatting is enough\n      // with masking we need to do some additional checks see `mask` below\n\n      const getCursorPosition = val => {\n        let start = 0;\n        let cleanPos = 0;\n        for (let i = 0; i !== valueBeforeSelectionStart.length; ++i) {\n          let newPos = val.indexOf(valueBeforeSelectionStart[i], start) + 1;\n          let newCleanPos = clean(val).indexOf(valueBeforeSelectionStart[i], cleanPos) + 1; // this skips position change if accepted symbols order was broken\n          // For example fixes edge case with fixed point numbers:\n          // You have '0|.00', then press 1, it becomes 01|.00 and after format 1.00, this breaks our assumption\n          // that order of accepted symbols is not changed after format,\n          // so here we don't update start position if other accepted symbols was inbetween current and new position\n\n          if (newCleanPos - cleanPos > 1) {\n            newPos = start;\n            newCleanPos = cleanPos;\n          }\n          cleanPos = Math.max(newCleanPos, cleanPos);\n          start = Math.max(start, newPos);\n        }\n        return start;\n      }; // Masking part, for masks if size of mask is above some value\n      // we need to replace symbols instead of do nothing as like in format\n\n      if (props.mask === true && isSizeIncreaseOperation && !isNoOperation) {\n        let start = getCursorPosition(eventValue);\n        const c = clean(eventValue.substr(start))[0];\n        start = eventValue.indexOf(c, start);\n        eventValue = \"\".concat(eventValue.substr(0, start)).concat(eventValue.substr(start + 1));\n      }\n      let formattedValue = props.format(eventValue);\n      if (append != null &&\n      // cursor at the end\n      input.selectionStart === eventValue.length && !isNoOperation) {\n        if (isSizeIncreaseOperation) {\n          formattedValue = append(formattedValue);\n        } else {\n          // If after delete last char is special character and we use append\n          // delete it too\n          // was: \"12-3|\" backspace pressed, then should be \"12|\"\n          if (clean(formattedValue.slice(-1)) === '') {\n            formattedValue = formattedValue.slice(0, -1);\n          }\n        }\n      }\n      const replacedValue = replace ? replace(formattedValue) : formattedValue;\n      if (userValue === replacedValue) {\n        // if nothing changed for formatted value, just refresh so userValue will be used at render\n        refresh();\n      } else {\n        props.onChange(replacedValue);\n      }\n      return () => {\n        let start = getCursorPosition(formattedValue); // Visually improves working with masked values,\n        // like cursor jumping over refused symbols\n        // as an example date mask: was \"5|1-24-3\" then user pressed \"6\"\n        // it becomes \"56-|12-43\" with this code, and \"56|-12-43\" without\n\n        if (props.mask != null && (isSizeIncreaseOperation || isDeleleteButtonDown && !deleteWasNoOp)) {\n          while (formattedValue[start] && clean(formattedValue[start]) === '') {\n            start += 1;\n          }\n        }\n        input.selectionStart = input.selectionEnd = start + (deleteWasNoOp ? 1 + charsToSkipAfterDelete : 0);\n      };\n    });\n  }\n  useEffect(() => {\n    // until https://developer.mozilla.org/en-US/docs/Web/API/InputEvent/inputType will be supported\n    // by all major browsers (now supported by: +chrome, +safari, ?edge, !firefox)\n    // there is no way I found to distinguish in onChange\n    // backspace or delete was called in some situations\n    // firefox track https://bugzilla.mozilla.org/show_bug.cgi?id=1447239\n    const handleKeyDown = evt => {\n      if (evt.code === 'Delete') {\n        isDeleleteButtonDownRef.current = true;\n      }\n    };\n    const handleKeyUp = evt => {\n      if (evt.code === 'Delete') {\n        isDeleleteButtonDownRef.current = false;\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n    document.addEventListener('keyup', handleKeyUp);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n      document.removeEventListener('keyup', handleKeyUp);\n    };\n  }, []);\n  return {\n    value: valueRef.current != null ? valueRef.current[0] : userValue,\n    onChange\n  };\n};\nconst Rifm = props => {\n  const renderProps = useRifm(props);\n  return props.children(renderProps);\n};\nexport { Rifm, useRifm };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}