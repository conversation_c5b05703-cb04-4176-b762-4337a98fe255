{"ast": null, "code": "import { AsapQueue } from './AsapQueue.js';\nimport { TaskFactory } from './TaskFactory.js';\nconst asapQueue = new AsapQueue();\nconst taskFactory = new TaskFactory(asapQueue.registerPendingError);\n/**\n * Calls a task as soon as possible after returning, in its own event, with priority\n * over other events like animation, reflow, and repaint. An error thrown from an\n * event will not interrupt, nor even substantially slow down the processing of\n * other events, but will be rather postponed to a lower priority event.\n * @param {{call}} task A callable object, typically a function that takes no\n * arguments.\n */\nexport function asap(task) {\n  asapQueue.enqueueTask(taskFactory.create(task));\n}\n\n//# sourceMappingURL=asap.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}