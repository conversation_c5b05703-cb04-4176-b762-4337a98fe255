{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/monthly-effort/MonthlyEffortMemberProjectSearch.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from 'react';\nimport { FormattedMessage } from 'react-intl';\n\n// material-ui\nimport ErrorIcon from '@mui/icons-material/Error';\nimport { Grid, Typography } from '@mui/material';\n\n// project imports\nimport { Button } from 'components';\nimport { Label } from 'components/extended/Form';\nimport { monthlyEffortConfig, monthlyEffortDepartmentProjectSchema } from 'pages/monthly-effort/Config';\nimport { Months, Project, SearchForm, Years } from '../search';\nimport { convertMonthFromToDate } from 'utils/date';\nimport { TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';\nimport ColorNoteTooltip from 'components/ColorNoteTooltip';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MonthlyEffortMemberProjectSearch = props => {\n  _s();\n  const {\n    formReset,\n    months,\n    handleChangeYear,\n    handleSearch\n  } = props;\n  const monthOnload = months.find(item => item.value === formReset.month);\n  const [month, setMonth] = useState({\n    fromDate: '',\n    toDate: ''\n  });\n  const {\n    effortbymember\n  } = TEXT_CONFIG_SCREEN.monthlyEffort;\n  const handleMonthChange = value => {\n    const getMonth = months.find(month => month.value === value);\n    if (getMonth) {\n      setMonth(convertMonthFromToDate(getMonth.label));\n    }\n  };\n  useEffect(() => {\n    if (monthOnload) {\n      setMonth(convertMonthFromToDate(monthOnload.label));\n    }\n  }, [monthOnload]);\n  return /*#__PURE__*/_jsxDEV(SearchForm, {\n    defaultValues: monthlyEffortConfig,\n    formSchema: monthlyEffortDepartmentProjectSchema,\n    handleSubmit: handleSearch,\n    formReset: formReset,\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      alignItems: \"center\",\n      spacing: 2,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 3,\n        children: /*#__PURE__*/_jsxDEV(Years, {\n          handleChangeYear: handleChangeYear,\n          label: effortbymember + 'year'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 3,\n        children: /*#__PURE__*/_jsxDEV(Months, {\n          months: months,\n          onChange: handleMonthChange,\n          isFilter: true,\n          label: effortbymember + 'month'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 3,\n        children: /*#__PURE__*/_jsxDEV(Project, {\n          isDefaultAll: true,\n          month: month,\n          isNotStatus: true,\n          label: /*#__PURE__*/_jsxDEV(Typography, {\n            display: \"flex\",\n            gap: 0.5,\n            children: [/*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: effortbymember + 'projects'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(ColorNoteTooltip, {\n              notes: TEXT_INPUT_COLOR_EFFORT_INCURRED,\n              children: /*#__PURE__*/_jsxDEV(ErrorIcon, {\n                sx: {\n                  fontSize: 15\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 3,\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          label: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          size: \"medium\",\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: effortbymember + 'search'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 35\n          }, this),\n          variant: \"contained\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 9\n  }, this);\n};\n_s(MonthlyEffortMemberProjectSearch, \"/WckuGu/y99YSn/VThIeN8DDnG8=\");\n_c = MonthlyEffortMemberProjectSearch;\nexport default MonthlyEffortMemberProjectSearch;\nvar _c;\n$RefreshReg$(_c, \"MonthlyEffortMemberProjectSearch\");", "map": {"version": 3, "names": ["useEffect", "useState", "FormattedMessage", "ErrorIcon", "Grid", "Typography", "<PERSON><PERSON>", "Label", "monthlyEffortConfig", "monthlyEffortDepartmentProjectSchema", "Months", "Project", "SearchForm", "Years", "convertMonthFromToDate", "TEXT_CONFIG_SCREEN", "TEXT_INPUT_COLOR_EFFORT_INCURRED", "ColorNoteTooltip", "jsxDEV", "_jsxDEV", "MonthlyEffortMemberProjectSearch", "props", "_s", "formReset", "months", "handleChangeYear", "handleSearch", "monthOnload", "find", "item", "value", "month", "setMonth", "fromDate", "toDate", "effortbymember", "monthlyEffort", "handleMonthChange", "getMonth", "label", "defaultValues", "formSchema", "handleSubmit", "children", "container", "alignItems", "spacing", "xs", "lg", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "isFilter", "isDefaultAll", "isNotStatus", "display", "gap", "id", "notes", "sx", "fontSize", "type", "size", "variant", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/monthly-effort/MonthlyEffortMemberProjectSearch.tsx"], "sourcesContent": ["import { useEffect, useState } from 'react';\nimport { FormattedMessage } from 'react-intl';\n\n// material-ui\nimport ErrorIcon from '@mui/icons-material/Error';\nimport { Grid, Typography } from '@mui/material';\n\n// project imports\nimport { Button } from 'components';\nimport { Label } from 'components/extended/Form';\nimport { IMonthlyEffortConfig, monthlyEffortConfig, monthlyEffortDepartmentProjectSchema } from 'pages/monthly-effort/Config';\nimport { IOption } from 'types';\nimport { Months, Project, SearchForm, Years } from '../search';\nimport { convertMonthFromToDate } from 'utils/date';\nimport { TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';\nimport ColorNoteTooltip from 'components/ColorNoteTooltip';\n\ninterface IMonthlyEffortMemberProjectSearchProps {\n    formReset: IMonthlyEffortConfig;\n    months: IOption[];\n    handleChangeYear: (e: any) => void;\n    handleSearch: (value: any) => void;\n}\n\nconst MonthlyEffortMemberProjectSearch = (props: IMonthlyEffortMemberProjectSearchProps) => {\n    const { formReset, months, handleChangeYear, handleSearch } = props;\n\n    const monthOnload = months.find((item) => item.value === formReset.month);\n\n    const [month, setMonth] = useState({ fromDate: '', toDate: '' });\n\n    const { effortbymember } = TEXT_CONFIG_SCREEN.monthlyEffort;\n\n    const handleMonthChange = (value: string) => {\n        const getMonth = months.find((month) => month.value === value);\n\n        if (getMonth) {\n            setMonth(convertMonthFromToDate(getMonth.label));\n        }\n    };\n    useEffect(() => {\n        if (monthOnload) {\n            setMonth(convertMonthFromToDate(monthOnload.label as string));\n        }\n    }, [monthOnload]);\n\n    return (\n        <SearchForm\n            defaultValues={monthlyEffortConfig}\n            formSchema={monthlyEffortDepartmentProjectSchema}\n            handleSubmit={handleSearch}\n            formReset={formReset}\n        >\n            <Grid container alignItems=\"center\" spacing={2}>\n                <Grid item xs={12} lg={3}>\n                    <Years handleChangeYear={handleChangeYear} label={effortbymember + 'year'} />\n                </Grid>\n                <Grid item xs={12} lg={3}>\n                    <Months months={months} onChange={handleMonthChange} isFilter label={effortbymember + 'month'} />\n                </Grid>\n                <Grid item xs={12} lg={3}>\n                    <Project\n                        isDefaultAll\n                        month={month}\n                        isNotStatus\n                        label={\n                            <Typography display=\"flex\" gap={0.5}>\n                                <FormattedMessage id={effortbymember + 'projects'} />\n                                <ColorNoteTooltip notes={TEXT_INPUT_COLOR_EFFORT_INCURRED}>\n                                    <ErrorIcon sx={{ fontSize: 15 }} />\n                                </ColorNoteTooltip>\n                            </Typography>\n                        }\n                    />\n                </Grid>\n                <Grid item xs={12} lg={3}>\n                    <Label label=\"&nbsp;\" />\n                    <Button\n                        type=\"submit\"\n                        size=\"medium\"\n                        children={<FormattedMessage id={effortbymember + 'search'} />}\n                        variant=\"contained\"\n                    />\n                </Grid>\n            </Grid>\n        </SearchForm>\n    );\n};\n\nexport default MonthlyEffortMemberProjectSearch;\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,OAAOC,SAAS,MAAM,2BAA2B;AACjD,SAASC,IAAI,EAAEC,UAAU,QAAQ,eAAe;;AAEhD;AACA,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAA+BC,mBAAmB,EAAEC,oCAAoC,QAAQ,6BAA6B;AAE7H,SAASC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAEC,KAAK,QAAQ,WAAW;AAC9D,SAASC,sBAAsB,QAAQ,YAAY;AACnD,SAASC,kBAAkB,EAAEC,gCAAgC,QAAQ,kBAAkB;AACvF,OAAOC,gBAAgB,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS3D,MAAMC,gCAAgC,GAAIC,KAA6C,IAAK;EAAAC,EAAA;EACxF,MAAM;IAAEC,SAAS;IAAEC,MAAM;IAAEC,gBAAgB;IAAEC;EAAa,CAAC,GAAGL,KAAK;EAEnE,MAAMM,WAAW,GAAGH,MAAM,CAACI,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACC,KAAK,KAAKP,SAAS,CAACQ,KAAK,CAAC;EAEzE,MAAM,CAACA,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC;IAAEgC,QAAQ,EAAE,EAAE;IAAEC,MAAM,EAAE;EAAG,CAAC,CAAC;EAEhE,MAAM;IAAEC;EAAe,CAAC,GAAGpB,kBAAkB,CAACqB,aAAa;EAE3D,MAAMC,iBAAiB,GAAIP,KAAa,IAAK;IACzC,MAAMQ,QAAQ,GAAGd,MAAM,CAACI,IAAI,CAAEG,KAAK,IAAKA,KAAK,CAACD,KAAK,KAAKA,KAAK,CAAC;IAE9D,IAAIQ,QAAQ,EAAE;MACVN,QAAQ,CAAClB,sBAAsB,CAACwB,QAAQ,CAACC,KAAK,CAAC,CAAC;IACpD;EACJ,CAAC;EACDvC,SAAS,CAAC,MAAM;IACZ,IAAI2B,WAAW,EAAE;MACbK,QAAQ,CAAClB,sBAAsB,CAACa,WAAW,CAACY,KAAe,CAAC,CAAC;IACjE;EACJ,CAAC,EAAE,CAACZ,WAAW,CAAC,CAAC;EAEjB,oBACIR,OAAA,CAACP,UAAU;IACP4B,aAAa,EAAEhC,mBAAoB;IACnCiC,UAAU,EAAEhC,oCAAqC;IACjDiC,YAAY,EAAEhB,YAAa;IAC3BH,SAAS,EAAEA,SAAU;IAAAoB,QAAA,eAErBxB,OAAA,CAACf,IAAI;MAACwC,SAAS;MAACC,UAAU,EAAC,QAAQ;MAACC,OAAO,EAAE,CAAE;MAAAH,QAAA,gBAC3CxB,OAAA,CAACf,IAAI;QAACyB,IAAI;QAACkB,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACrBxB,OAAA,CAACN,KAAK;UAACY,gBAAgB,EAAEA,gBAAiB;UAACc,KAAK,EAAEJ,cAAc,GAAG;QAAO;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eACPjC,OAAA,CAACf,IAAI;QAACyB,IAAI;QAACkB,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACrBxB,OAAA,CAACT,MAAM;UAACc,MAAM,EAAEA,MAAO;UAAC6B,QAAQ,EAAEhB,iBAAkB;UAACiB,QAAQ;UAACf,KAAK,EAAEJ,cAAc,GAAG;QAAQ;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC,eACPjC,OAAA,CAACf,IAAI;QAACyB,IAAI;QAACkB,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACrBxB,OAAA,CAACR,OAAO;UACJ4C,YAAY;UACZxB,KAAK,EAAEA,KAAM;UACbyB,WAAW;UACXjB,KAAK,eACDpB,OAAA,CAACd,UAAU;YAACoD,OAAO,EAAC,MAAM;YAACC,GAAG,EAAE,GAAI;YAAAf,QAAA,gBAChCxB,OAAA,CAACjB,gBAAgB;cAACyD,EAAE,EAAExB,cAAc,GAAG;YAAW;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDjC,OAAA,CAACF,gBAAgB;cAAC2C,KAAK,EAAE5C,gCAAiC;cAAA2B,QAAA,eACtDxB,OAAA,CAAChB,SAAS;gBAAC0D,EAAE,EAAE;kBAAEC,QAAQ,EAAE;gBAAG;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QACf;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACPjC,OAAA,CAACf,IAAI;QAACyB,IAAI;QAACkB,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,gBACrBxB,OAAA,CAACZ,KAAK;UAACgC,KAAK,EAAC;QAAQ;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxBjC,OAAA,CAACb,MAAM;UACHyD,IAAI,EAAC,QAAQ;UACbC,IAAI,EAAC,QAAQ;UACbrB,QAAQ,eAAExB,OAAA,CAACjB,gBAAgB;YAACyD,EAAE,EAAExB,cAAc,GAAG;UAAS;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9Da,OAAO,EAAC;QAAW;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAErB,CAAC;AAAC9B,EAAA,CA/DIF,gCAAgC;AAAA8C,EAAA,GAAhC9C,gCAAgC;AAiEtC,eAAeA,gCAAgC;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}