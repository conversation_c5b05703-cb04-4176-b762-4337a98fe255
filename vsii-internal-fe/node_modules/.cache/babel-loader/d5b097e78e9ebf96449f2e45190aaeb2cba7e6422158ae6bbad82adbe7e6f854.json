{"ast": null, "code": "import axios from 'axios';\nimport { syncUrl } from 'index';\nconst axiosClient = axios.create({\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add a request interceptor\naxiosClient.interceptors.request.use(function (config) {\n  return {\n    ...config,\n    baseURL: syncUrl\n  };\n}, function (error) {\n  return Promise.reject(error);\n});\n\n// Add a response interceptor\naxiosClient.interceptors.response.use(function (response) {\n  return response.data;\n}, function (error) {\n  return Promise.reject(error);\n});\nexport default axiosClient;", "map": {"version": 3, "names": ["axios", "syncUrl", "axiosClient", "create", "headers", "interceptors", "request", "use", "config", "baseURL", "error", "Promise", "reject", "response", "data"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/services/others/axiosClient.ts"], "sourcesContent": ["import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';\n\nimport { syncUrl } from 'index';\n\nconst axiosClient = axios.create({\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n\n// Add a request interceptor\naxiosClient.interceptors.request.use(\n    function (config: AxiosRequestConfig) {\n        return { ...config, baseURL: syncUrl };\n    },\n    function (error) {\n        return Promise.reject(error);\n    }\n);\n\n// Add a response interceptor\naxiosClient.interceptors.response.use(\n    function (response: AxiosResponse) {\n        return response.data;\n    },\n    function (error) {\n        return Promise.reject(error);\n    }\n);\n\nexport default axiosClient;\n"], "mappings": "AAAA,OAAOA,KAAK,MAA6C,OAAO;AAEhE,SAASC,OAAO,QAAQ,OAAO;AAE/B,MAAMC,WAAW,GAAGF,KAAK,CAACG,MAAM,CAAC;EAC7BC,OAAO,EAAE;IACL,cAAc,EAAE;EACpB;AACJ,CAAC,CAAC;;AAEF;AACAF,WAAW,CAACG,YAAY,CAACC,OAAO,CAACC,GAAG,CAChC,UAAUC,MAA0B,EAAE;EAClC,OAAO;IAAE,GAAGA,MAAM;IAAEC,OAAO,EAAER;EAAQ,CAAC;AAC1C,CAAC,EACD,UAAUS,KAAK,EAAE;EACb,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CACJ,CAAC;;AAED;AACAR,WAAW,CAACG,YAAY,CAACQ,QAAQ,CAACN,GAAG,CACjC,UAAUM,QAAuB,EAAE;EAC/B,OAAOA,QAAQ,CAACC,IAAI;AACxB,CAAC,EACD,UAAUJ,KAAK,EAAE;EACb,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CACJ,CAAC;AAED,eAAeR,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}