{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"actions\", \"anchorEl\", \"children\", \"component\", \"components\", \"componentsProps\", \"keepMounted\", \"listboxId\", \"onClose\", \"open\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { HTMLElementType, refType } from '@mui/utils';\nimport MenuUnstyledContext from './MenuUnstyledContext';\nimport { getMenuUnstyledUtilityClass } from './menuUnstyledClasses';\nimport useMenu from './useMenu';\nimport composeClasses from '../composeClasses';\nimport PopperUnstyled from '../PopperUnstyled';\nimport useSlotProps from '../utils/useSlotProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction getUtilityClasses(ownerState) {\n  const {\n    open\n  } = ownerState;\n  const slots = {\n    root: ['root', open && 'expanded'],\n    listbox: ['listbox', open && 'expanded']\n  };\n  return composeClasses(slots, getMenuUnstyledUtilityClass, {});\n}\n/**\n *\n * Demos:\n *\n * - [Unstyled menu](https://mui.com/base/react-menu/)\n *\n * API:\n *\n * - [MenuUnstyled API](https://mui.com/base/api/menu-unstyled/)\n */\n\nconst MenuUnstyled = /*#__PURE__*/React.forwardRef(function MenuUnstyled(props, forwardedRef) {\n  var _ref, _components$Listbox;\n  const {\n      actions,\n      anchorEl,\n      children,\n      component,\n      components = {},\n      componentsProps = {},\n      keepMounted = false,\n      listboxId,\n      onClose,\n      open = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    registerItem,\n    unregisterItem,\n    getListboxProps,\n    getItemProps,\n    getItemState,\n    highlightFirstItem,\n    highlightLastItem\n  } = useMenu({\n    open,\n    onClose,\n    listboxId\n  });\n  React.useImperativeHandle(actions, () => ({\n    highlightFirstItem,\n    highlightLastItem\n  }), [highlightFirstItem, highlightLastItem]);\n  const ownerState = _extends({}, props, {\n    open\n  });\n  const classes = getUtilityClasses(ownerState);\n  const Root = (_ref = component != null ? component : components.Root) != null ? _ref : PopperUnstyled;\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalForwardedProps: other,\n    externalSlotProps: componentsProps.root,\n    additionalProps: {\n      anchorEl,\n      open,\n      keepMounted,\n      role: undefined,\n      ref: forwardedRef\n    },\n    className: classes.root,\n    ownerState\n  });\n  const Listbox = (_components$Listbox = components.Listbox) != null ? _components$Listbox : 'ul';\n  const listboxProps = useSlotProps({\n    elementType: Listbox,\n    getSlotProps: getListboxProps,\n    externalSlotProps: componentsProps.listbox,\n    ownerState,\n    className: classes.listbox\n  });\n  const contextValue = {\n    registerItem,\n    unregisterItem,\n    getItemState,\n    getItemProps,\n    open\n  };\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: /*#__PURE__*/_jsx(Listbox, _extends({}, listboxProps, {\n      children: /*#__PURE__*/_jsx(MenuUnstyledContext.Provider, {\n        value: contextValue,\n        children: children\n      })\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? MenuUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * A ref with imperative actions.\n   * It allows to select the first or last menu item.\n   */\n  actions: refType,\n  /**\n   * An HTML element, [virtualElement](https://popper.js.org/docs/v2/virtual-elements/),\n   * or a function that returns either.\n   * It's used to set the position of the popper.\n   */\n  anchorEl: PropTypes\n  /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.object, PropTypes.func]),\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the Menu.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Listbox: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The props used for each slot inside the Menu.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    listbox: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * Always keep the menu in the DOM.\n   * This prop can be useful in SEO situation or when you want to maximize the responsiveness of the Menu.\n   *\n   * @default false\n   */\n  keepMounted: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  listboxId: PropTypes.string,\n  /**\n   * Triggered when focus leaves the menu and the menu should close.\n   */\n  onClose: PropTypes.func,\n  /**\n   * Controls whether the menu is displayed.\n   * @default false\n   */\n  open: PropTypes.bool\n} : void 0;\nexport default MenuUnstyled;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}