{"ast": null, "code": "import{Fragment}from'react';// material-ui\nimport{TableBody,TableCell,TableRow}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ErrorImportTBody=props=>{const{errorMessages}=props;return/*#__PURE__*/_jsx(TableBody,{children:errorMessages.map((err,key)=>/*#__PURE__*/_jsxs(Fragment,{children:[/*#__PURE__*/_jsx(TableRow,{style:{backgroundColor:'rgb(240 240 240)'},children:/*#__PURE__*/_jsx(TableCell,{colSpan:4,children:err.name})}),err.errorList.map((subErr,subKey)=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{}),/*#__PURE__*/_jsx(TableCell,{children:subErr})]},subKey))]},key))});};export default ErrorImportTBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}