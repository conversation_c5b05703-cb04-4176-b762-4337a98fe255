{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/OnGoingSearch.tsx\";\n// material-ui\nimport { Grid } from '@mui/material';\n\n// project imports\nimport { Button } from 'components';\nimport { Label } from 'components/extended/Form';\nimport { ProductionPerformance, SalePipelineStatus, SalePipelineType, SearchForm, SalesYear } from 'containers/search';\nimport { onGoingConfig, onGoingSchema } from 'pages/sales/Config';\nimport { E_SCREEN_SALES_YEAR } from 'constants/Common';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OnGoingSearch = props => {\n  const {\n    formReset,\n    handleSearch,\n    handleChangeYear,\n    handleChangeSalePipelineType,\n    handleChangeStatus,\n    handleChangeProject\n  } = props;\n  const {\n    salesReport\n  } = TEXT_CONFIG_SCREEN;\n  return /*#__PURE__*/_jsxDEV(SearchForm, {\n    defaultValues: onGoingConfig,\n    formSchema: onGoingSchema,\n    handleSubmit: handleSearch,\n    formReset: formReset,\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      alignItems: \"center\",\n      spacing: 2,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2.4,\n        children: /*#__PURE__*/_jsxDEV(SalePipelineType, {\n          isShowAll: true,\n          handleChangeSalePipelineType: handleChangeSalePipelineType,\n          label: salesReport.salesOnGoing + '-sale-pipeline-type'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2.4,\n        children: /*#__PURE__*/_jsxDEV(SalesYear, {\n          handleChangeYear: handleChangeYear,\n          screen: E_SCREEN_SALES_YEAR.ON_GOING,\n          label: salesReport.salesOnGoing + '-year'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2.4,\n        children: /*#__PURE__*/_jsxDEV(ProductionPerformance, {\n          isDefaultAll: false,\n          handleChange: handleChangeProject,\n          requestParams: formReset,\n          label: salesReport.salesOnGoing + '-project'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2.4,\n        children: /*#__PURE__*/_jsxDEV(SalePipelineStatus, {\n          isShowAll: true,\n          handleChangeStatus: handleChangeStatus,\n          label: salesReport.salesOnGoing + '-sale-pipeline-status'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2.4,\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          label: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          size: \"medium\",\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: salesReport.salesOnGoing + '-search'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 35\n          }, this),\n          variant: \"contained\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 9\n  }, this);\n};\n_c = OnGoingSearch;\nexport default OnGoingSearch;\nvar _c;\n$RefreshReg$(_c, \"OnGoingSearch\");", "map": {"version": 3, "names": ["Grid", "<PERSON><PERSON>", "Label", "ProductionPerformance", "SalePipelineStatus", "SalePipelineType", "SearchForm", "SalesYear", "onGoingConfig", "onGoingSchema", "E_SCREEN_SALES_YEAR", "FormattedMessage", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "OnGoingSearch", "props", "formReset", "handleSearch", "handleChangeYear", "handleChangeSalePipelineType", "handleChangeStatus", "handleChangeProject", "salesReport", "defaultValues", "formSchema", "handleSubmit", "children", "container", "alignItems", "spacing", "item", "xs", "lg", "isShowAll", "label", "salesOnGoing", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "screen", "ON_GOING", "isDefaultAll", "handleChange", "requestParams", "type", "size", "id", "variant", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/OnGoingSearch.tsx"], "sourcesContent": ["import { ChangeEvent } from 'react';\n\n// material-ui\nimport { Grid, SelectChangeEvent } from '@mui/material';\n\n// project imports\nimport { Button } from 'components';\nimport { Label } from 'components/extended/Form';\nimport { ProductionPerformance, SalePipelineStatus, SalePipelineType, SearchForm, SalesYear } from 'containers/search';\nimport { IOnGoingConfig, onGoingConfig, onGoingSchema } from 'pages/sales/Config';\nimport { E_SCREEN_SALES_YEAR } from 'constants/Common';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\n\ninterface OnGoingSearchProps {\n    formReset: IOnGoingConfig;\n    handleSearch: (value: any) => void;\n    handleChangeYear?: (year: ChangeEvent<HTMLInputElement>) => void;\n    handleChangeProject?: (data: any) => void;\n    handleChangeStatus?: (e: SelectChangeEvent<unknown>) => void;\n    handleChangeSalePipelineType?: (e: SelectChangeEvent<unknown>) => void;\n}\n\nconst OnGoingSearch = (props: OnGoingSearchProps) => {\n    const { formReset, handleSearch, handleChangeYear, handleChangeSalePipelineType, handleChangeStatus, handleChangeProject } = props;\n\n    const { salesReport } = TEXT_CONFIG_SCREEN;\n\n    return (\n        <SearchForm defaultValues={onGoingConfig} formSchema={onGoingSchema} handleSubmit={handleSearch} formReset={formReset}>\n            <Grid container alignItems=\"center\" spacing={2}>\n                <Grid item xs={12} lg={2.4}>\n                    <SalePipelineType\n                        isShowAll\n                        handleChangeSalePipelineType={handleChangeSalePipelineType}\n                        label={salesReport.salesOnGoing + '-sale-pipeline-type'}\n                    />\n                </Grid>\n                <Grid item xs={12} lg={2.4}>\n                    <SalesYear\n                        handleChangeYear={handleChangeYear}\n                        screen={E_SCREEN_SALES_YEAR.ON_GOING}\n                        label={salesReport.salesOnGoing + '-year'}\n                    />\n                </Grid>\n                <Grid item xs={12} lg={2.4}>\n                    <ProductionPerformance\n                        isDefaultAll={false}\n                        handleChange={handleChangeProject}\n                        requestParams={formReset}\n                        label={salesReport.salesOnGoing + '-project'}\n                    />\n                </Grid>\n                <Grid item xs={12} lg={2.4}>\n                    <SalePipelineStatus\n                        isShowAll\n                        handleChangeStatus={handleChangeStatus}\n                        label={salesReport.salesOnGoing + '-sale-pipeline-status'}\n                    />\n                </Grid>\n                <Grid item xs={12} lg={2.4}>\n                    <Label label=\"&nbsp;\" />\n                    <Button\n                        type=\"submit\"\n                        size=\"medium\"\n                        children={<FormattedMessage id={salesReport.salesOnGoing + '-search'} />}\n                        variant=\"contained\"\n                    />\n                </Grid>\n            </Grid>\n        </SearchForm>\n    );\n};\n\nexport default OnGoingSearch;\n"], "mappings": ";AAEA;AACA,SAASA,IAAI,QAA2B,eAAe;;AAEvD;AACA,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,qBAAqB,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,SAAS,QAAQ,mBAAmB;AACtH,SAAyBC,aAAa,EAAEC,aAAa,QAAQ,oBAAoB;AACjF,SAASC,mBAAmB,QAAQ,kBAAkB;;AAEtD;AACA,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWtD,MAAMC,aAAa,GAAIC,KAAyB,IAAK;EACjD,MAAM;IAAEC,SAAS;IAAEC,YAAY;IAAEC,gBAAgB;IAAEC,4BAA4B;IAAEC,kBAAkB;IAAEC;EAAoB,CAAC,GAAGN,KAAK;EAElI,MAAM;IAAEO;EAAY,CAAC,GAAGX,kBAAkB;EAE1C,oBACIE,OAAA,CAACR,UAAU;IAACkB,aAAa,EAAEhB,aAAc;IAACiB,UAAU,EAAEhB,aAAc;IAACiB,YAAY,EAAER,YAAa;IAACD,SAAS,EAAEA,SAAU;IAAAU,QAAA,eAClHb,OAAA,CAACd,IAAI;MAAC4B,SAAS;MAACC,UAAU,EAAC,QAAQ;MAACC,OAAO,EAAE,CAAE;MAAAH,QAAA,gBAC3Cb,OAAA,CAACd,IAAI;QAAC+B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,GAAI;QAAAN,QAAA,eACvBb,OAAA,CAACT,gBAAgB;UACb6B,SAAS;UACTd,4BAA4B,EAAEA,4BAA6B;UAC3De,KAAK,EAAEZ,WAAW,CAACa,YAAY,GAAG;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACP1B,OAAA,CAACd,IAAI;QAAC+B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,GAAI;QAAAN,QAAA,eACvBb,OAAA,CAACP,SAAS;UACNY,gBAAgB,EAAEA,gBAAiB;UACnCsB,MAAM,EAAE/B,mBAAmB,CAACgC,QAAS;UACrCP,KAAK,EAAEZ,WAAW,CAACa,YAAY,GAAG;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACP1B,OAAA,CAACd,IAAI;QAAC+B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,GAAI;QAAAN,QAAA,eACvBb,OAAA,CAACX,qBAAqB;UAClBwC,YAAY,EAAE,KAAM;UACpBC,YAAY,EAAEtB,mBAAoB;UAClCuB,aAAa,EAAE5B,SAAU;UACzBkB,KAAK,EAAEZ,WAAW,CAACa,YAAY,GAAG;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACP1B,OAAA,CAACd,IAAI;QAAC+B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,GAAI;QAAAN,QAAA,eACvBb,OAAA,CAACV,kBAAkB;UACf8B,SAAS;UACTb,kBAAkB,EAAEA,kBAAmB;UACvCc,KAAK,EAAEZ,WAAW,CAACa,YAAY,GAAG;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACP1B,OAAA,CAACd,IAAI;QAAC+B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,GAAI;QAAAN,QAAA,gBACvBb,OAAA,CAACZ,KAAK;UAACiC,KAAK,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxB1B,OAAA,CAACb,MAAM;UACH6C,IAAI,EAAC,QAAQ;UACbC,IAAI,EAAC,QAAQ;UACbpB,QAAQ,eAAEb,OAAA,CAACH,gBAAgB;YAACqC,EAAE,EAAEzB,WAAW,CAACa,YAAY,GAAG;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzES,OAAO,EAAC;QAAW;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAErB,CAAC;AAACU,EAAA,GAjDInC,aAAa;AAmDnB,eAAeA,aAAa;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}