{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/Username.tsx\";\nimport { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { Input } from 'components/extended/Form';\nimport { searchFormConfig } from './Config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Username = ({\n  label\n}) => {\n  return /*#__PURE__*/_jsxDEV(Input, {\n    name: searchFormConfig.userName.name,\n    label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n      id: label || searchFormConfig.userName.label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 64\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 12\n  }, this);\n};\n_c = Username;\nexport default Username;\nvar _c;\n$RefreshReg$(_c, \"Username\");", "map": {"version": 3, "names": ["FormattedMessage", "Input", "searchFormConfig", "jsxDEV", "_jsxDEV", "Username", "label", "name", "userName", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/Username.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { Input } from 'components/extended/Form';\nimport { searchFormConfig } from './Config';\ninterface IUsernameProps {\n    label?: string;\n}\nconst Username = ({ label }: IUsernameProps) => {\n    return <Input name={searchFormConfig.userName.name} label={<FormattedMessage id={label || searchFormConfig.userName.label} />} />;\n};\n\nexport default Username;\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,gBAAgB,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAI5C,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAsB,CAAC,KAAK;EAC5C,oBAAOF,OAAA,CAACH,KAAK;IAACM,IAAI,EAAEL,gBAAgB,CAACM,QAAQ,CAACD,IAAK;IAACD,KAAK,eAAEF,OAAA,CAACJ,gBAAgB;MAACS,EAAE,EAAEH,KAAK,IAAIJ,gBAAgB,CAACM,QAAQ,CAACF;IAAM;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACrI,CAAC;AAACC,EAAA,GAFIT,QAAQ;AAId,eAAeA,QAAQ;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}