{"ast": null, "code": "import * as React from 'react';\nimport { unstable_useControlled as useControlled, unstable_useId as useId } from '@mui/utils';\nconst useTabs = parameters => {\n  const {\n    value: valueProp,\n    defaultValue,\n    onChange,\n    orientation,\n    direction,\n    selectionFollowsFocus\n  } = parameters;\n  const [value, setValue] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'Tabs',\n    state: 'value'\n  });\n  const idPrefix = useId();\n  const onSelected = React.useCallback((e, newValue) => {\n    setValue(newValue);\n    if (onChange) {\n      onChange(e, newValue);\n    }\n  }, [onChange, setValue]);\n  const tabsContextValue = React.useMemo(() => {\n    return {\n      idPrefix,\n      value,\n      onSelected,\n      orientation,\n      direction,\n      selectionFollowsFocus\n    };\n  }, [idPrefix, value, onSelected, orientation, direction, selectionFollowsFocus]);\n  return {\n    tabsContextValue\n  };\n};\nexport default useTabs;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}