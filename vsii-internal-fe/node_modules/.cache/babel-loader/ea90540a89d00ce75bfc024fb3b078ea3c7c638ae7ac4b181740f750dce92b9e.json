{"ast": null, "code": "'use client';\n\nimport useLazyRef from '../useLazyRef/useLazyRef';\nimport useOnMount from '../useOnMount/useOnMount';\nexport class Timeout {\n  constructor() {\n    this.currentId = null;\n    this.clear = () => {\n      if (this.currentId !== null) {\n        clearTimeout(this.currentId);\n        this.currentId = null;\n      }\n    };\n    this.disposeEffect = () => {\n      return this.clear;\n    };\n  }\n  static create() {\n    return new Timeout();\n  }\n  /**\n   * Executes `fn` after `delay`, clearing any previously scheduled call.\n   */\n  start(delay, fn) {\n    this.clear();\n    this.currentId = setTimeout(() => {\n      this.currentId = null;\n      fn();\n    }, delay);\n  }\n}\nexport default function useTimeout() {\n  const timeout = useLazyRef(Timeout.create).current;\n  useOnMount(timeout.disposeEffect);\n  return timeout;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}