{"ast": null, "code": "import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';\nimport sendRequest from 'services/ApiService';\nimport Api from 'constants/Api';\n\n// interface\n\n// initial state\nconst initialState = {\n  loading: {},\n  warningNBMByMember: []\n};\nexport const getNBMByMember = createAsyncThunk(Api.non_billable_monitoring.getAll.url, async params => {\n  const response = await sendRequest(Api.non_billable_monitoring.getAll, params);\n  return response;\n});\nexport const getWarningNBMBytMember = createAsyncThunk(Api.non_billable_monitoring.getWarningNonbillable.url, async params => {\n  const response = await sendRequest(Api.non_billable_monitoring.getWarningNonbillable, params);\n  return response;\n});\nconst nonBillableMonitoringSlice = createSlice({\n  name: 'non-billable-monitoring',\n  initialState: initialState,\n  reducers: {},\n  extraReducers: builder => {\n    // getNBMByMember\n    builder.addCase(getNBMByMember.pending, state => {\n      state.NBMByMember = undefined;\n      state.loading[getNBMByMember.typePrefix] = true;\n    });\n    builder.addCase(getNBMByMember.fulfilled, (state, action) => {\n      state.loading[getNBMByMember.typePrefix] = false;\n      if (action.payload.status) {\n        state.NBMByMember = action.payload.result.content;\n      }\n    });\n    builder.addCase(getNBMByMember.rejected, state => {\n      state.loading[getNBMByMember.typePrefix] = false;\n    });\n\n    // getWarningNBMBytMember\n    builder.addCase(getWarningNBMBytMember.pending, state => {\n      state.warningNBMByMember = [];\n      state.loading[getWarningNBMBytMember.typePrefix] = true;\n    });\n    builder.addCase(getWarningNBMBytMember.fulfilled, (state, action) => {\n      state.loading[getWarningNBMBytMember.typePrefix] = false;\n      if (action.payload.status) {\n        state.warningNBMByMember = action.payload.result.content;\n      }\n    });\n    builder.addCase(getWarningNBMBytMember.rejected, state => {\n      state.loading[getWarningNBMBytMember.typePrefix] = false;\n    });\n  }\n});\n\n// export const {} = nonBillableMonitoringSlice.actions;\n\n// selectors\nexport const nonBillableMonitoringSelector = state => state.nonBillableMonitoring;\n\n// reducers\nexport default nonBillableMonitoringSlice.reducer;", "map": {"version": 3, "names": ["createAsyncThunk", "createSlice", "sendRequest", "Api", "initialState", "loading", "warningNBMByMember", "getNBMByMember", "non_billable_monitoring", "getAll", "url", "params", "response", "getWarningNBMBytMember", "getWarningNonbillable", "nonBillableMonitoringSlice", "name", "reducers", "extraReducers", "builder", "addCase", "pending", "state", "NBMByMember", "undefined", "typePrefix", "fulfilled", "action", "payload", "status", "result", "content", "rejected", "nonBillableMonitoringSelector", "nonBillableMonitoring", "reducer"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/store/slice/nonBillableMonitoringSlice.ts"], "sourcesContent": ["import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';\n\nimport { GetNonBillByMemberRequest, INonBillableMonitoringResponse, IResponseList, IWarningNonbillMemberResponse } from 'types';\nimport sendRequest from 'services/ApiService';\nimport { RootState } from 'app/store';\nimport Api from 'constants/Api';\n\n// interface\ninterface INonBillableMonitoringState {\n    loading: { [key: string]: boolean };\n    NBMByMember?: IResponseList<INonBillableMonitoringResponse>['result']['content'];\n    warningNBMByMember: IResponseList<IWarningNonbillMemberResponse>['result']['content'];\n}\n\n// initial state\nconst initialState: INonBillableMonitoringState = {\n    loading: {},\n    warningNBMByMember: []\n};\n\nexport const getNBMByMember = createAsyncThunk<IResponseList<INonBillableMonitoringResponse>, GetNonBillByMemberRequest>(\n    Api.non_billable_monitoring.getAll.url,\n    async (params) => {\n        const response = await sendRequest(Api.non_billable_monitoring.getAll, params);\n\n        return response;\n    }\n);\n\nexport const getWarningNBMBytMember = createAsyncThunk<IResponseList<IWarningNonbillMemberResponse>, GetNonBillByMemberRequest>(\n    Api.non_billable_monitoring.getWarningNonbillable.url,\n    async (params) => {\n        const response = await sendRequest(Api.non_billable_monitoring.getWarningNonbillable, params);\n\n        return response;\n    }\n);\n\nconst nonBillableMonitoringSlice = createSlice({\n    name: 'non-billable-monitoring',\n    initialState: initialState,\n    reducers: {},\n    extraReducers: (builder) => {\n        // getNBMByMember\n        builder.addCase(getNBMByMember.pending, (state) => {\n            state.NBMByMember = undefined;\n            state.loading[getNBMByMember.typePrefix] = true;\n        });\n        builder.addCase(getNBMByMember.fulfilled, (state, action) => {\n            state.loading[getNBMByMember.typePrefix] = false;\n            if (action.payload.status) {\n                state.NBMByMember = action.payload.result.content;\n            }\n        });\n        builder.addCase(getNBMByMember.rejected, (state) => {\n            state.loading[getNBMByMember.typePrefix] = false;\n        });\n\n        // getWarningNBMBytMember\n        builder.addCase(getWarningNBMBytMember.pending, (state) => {\n            state.warningNBMByMember = [];\n            state.loading[getWarningNBMBytMember.typePrefix] = true;\n        });\n        builder.addCase(getWarningNBMBytMember.fulfilled, (state, action) => {\n            state.loading[getWarningNBMBytMember.typePrefix] = false;\n            if (action.payload.status) {\n                state.warningNBMByMember = action.payload.result.content;\n            }\n        });\n        builder.addCase(getWarningNBMBytMember.rejected, (state) => {\n            state.loading[getWarningNBMBytMember.typePrefix] = false;\n        });\n    }\n});\n\n// export const {} = nonBillableMonitoringSlice.actions;\n\n// selectors\nexport const nonBillableMonitoringSelector = (state: RootState) => state.nonBillableMonitoring;\n\n// reducers\nexport default nonBillableMonitoringSlice.reducer;\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,WAAW,QAAQ,kBAAkB;AAGhE,OAAOC,WAAW,MAAM,qBAAqB;AAE7C,OAAOC,GAAG,MAAM,eAAe;;AAE/B;;AAOA;AACA,MAAMC,YAAyC,GAAG;EAC9CC,OAAO,EAAE,CAAC,CAAC;EACXC,kBAAkB,EAAE;AACxB,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGP,gBAAgB,CAC1CG,GAAG,CAACK,uBAAuB,CAACC,MAAM,CAACC,GAAG,EACtC,MAAOC,MAAM,IAAK;EACd,MAAMC,QAAQ,GAAG,MAAMV,WAAW,CAACC,GAAG,CAACK,uBAAuB,CAACC,MAAM,EAAEE,MAAM,CAAC;EAE9E,OAAOC,QAAQ;AACnB,CACJ,CAAC;AAED,OAAO,MAAMC,sBAAsB,GAAGb,gBAAgB,CAClDG,GAAG,CAACK,uBAAuB,CAACM,qBAAqB,CAACJ,GAAG,EACrD,MAAOC,MAAM,IAAK;EACd,MAAMC,QAAQ,GAAG,MAAMV,WAAW,CAACC,GAAG,CAACK,uBAAuB,CAACM,qBAAqB,EAAEH,MAAM,CAAC;EAE7F,OAAOC,QAAQ;AACnB,CACJ,CAAC;AAED,MAAMG,0BAA0B,GAAGd,WAAW,CAAC;EAC3Ce,IAAI,EAAE,yBAAyB;EAC/BZ,YAAY,EAAEA,YAAY;EAC1Ba,QAAQ,EAAE,CAAC,CAAC;EACZC,aAAa,EAAGC,OAAO,IAAK;IACxB;IACAA,OAAO,CAACC,OAAO,CAACb,cAAc,CAACc,OAAO,EAAGC,KAAK,IAAK;MAC/CA,KAAK,CAACC,WAAW,GAAGC,SAAS;MAC7BF,KAAK,CAACjB,OAAO,CAACE,cAAc,CAACkB,UAAU,CAAC,GAAG,IAAI;IACnD,CAAC,CAAC;IACFN,OAAO,CAACC,OAAO,CAACb,cAAc,CAACmB,SAAS,EAAE,CAACJ,KAAK,EAAEK,MAAM,KAAK;MACzDL,KAAK,CAACjB,OAAO,CAACE,cAAc,CAACkB,UAAU,CAAC,GAAG,KAAK;MAChD,IAAIE,MAAM,CAACC,OAAO,CAACC,MAAM,EAAE;QACvBP,KAAK,CAACC,WAAW,GAAGI,MAAM,CAACC,OAAO,CAACE,MAAM,CAACC,OAAO;MACrD;IACJ,CAAC,CAAC;IACFZ,OAAO,CAACC,OAAO,CAACb,cAAc,CAACyB,QAAQ,EAAGV,KAAK,IAAK;MAChDA,KAAK,CAACjB,OAAO,CAACE,cAAc,CAACkB,UAAU,CAAC,GAAG,KAAK;IACpD,CAAC,CAAC;;IAEF;IACAN,OAAO,CAACC,OAAO,CAACP,sBAAsB,CAACQ,OAAO,EAAGC,KAAK,IAAK;MACvDA,KAAK,CAAChB,kBAAkB,GAAG,EAAE;MAC7BgB,KAAK,CAACjB,OAAO,CAACQ,sBAAsB,CAACY,UAAU,CAAC,GAAG,IAAI;IAC3D,CAAC,CAAC;IACFN,OAAO,CAACC,OAAO,CAACP,sBAAsB,CAACa,SAAS,EAAE,CAACJ,KAAK,EAAEK,MAAM,KAAK;MACjEL,KAAK,CAACjB,OAAO,CAACQ,sBAAsB,CAACY,UAAU,CAAC,GAAG,KAAK;MACxD,IAAIE,MAAM,CAACC,OAAO,CAACC,MAAM,EAAE;QACvBP,KAAK,CAAChB,kBAAkB,GAAGqB,MAAM,CAACC,OAAO,CAACE,MAAM,CAACC,OAAO;MAC5D;IACJ,CAAC,CAAC;IACFZ,OAAO,CAACC,OAAO,CAACP,sBAAsB,CAACmB,QAAQ,EAAGV,KAAK,IAAK;MACxDA,KAAK,CAACjB,OAAO,CAACQ,sBAAsB,CAACY,UAAU,CAAC,GAAG,KAAK;IAC5D,CAAC,CAAC;EACN;AACJ,CAAC,CAAC;;AAEF;;AAEA;AACA,OAAO,MAAMQ,6BAA6B,GAAIX,KAAgB,IAAKA,KAAK,CAACY,qBAAqB;;AAE9F;AACA,eAAenB,0BAA0B,CAACoB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}