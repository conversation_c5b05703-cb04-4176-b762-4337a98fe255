{"ast": null, "code": "/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function () {\n    'use strict';\n\n    /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n    if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart === 'function') {\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n    }\n    var React = require('react');\n    var shim = require('use-sync-external-store/shim');\n\n    /**\n     * inlined Object.is polyfill to avoid requiring consumers ship their own\n     * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n     */\n    function is(x, y) {\n      return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n      ;\n    }\n    var objectIs = typeof Object.is === 'function' ? Object.is : is;\n    var useSyncExternalStore = shim.useSyncExternalStore;\n\n    // for CommonJS interop.\n\n    var useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue; // Same as useSyncExternalStore, but supports selector and isEqual arguments.\n\n    function useSyncExternalStoreWithSelector(subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {\n      // Use this to track the rendered snapshot.\n      var instRef = useRef(null);\n      var inst;\n      if (instRef.current === null) {\n        inst = {\n          hasValue: false,\n          value: null\n        };\n        instRef.current = inst;\n      } else {\n        inst = instRef.current;\n      }\n      var _useMemo = useMemo(function () {\n          // Track the memoized state using closure variables that are local to this\n          // memoized instance of a getSnapshot function. Intentionally not using a\n          // useRef hook, because that state would be shared across all concurrent\n          // copies of the hook/component.\n          var hasMemo = false;\n          var memoizedSnapshot;\n          var memoizedSelection;\n          var memoizedSelector = function (nextSnapshot) {\n            if (!hasMemo) {\n              // The first time the hook is called, there is no memoized result.\n              hasMemo = true;\n              memoizedSnapshot = nextSnapshot;\n              var _nextSelection = selector(nextSnapshot);\n              if (isEqual !== undefined) {\n                // Even if the selector has changed, the currently rendered selection\n                // may be equal to the new selection. We should attempt to reuse the\n                // current value if possible, to preserve downstream memoizations.\n                if (inst.hasValue) {\n                  var currentSelection = inst.value;\n                  if (isEqual(currentSelection, _nextSelection)) {\n                    memoizedSelection = currentSelection;\n                    return currentSelection;\n                  }\n                }\n              }\n              memoizedSelection = _nextSelection;\n              return _nextSelection;\n            } // We may be able to reuse the previous invocation's result.\n\n            // We may be able to reuse the previous invocation's result.\n            var prevSnapshot = memoizedSnapshot;\n            var prevSelection = memoizedSelection;\n            if (objectIs(prevSnapshot, nextSnapshot)) {\n              // The snapshot is the same as last time. Reuse the previous selection.\n              return prevSelection;\n            } // The snapshot has changed, so we need to compute a new selection.\n\n            // The snapshot has changed, so we need to compute a new selection.\n            var nextSelection = selector(nextSnapshot); // If a custom isEqual function is provided, use that to check if the data\n            // has changed. If it hasn't, return the previous selection. That signals\n            // to React that the selections are conceptually equal, and we can bail\n            // out of rendering.\n\n            // If a custom isEqual function is provided, use that to check if the data\n            // has changed. If it hasn't, return the previous selection. That signals\n            // to React that the selections are conceptually equal, and we can bail\n            // out of rendering.\n            if (isEqual !== undefined && isEqual(prevSelection, nextSelection)) {\n              return prevSelection;\n            }\n            memoizedSnapshot = nextSnapshot;\n            memoizedSelection = nextSelection;\n            return nextSelection;\n          }; // Assigning this to a constant so that Flow knows it can't change.\n\n          // Assigning this to a constant so that Flow knows it can't change.\n          var maybeGetServerSnapshot = getServerSnapshot === undefined ? null : getServerSnapshot;\n          var getSnapshotWithSelector = function () {\n            return memoizedSelector(getSnapshot());\n          };\n          var getServerSnapshotWithSelector = maybeGetServerSnapshot === null ? undefined : function () {\n            return memoizedSelector(maybeGetServerSnapshot());\n          };\n          return [getSnapshotWithSelector, getServerSnapshotWithSelector];\n        }, [getSnapshot, getServerSnapshot, selector, isEqual]),\n        getSelection = _useMemo[0],\n        getServerSelection = _useMemo[1];\n      var value = useSyncExternalStore(subscribe, getSelection, getServerSelection);\n      useEffect(function () {\n        inst.hasValue = true;\n        inst.value = value;\n      }, [value]);\n      useDebugValue(value);\n      return value;\n    }\n    exports.useSyncExternalStoreWithSelector = useSyncExternalStoreWithSelector;\n    /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n    if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop === 'function') {\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n    }\n  })();\n}", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}