{"ast": null, "code": "import { onNextFrame, defaultTimestep } from './on-next-frame.mjs';\nimport { createRenderStep } from './create-render-step.mjs';\nimport { frameData } from './data.mjs';\nconst maxElapsed = 40;\nlet useDefaultElapsed = true;\nlet runNextFrame = false;\nlet isProcessing = false;\nconst stepsOrder = [\"read\", \"update\", \"preRender\", \"render\", \"postRender\"];\nconst steps = stepsOrder.reduce((acc, key) => {\n  acc[key] = createRenderStep(() => runNextFrame = true);\n  return acc;\n}, {});\nconst sync = stepsOrder.reduce((acc, key) => {\n  const step = steps[key];\n  acc[key] = (process, keepAlive = false, immediate = false) => {\n    if (!runNextFrame) startLoop();\n    return step.schedule(process, keepAlive, immediate);\n  };\n  return acc;\n}, {});\nconst cancelSync = stepsOrder.reduce((acc, key) => {\n  acc[key] = steps[key].cancel;\n  return acc;\n}, {});\nconst flushSync = stepsOrder.reduce((acc, key) => {\n  acc[key] = () => steps[key].process(frameData);\n  return acc;\n}, {});\nconst processStep = stepId => steps[stepId].process(frameData);\nconst processFrame = timestamp => {\n  runNextFrame = false;\n  frameData.delta = useDefaultElapsed ? defaultTimestep : Math.max(Math.min(timestamp - frameData.timestamp, maxElapsed), 1);\n  frameData.timestamp = timestamp;\n  isProcessing = true;\n  stepsOrder.forEach(processStep);\n  isProcessing = false;\n  if (runNextFrame) {\n    useDefaultElapsed = false;\n    onNextFrame(processFrame);\n  }\n};\nconst startLoop = () => {\n  runNextFrame = true;\n  useDefaultElapsed = true;\n  if (!isProcessing) onNextFrame(processFrame);\n};\nexport { cancelSync, flushSync, sync };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}