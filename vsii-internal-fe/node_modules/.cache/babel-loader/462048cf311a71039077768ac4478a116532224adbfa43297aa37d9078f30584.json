{"ast": null, "code": "import{I<PERSON><PERSON><PERSON><PERSON>,Stack,TableBody,TableCell,TableRow,Tooltip}from'@mui/material';import HighlightOffIcon from'@mui/icons-material/HighlightOff';import EditTwoToneIcon from'@mui/icons-material/EditTwoTone';import{FormattedMessage}from'react-intl';import{deleteProjectType,getSearchProjectType}from'store/slice/projectTypeSlice';import{closeConfirm,openConfirm}from'store/slice/confirmSlice';import{checkAllowedPermission}from'utils/authorization';import{openSnackbar}from'store/slice/snackbarSlice';import{Checkbox}from'components/extended/Form';import{PERMISSIONS}from'constants/Permission';import{DATE_FORMAT}from'constants/Common';import{useAppDispatch}from'app/hooks';import{dateFormat}from'utils/date';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ProjectTypeTBody=props=>{const{conditions,data,handleOpen}=props;const{projectTypeConfigPermission}=PERMISSIONS.admin;const dispatch=useAppDispatch();const handleDelete=prjType=>{dispatch(openConfirm({open:true,title:/*#__PURE__*/_jsx(FormattedMessage,{id:\"warning\"}),content:/*#__PURE__*/_jsx(FormattedMessage,{id:\"delete-record\"}),width:'400px',handleConfirm:async()=>{const resultAction=await dispatch(deleteProjectType(prjType.id));if(deleteProjectType.fulfilled.match(resultAction)){var _resultAction$payload;if((_resultAction$payload=resultAction.payload)!==null&&_resultAction$payload!==void 0&&_resultAction$payload.status){dispatch(openSnackbar({open:true,message:resultAction.payload.result.content,variant:'alert',alert:{color:'success'}}));await dispatch(getSearchProjectType(conditions));}else{var _resultAction$payload2;dispatch(openSnackbar({open:true,message:((_resultAction$payload2=resultAction.payload.result)===null||_resultAction$payload2===void 0?void 0:_resultAction$payload2.content)||'Error',variant:'alert',alert:{color:'error'}}));}}dispatch(closeConfirm());}}));};return/*#__PURE__*/_jsx(TableBody,{children:data===null||data===void 0?void 0:data.map((value,key)=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{align:\"center\",sx:{width:'5%'},children:conditions.size*(conditions.page-1)+key+1}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'15%'},children:value.typeCode}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'30%'},children:value.projectTypeName}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'15%'},children:dateFormat(value.lastUpdate,DATE_FORMAT.DDMMYYYY)}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'15%'},children:value.userUpdate}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'10%',textAlign:'center'},children:/*#__PURE__*/_jsx(Checkbox,{name:\"fixCost\",isControl:false,valueChecked:value.fixCost,disabled:true,sx:{mr:0}})}),checkAllowedPermission(projectTypeConfigPermission.edit)&&/*#__PURE__*/_jsx(TableCell,{sx:{width:'10%'},children:/*#__PURE__*/_jsxs(Stack,{direction:\"row\",justifyContent:\"center\",alignItems:\"center\",children:[/*#__PURE__*/_jsx(Tooltip,{placement:\"top\",title:/*#__PURE__*/_jsx(FormattedMessage,{id:'edit'}),onClick:()=>handleOpen(value),children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"edit\",size:\"small\",children:/*#__PURE__*/_jsx(EditTwoToneIcon,{sx:{fontSize:'1.1rem'}})})}),/*#__PURE__*/_jsx(Tooltip,{placement:\"top\",title:/*#__PURE__*/_jsx(FormattedMessage,{id:\"delete\"}),onClick:()=>handleDelete(value),children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"delete\",size:\"small\",children:/*#__PURE__*/_jsx(HighlightOffIcon,{sx:{fontSize:'1.1rem'}})})})]})})]},key))});};export default ProjectTypeTBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}