{"ast": null, "code": "export { default as appendOwnerState } from './appendOwnerState';\nexport { default as areArraysEqual } from './areArraysEqual';\nexport { default as extractEventHandlers } from './extractEventHandlers';\nexport { default as isHostComponent } from './isHostComponent';\nexport { default as resolveComponentProps } from './resolveComponentProps';\nexport { default as useSlotProps } from './useSlotProps';\nexport * from './types';", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}