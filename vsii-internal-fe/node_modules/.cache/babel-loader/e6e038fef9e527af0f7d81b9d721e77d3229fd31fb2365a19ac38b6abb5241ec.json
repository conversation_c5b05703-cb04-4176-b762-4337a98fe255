{"ast": null, "code": "// material-ui\nimport{useTheme}from'@mui/material/styles';import{Avatar,Box}from'@mui/material';// project imports\nimport LogoSection from'../LogoSection';// import SearchSection from './SearchSection';\nimport MobileSection from'./MobileSection';import ProfileSection from'./ProfileSection';import LocalizationSection from'./LocalizationSection';import ManualSyncSection from'./ManualSyncSection';import{useAppSelector,useAppDispatch}from'app/hooks';import{openDrawer}from'store/slice/menuSlice';// assets\nimport{IconMenu2}from'@tabler/icons';import{checkAllowedPermission}from'utils/authorization';import{PERMISSIONS}from'constants/Permission';// ==============================|| MAIN NAVBAR / HEADER ||============================== //\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Header=()=>{const theme=useTheme();const dispatch=useAppDispatch();const{drawerOpen}=useAppSelector(state=>state.menu);const{synchronize}=PERMISSIONS.syn;return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Box,{sx:{width:228,display:'flex',alignItems:'center',[theme.breakpoints.down('md')]:{width:'auto'}},children:[/*#__PURE__*/_jsx(Box,{component:\"span\",sx:{display:{xs:'none',md:'block'},flexGrow:1},children:/*#__PURE__*/_jsx(LogoSection,{})}),/*#__PURE__*/_jsx(Avatar,{variant:\"rounded\",sx:{...theme.typography.commonAvatar,...theme.typography.mediumAvatar,overflow:'hidden',transition:'all .2s ease-in-out',background:theme.palette.mode==='dark'?theme.palette.dark.main:theme.palette.secondary.light,color:theme.palette.mode==='dark'?theme.palette.secondary.main:theme.palette.secondary.dark,'&:hover':{background:theme.palette.mode==='dark'?theme.palette.secondary.main:theme.palette.secondary.dark,color:theme.palette.mode==='dark'?theme.palette.secondary.light:theme.palette.secondary.light}},onClick:()=>dispatch(openDrawer(!drawerOpen)),color:\"inherit\",children:/*#__PURE__*/_jsx(IconMenu2,{stroke:1.5,size:\"20px\"})})]}),/*#__PURE__*/_jsx(Box,{sx:{flexGrow:1}}),/*#__PURE__*/_jsx(Box,{sx:{flexGrow:1}}),checkAllowedPermission(synchronize)&&/*#__PURE__*/_jsx(ManualSyncSection,{}),/*#__PURE__*/_jsx(Box,{sx:{mr:3,[theme.breakpoints.down('md')]:{mr:2}},children:/*#__PURE__*/_jsx(LocalizationSection,{})}),/*#__PURE__*/_jsx(ProfileSection,{}),/*#__PURE__*/_jsx(Box,{sx:{display:{xs:'block',sm:'none'}},children:/*#__PURE__*/_jsx(MobileSection,{})})]});};export default Header;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}