{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/ErrorImportTBody.tsx\";\nimport { Fragment } from 'react';\n\n// material-ui\nimport { TableBody, TableCell, TableRow } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ErrorImportTBody = props => {\n  const {\n    errorMessages\n  } = props;\n  return /*#__PURE__*/_jsxDEV(TableBody, {\n    children: errorMessages.map((err, key) => /*#__PURE__*/_jsxDEV(Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(TableRow, {\n        style: {\n          backgroundColor: 'rgb(240 240 240)'\n        },\n        children: /*#__PURE__*/_jsxDEV(TableCell, {\n          colSpan: 4,\n          children: err.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 21\n      }, this), err.errorList.map((subErr, subKey) => /*#__PURE__*/_jsxDEV(TableRow, {\n        children: [/*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          children: subErr\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 29\n        }, this)]\n      }, subKey, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 25\n      }, this))]\n    }, key, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 17\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 9\n  }, this);\n};\n_c = ErrorImportTBody;\nexport default ErrorImportTBody;\nvar _c;\n$RefreshReg$(_c, \"ErrorImportTBody\");", "map": {"version": 3, "names": ["Fragment", "TableBody", "TableCell", "TableRow", "jsxDEV", "_jsxDEV", "ErrorImportTBody", "props", "errorMessages", "children", "map", "err", "key", "style", "backgroundColor", "colSpan", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "errorList", "subErr", "subKey", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/ErrorImportTBody.tsx"], "sourcesContent": ["import { Fragment } from 'react';\n\n// material-ui\nimport { TableBody, TableCell, TableRow } from '@mui/material';\n\nimport { IImportError } from './ManualSyncDialog';\n\ninterface ErrorImportTBodyProps {\n    errorMessages: IImportError[];\n}\n\nconst ErrorImportTBody = (props: ErrorImportTBodyProps) => {\n    const { errorMessages } = props;\n    return (\n        <TableBody>\n            {errorMessages.map((err: IImportError, key: number) => (\n                <Fragment key={key}>\n                    <TableRow style={{ backgroundColor: 'rgb(240 240 240)' }}>\n                        <TableCell colSpan={4}>{err.name}</TableCell>\n                    </TableRow>\n                    {err.errorList.map((subErr: string, subKey: number) => (\n                        <TableRow key={subKey}>\n                            <TableCell></TableCell>\n                            <TableCell>{subErr}</TableCell>\n                        </TableRow>\n                    ))}\n                </Fragment>\n            ))}\n        </TableBody>\n    );\n};\n\nexport default ErrorImportTBody;\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,OAAO;;AAEhC;AACA,SAASC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ/D,MAAMC,gBAAgB,GAAIC,KAA4B,IAAK;EACvD,MAAM;IAAEC;EAAc,CAAC,GAAGD,KAAK;EAC/B,oBACIF,OAAA,CAACJ,SAAS;IAAAQ,QAAA,EACLD,aAAa,CAACE,GAAG,CAAC,CAACC,GAAiB,EAAEC,GAAW,kBAC9CP,OAAA,CAACL,QAAQ;MAAAS,QAAA,gBACLJ,OAAA,CAACF,QAAQ;QAACU,KAAK,EAAE;UAAEC,eAAe,EAAE;QAAmB,CAAE;QAAAL,QAAA,eACrDJ,OAAA,CAACH,SAAS;UAACa,OAAO,EAAE,CAAE;UAAAN,QAAA,EAAEE,GAAG,CAACK;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,EACVT,GAAG,CAACU,SAAS,CAACX,GAAG,CAAC,CAACY,MAAc,EAAEC,MAAc,kBAC9ClB,OAAA,CAACF,QAAQ;QAAAM,QAAA,gBACLJ,OAAA,CAACH,SAAS;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACvBf,OAAA,CAACH,SAAS;UAAAO,QAAA,EAAEa;QAAM;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA,GAFpBG,MAAM;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGX,CACb,CAAC;IAAA,GATSR,GAAG;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAUR,CACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEpB,CAAC;AAACI,EAAA,GAnBIlB,gBAAgB;AAqBtB,eAAeA,gBAAgB;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}