{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/extended/AnimateButton.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\n// third-party\nimport { motion, useCycle } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// ==============================|| ANIMATION BUTTON ||============================== //\n\nconst AnimateButton = /*#__PURE__*/_s(/*#__PURE__*/React.forwardRef(_c = _s(({\n  children,\n  type,\n  direction,\n  offset,\n  scale\n}, ref) => {\n  var _scale, _scale2;\n  _s();\n  let offset1;\n  let offset2;\n  switch (direction) {\n    case 'up':\n    case 'left':\n      offset1 = offset;\n      offset2 = 0;\n      break;\n    case 'right':\n    case 'down':\n    default:\n      offset1 = 0;\n      offset2 = offset;\n      break;\n  }\n  const [x, cycleX] = useCycle(offset1, offset2);\n  const [y, cycleY] = useCycle(offset1, offset2);\n  switch (type) {\n    case 'rotate':\n      return /*#__PURE__*/_jsxDEV(motion.div, {\n        ref: ref,\n        animate: {\n          rotate: 360\n        },\n        transition: {\n          repeat: Infinity,\n          repeatType: 'loop',\n          duration: 2,\n          repeatDelay: 0\n        },\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 17\n      }, this);\n    case 'slide':\n      if (direction === 'up' || direction === 'down') {\n        return /*#__PURE__*/_jsxDEV(motion.div, {\n          ref: ref,\n          animate: {\n            y: y !== undefined ? y : ''\n          },\n          onHoverEnd: () => cycleY(),\n          onHoverStart: () => cycleY(),\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 21\n        }, this);\n      }\n      return /*#__PURE__*/_jsxDEV(motion.div, {\n        ref: ref,\n        animate: {\n          x: x !== undefined ? x : ''\n        },\n        onHoverEnd: () => cycleX(),\n        onHoverStart: () => cycleX(),\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 17\n      }, this);\n    case 'scale':\n    default:\n      if (typeof scale === 'number') {\n        scale = {\n          hover: scale,\n          tap: scale\n        };\n      }\n      return /*#__PURE__*/_jsxDEV(motion.div, {\n        ref: ref,\n        whileHover: {\n          scale: (_scale = scale) === null || _scale === void 0 ? void 0 : _scale.hover\n        },\n        whileTap: {\n          scale: (_scale2 = scale) === null || _scale2 === void 0 ? void 0 : _scale2.tap\n        },\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 17\n      }, this);\n  }\n}, \"99A/Pg3SE7DRlTWvIuO6X3SrQwY=\", false, function () {\n  return [useCycle, useCycle];\n})), \"99A/Pg3SE7DRlTWvIuO6X3SrQwY=\", false, function () {\n  return [useCycle, useCycle];\n});\n_c2 = AnimateButton;\nAnimateButton.defaultProps = {\n  type: 'scale',\n  offset: 10,\n  direction: 'right',\n  scale: {\n    hover: 1,\n    tap: 0.9\n  }\n};\nexport default AnimateButton;\nvar _c, _c2;\n$RefreshReg$(_c, \"AnimateButton$React.forwardRef\");\n$RefreshReg$(_c2, \"AnimateButton\");", "map": {"version": 3, "names": ["React", "motion", "useCycle", "jsxDEV", "_jsxDEV", "AnimateButton", "_s", "forwardRef", "_c", "children", "type", "direction", "offset", "scale", "ref", "_scale", "_scale2", "offset1", "offset2", "x", "cycleX", "y", "cycleY", "div", "animate", "rotate", "transition", "repeat", "Infinity", "repeatType", "duration", "repeatDelay", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "undefined", "onHoverEnd", "onHoverStart", "hover", "tap", "whileHover", "whileTap", "_c2", "defaultProps", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/extended/AnimateButton.tsx"], "sourcesContent": ["import React, { Ref } from 'react';\n// third-party\nimport { motion, useCycle } from 'framer-motion';\n\ninterface ScaleProps {\n    hover: number | string | undefined;\n    tap: number | string | undefined;\n}\n\ninterface AnimateButtonProps {\n    children?: React.ReactNode;\n    type?: 'slide' | 'scale' | 'rotate';\n    direction?: 'up' | 'down' | 'left' | 'right';\n    offset?: number;\n    scale?: ScaleProps;\n}\n\n// ==============================|| ANIMATION BUTTON ||============================== //\n\nconst AnimateButton = React.forwardRef(({ children, type, direction, offset, scale }: AnimateButtonProps, ref: Ref<HTMLDivElement>) => {\n    let offset1;\n    let offset2;\n    switch (direction) {\n        case 'up':\n        case 'left':\n            offset1 = offset;\n            offset2 = 0;\n            break;\n        case 'right':\n        case 'down':\n        default:\n            offset1 = 0;\n            offset2 = offset;\n            break;\n    }\n\n    const [x, cycleX] = useCycle(offset1, offset2);\n    const [y, cycleY] = useCycle(offset1, offset2);\n\n    switch (type) {\n        case 'rotate':\n            return (\n                <motion.div\n                    ref={ref}\n                    animate={{ rotate: 360 }}\n                    transition={{\n                        repeat: Infinity,\n                        repeatType: 'loop',\n                        duration: 2,\n                        repeatDelay: 0\n                    }}\n                >\n                    {children}\n                </motion.div>\n            );\n        case 'slide':\n            if (direction === 'up' || direction === 'down') {\n                return (\n                    <motion.div\n                        ref={ref}\n                        animate={{ y: y !== undefined ? y : '' }}\n                        onHoverEnd={() => cycleY()}\n                        onHoverStart={() => cycleY()}\n                    >\n                        {children}\n                    </motion.div>\n                );\n            }\n            return (\n                <motion.div ref={ref} animate={{ x: x !== undefined ? x : '' }} onHoverEnd={() => cycleX()} onHoverStart={() => cycleX()}>\n                    {children}\n                </motion.div>\n            );\n\n        case 'scale':\n        default:\n            if (typeof scale === 'number') {\n                scale = {\n                    hover: scale,\n                    tap: scale\n                };\n            }\n            return (\n                <motion.div ref={ref} whileHover={{ scale: scale?.hover }} whileTap={{ scale: scale?.tap }}>\n                    {children}\n                </motion.div>\n            );\n    }\n});\n\nAnimateButton.defaultProps = {\n    type: 'scale',\n    offset: 10,\n    direction: 'right',\n    scale: {\n        hover: 1,\n        tap: 0.9\n    }\n};\n\nexport default AnimateButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAe,OAAO;AAClC;AACA,SAASC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAejD;;AAEA,MAAMC,aAAa,gBAAAC,EAAA,cAAGN,KAAK,CAACO,UAAU,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,QAAQ;EAAEC,IAAI;EAAEC,SAAS;EAAEC,MAAM;EAAEC;AAA0B,CAAC,EAAEC,GAAwB,KAAK;EAAA,IAAAC,MAAA,EAAAC,OAAA;EAAAV,EAAA;EACnI,IAAIW,OAAO;EACX,IAAIC,OAAO;EACX,QAAQP,SAAS;IACb,KAAK,IAAI;IACT,KAAK,MAAM;MACPM,OAAO,GAAGL,MAAM;MAChBM,OAAO,GAAG,CAAC;MACX;IACJ,KAAK,OAAO;IACZ,KAAK,MAAM;IACX;MACID,OAAO,GAAG,CAAC;MACXC,OAAO,GAAGN,MAAM;MAChB;EACR;EAEA,MAAM,CAACO,CAAC,EAAEC,MAAM,CAAC,GAAGlB,QAAQ,CAACe,OAAO,EAAEC,OAAO,CAAC;EAC9C,MAAM,CAACG,CAAC,EAAEC,MAAM,CAAC,GAAGpB,QAAQ,CAACe,OAAO,EAAEC,OAAO,CAAC;EAE9C,QAAQR,IAAI;IACR,KAAK,QAAQ;MACT,oBACIN,OAAA,CAACH,MAAM,CAACsB,GAAG;QACPT,GAAG,EAAEA,GAAI;QACTU,OAAO,EAAE;UAAEC,MAAM,EAAE;QAAI,CAAE;QACzBC,UAAU,EAAE;UACRC,MAAM,EAAEC,QAAQ;UAChBC,UAAU,EAAE,MAAM;UAClBC,QAAQ,EAAE,CAAC;UACXC,WAAW,EAAE;QACjB,CAAE;QAAAtB,QAAA,EAEDA;MAAQ;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAErB,KAAK,OAAO;MACR,IAAIxB,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,MAAM,EAAE;QAC5C,oBACIP,OAAA,CAACH,MAAM,CAACsB,GAAG;UACPT,GAAG,EAAEA,GAAI;UACTU,OAAO,EAAE;YAAEH,CAAC,EAAEA,CAAC,KAAKe,SAAS,GAAGf,CAAC,GAAG;UAAG,CAAE;UACzCgB,UAAU,EAAEA,CAAA,KAAMf,MAAM,CAAC,CAAE;UAC3BgB,YAAY,EAAEA,CAAA,KAAMhB,MAAM,CAAC,CAAE;UAAAb,QAAA,EAE5BA;QAAQ;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAErB;MACA,oBACI/B,OAAA,CAACH,MAAM,CAACsB,GAAG;QAACT,GAAG,EAAEA,GAAI;QAACU,OAAO,EAAE;UAAEL,CAAC,EAAEA,CAAC,KAAKiB,SAAS,GAAGjB,CAAC,GAAG;QAAG,CAAE;QAACkB,UAAU,EAAEA,CAAA,KAAMjB,MAAM,CAAC,CAAE;QAACkB,YAAY,EAAEA,CAAA,KAAMlB,MAAM,CAAC,CAAE;QAAAX,QAAA,EACpHA;MAAQ;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAGrB,KAAK,OAAO;IACZ;MACI,IAAI,OAAOtB,KAAK,KAAK,QAAQ,EAAE;QAC3BA,KAAK,GAAG;UACJ0B,KAAK,EAAE1B,KAAK;UACZ2B,GAAG,EAAE3B;QACT,CAAC;MACL;MACA,oBACIT,OAAA,CAACH,MAAM,CAACsB,GAAG;QAACT,GAAG,EAAEA,GAAI;QAAC2B,UAAU,EAAE;UAAE5B,KAAK,GAAAE,MAAA,GAAEF,KAAK,cAAAE,MAAA,uBAALA,MAAA,CAAOwB;QAAM,CAAE;QAACG,QAAQ,EAAE;UAAE7B,KAAK,GAAAG,OAAA,GAAEH,KAAK,cAAAG,OAAA,uBAALA,OAAA,CAAOwB;QAAI,CAAE;QAAA/B,QAAA,EACtFA;MAAQ;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;EAEzB;AACJ,CAAC;EAAA,QApDuBjC,QAAQ,EACRA,QAAQ;AAAA,EAmD/B,CAAC;EAAA,QApDsBA,QAAQ,EACRA,QAAQ;AAAA,EAmD9B;AAACyC,GAAA,GArEGtC,aAAa;AAuEnBA,aAAa,CAACuC,YAAY,GAAG;EACzBlC,IAAI,EAAE,OAAO;EACbE,MAAM,EAAE,EAAE;EACVD,SAAS,EAAE,OAAO;EAClBE,KAAK,EAAE;IACH0B,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE;EACT;AACJ,CAAC;AAED,eAAenC,aAAa;AAAC,IAAAG,EAAA,EAAAmC,GAAA;AAAAE,YAAA,CAAArC,EAAA;AAAAqC,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}