{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/ServiceType.tsx\";\nimport { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { Select } from 'components/extended/Form';\nimport { DEFAULT_VALUE_OPTION, SERVICE_TYPE, DEFAULT_VALUE_OPTION_SELECT } from 'constants/Common';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ServiceType = props => {\n  const {\n    isShowAll,\n    required,\n    name,\n    disabled,\n    label\n  } = props;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Select, {\n      required: required,\n      disabled: disabled,\n      selects: !isShowAll ? [DEFAULT_VALUE_OPTION, ...SERVICE_TYPE] : [DEFAULT_VALUE_OPTION_SELECT, ...SERVICE_TYPE],\n      name: name,\n      label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n        id: label || 'service-type'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 24\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_c = ServiceType;\nServiceType.defaultProps = {\n  isShowAll: false,\n  name: 'project.serviceType'\n};\nexport default ServiceType;\nvar _c;\n$RefreshReg$(_c, \"ServiceType\");", "map": {"version": 3, "names": ["FormattedMessage", "Select", "DEFAULT_VALUE_OPTION", "SERVICE_TYPE", "DEFAULT_VALUE_OPTION_SELECT", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ServiceType", "props", "isShowAll", "required", "name", "disabled", "label", "children", "selects", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "defaultProps", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/ServiceType.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { Select } from 'components/extended/Form';\nimport { DEFAULT_VALUE_OPTION, SERVICE_TYPE, DEFAULT_VALUE_OPTION_SELECT } from 'constants/Common';\n\ninterface IServiceTypeProps {\n    isShowAll?: boolean;\n    required?: boolean;\n    name: string;\n    disabled?: boolean;\n    label?: string;\n}\n\nconst ServiceType = (props: IServiceTypeProps) => {\n    const { isShowAll, required, name, disabled, label } = props;\n    return (\n        <>\n            <Select\n                required={required}\n                disabled={disabled}\n                selects={!isShowAll ? [DEFAULT_VALUE_OPTION, ...SERVICE_TYPE] : [DEFAULT_VALUE_OPTION_SELECT, ...SERVICE_TYPE]}\n                name={name}\n                label={<FormattedMessage id={label || 'service-type'} />}\n            />\n        </>\n    );\n};\n\nServiceType.defaultProps = {\n    isShowAll: false,\n    name: 'project.serviceType'\n};\n\nexport default ServiceType;\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,oBAAoB,EAAEC,YAAY,EAAEC,2BAA2B,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAUnG,MAAMC,WAAW,GAAIC,KAAwB,IAAK;EAC9C,MAAM;IAAEC,SAAS;IAAEC,QAAQ;IAAEC,IAAI;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAGL,KAAK;EAC5D,oBACIJ,OAAA,CAAAE,SAAA;IAAAQ,QAAA,eACIV,OAAA,CAACL,MAAM;MACHW,QAAQ,EAAEA,QAAS;MACnBE,QAAQ,EAAEA,QAAS;MACnBG,OAAO,EAAE,CAACN,SAAS,GAAG,CAACT,oBAAoB,EAAE,GAAGC,YAAY,CAAC,GAAG,CAACC,2BAA2B,EAAE,GAAGD,YAAY,CAAE;MAC/GU,IAAI,EAAEA,IAAK;MACXE,KAAK,eAAET,OAAA,CAACN,gBAAgB;QAACkB,EAAE,EAAEH,KAAK,IAAI;MAAe;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D;EAAC,gBACJ,CAAC;AAEX,CAAC;AAACC,EAAA,GAbId,WAAW;AAejBA,WAAW,CAACe,YAAY,GAAG;EACvBb,SAAS,EAAE,KAAK;EAChBE,IAAI,EAAE;AACV,CAAC;AAED,eAAeJ,WAAW;AAAC,IAAAc,EAAA;AAAAE,YAAA,CAAAF,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}