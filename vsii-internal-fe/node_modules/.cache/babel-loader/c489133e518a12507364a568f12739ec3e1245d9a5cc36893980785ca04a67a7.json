{"ast": null, "code": "import{useEffect,useState}from'react';import{FormattedMessage}from'react-intl';// project imports\nimport{DEFAULT_VALUE_OPTION}from'constants/Common';import{Select}from'components/extended/Form';import sendRequest from'services/ApiService';import{searchFormConfig}from'./Config';import Api from'constants/Api';import{jsx as _jsx}from\"react/jsx-runtime\";const ProjectTypeNonBillableConfig=_ref=>{let{name,required,nonBillable,disabled,handleChangeProjectType,disableLabel,noDefaultOption}=_ref;const[projectType,setProjectType]=useState([DEFAULT_VALUE_OPTION]);async function getAllProjectType(nonBillable){const response=await sendRequest(Api.master.getProjectTypeNonBillableConfig(nonBillable));if(!response)return;const{status,result}=response;if(status){setProjectType([...(noDefaultOption?[]:[DEFAULT_VALUE_OPTION]),...result.content.map(proType=>({value:proType.typeCode,label:proType.projectTypeName}))]);}}useEffect(()=>{getAllProjectType(nonBillable);// eslint-disable-next-line react-hooks/exhaustive-deps\n},[nonBillable]);return/*#__PURE__*/_jsx(Select,{required:required,selects:projectType,name:name,label:disableLabel?'':/*#__PURE__*/_jsx(FormattedMessage,{id:searchFormConfig.projectType.label}),disabled:disabled,handleChange:handleChangeProjectType});};ProjectTypeNonBillableConfig.defaultProps={name:searchFormConfig.projectType.name};export default ProjectTypeNonBillableConfig;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}