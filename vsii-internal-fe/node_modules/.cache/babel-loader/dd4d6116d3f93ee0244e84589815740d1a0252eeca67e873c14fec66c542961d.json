{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/non-billable-monitoring/NonBillByMemberTBody.tsx\";\n// materia-ui\nimport SpeakerNotesIcon from '@mui/icons-material/SpeakerNotes';\nimport { IconButton, Stack, TableBody, TableCell, TableRow, Tooltip } from '@mui/material';\n\n// project imports\nimport { PERMISSIONS } from 'constants/Permission';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { formatTableCellProject } from 'utils/common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NonBillByMemberTBody = props => {\n  const {\n    data,\n    handleOpenCommentDialog\n  } = props;\n  const {\n    nonBillable\n  } = PERMISSIONS.report;\n  return /*#__PURE__*/_jsxDEV(TableBody, {\n    children: data.map((item, key) => {\n      var _item$comment;\n      return /*#__PURE__*/_jsxDEV(TableRow, {\n        sx: {\n          backgroundColor: item.color\n        },\n        children: [/*#__PURE__*/_jsxDEV(TableCell, {\n          children: key + 1\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          children: item.id\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          children: item.firstName + ' ' + item.lastName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          children: item.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          children: item.department\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          children: item.nonBillDivideTotal\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          children: item.projectList && formatTableCellProject(item.projectList)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          sx: {\n            fontWeight: '700 !important'\n          },\n          children: item.notLogtimeYet\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          sx: {\n            fontWeight: '700 !important'\n          },\n          children: item.billAbleProject\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 21\n        }, this), checkAllowedPermission(nonBillable.commentDetail) && /*#__PURE__*/_jsxDEV(TableCell, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            children: /*#__PURE__*/_jsxDEV(Tooltip, {\n              placement: \"top\",\n              title: (_item$comment = item.comment) === null || _item$comment === void 0 ? void 0 : _item$comment.note,\n              onClick: () => handleOpenCommentDialog(item.userId, `${item.firstName} ${item.lastName}`),\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                \"aria-label\": \"comment\",\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(SpeakerNotesIcon, {\n                  sx: {\n                    fontSize: '1.1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 47,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 25\n        }, this)]\n      }, key, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 17\n      }, this);\n    })\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 9\n  }, this);\n};\n_c = NonBillByMemberTBody;\nexport default NonBillByMemberTBody;\nvar _c;\n$RefreshReg$(_c, \"NonBillByMemberTBody\");", "map": {"version": 3, "names": ["SpeakerNotesIcon", "IconButton", "<PERSON><PERSON>", "TableBody", "TableCell", "TableRow", "<PERSON><PERSON><PERSON>", "PERMISSIONS", "checkAllowedPermission", "formatTableCellProject", "jsxDEV", "_jsxDEV", "NonBillByMemberTBody", "props", "data", "handleOpenCommentDialog", "nonBillable", "report", "children", "map", "item", "key", "_item$comment", "sx", "backgroundColor", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "firstName", "lastName", "title", "department", "nonBillDivideTotal", "projectList", "fontWeight", "notLogtimeYet", "billAbleProject", "commentDetail", "direction", "justifyContent", "alignItems", "placement", "comment", "note", "onClick", "userId", "size", "fontSize", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/non-billable-monitoring/NonBillByMemberTBody.tsx"], "sourcesContent": ["// materia-ui\nimport SpeakerNotesIcon from '@mui/icons-material/SpeakerNotes';\nimport { IconButton, Stack, TableBody, TableCell, TableRow, Tooltip } from '@mui/material';\n\n// project imports\nimport { PERMISSIONS } from 'constants/Permission';\nimport { INonBillableMonitoring } from 'types';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { formatTableCellProject } from 'utils/common';\n\ninterface NonBillByMemberTBodyProps {\n    data: INonBillableMonitoring[];\n    handleOpenCommentDialog: (userId?: string, subTitle?: string) => void;\n}\n\nconst NonBillByMemberTBody = (props: NonBillByMemberTBodyProps) => {\n    const { data, handleOpenCommentDialog } = props;\n    const { nonBillable } = PERMISSIONS.report;\n\n    return (\n        <TableBody>\n            {data.map((item, key) => (\n                <TableRow\n                    key={key}\n                    sx={{\n                        backgroundColor: item.color\n                    }}\n                >\n                    <TableCell>{key + 1}</TableCell>\n                    <TableCell>{item.id}</TableCell>\n                    <TableCell>{item.firstName + ' ' + item.lastName}</TableCell>\n                    <TableCell>{item.title}</TableCell>\n                    <TableCell>{item.department}</TableCell>\n                    <TableCell>{item.nonBillDivideTotal}</TableCell>\n                    <TableCell>{item.projectList && formatTableCellProject(item.projectList)}</TableCell>\n                    <TableCell sx={{ fontWeight: '700 !important' }}>{item.notLogtimeYet}</TableCell>\n                    <TableCell sx={{ fontWeight: '700 !important' }}>{item.billAbleProject}</TableCell>\n                    {checkAllowedPermission(nonBillable.commentDetail) && (\n                        <TableCell>\n                            <Stack direction=\"row\" justifyContent=\"center\" alignItems=\"center\">\n                                <Tooltip\n                                    placement=\"top\"\n                                    title={item.comment?.note}\n                                    onClick={() => handleOpenCommentDialog(item.userId, `${item.firstName} ${item.lastName}`)}\n                                >\n                                    <IconButton aria-label=\"comment\" size=\"small\">\n                                        <SpeakerNotesIcon sx={{ fontSize: '1.1rem' }} />\n                                    </IconButton>\n                                </Tooltip>\n                            </Stack>\n                        </TableCell>\n                    )}\n                </TableRow>\n            ))}\n        </TableBody>\n    );\n};\n\nexport default NonBillByMemberTBody;\n"], "mappings": ";AAAA;AACA,OAAOA,gBAAgB,MAAM,kCAAkC;AAC/D,SAASC,UAAU,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,eAAe;;AAE1F;AACA,SAASC,WAAW,QAAQ,sBAAsB;AAElD,SAASC,sBAAsB,QAAQ,qBAAqB;AAC5D,SAASC,sBAAsB,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOtD,MAAMC,oBAAoB,GAAIC,KAAgC,IAAK;EAC/D,MAAM;IAAEC,IAAI;IAAEC;EAAwB,CAAC,GAAGF,KAAK;EAC/C,MAAM;IAAEG;EAAY,CAAC,GAAGT,WAAW,CAACU,MAAM;EAE1C,oBACIN,OAAA,CAACR,SAAS;IAAAe,QAAA,EACLJ,IAAI,CAACK,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG;MAAA,IAAAC,aAAA;MAAA,oBAChBX,OAAA,CAACN,QAAQ;QAELkB,EAAE,EAAE;UACAC,eAAe,EAAEJ,IAAI,CAACK;QAC1B,CAAE;QAAAP,QAAA,gBAEFP,OAAA,CAACP,SAAS;UAAAc,QAAA,EAAEG,GAAG,GAAG;QAAC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAChClB,OAAA,CAACP,SAAS;UAAAc,QAAA,EAAEE,IAAI,CAACU;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAChClB,OAAA,CAACP,SAAS;UAAAc,QAAA,EAAEE,IAAI,CAACW,SAAS,GAAG,GAAG,GAAGX,IAAI,CAACY;QAAQ;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC7DlB,OAAA,CAACP,SAAS;UAAAc,QAAA,EAAEE,IAAI,CAACa;QAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACnClB,OAAA,CAACP,SAAS;UAAAc,QAAA,EAAEE,IAAI,CAACc;QAAU;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACxClB,OAAA,CAACP,SAAS;UAAAc,QAAA,EAAEE,IAAI,CAACe;QAAkB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAChDlB,OAAA,CAACP,SAAS;UAAAc,QAAA,EAAEE,IAAI,CAACgB,WAAW,IAAI3B,sBAAsB,CAACW,IAAI,CAACgB,WAAW;QAAC;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACrFlB,OAAA,CAACP,SAAS;UAACmB,EAAE,EAAE;YAAEc,UAAU,EAAE;UAAiB,CAAE;UAAAnB,QAAA,EAAEE,IAAI,CAACkB;QAAa;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACjFlB,OAAA,CAACP,SAAS;UAACmB,EAAE,EAAE;YAAEc,UAAU,EAAE;UAAiB,CAAE;UAAAnB,QAAA,EAAEE,IAAI,CAACmB;QAAe;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAClFrB,sBAAsB,CAACQ,WAAW,CAACwB,aAAa,CAAC,iBAC9C7B,OAAA,CAACP,SAAS;UAAAc,QAAA,eACNP,OAAA,CAACT,KAAK;YAACuC,SAAS,EAAC,KAAK;YAACC,cAAc,EAAC,QAAQ;YAACC,UAAU,EAAC,QAAQ;YAAAzB,QAAA,eAC9DP,OAAA,CAACL,OAAO;cACJsC,SAAS,EAAC,KAAK;cACfX,KAAK,GAAAX,aAAA,GAAEF,IAAI,CAACyB,OAAO,cAAAvB,aAAA,uBAAZA,aAAA,CAAcwB,IAAK;cAC1BC,OAAO,EAAEA,CAAA,KAAMhC,uBAAuB,CAACK,IAAI,CAAC4B,MAAM,EAAE,GAAG5B,IAAI,CAACW,SAAS,IAAIX,IAAI,CAACY,QAAQ,EAAE,CAAE;cAAAd,QAAA,eAE1FP,OAAA,CAACV,UAAU;gBAAC,cAAW,SAAS;gBAACgD,IAAI,EAAC,OAAO;gBAAA/B,QAAA,eACzCP,OAAA,CAACX,gBAAgB;kBAACuB,EAAE,EAAE;oBAAE2B,QAAQ,EAAE;kBAAS;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACd;MAAA,GA5BIR,GAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA6BF,CAAC;IAAA,CACd;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEpB,CAAC;AAACsB,EAAA,GAzCIvC,oBAAoB;AA2C1B,eAAeA,oBAAoB;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}