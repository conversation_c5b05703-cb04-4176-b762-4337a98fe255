{"ast": null, "code": "import{yupResolver}from'@hookform/resolvers/yup';import{FormattedMessage}from'react-intl';// material-ui\nimport{LoadingButton}from'@mui/lab';import{Button,DialogActions,Grid,Typography}from'@mui/material';// project imports\nimport{FormProvider,Input}from'components/extended/Form';import Modal from'components/extended/Modal';import{MessageWorkingCalendarConfig,MessageWorkingCalendarSchema}from'pages/register-working-calendar/Config';import{gridSpacing}from'store/constant';import{TEXT_CONFIG_SCREEN}from'constants/Common';// ==============================|| Message Working Calendar Modal ||============================== //\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const MessageWorkingCalendarModal=_ref=>{let{loading,open,handleClose,item,isEdit,onSubmitMessage,postUpdateStatus}=_ref;const{register_working_calendar}=TEXT_CONFIG_SCREEN.workingCalendar;const handleSubmit=values=>{const{message}=values;onSubmitMessage(message);};const hanldeVerified=value=>{if(item)postUpdateStatus({...item,status:value});};return/*#__PURE__*/_jsx(Modal,{isOpen:open,title:register_working_calendar+'message',onClose:handleClose,keepMounted:false,children:/*#__PURE__*/_jsxs(FormProvider,{form:{defaultValues:MessageWorkingCalendarConfig,resolver:yupResolver(MessageWorkingCalendarSchema(isEdit))},onSubmit:handleSubmit,formReset:{...item,message:item!==null&&item!==void 0&&item.message?item.message:''},children:[/*#__PURE__*/_jsxs(Grid,{container:true,spacing:gridSpacing,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Typography,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:isEdit?register_working_calendar+'message-working-calendar-detail':register_working_calendar+'message-working-calendar-verify'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{multiline:true,rows:4,disabled:!isEdit},name:\"message\"})})]}),/*#__PURE__*/_jsx(DialogActions,{children:isEdit?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Button,{color:\"error\",onClick:handleClose,children:/*#__PURE__*/_jsx(FormattedMessage,{id:register_working_calendar+'cancel'})}),/*#__PURE__*/_jsx(LoadingButton,{loading:loading,variant:\"contained\",type:\"submit\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:register_working_calendar+'submit'})})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(LoadingButton,{loading:loading,variant:\"contained\",type:\"submit\",onClick:()=>hanldeVerified('approve'),children:/*#__PURE__*/_jsx(FormattedMessage,{id:register_working_calendar+'approve'})}),/*#__PURE__*/_jsx(LoadingButton,{loading:loading,variant:\"contained\",type:\"submit\",color:\"error\",onClick:()=>hanldeVerified('declines'),children:/*#__PURE__*/_jsx(FormattedMessage,{id:register_working_calendar+'declines'})})]})})]})});};export default MessageWorkingCalendarModal;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}