{"ast": null, "code": "/* eslint-disable prettier/prettier */import React from'react';// react-hook-form\nimport{Form<PERSON>rovider as R<PERSON><PERSON>rovider,useForm}from'react-hook-form';import{jsx as _jsx}from\"react/jsx-runtime\";function FormProvider(props){var _React$Children;const{children,onSubmit,form,formReset,formReturn,...other}=props;// eslint-disable-next-line react-hooks/rules-of-hooks\nconst methods=formReturn?{...formReturn}:useForm({...form,mode:'all'});React.useEffect(()=>{methods.reset(formReset);// eslint-disable-next-line react-hooks/exhaustive-deps\n},[formReset]);return/*#__PURE__*/_jsx(RHFProvider,{...methods,children:/*#__PURE__*/_jsx(\"form\",{onSubmit:methods.handleSubmit(onSubmit),...other,children:(_React$Children=React.Children)===null||_React$Children===void 0?void 0:_React$Children.map(children,child=>{var _child$props;return child!==null&&child!==void 0&&(_child$props=child.props)!==null&&_child$props!==void 0&&_child$props.name?/*#__PURE__*/React.createElement(child.type,{...{key:child.props.name,...methods.register,...child.props}}):child;})})});}export default FormProvider;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}