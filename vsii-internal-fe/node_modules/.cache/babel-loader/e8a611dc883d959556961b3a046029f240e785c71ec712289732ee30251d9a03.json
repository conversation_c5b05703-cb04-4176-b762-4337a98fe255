{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/monthly-project-cost/DetailReportByMonthSearch.tsx\";\nimport { FormattedMessage } from 'react-intl';\n\n// material-ui\nimport ErrorIcon from '@mui/icons-material/Error';\nimport { Grid, Typography } from '@mui/material';\nimport { Button } from 'components';\n\n// project imports\nimport { Label } from 'components/extended/Form';\nimport { detailReportByMonthConfig, detailReportByMonthSchema } from 'pages/monthly-project-cost/Config';\nimport { Months, Project, SearchForm, Years } from '../search';\nimport { TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';\nimport ColorNoteTooltip from 'components/ColorNoteTooltip';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DetailReportByMonthSearch = props => {\n  const {\n    months,\n    handleChangeYear,\n    handleSearch,\n    formReset,\n    handleChangeMonth,\n    month\n  } = props;\n  const {\n    monthlyProjectCost\n  } = TEXT_CONFIG_SCREEN;\n  return /*#__PURE__*/_jsxDEV(SearchForm, {\n    defaultValues: detailReportByMonthConfig,\n    formSchema: detailReportByMonthSchema,\n    handleSubmit: handleSearch,\n    formReset: formReset,\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 3,\n        children: /*#__PURE__*/_jsxDEV(Years, {\n          handleChangeYear: handleChangeYear,\n          label: monthlyProjectCost.detailReportByMonth + 'year'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 3,\n        children: /*#__PURE__*/_jsxDEV(Months, {\n          months: months,\n          isShow13MonthSalary: true,\n          onChange: handleChangeMonth,\n          isFilter: true,\n          year: formReset.year,\n          label: monthlyProjectCost.detailReportByMonth + 'month'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 3,\n        children: /*#__PURE__*/_jsxDEV(Project, {\n          isNotStatus: true,\n          month: month,\n          label: /*#__PURE__*/_jsxDEV(Typography, {\n            display: \"flex\",\n            gap: 0.5,\n            children: [/*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: monthlyProjectCost.detailReportByMonth + 'project'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(ColorNoteTooltip, {\n              notes: TEXT_INPUT_COLOR_EFFORT_INCURRED,\n              children: /*#__PURE__*/_jsxDEV(ErrorIcon, {\n                sx: {\n                  fontSize: 15\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 3,\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          label: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          size: \"medium\",\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: monthlyProjectCost.detailReportByMonth + 'search'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 35\n          }, this),\n          variant: \"contained\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 9\n  }, this);\n};\n_c = DetailReportByMonthSearch;\nexport default DetailReportByMonthSearch;\nvar _c;\n$RefreshReg$(_c, \"DetailReportByMonthSearch\");", "map": {"version": 3, "names": ["FormattedMessage", "ErrorIcon", "Grid", "Typography", "<PERSON><PERSON>", "Label", "detailReportByMonthConfig", "detailReportByMonthSchema", "Months", "Project", "SearchForm", "Years", "TEXT_CONFIG_SCREEN", "TEXT_INPUT_COLOR_EFFORT_INCURRED", "ColorNoteTooltip", "jsxDEV", "_jsxDEV", "DetailReportByMonthSearch", "props", "months", "handleChangeYear", "handleSearch", "formReset", "handleChangeMonth", "month", "monthlyProjectCost", "defaultValues", "formSchema", "handleSubmit", "children", "container", "spacing", "item", "xs", "lg", "label", "detailReportByMonth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isShow13MonthSalary", "onChange", "isFilter", "year", "isNotStatus", "display", "gap", "id", "notes", "sx", "fontSize", "type", "size", "variant", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/monthly-project-cost/DetailReportByMonthSearch.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\n\n// material-ui\nimport ErrorIcon from '@mui/icons-material/Error';\nimport { Grid, Typography } from '@mui/material';\nimport { Button } from 'components';\n\n// project imports\nimport { Label } from 'components/extended/Form';\nimport { detailReportByMonthConfig, detailReportByMonthSchema } from 'pages/monthly-project-cost/Config';\nimport { IOption } from 'types';\nimport { Months, Project, SearchForm, Years } from '../search';\nimport { TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';\nimport ColorNoteTooltip from 'components/ColorNoteTooltip';\n\ninterface IDetailReportByMonthSearchProps {\n    months: IOption[];\n    handleChangeYear: (e: any) => void;\n    handleSearch: (value: any) => void;\n    formReset: any;\n    handleChangeMonth: (e: any) => void;\n    month: any;\n}\n\nconst DetailReportByMonthSearch = (props: IDetailReportByMonthSearchProps) => {\n    const { months, handleChangeYear, handleSearch, formReset, handleChangeMonth, month } = props;\n    const { monthlyProjectCost } = TEXT_CONFIG_SCREEN;\n    return (\n        <SearchForm\n            defaultValues={detailReportByMonthConfig}\n            formSchema={detailReportByMonthSchema}\n            handleSubmit={handleSearch}\n            formReset={formReset}\n        >\n            <Grid container spacing={2}>\n                <Grid item xs={12} lg={3}>\n                    <Years handleChangeYear={handleChangeYear} label={monthlyProjectCost.detailReportByMonth + 'year'} />\n                </Grid>\n                <Grid item xs={12} lg={3}>\n                    <Months\n                        months={months}\n                        isShow13MonthSalary\n                        onChange={handleChangeMonth}\n                        isFilter\n                        year={formReset.year}\n                        label={monthlyProjectCost.detailReportByMonth + 'month'}\n                    />\n                </Grid>\n                <Grid item xs={12} lg={3}>\n                    <Project\n                        isNotStatus\n                        month={month}\n                        label={\n                            <Typography display=\"flex\" gap={0.5}>\n                                <FormattedMessage id={monthlyProjectCost.detailReportByMonth + 'project'} />\n                                <ColorNoteTooltip notes={TEXT_INPUT_COLOR_EFFORT_INCURRED}>\n                                    <ErrorIcon sx={{ fontSize: 15 }} />\n                                </ColorNoteTooltip>\n                            </Typography>\n                        }\n                    />\n                </Grid>\n                <Grid item xs={12} lg={3}>\n                    <Label label=\"&nbsp;\" />\n                    <Button\n                        type=\"submit\"\n                        size=\"medium\"\n                        children={<FormattedMessage id={monthlyProjectCost.detailReportByMonth + 'search'} />}\n                        variant=\"contained\"\n                    />\n                </Grid>\n            </Grid>\n        </SearchForm>\n    );\n};\n\nexport default DetailReportByMonthSearch;\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,OAAOC,SAAS,MAAM,2BAA2B;AACjD,SAASC,IAAI,EAAEC,UAAU,QAAQ,eAAe;AAChD,SAASC,MAAM,QAAQ,YAAY;;AAEnC;AACA,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,yBAAyB,EAAEC,yBAAyB,QAAQ,mCAAmC;AAExG,SAASC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAEC,KAAK,QAAQ,WAAW;AAC9D,SAASC,kBAAkB,EAAEC,gCAAgC,QAAQ,kBAAkB;AACvF,OAAOC,gBAAgB,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAW3D,MAAMC,yBAAyB,GAAIC,KAAsC,IAAK;EAC1E,MAAM;IAAEC,MAAM;IAAEC,gBAAgB;IAAEC,YAAY;IAAEC,SAAS;IAAEC,iBAAiB;IAAEC;EAAM,CAAC,GAAGN,KAAK;EAC7F,MAAM;IAAEO;EAAmB,CAAC,GAAGb,kBAAkB;EACjD,oBACII,OAAA,CAACN,UAAU;IACPgB,aAAa,EAAEpB,yBAA0B;IACzCqB,UAAU,EAAEpB,yBAA0B;IACtCqB,YAAY,EAAEP,YAAa;IAC3BC,SAAS,EAAEA,SAAU;IAAAO,QAAA,eAErBb,OAAA,CAACd,IAAI;MAAC4B,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAF,QAAA,gBACvBb,OAAA,CAACd,IAAI;QAAC8B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACrBb,OAAA,CAACL,KAAK;UAACS,gBAAgB,EAAEA,gBAAiB;UAACe,KAAK,EAAEV,kBAAkB,CAACW,mBAAmB,GAAG;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnG,CAAC,eACPxB,OAAA,CAACd,IAAI;QAAC8B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACrBb,OAAA,CAACR,MAAM;UACHW,MAAM,EAAEA,MAAO;UACfsB,mBAAmB;UACnBC,QAAQ,EAAEnB,iBAAkB;UAC5BoB,QAAQ;UACRC,IAAI,EAAEtB,SAAS,CAACsB,IAAK;UACrBT,KAAK,EAAEV,kBAAkB,CAACW,mBAAmB,GAAG;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACPxB,OAAA,CAACd,IAAI;QAAC8B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACrBb,OAAA,CAACP,OAAO;UACJoC,WAAW;UACXrB,KAAK,EAAEA,KAAM;UACbW,KAAK,eACDnB,OAAA,CAACb,UAAU;YAAC2C,OAAO,EAAC,MAAM;YAACC,GAAG,EAAE,GAAI;YAAAlB,QAAA,gBAChCb,OAAA,CAAChB,gBAAgB;cAACgD,EAAE,EAAEvB,kBAAkB,CAACW,mBAAmB,GAAG;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5ExB,OAAA,CAACF,gBAAgB;cAACmC,KAAK,EAAEpC,gCAAiC;cAAAgB,QAAA,eACtDb,OAAA,CAACf,SAAS;gBAACiD,EAAE,EAAE;kBAAEC,QAAQ,EAAE;gBAAG;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QACf;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACPxB,OAAA,CAACd,IAAI;QAAC8B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,gBACrBb,OAAA,CAACX,KAAK;UAAC8B,KAAK,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxBxB,OAAA,CAACZ,MAAM;UACHgD,IAAI,EAAC,QAAQ;UACbC,IAAI,EAAC,QAAQ;UACbxB,QAAQ,eAAEb,OAAA,CAAChB,gBAAgB;YAACgD,EAAE,EAAEvB,kBAAkB,CAACW,mBAAmB,GAAG;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtFc,OAAO,EAAC;QAAW;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAErB,CAAC;AAACe,EAAA,GAlDItC,yBAAyB;AAoD/B,eAAeA,yBAAyB;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}