{"ast": null, "code": "import { useRef, useContext, useEffect } from 'react';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { useUnmountEffect } from '../utils/use-unmount-effect.mjs';\nimport { usePointerEvent } from '../events/use-pointer-event.mjs';\nimport { PanSession } from './PanSession.mjs';\n\n/**\n *\n * @param handlers -\n * @param ref -\n *\n * @privateRemarks\n * Currently this sets new pan gesture functions every render. The memo route has been explored\n * in the past but ultimately we're still creating new functions every render. An optimisation\n * to explore is creating the pan gestures and loading them into a `ref`.\n *\n * @internal\n */\nfunction usePanGesture({\n  onPan,\n  onPanStart,\n  onPanEnd,\n  onPanSessionStart,\n  visualElement\n}) {\n  const hasPanEvents = onPan || onPanStart || onPanEnd || onPanSessionStart;\n  const panSession = useRef(null);\n  const {\n    transformPagePoint\n  } = useContext(MotionConfigContext);\n  const handlers = {\n    onSessionStart: onPanSessionStart,\n    onStart: onPanStart,\n    onMove: onPan,\n    onEnd: (event, info) => {\n      panSession.current = null;\n      onPanEnd && onPanEnd(event, info);\n    }\n  };\n  useEffect(() => {\n    if (panSession.current !== null) {\n      panSession.current.updateHandlers(handlers);\n    }\n  });\n  function onPointerDown(event) {\n    panSession.current = new PanSession(event, handlers, {\n      transformPagePoint\n    });\n  }\n  usePointerEvent(visualElement, \"pointerdown\", hasPanEvents && onPointerDown);\n  useUnmountEffect(() => panSession.current && panSession.current.end());\n}\nexport { usePanGesture };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}