{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/AddLanguage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useMemo, useState } from 'react';\nimport { Button, DialogActions, Grid, Stack, Typography } from '@mui/material';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport { FormattedMessage } from 'react-intl';\nimport { useForm } from 'react-hook-form';\nimport { LoadingButton } from '@mui/lab';\nimport { languageConfigSelector } from 'store/slice/languageConfigSlice';\nimport { Autocomplete, FormProvider } from 'components/extended/Form';\nimport { addLanguageSchema } from 'pages/administration/Config';\nimport { useAppDispatch, useAppSelector } from 'app/hooks';\nimport { openSnackbar } from 'store/slice/snackbarSlice';\nimport { LANGUAGE_LIST, TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport Modal from 'components/extended/Modal';\nimport { gridSpacing } from 'store/constant';\nimport sendRequest from 'services/ApiService';\nimport Api from 'constants/Api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AddLanguage = ({\n  open,\n  handleClose,\n  handlekAfterAdd\n}) => {\n  _s();\n  const {\n    language_config\n  } = TEXT_CONFIG_SCREEN.administration.flexibleReport;\n  const [loading, setloading] = useState(false);\n  const dispatch = useAppDispatch();\n  const {\n    laguageConfigList\n  } = useAppSelector(languageConfigSelector);\n  const languageList = useMemo(() => {\n    return LANGUAGE_LIST.filter(language => !laguageConfigList.some(item => item.languageCode === language.value));\n  }, [laguageConfigList]);\n  const methods = useForm({\n    defaultValues: {\n      language: {\n        value: '',\n        label: ''\n      }\n    },\n    resolver: yupResolver(addLanguageSchema)\n  });\n  const handleSubmit = async values => {\n    setloading(true);\n    const res = await sendRequest(Api.flexible_textConfig.addLanguage, {\n      languageCode: values.language.value,\n      languageName: values.language.label\n    });\n    dispatch(openSnackbar({\n      open: true,\n      message: res.status ? res.result.content : res.result.content.message,\n      variant: 'alert',\n      alert: {\n        color: res.status ? 'success' : 'error'\n      }\n    }));\n    if (res.status) {\n      handleClose();\n      handlekAfterAdd === null || handlekAfterAdd === void 0 ? void 0 : handlekAfterAdd(values.language.value);\n    }\n    setloading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    isOpen: open,\n    title: language_config + 'Add-language',\n    onClose: handleClose,\n    maxWidth: \"sm\",\n    children: /*#__PURE__*/_jsxDEV(FormProvider, {\n      formReturn: methods,\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: gridSpacing,\n        padding: 2,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 2.5,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                color: '#333',\n                display: 'flex',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(FormattedMessage, {\n                id: language_config + 'language'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                sx: {\n                  color: '#D02C2C'\n                },\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 9,\n            children: /*#__PURE__*/_jsxDEV(Autocomplete, {\n              name: \"language\",\n              options: languageList,\n              isDefaultAll: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            justifyContent: \"flex-end\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              color: \"error\",\n              onClick: handleClose,\n              children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n                id: language_config + 'cancel'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(LoadingButton, {\n              variant: \"contained\",\n              type: \"submit\",\n              loading: loading,\n              children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n                id: language_config + 'submit'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 9\n  }, this);\n};\n_s(AddLanguage, \"97+f6vSuuK9D1s6Bmyx0fyddzBY=\", false, function () {\n  return [useAppDispatch, useAppSelector, useForm];\n});\n_c = AddLanguage;\nexport default AddLanguage;\nvar _c;\n$RefreshReg$(_c, \"AddLanguage\");", "map": {"version": 3, "names": ["React", "useMemo", "useState", "<PERSON><PERSON>", "DialogActions", "Grid", "<PERSON><PERSON>", "Typography", "yupResolver", "FormattedMessage", "useForm", "LoadingButton", "languageConfigSelector", "Autocomplete", "FormProvider", "addLanguageSchema", "useAppDispatch", "useAppSelector", "openSnackbar", "LANGUAGE_LIST", "TEXT_CONFIG_SCREEN", "Modal", "gridSpacing", "sendRequest", "Api", "jsxDEV", "_jsxDEV", "AddLanguage", "open", "handleClose", "handlekAfterAdd", "_s", "language_config", "administration", "flexibleReport", "loading", "setloading", "dispatch", "laguageConfigList", "languageList", "filter", "language", "some", "item", "languageCode", "value", "methods", "defaultValues", "label", "resolver", "handleSubmit", "values", "res", "flexible_textConfig", "addLanguage", "languageName", "message", "status", "result", "content", "variant", "alert", "color", "isOpen", "title", "onClose", "max<PERSON><PERSON><PERSON>", "children", "formReturn", "onSubmit", "container", "spacing", "padding", "xs", "display", "justifyContent", "alignItems", "sx", "gap", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "options", "isDefaultAll", "direction", "onClick", "type", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/AddLanguage.tsx"], "sourcesContent": ["import React, { useMemo, useState } from 'react';\nimport { Button, <PERSON>alogActions, <PERSON>rid, Stack, Typography } from '@mui/material';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport { FormattedMessage } from 'react-intl';\nimport { useForm } from 'react-hook-form';\nimport { LoadingButton } from '@mui/lab';\n\nimport { languageConfigSelector } from 'store/slice/languageConfigSlice';\nimport { Autocomplete, FormProvider } from 'components/extended/Form';\nimport { addLanguageSchema } from 'pages/administration/Config';\nimport { useAppDispatch, useAppSelector } from 'app/hooks';\nimport { openSnackbar } from 'store/slice/snackbarSlice';\nimport { LANGUAGE_LIST, TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport Modal from 'components/extended/Modal';\nimport { gridSpacing } from 'store/constant';\nimport sendRequest from 'services/ApiService';\nimport Api from 'constants/Api';\n\ninterface Props {\n    open: boolean;\n    handleClose: () => void;\n    handlekAfterAdd?: (languageCode: string) => void;\n}\n\nconst AddLanguage: React.FC<Props> = ({ open, handleClose, handlekAfterAdd }) => {\n    const { language_config } = TEXT_CONFIG_SCREEN.administration.flexibleReport;\n    const [loading, setloading] = useState(false);\n    const dispatch = useAppDispatch();\n\n    const { laguageConfigList } = useAppSelector(languageConfigSelector);\n\n    const languageList = useMemo(() => {\n        return LANGUAGE_LIST.filter((language) => !laguageConfigList.some((item) => item.languageCode === language.value));\n    }, [laguageConfigList]);\n\n    const methods = useForm({\n        defaultValues: { language: { value: '', label: '' } },\n        resolver: yupResolver(addLanguageSchema)\n    });\n\n    const handleSubmit = async (values: { language: { value: string; label: string } }) => {\n        setloading(true);\n        const res = await sendRequest(Api.flexible_textConfig.addLanguage, {\n            languageCode: values.language.value,\n            languageName: values.language.label\n        });\n\n        dispatch(\n            openSnackbar({\n                open: true,\n                message: res.status ? res.result.content : res.result.content.message,\n                variant: 'alert',\n                alert: { color: res.status ? 'success' : 'error' }\n            })\n        );\n\n        if (res.status) {\n            handleClose();\n            handlekAfterAdd?.(values.language.value);\n        }\n        setloading(false);\n    };\n\n    return (\n        <Modal isOpen={open} title={language_config + 'Add-language'} onClose={handleClose} maxWidth=\"sm\">\n            <FormProvider formReturn={methods} onSubmit={handleSubmit}>\n                <Grid container spacing={gridSpacing} padding={2}>\n                    <Grid item xs={12} display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n                        <Grid item xs={2.5}>\n                            <Typography sx={{ color: '#333', display: 'flex', gap: 1 }}>\n                                <FormattedMessage id={language_config + 'language'} />\n                                <Typography sx={{ color: '#D02C2C' }}>*</Typography>\n                            </Typography>\n                        </Grid>\n                        <Grid item xs={9}>\n                            <Autocomplete name=\"language\" options={languageList} isDefaultAll />\n                        </Grid>\n                    </Grid>\n                </Grid>\n                {/* Cancel | Submit */}\n                <Grid item xs={12}>\n                    <DialogActions>\n                        <Stack direction=\"row\" spacing={1} justifyContent=\"flex-end\">\n                            <Button color=\"error\" onClick={handleClose}>\n                                <FormattedMessage id={language_config + 'cancel'} />\n                            </Button>\n                            <LoadingButton variant=\"contained\" type=\"submit\" loading={loading}>\n                                <FormattedMessage id={language_config + 'submit'} />\n                            </LoadingButton>\n                        </Stack>\n                    </DialogActions>\n                </Grid>\n            </FormProvider>\n        </Modal>\n    );\n};\n\nexport default AddLanguage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,OAAO,EAAEC,QAAQ,QAAQ,OAAO;AAChD,SAASC,MAAM,EAAEC,aAAa,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,QAAQ,eAAe;AAC9E,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,aAAa,QAAQ,UAAU;AAExC,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,SAASC,YAAY,EAAEC,YAAY,QAAQ,0BAA0B;AACrE,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,cAAc,EAAEC,cAAc,QAAQ,WAAW;AAC1D,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,kBAAkB;AACpE,OAAOC,KAAK,MAAM,2BAA2B;AAC7C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,GAAG,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQhC,MAAMC,WAA4B,GAAGA,CAAC;EAAEC,IAAI;EAAEC,WAAW;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAC7E,MAAM;IAAEC;EAAgB,CAAC,GAAGZ,kBAAkB,CAACa,cAAc,CAACC,cAAc;EAC5E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMmC,QAAQ,GAAGrB,cAAc,CAAC,CAAC;EAEjC,MAAM;IAAEsB;EAAkB,CAAC,GAAGrB,cAAc,CAACL,sBAAsB,CAAC;EAEpE,MAAM2B,YAAY,GAAGtC,OAAO,CAAC,MAAM;IAC/B,OAAOkB,aAAa,CAACqB,MAAM,CAAEC,QAAQ,IAAK,CAACH,iBAAiB,CAACI,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACC,YAAY,KAAKH,QAAQ,CAACI,KAAK,CAAC,CAAC;EACtH,CAAC,EAAE,CAACP,iBAAiB,CAAC,CAAC;EAEvB,MAAMQ,OAAO,GAAGpC,OAAO,CAAC;IACpBqC,aAAa,EAAE;MAAEN,QAAQ,EAAE;QAAEI,KAAK,EAAE,EAAE;QAAEG,KAAK,EAAE;MAAG;IAAE,CAAC;IACrDC,QAAQ,EAAEzC,WAAW,CAACO,iBAAiB;EAC3C,CAAC,CAAC;EAEF,MAAMmC,YAAY,GAAG,MAAOC,MAAsD,IAAK;IACnFf,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMgB,GAAG,GAAG,MAAM7B,WAAW,CAACC,GAAG,CAAC6B,mBAAmB,CAACC,WAAW,EAAE;MAC/DV,YAAY,EAAEO,MAAM,CAACV,QAAQ,CAACI,KAAK;MACnCU,YAAY,EAAEJ,MAAM,CAACV,QAAQ,CAACO;IAClC,CAAC,CAAC;IAEFX,QAAQ,CACJnB,YAAY,CAAC;MACTU,IAAI,EAAE,IAAI;MACV4B,OAAO,EAAEJ,GAAG,CAACK,MAAM,GAAGL,GAAG,CAACM,MAAM,CAACC,OAAO,GAAGP,GAAG,CAACM,MAAM,CAACC,OAAO,CAACH,OAAO;MACrEI,OAAO,EAAE,OAAO;MAChBC,KAAK,EAAE;QAAEC,KAAK,EAAEV,GAAG,CAACK,MAAM,GAAG,SAAS,GAAG;MAAQ;IACrD,CAAC,CACL,CAAC;IAED,IAAIL,GAAG,CAACK,MAAM,EAAE;MACZ5B,WAAW,CAAC,CAAC;MACbC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGqB,MAAM,CAACV,QAAQ,CAACI,KAAK,CAAC;IAC5C;IACAT,UAAU,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,oBACIV,OAAA,CAACL,KAAK;IAAC0C,MAAM,EAAEnC,IAAK;IAACoC,KAAK,EAAEhC,eAAe,GAAG,cAAe;IAACiC,OAAO,EAAEpC,WAAY;IAACqC,QAAQ,EAAC,IAAI;IAAAC,QAAA,eAC7FzC,OAAA,CAACZ,YAAY;MAACsD,UAAU,EAAEtB,OAAQ;MAACuB,QAAQ,EAAEnB,YAAa;MAAAiB,QAAA,gBACtDzC,OAAA,CAACrB,IAAI;QAACiE,SAAS;QAACC,OAAO,EAAEjD,WAAY;QAACkD,OAAO,EAAE,CAAE;QAAAL,QAAA,eAC7CzC,OAAA,CAACrB,IAAI;UAACsC,IAAI;UAAC8B,EAAE,EAAE,EAAG;UAACC,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAACC,UAAU,EAAC,QAAQ;UAAAT,QAAA,gBAChFzC,OAAA,CAACrB,IAAI;YAACsC,IAAI;YAAC8B,EAAE,EAAE,GAAI;YAAAN,QAAA,eACfzC,OAAA,CAACnB,UAAU;cAACsE,EAAE,EAAE;gBAAEf,KAAK,EAAE,MAAM;gBAAEY,OAAO,EAAE,MAAM;gBAAEI,GAAG,EAAE;cAAE,CAAE;cAAAX,QAAA,gBACvDzC,OAAA,CAACjB,gBAAgB;gBAACsE,EAAE,EAAE/C,eAAe,GAAG;cAAW;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtDzD,OAAA,CAACnB,UAAU;gBAACsE,EAAE,EAAE;kBAAEf,KAAK,EAAE;gBAAU,CAAE;gBAAAK,QAAA,EAAC;cAAC;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACPzD,OAAA,CAACrB,IAAI;YAACsC,IAAI;YAAC8B,EAAE,EAAE,CAAE;YAAAN,QAAA,eACbzC,OAAA,CAACb,YAAY;cAACuE,IAAI,EAAC,UAAU;cAACC,OAAO,EAAE9C,YAAa;cAAC+C,YAAY;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPzD,OAAA,CAACrB,IAAI;QAACsC,IAAI;QAAC8B,EAAE,EAAE,EAAG;QAAAN,QAAA,eACdzC,OAAA,CAACtB,aAAa;UAAA+D,QAAA,eACVzC,OAAA,CAACpB,KAAK;YAACiF,SAAS,EAAC,KAAK;YAAChB,OAAO,EAAE,CAAE;YAACI,cAAc,EAAC,UAAU;YAAAR,QAAA,gBACxDzC,OAAA,CAACvB,MAAM;cAAC2D,KAAK,EAAC,OAAO;cAAC0B,OAAO,EAAE3D,WAAY;cAAAsC,QAAA,eACvCzC,OAAA,CAACjB,gBAAgB;gBAACsE,EAAE,EAAE/C,eAAe,GAAG;cAAS;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACTzD,OAAA,CAACf,aAAa;cAACiD,OAAO,EAAC,WAAW;cAAC6B,IAAI,EAAC,QAAQ;cAACtD,OAAO,EAAEA,OAAQ;cAAAgC,QAAA,eAC9DzC,OAAA,CAACjB,gBAAgB;gBAACsE,EAAE,EAAE/C,eAAe,GAAG;cAAS;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEhB,CAAC;AAACpD,EAAA,CAvEIJ,WAA4B;EAAA,QAGbX,cAAc,EAEDC,cAAc,EAM5BP,OAAO;AAAA;AAAAgF,EAAA,GAXrB/D,WAA4B;AAyElC,eAAeA,WAAW;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}