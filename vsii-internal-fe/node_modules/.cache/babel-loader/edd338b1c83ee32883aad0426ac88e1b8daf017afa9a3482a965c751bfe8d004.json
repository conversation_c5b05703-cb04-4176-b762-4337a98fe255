{"ast": null, "code": "// material-ui\nimport{<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON>ack,TableCell,TableRow}from'@mui/material';// project imports\nimport{Checkbox}from'components/extended/Form';import{EXPERT_LEVEL}from'constants/Common';import{Language}from'containers/search';import ExpertLevel from'./ExpertLevel';import InputTable from'./InputTable';// assets\nimport Visibility from'@mui/icons-material/Visibility';import VisibilityOff from'@mui/icons-material/VisibilityOff';import{DeleteTwoToneIcon}from'assets/images/icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FieldsForeignLanguage=props=>{const{index,handleRemove,idHexString,languageSelect,handleChangeLanguage}=props;return/*#__PURE__*/_jsxs(TableRow,{sx:{position:'relative','& td':{textAlign:'center',height:0}},children:[/*#__PURE__*/_jsx(TableCell,{}),/*#__PURE__*/_jsx(TableCell,{sx:{textAlign:'left !important'},children:/*#__PURE__*/_jsx(Language,{name:\"foreignLanguage.\".concat(index,\".name\"),isShowAll:false,languageSelect:languageSelect,handleChangeLanguage:handleChangeLanguage})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(InputTable,{name:\"foreignLanguage.\".concat(index,\".experiences\"),textAlign:\"center\"})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(InputTable,{name:\"foreignLanguage.\".concat(index,\".lastedUsed\"),textAlign:\"center\"})}),/*#__PURE__*/_jsxs(TableCell,{colSpan:5,sx:{padding:0},children:[/*#__PURE__*/_jsx(ExpertLevel,{name:\"foreignLanguage.\".concat(index,\".level\"),options:EXPERT_LEVEL}),/*#__PURE__*/_jsxs(Stack,{sx:{position:'absolute',top:'50%',right:'-100px',transform:'translateY(-50%)','& .Mui-checked':{color:'#9e9e9e !important'}},direction:\"row\",justifyContent:\"space-between\",spacing:2,children:[/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleRemove(index,idHexString),children:/*#__PURE__*/_jsx(DeleteTwoToneIcon,{fontSize:\"small\"})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Checkbox,{name:\"foreignLanguage.\".concat(index,\".visible\"),checkboxProps:{icon:/*#__PURE__*/_jsx(VisibilityOff,{fontSize:\"small\"}),checkedIcon:/*#__PURE__*/_jsx(Visibility,{fontSize:\"small\"})}})})]})]})]});};export default FieldsForeignLanguage;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}