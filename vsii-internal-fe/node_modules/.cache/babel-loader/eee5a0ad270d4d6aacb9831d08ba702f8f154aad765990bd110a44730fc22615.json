{"ast": null, "code": "import React, { useLayoutEffect, useEffect, useRef, useState, useContext } from 'react';\nimport _inheritsLoose from '@babel/runtime/helpers/esm/inheritsLoose';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport { createStore as createStore$1, applyMiddleware, compose, bindActionCreators } from 'redux';\nimport { Provider, connect } from 'react-redux';\nimport { useMemo, useCallback } from 'use-memo-one';\nimport { getRect, expand, offset, withScroll, getBox, createBox, calculateBox } from 'css-box-model';\nimport memoizeOne from 'memoize-one';\nimport rafSchd from 'raf-schd';\nimport ReactDOM from 'react-dom';\nvar isProduction = process.env.NODE_ENV === 'production';\nvar spacesAndTabs = /[ \\t]{2,}/g;\nvar lineStartWithSpaces = /^[ \\t]*/gm;\nvar clean = function clean(value) {\n  return value.replace(spacesAndTabs, ' ').replace(lineStartWithSpaces, '').trim();\n};\nvar getDevMessage = function getDevMessage(message) {\n  return clean(\"\\n  %creact-beautiful-dnd\\n\\n  %c\" + clean(message) + \"\\n\\n  %c\\uD83D\\uDC77\\u200D This is a development only message. It will be removed in production builds.\\n\");\n};\nvar getFormattedMessage = function getFormattedMessage(message) {\n  return [getDevMessage(message), 'color: #00C584; font-size: 1.2em; font-weight: bold;', 'line-height: 1.5', 'color: #723874;'];\n};\nvar isDisabledFlag = '__react-beautiful-dnd-disable-dev-warnings';\nfunction log(type, message) {\n  var _console;\n  if (isProduction) {\n    return;\n  }\n  if (typeof window !== 'undefined' && window[isDisabledFlag]) {\n    return;\n  }\n  (_console = console)[type].apply(_console, getFormattedMessage(message));\n}\nvar warning = log.bind(null, 'warn');\nvar error = log.bind(null, 'error');\nfunction noop() {}\nfunction getOptions(shared, fromBinding) {\n  return _extends({}, shared, {}, fromBinding);\n}\nfunction bindEvents(el, bindings, sharedOptions) {\n  var unbindings = bindings.map(function (binding) {\n    var options = getOptions(sharedOptions, binding.options);\n    el.addEventListener(binding.eventName, binding.fn, options);\n    return function unbind() {\n      el.removeEventListener(binding.eventName, binding.fn, options);\n    };\n  });\n  return function unbindAll() {\n    unbindings.forEach(function (unbind) {\n      unbind();\n    });\n  };\n}\nvar isProduction$1 = process.env.NODE_ENV === 'production';\nvar prefix = 'Invariant failed';\nfunction RbdInvariant(message) {\n  this.message = message;\n}\nRbdInvariant.prototype.toString = function toString() {\n  return this.message;\n};\nfunction invariant(condition, message) {\n  if (condition) {\n    return;\n  }\n  if (isProduction$1) {\n    throw new RbdInvariant(prefix);\n  } else {\n    throw new RbdInvariant(prefix + \": \" + (message || ''));\n  }\n}\nvar ErrorBoundary = function (_React$Component) {\n  _inheritsLoose(ErrorBoundary, _React$Component);\n  function ErrorBoundary() {\n    var _this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n    _this.callbacks = null;\n    _this.unbind = noop;\n    _this.onWindowError = function (event) {\n      var callbacks = _this.getCallbacks();\n      if (callbacks.isDragging()) {\n        callbacks.tryAbort();\n        process.env.NODE_ENV !== \"production\" ? warning(\"\\n        An error was caught by our window 'error' event listener while a drag was occurring.\\n        The active drag has been aborted.\\n      \") : void 0;\n      }\n      var err = event.error;\n      if (err instanceof RbdInvariant) {\n        event.preventDefault();\n        if (process.env.NODE_ENV !== 'production') {\n          error(err.message);\n        }\n      }\n    };\n    _this.getCallbacks = function () {\n      if (!_this.callbacks) {\n        throw new Error('Unable to find AppCallbacks in <ErrorBoundary/>');\n      }\n      return _this.callbacks;\n    };\n    _this.setCallbacks = function (callbacks) {\n      _this.callbacks = callbacks;\n    };\n    return _this;\n  }\n  var _proto = ErrorBoundary.prototype;\n  _proto.componentDidMount = function componentDidMount() {\n    this.unbind = bindEvents(window, [{\n      eventName: 'error',\n      fn: this.onWindowError\n    }]);\n  };\n  _proto.componentDidCatch = function componentDidCatch(err) {\n    if (err instanceof RbdInvariant) {\n      if (process.env.NODE_ENV !== 'production') {\n        error(err.message);\n      }\n      this.setState({});\n      return;\n    }\n    throw err;\n  };\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.unbind();\n  };\n  _proto.render = function render() {\n    return this.props.children(this.setCallbacks);\n  };\n  return ErrorBoundary;\n}(React.Component);\nvar dragHandleUsageInstructions = \"\\n  Press space bar to start a drag.\\n  When dragging you can use the arrow keys to move the item around and escape to cancel.\\n  Some screen readers may require you to be in focus mode or to use your pass through key\\n\";\nvar position = function position(index) {\n  return index + 1;\n};\nvar onDragStart = function onDragStart(start) {\n  return \"\\n  You have lifted an item in position \" + position(start.source.index) + \"\\n\";\n};\nvar withLocation = function withLocation(source, destination) {\n  var isInHomeList = source.droppableId === destination.droppableId;\n  var startPosition = position(source.index);\n  var endPosition = position(destination.index);\n  if (isInHomeList) {\n    return \"\\n      You have moved the item from position \" + startPosition + \"\\n      to position \" + endPosition + \"\\n    \";\n  }\n  return \"\\n    You have moved the item from position \" + startPosition + \"\\n    in list \" + source.droppableId + \"\\n    to list \" + destination.droppableId + \"\\n    in position \" + endPosition + \"\\n  \";\n};\nvar withCombine = function withCombine(id, source, combine) {\n  var inHomeList = source.droppableId === combine.droppableId;\n  if (inHomeList) {\n    return \"\\n      The item \" + id + \"\\n      has been combined with \" + combine.draggableId;\n  }\n  return \"\\n      The item \" + id + \"\\n      in list \" + source.droppableId + \"\\n      has been combined with \" + combine.draggableId + \"\\n      in list \" + combine.droppableId + \"\\n    \";\n};\nvar onDragUpdate = function onDragUpdate(update) {\n  var location = update.destination;\n  if (location) {\n    return withLocation(update.source, location);\n  }\n  var combine = update.combine;\n  if (combine) {\n    return withCombine(update.draggableId, update.source, combine);\n  }\n  return 'You are over an area that cannot be dropped on';\n};\nvar returnedToStart = function returnedToStart(source) {\n  return \"\\n  The item has returned to its starting position\\n  of \" + position(source.index) + \"\\n\";\n};\nvar onDragEnd = function onDragEnd(result) {\n  if (result.reason === 'CANCEL') {\n    return \"\\n      Movement cancelled.\\n      \" + returnedToStart(result.source) + \"\\n    \";\n  }\n  var location = result.destination;\n  var combine = result.combine;\n  if (location) {\n    return \"\\n      You have dropped the item.\\n      \" + withLocation(result.source, location) + \"\\n    \";\n  }\n  if (combine) {\n    return \"\\n      You have dropped the item.\\n      \" + withCombine(result.draggableId, result.source, combine) + \"\\n    \";\n  }\n  return \"\\n    The item has been dropped while not over a drop area.\\n    \" + returnedToStart(result.source) + \"\\n  \";\n};\nvar preset = {\n  dragHandleUsageInstructions: dragHandleUsageInstructions,\n  onDragStart: onDragStart,\n  onDragUpdate: onDragUpdate,\n  onDragEnd: onDragEnd\n};\nvar origin = {\n  x: 0,\n  y: 0\n};\nvar add = function add(point1, point2) {\n  return {\n    x: point1.x + point2.x,\n    y: point1.y + point2.y\n  };\n};\nvar subtract = function subtract(point1, point2) {\n  return {\n    x: point1.x - point2.x,\n    y: point1.y - point2.y\n  };\n};\nvar isEqual = function isEqual(point1, point2) {\n  return point1.x === point2.x && point1.y === point2.y;\n};\nvar negate = function negate(point) {\n  return {\n    x: point.x !== 0 ? -point.x : 0,\n    y: point.y !== 0 ? -point.y : 0\n  };\n};\nvar patch = function patch(line, value, otherValue) {\n  var _ref;\n  if (otherValue === void 0) {\n    otherValue = 0;\n  }\n  return _ref = {}, _ref[line] = value, _ref[line === 'x' ? 'y' : 'x'] = otherValue, _ref;\n};\nvar distance = function distance(point1, point2) {\n  return Math.sqrt(Math.pow(point2.x - point1.x, 2) + Math.pow(point2.y - point1.y, 2));\n};\nvar closest = function closest(target, points) {\n  return Math.min.apply(Math, points.map(function (point) {\n    return distance(target, point);\n  }));\n};\nvar apply = function apply(fn) {\n  return function (point) {\n    return {\n      x: fn(point.x),\n      y: fn(point.y)\n    };\n  };\n};\nvar executeClip = function (frame, subject) {\n  var result = getRect({\n    top: Math.max(subject.top, frame.top),\n    right: Math.min(subject.right, frame.right),\n    bottom: Math.min(subject.bottom, frame.bottom),\n    left: Math.max(subject.left, frame.left)\n  });\n  if (result.width <= 0 || result.height <= 0) {\n    return null;\n  }\n  return result;\n};\nvar offsetByPosition = function offsetByPosition(spacing, point) {\n  return {\n    top: spacing.top + point.y,\n    left: spacing.left + point.x,\n    bottom: spacing.bottom + point.y,\n    right: spacing.right + point.x\n  };\n};\nvar getCorners = function getCorners(spacing) {\n  return [{\n    x: spacing.left,\n    y: spacing.top\n  }, {\n    x: spacing.right,\n    y: spacing.top\n  }, {\n    x: spacing.left,\n    y: spacing.bottom\n  }, {\n    x: spacing.right,\n    y: spacing.bottom\n  }];\n};\nvar noSpacing = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\nvar scroll = function scroll(target, frame) {\n  if (!frame) {\n    return target;\n  }\n  return offsetByPosition(target, frame.scroll.diff.displacement);\n};\nvar increase = function increase(target, axis, withPlaceholder) {\n  if (withPlaceholder && withPlaceholder.increasedBy) {\n    var _extends2;\n    return _extends({}, target, (_extends2 = {}, _extends2[axis.end] = target[axis.end] + withPlaceholder.increasedBy[axis.line], _extends2));\n  }\n  return target;\n};\nvar clip = function clip(target, frame) {\n  if (frame && frame.shouldClipSubject) {\n    return executeClip(frame.pageMarginBox, target);\n  }\n  return getRect(target);\n};\nvar getSubject = function (_ref) {\n  var page = _ref.page,\n    withPlaceholder = _ref.withPlaceholder,\n    axis = _ref.axis,\n    frame = _ref.frame;\n  var scrolled = scroll(page.marginBox, frame);\n  var increased = increase(scrolled, axis, withPlaceholder);\n  var clipped = clip(increased, frame);\n  return {\n    page: page,\n    withPlaceholder: withPlaceholder,\n    active: clipped\n  };\n};\nvar scrollDroppable = function (droppable, newScroll) {\n  !droppable.frame ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n  var scrollable = droppable.frame;\n  var scrollDiff = subtract(newScroll, scrollable.scroll.initial);\n  var scrollDisplacement = negate(scrollDiff);\n  var frame = _extends({}, scrollable, {\n    scroll: {\n      initial: scrollable.scroll.initial,\n      current: newScroll,\n      diff: {\n        value: scrollDiff,\n        displacement: scrollDisplacement\n      },\n      max: scrollable.scroll.max\n    }\n  });\n  var subject = getSubject({\n    page: droppable.subject.page,\n    withPlaceholder: droppable.subject.withPlaceholder,\n    axis: droppable.axis,\n    frame: frame\n  });\n  var result = _extends({}, droppable, {\n    frame: frame,\n    subject: subject\n  });\n  return result;\n};\nfunction isInteger(value) {\n  if (Number.isInteger) {\n    return Number.isInteger(value);\n  }\n  return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n}\nfunction values(map) {\n  if (Object.values) {\n    return Object.values(map);\n  }\n  return Object.keys(map).map(function (key) {\n    return map[key];\n  });\n}\nfunction findIndex(list, predicate) {\n  if (list.findIndex) {\n    return list.findIndex(predicate);\n  }\n  for (var i = 0; i < list.length; i++) {\n    if (predicate(list[i])) {\n      return i;\n    }\n  }\n  return -1;\n}\nfunction find(list, predicate) {\n  if (list.find) {\n    return list.find(predicate);\n  }\n  var index = findIndex(list, predicate);\n  if (index !== -1) {\n    return list[index];\n  }\n  return undefined;\n}\nfunction toArray(list) {\n  return Array.prototype.slice.call(list);\n}\nvar toDroppableMap = memoizeOne(function (droppables) {\n  return droppables.reduce(function (previous, current) {\n    previous[current.descriptor.id] = current;\n    return previous;\n  }, {});\n});\nvar toDraggableMap = memoizeOne(function (draggables) {\n  return draggables.reduce(function (previous, current) {\n    previous[current.descriptor.id] = current;\n    return previous;\n  }, {});\n});\nvar toDroppableList = memoizeOne(function (droppables) {\n  return values(droppables);\n});\nvar toDraggableList = memoizeOne(function (draggables) {\n  return values(draggables);\n});\nvar getDraggablesInsideDroppable = memoizeOne(function (droppableId, draggables) {\n  var result = toDraggableList(draggables).filter(function (draggable) {\n    return droppableId === draggable.descriptor.droppableId;\n  }).sort(function (a, b) {\n    return a.descriptor.index - b.descriptor.index;\n  });\n  return result;\n});\nfunction tryGetDestination(impact) {\n  if (impact.at && impact.at.type === 'REORDER') {\n    return impact.at.destination;\n  }\n  return null;\n}\nfunction tryGetCombine(impact) {\n  if (impact.at && impact.at.type === 'COMBINE') {\n    return impact.at.combine;\n  }\n  return null;\n}\nvar removeDraggableFromList = memoizeOne(function (remove, list) {\n  return list.filter(function (item) {\n    return item.descriptor.id !== remove.descriptor.id;\n  });\n});\nvar moveToNextCombine = function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n    draggable = _ref.draggable,\n    destination = _ref.destination,\n    insideDestination = _ref.insideDestination,\n    previousImpact = _ref.previousImpact;\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n  var location = tryGetDestination(previousImpact);\n  if (!location) {\n    return null;\n  }\n  function getImpact(target) {\n    var at = {\n      type: 'COMBINE',\n      combine: {\n        draggableId: target,\n        droppableId: destination.descriptor.id\n      }\n    };\n    return _extends({}, previousImpact, {\n      at: at\n    });\n  }\n  var all = previousImpact.displaced.all;\n  var closestId = all.length ? all[0] : null;\n  if (isMovingForward) {\n    return closestId ? getImpact(closestId) : null;\n  }\n  var withoutDraggable = removeDraggableFromList(draggable, insideDestination);\n  if (!closestId) {\n    if (!withoutDraggable.length) {\n      return null;\n    }\n    var last = withoutDraggable[withoutDraggable.length - 1];\n    return getImpact(last.descriptor.id);\n  }\n  var indexOfClosest = findIndex(withoutDraggable, function (d) {\n    return d.descriptor.id === closestId;\n  });\n  !(indexOfClosest !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find displaced item in set') : invariant(false) : void 0;\n  var proposedIndex = indexOfClosest - 1;\n  if (proposedIndex < 0) {\n    return null;\n  }\n  var before = withoutDraggable[proposedIndex];\n  return getImpact(before.descriptor.id);\n};\nvar isHomeOf = function (draggable, destination) {\n  return draggable.descriptor.droppableId === destination.descriptor.id;\n};\nvar noDisplacedBy = {\n  point: origin,\n  value: 0\n};\nvar emptyGroups = {\n  invisible: {},\n  visible: {},\n  all: []\n};\nvar noImpact = {\n  displaced: emptyGroups,\n  displacedBy: noDisplacedBy,\n  at: null\n};\nvar isWithin = function (lowerBound, upperBound) {\n  return function (value) {\n    return lowerBound <= value && value <= upperBound;\n  };\n};\nvar isPartiallyVisibleThroughFrame = function (frame) {\n  var isWithinVertical = isWithin(frame.top, frame.bottom);\n  var isWithinHorizontal = isWithin(frame.left, frame.right);\n  return function (subject) {\n    var isContained = isWithinVertical(subject.top) && isWithinVertical(subject.bottom) && isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n    if (isContained) {\n      return true;\n    }\n    var isPartiallyVisibleVertically = isWithinVertical(subject.top) || isWithinVertical(subject.bottom);\n    var isPartiallyVisibleHorizontally = isWithinHorizontal(subject.left) || isWithinHorizontal(subject.right);\n    var isPartiallyContained = isPartiallyVisibleVertically && isPartiallyVisibleHorizontally;\n    if (isPartiallyContained) {\n      return true;\n    }\n    var isBiggerVertically = subject.top < frame.top && subject.bottom > frame.bottom;\n    var isBiggerHorizontally = subject.left < frame.left && subject.right > frame.right;\n    var isTargetBiggerThanFrame = isBiggerVertically && isBiggerHorizontally;\n    if (isTargetBiggerThanFrame) {\n      return true;\n    }\n    var isTargetBiggerOnOneAxis = isBiggerVertically && isPartiallyVisibleHorizontally || isBiggerHorizontally && isPartiallyVisibleVertically;\n    return isTargetBiggerOnOneAxis;\n  };\n};\nvar isTotallyVisibleThroughFrame = function (frame) {\n  var isWithinVertical = isWithin(frame.top, frame.bottom);\n  var isWithinHorizontal = isWithin(frame.left, frame.right);\n  return function (subject) {\n    var isContained = isWithinVertical(subject.top) && isWithinVertical(subject.bottom) && isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n    return isContained;\n  };\n};\nvar vertical = {\n  direction: 'vertical',\n  line: 'y',\n  crossAxisLine: 'x',\n  start: 'top',\n  end: 'bottom',\n  size: 'height',\n  crossAxisStart: 'left',\n  crossAxisEnd: 'right',\n  crossAxisSize: 'width'\n};\nvar horizontal = {\n  direction: 'horizontal',\n  line: 'x',\n  crossAxisLine: 'y',\n  start: 'left',\n  end: 'right',\n  size: 'width',\n  crossAxisStart: 'top',\n  crossAxisEnd: 'bottom',\n  crossAxisSize: 'height'\n};\nvar isTotallyVisibleThroughFrameOnAxis = function (axis) {\n  return function (frame) {\n    var isWithinVertical = isWithin(frame.top, frame.bottom);\n    var isWithinHorizontal = isWithin(frame.left, frame.right);\n    return function (subject) {\n      if (axis === vertical) {\n        return isWithinVertical(subject.top) && isWithinVertical(subject.bottom);\n      }\n      return isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n    };\n  };\n};\nvar getDroppableDisplaced = function getDroppableDisplaced(target, destination) {\n  var displacement = destination.frame ? destination.frame.scroll.diff.displacement : origin;\n  return offsetByPosition(target, displacement);\n};\nvar isVisibleInDroppable = function isVisibleInDroppable(target, destination, isVisibleThroughFrameFn) {\n  if (!destination.subject.active) {\n    return false;\n  }\n  return isVisibleThroughFrameFn(destination.subject.active)(target);\n};\nvar isVisibleInViewport = function isVisibleInViewport(target, viewport, isVisibleThroughFrameFn) {\n  return isVisibleThroughFrameFn(viewport)(target);\n};\nvar isVisible = function isVisible(_ref) {\n  var toBeDisplaced = _ref.target,\n    destination = _ref.destination,\n    viewport = _ref.viewport,\n    withDroppableDisplacement = _ref.withDroppableDisplacement,\n    isVisibleThroughFrameFn = _ref.isVisibleThroughFrameFn;\n  var displacedTarget = withDroppableDisplacement ? getDroppableDisplaced(toBeDisplaced, destination) : toBeDisplaced;\n  return isVisibleInDroppable(displacedTarget, destination, isVisibleThroughFrameFn) && isVisibleInViewport(displacedTarget, viewport, isVisibleThroughFrameFn);\n};\nvar isPartiallyVisible = function isPartiallyVisible(args) {\n  return isVisible(_extends({}, args, {\n    isVisibleThroughFrameFn: isPartiallyVisibleThroughFrame\n  }));\n};\nvar isTotallyVisible = function isTotallyVisible(args) {\n  return isVisible(_extends({}, args, {\n    isVisibleThroughFrameFn: isTotallyVisibleThroughFrame\n  }));\n};\nvar isTotallyVisibleOnAxis = function isTotallyVisibleOnAxis(args) {\n  return isVisible(_extends({}, args, {\n    isVisibleThroughFrameFn: isTotallyVisibleThroughFrameOnAxis(args.destination.axis)\n  }));\n};\nvar getShouldAnimate = function getShouldAnimate(id, last, forceShouldAnimate) {\n  if (typeof forceShouldAnimate === 'boolean') {\n    return forceShouldAnimate;\n  }\n  if (!last) {\n    return true;\n  }\n  var invisible = last.invisible,\n    visible = last.visible;\n  if (invisible[id]) {\n    return false;\n  }\n  var previous = visible[id];\n  return previous ? previous.shouldAnimate : true;\n};\nfunction getTarget(draggable, displacedBy) {\n  var marginBox = draggable.page.marginBox;\n  var expandBy = {\n    top: displacedBy.point.y,\n    right: 0,\n    bottom: 0,\n    left: displacedBy.point.x\n  };\n  return getRect(expand(marginBox, expandBy));\n}\nfunction getDisplacementGroups(_ref) {\n  var afterDragging = _ref.afterDragging,\n    destination = _ref.destination,\n    displacedBy = _ref.displacedBy,\n    viewport = _ref.viewport,\n    forceShouldAnimate = _ref.forceShouldAnimate,\n    last = _ref.last;\n  return afterDragging.reduce(function process(groups, draggable) {\n    var target = getTarget(draggable, displacedBy);\n    var id = draggable.descriptor.id;\n    groups.all.push(id);\n    var isVisible = isPartiallyVisible({\n      target: target,\n      destination: destination,\n      viewport: viewport,\n      withDroppableDisplacement: true\n    });\n    if (!isVisible) {\n      groups.invisible[draggable.descriptor.id] = true;\n      return groups;\n    }\n    var shouldAnimate = getShouldAnimate(id, last, forceShouldAnimate);\n    var displacement = {\n      draggableId: id,\n      shouldAnimate: shouldAnimate\n    };\n    groups.visible[id] = displacement;\n    return groups;\n  }, {\n    all: [],\n    visible: {},\n    invisible: {}\n  });\n}\nfunction getIndexOfLastItem(draggables, options) {\n  if (!draggables.length) {\n    return 0;\n  }\n  var indexOfLastItem = draggables[draggables.length - 1].descriptor.index;\n  return options.inHomeList ? indexOfLastItem : indexOfLastItem + 1;\n}\nfunction goAtEnd(_ref) {\n  var insideDestination = _ref.insideDestination,\n    inHomeList = _ref.inHomeList,\n    displacedBy = _ref.displacedBy,\n    destination = _ref.destination;\n  var newIndex = getIndexOfLastItem(insideDestination, {\n    inHomeList: inHomeList\n  });\n  return {\n    displaced: emptyGroups,\n    displacedBy: displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: {\n        droppableId: destination.descriptor.id,\n        index: newIndex\n      }\n    }\n  };\n}\nfunction calculateReorderImpact(_ref2) {\n  var draggable = _ref2.draggable,\n    insideDestination = _ref2.insideDestination,\n    destination = _ref2.destination,\n    viewport = _ref2.viewport,\n    displacedBy = _ref2.displacedBy,\n    last = _ref2.last,\n    index = _ref2.index,\n    forceShouldAnimate = _ref2.forceShouldAnimate;\n  var inHomeList = isHomeOf(draggable, destination);\n  if (index == null) {\n    return goAtEnd({\n      insideDestination: insideDestination,\n      inHomeList: inHomeList,\n      displacedBy: displacedBy,\n      destination: destination\n    });\n  }\n  var match = find(insideDestination, function (item) {\n    return item.descriptor.index === index;\n  });\n  if (!match) {\n    return goAtEnd({\n      insideDestination: insideDestination,\n      inHomeList: inHomeList,\n      displacedBy: displacedBy,\n      destination: destination\n    });\n  }\n  var withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  var sliceFrom = insideDestination.indexOf(match);\n  var impacted = withoutDragging.slice(sliceFrom);\n  var displaced = getDisplacementGroups({\n    afterDragging: impacted,\n    destination: destination,\n    displacedBy: displacedBy,\n    last: last,\n    viewport: viewport.frame,\n    forceShouldAnimate: forceShouldAnimate\n  });\n  return {\n    displaced: displaced,\n    displacedBy: displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: {\n        droppableId: destination.descriptor.id,\n        index: index\n      }\n    }\n  };\n}\nfunction didStartAfterCritical(draggableId, afterCritical) {\n  return Boolean(afterCritical.effected[draggableId]);\n}\nvar fromCombine = function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n    destination = _ref.destination,\n    draggables = _ref.draggables,\n    combine = _ref.combine,\n    afterCritical = _ref.afterCritical;\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n  var combineId = combine.draggableId;\n  var combineWith = draggables[combineId];\n  var combineWithIndex = combineWith.descriptor.index;\n  var didCombineWithStartAfterCritical = didStartAfterCritical(combineId, afterCritical);\n  if (didCombineWithStartAfterCritical) {\n    if (isMovingForward) {\n      return combineWithIndex;\n    }\n    return combineWithIndex - 1;\n  }\n  if (isMovingForward) {\n    return combineWithIndex + 1;\n  }\n  return combineWithIndex;\n};\nvar fromReorder = function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n    isInHomeList = _ref.isInHomeList,\n    insideDestination = _ref.insideDestination,\n    location = _ref.location;\n  if (!insideDestination.length) {\n    return null;\n  }\n  var currentIndex = location.index;\n  var proposedIndex = isMovingForward ? currentIndex + 1 : currentIndex - 1;\n  var firstIndex = insideDestination[0].descriptor.index;\n  var lastIndex = insideDestination[insideDestination.length - 1].descriptor.index;\n  var upperBound = isInHomeList ? lastIndex : lastIndex + 1;\n  if (proposedIndex < firstIndex) {\n    return null;\n  }\n  if (proposedIndex > upperBound) {\n    return null;\n  }\n  return proposedIndex;\n};\nvar moveToNextIndex = function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n    isInHomeList = _ref.isInHomeList,\n    draggable = _ref.draggable,\n    draggables = _ref.draggables,\n    destination = _ref.destination,\n    insideDestination = _ref.insideDestination,\n    previousImpact = _ref.previousImpact,\n    viewport = _ref.viewport,\n    afterCritical = _ref.afterCritical;\n  var wasAt = previousImpact.at;\n  !wasAt ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot move in direction without previous impact location') : invariant(false) : void 0;\n  if (wasAt.type === 'REORDER') {\n    var _newIndex = fromReorder({\n      isMovingForward: isMovingForward,\n      isInHomeList: isInHomeList,\n      location: wasAt.destination,\n      insideDestination: insideDestination\n    });\n    if (_newIndex == null) {\n      return null;\n    }\n    return calculateReorderImpact({\n      draggable: draggable,\n      insideDestination: insideDestination,\n      destination: destination,\n      viewport: viewport,\n      last: previousImpact.displaced,\n      displacedBy: previousImpact.displacedBy,\n      index: _newIndex\n    });\n  }\n  var newIndex = fromCombine({\n    isMovingForward: isMovingForward,\n    destination: destination,\n    displaced: previousImpact.displaced,\n    draggables: draggables,\n    combine: wasAt.combine,\n    afterCritical: afterCritical\n  });\n  if (newIndex == null) {\n    return null;\n  }\n  return calculateReorderImpact({\n    draggable: draggable,\n    insideDestination: insideDestination,\n    destination: destination,\n    viewport: viewport,\n    last: previousImpact.displaced,\n    displacedBy: previousImpact.displacedBy,\n    index: newIndex\n  });\n};\nvar getCombinedItemDisplacement = function (_ref) {\n  var displaced = _ref.displaced,\n    afterCritical = _ref.afterCritical,\n    combineWith = _ref.combineWith,\n    displacedBy = _ref.displacedBy;\n  var isDisplaced = Boolean(displaced.visible[combineWith] || displaced.invisible[combineWith]);\n  if (didStartAfterCritical(combineWith, afterCritical)) {\n    return isDisplaced ? origin : negate(displacedBy.point);\n  }\n  return isDisplaced ? displacedBy.point : origin;\n};\nvar whenCombining = function (_ref) {\n  var afterCritical = _ref.afterCritical,\n    impact = _ref.impact,\n    draggables = _ref.draggables;\n  var combine = tryGetCombine(impact);\n  !combine ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n  var combineWith = combine.draggableId;\n  var center = draggables[combineWith].page.borderBox.center;\n  var displaceBy = getCombinedItemDisplacement({\n    displaced: impact.displaced,\n    afterCritical: afterCritical,\n    combineWith: combineWith,\n    displacedBy: impact.displacedBy\n  });\n  return add(center, displaceBy);\n};\nvar distanceFromStartToBorderBoxCenter = function distanceFromStartToBorderBoxCenter(axis, box) {\n  return box.margin[axis.start] + box.borderBox[axis.size] / 2;\n};\nvar distanceFromEndToBorderBoxCenter = function distanceFromEndToBorderBoxCenter(axis, box) {\n  return box.margin[axis.end] + box.borderBox[axis.size] / 2;\n};\nvar getCrossAxisBorderBoxCenter = function getCrossAxisBorderBoxCenter(axis, target, isMoving) {\n  return target[axis.crossAxisStart] + isMoving.margin[axis.crossAxisStart] + isMoving.borderBox[axis.crossAxisSize] / 2;\n};\nvar goAfter = function goAfter(_ref) {\n  var axis = _ref.axis,\n    moveRelativeTo = _ref.moveRelativeTo,\n    isMoving = _ref.isMoving;\n  return patch(axis.line, moveRelativeTo.marginBox[axis.end] + distanceFromStartToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveRelativeTo.marginBox, isMoving));\n};\nvar goBefore = function goBefore(_ref2) {\n  var axis = _ref2.axis,\n    moveRelativeTo = _ref2.moveRelativeTo,\n    isMoving = _ref2.isMoving;\n  return patch(axis.line, moveRelativeTo.marginBox[axis.start] - distanceFromEndToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveRelativeTo.marginBox, isMoving));\n};\nvar goIntoStart = function goIntoStart(_ref3) {\n  var axis = _ref3.axis,\n    moveInto = _ref3.moveInto,\n    isMoving = _ref3.isMoving;\n  return patch(axis.line, moveInto.contentBox[axis.start] + distanceFromStartToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveInto.contentBox, isMoving));\n};\nvar whenReordering = function (_ref) {\n  var impact = _ref.impact,\n    draggable = _ref.draggable,\n    draggables = _ref.draggables,\n    droppable = _ref.droppable,\n    afterCritical = _ref.afterCritical;\n  var insideDestination = getDraggablesInsideDroppable(droppable.descriptor.id, draggables);\n  var draggablePage = draggable.page;\n  var axis = droppable.axis;\n  if (!insideDestination.length) {\n    return goIntoStart({\n      axis: axis,\n      moveInto: droppable.page,\n      isMoving: draggablePage\n    });\n  }\n  var displaced = impact.displaced,\n    displacedBy = impact.displacedBy;\n  var closestAfter = displaced.all[0];\n  if (closestAfter) {\n    var closest = draggables[closestAfter];\n    if (didStartAfterCritical(closestAfter, afterCritical)) {\n      return goBefore({\n        axis: axis,\n        moveRelativeTo: closest.page,\n        isMoving: draggablePage\n      });\n    }\n    var withDisplacement = offset(closest.page, displacedBy.point);\n    return goBefore({\n      axis: axis,\n      moveRelativeTo: withDisplacement,\n      isMoving: draggablePage\n    });\n  }\n  var last = insideDestination[insideDestination.length - 1];\n  if (last.descriptor.id === draggable.descriptor.id) {\n    return draggablePage.borderBox.center;\n  }\n  if (didStartAfterCritical(last.descriptor.id, afterCritical)) {\n    var page = offset(last.page, negate(afterCritical.displacedBy.point));\n    return goAfter({\n      axis: axis,\n      moveRelativeTo: page,\n      isMoving: draggablePage\n    });\n  }\n  return goAfter({\n    axis: axis,\n    moveRelativeTo: last.page,\n    isMoving: draggablePage\n  });\n};\nvar withDroppableDisplacement = function (droppable, point) {\n  var frame = droppable.frame;\n  if (!frame) {\n    return point;\n  }\n  return add(point, frame.scroll.diff.displacement);\n};\nvar getResultWithoutDroppableDisplacement = function getResultWithoutDroppableDisplacement(_ref) {\n  var impact = _ref.impact,\n    draggable = _ref.draggable,\n    droppable = _ref.droppable,\n    draggables = _ref.draggables,\n    afterCritical = _ref.afterCritical;\n  var original = draggable.page.borderBox.center;\n  var at = impact.at;\n  if (!droppable) {\n    return original;\n  }\n  if (!at) {\n    return original;\n  }\n  if (at.type === 'REORDER') {\n    return whenReordering({\n      impact: impact,\n      draggable: draggable,\n      draggables: draggables,\n      droppable: droppable,\n      afterCritical: afterCritical\n    });\n  }\n  return whenCombining({\n    impact: impact,\n    draggables: draggables,\n    afterCritical: afterCritical\n  });\n};\nvar getPageBorderBoxCenterFromImpact = function (args) {\n  var withoutDisplacement = getResultWithoutDroppableDisplacement(args);\n  var droppable = args.droppable;\n  var withDisplacement = droppable ? withDroppableDisplacement(droppable, withoutDisplacement) : withoutDisplacement;\n  return withDisplacement;\n};\nvar scrollViewport = function (viewport, newScroll) {\n  var diff = subtract(newScroll, viewport.scroll.initial);\n  var displacement = negate(diff);\n  var frame = getRect({\n    top: newScroll.y,\n    bottom: newScroll.y + viewport.frame.height,\n    left: newScroll.x,\n    right: newScroll.x + viewport.frame.width\n  });\n  var updated = {\n    frame: frame,\n    scroll: {\n      initial: viewport.scroll.initial,\n      max: viewport.scroll.max,\n      current: newScroll,\n      diff: {\n        value: diff,\n        displacement: displacement\n      }\n    }\n  };\n  return updated;\n};\nfunction getDraggables(ids, draggables) {\n  return ids.map(function (id) {\n    return draggables[id];\n  });\n}\nfunction tryGetVisible(id, groups) {\n  for (var i = 0; i < groups.length; i++) {\n    var displacement = groups[i].visible[id];\n    if (displacement) {\n      return displacement;\n    }\n  }\n  return null;\n}\nvar speculativelyIncrease = function (_ref) {\n  var impact = _ref.impact,\n    viewport = _ref.viewport,\n    destination = _ref.destination,\n    draggables = _ref.draggables,\n    maxScrollChange = _ref.maxScrollChange;\n  var scrolledViewport = scrollViewport(viewport, add(viewport.scroll.current, maxScrollChange));\n  var scrolledDroppable = destination.frame ? scrollDroppable(destination, add(destination.frame.scroll.current, maxScrollChange)) : destination;\n  var last = impact.displaced;\n  var withViewportScroll = getDisplacementGroups({\n    afterDragging: getDraggables(last.all, draggables),\n    destination: destination,\n    displacedBy: impact.displacedBy,\n    viewport: scrolledViewport.frame,\n    last: last,\n    forceShouldAnimate: false\n  });\n  var withDroppableScroll = getDisplacementGroups({\n    afterDragging: getDraggables(last.all, draggables),\n    destination: scrolledDroppable,\n    displacedBy: impact.displacedBy,\n    viewport: viewport.frame,\n    last: last,\n    forceShouldAnimate: false\n  });\n  var invisible = {};\n  var visible = {};\n  var groups = [last, withViewportScroll, withDroppableScroll];\n  last.all.forEach(function (id) {\n    var displacement = tryGetVisible(id, groups);\n    if (displacement) {\n      visible[id] = displacement;\n      return;\n    }\n    invisible[id] = true;\n  });\n  var newImpact = _extends({}, impact, {\n    displaced: {\n      all: last.all,\n      invisible: invisible,\n      visible: visible\n    }\n  });\n  return newImpact;\n};\nvar withViewportDisplacement = function (viewport, point) {\n  return add(viewport.scroll.diff.displacement, point);\n};\nvar getClientFromPageBorderBoxCenter = function (_ref) {\n  var pageBorderBoxCenter = _ref.pageBorderBoxCenter,\n    draggable = _ref.draggable,\n    viewport = _ref.viewport;\n  var withoutPageScrollChange = withViewportDisplacement(viewport, pageBorderBoxCenter);\n  var offset = subtract(withoutPageScrollChange, draggable.page.borderBox.center);\n  return add(draggable.client.borderBox.center, offset);\n};\nvar isTotallyVisibleInNewLocation = function (_ref) {\n  var draggable = _ref.draggable,\n    destination = _ref.destination,\n    newPageBorderBoxCenter = _ref.newPageBorderBoxCenter,\n    viewport = _ref.viewport,\n    withDroppableDisplacement = _ref.withDroppableDisplacement,\n    _ref$onlyOnMainAxis = _ref.onlyOnMainAxis,\n    onlyOnMainAxis = _ref$onlyOnMainAxis === void 0 ? false : _ref$onlyOnMainAxis;\n  var changeNeeded = subtract(newPageBorderBoxCenter, draggable.page.borderBox.center);\n  var shifted = offsetByPosition(draggable.page.borderBox, changeNeeded);\n  var args = {\n    target: shifted,\n    destination: destination,\n    withDroppableDisplacement: withDroppableDisplacement,\n    viewport: viewport\n  };\n  return onlyOnMainAxis ? isTotallyVisibleOnAxis(args) : isTotallyVisible(args);\n};\nvar moveToNextPlace = function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n    draggable = _ref.draggable,\n    destination = _ref.destination,\n    draggables = _ref.draggables,\n    previousImpact = _ref.previousImpact,\n    viewport = _ref.viewport,\n    previousPageBorderBoxCenter = _ref.previousPageBorderBoxCenter,\n    previousClientSelection = _ref.previousClientSelection,\n    afterCritical = _ref.afterCritical;\n  if (!destination.isEnabled) {\n    return null;\n  }\n  var insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  var isInHomeList = isHomeOf(draggable, destination);\n  var impact = moveToNextCombine({\n    isMovingForward: isMovingForward,\n    draggable: draggable,\n    destination: destination,\n    insideDestination: insideDestination,\n    previousImpact: previousImpact\n  }) || moveToNextIndex({\n    isMovingForward: isMovingForward,\n    isInHomeList: isInHomeList,\n    draggable: draggable,\n    draggables: draggables,\n    destination: destination,\n    insideDestination: insideDestination,\n    previousImpact: previousImpact,\n    viewport: viewport,\n    afterCritical: afterCritical\n  });\n  if (!impact) {\n    return null;\n  }\n  var pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact: impact,\n    draggable: draggable,\n    droppable: destination,\n    draggables: draggables,\n    afterCritical: afterCritical\n  });\n  var isVisibleInNewLocation = isTotallyVisibleInNewLocation({\n    draggable: draggable,\n    destination: destination,\n    newPageBorderBoxCenter: pageBorderBoxCenter,\n    viewport: viewport.frame,\n    withDroppableDisplacement: false,\n    onlyOnMainAxis: true\n  });\n  if (isVisibleInNewLocation) {\n    var clientSelection = getClientFromPageBorderBoxCenter({\n      pageBorderBoxCenter: pageBorderBoxCenter,\n      draggable: draggable,\n      viewport: viewport\n    });\n    return {\n      clientSelection: clientSelection,\n      impact: impact,\n      scrollJumpRequest: null\n    };\n  }\n  var distance = subtract(pageBorderBoxCenter, previousPageBorderBoxCenter);\n  var cautious = speculativelyIncrease({\n    impact: impact,\n    viewport: viewport,\n    destination: destination,\n    draggables: draggables,\n    maxScrollChange: distance\n  });\n  return {\n    clientSelection: previousClientSelection,\n    impact: cautious,\n    scrollJumpRequest: distance\n  };\n};\nvar getKnownActive = function getKnownActive(droppable) {\n  var rect = droppable.subject.active;\n  !rect ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot get clipped area from droppable') : invariant(false) : void 0;\n  return rect;\n};\nvar getBestCrossAxisDroppable = function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n    pageBorderBoxCenter = _ref.pageBorderBoxCenter,\n    source = _ref.source,\n    droppables = _ref.droppables,\n    viewport = _ref.viewport;\n  var active = source.subject.active;\n  if (!active) {\n    return null;\n  }\n  var axis = source.axis;\n  var isBetweenSourceClipped = isWithin(active[axis.start], active[axis.end]);\n  var candidates = toDroppableList(droppables).filter(function (droppable) {\n    return droppable !== source;\n  }).filter(function (droppable) {\n    return droppable.isEnabled;\n  }).filter(function (droppable) {\n    return Boolean(droppable.subject.active);\n  }).filter(function (droppable) {\n    return isPartiallyVisibleThroughFrame(viewport.frame)(getKnownActive(droppable));\n  }).filter(function (droppable) {\n    var activeOfTarget = getKnownActive(droppable);\n    if (isMovingForward) {\n      return active[axis.crossAxisEnd] < activeOfTarget[axis.crossAxisEnd];\n    }\n    return activeOfTarget[axis.crossAxisStart] < active[axis.crossAxisStart];\n  }).filter(function (droppable) {\n    var activeOfTarget = getKnownActive(droppable);\n    var isBetweenDestinationClipped = isWithin(activeOfTarget[axis.start], activeOfTarget[axis.end]);\n    return isBetweenSourceClipped(activeOfTarget[axis.start]) || isBetweenSourceClipped(activeOfTarget[axis.end]) || isBetweenDestinationClipped(active[axis.start]) || isBetweenDestinationClipped(active[axis.end]);\n  }).sort(function (a, b) {\n    var first = getKnownActive(a)[axis.crossAxisStart];\n    var second = getKnownActive(b)[axis.crossAxisStart];\n    if (isMovingForward) {\n      return first - second;\n    }\n    return second - first;\n  }).filter(function (droppable, index, array) {\n    return getKnownActive(droppable)[axis.crossAxisStart] === getKnownActive(array[0])[axis.crossAxisStart];\n  });\n  if (!candidates.length) {\n    return null;\n  }\n  if (candidates.length === 1) {\n    return candidates[0];\n  }\n  var contains = candidates.filter(function (droppable) {\n    var isWithinDroppable = isWithin(getKnownActive(droppable)[axis.start], getKnownActive(droppable)[axis.end]);\n    return isWithinDroppable(pageBorderBoxCenter[axis.line]);\n  });\n  if (contains.length === 1) {\n    return contains[0];\n  }\n  if (contains.length > 1) {\n    return contains.sort(function (a, b) {\n      return getKnownActive(a)[axis.start] - getKnownActive(b)[axis.start];\n    })[0];\n  }\n  return candidates.sort(function (a, b) {\n    var first = closest(pageBorderBoxCenter, getCorners(getKnownActive(a)));\n    var second = closest(pageBorderBoxCenter, getCorners(getKnownActive(b)));\n    if (first !== second) {\n      return first - second;\n    }\n    return getKnownActive(a)[axis.start] - getKnownActive(b)[axis.start];\n  })[0];\n};\nvar getCurrentPageBorderBoxCenter = function getCurrentPageBorderBoxCenter(draggable, afterCritical) {\n  var original = draggable.page.borderBox.center;\n  return didStartAfterCritical(draggable.descriptor.id, afterCritical) ? subtract(original, afterCritical.displacedBy.point) : original;\n};\nvar getCurrentPageBorderBox = function getCurrentPageBorderBox(draggable, afterCritical) {\n  var original = draggable.page.borderBox;\n  return didStartAfterCritical(draggable.descriptor.id, afterCritical) ? offsetByPosition(original, negate(afterCritical.displacedBy.point)) : original;\n};\nvar getClosestDraggable = function (_ref) {\n  var pageBorderBoxCenter = _ref.pageBorderBoxCenter,\n    viewport = _ref.viewport,\n    destination = _ref.destination,\n    insideDestination = _ref.insideDestination,\n    afterCritical = _ref.afterCritical;\n  var sorted = insideDestination.filter(function (draggable) {\n    return isTotallyVisible({\n      target: getCurrentPageBorderBox(draggable, afterCritical),\n      destination: destination,\n      viewport: viewport.frame,\n      withDroppableDisplacement: true\n    });\n  }).sort(function (a, b) {\n    var distanceToA = distance(pageBorderBoxCenter, withDroppableDisplacement(destination, getCurrentPageBorderBoxCenter(a, afterCritical)));\n    var distanceToB = distance(pageBorderBoxCenter, withDroppableDisplacement(destination, getCurrentPageBorderBoxCenter(b, afterCritical)));\n    if (distanceToA < distanceToB) {\n      return -1;\n    }\n    if (distanceToB < distanceToA) {\n      return 1;\n    }\n    return a.descriptor.index - b.descriptor.index;\n  });\n  return sorted[0] || null;\n};\nvar getDisplacedBy = memoizeOne(function getDisplacedBy(axis, displaceBy) {\n  var displacement = displaceBy[axis.line];\n  return {\n    value: displacement,\n    point: patch(axis.line, displacement)\n  };\n});\nvar getRequiredGrowthForPlaceholder = function getRequiredGrowthForPlaceholder(droppable, placeholderSize, draggables) {\n  var axis = droppable.axis;\n  if (droppable.descriptor.mode === 'virtual') {\n    return patch(axis.line, placeholderSize[axis.line]);\n  }\n  var availableSpace = droppable.subject.page.contentBox[axis.size];\n  var insideDroppable = getDraggablesInsideDroppable(droppable.descriptor.id, draggables);\n  var spaceUsed = insideDroppable.reduce(function (sum, dimension) {\n    return sum + dimension.client.marginBox[axis.size];\n  }, 0);\n  var requiredSpace = spaceUsed + placeholderSize[axis.line];\n  var needsToGrowBy = requiredSpace - availableSpace;\n  if (needsToGrowBy <= 0) {\n    return null;\n  }\n  return patch(axis.line, needsToGrowBy);\n};\nvar withMaxScroll = function withMaxScroll(frame, max) {\n  return _extends({}, frame, {\n    scroll: _extends({}, frame.scroll, {\n      max: max\n    })\n  });\n};\nvar addPlaceholder = function addPlaceholder(droppable, draggable, draggables) {\n  var frame = droppable.frame;\n  !!isHomeOf(draggable, droppable) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Should not add placeholder space to home list') : invariant(false) : void 0;\n  !!droppable.subject.withPlaceholder ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot add placeholder size to a subject when it already has one') : invariant(false) : void 0;\n  var placeholderSize = getDisplacedBy(droppable.axis, draggable.displaceBy).point;\n  var requiredGrowth = getRequiredGrowthForPlaceholder(droppable, placeholderSize, draggables);\n  var added = {\n    placeholderSize: placeholderSize,\n    increasedBy: requiredGrowth,\n    oldFrameMaxScroll: droppable.frame ? droppable.frame.scroll.max : null\n  };\n  if (!frame) {\n    var _subject = getSubject({\n      page: droppable.subject.page,\n      withPlaceholder: added,\n      axis: droppable.axis,\n      frame: droppable.frame\n    });\n    return _extends({}, droppable, {\n      subject: _subject\n    });\n  }\n  var maxScroll = requiredGrowth ? add(frame.scroll.max, requiredGrowth) : frame.scroll.max;\n  var newFrame = withMaxScroll(frame, maxScroll);\n  var subject = getSubject({\n    page: droppable.subject.page,\n    withPlaceholder: added,\n    axis: droppable.axis,\n    frame: newFrame\n  });\n  return _extends({}, droppable, {\n    subject: subject,\n    frame: newFrame\n  });\n};\nvar removePlaceholder = function removePlaceholder(droppable) {\n  var added = droppable.subject.withPlaceholder;\n  !added ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot remove placeholder form subject when there was none') : invariant(false) : void 0;\n  var frame = droppable.frame;\n  if (!frame) {\n    var _subject2 = getSubject({\n      page: droppable.subject.page,\n      axis: droppable.axis,\n      frame: null,\n      withPlaceholder: null\n    });\n    return _extends({}, droppable, {\n      subject: _subject2\n    });\n  }\n  var oldMaxScroll = added.oldFrameMaxScroll;\n  !oldMaxScroll ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected droppable with frame to have old max frame scroll when removing placeholder') : invariant(false) : void 0;\n  var newFrame = withMaxScroll(frame, oldMaxScroll);\n  var subject = getSubject({\n    page: droppable.subject.page,\n    axis: droppable.axis,\n    frame: newFrame,\n    withPlaceholder: null\n  });\n  return _extends({}, droppable, {\n    subject: subject,\n    frame: newFrame\n  });\n};\nvar moveToNewDroppable = function (_ref) {\n  var previousPageBorderBoxCenter = _ref.previousPageBorderBoxCenter,\n    moveRelativeTo = _ref.moveRelativeTo,\n    insideDestination = _ref.insideDestination,\n    draggable = _ref.draggable,\n    draggables = _ref.draggables,\n    destination = _ref.destination,\n    viewport = _ref.viewport,\n    afterCritical = _ref.afterCritical;\n  if (!moveRelativeTo) {\n    if (insideDestination.length) {\n      return null;\n    }\n    var proposed = {\n      displaced: emptyGroups,\n      displacedBy: noDisplacedBy,\n      at: {\n        type: 'REORDER',\n        destination: {\n          droppableId: destination.descriptor.id,\n          index: 0\n        }\n      }\n    };\n    var proposedPageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n      impact: proposed,\n      draggable: draggable,\n      droppable: destination,\n      draggables: draggables,\n      afterCritical: afterCritical\n    });\n    var withPlaceholder = isHomeOf(draggable, destination) ? destination : addPlaceholder(destination, draggable, draggables);\n    var isVisibleInNewLocation = isTotallyVisibleInNewLocation({\n      draggable: draggable,\n      destination: withPlaceholder,\n      newPageBorderBoxCenter: proposedPageBorderBoxCenter,\n      viewport: viewport.frame,\n      withDroppableDisplacement: false,\n      onlyOnMainAxis: true\n    });\n    return isVisibleInNewLocation ? proposed : null;\n  }\n  var isGoingBeforeTarget = Boolean(previousPageBorderBoxCenter[destination.axis.line] <= moveRelativeTo.page.borderBox.center[destination.axis.line]);\n  var proposedIndex = function () {\n    var relativeTo = moveRelativeTo.descriptor.index;\n    if (moveRelativeTo.descriptor.id === draggable.descriptor.id) {\n      return relativeTo;\n    }\n    if (isGoingBeforeTarget) {\n      return relativeTo;\n    }\n    return relativeTo + 1;\n  }();\n  var displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  return calculateReorderImpact({\n    draggable: draggable,\n    insideDestination: insideDestination,\n    destination: destination,\n    viewport: viewport,\n    displacedBy: displacedBy,\n    last: emptyGroups,\n    index: proposedIndex\n  });\n};\nvar moveCrossAxis = function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n    previousPageBorderBoxCenter = _ref.previousPageBorderBoxCenter,\n    draggable = _ref.draggable,\n    isOver = _ref.isOver,\n    draggables = _ref.draggables,\n    droppables = _ref.droppables,\n    viewport = _ref.viewport,\n    afterCritical = _ref.afterCritical;\n  var destination = getBestCrossAxisDroppable({\n    isMovingForward: isMovingForward,\n    pageBorderBoxCenter: previousPageBorderBoxCenter,\n    source: isOver,\n    droppables: droppables,\n    viewport: viewport\n  });\n  if (!destination) {\n    return null;\n  }\n  var insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  var moveRelativeTo = getClosestDraggable({\n    pageBorderBoxCenter: previousPageBorderBoxCenter,\n    viewport: viewport,\n    destination: destination,\n    insideDestination: insideDestination,\n    afterCritical: afterCritical\n  });\n  var impact = moveToNewDroppable({\n    previousPageBorderBoxCenter: previousPageBorderBoxCenter,\n    destination: destination,\n    draggable: draggable,\n    draggables: draggables,\n    moveRelativeTo: moveRelativeTo,\n    insideDestination: insideDestination,\n    viewport: viewport,\n    afterCritical: afterCritical\n  });\n  if (!impact) {\n    return null;\n  }\n  var pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact: impact,\n    draggable: draggable,\n    droppable: destination,\n    draggables: draggables,\n    afterCritical: afterCritical\n  });\n  var clientSelection = getClientFromPageBorderBoxCenter({\n    pageBorderBoxCenter: pageBorderBoxCenter,\n    draggable: draggable,\n    viewport: viewport\n  });\n  return {\n    clientSelection: clientSelection,\n    impact: impact,\n    scrollJumpRequest: null\n  };\n};\nvar whatIsDraggedOver = function (impact) {\n  var at = impact.at;\n  if (!at) {\n    return null;\n  }\n  if (at.type === 'REORDER') {\n    return at.destination.droppableId;\n  }\n  return at.combine.droppableId;\n};\nvar getDroppableOver = function getDroppableOver(impact, droppables) {\n  var id = whatIsDraggedOver(impact);\n  return id ? droppables[id] : null;\n};\nvar moveInDirection = function (_ref) {\n  var state = _ref.state,\n    type = _ref.type;\n  var isActuallyOver = getDroppableOver(state.impact, state.dimensions.droppables);\n  var isMainAxisMovementAllowed = Boolean(isActuallyOver);\n  var home = state.dimensions.droppables[state.critical.droppable.id];\n  var isOver = isActuallyOver || home;\n  var direction = isOver.axis.direction;\n  var isMovingOnMainAxis = direction === 'vertical' && (type === 'MOVE_UP' || type === 'MOVE_DOWN') || direction === 'horizontal' && (type === 'MOVE_LEFT' || type === 'MOVE_RIGHT');\n  if (isMovingOnMainAxis && !isMainAxisMovementAllowed) {\n    return null;\n  }\n  var isMovingForward = type === 'MOVE_DOWN' || type === 'MOVE_RIGHT';\n  var draggable = state.dimensions.draggables[state.critical.draggable.id];\n  var previousPageBorderBoxCenter = state.current.page.borderBoxCenter;\n  var _state$dimensions = state.dimensions,\n    draggables = _state$dimensions.draggables,\n    droppables = _state$dimensions.droppables;\n  return isMovingOnMainAxis ? moveToNextPlace({\n    isMovingForward: isMovingForward,\n    previousPageBorderBoxCenter: previousPageBorderBoxCenter,\n    draggable: draggable,\n    destination: isOver,\n    draggables: draggables,\n    viewport: state.viewport,\n    previousClientSelection: state.current.client.selection,\n    previousImpact: state.impact,\n    afterCritical: state.afterCritical\n  }) : moveCrossAxis({\n    isMovingForward: isMovingForward,\n    previousPageBorderBoxCenter: previousPageBorderBoxCenter,\n    draggable: draggable,\n    isOver: isOver,\n    draggables: draggables,\n    droppables: droppables,\n    viewport: state.viewport,\n    afterCritical: state.afterCritical\n  });\n};\nfunction isMovementAllowed(state) {\n  return state.phase === 'DRAGGING' || state.phase === 'COLLECTING';\n}\nfunction isPositionInFrame(frame) {\n  var isWithinVertical = isWithin(frame.top, frame.bottom);\n  var isWithinHorizontal = isWithin(frame.left, frame.right);\n  return function run(point) {\n    return isWithinVertical(point.y) && isWithinHorizontal(point.x);\n  };\n}\nfunction getHasOverlap(first, second) {\n  return first.left < second.right && first.right > second.left && first.top < second.bottom && first.bottom > second.top;\n}\nfunction getFurthestAway(_ref) {\n  var pageBorderBox = _ref.pageBorderBox,\n    draggable = _ref.draggable,\n    candidates = _ref.candidates;\n  var startCenter = draggable.page.borderBox.center;\n  var sorted = candidates.map(function (candidate) {\n    var axis = candidate.axis;\n    var target = patch(candidate.axis.line, pageBorderBox.center[axis.line], candidate.page.borderBox.center[axis.crossAxisLine]);\n    return {\n      id: candidate.descriptor.id,\n      distance: distance(startCenter, target)\n    };\n  }).sort(function (a, b) {\n    return b.distance - a.distance;\n  });\n  return sorted[0] ? sorted[0].id : null;\n}\nfunction getDroppableOver$1(_ref2) {\n  var pageBorderBox = _ref2.pageBorderBox,\n    draggable = _ref2.draggable,\n    droppables = _ref2.droppables;\n  var candidates = toDroppableList(droppables).filter(function (item) {\n    if (!item.isEnabled) {\n      return false;\n    }\n    var active = item.subject.active;\n    if (!active) {\n      return false;\n    }\n    if (!getHasOverlap(pageBorderBox, active)) {\n      return false;\n    }\n    if (isPositionInFrame(active)(pageBorderBox.center)) {\n      return true;\n    }\n    var axis = item.axis;\n    var childCenter = active.center[axis.crossAxisLine];\n    var crossAxisStart = pageBorderBox[axis.crossAxisStart];\n    var crossAxisEnd = pageBorderBox[axis.crossAxisEnd];\n    var isContained = isWithin(active[axis.crossAxisStart], active[axis.crossAxisEnd]);\n    var isStartContained = isContained(crossAxisStart);\n    var isEndContained = isContained(crossAxisEnd);\n    if (!isStartContained && !isEndContained) {\n      return true;\n    }\n    if (isStartContained) {\n      return crossAxisStart < childCenter;\n    }\n    return crossAxisEnd > childCenter;\n  });\n  if (!candidates.length) {\n    return null;\n  }\n  if (candidates.length === 1) {\n    return candidates[0].descriptor.id;\n  }\n  return getFurthestAway({\n    pageBorderBox: pageBorderBox,\n    draggable: draggable,\n    candidates: candidates\n  });\n}\nvar offsetRectByPosition = function offsetRectByPosition(rect, point) {\n  return getRect(offsetByPosition(rect, point));\n};\nvar withDroppableScroll = function (droppable, area) {\n  var frame = droppable.frame;\n  if (!frame) {\n    return area;\n  }\n  return offsetRectByPosition(area, frame.scroll.diff.value);\n};\nfunction getIsDisplaced(_ref) {\n  var displaced = _ref.displaced,\n    id = _ref.id;\n  return Boolean(displaced.visible[id] || displaced.invisible[id]);\n}\nfunction atIndex(_ref) {\n  var draggable = _ref.draggable,\n    closest = _ref.closest,\n    inHomeList = _ref.inHomeList;\n  if (!closest) {\n    return null;\n  }\n  if (!inHomeList) {\n    return closest.descriptor.index;\n  }\n  if (closest.descriptor.index > draggable.descriptor.index) {\n    return closest.descriptor.index - 1;\n  }\n  return closest.descriptor.index;\n}\nvar getReorderImpact = function (_ref2) {\n  var targetRect = _ref2.pageBorderBoxWithDroppableScroll,\n    draggable = _ref2.draggable,\n    destination = _ref2.destination,\n    insideDestination = _ref2.insideDestination,\n    last = _ref2.last,\n    viewport = _ref2.viewport,\n    afterCritical = _ref2.afterCritical;\n  var axis = destination.axis;\n  var displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  var displacement = displacedBy.value;\n  var targetStart = targetRect[axis.start];\n  var targetEnd = targetRect[axis.end];\n  var withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  var closest = find(withoutDragging, function (child) {\n    var id = child.descriptor.id;\n    var childCenter = child.page.borderBox.center[axis.line];\n    var didStartAfterCritical$1 = didStartAfterCritical(id, afterCritical);\n    var isDisplaced = getIsDisplaced({\n      displaced: last,\n      id: id\n    });\n    if (didStartAfterCritical$1) {\n      if (isDisplaced) {\n        return targetEnd <= childCenter;\n      }\n      return targetStart < childCenter - displacement;\n    }\n    if (isDisplaced) {\n      return targetEnd <= childCenter + displacement;\n    }\n    return targetStart < childCenter;\n  });\n  var newIndex = atIndex({\n    draggable: draggable,\n    closest: closest,\n    inHomeList: isHomeOf(draggable, destination)\n  });\n  return calculateReorderImpact({\n    draggable: draggable,\n    insideDestination: insideDestination,\n    destination: destination,\n    viewport: viewport,\n    last: last,\n    displacedBy: displacedBy,\n    index: newIndex\n  });\n};\nvar combineThresholdDivisor = 4;\nvar getCombineImpact = function (_ref) {\n  var draggable = _ref.draggable,\n    targetRect = _ref.pageBorderBoxWithDroppableScroll,\n    previousImpact = _ref.previousImpact,\n    destination = _ref.destination,\n    insideDestination = _ref.insideDestination,\n    afterCritical = _ref.afterCritical;\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n  var axis = destination.axis;\n  var displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  var displacement = displacedBy.value;\n  var targetStart = targetRect[axis.start];\n  var targetEnd = targetRect[axis.end];\n  var withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  var combineWith = find(withoutDragging, function (child) {\n    var id = child.descriptor.id;\n    var childRect = child.page.borderBox;\n    var childSize = childRect[axis.size];\n    var threshold = childSize / combineThresholdDivisor;\n    var didStartAfterCritical$1 = didStartAfterCritical(id, afterCritical);\n    var isDisplaced = getIsDisplaced({\n      displaced: previousImpact.displaced,\n      id: id\n    });\n    if (didStartAfterCritical$1) {\n      if (isDisplaced) {\n        return targetEnd > childRect[axis.start] + threshold && targetEnd < childRect[axis.end] - threshold;\n      }\n      return targetStart > childRect[axis.start] - displacement + threshold && targetStart < childRect[axis.end] - displacement - threshold;\n    }\n    if (isDisplaced) {\n      return targetEnd > childRect[axis.start] + displacement + threshold && targetEnd < childRect[axis.end] + displacement - threshold;\n    }\n    return targetStart > childRect[axis.start] + threshold && targetStart < childRect[axis.end] - threshold;\n  });\n  if (!combineWith) {\n    return null;\n  }\n  var impact = {\n    displacedBy: displacedBy,\n    displaced: previousImpact.displaced,\n    at: {\n      type: 'COMBINE',\n      combine: {\n        draggableId: combineWith.descriptor.id,\n        droppableId: destination.descriptor.id\n      }\n    }\n  };\n  return impact;\n};\nvar getDragImpact = function (_ref) {\n  var pageOffset = _ref.pageOffset,\n    draggable = _ref.draggable,\n    draggables = _ref.draggables,\n    droppables = _ref.droppables,\n    previousImpact = _ref.previousImpact,\n    viewport = _ref.viewport,\n    afterCritical = _ref.afterCritical;\n  var pageBorderBox = offsetRectByPosition(draggable.page.borderBox, pageOffset);\n  var destinationId = getDroppableOver$1({\n    pageBorderBox: pageBorderBox,\n    draggable: draggable,\n    droppables: droppables\n  });\n  if (!destinationId) {\n    return noImpact;\n  }\n  var destination = droppables[destinationId];\n  var insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  var pageBorderBoxWithDroppableScroll = withDroppableScroll(destination, pageBorderBox);\n  return getCombineImpact({\n    pageBorderBoxWithDroppableScroll: pageBorderBoxWithDroppableScroll,\n    draggable: draggable,\n    previousImpact: previousImpact,\n    destination: destination,\n    insideDestination: insideDestination,\n    afterCritical: afterCritical\n  }) || getReorderImpact({\n    pageBorderBoxWithDroppableScroll: pageBorderBoxWithDroppableScroll,\n    draggable: draggable,\n    destination: destination,\n    insideDestination: insideDestination,\n    last: previousImpact.displaced,\n    viewport: viewport,\n    afterCritical: afterCritical\n  });\n};\nvar patchDroppableMap = function (droppables, updated) {\n  var _extends2;\n  return _extends({}, droppables, (_extends2 = {}, _extends2[updated.descriptor.id] = updated, _extends2));\n};\nvar clearUnusedPlaceholder = function clearUnusedPlaceholder(_ref) {\n  var previousImpact = _ref.previousImpact,\n    impact = _ref.impact,\n    droppables = _ref.droppables;\n  var last = whatIsDraggedOver(previousImpact);\n  var now = whatIsDraggedOver(impact);\n  if (!last) {\n    return droppables;\n  }\n  if (last === now) {\n    return droppables;\n  }\n  var lastDroppable = droppables[last];\n  if (!lastDroppable.subject.withPlaceholder) {\n    return droppables;\n  }\n  var updated = removePlaceholder(lastDroppable);\n  return patchDroppableMap(droppables, updated);\n};\nvar recomputePlaceholders = function (_ref2) {\n  var draggable = _ref2.draggable,\n    draggables = _ref2.draggables,\n    droppables = _ref2.droppables,\n    previousImpact = _ref2.previousImpact,\n    impact = _ref2.impact;\n  var cleaned = clearUnusedPlaceholder({\n    previousImpact: previousImpact,\n    impact: impact,\n    droppables: droppables\n  });\n  var isOver = whatIsDraggedOver(impact);\n  if (!isOver) {\n    return cleaned;\n  }\n  var droppable = droppables[isOver];\n  if (isHomeOf(draggable, droppable)) {\n    return cleaned;\n  }\n  if (droppable.subject.withPlaceholder) {\n    return cleaned;\n  }\n  var patched = addPlaceholder(droppable, draggable, draggables);\n  return patchDroppableMap(cleaned, patched);\n};\nvar update = function (_ref) {\n  var state = _ref.state,\n    forcedClientSelection = _ref.clientSelection,\n    forcedDimensions = _ref.dimensions,\n    forcedViewport = _ref.viewport,\n    forcedImpact = _ref.impact,\n    scrollJumpRequest = _ref.scrollJumpRequest;\n  var viewport = forcedViewport || state.viewport;\n  var dimensions = forcedDimensions || state.dimensions;\n  var clientSelection = forcedClientSelection || state.current.client.selection;\n  var offset = subtract(clientSelection, state.initial.client.selection);\n  var client = {\n    offset: offset,\n    selection: clientSelection,\n    borderBoxCenter: add(state.initial.client.borderBoxCenter, offset)\n  };\n  var page = {\n    selection: add(client.selection, viewport.scroll.current),\n    borderBoxCenter: add(client.borderBoxCenter, viewport.scroll.current),\n    offset: add(client.offset, viewport.scroll.diff.value)\n  };\n  var current = {\n    client: client,\n    page: page\n  };\n  if (state.phase === 'COLLECTING') {\n    return _extends({\n      phase: 'COLLECTING'\n    }, state, {\n      dimensions: dimensions,\n      viewport: viewport,\n      current: current\n    });\n  }\n  var draggable = dimensions.draggables[state.critical.draggable.id];\n  var newImpact = forcedImpact || getDragImpact({\n    pageOffset: page.offset,\n    draggable: draggable,\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables,\n    previousImpact: state.impact,\n    viewport: viewport,\n    afterCritical: state.afterCritical\n  });\n  var withUpdatedPlaceholders = recomputePlaceholders({\n    draggable: draggable,\n    impact: newImpact,\n    previousImpact: state.impact,\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables\n  });\n  var result = _extends({}, state, {\n    current: current,\n    dimensions: {\n      draggables: dimensions.draggables,\n      droppables: withUpdatedPlaceholders\n    },\n    impact: newImpact,\n    viewport: viewport,\n    scrollJumpRequest: scrollJumpRequest || null,\n    forceShouldAnimate: scrollJumpRequest ? false : null\n  });\n  return result;\n};\nfunction getDraggables$1(ids, draggables) {\n  return ids.map(function (id) {\n    return draggables[id];\n  });\n}\nvar recompute = function (_ref) {\n  var impact = _ref.impact,\n    viewport = _ref.viewport,\n    draggables = _ref.draggables,\n    destination = _ref.destination,\n    forceShouldAnimate = _ref.forceShouldAnimate;\n  var last = impact.displaced;\n  var afterDragging = getDraggables$1(last.all, draggables);\n  var displaced = getDisplacementGroups({\n    afterDragging: afterDragging,\n    destination: destination,\n    displacedBy: impact.displacedBy,\n    viewport: viewport.frame,\n    forceShouldAnimate: forceShouldAnimate,\n    last: last\n  });\n  return _extends({}, impact, {\n    displaced: displaced\n  });\n};\nvar getClientBorderBoxCenter = function (_ref) {\n  var impact = _ref.impact,\n    draggable = _ref.draggable,\n    droppable = _ref.droppable,\n    draggables = _ref.draggables,\n    viewport = _ref.viewport,\n    afterCritical = _ref.afterCritical;\n  var pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact: impact,\n    draggable: draggable,\n    draggables: draggables,\n    droppable: droppable,\n    afterCritical: afterCritical\n  });\n  return getClientFromPageBorderBoxCenter({\n    pageBorderBoxCenter: pageBorderBoxCenter,\n    draggable: draggable,\n    viewport: viewport\n  });\n};\nvar refreshSnap = function (_ref) {\n  var state = _ref.state,\n    forcedDimensions = _ref.dimensions,\n    forcedViewport = _ref.viewport;\n  !(state.movementMode === 'SNAP') ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n  var needsVisibilityCheck = state.impact;\n  var viewport = forcedViewport || state.viewport;\n  var dimensions = forcedDimensions || state.dimensions;\n  var draggables = dimensions.draggables,\n    droppables = dimensions.droppables;\n  var draggable = draggables[state.critical.draggable.id];\n  var isOver = whatIsDraggedOver(needsVisibilityCheck);\n  !isOver ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Must be over a destination in SNAP movement mode') : invariant(false) : void 0;\n  var destination = droppables[isOver];\n  var impact = recompute({\n    impact: needsVisibilityCheck,\n    viewport: viewport,\n    destination: destination,\n    draggables: draggables\n  });\n  var clientSelection = getClientBorderBoxCenter({\n    impact: impact,\n    draggable: draggable,\n    droppable: destination,\n    draggables: draggables,\n    viewport: viewport,\n    afterCritical: state.afterCritical\n  });\n  return update({\n    impact: impact,\n    clientSelection: clientSelection,\n    state: state,\n    dimensions: dimensions,\n    viewport: viewport\n  });\n};\nvar getHomeLocation = function (descriptor) {\n  return {\n    index: descriptor.index,\n    droppableId: descriptor.droppableId\n  };\n};\nvar getLiftEffect = function (_ref) {\n  var draggable = _ref.draggable,\n    home = _ref.home,\n    draggables = _ref.draggables,\n    viewport = _ref.viewport;\n  var displacedBy = getDisplacedBy(home.axis, draggable.displaceBy);\n  var insideHome = getDraggablesInsideDroppable(home.descriptor.id, draggables);\n  var rawIndex = insideHome.indexOf(draggable);\n  !(rawIndex !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected draggable to be inside home list') : invariant(false) : void 0;\n  var afterDragging = insideHome.slice(rawIndex + 1);\n  var effected = afterDragging.reduce(function (previous, item) {\n    previous[item.descriptor.id] = true;\n    return previous;\n  }, {});\n  var afterCritical = {\n    inVirtualList: home.descriptor.mode === 'virtual',\n    displacedBy: displacedBy,\n    effected: effected\n  };\n  var displaced = getDisplacementGroups({\n    afterDragging: afterDragging,\n    destination: home,\n    displacedBy: displacedBy,\n    last: null,\n    viewport: viewport.frame,\n    forceShouldAnimate: false\n  });\n  var impact = {\n    displaced: displaced,\n    displacedBy: displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: getHomeLocation(draggable.descriptor)\n    }\n  };\n  return {\n    impact: impact,\n    afterCritical: afterCritical\n  };\n};\nvar patchDimensionMap = function (dimensions, updated) {\n  return {\n    draggables: dimensions.draggables,\n    droppables: patchDroppableMap(dimensions.droppables, updated)\n  };\n};\nvar start = function start(key) {\n  if (process.env.NODE_ENV !== 'production') {\n    {\n      return;\n    }\n  }\n};\nvar finish = function finish(key) {\n  if (process.env.NODE_ENV !== 'production') {\n    {\n      return;\n    }\n  }\n};\nvar offsetDraggable = function (_ref) {\n  var draggable = _ref.draggable,\n    offset$1 = _ref.offset,\n    initialWindowScroll = _ref.initialWindowScroll;\n  var client = offset(draggable.client, offset$1);\n  var page = withScroll(client, initialWindowScroll);\n  var moved = _extends({}, draggable, {\n    placeholder: _extends({}, draggable.placeholder, {\n      client: client\n    }),\n    client: client,\n    page: page\n  });\n  return moved;\n};\nvar getFrame = function (droppable) {\n  var frame = droppable.frame;\n  !frame ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected Droppable to have a frame') : invariant(false) : void 0;\n  return frame;\n};\nvar adjustAdditionsForScrollChanges = function (_ref) {\n  var additions = _ref.additions,\n    updatedDroppables = _ref.updatedDroppables,\n    viewport = _ref.viewport;\n  var windowScrollChange = viewport.scroll.diff.value;\n  return additions.map(function (draggable) {\n    var droppableId = draggable.descriptor.droppableId;\n    var modified = updatedDroppables[droppableId];\n    var frame = getFrame(modified);\n    var droppableScrollChange = frame.scroll.diff.value;\n    var totalChange = add(windowScrollChange, droppableScrollChange);\n    var moved = offsetDraggable({\n      draggable: draggable,\n      offset: totalChange,\n      initialWindowScroll: viewport.scroll.initial\n    });\n    return moved;\n  });\n};\nvar publishWhileDraggingInVirtual = function (_ref) {\n  var state = _ref.state,\n    published = _ref.published;\n  start();\n  var withScrollChange = published.modified.map(function (update) {\n    var existing = state.dimensions.droppables[update.droppableId];\n    var scrolled = scrollDroppable(existing, update.scroll);\n    return scrolled;\n  });\n  var droppables = _extends({}, state.dimensions.droppables, {}, toDroppableMap(withScrollChange));\n  var updatedAdditions = toDraggableMap(adjustAdditionsForScrollChanges({\n    additions: published.additions,\n    updatedDroppables: droppables,\n    viewport: state.viewport\n  }));\n  var draggables = _extends({}, state.dimensions.draggables, {}, updatedAdditions);\n  published.removals.forEach(function (id) {\n    delete draggables[id];\n  });\n  var dimensions = {\n    droppables: droppables,\n    draggables: draggables\n  };\n  var wasOverId = whatIsDraggedOver(state.impact);\n  var wasOver = wasOverId ? dimensions.droppables[wasOverId] : null;\n  var draggable = dimensions.draggables[state.critical.draggable.id];\n  var home = dimensions.droppables[state.critical.droppable.id];\n  var _getLiftEffect = getLiftEffect({\n      draggable: draggable,\n      home: home,\n      draggables: draggables,\n      viewport: state.viewport\n    }),\n    onLiftImpact = _getLiftEffect.impact,\n    afterCritical = _getLiftEffect.afterCritical;\n  var previousImpact = wasOver && wasOver.isCombineEnabled ? state.impact : onLiftImpact;\n  var impact = getDragImpact({\n    pageOffset: state.current.page.offset,\n    draggable: dimensions.draggables[state.critical.draggable.id],\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables,\n    previousImpact: previousImpact,\n    viewport: state.viewport,\n    afterCritical: afterCritical\n  });\n  finish();\n  var draggingState = _extends({\n    phase: 'DRAGGING'\n  }, state, {\n    phase: 'DRAGGING',\n    impact: impact,\n    onLiftImpact: onLiftImpact,\n    dimensions: dimensions,\n    afterCritical: afterCritical,\n    forceShouldAnimate: false\n  });\n  if (state.phase === 'COLLECTING') {\n    return draggingState;\n  }\n  var dropPending = _extends({\n    phase: 'DROP_PENDING'\n  }, draggingState, {\n    phase: 'DROP_PENDING',\n    reason: state.reason,\n    isWaiting: false\n  });\n  return dropPending;\n};\nvar isSnapping = function isSnapping(state) {\n  return state.movementMode === 'SNAP';\n};\nvar postDroppableChange = function postDroppableChange(state, updated, isEnabledChanging) {\n  var dimensions = patchDimensionMap(state.dimensions, updated);\n  if (!isSnapping(state) || isEnabledChanging) {\n    return update({\n      state: state,\n      dimensions: dimensions\n    });\n  }\n  return refreshSnap({\n    state: state,\n    dimensions: dimensions\n  });\n};\nfunction removeScrollJumpRequest(state) {\n  if (state.isDragging && state.movementMode === 'SNAP') {\n    return _extends({\n      phase: 'DRAGGING'\n    }, state, {\n      scrollJumpRequest: null\n    });\n  }\n  return state;\n}\nvar idle = {\n  phase: 'IDLE',\n  completed: null,\n  shouldFlush: false\n};\nvar reducer = function (state, action) {\n  if (state === void 0) {\n    state = idle;\n  }\n  if (action.type === 'FLUSH') {\n    return _extends({}, idle, {\n      shouldFlush: true\n    });\n  }\n  if (action.type === 'INITIAL_PUBLISH') {\n    !(state.phase === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'INITIAL_PUBLISH must come after a IDLE phase') : invariant(false) : void 0;\n    var _action$payload = action.payload,\n      critical = _action$payload.critical,\n      clientSelection = _action$payload.clientSelection,\n      viewport = _action$payload.viewport,\n      dimensions = _action$payload.dimensions,\n      movementMode = _action$payload.movementMode;\n    var draggable = dimensions.draggables[critical.draggable.id];\n    var home = dimensions.droppables[critical.droppable.id];\n    var client = {\n      selection: clientSelection,\n      borderBoxCenter: draggable.client.borderBox.center,\n      offset: origin\n    };\n    var initial = {\n      client: client,\n      page: {\n        selection: add(client.selection, viewport.scroll.initial),\n        borderBoxCenter: add(client.selection, viewport.scroll.initial),\n        offset: add(client.selection, viewport.scroll.diff.value)\n      }\n    };\n    var isWindowScrollAllowed = toDroppableList(dimensions.droppables).every(function (item) {\n      return !item.isFixedOnPage;\n    });\n    var _getLiftEffect = getLiftEffect({\n        draggable: draggable,\n        home: home,\n        draggables: dimensions.draggables,\n        viewport: viewport\n      }),\n      impact = _getLiftEffect.impact,\n      afterCritical = _getLiftEffect.afterCritical;\n    var result = {\n      phase: 'DRAGGING',\n      isDragging: true,\n      critical: critical,\n      movementMode: movementMode,\n      dimensions: dimensions,\n      initial: initial,\n      current: initial,\n      isWindowScrollAllowed: isWindowScrollAllowed,\n      impact: impact,\n      afterCritical: afterCritical,\n      onLiftImpact: impact,\n      viewport: viewport,\n      scrollJumpRequest: null,\n      forceShouldAnimate: null\n    };\n    return result;\n  }\n  if (action.type === 'COLLECTION_STARTING') {\n    if (state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Collection cannot start from phase \" + state.phase) : invariant(false) : void 0;\n    var _result = _extends({\n      phase: 'COLLECTING'\n    }, state, {\n      phase: 'COLLECTING'\n    });\n    return _result;\n  }\n  if (action.type === 'PUBLISH_WHILE_DRAGGING') {\n    !(state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Unexpected \" + action.type + \" received in phase \" + state.phase) : invariant(false) : void 0;\n    return publishWhileDraggingInVirtual({\n      state: state,\n      published: action.payload\n    });\n  }\n  if (action.type === 'MOVE') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, action.type + \" not permitted in phase \" + state.phase) : invariant(false) : void 0;\n    var _clientSelection = action.payload.client;\n    if (isEqual(_clientSelection, state.current.client.selection)) {\n      return state;\n    }\n    return update({\n      state: state,\n      clientSelection: _clientSelection,\n      impact: isSnapping(state) ? state.impact : null\n    });\n  }\n  if (action.type === 'UPDATE_DROPPABLE_SCROLL') {\n    if (state.phase === 'DROP_PENDING') {\n      return removeScrollJumpRequest(state);\n    }\n    if (state.phase === 'COLLECTING') {\n      return removeScrollJumpRequest(state);\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, action.type + \" not permitted in phase \" + state.phase) : invariant(false) : void 0;\n    var _action$payload2 = action.payload,\n      id = _action$payload2.id,\n      newScroll = _action$payload2.newScroll;\n    var target = state.dimensions.droppables[id];\n    if (!target) {\n      return state;\n    }\n    var scrolled = scrollDroppable(target, newScroll);\n    return postDroppableChange(state, scrolled, false);\n  }\n  if (action.type === 'UPDATE_DROPPABLE_IS_ENABLED') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Attempting to move in an unsupported phase \" + state.phase) : invariant(false) : void 0;\n    var _action$payload3 = action.payload,\n      _id = _action$payload3.id,\n      isEnabled = _action$payload3.isEnabled;\n    var _target = state.dimensions.droppables[_id];\n    !_target ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot find Droppable[id: \" + _id + \"] to toggle its enabled state\") : invariant(false) : void 0;\n    !(_target.isEnabled !== isEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Trying to set droppable isEnabled to \" + String(isEnabled) + \"\\n      but it is already \" + String(_target.isEnabled)) : invariant(false) : void 0;\n    var updated = _extends({}, _target, {\n      isEnabled: isEnabled\n    });\n    return postDroppableChange(state, updated, true);\n  }\n  if (action.type === 'UPDATE_DROPPABLE_IS_COMBINE_ENABLED') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Attempting to move in an unsupported phase \" + state.phase) : invariant(false) : void 0;\n    var _action$payload4 = action.payload,\n      _id2 = _action$payload4.id,\n      isCombineEnabled = _action$payload4.isCombineEnabled;\n    var _target2 = state.dimensions.droppables[_id2];\n    !_target2 ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot find Droppable[id: \" + _id2 + \"] to toggle its isCombineEnabled state\") : invariant(false) : void 0;\n    !(_target2.isCombineEnabled !== isCombineEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Trying to set droppable isCombineEnabled to \" + String(isCombineEnabled) + \"\\n      but it is already \" + String(_target2.isCombineEnabled)) : invariant(false) : void 0;\n    var _updated = _extends({}, _target2, {\n      isCombineEnabled: isCombineEnabled\n    });\n    return postDroppableChange(state, _updated, true);\n  }\n  if (action.type === 'MOVE_BY_WINDOW_SCROLL') {\n    if (state.phase === 'DROP_PENDING' || state.phase === 'DROP_ANIMATING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot move by window in phase \" + state.phase) : invariant(false) : void 0;\n    !state.isWindowScrollAllowed ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Window scrolling is currently not supported for fixed lists') : invariant(false) : void 0;\n    var _newScroll = action.payload.newScroll;\n    if (isEqual(state.viewport.scroll.current, _newScroll)) {\n      return removeScrollJumpRequest(state);\n    }\n    var _viewport = scrollViewport(state.viewport, _newScroll);\n    if (isSnapping(state)) {\n      return refreshSnap({\n        state: state,\n        viewport: _viewport\n      });\n    }\n    return update({\n      state: state,\n      viewport: _viewport\n    });\n  }\n  if (action.type === 'UPDATE_VIEWPORT_MAX_SCROLL') {\n    if (!isMovementAllowed(state)) {\n      return state;\n    }\n    var maxScroll = action.payload.maxScroll;\n    if (isEqual(maxScroll, state.viewport.scroll.max)) {\n      return state;\n    }\n    var withMaxScroll = _extends({}, state.viewport, {\n      scroll: _extends({}, state.viewport.scroll, {\n        max: maxScroll\n      })\n    });\n    return _extends({\n      phase: 'DRAGGING'\n    }, state, {\n      viewport: withMaxScroll\n    });\n  }\n  if (action.type === 'MOVE_UP' || action.type === 'MOVE_DOWN' || action.type === 'MOVE_LEFT' || action.type === 'MOVE_RIGHT') {\n    if (state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, action.type + \" received while not in DRAGGING phase\") : invariant(false) : void 0;\n    var _result2 = moveInDirection({\n      state: state,\n      type: action.type\n    });\n    if (!_result2) {\n      return state;\n    }\n    return update({\n      state: state,\n      impact: _result2.impact,\n      clientSelection: _result2.clientSelection,\n      scrollJumpRequest: _result2.scrollJumpRequest\n    });\n  }\n  if (action.type === 'DROP_PENDING') {\n    var reason = action.payload.reason;\n    !(state.phase === 'COLLECTING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only move into the DROP_PENDING phase from the COLLECTING phase') : invariant(false) : void 0;\n    var newState = _extends({\n      phase: 'DROP_PENDING'\n    }, state, {\n      phase: 'DROP_PENDING',\n      isWaiting: true,\n      reason: reason\n    });\n    return newState;\n  }\n  if (action.type === 'DROP_ANIMATE') {\n    var _action$payload5 = action.payload,\n      completed = _action$payload5.completed,\n      dropDuration = _action$payload5.dropDuration,\n      newHomeClientOffset = _action$payload5.newHomeClientOffset;\n    !(state.phase === 'DRAGGING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot animate drop from phase \" + state.phase) : invariant(false) : void 0;\n    var _result3 = {\n      phase: 'DROP_ANIMATING',\n      completed: completed,\n      dropDuration: dropDuration,\n      newHomeClientOffset: newHomeClientOffset,\n      dimensions: state.dimensions\n    };\n    return _result3;\n  }\n  if (action.type === 'DROP_COMPLETE') {\n    var _completed = action.payload.completed;\n    return {\n      phase: 'IDLE',\n      completed: _completed,\n      shouldFlush: false\n    };\n  }\n  return state;\n};\nvar beforeInitialCapture = function beforeInitialCapture(args) {\n  return {\n    type: 'BEFORE_INITIAL_CAPTURE',\n    payload: args\n  };\n};\nvar lift = function lift(args) {\n  return {\n    type: 'LIFT',\n    payload: args\n  };\n};\nvar initialPublish = function initialPublish(args) {\n  return {\n    type: 'INITIAL_PUBLISH',\n    payload: args\n  };\n};\nvar publishWhileDragging = function publishWhileDragging(args) {\n  return {\n    type: 'PUBLISH_WHILE_DRAGGING',\n    payload: args\n  };\n};\nvar collectionStarting = function collectionStarting() {\n  return {\n    type: 'COLLECTION_STARTING',\n    payload: null\n  };\n};\nvar updateDroppableScroll = function updateDroppableScroll(args) {\n  return {\n    type: 'UPDATE_DROPPABLE_SCROLL',\n    payload: args\n  };\n};\nvar updateDroppableIsEnabled = function updateDroppableIsEnabled(args) {\n  return {\n    type: 'UPDATE_DROPPABLE_IS_ENABLED',\n    payload: args\n  };\n};\nvar updateDroppableIsCombineEnabled = function updateDroppableIsCombineEnabled(args) {\n  return {\n    type: 'UPDATE_DROPPABLE_IS_COMBINE_ENABLED',\n    payload: args\n  };\n};\nvar move = function move(args) {\n  return {\n    type: 'MOVE',\n    payload: args\n  };\n};\nvar moveByWindowScroll = function moveByWindowScroll(args) {\n  return {\n    type: 'MOVE_BY_WINDOW_SCROLL',\n    payload: args\n  };\n};\nvar updateViewportMaxScroll = function updateViewportMaxScroll(args) {\n  return {\n    type: 'UPDATE_VIEWPORT_MAX_SCROLL',\n    payload: args\n  };\n};\nvar moveUp = function moveUp() {\n  return {\n    type: 'MOVE_UP',\n    payload: null\n  };\n};\nvar moveDown = function moveDown() {\n  return {\n    type: 'MOVE_DOWN',\n    payload: null\n  };\n};\nvar moveRight = function moveRight() {\n  return {\n    type: 'MOVE_RIGHT',\n    payload: null\n  };\n};\nvar moveLeft = function moveLeft() {\n  return {\n    type: 'MOVE_LEFT',\n    payload: null\n  };\n};\nvar flush = function flush() {\n  return {\n    type: 'FLUSH',\n    payload: null\n  };\n};\nvar animateDrop = function animateDrop(args) {\n  return {\n    type: 'DROP_ANIMATE',\n    payload: args\n  };\n};\nvar completeDrop = function completeDrop(args) {\n  return {\n    type: 'DROP_COMPLETE',\n    payload: args\n  };\n};\nvar drop = function drop(args) {\n  return {\n    type: 'DROP',\n    payload: args\n  };\n};\nvar dropPending = function dropPending(args) {\n  return {\n    type: 'DROP_PENDING',\n    payload: args\n  };\n};\nvar dropAnimationFinished = function dropAnimationFinished() {\n  return {\n    type: 'DROP_ANIMATION_FINISHED',\n    payload: null\n  };\n};\nfunction checkIndexes(insideDestination) {\n  if (insideDestination.length <= 1) {\n    return;\n  }\n  var indexes = insideDestination.map(function (d) {\n    return d.descriptor.index;\n  });\n  var errors = {};\n  for (var i = 1; i < indexes.length; i++) {\n    var current = indexes[i];\n    var previous = indexes[i - 1];\n    if (current !== previous + 1) {\n      errors[current] = true;\n    }\n  }\n  if (!Object.keys(errors).length) {\n    return;\n  }\n  var formatted = indexes.map(function (index) {\n    var hasError = Boolean(errors[index]);\n    return hasError ? \"[\\uD83D\\uDD25\" + index + \"]\" : \"\" + index;\n  }).join(', ');\n  process.env.NODE_ENV !== \"production\" ? warning(\"\\n    Detected non-consecutive <Draggable /> indexes.\\n\\n    (This can cause unexpected bugs)\\n\\n    \" + formatted + \"\\n  \") : void 0;\n}\nfunction validateDimensions(critical, dimensions) {\n  if (process.env.NODE_ENV !== 'production') {\n    var insideDestination = getDraggablesInsideDroppable(critical.droppable.id, dimensions.draggables);\n    checkIndexes(insideDestination);\n  }\n}\nvar lift$1 = function (marshal) {\n  return function (_ref) {\n    var getState = _ref.getState,\n      dispatch = _ref.dispatch;\n    return function (next) {\n      return function (action) {\n        if (action.type !== 'LIFT') {\n          next(action);\n          return;\n        }\n        var _action$payload = action.payload,\n          id = _action$payload.id,\n          clientSelection = _action$payload.clientSelection,\n          movementMode = _action$payload.movementMode;\n        var initial = getState();\n        if (initial.phase === 'DROP_ANIMATING') {\n          dispatch(completeDrop({\n            completed: initial.completed\n          }));\n        }\n        !(getState().phase === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Unexpected phase to start a drag') : invariant(false) : void 0;\n        dispatch(flush());\n        dispatch(beforeInitialCapture({\n          draggableId: id,\n          movementMode: movementMode\n        }));\n        var scrollOptions = {\n          shouldPublishImmediately: movementMode === 'SNAP'\n        };\n        var request = {\n          draggableId: id,\n          scrollOptions: scrollOptions\n        };\n        var _marshal$startPublish = marshal.startPublishing(request),\n          critical = _marshal$startPublish.critical,\n          dimensions = _marshal$startPublish.dimensions,\n          viewport = _marshal$startPublish.viewport;\n        validateDimensions(critical, dimensions);\n        dispatch(initialPublish({\n          critical: critical,\n          dimensions: dimensions,\n          clientSelection: clientSelection,\n          movementMode: movementMode,\n          viewport: viewport\n        }));\n      };\n    };\n  };\n};\nvar style = function (marshal) {\n  return function () {\n    return function (next) {\n      return function (action) {\n        if (action.type === 'INITIAL_PUBLISH') {\n          marshal.dragging();\n        }\n        if (action.type === 'DROP_ANIMATE') {\n          marshal.dropping(action.payload.completed.result.reason);\n        }\n        if (action.type === 'FLUSH' || action.type === 'DROP_COMPLETE') {\n          marshal.resting();\n        }\n        next(action);\n      };\n    };\n  };\n};\nvar curves = {\n  outOfTheWay: 'cubic-bezier(0.2, 0, 0, 1)',\n  drop: 'cubic-bezier(.2,1,.1,1)'\n};\nvar combine = {\n  opacity: {\n    drop: 0,\n    combining: 0.7\n  },\n  scale: {\n    drop: 0.75\n  }\n};\nvar timings = {\n  outOfTheWay: 0.2,\n  minDropTime: 0.33,\n  maxDropTime: 0.55\n};\nvar outOfTheWayTiming = timings.outOfTheWay + \"s \" + curves.outOfTheWay;\nvar transitions = {\n  fluid: \"opacity \" + outOfTheWayTiming,\n  snap: \"transform \" + outOfTheWayTiming + \", opacity \" + outOfTheWayTiming,\n  drop: function drop(duration) {\n    var timing = duration + \"s \" + curves.drop;\n    return \"transform \" + timing + \", opacity \" + timing;\n  },\n  outOfTheWay: \"transform \" + outOfTheWayTiming,\n  placeholder: \"height \" + outOfTheWayTiming + \", width \" + outOfTheWayTiming + \", margin \" + outOfTheWayTiming\n};\nvar moveTo = function moveTo(offset) {\n  return isEqual(offset, origin) ? null : \"translate(\" + offset.x + \"px, \" + offset.y + \"px)\";\n};\nvar transforms = {\n  moveTo: moveTo,\n  drop: function drop(offset, isCombining) {\n    var translate = moveTo(offset);\n    if (!translate) {\n      return null;\n    }\n    if (!isCombining) {\n      return translate;\n    }\n    return translate + \" scale(\" + combine.scale.drop + \")\";\n  }\n};\nvar minDropTime = timings.minDropTime,\n  maxDropTime = timings.maxDropTime;\nvar dropTimeRange = maxDropTime - minDropTime;\nvar maxDropTimeAtDistance = 1500;\nvar cancelDropModifier = 0.6;\nvar getDropDuration = function (_ref) {\n  var current = _ref.current,\n    destination = _ref.destination,\n    reason = _ref.reason;\n  var distance$1 = distance(current, destination);\n  if (distance$1 <= 0) {\n    return minDropTime;\n  }\n  if (distance$1 >= maxDropTimeAtDistance) {\n    return maxDropTime;\n  }\n  var percentage = distance$1 / maxDropTimeAtDistance;\n  var duration = minDropTime + dropTimeRange * percentage;\n  var withDuration = reason === 'CANCEL' ? duration * cancelDropModifier : duration;\n  return Number(withDuration.toFixed(2));\n};\nvar getNewHomeClientOffset = function (_ref) {\n  var impact = _ref.impact,\n    draggable = _ref.draggable,\n    dimensions = _ref.dimensions,\n    viewport = _ref.viewport,\n    afterCritical = _ref.afterCritical;\n  var draggables = dimensions.draggables,\n    droppables = dimensions.droppables;\n  var droppableId = whatIsDraggedOver(impact);\n  var destination = droppableId ? droppables[droppableId] : null;\n  var home = droppables[draggable.descriptor.droppableId];\n  var newClientCenter = getClientBorderBoxCenter({\n    impact: impact,\n    draggable: draggable,\n    draggables: draggables,\n    afterCritical: afterCritical,\n    droppable: destination || home,\n    viewport: viewport\n  });\n  var offset = subtract(newClientCenter, draggable.client.borderBox.center);\n  return offset;\n};\nvar getDropImpact = function (_ref) {\n  var draggables = _ref.draggables,\n    reason = _ref.reason,\n    lastImpact = _ref.lastImpact,\n    home = _ref.home,\n    viewport = _ref.viewport,\n    onLiftImpact = _ref.onLiftImpact;\n  if (!lastImpact.at || reason !== 'DROP') {\n    var recomputedHomeImpact = recompute({\n      draggables: draggables,\n      impact: onLiftImpact,\n      destination: home,\n      viewport: viewport,\n      forceShouldAnimate: true\n    });\n    return {\n      impact: recomputedHomeImpact,\n      didDropInsideDroppable: false\n    };\n  }\n  if (lastImpact.at.type === 'REORDER') {\n    return {\n      impact: lastImpact,\n      didDropInsideDroppable: true\n    };\n  }\n  var withoutMovement = _extends({}, lastImpact, {\n    displaced: emptyGroups\n  });\n  return {\n    impact: withoutMovement,\n    didDropInsideDroppable: true\n  };\n};\nvar drop$1 = function (_ref) {\n  var getState = _ref.getState,\n    dispatch = _ref.dispatch;\n  return function (next) {\n    return function (action) {\n      if (action.type !== 'DROP') {\n        next(action);\n        return;\n      }\n      var state = getState();\n      var reason = action.payload.reason;\n      if (state.phase === 'COLLECTING') {\n        dispatch(dropPending({\n          reason: reason\n        }));\n        return;\n      }\n      if (state.phase === 'IDLE') {\n        return;\n      }\n      var isWaitingForDrop = state.phase === 'DROP_PENDING' && state.isWaiting;\n      !!isWaitingForDrop ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'A DROP action occurred while DROP_PENDING and still waiting') : invariant(false) : void 0;\n      !(state.phase === 'DRAGGING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot drop in phase: \" + state.phase) : invariant(false) : void 0;\n      var critical = state.critical;\n      var dimensions = state.dimensions;\n      var draggable = dimensions.draggables[state.critical.draggable.id];\n      var _getDropImpact = getDropImpact({\n          reason: reason,\n          lastImpact: state.impact,\n          afterCritical: state.afterCritical,\n          onLiftImpact: state.onLiftImpact,\n          home: state.dimensions.droppables[state.critical.droppable.id],\n          viewport: state.viewport,\n          draggables: state.dimensions.draggables\n        }),\n        impact = _getDropImpact.impact,\n        didDropInsideDroppable = _getDropImpact.didDropInsideDroppable;\n      var destination = didDropInsideDroppable ? tryGetDestination(impact) : null;\n      var combine = didDropInsideDroppable ? tryGetCombine(impact) : null;\n      var source = {\n        index: critical.draggable.index,\n        droppableId: critical.droppable.id\n      };\n      var result = {\n        draggableId: draggable.descriptor.id,\n        type: draggable.descriptor.type,\n        source: source,\n        reason: reason,\n        mode: state.movementMode,\n        destination: destination,\n        combine: combine\n      };\n      var newHomeClientOffset = getNewHomeClientOffset({\n        impact: impact,\n        draggable: draggable,\n        dimensions: dimensions,\n        viewport: state.viewport,\n        afterCritical: state.afterCritical\n      });\n      var completed = {\n        critical: state.critical,\n        afterCritical: state.afterCritical,\n        result: result,\n        impact: impact\n      };\n      var isAnimationRequired = !isEqual(state.current.client.offset, newHomeClientOffset) || Boolean(result.combine);\n      if (!isAnimationRequired) {\n        dispatch(completeDrop({\n          completed: completed\n        }));\n        return;\n      }\n      var dropDuration = getDropDuration({\n        current: state.current.client.offset,\n        destination: newHomeClientOffset,\n        reason: reason\n      });\n      var args = {\n        newHomeClientOffset: newHomeClientOffset,\n        dropDuration: dropDuration,\n        completed: completed\n      };\n      dispatch(animateDrop(args));\n    };\n  };\n};\nvar getWindowScroll = function () {\n  return {\n    x: window.pageXOffset,\n    y: window.pageYOffset\n  };\n};\nfunction getWindowScrollBinding(update) {\n  return {\n    eventName: 'scroll',\n    options: {\n      passive: true,\n      capture: false\n    },\n    fn: function fn(event) {\n      if (event.target !== window && event.target !== window.document) {\n        return;\n      }\n      update();\n    }\n  };\n}\nfunction getScrollListener(_ref) {\n  var onWindowScroll = _ref.onWindowScroll;\n  function updateScroll() {\n    onWindowScroll(getWindowScroll());\n  }\n  var scheduled = rafSchd(updateScroll);\n  var binding = getWindowScrollBinding(scheduled);\n  var unbind = noop;\n  function isActive() {\n    return unbind !== noop;\n  }\n  function start() {\n    !!isActive() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start scroll listener when already active') : invariant(false) : void 0;\n    unbind = bindEvents(window, [binding]);\n  }\n  function stop() {\n    !isActive() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop scroll listener when not active') : invariant(false) : void 0;\n    scheduled.cancel();\n    unbind();\n    unbind = noop;\n  }\n  return {\n    start: start,\n    stop: stop,\n    isActive: isActive\n  };\n}\nvar shouldEnd = function shouldEnd(action) {\n  return action.type === 'DROP_COMPLETE' || action.type === 'DROP_ANIMATE' || action.type === 'FLUSH';\n};\nvar scrollListener = function (store) {\n  var listener = getScrollListener({\n    onWindowScroll: function onWindowScroll(newScroll) {\n      store.dispatch(moveByWindowScroll({\n        newScroll: newScroll\n      }));\n    }\n  });\n  return function (next) {\n    return function (action) {\n      if (!listener.isActive() && action.type === 'INITIAL_PUBLISH') {\n        listener.start();\n      }\n      if (listener.isActive() && shouldEnd(action)) {\n        listener.stop();\n      }\n      next(action);\n    };\n  };\n};\nvar getExpiringAnnounce = function (announce) {\n  var wasCalled = false;\n  var isExpired = false;\n  var timeoutId = setTimeout(function () {\n    isExpired = true;\n  });\n  var result = function result(message) {\n    if (wasCalled) {\n      process.env.NODE_ENV !== \"production\" ? warning('Announcement already made. Not making a second announcement') : void 0;\n      return;\n    }\n    if (isExpired) {\n      process.env.NODE_ENV !== \"production\" ? warning(\"\\n        Announcements cannot be made asynchronously.\\n        Default message has already been announced.\\n      \") : void 0;\n      return;\n    }\n    wasCalled = true;\n    announce(message);\n    clearTimeout(timeoutId);\n  };\n  result.wasCalled = function () {\n    return wasCalled;\n  };\n  return result;\n};\nvar getAsyncMarshal = function () {\n  var entries = [];\n  var execute = function execute(timerId) {\n    var index = findIndex(entries, function (item) {\n      return item.timerId === timerId;\n    });\n    !(index !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find timer') : invariant(false) : void 0;\n    var _entries$splice = entries.splice(index, 1),\n      entry = _entries$splice[0];\n    entry.callback();\n  };\n  var add = function add(fn) {\n    var timerId = setTimeout(function () {\n      return execute(timerId);\n    });\n    var entry = {\n      timerId: timerId,\n      callback: fn\n    };\n    entries.push(entry);\n  };\n  var flush = function flush() {\n    if (!entries.length) {\n      return;\n    }\n    var shallow = [].concat(entries);\n    entries.length = 0;\n    shallow.forEach(function (entry) {\n      clearTimeout(entry.timerId);\n      entry.callback();\n    });\n  };\n  return {\n    add: add,\n    flush: flush\n  };\n};\nvar areLocationsEqual = function areLocationsEqual(first, second) {\n  if (first == null && second == null) {\n    return true;\n  }\n  if (first == null || second == null) {\n    return false;\n  }\n  return first.droppableId === second.droppableId && first.index === second.index;\n};\nvar isCombineEqual = function isCombineEqual(first, second) {\n  if (first == null && second == null) {\n    return true;\n  }\n  if (first == null || second == null) {\n    return false;\n  }\n  return first.draggableId === second.draggableId && first.droppableId === second.droppableId;\n};\nvar isCriticalEqual = function isCriticalEqual(first, second) {\n  if (first === second) {\n    return true;\n  }\n  var isDraggableEqual = first.draggable.id === second.draggable.id && first.draggable.droppableId === second.draggable.droppableId && first.draggable.type === second.draggable.type && first.draggable.index === second.draggable.index;\n  var isDroppableEqual = first.droppable.id === second.droppable.id && first.droppable.type === second.droppable.type;\n  return isDraggableEqual && isDroppableEqual;\n};\nvar withTimings = function withTimings(key, fn) {\n  start();\n  fn();\n  finish();\n};\nvar getDragStart = function getDragStart(critical, mode) {\n  return {\n    draggableId: critical.draggable.id,\n    type: critical.droppable.type,\n    source: {\n      droppableId: critical.droppable.id,\n      index: critical.draggable.index\n    },\n    mode: mode\n  };\n};\nvar execute = function execute(responder, data, announce, getDefaultMessage) {\n  if (!responder) {\n    announce(getDefaultMessage(data));\n    return;\n  }\n  var willExpire = getExpiringAnnounce(announce);\n  var provided = {\n    announce: willExpire\n  };\n  responder(data, provided);\n  if (!willExpire.wasCalled()) {\n    announce(getDefaultMessage(data));\n  }\n};\nvar getPublisher = function (getResponders, announce) {\n  var asyncMarshal = getAsyncMarshal();\n  var dragging = null;\n  var beforeCapture = function beforeCapture(draggableId, mode) {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeCapture as a drag start has already been published') : invariant(false) : void 0;\n    withTimings('onBeforeCapture', function () {\n      var fn = getResponders().onBeforeCapture;\n      if (fn) {\n        var before = {\n          draggableId: draggableId,\n          mode: mode\n        };\n        fn(before);\n      }\n    });\n  };\n  var beforeStart = function beforeStart(critical, mode) {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeDragStart as a drag start has already been published') : invariant(false) : void 0;\n    withTimings('onBeforeDragStart', function () {\n      var fn = getResponders().onBeforeDragStart;\n      if (fn) {\n        fn(getDragStart(critical, mode));\n      }\n    });\n  };\n  var start = function start(critical, mode) {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeDragStart as a drag start has already been published') : invariant(false) : void 0;\n    var data = getDragStart(critical, mode);\n    dragging = {\n      mode: mode,\n      lastCritical: critical,\n      lastLocation: data.source,\n      lastCombine: null\n    };\n    asyncMarshal.add(function () {\n      withTimings('onDragStart', function () {\n        return execute(getResponders().onDragStart, data, announce, preset.onDragStart);\n      });\n    });\n  };\n  var update = function update(critical, impact) {\n    var location = tryGetDestination(impact);\n    var combine = tryGetCombine(impact);\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onDragMove when onDragStart has not been called') : invariant(false) : void 0;\n    var hasCriticalChanged = !isCriticalEqual(critical, dragging.lastCritical);\n    if (hasCriticalChanged) {\n      dragging.lastCritical = critical;\n    }\n    var hasLocationChanged = !areLocationsEqual(dragging.lastLocation, location);\n    if (hasLocationChanged) {\n      dragging.lastLocation = location;\n    }\n    var hasGroupingChanged = !isCombineEqual(dragging.lastCombine, combine);\n    if (hasGroupingChanged) {\n      dragging.lastCombine = combine;\n    }\n    if (!hasCriticalChanged && !hasLocationChanged && !hasGroupingChanged) {\n      return;\n    }\n    var data = _extends({}, getDragStart(critical, dragging.mode), {\n      combine: combine,\n      destination: location\n    });\n    asyncMarshal.add(function () {\n      withTimings('onDragUpdate', function () {\n        return execute(getResponders().onDragUpdate, data, announce, preset.onDragUpdate);\n      });\n    });\n  };\n  var flush = function flush() {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only flush responders while dragging') : invariant(false) : void 0;\n    asyncMarshal.flush();\n  };\n  var drop = function drop(result) {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onDragEnd when there is no matching onDragStart') : invariant(false) : void 0;\n    dragging = null;\n    withTimings('onDragEnd', function () {\n      return execute(getResponders().onDragEnd, result, announce, preset.onDragEnd);\n    });\n  };\n  var abort = function abort() {\n    if (!dragging) {\n      return;\n    }\n    var result = _extends({}, getDragStart(dragging.lastCritical, dragging.mode), {\n      combine: null,\n      destination: null,\n      reason: 'CANCEL'\n    });\n    drop(result);\n  };\n  return {\n    beforeCapture: beforeCapture,\n    beforeStart: beforeStart,\n    start: start,\n    update: update,\n    flush: flush,\n    drop: drop,\n    abort: abort\n  };\n};\nvar responders = function (getResponders, announce) {\n  var publisher = getPublisher(getResponders, announce);\n  return function (store) {\n    return function (next) {\n      return function (action) {\n        if (action.type === 'BEFORE_INITIAL_CAPTURE') {\n          publisher.beforeCapture(action.payload.draggableId, action.payload.movementMode);\n          return;\n        }\n        if (action.type === 'INITIAL_PUBLISH') {\n          var critical = action.payload.critical;\n          publisher.beforeStart(critical, action.payload.movementMode);\n          next(action);\n          publisher.start(critical, action.payload.movementMode);\n          return;\n        }\n        if (action.type === 'DROP_COMPLETE') {\n          var result = action.payload.completed.result;\n          publisher.flush();\n          next(action);\n          publisher.drop(result);\n          return;\n        }\n        next(action);\n        if (action.type === 'FLUSH') {\n          publisher.abort();\n          return;\n        }\n        var state = store.getState();\n        if (state.phase === 'DRAGGING') {\n          publisher.update(state.critical, state.impact);\n        }\n      };\n    };\n  };\n};\nvar dropAnimationFinish = function (store) {\n  return function (next) {\n    return function (action) {\n      if (action.type !== 'DROP_ANIMATION_FINISHED') {\n        next(action);\n        return;\n      }\n      var state = store.getState();\n      !(state.phase === 'DROP_ANIMATING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot finish a drop animating when no drop is occurring') : invariant(false) : void 0;\n      store.dispatch(completeDrop({\n        completed: state.completed\n      }));\n    };\n  };\n};\nvar dropAnimationFlushOnScroll = function (store) {\n  var unbind = null;\n  var frameId = null;\n  function clear() {\n    if (frameId) {\n      cancelAnimationFrame(frameId);\n      frameId = null;\n    }\n    if (unbind) {\n      unbind();\n      unbind = null;\n    }\n  }\n  return function (next) {\n    return function (action) {\n      if (action.type === 'FLUSH' || action.type === 'DROP_COMPLETE' || action.type === 'DROP_ANIMATION_FINISHED') {\n        clear();\n      }\n      next(action);\n      if (action.type !== 'DROP_ANIMATE') {\n        return;\n      }\n      var binding = {\n        eventName: 'scroll',\n        options: {\n          capture: true,\n          passive: false,\n          once: true\n        },\n        fn: function flushDropAnimation() {\n          var state = store.getState();\n          if (state.phase === 'DROP_ANIMATING') {\n            store.dispatch(dropAnimationFinished());\n          }\n        }\n      };\n      frameId = requestAnimationFrame(function () {\n        frameId = null;\n        unbind = bindEvents(window, [binding]);\n      });\n    };\n  };\n};\nvar dimensionMarshalStopper = function (marshal) {\n  return function () {\n    return function (next) {\n      return function (action) {\n        if (action.type === 'DROP_COMPLETE' || action.type === 'FLUSH' || action.type === 'DROP_ANIMATE') {\n          marshal.stopPublishing();\n        }\n        next(action);\n      };\n    };\n  };\n};\nvar focus = function (marshal) {\n  var isWatching = false;\n  return function () {\n    return function (next) {\n      return function (action) {\n        if (action.type === 'INITIAL_PUBLISH') {\n          isWatching = true;\n          marshal.tryRecordFocus(action.payload.critical.draggable.id);\n          next(action);\n          marshal.tryRestoreFocusRecorded();\n          return;\n        }\n        next(action);\n        if (!isWatching) {\n          return;\n        }\n        if (action.type === 'FLUSH') {\n          isWatching = false;\n          marshal.tryRestoreFocusRecorded();\n          return;\n        }\n        if (action.type === 'DROP_COMPLETE') {\n          isWatching = false;\n          var result = action.payload.completed.result;\n          if (result.combine) {\n            marshal.tryShiftRecord(result.draggableId, result.combine.draggableId);\n          }\n          marshal.tryRestoreFocusRecorded();\n        }\n      };\n    };\n  };\n};\nvar shouldStop = function shouldStop(action) {\n  return action.type === 'DROP_COMPLETE' || action.type === 'DROP_ANIMATE' || action.type === 'FLUSH';\n};\nvar autoScroll = function (autoScroller) {\n  return function (store) {\n    return function (next) {\n      return function (action) {\n        if (shouldStop(action)) {\n          autoScroller.stop();\n          next(action);\n          return;\n        }\n        if (action.type === 'INITIAL_PUBLISH') {\n          next(action);\n          var state = store.getState();\n          !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected phase to be DRAGGING after INITIAL_PUBLISH') : invariant(false) : void 0;\n          autoScroller.start(state);\n          return;\n        }\n        next(action);\n        autoScroller.scroll(store.getState());\n      };\n    };\n  };\n};\nvar pendingDrop = function (store) {\n  return function (next) {\n    return function (action) {\n      next(action);\n      if (action.type !== 'PUBLISH_WHILE_DRAGGING') {\n        return;\n      }\n      var postActionState = store.getState();\n      if (postActionState.phase !== 'DROP_PENDING') {\n        return;\n      }\n      if (postActionState.isWaiting) {\n        return;\n      }\n      store.dispatch(drop({\n        reason: postActionState.reason\n      }));\n    };\n  };\n};\nvar composeEnhancers = process.env.NODE_ENV !== 'production' && typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({\n  name: 'react-beautiful-dnd'\n}) : compose;\nvar createStore = function (_ref) {\n  var dimensionMarshal = _ref.dimensionMarshal,\n    focusMarshal = _ref.focusMarshal,\n    styleMarshal = _ref.styleMarshal,\n    getResponders = _ref.getResponders,\n    announce = _ref.announce,\n    autoScroller = _ref.autoScroller;\n  return createStore$1(reducer, composeEnhancers(applyMiddleware(style(styleMarshal), dimensionMarshalStopper(dimensionMarshal), lift$1(dimensionMarshal), drop$1, dropAnimationFinish, dropAnimationFlushOnScroll, pendingDrop, autoScroll(autoScroller), scrollListener, focus(focusMarshal), responders(getResponders, announce))));\n};\nvar clean$1 = function clean() {\n  return {\n    additions: {},\n    removals: {},\n    modified: {}\n  };\n};\nfunction createPublisher(_ref) {\n  var registry = _ref.registry,\n    callbacks = _ref.callbacks;\n  var staging = clean$1();\n  var frameId = null;\n  var collect = function collect() {\n    if (frameId) {\n      return;\n    }\n    callbacks.collectionStarting();\n    frameId = requestAnimationFrame(function () {\n      frameId = null;\n      start();\n      var _staging = staging,\n        additions = _staging.additions,\n        removals = _staging.removals,\n        modified = _staging.modified;\n      var added = Object.keys(additions).map(function (id) {\n        return registry.draggable.getById(id).getDimension(origin);\n      }).sort(function (a, b) {\n        return a.descriptor.index - b.descriptor.index;\n      });\n      var updated = Object.keys(modified).map(function (id) {\n        var entry = registry.droppable.getById(id);\n        var scroll = entry.callbacks.getScrollWhileDragging();\n        return {\n          droppableId: id,\n          scroll: scroll\n        };\n      });\n      var result = {\n        additions: added,\n        removals: Object.keys(removals),\n        modified: updated\n      };\n      staging = clean$1();\n      finish();\n      callbacks.publish(result);\n    });\n  };\n  var add = function add(entry) {\n    var id = entry.descriptor.id;\n    staging.additions[id] = entry;\n    staging.modified[entry.descriptor.droppableId] = true;\n    if (staging.removals[id]) {\n      delete staging.removals[id];\n    }\n    collect();\n  };\n  var remove = function remove(entry) {\n    var descriptor = entry.descriptor;\n    staging.removals[descriptor.id] = true;\n    staging.modified[descriptor.droppableId] = true;\n    if (staging.additions[descriptor.id]) {\n      delete staging.additions[descriptor.id];\n    }\n    collect();\n  };\n  var stop = function stop() {\n    if (!frameId) {\n      return;\n    }\n    cancelAnimationFrame(frameId);\n    frameId = null;\n    staging = clean$1();\n  };\n  return {\n    add: add,\n    remove: remove,\n    stop: stop\n  };\n}\nvar getMaxScroll = function (_ref) {\n  var scrollHeight = _ref.scrollHeight,\n    scrollWidth = _ref.scrollWidth,\n    height = _ref.height,\n    width = _ref.width;\n  var maxScroll = subtract({\n    x: scrollWidth,\n    y: scrollHeight\n  }, {\n    x: width,\n    y: height\n  });\n  var adjustedMaxScroll = {\n    x: Math.max(0, maxScroll.x),\n    y: Math.max(0, maxScroll.y)\n  };\n  return adjustedMaxScroll;\n};\nvar getDocumentElement = function () {\n  var doc = document.documentElement;\n  !doc ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find document.documentElement') : invariant(false) : void 0;\n  return doc;\n};\nvar getMaxWindowScroll = function () {\n  var doc = getDocumentElement();\n  var maxScroll = getMaxScroll({\n    scrollHeight: doc.scrollHeight,\n    scrollWidth: doc.scrollWidth,\n    width: doc.clientWidth,\n    height: doc.clientHeight\n  });\n  return maxScroll;\n};\nvar getViewport = function () {\n  var scroll = getWindowScroll();\n  var maxScroll = getMaxWindowScroll();\n  var top = scroll.y;\n  var left = scroll.x;\n  var doc = getDocumentElement();\n  var width = doc.clientWidth;\n  var height = doc.clientHeight;\n  var right = left + width;\n  var bottom = top + height;\n  var frame = getRect({\n    top: top,\n    left: left,\n    right: right,\n    bottom: bottom\n  });\n  var viewport = {\n    frame: frame,\n    scroll: {\n      initial: scroll,\n      current: scroll,\n      max: maxScroll,\n      diff: {\n        value: origin,\n        displacement: origin\n      }\n    }\n  };\n  return viewport;\n};\nvar getInitialPublish = function (_ref) {\n  var critical = _ref.critical,\n    scrollOptions = _ref.scrollOptions,\n    registry = _ref.registry;\n  start();\n  var viewport = getViewport();\n  var windowScroll = viewport.scroll.current;\n  var home = critical.droppable;\n  var droppables = registry.droppable.getAllByType(home.type).map(function (entry) {\n    return entry.callbacks.getDimensionAndWatchScroll(windowScroll, scrollOptions);\n  });\n  var draggables = registry.draggable.getAllByType(critical.draggable.type).map(function (entry) {\n    return entry.getDimension(windowScroll);\n  });\n  var dimensions = {\n    draggables: toDraggableMap(draggables),\n    droppables: toDroppableMap(droppables)\n  };\n  finish();\n  var result = {\n    dimensions: dimensions,\n    critical: critical,\n    viewport: viewport\n  };\n  return result;\n};\nfunction shouldPublishUpdate(registry, dragging, entry) {\n  if (entry.descriptor.id === dragging.id) {\n    return false;\n  }\n  if (entry.descriptor.type !== dragging.type) {\n    return false;\n  }\n  var home = registry.droppable.getById(entry.descriptor.droppableId);\n  if (home.descriptor.mode !== 'virtual') {\n    process.env.NODE_ENV !== \"production\" ? warning(\"\\n      You are attempting to add or remove a Draggable [id: \" + entry.descriptor.id + \"]\\n      while a drag is occurring. This is only supported for virtual lists.\\n\\n      See https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/patterns/virtual-lists.md\\n    \") : void 0;\n    return false;\n  }\n  return true;\n}\nvar createDimensionMarshal = function (registry, callbacks) {\n  var collection = null;\n  var publisher = createPublisher({\n    callbacks: {\n      publish: callbacks.publishWhileDragging,\n      collectionStarting: callbacks.collectionStarting\n    },\n    registry: registry\n  });\n  var updateDroppableIsEnabled = function updateDroppableIsEnabled(id, isEnabled) {\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot update is enabled flag of Droppable \" + id + \" as it is not registered\") : invariant(false) : void 0;\n    if (!collection) {\n      return;\n    }\n    callbacks.updateDroppableIsEnabled({\n      id: id,\n      isEnabled: isEnabled\n    });\n  };\n  var updateDroppableIsCombineEnabled = function updateDroppableIsCombineEnabled(id, isCombineEnabled) {\n    if (!collection) {\n      return;\n    }\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot update isCombineEnabled flag of Droppable \" + id + \" as it is not registered\") : invariant(false) : void 0;\n    callbacks.updateDroppableIsCombineEnabled({\n      id: id,\n      isCombineEnabled: isCombineEnabled\n    });\n  };\n  var updateDroppableScroll = function updateDroppableScroll(id, newScroll) {\n    if (!collection) {\n      return;\n    }\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot update the scroll on Droppable \" + id + \" as it is not registered\") : invariant(false) : void 0;\n    callbacks.updateDroppableScroll({\n      id: id,\n      newScroll: newScroll\n    });\n  };\n  var scrollDroppable = function scrollDroppable(id, change) {\n    if (!collection) {\n      return;\n    }\n    registry.droppable.getById(id).callbacks.scroll(change);\n  };\n  var stopPublishing = function stopPublishing() {\n    if (!collection) {\n      return;\n    }\n    publisher.stop();\n    var home = collection.critical.droppable;\n    registry.droppable.getAllByType(home.type).forEach(function (entry) {\n      return entry.callbacks.dragStopped();\n    });\n    collection.unsubscribe();\n    collection = null;\n  };\n  var subscriber = function subscriber(event) {\n    !collection ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Should only be subscribed when a collection is occurring') : invariant(false) : void 0;\n    var dragging = collection.critical.draggable;\n    if (event.type === 'ADDITION') {\n      if (shouldPublishUpdate(registry, dragging, event.value)) {\n        publisher.add(event.value);\n      }\n    }\n    if (event.type === 'REMOVAL') {\n      if (shouldPublishUpdate(registry, dragging, event.value)) {\n        publisher.remove(event.value);\n      }\n    }\n  };\n  var startPublishing = function startPublishing(request) {\n    !!collection ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start capturing critical dimensions as there is already a collection') : invariant(false) : void 0;\n    var entry = registry.draggable.getById(request.draggableId);\n    var home = registry.droppable.getById(entry.descriptor.droppableId);\n    var critical = {\n      draggable: entry.descriptor,\n      droppable: home.descriptor\n    };\n    var unsubscribe = registry.subscribe(subscriber);\n    collection = {\n      critical: critical,\n      unsubscribe: unsubscribe\n    };\n    return getInitialPublish({\n      critical: critical,\n      registry: registry,\n      scrollOptions: request.scrollOptions\n    });\n  };\n  var marshal = {\n    updateDroppableIsEnabled: updateDroppableIsEnabled,\n    updateDroppableIsCombineEnabled: updateDroppableIsCombineEnabled,\n    scrollDroppable: scrollDroppable,\n    updateDroppableScroll: updateDroppableScroll,\n    startPublishing: startPublishing,\n    stopPublishing: stopPublishing\n  };\n  return marshal;\n};\nvar canStartDrag = function (state, id) {\n  if (state.phase === 'IDLE') {\n    return true;\n  }\n  if (state.phase !== 'DROP_ANIMATING') {\n    return false;\n  }\n  if (state.completed.result.draggableId === id) {\n    return false;\n  }\n  return state.completed.result.reason === 'DROP';\n};\nvar scrollWindow = function (change) {\n  window.scrollBy(change.x, change.y);\n};\nvar getScrollableDroppables = memoizeOne(function (droppables) {\n  return toDroppableList(droppables).filter(function (droppable) {\n    if (!droppable.isEnabled) {\n      return false;\n    }\n    if (!droppable.frame) {\n      return false;\n    }\n    return true;\n  });\n});\nvar getScrollableDroppableOver = function getScrollableDroppableOver(target, droppables) {\n  var maybe = find(getScrollableDroppables(droppables), function (droppable) {\n    !droppable.frame ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Invalid result') : invariant(false) : void 0;\n    return isPositionInFrame(droppable.frame.pageMarginBox)(target);\n  });\n  return maybe;\n};\nvar getBestScrollableDroppable = function (_ref) {\n  var center = _ref.center,\n    destination = _ref.destination,\n    droppables = _ref.droppables;\n  if (destination) {\n    var _dimension = droppables[destination];\n    if (!_dimension.frame) {\n      return null;\n    }\n    return _dimension;\n  }\n  var dimension = getScrollableDroppableOver(center, droppables);\n  return dimension;\n};\nvar config = {\n  startFromPercentage: 0.25,\n  maxScrollAtPercentage: 0.05,\n  maxPixelScroll: 28,\n  ease: function ease(percentage) {\n    return Math.pow(percentage, 2);\n  },\n  durationDampening: {\n    stopDampeningAt: 1200,\n    accelerateAt: 360\n  }\n};\nvar getDistanceThresholds = function (container, axis) {\n  var startScrollingFrom = container[axis.size] * config.startFromPercentage;\n  var maxScrollValueAt = container[axis.size] * config.maxScrollAtPercentage;\n  var thresholds = {\n    startScrollingFrom: startScrollingFrom,\n    maxScrollValueAt: maxScrollValueAt\n  };\n  return thresholds;\n};\nvar getPercentage = function (_ref) {\n  var startOfRange = _ref.startOfRange,\n    endOfRange = _ref.endOfRange,\n    current = _ref.current;\n  var range = endOfRange - startOfRange;\n  if (range === 0) {\n    process.env.NODE_ENV !== \"production\" ? warning(\"\\n      Detected distance range of 0 in the fluid auto scroller\\n      This is unexpected and would cause a divide by 0 issue.\\n      Not allowing an auto scroll\\n    \") : void 0;\n    return 0;\n  }\n  var currentInRange = current - startOfRange;\n  var percentage = currentInRange / range;\n  return percentage;\n};\nvar minScroll = 1;\nvar getValueFromDistance = function (distanceToEdge, thresholds) {\n  if (distanceToEdge > thresholds.startScrollingFrom) {\n    return 0;\n  }\n  if (distanceToEdge <= thresholds.maxScrollValueAt) {\n    return config.maxPixelScroll;\n  }\n  if (distanceToEdge === thresholds.startScrollingFrom) {\n    return minScroll;\n  }\n  var percentageFromMaxScrollValueAt = getPercentage({\n    startOfRange: thresholds.maxScrollValueAt,\n    endOfRange: thresholds.startScrollingFrom,\n    current: distanceToEdge\n  });\n  var percentageFromStartScrollingFrom = 1 - percentageFromMaxScrollValueAt;\n  var scroll = config.maxPixelScroll * config.ease(percentageFromStartScrollingFrom);\n  return Math.ceil(scroll);\n};\nvar accelerateAt = config.durationDampening.accelerateAt;\nvar stopAt = config.durationDampening.stopDampeningAt;\nvar dampenValueByTime = function (proposedScroll, dragStartTime) {\n  var startOfRange = dragStartTime;\n  var endOfRange = stopAt;\n  var now = Date.now();\n  var runTime = now - startOfRange;\n  if (runTime >= stopAt) {\n    return proposedScroll;\n  }\n  if (runTime < accelerateAt) {\n    return minScroll;\n  }\n  var betweenAccelerateAtAndStopAtPercentage = getPercentage({\n    startOfRange: accelerateAt,\n    endOfRange: endOfRange,\n    current: runTime\n  });\n  var scroll = proposedScroll * config.ease(betweenAccelerateAtAndStopAtPercentage);\n  return Math.ceil(scroll);\n};\nvar getValue = function (_ref) {\n  var distanceToEdge = _ref.distanceToEdge,\n    thresholds = _ref.thresholds,\n    dragStartTime = _ref.dragStartTime,\n    shouldUseTimeDampening = _ref.shouldUseTimeDampening;\n  var scroll = getValueFromDistance(distanceToEdge, thresholds);\n  if (scroll === 0) {\n    return 0;\n  }\n  if (!shouldUseTimeDampening) {\n    return scroll;\n  }\n  return Math.max(dampenValueByTime(scroll, dragStartTime), minScroll);\n};\nvar getScrollOnAxis = function (_ref) {\n  var container = _ref.container,\n    distanceToEdges = _ref.distanceToEdges,\n    dragStartTime = _ref.dragStartTime,\n    axis = _ref.axis,\n    shouldUseTimeDampening = _ref.shouldUseTimeDampening;\n  var thresholds = getDistanceThresholds(container, axis);\n  var isCloserToEnd = distanceToEdges[axis.end] < distanceToEdges[axis.start];\n  if (isCloserToEnd) {\n    return getValue({\n      distanceToEdge: distanceToEdges[axis.end],\n      thresholds: thresholds,\n      dragStartTime: dragStartTime,\n      shouldUseTimeDampening: shouldUseTimeDampening\n    });\n  }\n  return -1 * getValue({\n    distanceToEdge: distanceToEdges[axis.start],\n    thresholds: thresholds,\n    dragStartTime: dragStartTime,\n    shouldUseTimeDampening: shouldUseTimeDampening\n  });\n};\nvar adjustForSizeLimits = function (_ref) {\n  var container = _ref.container,\n    subject = _ref.subject,\n    proposedScroll = _ref.proposedScroll;\n  var isTooBigVertically = subject.height > container.height;\n  var isTooBigHorizontally = subject.width > container.width;\n  if (!isTooBigHorizontally && !isTooBigVertically) {\n    return proposedScroll;\n  }\n  if (isTooBigHorizontally && isTooBigVertically) {\n    return null;\n  }\n  return {\n    x: isTooBigHorizontally ? 0 : proposedScroll.x,\n    y: isTooBigVertically ? 0 : proposedScroll.y\n  };\n};\nvar clean$2 = apply(function (value) {\n  return value === 0 ? 0 : value;\n});\nvar getScroll = function (_ref) {\n  var dragStartTime = _ref.dragStartTime,\n    container = _ref.container,\n    subject = _ref.subject,\n    center = _ref.center,\n    shouldUseTimeDampening = _ref.shouldUseTimeDampening;\n  var distanceToEdges = {\n    top: center.y - container.top,\n    right: container.right - center.x,\n    bottom: container.bottom - center.y,\n    left: center.x - container.left\n  };\n  var y = getScrollOnAxis({\n    container: container,\n    distanceToEdges: distanceToEdges,\n    dragStartTime: dragStartTime,\n    axis: vertical,\n    shouldUseTimeDampening: shouldUseTimeDampening\n  });\n  var x = getScrollOnAxis({\n    container: container,\n    distanceToEdges: distanceToEdges,\n    dragStartTime: dragStartTime,\n    axis: horizontal,\n    shouldUseTimeDampening: shouldUseTimeDampening\n  });\n  var required = clean$2({\n    x: x,\n    y: y\n  });\n  if (isEqual(required, origin)) {\n    return null;\n  }\n  var limited = adjustForSizeLimits({\n    container: container,\n    subject: subject,\n    proposedScroll: required\n  });\n  if (!limited) {\n    return null;\n  }\n  return isEqual(limited, origin) ? null : limited;\n};\nvar smallestSigned = apply(function (value) {\n  if (value === 0) {\n    return 0;\n  }\n  return value > 0 ? 1 : -1;\n});\nvar getOverlap = function () {\n  var getRemainder = function getRemainder(target, max) {\n    if (target < 0) {\n      return target;\n    }\n    if (target > max) {\n      return target - max;\n    }\n    return 0;\n  };\n  return function (_ref) {\n    var current = _ref.current,\n      max = _ref.max,\n      change = _ref.change;\n    var targetScroll = add(current, change);\n    var overlap = {\n      x: getRemainder(targetScroll.x, max.x),\n      y: getRemainder(targetScroll.y, max.y)\n    };\n    if (isEqual(overlap, origin)) {\n      return null;\n    }\n    return overlap;\n  };\n}();\nvar canPartiallyScroll = function canPartiallyScroll(_ref2) {\n  var rawMax = _ref2.max,\n    current = _ref2.current,\n    change = _ref2.change;\n  var max = {\n    x: Math.max(current.x, rawMax.x),\n    y: Math.max(current.y, rawMax.y)\n  };\n  var smallestChange = smallestSigned(change);\n  var overlap = getOverlap({\n    max: max,\n    current: current,\n    change: smallestChange\n  });\n  if (!overlap) {\n    return true;\n  }\n  if (smallestChange.x !== 0 && overlap.x === 0) {\n    return true;\n  }\n  if (smallestChange.y !== 0 && overlap.y === 0) {\n    return true;\n  }\n  return false;\n};\nvar canScrollWindow = function canScrollWindow(viewport, change) {\n  return canPartiallyScroll({\n    current: viewport.scroll.current,\n    max: viewport.scroll.max,\n    change: change\n  });\n};\nvar getWindowOverlap = function getWindowOverlap(viewport, change) {\n  if (!canScrollWindow(viewport, change)) {\n    return null;\n  }\n  var max = viewport.scroll.max;\n  var current = viewport.scroll.current;\n  return getOverlap({\n    current: current,\n    max: max,\n    change: change\n  });\n};\nvar canScrollDroppable = function canScrollDroppable(droppable, change) {\n  var frame = droppable.frame;\n  if (!frame) {\n    return false;\n  }\n  return canPartiallyScroll({\n    current: frame.scroll.current,\n    max: frame.scroll.max,\n    change: change\n  });\n};\nvar getDroppableOverlap = function getDroppableOverlap(droppable, change) {\n  var frame = droppable.frame;\n  if (!frame) {\n    return null;\n  }\n  if (!canScrollDroppable(droppable, change)) {\n    return null;\n  }\n  return getOverlap({\n    current: frame.scroll.current,\n    max: frame.scroll.max,\n    change: change\n  });\n};\nvar getWindowScrollChange = function (_ref) {\n  var viewport = _ref.viewport,\n    subject = _ref.subject,\n    center = _ref.center,\n    dragStartTime = _ref.dragStartTime,\n    shouldUseTimeDampening = _ref.shouldUseTimeDampening;\n  var scroll = getScroll({\n    dragStartTime: dragStartTime,\n    container: viewport.frame,\n    subject: subject,\n    center: center,\n    shouldUseTimeDampening: shouldUseTimeDampening\n  });\n  return scroll && canScrollWindow(viewport, scroll) ? scroll : null;\n};\nvar getDroppableScrollChange = function (_ref) {\n  var droppable = _ref.droppable,\n    subject = _ref.subject,\n    center = _ref.center,\n    dragStartTime = _ref.dragStartTime,\n    shouldUseTimeDampening = _ref.shouldUseTimeDampening;\n  var frame = droppable.frame;\n  if (!frame) {\n    return null;\n  }\n  var scroll = getScroll({\n    dragStartTime: dragStartTime,\n    container: frame.pageMarginBox,\n    subject: subject,\n    center: center,\n    shouldUseTimeDampening: shouldUseTimeDampening\n  });\n  return scroll && canScrollDroppable(droppable, scroll) ? scroll : null;\n};\nvar scroll$1 = function (_ref) {\n  var state = _ref.state,\n    dragStartTime = _ref.dragStartTime,\n    shouldUseTimeDampening = _ref.shouldUseTimeDampening,\n    scrollWindow = _ref.scrollWindow,\n    scrollDroppable = _ref.scrollDroppable;\n  var center = state.current.page.borderBoxCenter;\n  var draggable = state.dimensions.draggables[state.critical.draggable.id];\n  var subject = draggable.page.marginBox;\n  if (state.isWindowScrollAllowed) {\n    var viewport = state.viewport;\n    var _change = getWindowScrollChange({\n      dragStartTime: dragStartTime,\n      viewport: viewport,\n      subject: subject,\n      center: center,\n      shouldUseTimeDampening: shouldUseTimeDampening\n    });\n    if (_change) {\n      scrollWindow(_change);\n      return;\n    }\n  }\n  var droppable = getBestScrollableDroppable({\n    center: center,\n    destination: whatIsDraggedOver(state.impact),\n    droppables: state.dimensions.droppables\n  });\n  if (!droppable) {\n    return;\n  }\n  var change = getDroppableScrollChange({\n    dragStartTime: dragStartTime,\n    droppable: droppable,\n    subject: subject,\n    center: center,\n    shouldUseTimeDampening: shouldUseTimeDampening\n  });\n  if (change) {\n    scrollDroppable(droppable.descriptor.id, change);\n  }\n};\nvar createFluidScroller = function (_ref) {\n  var scrollWindow = _ref.scrollWindow,\n    scrollDroppable = _ref.scrollDroppable;\n  var scheduleWindowScroll = rafSchd(scrollWindow);\n  var scheduleDroppableScroll = rafSchd(scrollDroppable);\n  var dragging = null;\n  var tryScroll = function tryScroll(state) {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fluid scroll if not dragging') : invariant(false) : void 0;\n    var _dragging = dragging,\n      shouldUseTimeDampening = _dragging.shouldUseTimeDampening,\n      dragStartTime = _dragging.dragStartTime;\n    scroll$1({\n      state: state,\n      scrollWindow: scheduleWindowScroll,\n      scrollDroppable: scheduleDroppableScroll,\n      dragStartTime: dragStartTime,\n      shouldUseTimeDampening: shouldUseTimeDampening\n    });\n  };\n  var start$1 = function start$1(state) {\n    start();\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start auto scrolling when already started') : invariant(false) : void 0;\n    var dragStartTime = Date.now();\n    var wasScrollNeeded = false;\n    var fakeScrollCallback = function fakeScrollCallback() {\n      wasScrollNeeded = true;\n    };\n    scroll$1({\n      state: state,\n      dragStartTime: 0,\n      shouldUseTimeDampening: false,\n      scrollWindow: fakeScrollCallback,\n      scrollDroppable: fakeScrollCallback\n    });\n    dragging = {\n      dragStartTime: dragStartTime,\n      shouldUseTimeDampening: wasScrollNeeded\n    };\n    finish();\n    if (wasScrollNeeded) {\n      tryScroll(state);\n    }\n  };\n  var stop = function stop() {\n    if (!dragging) {\n      return;\n    }\n    scheduleWindowScroll.cancel();\n    scheduleDroppableScroll.cancel();\n    dragging = null;\n  };\n  return {\n    start: start$1,\n    stop: stop,\n    scroll: tryScroll\n  };\n};\nvar createJumpScroller = function (_ref) {\n  var move = _ref.move,\n    scrollDroppable = _ref.scrollDroppable,\n    scrollWindow = _ref.scrollWindow;\n  var moveByOffset = function moveByOffset(state, offset) {\n    var client = add(state.current.client.selection, offset);\n    move({\n      client: client\n    });\n  };\n  var scrollDroppableAsMuchAsItCan = function scrollDroppableAsMuchAsItCan(droppable, change) {\n    if (!canScrollDroppable(droppable, change)) {\n      return change;\n    }\n    var overlap = getDroppableOverlap(droppable, change);\n    if (!overlap) {\n      scrollDroppable(droppable.descriptor.id, change);\n      return null;\n    }\n    var whatTheDroppableCanScroll = subtract(change, overlap);\n    scrollDroppable(droppable.descriptor.id, whatTheDroppableCanScroll);\n    var remainder = subtract(change, whatTheDroppableCanScroll);\n    return remainder;\n  };\n  var scrollWindowAsMuchAsItCan = function scrollWindowAsMuchAsItCan(isWindowScrollAllowed, viewport, change) {\n    if (!isWindowScrollAllowed) {\n      return change;\n    }\n    if (!canScrollWindow(viewport, change)) {\n      return change;\n    }\n    var overlap = getWindowOverlap(viewport, change);\n    if (!overlap) {\n      scrollWindow(change);\n      return null;\n    }\n    var whatTheWindowCanScroll = subtract(change, overlap);\n    scrollWindow(whatTheWindowCanScroll);\n    var remainder = subtract(change, whatTheWindowCanScroll);\n    return remainder;\n  };\n  var jumpScroller = function jumpScroller(state) {\n    var request = state.scrollJumpRequest;\n    if (!request) {\n      return;\n    }\n    var destination = whatIsDraggedOver(state.impact);\n    !destination ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot perform a jump scroll when there is no destination') : invariant(false) : void 0;\n    var droppableRemainder = scrollDroppableAsMuchAsItCan(state.dimensions.droppables[destination], request);\n    if (!droppableRemainder) {\n      return;\n    }\n    var viewport = state.viewport;\n    var windowRemainder = scrollWindowAsMuchAsItCan(state.isWindowScrollAllowed, viewport, droppableRemainder);\n    if (!windowRemainder) {\n      return;\n    }\n    moveByOffset(state, windowRemainder);\n  };\n  return jumpScroller;\n};\nvar createAutoScroller = function (_ref) {\n  var scrollDroppable = _ref.scrollDroppable,\n    scrollWindow = _ref.scrollWindow,\n    move = _ref.move;\n  var fluidScroller = createFluidScroller({\n    scrollWindow: scrollWindow,\n    scrollDroppable: scrollDroppable\n  });\n  var jumpScroll = createJumpScroller({\n    move: move,\n    scrollWindow: scrollWindow,\n    scrollDroppable: scrollDroppable\n  });\n  var scroll = function scroll(state) {\n    if (state.phase !== 'DRAGGING') {\n      return;\n    }\n    if (state.movementMode === 'FLUID') {\n      fluidScroller.scroll(state);\n      return;\n    }\n    if (!state.scrollJumpRequest) {\n      return;\n    }\n    jumpScroll(state);\n  };\n  var scroller = {\n    scroll: scroll,\n    start: fluidScroller.start,\n    stop: fluidScroller.stop\n  };\n  return scroller;\n};\nvar prefix$1 = 'data-rbd';\nvar dragHandle = function () {\n  var base = prefix$1 + \"-drag-handle\";\n  return {\n    base: base,\n    draggableId: base + \"-draggable-id\",\n    contextId: base + \"-context-id\"\n  };\n}();\nvar draggable = function () {\n  var base = prefix$1 + \"-draggable\";\n  return {\n    base: base,\n    contextId: base + \"-context-id\",\n    id: base + \"-id\"\n  };\n}();\nvar droppable = function () {\n  var base = prefix$1 + \"-droppable\";\n  return {\n    base: base,\n    contextId: base + \"-context-id\",\n    id: base + \"-id\"\n  };\n}();\nvar scrollContainer = {\n  contextId: prefix$1 + \"-scroll-container-context-id\"\n};\nvar makeGetSelector = function makeGetSelector(context) {\n  return function (attribute) {\n    return \"[\" + attribute + \"=\\\"\" + context + \"\\\"]\";\n  };\n};\nvar getStyles = function getStyles(rules, property) {\n  return rules.map(function (rule) {\n    var value = rule.styles[property];\n    if (!value) {\n      return '';\n    }\n    return rule.selector + \" { \" + value + \" }\";\n  }).join(' ');\n};\nvar noPointerEvents = 'pointer-events: none;';\nvar getStyles$1 = function (contextId) {\n  var getSelector = makeGetSelector(contextId);\n  var dragHandle$1 = function () {\n    var grabCursor = \"\\n      cursor: -webkit-grab;\\n      cursor: grab;\\n    \";\n    return {\n      selector: getSelector(dragHandle.contextId),\n      styles: {\n        always: \"\\n          -webkit-touch-callout: none;\\n          -webkit-tap-highlight-color: rgba(0,0,0,0);\\n          touch-action: manipulation;\\n        \",\n        resting: grabCursor,\n        dragging: noPointerEvents,\n        dropAnimating: grabCursor\n      }\n    };\n  }();\n  var draggable$1 = function () {\n    var transition = \"\\n      transition: \" + transitions.outOfTheWay + \";\\n    \";\n    return {\n      selector: getSelector(draggable.contextId),\n      styles: {\n        dragging: transition,\n        dropAnimating: transition,\n        userCancel: transition\n      }\n    };\n  }();\n  var droppable$1 = {\n    selector: getSelector(droppable.contextId),\n    styles: {\n      always: \"overflow-anchor: none;\"\n    }\n  };\n  var body = {\n    selector: 'body',\n    styles: {\n      dragging: \"\\n        cursor: grabbing;\\n        cursor: -webkit-grabbing;\\n        user-select: none;\\n        -webkit-user-select: none;\\n        -moz-user-select: none;\\n        -ms-user-select: none;\\n        overflow-anchor: none;\\n      \"\n    }\n  };\n  var rules = [draggable$1, dragHandle$1, droppable$1, body];\n  return {\n    always: getStyles(rules, 'always'),\n    resting: getStyles(rules, 'resting'),\n    dragging: getStyles(rules, 'dragging'),\n    dropAnimating: getStyles(rules, 'dropAnimating'),\n    userCancel: getStyles(rules, 'userCancel')\n  };\n};\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? useLayoutEffect : useEffect;\nvar getHead = function getHead() {\n  var head = document.querySelector('head');\n  !head ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find the head to append a style to') : invariant(false) : void 0;\n  return head;\n};\nvar createStyleEl = function createStyleEl(nonce) {\n  var el = document.createElement('style');\n  if (nonce) {\n    el.setAttribute('nonce', nonce);\n  }\n  el.type = 'text/css';\n  return el;\n};\nfunction useStyleMarshal(contextId, nonce) {\n  var styles = useMemo(function () {\n    return getStyles$1(contextId);\n  }, [contextId]);\n  var alwaysRef = useRef(null);\n  var dynamicRef = useRef(null);\n  var setDynamicStyle = useCallback(memoizeOne(function (proposed) {\n    var el = dynamicRef.current;\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot set dynamic style element if it is not set') : invariant(false) : void 0;\n    el.textContent = proposed;\n  }), []);\n  var setAlwaysStyle = useCallback(function (proposed) {\n    var el = alwaysRef.current;\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot set dynamic style element if it is not set') : invariant(false) : void 0;\n    el.textContent = proposed;\n  }, []);\n  useIsomorphicLayoutEffect(function () {\n    !(!alwaysRef.current && !dynamicRef.current) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'style elements already mounted') : invariant(false) : void 0;\n    var always = createStyleEl(nonce);\n    var dynamic = createStyleEl(nonce);\n    alwaysRef.current = always;\n    dynamicRef.current = dynamic;\n    always.setAttribute(prefix$1 + \"-always\", contextId);\n    dynamic.setAttribute(prefix$1 + \"-dynamic\", contextId);\n    getHead().appendChild(always);\n    getHead().appendChild(dynamic);\n    setAlwaysStyle(styles.always);\n    setDynamicStyle(styles.resting);\n    return function () {\n      var remove = function remove(ref) {\n        var current = ref.current;\n        !current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot unmount ref as it is not set') : invariant(false) : void 0;\n        getHead().removeChild(current);\n        ref.current = null;\n      };\n      remove(alwaysRef);\n      remove(dynamicRef);\n    };\n  }, [nonce, setAlwaysStyle, setDynamicStyle, styles.always, styles.resting, contextId]);\n  var dragging = useCallback(function () {\n    return setDynamicStyle(styles.dragging);\n  }, [setDynamicStyle, styles.dragging]);\n  var dropping = useCallback(function (reason) {\n    if (reason === 'DROP') {\n      setDynamicStyle(styles.dropAnimating);\n      return;\n    }\n    setDynamicStyle(styles.userCancel);\n  }, [setDynamicStyle, styles.dropAnimating, styles.userCancel]);\n  var resting = useCallback(function () {\n    if (!dynamicRef.current) {\n      return;\n    }\n    setDynamicStyle(styles.resting);\n  }, [setDynamicStyle, styles.resting]);\n  var marshal = useMemo(function () {\n    return {\n      dragging: dragging,\n      dropping: dropping,\n      resting: resting\n    };\n  }, [dragging, dropping, resting]);\n  return marshal;\n}\nvar getWindowFromEl = function (el) {\n  return el && el.ownerDocument ? el.ownerDocument.defaultView : window;\n};\nfunction isHtmlElement(el) {\n  return el instanceof getWindowFromEl(el).HTMLElement;\n}\nfunction findDragHandle(contextId, draggableId) {\n  var selector = \"[\" + dragHandle.contextId + \"=\\\"\" + contextId + \"\\\"]\";\n  var possible = toArray(document.querySelectorAll(selector));\n  if (!possible.length) {\n    process.env.NODE_ENV !== \"production\" ? warning(\"Unable to find any drag handles in the context \\\"\" + contextId + \"\\\"\") : void 0;\n    return null;\n  }\n  var handle = find(possible, function (el) {\n    return el.getAttribute(dragHandle.draggableId) === draggableId;\n  });\n  if (!handle) {\n    process.env.NODE_ENV !== \"production\" ? warning(\"Unable to find drag handle with id \\\"\" + draggableId + \"\\\" as no handle with a matching id was found\") : void 0;\n    return null;\n  }\n  if (!isHtmlElement(handle)) {\n    process.env.NODE_ENV !== \"production\" ? warning('drag handle needs to be a HTMLElement') : void 0;\n    return null;\n  }\n  return handle;\n}\nfunction useFocusMarshal(contextId) {\n  var entriesRef = useRef({});\n  var recordRef = useRef(null);\n  var restoreFocusFrameRef = useRef(null);\n  var isMountedRef = useRef(false);\n  var register = useCallback(function register(id, focus) {\n    var entry = {\n      id: id,\n      focus: focus\n    };\n    entriesRef.current[id] = entry;\n    return function unregister() {\n      var entries = entriesRef.current;\n      var current = entries[id];\n      if (current !== entry) {\n        delete entries[id];\n      }\n    };\n  }, []);\n  var tryGiveFocus = useCallback(function tryGiveFocus(tryGiveFocusTo) {\n    var handle = findDragHandle(contextId, tryGiveFocusTo);\n    if (handle && handle !== document.activeElement) {\n      handle.focus();\n    }\n  }, [contextId]);\n  var tryShiftRecord = useCallback(function tryShiftRecord(previous, redirectTo) {\n    if (recordRef.current === previous) {\n      recordRef.current = redirectTo;\n    }\n  }, []);\n  var tryRestoreFocusRecorded = useCallback(function tryRestoreFocusRecorded() {\n    if (restoreFocusFrameRef.current) {\n      return;\n    }\n    if (!isMountedRef.current) {\n      return;\n    }\n    restoreFocusFrameRef.current = requestAnimationFrame(function () {\n      restoreFocusFrameRef.current = null;\n      var record = recordRef.current;\n      if (record) {\n        tryGiveFocus(record);\n      }\n    });\n  }, [tryGiveFocus]);\n  var tryRecordFocus = useCallback(function tryRecordFocus(id) {\n    recordRef.current = null;\n    var focused = document.activeElement;\n    if (!focused) {\n      return;\n    }\n    if (focused.getAttribute(dragHandle.draggableId) !== id) {\n      return;\n    }\n    recordRef.current = id;\n  }, []);\n  useIsomorphicLayoutEffect(function () {\n    isMountedRef.current = true;\n    return function clearFrameOnUnmount() {\n      isMountedRef.current = false;\n      var frameId = restoreFocusFrameRef.current;\n      if (frameId) {\n        cancelAnimationFrame(frameId);\n      }\n    };\n  }, []);\n  var marshal = useMemo(function () {\n    return {\n      register: register,\n      tryRecordFocus: tryRecordFocus,\n      tryRestoreFocusRecorded: tryRestoreFocusRecorded,\n      tryShiftRecord: tryShiftRecord\n    };\n  }, [register, tryRecordFocus, tryRestoreFocusRecorded, tryShiftRecord]);\n  return marshal;\n}\nfunction createRegistry() {\n  var entries = {\n    draggables: {},\n    droppables: {}\n  };\n  var subscribers = [];\n  function subscribe(cb) {\n    subscribers.push(cb);\n    return function unsubscribe() {\n      var index = subscribers.indexOf(cb);\n      if (index === -1) {\n        return;\n      }\n      subscribers.splice(index, 1);\n    };\n  }\n  function notify(event) {\n    if (subscribers.length) {\n      subscribers.forEach(function (cb) {\n        return cb(event);\n      });\n    }\n  }\n  function findDraggableById(id) {\n    return entries.draggables[id] || null;\n  }\n  function getDraggableById(id) {\n    var entry = findDraggableById(id);\n    !entry ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot find draggable entry with id [\" + id + \"]\") : invariant(false) : void 0;\n    return entry;\n  }\n  var draggableAPI = {\n    register: function register(entry) {\n      entries.draggables[entry.descriptor.id] = entry;\n      notify({\n        type: 'ADDITION',\n        value: entry\n      });\n    },\n    update: function update(entry, last) {\n      var current = entries.draggables[last.descriptor.id];\n      if (!current) {\n        return;\n      }\n      if (current.uniqueId !== entry.uniqueId) {\n        return;\n      }\n      delete entries.draggables[last.descriptor.id];\n      entries.draggables[entry.descriptor.id] = entry;\n    },\n    unregister: function unregister(entry) {\n      var draggableId = entry.descriptor.id;\n      var current = findDraggableById(draggableId);\n      if (!current) {\n        return;\n      }\n      if (entry.uniqueId !== current.uniqueId) {\n        return;\n      }\n      delete entries.draggables[draggableId];\n      notify({\n        type: 'REMOVAL',\n        value: entry\n      });\n    },\n    getById: getDraggableById,\n    findById: findDraggableById,\n    exists: function exists(id) {\n      return Boolean(findDraggableById(id));\n    },\n    getAllByType: function getAllByType(type) {\n      return values(entries.draggables).filter(function (entry) {\n        return entry.descriptor.type === type;\n      });\n    }\n  };\n  function findDroppableById(id) {\n    return entries.droppables[id] || null;\n  }\n  function getDroppableById(id) {\n    var entry = findDroppableById(id);\n    !entry ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot find droppable entry with id [\" + id + \"]\") : invariant(false) : void 0;\n    return entry;\n  }\n  var droppableAPI = {\n    register: function register(entry) {\n      entries.droppables[entry.descriptor.id] = entry;\n    },\n    unregister: function unregister(entry) {\n      var current = findDroppableById(entry.descriptor.id);\n      if (!current) {\n        return;\n      }\n      if (entry.uniqueId !== current.uniqueId) {\n        return;\n      }\n      delete entries.droppables[entry.descriptor.id];\n    },\n    getById: getDroppableById,\n    findById: findDroppableById,\n    exists: function exists(id) {\n      return Boolean(findDroppableById(id));\n    },\n    getAllByType: function getAllByType(type) {\n      return values(entries.droppables).filter(function (entry) {\n        return entry.descriptor.type === type;\n      });\n    }\n  };\n  function clean() {\n    entries.draggables = {};\n    entries.droppables = {};\n    subscribers.length = 0;\n  }\n  return {\n    draggable: draggableAPI,\n    droppable: droppableAPI,\n    subscribe: subscribe,\n    clean: clean\n  };\n}\nfunction useRegistry() {\n  var registry = useMemo(createRegistry, []);\n  useEffect(function () {\n    return function unmount() {\n      requestAnimationFrame(registry.clean);\n    };\n  }, [registry]);\n  return registry;\n}\nvar StoreContext = React.createContext(null);\nvar getBodyElement = function () {\n  var body = document.body;\n  !body ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find document.body') : invariant(false) : void 0;\n  return body;\n};\nvar visuallyHidden = {\n  position: 'absolute',\n  width: '1px',\n  height: '1px',\n  margin: '-1px',\n  border: '0',\n  padding: '0',\n  overflow: 'hidden',\n  clip: 'rect(0 0 0 0)',\n  'clip-path': 'inset(100%)'\n};\nvar getId = function getId(contextId) {\n  return \"rbd-announcement-\" + contextId;\n};\nfunction useAnnouncer(contextId) {\n  var id = useMemo(function () {\n    return getId(contextId);\n  }, [contextId]);\n  var ref = useRef(null);\n  useEffect(function setup() {\n    var el = document.createElement('div');\n    ref.current = el;\n    el.id = id;\n    el.setAttribute('aria-live', 'assertive');\n    el.setAttribute('aria-atomic', 'true');\n    _extends(el.style, visuallyHidden);\n    getBodyElement().appendChild(el);\n    return function cleanup() {\n      setTimeout(function remove() {\n        var body = getBodyElement();\n        if (body.contains(el)) {\n          body.removeChild(el);\n        }\n        if (el === ref.current) {\n          ref.current = null;\n        }\n      });\n    };\n  }, [id]);\n  var announce = useCallback(function (message) {\n    var el = ref.current;\n    if (el) {\n      el.textContent = message;\n      return;\n    }\n    process.env.NODE_ENV !== \"production\" ? warning(\"\\n      A screen reader message was trying to be announced but it was unable to do so.\\n      This can occur if you unmount your <DragDropContext /> in your onDragEnd.\\n      Consider calling provided.announce() before the unmount so that the instruction will\\n      not be lost for users relying on a screen reader.\\n\\n      Message not passed to screen reader:\\n\\n      \\\"\" + message + \"\\\"\\n    \") : void 0;\n  }, []);\n  return announce;\n}\nvar count = 0;\nvar defaults = {\n  separator: '::'\n};\nfunction reset() {\n  count = 0;\n}\nfunction useUniqueId(prefix, options) {\n  if (options === void 0) {\n    options = defaults;\n  }\n  return useMemo(function () {\n    return \"\" + prefix + options.separator + count++;\n  }, [options.separator, prefix]);\n}\nfunction getElementId(_ref) {\n  var contextId = _ref.contextId,\n    uniqueId = _ref.uniqueId;\n  return \"rbd-hidden-text-\" + contextId + \"-\" + uniqueId;\n}\nfunction useHiddenTextElement(_ref2) {\n  var contextId = _ref2.contextId,\n    text = _ref2.text;\n  var uniqueId = useUniqueId('hidden-text', {\n    separator: '-'\n  });\n  var id = useMemo(function () {\n    return getElementId({\n      contextId: contextId,\n      uniqueId: uniqueId\n    });\n  }, [uniqueId, contextId]);\n  useEffect(function mount() {\n    var el = document.createElement('div');\n    el.id = id;\n    el.textContent = text;\n    el.style.display = 'none';\n    getBodyElement().appendChild(el);\n    return function unmount() {\n      var body = getBodyElement();\n      if (body.contains(el)) {\n        body.removeChild(el);\n      }\n    };\n  }, [id, text]);\n  return id;\n}\nvar AppContext = React.createContext(null);\nvar peerDependencies = {\n  react: \"^16.8.5 || ^17.0.0 || ^18.0.0\",\n  \"react-dom\": \"^16.8.5 || ^17.0.0 || ^18.0.0\"\n};\nvar semver = /(\\d+)\\.(\\d+)\\.(\\d+)/;\nvar getVersion = function getVersion(value) {\n  var result = semver.exec(value);\n  !(result != null) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Unable to parse React version \" + value) : invariant(false) : void 0;\n  var major = Number(result[1]);\n  var minor = Number(result[2]);\n  var patch = Number(result[3]);\n  return {\n    major: major,\n    minor: minor,\n    patch: patch,\n    raw: value\n  };\n};\nvar isSatisfied = function isSatisfied(expected, actual) {\n  if (actual.major > expected.major) {\n    return true;\n  }\n  if (actual.major < expected.major) {\n    return false;\n  }\n  if (actual.minor > expected.minor) {\n    return true;\n  }\n  if (actual.minor < expected.minor) {\n    return false;\n  }\n  return actual.patch >= expected.patch;\n};\nvar checkReactVersion = function (peerDepValue, actualValue) {\n  var peerDep = getVersion(peerDepValue);\n  var actual = getVersion(actualValue);\n  if (isSatisfied(peerDep, actual)) {\n    return;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(\"\\n    React version: [\" + actual.raw + \"]\\n    does not satisfy expected peer dependency version: [\" + peerDep.raw + \"]\\n\\n    This can result in run time bugs, and even fatal crashes\\n  \") : void 0;\n};\nvar suffix = \"\\n  We expect a html5 doctype: <!doctype html>\\n  This is to ensure consistent browser layout and measurement\\n\\n  More information: https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/guides/doctype.md\\n\";\nvar checkDoctype = function (doc) {\n  var doctype = doc.doctype;\n  if (!doctype) {\n    process.env.NODE_ENV !== \"production\" ? warning(\"\\n      No <!doctype html> found.\\n\\n      \" + suffix + \"\\n    \") : void 0;\n    return;\n  }\n  if (doctype.name.toLowerCase() !== 'html') {\n    process.env.NODE_ENV !== \"production\" ? warning(\"\\n      Unexpected <!doctype> found: (\" + doctype.name + \")\\n\\n      \" + suffix + \"\\n    \") : void 0;\n  }\n  if (doctype.publicId !== '') {\n    process.env.NODE_ENV !== \"production\" ? warning(\"\\n      Unexpected <!doctype> publicId found: (\" + doctype.publicId + \")\\n      A html5 doctype does not have a publicId\\n\\n      \" + suffix + \"\\n    \") : void 0;\n  }\n};\nfunction useDev(useHook) {\n  if (process.env.NODE_ENV !== 'production') {\n    useHook();\n  }\n}\nfunction useDevSetupWarning(fn, inputs) {\n  useDev(function () {\n    useEffect(function () {\n      try {\n        fn();\n      } catch (e) {\n        error(\"\\n          A setup problem was encountered.\\n\\n          > \" + e.message + \"\\n        \");\n      }\n    }, inputs);\n  });\n}\nfunction useStartupValidation() {\n  useDevSetupWarning(function () {\n    checkReactVersion(peerDependencies.react, React.version);\n    checkDoctype(document);\n  }, []);\n}\nfunction usePrevious(current) {\n  var ref = useRef(current);\n  useEffect(function () {\n    ref.current = current;\n  });\n  return ref;\n}\nfunction create() {\n  var lock = null;\n  function isClaimed() {\n    return Boolean(lock);\n  }\n  function isActive(value) {\n    return value === lock;\n  }\n  function claim(abandon) {\n    !!lock ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot claim lock as it is already claimed') : invariant(false) : void 0;\n    var newLock = {\n      abandon: abandon\n    };\n    lock = newLock;\n    return newLock;\n  }\n  function release() {\n    !lock ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot release lock when there is no lock') : invariant(false) : void 0;\n    lock = null;\n  }\n  function tryAbandon() {\n    if (lock) {\n      lock.abandon();\n      release();\n    }\n  }\n  return {\n    isClaimed: isClaimed,\n    isActive: isActive,\n    claim: claim,\n    release: release,\n    tryAbandon: tryAbandon\n  };\n}\nvar tab = 9;\nvar enter = 13;\nvar escape = 27;\nvar space = 32;\nvar pageUp = 33;\nvar pageDown = 34;\nvar end = 35;\nvar home = 36;\nvar arrowLeft = 37;\nvar arrowUp = 38;\nvar arrowRight = 39;\nvar arrowDown = 40;\nvar _preventedKeys;\nvar preventedKeys = (_preventedKeys = {}, _preventedKeys[enter] = true, _preventedKeys[tab] = true, _preventedKeys);\nvar preventStandardKeyEvents = function (event) {\n  if (preventedKeys[event.keyCode]) {\n    event.preventDefault();\n  }\n};\nvar supportedEventName = function () {\n  var base = 'visibilitychange';\n  if (typeof document === 'undefined') {\n    return base;\n  }\n  var candidates = [base, \"ms\" + base, \"webkit\" + base, \"moz\" + base, \"o\" + base];\n  var supported = find(candidates, function (eventName) {\n    return \"on\" + eventName in document;\n  });\n  return supported || base;\n}();\nvar primaryButton = 0;\nvar sloppyClickThreshold = 5;\nfunction isSloppyClickThresholdExceeded(original, current) {\n  return Math.abs(current.x - original.x) >= sloppyClickThreshold || Math.abs(current.y - original.y) >= sloppyClickThreshold;\n}\nvar idle$1 = {\n  type: 'IDLE'\n};\nfunction getCaptureBindings(_ref) {\n  var cancel = _ref.cancel,\n    completed = _ref.completed,\n    getPhase = _ref.getPhase,\n    setPhase = _ref.setPhase;\n  return [{\n    eventName: 'mousemove',\n    fn: function fn(event) {\n      var button = event.button,\n        clientX = event.clientX,\n        clientY = event.clientY;\n      if (button !== primaryButton) {\n        return;\n      }\n      var point = {\n        x: clientX,\n        y: clientY\n      };\n      var phase = getPhase();\n      if (phase.type === 'DRAGGING') {\n        event.preventDefault();\n        phase.actions.move(point);\n        return;\n      }\n      !(phase.type === 'PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot be IDLE') : invariant(false) : void 0;\n      var pending = phase.point;\n      if (!isSloppyClickThresholdExceeded(pending, point)) {\n        return;\n      }\n      event.preventDefault();\n      var actions = phase.actions.fluidLift(point);\n      setPhase({\n        type: 'DRAGGING',\n        actions: actions\n      });\n    }\n  }, {\n    eventName: 'mouseup',\n    fn: function fn(event) {\n      var phase = getPhase();\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n      phase.actions.drop({\n        shouldBlockNextClick: true\n      });\n      completed();\n    }\n  }, {\n    eventName: 'mousedown',\n    fn: function fn(event) {\n      if (getPhase().type === 'DRAGGING') {\n        event.preventDefault();\n      }\n      cancel();\n    }\n  }, {\n    eventName: 'keydown',\n    fn: function fn(event) {\n      var phase = getPhase();\n      if (phase.type === 'PENDING') {\n        cancel();\n        return;\n      }\n      if (event.keyCode === escape) {\n        event.preventDefault();\n        cancel();\n        return;\n      }\n      preventStandardKeyEvents(event);\n    }\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'scroll',\n    options: {\n      passive: true,\n      capture: false\n    },\n    fn: function fn() {\n      if (getPhase().type === 'PENDING') {\n        cancel();\n      }\n    }\n  }, {\n    eventName: 'webkitmouseforcedown',\n    fn: function fn(event) {\n      var phase = getPhase();\n      !(phase.type !== 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Unexpected phase') : invariant(false) : void 0;\n      if (phase.actions.shouldRespectForcePress()) {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\nfunction useMouseSensor(api) {\n  var phaseRef = useRef(idle$1);\n  var unbindEventsRef = useRef(noop);\n  var startCaptureBinding = useMemo(function () {\n    return {\n      eventName: 'mousedown',\n      fn: function onMouseDown(event) {\n        if (event.defaultPrevented) {\n          return;\n        }\n        if (event.button !== primaryButton) {\n          return;\n        }\n        if (event.ctrlKey || event.metaKey || event.shiftKey || event.altKey) {\n          return;\n        }\n        var draggableId = api.findClosestDraggableId(event);\n        if (!draggableId) {\n          return;\n        }\n        var actions = api.tryGetLock(draggableId, stop, {\n          sourceEvent: event\n        });\n        if (!actions) {\n          return;\n        }\n        event.preventDefault();\n        var point = {\n          x: event.clientX,\n          y: event.clientY\n        };\n        unbindEventsRef.current();\n        startPendingDrag(actions, point);\n      }\n    };\n  }, [api]);\n  var preventForcePressBinding = useMemo(function () {\n    return {\n      eventName: 'webkitmouseforcewillbegin',\n      fn: function fn(event) {\n        if (event.defaultPrevented) {\n          return;\n        }\n        var id = api.findClosestDraggableId(event);\n        if (!id) {\n          return;\n        }\n        var options = api.findOptionsForDraggable(id);\n        if (!options) {\n          return;\n        }\n        if (options.shouldRespectForcePress) {\n          return;\n        }\n        if (!api.canGetLock(id)) {\n          return;\n        }\n        event.preventDefault();\n      }\n    };\n  }, [api]);\n  var listenForCapture = useCallback(function listenForCapture() {\n    var options = {\n      passive: false,\n      capture: true\n    };\n    unbindEventsRef.current = bindEvents(window, [preventForcePressBinding, startCaptureBinding], options);\n  }, [preventForcePressBinding, startCaptureBinding]);\n  var stop = useCallback(function () {\n    var current = phaseRef.current;\n    if (current.type === 'IDLE') {\n      return;\n    }\n    phaseRef.current = idle$1;\n    unbindEventsRef.current();\n    listenForCapture();\n  }, [listenForCapture]);\n  var cancel = useCallback(function () {\n    var phase = phaseRef.current;\n    stop();\n    if (phase.type === 'DRAGGING') {\n      phase.actions.cancel({\n        shouldBlockNextClick: true\n      });\n    }\n    if (phase.type === 'PENDING') {\n      phase.actions.abort();\n    }\n  }, [stop]);\n  var bindCapturingEvents = useCallback(function bindCapturingEvents() {\n    var options = {\n      capture: true,\n      passive: false\n    };\n    var bindings = getCaptureBindings({\n      cancel: cancel,\n      completed: stop,\n      getPhase: function getPhase() {\n        return phaseRef.current;\n      },\n      setPhase: function setPhase(phase) {\n        phaseRef.current = phase;\n      }\n    });\n    unbindEventsRef.current = bindEvents(window, bindings, options);\n  }, [cancel, stop]);\n  var startPendingDrag = useCallback(function startPendingDrag(actions, point) {\n    !(phaseRef.current.type === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected to move from IDLE to PENDING drag') : invariant(false) : void 0;\n    phaseRef.current = {\n      type: 'PENDING',\n      point: point,\n      actions: actions\n    };\n    bindCapturingEvents();\n  }, [bindCapturingEvents]);\n  useIsomorphicLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n    };\n  }, [listenForCapture]);\n}\nvar _scrollJumpKeys;\nfunction noop$1() {}\nvar scrollJumpKeys = (_scrollJumpKeys = {}, _scrollJumpKeys[pageDown] = true, _scrollJumpKeys[pageUp] = true, _scrollJumpKeys[home] = true, _scrollJumpKeys[end] = true, _scrollJumpKeys);\nfunction getDraggingBindings(actions, stop) {\n  function cancel() {\n    stop();\n    actions.cancel();\n  }\n  function drop() {\n    stop();\n    actions.drop();\n  }\n  return [{\n    eventName: 'keydown',\n    fn: function fn(event) {\n      if (event.keyCode === escape) {\n        event.preventDefault();\n        cancel();\n        return;\n      }\n      if (event.keyCode === space) {\n        event.preventDefault();\n        drop();\n        return;\n      }\n      if (event.keyCode === arrowDown) {\n        event.preventDefault();\n        actions.moveDown();\n        return;\n      }\n      if (event.keyCode === arrowUp) {\n        event.preventDefault();\n        actions.moveUp();\n        return;\n      }\n      if (event.keyCode === arrowRight) {\n        event.preventDefault();\n        actions.moveRight();\n        return;\n      }\n      if (event.keyCode === arrowLeft) {\n        event.preventDefault();\n        actions.moveLeft();\n        return;\n      }\n      if (scrollJumpKeys[event.keyCode]) {\n        event.preventDefault();\n        return;\n      }\n      preventStandardKeyEvents(event);\n    }\n  }, {\n    eventName: 'mousedown',\n    fn: cancel\n  }, {\n    eventName: 'mouseup',\n    fn: cancel\n  }, {\n    eventName: 'click',\n    fn: cancel\n  }, {\n    eventName: 'touchstart',\n    fn: cancel\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'wheel',\n    fn: cancel,\n    options: {\n      passive: true\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\nfunction useKeyboardSensor(api) {\n  var unbindEventsRef = useRef(noop$1);\n  var startCaptureBinding = useMemo(function () {\n    return {\n      eventName: 'keydown',\n      fn: function onKeyDown(event) {\n        if (event.defaultPrevented) {\n          return;\n        }\n        if (event.keyCode !== space) {\n          return;\n        }\n        var draggableId = api.findClosestDraggableId(event);\n        if (!draggableId) {\n          return;\n        }\n        var preDrag = api.tryGetLock(draggableId, stop, {\n          sourceEvent: event\n        });\n        if (!preDrag) {\n          return;\n        }\n        event.preventDefault();\n        var isCapturing = true;\n        var actions = preDrag.snapLift();\n        unbindEventsRef.current();\n        function stop() {\n          !isCapturing ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop capturing a keyboard drag when not capturing') : invariant(false) : void 0;\n          isCapturing = false;\n          unbindEventsRef.current();\n          listenForCapture();\n        }\n        unbindEventsRef.current = bindEvents(window, getDraggingBindings(actions, stop), {\n          capture: true,\n          passive: false\n        });\n      }\n    };\n  }, [api]);\n  var listenForCapture = useCallback(function tryStartCapture() {\n    var options = {\n      passive: false,\n      capture: true\n    };\n    unbindEventsRef.current = bindEvents(window, [startCaptureBinding], options);\n  }, [startCaptureBinding]);\n  useIsomorphicLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n    };\n  }, [listenForCapture]);\n}\nvar idle$2 = {\n  type: 'IDLE'\n};\nvar timeForLongPress = 120;\nvar forcePressThreshold = 0.15;\nfunction getWindowBindings(_ref) {\n  var cancel = _ref.cancel,\n    getPhase = _ref.getPhase;\n  return [{\n    eventName: 'orientationchange',\n    fn: cancel\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'contextmenu',\n    fn: function fn(event) {\n      event.preventDefault();\n    }\n  }, {\n    eventName: 'keydown',\n    fn: function fn(event) {\n      if (getPhase().type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      if (event.keyCode === escape) {\n        event.preventDefault();\n      }\n      cancel();\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\nfunction getHandleBindings(_ref2) {\n  var cancel = _ref2.cancel,\n    completed = _ref2.completed,\n    getPhase = _ref2.getPhase;\n  return [{\n    eventName: 'touchmove',\n    options: {\n      capture: false\n    },\n    fn: function fn(event) {\n      var phase = getPhase();\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      phase.hasMoved = true;\n      var _event$touches$ = event.touches[0],\n        clientX = _event$touches$.clientX,\n        clientY = _event$touches$.clientY;\n      var point = {\n        x: clientX,\n        y: clientY\n      };\n      event.preventDefault();\n      phase.actions.move(point);\n    }\n  }, {\n    eventName: 'touchend',\n    fn: function fn(event) {\n      var phase = getPhase();\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n      phase.actions.drop({\n        shouldBlockNextClick: true\n      });\n      completed();\n    }\n  }, {\n    eventName: 'touchcancel',\n    fn: function fn(event) {\n      if (getPhase().type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n      cancel();\n    }\n  }, {\n    eventName: 'touchforcechange',\n    fn: function fn(event) {\n      var phase = getPhase();\n      !(phase.type !== 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n      var touch = event.touches[0];\n      if (!touch) {\n        return;\n      }\n      var isForcePress = touch.force >= forcePressThreshold;\n      if (!isForcePress) {\n        return;\n      }\n      var shouldRespect = phase.actions.shouldRespectForcePress();\n      if (phase.type === 'PENDING') {\n        if (shouldRespect) {\n          cancel();\n        }\n        return;\n      }\n      if (shouldRespect) {\n        if (phase.hasMoved) {\n          event.preventDefault();\n          return;\n        }\n        cancel();\n        return;\n      }\n      event.preventDefault();\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\nfunction useTouchSensor(api) {\n  var phaseRef = useRef(idle$2);\n  var unbindEventsRef = useRef(noop);\n  var getPhase = useCallback(function getPhase() {\n    return phaseRef.current;\n  }, []);\n  var setPhase = useCallback(function setPhase(phase) {\n    phaseRef.current = phase;\n  }, []);\n  var startCaptureBinding = useMemo(function () {\n    return {\n      eventName: 'touchstart',\n      fn: function onTouchStart(event) {\n        if (event.defaultPrevented) {\n          return;\n        }\n        var draggableId = api.findClosestDraggableId(event);\n        if (!draggableId) {\n          return;\n        }\n        var actions = api.tryGetLock(draggableId, stop, {\n          sourceEvent: event\n        });\n        if (!actions) {\n          return;\n        }\n        var touch = event.touches[0];\n        var clientX = touch.clientX,\n          clientY = touch.clientY;\n        var point = {\n          x: clientX,\n          y: clientY\n        };\n        unbindEventsRef.current();\n        startPendingDrag(actions, point);\n      }\n    };\n  }, [api]);\n  var listenForCapture = useCallback(function listenForCapture() {\n    var options = {\n      capture: true,\n      passive: false\n    };\n    unbindEventsRef.current = bindEvents(window, [startCaptureBinding], options);\n  }, [startCaptureBinding]);\n  var stop = useCallback(function () {\n    var current = phaseRef.current;\n    if (current.type === 'IDLE') {\n      return;\n    }\n    if (current.type === 'PENDING') {\n      clearTimeout(current.longPressTimerId);\n    }\n    setPhase(idle$2);\n    unbindEventsRef.current();\n    listenForCapture();\n  }, [listenForCapture, setPhase]);\n  var cancel = useCallback(function () {\n    var phase = phaseRef.current;\n    stop();\n    if (phase.type === 'DRAGGING') {\n      phase.actions.cancel({\n        shouldBlockNextClick: true\n      });\n    }\n    if (phase.type === 'PENDING') {\n      phase.actions.abort();\n    }\n  }, [stop]);\n  var bindCapturingEvents = useCallback(function bindCapturingEvents() {\n    var options = {\n      capture: true,\n      passive: false\n    };\n    var args = {\n      cancel: cancel,\n      completed: stop,\n      getPhase: getPhase\n    };\n    var unbindTarget = bindEvents(window, getHandleBindings(args), options);\n    var unbindWindow = bindEvents(window, getWindowBindings(args), options);\n    unbindEventsRef.current = function unbindAll() {\n      unbindTarget();\n      unbindWindow();\n    };\n  }, [cancel, getPhase, stop]);\n  var startDragging = useCallback(function startDragging() {\n    var phase = getPhase();\n    !(phase.type === 'PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot start dragging from phase \" + phase.type) : invariant(false) : void 0;\n    var actions = phase.actions.fluidLift(phase.point);\n    setPhase({\n      type: 'DRAGGING',\n      actions: actions,\n      hasMoved: false\n    });\n  }, [getPhase, setPhase]);\n  var startPendingDrag = useCallback(function startPendingDrag(actions, point) {\n    !(getPhase().type === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected to move from IDLE to PENDING drag') : invariant(false) : void 0;\n    var longPressTimerId = setTimeout(startDragging, timeForLongPress);\n    setPhase({\n      type: 'PENDING',\n      point: point,\n      actions: actions,\n      longPressTimerId: longPressTimerId\n    });\n    bindCapturingEvents();\n  }, [bindCapturingEvents, getPhase, setPhase, startDragging]);\n  useIsomorphicLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n      var phase = getPhase();\n      if (phase.type === 'PENDING') {\n        clearTimeout(phase.longPressTimerId);\n        setPhase(idle$2);\n      }\n    };\n  }, [getPhase, listenForCapture, setPhase]);\n  useIsomorphicLayoutEffect(function webkitHack() {\n    var unbind = bindEvents(window, [{\n      eventName: 'touchmove',\n      fn: function fn() {},\n      options: {\n        capture: false,\n        passive: false\n      }\n    }]);\n    return unbind;\n  }, []);\n}\nfunction useValidateSensorHooks(sensorHooks) {\n  useDev(function () {\n    var previousRef = usePrevious(sensorHooks);\n    useDevSetupWarning(function () {\n      !(previousRef.current.length === sensorHooks.length) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot change the amount of sensor hooks after mounting') : invariant(false) : void 0;\n    });\n  });\n}\nvar interactiveTagNames = {\n  input: true,\n  button: true,\n  textarea: true,\n  select: true,\n  option: true,\n  optgroup: true,\n  video: true,\n  audio: true\n};\nfunction isAnInteractiveElement(parent, current) {\n  if (current == null) {\n    return false;\n  }\n  var hasAnInteractiveTag = Boolean(interactiveTagNames[current.tagName.toLowerCase()]);\n  if (hasAnInteractiveTag) {\n    return true;\n  }\n  var attribute = current.getAttribute('contenteditable');\n  if (attribute === 'true' || attribute === '') {\n    return true;\n  }\n  if (current === parent) {\n    return false;\n  }\n  return isAnInteractiveElement(parent, current.parentElement);\n}\nfunction isEventInInteractiveElement(draggable, event) {\n  var target = event.target;\n  if (!isHtmlElement(target)) {\n    return false;\n  }\n  return isAnInteractiveElement(draggable, target);\n}\nvar getBorderBoxCenterPosition = function (el) {\n  return getRect(el.getBoundingClientRect()).center;\n};\nfunction isElement(el) {\n  return el instanceof getWindowFromEl(el).Element;\n}\nvar supportedMatchesName = function () {\n  var base = 'matches';\n  if (typeof document === 'undefined') {\n    return base;\n  }\n  var candidates = [base, 'msMatchesSelector', 'webkitMatchesSelector'];\n  var value = find(candidates, function (name) {\n    return name in Element.prototype;\n  });\n  return value || base;\n}();\nfunction closestPonyfill(el, selector) {\n  if (el == null) {\n    return null;\n  }\n  if (el[supportedMatchesName](selector)) {\n    return el;\n  }\n  return closestPonyfill(el.parentElement, selector);\n}\nfunction closest$1(el, selector) {\n  if (el.closest) {\n    return el.closest(selector);\n  }\n  return closestPonyfill(el, selector);\n}\nfunction getSelector(contextId) {\n  return \"[\" + dragHandle.contextId + \"=\\\"\" + contextId + \"\\\"]\";\n}\nfunction findClosestDragHandleFromEvent(contextId, event) {\n  var target = event.target;\n  if (!isElement(target)) {\n    process.env.NODE_ENV !== \"production\" ? warning('event.target must be a Element') : void 0;\n    return null;\n  }\n  var selector = getSelector(contextId);\n  var handle = closest$1(target, selector);\n  if (!handle) {\n    return null;\n  }\n  if (!isHtmlElement(handle)) {\n    process.env.NODE_ENV !== \"production\" ? warning('drag handle must be a HTMLElement') : void 0;\n    return null;\n  }\n  return handle;\n}\nfunction tryGetClosestDraggableIdFromEvent(contextId, event) {\n  var handle = findClosestDragHandleFromEvent(contextId, event);\n  if (!handle) {\n    return null;\n  }\n  return handle.getAttribute(dragHandle.draggableId);\n}\nfunction findDraggable(contextId, draggableId) {\n  var selector = \"[\" + draggable.contextId + \"=\\\"\" + contextId + \"\\\"]\";\n  var possible = toArray(document.querySelectorAll(selector));\n  var draggable$1 = find(possible, function (el) {\n    return el.getAttribute(draggable.id) === draggableId;\n  });\n  if (!draggable$1) {\n    return null;\n  }\n  if (!isHtmlElement(draggable$1)) {\n    process.env.NODE_ENV !== \"production\" ? warning('Draggable element is not a HTMLElement') : void 0;\n    return null;\n  }\n  return draggable$1;\n}\nfunction preventDefault(event) {\n  event.preventDefault();\n}\nfunction _isActive(_ref) {\n  var expected = _ref.expected,\n    phase = _ref.phase,\n    isLockActive = _ref.isLockActive,\n    shouldWarn = _ref.shouldWarn;\n  if (!isLockActive()) {\n    if (shouldWarn) {\n      process.env.NODE_ENV !== \"production\" ? warning(\"\\n        Cannot perform action.\\n        The sensor no longer has an action lock.\\n\\n        Tips:\\n\\n        - Throw away your action handlers when forceStop() is called\\n        - Check actions.isActive() if you really need to\\n      \") : void 0;\n    }\n    return false;\n  }\n  if (expected !== phase) {\n    if (shouldWarn) {\n      process.env.NODE_ENV !== \"production\" ? warning(\"\\n        Cannot perform action.\\n        The actions you used belong to an outdated phase\\n\\n        Current phase: \" + expected + \"\\n        You called an action from outdated phase: \" + phase + \"\\n\\n        Tips:\\n\\n        - Do not use preDragActions actions after calling preDragActions.lift()\\n      \") : void 0;\n    }\n    return false;\n  }\n  return true;\n}\nfunction canStart(_ref2) {\n  var lockAPI = _ref2.lockAPI,\n    store = _ref2.store,\n    registry = _ref2.registry,\n    draggableId = _ref2.draggableId;\n  if (lockAPI.isClaimed()) {\n    return false;\n  }\n  var entry = registry.draggable.findById(draggableId);\n  if (!entry) {\n    process.env.NODE_ENV !== \"production\" ? warning(\"Unable to find draggable with id: \" + draggableId) : void 0;\n    return false;\n  }\n  if (!entry.options.isEnabled) {\n    return false;\n  }\n  if (!canStartDrag(store.getState(), draggableId)) {\n    return false;\n  }\n  return true;\n}\nfunction tryStart(_ref3) {\n  var lockAPI = _ref3.lockAPI,\n    contextId = _ref3.contextId,\n    store = _ref3.store,\n    registry = _ref3.registry,\n    draggableId = _ref3.draggableId,\n    forceSensorStop = _ref3.forceSensorStop,\n    sourceEvent = _ref3.sourceEvent;\n  var shouldStart = canStart({\n    lockAPI: lockAPI,\n    store: store,\n    registry: registry,\n    draggableId: draggableId\n  });\n  if (!shouldStart) {\n    return null;\n  }\n  var entry = registry.draggable.getById(draggableId);\n  var el = findDraggable(contextId, entry.descriptor.id);\n  if (!el) {\n    process.env.NODE_ENV !== \"production\" ? warning(\"Unable to find draggable element with id: \" + draggableId) : void 0;\n    return null;\n  }\n  if (sourceEvent && !entry.options.canDragInteractiveElements && isEventInInteractiveElement(el, sourceEvent)) {\n    return null;\n  }\n  var lock = lockAPI.claim(forceSensorStop || noop);\n  var phase = 'PRE_DRAG';\n  function getShouldRespectForcePress() {\n    return entry.options.shouldRespectForcePress;\n  }\n  function isLockActive() {\n    return lockAPI.isActive(lock);\n  }\n  function tryDispatch(expected, getAction) {\n    if (_isActive({\n      expected: expected,\n      phase: phase,\n      isLockActive: isLockActive,\n      shouldWarn: true\n    })) {\n      store.dispatch(getAction());\n    }\n  }\n  var tryDispatchWhenDragging = tryDispatch.bind(null, 'DRAGGING');\n  function lift$1(args) {\n    function completed() {\n      lockAPI.release();\n      phase = 'COMPLETED';\n    }\n    if (phase !== 'PRE_DRAG') {\n      completed();\n      !(phase === 'PRE_DRAG') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot lift in phase \" + phase) : invariant(false) : void 0;\n    }\n    store.dispatch(lift(args.liftActionArgs));\n    phase = 'DRAGGING';\n    function finish(reason, options) {\n      if (options === void 0) {\n        options = {\n          shouldBlockNextClick: false\n        };\n      }\n      args.cleanup();\n      if (options.shouldBlockNextClick) {\n        var unbind = bindEvents(window, [{\n          eventName: 'click',\n          fn: preventDefault,\n          options: {\n            once: true,\n            passive: false,\n            capture: true\n          }\n        }]);\n        setTimeout(unbind);\n      }\n      completed();\n      store.dispatch(drop({\n        reason: reason\n      }));\n    }\n    return _extends({\n      isActive: function isActive() {\n        return _isActive({\n          expected: 'DRAGGING',\n          phase: phase,\n          isLockActive: isLockActive,\n          shouldWarn: false\n        });\n      },\n      shouldRespectForcePress: getShouldRespectForcePress,\n      drop: function drop(options) {\n        return finish('DROP', options);\n      },\n      cancel: function cancel(options) {\n        return finish('CANCEL', options);\n      }\n    }, args.actions);\n  }\n  function fluidLift(clientSelection) {\n    var move$1 = rafSchd(function (client) {\n      tryDispatchWhenDragging(function () {\n        return move({\n          client: client\n        });\n      });\n    });\n    var api = lift$1({\n      liftActionArgs: {\n        id: draggableId,\n        clientSelection: clientSelection,\n        movementMode: 'FLUID'\n      },\n      cleanup: function cleanup() {\n        return move$1.cancel();\n      },\n      actions: {\n        move: move$1\n      }\n    });\n    return _extends({}, api, {\n      move: move$1\n    });\n  }\n  function snapLift() {\n    var actions = {\n      moveUp: function moveUp$1() {\n        return tryDispatchWhenDragging(moveUp);\n      },\n      moveRight: function moveRight$1() {\n        return tryDispatchWhenDragging(moveRight);\n      },\n      moveDown: function moveDown$1() {\n        return tryDispatchWhenDragging(moveDown);\n      },\n      moveLeft: function moveLeft$1() {\n        return tryDispatchWhenDragging(moveLeft);\n      }\n    };\n    return lift$1({\n      liftActionArgs: {\n        id: draggableId,\n        clientSelection: getBorderBoxCenterPosition(el),\n        movementMode: 'SNAP'\n      },\n      cleanup: noop,\n      actions: actions\n    });\n  }\n  function abortPreDrag() {\n    var shouldRelease = _isActive({\n      expected: 'PRE_DRAG',\n      phase: phase,\n      isLockActive: isLockActive,\n      shouldWarn: true\n    });\n    if (shouldRelease) {\n      lockAPI.release();\n    }\n  }\n  var preDrag = {\n    isActive: function isActive() {\n      return _isActive({\n        expected: 'PRE_DRAG',\n        phase: phase,\n        isLockActive: isLockActive,\n        shouldWarn: false\n      });\n    },\n    shouldRespectForcePress: getShouldRespectForcePress,\n    fluidLift: fluidLift,\n    snapLift: snapLift,\n    abort: abortPreDrag\n  };\n  return preDrag;\n}\nvar defaultSensors = [useMouseSensor, useKeyboardSensor, useTouchSensor];\nfunction useSensorMarshal(_ref4) {\n  var contextId = _ref4.contextId,\n    store = _ref4.store,\n    registry = _ref4.registry,\n    customSensors = _ref4.customSensors,\n    enableDefaultSensors = _ref4.enableDefaultSensors;\n  var useSensors = [].concat(enableDefaultSensors ? defaultSensors : [], customSensors || []);\n  var lockAPI = useState(function () {\n    return create();\n  })[0];\n  var tryAbandonLock = useCallback(function tryAbandonLock(previous, current) {\n    if (previous.isDragging && !current.isDragging) {\n      lockAPI.tryAbandon();\n    }\n  }, [lockAPI]);\n  useIsomorphicLayoutEffect(function listenToStore() {\n    var previous = store.getState();\n    var unsubscribe = store.subscribe(function () {\n      var current = store.getState();\n      tryAbandonLock(previous, current);\n      previous = current;\n    });\n    return unsubscribe;\n  }, [lockAPI, store, tryAbandonLock]);\n  useIsomorphicLayoutEffect(function () {\n    return lockAPI.tryAbandon;\n  }, [lockAPI.tryAbandon]);\n  var canGetLock = useCallback(function (draggableId) {\n    return canStart({\n      lockAPI: lockAPI,\n      registry: registry,\n      store: store,\n      draggableId: draggableId\n    });\n  }, [lockAPI, registry, store]);\n  var tryGetLock = useCallback(function (draggableId, forceStop, options) {\n    return tryStart({\n      lockAPI: lockAPI,\n      registry: registry,\n      contextId: contextId,\n      store: store,\n      draggableId: draggableId,\n      forceSensorStop: forceStop,\n      sourceEvent: options && options.sourceEvent ? options.sourceEvent : null\n    });\n  }, [contextId, lockAPI, registry, store]);\n  var findClosestDraggableId = useCallback(function (event) {\n    return tryGetClosestDraggableIdFromEvent(contextId, event);\n  }, [contextId]);\n  var findOptionsForDraggable = useCallback(function (id) {\n    var entry = registry.draggable.findById(id);\n    return entry ? entry.options : null;\n  }, [registry.draggable]);\n  var tryReleaseLock = useCallback(function tryReleaseLock() {\n    if (!lockAPI.isClaimed()) {\n      return;\n    }\n    lockAPI.tryAbandon();\n    if (store.getState().phase !== 'IDLE') {\n      store.dispatch(flush());\n    }\n  }, [lockAPI, store]);\n  var isLockClaimed = useCallback(lockAPI.isClaimed, [lockAPI]);\n  var api = useMemo(function () {\n    return {\n      canGetLock: canGetLock,\n      tryGetLock: tryGetLock,\n      findClosestDraggableId: findClosestDraggableId,\n      findOptionsForDraggable: findOptionsForDraggable,\n      tryReleaseLock: tryReleaseLock,\n      isLockClaimed: isLockClaimed\n    };\n  }, [canGetLock, tryGetLock, findClosestDraggableId, findOptionsForDraggable, tryReleaseLock, isLockClaimed]);\n  useValidateSensorHooks(useSensors);\n  for (var i = 0; i < useSensors.length; i++) {\n    useSensors[i](api);\n  }\n}\nvar createResponders = function createResponders(props) {\n  return {\n    onBeforeCapture: props.onBeforeCapture,\n    onBeforeDragStart: props.onBeforeDragStart,\n    onDragStart: props.onDragStart,\n    onDragEnd: props.onDragEnd,\n    onDragUpdate: props.onDragUpdate\n  };\n};\nfunction getStore(lazyRef) {\n  !lazyRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find store from lazy ref') : invariant(false) : void 0;\n  return lazyRef.current;\n}\nfunction App(props) {\n  var contextId = props.contextId,\n    setCallbacks = props.setCallbacks,\n    sensors = props.sensors,\n    nonce = props.nonce,\n    dragHandleUsageInstructions = props.dragHandleUsageInstructions;\n  var lazyStoreRef = useRef(null);\n  useStartupValidation();\n  var lastPropsRef = usePrevious(props);\n  var getResponders = useCallback(function () {\n    return createResponders(lastPropsRef.current);\n  }, [lastPropsRef]);\n  var announce = useAnnouncer(contextId);\n  var dragHandleUsageInstructionsId = useHiddenTextElement({\n    contextId: contextId,\n    text: dragHandleUsageInstructions\n  });\n  var styleMarshal = useStyleMarshal(contextId, nonce);\n  var lazyDispatch = useCallback(function (action) {\n    getStore(lazyStoreRef).dispatch(action);\n  }, []);\n  var marshalCallbacks = useMemo(function () {\n    return bindActionCreators({\n      publishWhileDragging: publishWhileDragging,\n      updateDroppableScroll: updateDroppableScroll,\n      updateDroppableIsEnabled: updateDroppableIsEnabled,\n      updateDroppableIsCombineEnabled: updateDroppableIsCombineEnabled,\n      collectionStarting: collectionStarting\n    }, lazyDispatch);\n  }, [lazyDispatch]);\n  var registry = useRegistry();\n  var dimensionMarshal = useMemo(function () {\n    return createDimensionMarshal(registry, marshalCallbacks);\n  }, [registry, marshalCallbacks]);\n  var autoScroller = useMemo(function () {\n    return createAutoScroller(_extends({\n      scrollWindow: scrollWindow,\n      scrollDroppable: dimensionMarshal.scrollDroppable\n    }, bindActionCreators({\n      move: move\n    }, lazyDispatch)));\n  }, [dimensionMarshal.scrollDroppable, lazyDispatch]);\n  var focusMarshal = useFocusMarshal(contextId);\n  var store = useMemo(function () {\n    return createStore({\n      announce: announce,\n      autoScroller: autoScroller,\n      dimensionMarshal: dimensionMarshal,\n      focusMarshal: focusMarshal,\n      getResponders: getResponders,\n      styleMarshal: styleMarshal\n    });\n  }, [announce, autoScroller, dimensionMarshal, focusMarshal, getResponders, styleMarshal]);\n  if (process.env.NODE_ENV !== 'production') {\n    if (lazyStoreRef.current && lazyStoreRef.current !== store) {\n      process.env.NODE_ENV !== \"production\" ? warning('unexpected store change') : void 0;\n    }\n  }\n  lazyStoreRef.current = store;\n  var tryResetStore = useCallback(function () {\n    var current = getStore(lazyStoreRef);\n    var state = current.getState();\n    if (state.phase !== 'IDLE') {\n      current.dispatch(flush());\n    }\n  }, []);\n  var isDragging = useCallback(function () {\n    var state = getStore(lazyStoreRef).getState();\n    return state.isDragging || state.phase === 'DROP_ANIMATING';\n  }, []);\n  var appCallbacks = useMemo(function () {\n    return {\n      isDragging: isDragging,\n      tryAbort: tryResetStore\n    };\n  }, [isDragging, tryResetStore]);\n  setCallbacks(appCallbacks);\n  var getCanLift = useCallback(function (id) {\n    return canStartDrag(getStore(lazyStoreRef).getState(), id);\n  }, []);\n  var getIsMovementAllowed = useCallback(function () {\n    return isMovementAllowed(getStore(lazyStoreRef).getState());\n  }, []);\n  var appContext = useMemo(function () {\n    return {\n      marshal: dimensionMarshal,\n      focus: focusMarshal,\n      contextId: contextId,\n      canLift: getCanLift,\n      isMovementAllowed: getIsMovementAllowed,\n      dragHandleUsageInstructionsId: dragHandleUsageInstructionsId,\n      registry: registry\n    };\n  }, [contextId, dimensionMarshal, dragHandleUsageInstructionsId, focusMarshal, getCanLift, getIsMovementAllowed, registry]);\n  useSensorMarshal({\n    contextId: contextId,\n    store: store,\n    registry: registry,\n    customSensors: sensors,\n    enableDefaultSensors: props.enableDefaultSensors !== false\n  });\n  useEffect(function () {\n    return tryResetStore;\n  }, [tryResetStore]);\n  return React.createElement(AppContext.Provider, {\n    value: appContext\n  }, React.createElement(Provider, {\n    context: StoreContext,\n    store: store\n  }, props.children));\n}\nvar count$1 = 0;\nfunction reset$1() {\n  count$1 = 0;\n}\nfunction useInstanceCount() {\n  return useMemo(function () {\n    return \"\" + count$1++;\n  }, []);\n}\nfunction resetServerContext() {\n  reset$1();\n  reset();\n}\nfunction DragDropContext(props) {\n  var contextId = useInstanceCount();\n  var dragHandleUsageInstructions = props.dragHandleUsageInstructions || preset.dragHandleUsageInstructions;\n  return React.createElement(ErrorBoundary, null, function (setCallbacks) {\n    return React.createElement(App, {\n      nonce: props.nonce,\n      contextId: contextId,\n      setCallbacks: setCallbacks,\n      dragHandleUsageInstructions: dragHandleUsageInstructions,\n      enableDefaultSensors: props.enableDefaultSensors,\n      sensors: props.sensors,\n      onBeforeCapture: props.onBeforeCapture,\n      onBeforeDragStart: props.onBeforeDragStart,\n      onDragStart: props.onDragStart,\n      onDragUpdate: props.onDragUpdate,\n      onDragEnd: props.onDragEnd\n    }, props.children);\n  });\n}\nvar isEqual$1 = function isEqual(base) {\n  return function (value) {\n    return base === value;\n  };\n};\nvar isScroll = isEqual$1('scroll');\nvar isAuto = isEqual$1('auto');\nvar isVisible$1 = isEqual$1('visible');\nvar isEither = function isEither(overflow, fn) {\n  return fn(overflow.overflowX) || fn(overflow.overflowY);\n};\nvar isBoth = function isBoth(overflow, fn) {\n  return fn(overflow.overflowX) && fn(overflow.overflowY);\n};\nvar isElementScrollable = function isElementScrollable(el) {\n  var style = window.getComputedStyle(el);\n  var overflow = {\n    overflowX: style.overflowX,\n    overflowY: style.overflowY\n  };\n  return isEither(overflow, isScroll) || isEither(overflow, isAuto);\n};\nvar isBodyScrollable = function isBodyScrollable() {\n  if (process.env.NODE_ENV === 'production') {\n    return false;\n  }\n  var body = getBodyElement();\n  var html = document.documentElement;\n  !html ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n  if (!isElementScrollable(body)) {\n    return false;\n  }\n  var htmlStyle = window.getComputedStyle(html);\n  var htmlOverflow = {\n    overflowX: htmlStyle.overflowX,\n    overflowY: htmlStyle.overflowY\n  };\n  if (isBoth(htmlOverflow, isVisible$1)) {\n    return false;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(\"\\n    We have detected that your <body> element might be a scroll container.\\n    We have found no reliable way of detecting whether the <body> element is a scroll container.\\n    Under most circumstances a <body> scroll bar will be on the <html> element (document.documentElement)\\n\\n    Because we cannot determine if the <body> is a scroll container, and generally it is not one,\\n    we will be treating the <body> as *not* a scroll container\\n\\n    More information: https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/guides/how-we-detect-scroll-containers.md\\n  \") : void 0;\n  return false;\n};\nvar getClosestScrollable = function getClosestScrollable(el) {\n  if (el == null) {\n    return null;\n  }\n  if (el === document.body) {\n    return isBodyScrollable() ? el : null;\n  }\n  if (el === document.documentElement) {\n    return null;\n  }\n  if (!isElementScrollable(el)) {\n    return getClosestScrollable(el.parentElement);\n  }\n  return el;\n};\nvar checkForNestedScrollContainers = function (scrollable) {\n  if (!scrollable) {\n    return;\n  }\n  var anotherScrollParent = getClosestScrollable(scrollable.parentElement);\n  if (!anotherScrollParent) {\n    return;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(\"\\n    Droppable: unsupported nested scroll container detected.\\n    A Droppable can only have one scroll parent (which can be itself)\\n    Nested scroll containers are currently not supported.\\n\\n    We hope to support nested scroll containers soon: https://github.com/atlassian/react-beautiful-dnd/issues/131\\n  \") : void 0;\n};\nvar getScroll$1 = function (el) {\n  return {\n    x: el.scrollLeft,\n    y: el.scrollTop\n  };\n};\nvar getIsFixed = function getIsFixed(el) {\n  if (!el) {\n    return false;\n  }\n  var style = window.getComputedStyle(el);\n  if (style.position === 'fixed') {\n    return true;\n  }\n  return getIsFixed(el.parentElement);\n};\nvar getEnv = function (start) {\n  var closestScrollable = getClosestScrollable(start);\n  var isFixedOnPage = getIsFixed(start);\n  return {\n    closestScrollable: closestScrollable,\n    isFixedOnPage: isFixedOnPage\n  };\n};\nvar getDroppableDimension = function (_ref) {\n  var descriptor = _ref.descriptor,\n    isEnabled = _ref.isEnabled,\n    isCombineEnabled = _ref.isCombineEnabled,\n    isFixedOnPage = _ref.isFixedOnPage,\n    direction = _ref.direction,\n    client = _ref.client,\n    page = _ref.page,\n    closest = _ref.closest;\n  var frame = function () {\n    if (!closest) {\n      return null;\n    }\n    var scrollSize = closest.scrollSize,\n      frameClient = closest.client;\n    var maxScroll = getMaxScroll({\n      scrollHeight: scrollSize.scrollHeight,\n      scrollWidth: scrollSize.scrollWidth,\n      height: frameClient.paddingBox.height,\n      width: frameClient.paddingBox.width\n    });\n    return {\n      pageMarginBox: closest.page.marginBox,\n      frameClient: frameClient,\n      scrollSize: scrollSize,\n      shouldClipSubject: closest.shouldClipSubject,\n      scroll: {\n        initial: closest.scroll,\n        current: closest.scroll,\n        max: maxScroll,\n        diff: {\n          value: origin,\n          displacement: origin\n        }\n      }\n    };\n  }();\n  var axis = direction === 'vertical' ? vertical : horizontal;\n  var subject = getSubject({\n    page: page,\n    withPlaceholder: null,\n    axis: axis,\n    frame: frame\n  });\n  var dimension = {\n    descriptor: descriptor,\n    isCombineEnabled: isCombineEnabled,\n    isFixedOnPage: isFixedOnPage,\n    axis: axis,\n    isEnabled: isEnabled,\n    client: client,\n    page: page,\n    frame: frame,\n    subject: subject\n  };\n  return dimension;\n};\nvar getClient = function getClient(targetRef, closestScrollable) {\n  var base = getBox(targetRef);\n  if (!closestScrollable) {\n    return base;\n  }\n  if (targetRef !== closestScrollable) {\n    return base;\n  }\n  var top = base.paddingBox.top - closestScrollable.scrollTop;\n  var left = base.paddingBox.left - closestScrollable.scrollLeft;\n  var bottom = top + closestScrollable.scrollHeight;\n  var right = left + closestScrollable.scrollWidth;\n  var paddingBox = {\n    top: top,\n    right: right,\n    bottom: bottom,\n    left: left\n  };\n  var borderBox = expand(paddingBox, base.border);\n  var client = createBox({\n    borderBox: borderBox,\n    margin: base.margin,\n    border: base.border,\n    padding: base.padding\n  });\n  return client;\n};\nvar getDimension = function (_ref) {\n  var ref = _ref.ref,\n    descriptor = _ref.descriptor,\n    env = _ref.env,\n    windowScroll = _ref.windowScroll,\n    direction = _ref.direction,\n    isDropDisabled = _ref.isDropDisabled,\n    isCombineEnabled = _ref.isCombineEnabled,\n    shouldClipSubject = _ref.shouldClipSubject;\n  var closestScrollable = env.closestScrollable;\n  var client = getClient(ref, closestScrollable);\n  var page = withScroll(client, windowScroll);\n  var closest = function () {\n    if (!closestScrollable) {\n      return null;\n    }\n    var frameClient = getBox(closestScrollable);\n    var scrollSize = {\n      scrollHeight: closestScrollable.scrollHeight,\n      scrollWidth: closestScrollable.scrollWidth\n    };\n    return {\n      client: frameClient,\n      page: withScroll(frameClient, windowScroll),\n      scroll: getScroll$1(closestScrollable),\n      scrollSize: scrollSize,\n      shouldClipSubject: shouldClipSubject\n    };\n  }();\n  var dimension = getDroppableDimension({\n    descriptor: descriptor,\n    isEnabled: !isDropDisabled,\n    isCombineEnabled: isCombineEnabled,\n    isFixedOnPage: env.isFixedOnPage,\n    direction: direction,\n    client: client,\n    page: page,\n    closest: closest\n  });\n  return dimension;\n};\nvar immediate = {\n  passive: false\n};\nvar delayed = {\n  passive: true\n};\nvar getListenerOptions = function (options) {\n  return options.shouldPublishImmediately ? immediate : delayed;\n};\nfunction useRequiredContext(Context) {\n  var result = useContext(Context);\n  !result ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find required context') : invariant(false) : void 0;\n  return result;\n}\nvar getClosestScrollableFromDrag = function getClosestScrollableFromDrag(dragging) {\n  return dragging && dragging.env.closestScrollable || null;\n};\nfunction useDroppablePublisher(args) {\n  var whileDraggingRef = useRef(null);\n  var appContext = useRequiredContext(AppContext);\n  var uniqueId = useUniqueId('droppable');\n  var registry = appContext.registry,\n    marshal = appContext.marshal;\n  var previousRef = usePrevious(args);\n  var descriptor = useMemo(function () {\n    return {\n      id: args.droppableId,\n      type: args.type,\n      mode: args.mode\n    };\n  }, [args.droppableId, args.mode, args.type]);\n  var publishedDescriptorRef = useRef(descriptor);\n  var memoizedUpdateScroll = useMemo(function () {\n    return memoizeOne(function (x, y) {\n      !whileDraggingRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only update scroll when dragging') : invariant(false) : void 0;\n      var scroll = {\n        x: x,\n        y: y\n      };\n      marshal.updateDroppableScroll(descriptor.id, scroll);\n    });\n  }, [descriptor.id, marshal]);\n  var getClosestScroll = useCallback(function () {\n    var dragging = whileDraggingRef.current;\n    if (!dragging || !dragging.env.closestScrollable) {\n      return origin;\n    }\n    return getScroll$1(dragging.env.closestScrollable);\n  }, []);\n  var updateScroll = useCallback(function () {\n    var scroll = getClosestScroll();\n    memoizedUpdateScroll(scroll.x, scroll.y);\n  }, [getClosestScroll, memoizedUpdateScroll]);\n  var scheduleScrollUpdate = useMemo(function () {\n    return rafSchd(updateScroll);\n  }, [updateScroll]);\n  var onClosestScroll = useCallback(function () {\n    var dragging = whileDraggingRef.current;\n    var closest = getClosestScrollableFromDrag(dragging);\n    !(dragging && closest) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find scroll options while scrolling') : invariant(false) : void 0;\n    var options = dragging.scrollOptions;\n    if (options.shouldPublishImmediately) {\n      updateScroll();\n      return;\n    }\n    scheduleScrollUpdate();\n  }, [scheduleScrollUpdate, updateScroll]);\n  var getDimensionAndWatchScroll = useCallback(function (windowScroll, options) {\n    !!whileDraggingRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot collect a droppable while a drag is occurring') : invariant(false) : void 0;\n    var previous = previousRef.current;\n    var ref = previous.getDroppableRef();\n    !ref ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot collect without a droppable ref') : invariant(false) : void 0;\n    var env = getEnv(ref);\n    var dragging = {\n      ref: ref,\n      descriptor: descriptor,\n      env: env,\n      scrollOptions: options\n    };\n    whileDraggingRef.current = dragging;\n    var dimension = getDimension({\n      ref: ref,\n      descriptor: descriptor,\n      env: env,\n      windowScroll: windowScroll,\n      direction: previous.direction,\n      isDropDisabled: previous.isDropDisabled,\n      isCombineEnabled: previous.isCombineEnabled,\n      shouldClipSubject: !previous.ignoreContainerClipping\n    });\n    var scrollable = env.closestScrollable;\n    if (scrollable) {\n      scrollable.setAttribute(scrollContainer.contextId, appContext.contextId);\n      scrollable.addEventListener('scroll', onClosestScroll, getListenerOptions(dragging.scrollOptions));\n      if (process.env.NODE_ENV !== 'production') {\n        checkForNestedScrollContainers(scrollable);\n      }\n    }\n    return dimension;\n  }, [appContext.contextId, descriptor, onClosestScroll, previousRef]);\n  var getScrollWhileDragging = useCallback(function () {\n    var dragging = whileDraggingRef.current;\n    var closest = getClosestScrollableFromDrag(dragging);\n    !(dragging && closest) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only recollect Droppable client for Droppables that have a scroll container') : invariant(false) : void 0;\n    return getScroll$1(closest);\n  }, []);\n  var dragStopped = useCallback(function () {\n    var dragging = whileDraggingRef.current;\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop drag when no active drag') : invariant(false) : void 0;\n    var closest = getClosestScrollableFromDrag(dragging);\n    whileDraggingRef.current = null;\n    if (!closest) {\n      return;\n    }\n    scheduleScrollUpdate.cancel();\n    closest.removeAttribute(scrollContainer.contextId);\n    closest.removeEventListener('scroll', onClosestScroll, getListenerOptions(dragging.scrollOptions));\n  }, [onClosestScroll, scheduleScrollUpdate]);\n  var scroll = useCallback(function (change) {\n    var dragging = whileDraggingRef.current;\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot scroll when there is no drag') : invariant(false) : void 0;\n    var closest = getClosestScrollableFromDrag(dragging);\n    !closest ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot scroll a droppable with no closest scrollable') : invariant(false) : void 0;\n    closest.scrollTop += change.y;\n    closest.scrollLeft += change.x;\n  }, []);\n  var callbacks = useMemo(function () {\n    return {\n      getDimensionAndWatchScroll: getDimensionAndWatchScroll,\n      getScrollWhileDragging: getScrollWhileDragging,\n      dragStopped: dragStopped,\n      scroll: scroll\n    };\n  }, [dragStopped, getDimensionAndWatchScroll, getScrollWhileDragging, scroll]);\n  var entry = useMemo(function () {\n    return {\n      uniqueId: uniqueId,\n      descriptor: descriptor,\n      callbacks: callbacks\n    };\n  }, [callbacks, descriptor, uniqueId]);\n  useIsomorphicLayoutEffect(function () {\n    publishedDescriptorRef.current = entry.descriptor;\n    registry.droppable.register(entry);\n    return function () {\n      if (whileDraggingRef.current) {\n        process.env.NODE_ENV !== \"production\" ? warning('Unsupported: changing the droppableId or type of a Droppable during a drag') : void 0;\n        dragStopped();\n      }\n      registry.droppable.unregister(entry);\n    };\n  }, [callbacks, descriptor, dragStopped, entry, marshal, registry.droppable]);\n  useIsomorphicLayoutEffect(function () {\n    if (!whileDraggingRef.current) {\n      return;\n    }\n    marshal.updateDroppableIsEnabled(publishedDescriptorRef.current.id, !args.isDropDisabled);\n  }, [args.isDropDisabled, marshal]);\n  useIsomorphicLayoutEffect(function () {\n    if (!whileDraggingRef.current) {\n      return;\n    }\n    marshal.updateDroppableIsCombineEnabled(publishedDescriptorRef.current.id, args.isCombineEnabled);\n  }, [args.isCombineEnabled, marshal]);\n}\nfunction noop$2() {}\nvar empty = {\n  width: 0,\n  height: 0,\n  margin: noSpacing\n};\nvar getSize = function getSize(_ref) {\n  var isAnimatingOpenOnMount = _ref.isAnimatingOpenOnMount,\n    placeholder = _ref.placeholder,\n    animate = _ref.animate;\n  if (isAnimatingOpenOnMount) {\n    return empty;\n  }\n  if (animate === 'close') {\n    return empty;\n  }\n  return {\n    height: placeholder.client.borderBox.height,\n    width: placeholder.client.borderBox.width,\n    margin: placeholder.client.margin\n  };\n};\nvar getStyle = function getStyle(_ref2) {\n  var isAnimatingOpenOnMount = _ref2.isAnimatingOpenOnMount,\n    placeholder = _ref2.placeholder,\n    animate = _ref2.animate;\n  var size = getSize({\n    isAnimatingOpenOnMount: isAnimatingOpenOnMount,\n    placeholder: placeholder,\n    animate: animate\n  });\n  return {\n    display: placeholder.display,\n    boxSizing: 'border-box',\n    width: size.width,\n    height: size.height,\n    marginTop: size.margin.top,\n    marginRight: size.margin.right,\n    marginBottom: size.margin.bottom,\n    marginLeft: size.margin.left,\n    flexShrink: '0',\n    flexGrow: '0',\n    pointerEvents: 'none',\n    transition: animate !== 'none' ? transitions.placeholder : null\n  };\n};\nfunction Placeholder(props) {\n  var animateOpenTimerRef = useRef(null);\n  var tryClearAnimateOpenTimer = useCallback(function () {\n    if (!animateOpenTimerRef.current) {\n      return;\n    }\n    clearTimeout(animateOpenTimerRef.current);\n    animateOpenTimerRef.current = null;\n  }, []);\n  var animate = props.animate,\n    onTransitionEnd = props.onTransitionEnd,\n    onClose = props.onClose,\n    contextId = props.contextId;\n  var _useState = useState(props.animate === 'open'),\n    isAnimatingOpenOnMount = _useState[0],\n    setIsAnimatingOpenOnMount = _useState[1];\n  useEffect(function () {\n    if (!isAnimatingOpenOnMount) {\n      return noop$2;\n    }\n    if (animate !== 'open') {\n      tryClearAnimateOpenTimer();\n      setIsAnimatingOpenOnMount(false);\n      return noop$2;\n    }\n    if (animateOpenTimerRef.current) {\n      return noop$2;\n    }\n    animateOpenTimerRef.current = setTimeout(function () {\n      animateOpenTimerRef.current = null;\n      setIsAnimatingOpenOnMount(false);\n    });\n    return tryClearAnimateOpenTimer;\n  }, [animate, isAnimatingOpenOnMount, tryClearAnimateOpenTimer]);\n  var onSizeChangeEnd = useCallback(function (event) {\n    if (event.propertyName !== 'height') {\n      return;\n    }\n    onTransitionEnd();\n    if (animate === 'close') {\n      onClose();\n    }\n  }, [animate, onClose, onTransitionEnd]);\n  var style = getStyle({\n    isAnimatingOpenOnMount: isAnimatingOpenOnMount,\n    animate: props.animate,\n    placeholder: props.placeholder\n  });\n  return React.createElement(props.placeholder.tagName, {\n    style: style,\n    'data-rbd-placeholder-context-id': contextId,\n    onTransitionEnd: onSizeChangeEnd,\n    ref: props.innerRef\n  });\n}\nvar Placeholder$1 = React.memo(Placeholder);\nvar DroppableContext = React.createContext(null);\nfunction checkIsValidInnerRef(el) {\n  !(el && isHtmlElement(el)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"\\n    provided.innerRef has not been provided with a HTMLElement.\\n\\n    You can find a guide on using the innerRef callback functions at:\\n    https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/guides/using-inner-ref.md\\n  \") : invariant(false) : void 0;\n}\nfunction isBoolean(value) {\n  return typeof value === 'boolean';\n}\nfunction runChecks(args, checks) {\n  checks.forEach(function (check) {\n    return check(args);\n  });\n}\nvar shared = [function required(_ref) {\n  var props = _ref.props;\n  !props.droppableId ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'A Droppable requires a droppableId prop') : invariant(false) : void 0;\n  !(typeof props.droppableId === 'string') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"A Droppable requires a [string] droppableId. Provided: [\" + typeof props.droppableId + \"]\") : invariant(false) : void 0;\n}, function _boolean(_ref2) {\n  var props = _ref2.props;\n  !isBoolean(props.isDropDisabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'isDropDisabled must be a boolean') : invariant(false) : void 0;\n  !isBoolean(props.isCombineEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'isCombineEnabled must be a boolean') : invariant(false) : void 0;\n  !isBoolean(props.ignoreContainerClipping) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'ignoreContainerClipping must be a boolean') : invariant(false) : void 0;\n}, function ref(_ref3) {\n  var getDroppableRef = _ref3.getDroppableRef;\n  checkIsValidInnerRef(getDroppableRef());\n}];\nvar standard = [function placeholder(_ref4) {\n  var props = _ref4.props,\n    getPlaceholderRef = _ref4.getPlaceholderRef;\n  if (!props.placeholder) {\n    return;\n  }\n  var ref = getPlaceholderRef();\n  if (ref) {\n    return;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(\"\\n      Droppable setup issue [droppableId: \\\"\" + props.droppableId + \"\\\"]:\\n      DroppableProvided > placeholder could not be found.\\n\\n      Please be sure to add the {provided.placeholder} React Node as a child of your Droppable.\\n      More information: https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/api/droppable.md\\n    \") : void 0;\n}];\nvar virtual = [function hasClone(_ref5) {\n  var props = _ref5.props;\n  !props.renderClone ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Must provide a clone render function (renderClone) for virtual lists') : invariant(false) : void 0;\n}, function hasNoPlaceholder(_ref6) {\n  var getPlaceholderRef = _ref6.getPlaceholderRef;\n  !!getPlaceholderRef() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected virtual list to not have a placeholder') : invariant(false) : void 0;\n}];\nfunction useValidation(args) {\n  useDevSetupWarning(function () {\n    runChecks(args, shared);\n    if (args.props.mode === 'standard') {\n      runChecks(args, standard);\n    }\n    if (args.props.mode === 'virtual') {\n      runChecks(args, virtual);\n    }\n  });\n}\nvar AnimateInOut = function (_React$PureComponent) {\n  _inheritsLoose(AnimateInOut, _React$PureComponent);\n  function AnimateInOut() {\n    var _this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _React$PureComponent.call.apply(_React$PureComponent, [this].concat(args)) || this;\n    _this.state = {\n      isVisible: Boolean(_this.props.on),\n      data: _this.props.on,\n      animate: _this.props.shouldAnimate && _this.props.on ? 'open' : 'none'\n    };\n    _this.onClose = function () {\n      if (_this.state.animate !== 'close') {\n        return;\n      }\n      _this.setState({\n        isVisible: false\n      });\n    };\n    return _this;\n  }\n  AnimateInOut.getDerivedStateFromProps = function getDerivedStateFromProps(props, state) {\n    if (!props.shouldAnimate) {\n      return {\n        isVisible: Boolean(props.on),\n        data: props.on,\n        animate: 'none'\n      };\n    }\n    if (props.on) {\n      return {\n        isVisible: true,\n        data: props.on,\n        animate: 'open'\n      };\n    }\n    if (state.isVisible) {\n      return {\n        isVisible: true,\n        data: state.data,\n        animate: 'close'\n      };\n    }\n    return {\n      isVisible: false,\n      animate: 'close',\n      data: null\n    };\n  };\n  var _proto = AnimateInOut.prototype;\n  _proto.render = function render() {\n    if (!this.state.isVisible) {\n      return null;\n    }\n    var provided = {\n      onClose: this.onClose,\n      data: this.state.data,\n      animate: this.state.animate\n    };\n    return this.props.children(provided);\n  };\n  return AnimateInOut;\n}(React.PureComponent);\nvar zIndexOptions = {\n  dragging: 5000,\n  dropAnimating: 4500\n};\nvar getDraggingTransition = function getDraggingTransition(shouldAnimateDragMovement, dropping) {\n  if (dropping) {\n    return transitions.drop(dropping.duration);\n  }\n  if (shouldAnimateDragMovement) {\n    return transitions.snap;\n  }\n  return transitions.fluid;\n};\nvar getDraggingOpacity = function getDraggingOpacity(isCombining, isDropAnimating) {\n  if (!isCombining) {\n    return null;\n  }\n  return isDropAnimating ? combine.opacity.drop : combine.opacity.combining;\n};\nvar getShouldDraggingAnimate = function getShouldDraggingAnimate(dragging) {\n  if (dragging.forceShouldAnimate != null) {\n    return dragging.forceShouldAnimate;\n  }\n  return dragging.mode === 'SNAP';\n};\nfunction getDraggingStyle(dragging) {\n  var dimension = dragging.dimension;\n  var box = dimension.client;\n  var offset = dragging.offset,\n    combineWith = dragging.combineWith,\n    dropping = dragging.dropping;\n  var isCombining = Boolean(combineWith);\n  var shouldAnimate = getShouldDraggingAnimate(dragging);\n  var isDropAnimating = Boolean(dropping);\n  var transform = isDropAnimating ? transforms.drop(offset, isCombining) : transforms.moveTo(offset);\n  var style = {\n    position: 'fixed',\n    top: box.marginBox.top,\n    left: box.marginBox.left,\n    boxSizing: 'border-box',\n    width: box.borderBox.width,\n    height: box.borderBox.height,\n    transition: getDraggingTransition(shouldAnimate, dropping),\n    transform: transform,\n    opacity: getDraggingOpacity(isCombining, isDropAnimating),\n    zIndex: isDropAnimating ? zIndexOptions.dropAnimating : zIndexOptions.dragging,\n    pointerEvents: 'none'\n  };\n  return style;\n}\nfunction getSecondaryStyle(secondary) {\n  return {\n    transform: transforms.moveTo(secondary.offset),\n    transition: secondary.shouldAnimateDisplacement ? null : 'none'\n  };\n}\nfunction getStyle$1(mapped) {\n  return mapped.type === 'DRAGGING' ? getDraggingStyle(mapped) : getSecondaryStyle(mapped);\n}\nfunction getDimension$1(descriptor, el, windowScroll) {\n  if (windowScroll === void 0) {\n    windowScroll = origin;\n  }\n  var computedStyles = window.getComputedStyle(el);\n  var borderBox = el.getBoundingClientRect();\n  var client = calculateBox(borderBox, computedStyles);\n  var page = withScroll(client, windowScroll);\n  var placeholder = {\n    client: client,\n    tagName: el.tagName.toLowerCase(),\n    display: computedStyles.display\n  };\n  var displaceBy = {\n    x: client.marginBox.width,\n    y: client.marginBox.height\n  };\n  var dimension = {\n    descriptor: descriptor,\n    placeholder: placeholder,\n    displaceBy: displaceBy,\n    client: client,\n    page: page\n  };\n  return dimension;\n}\nfunction useDraggablePublisher(args) {\n  var uniqueId = useUniqueId('draggable');\n  var descriptor = args.descriptor,\n    registry = args.registry,\n    getDraggableRef = args.getDraggableRef,\n    canDragInteractiveElements = args.canDragInteractiveElements,\n    shouldRespectForcePress = args.shouldRespectForcePress,\n    isEnabled = args.isEnabled;\n  var options = useMemo(function () {\n    return {\n      canDragInteractiveElements: canDragInteractiveElements,\n      shouldRespectForcePress: shouldRespectForcePress,\n      isEnabled: isEnabled\n    };\n  }, [canDragInteractiveElements, isEnabled, shouldRespectForcePress]);\n  var getDimension = useCallback(function (windowScroll) {\n    var el = getDraggableRef();\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot get dimension when no ref is set') : invariant(false) : void 0;\n    return getDimension$1(descriptor, el, windowScroll);\n  }, [descriptor, getDraggableRef]);\n  var entry = useMemo(function () {\n    return {\n      uniqueId: uniqueId,\n      descriptor: descriptor,\n      options: options,\n      getDimension: getDimension\n    };\n  }, [descriptor, getDimension, options, uniqueId]);\n  var publishedRef = useRef(entry);\n  var isFirstPublishRef = useRef(true);\n  useIsomorphicLayoutEffect(function () {\n    registry.draggable.register(publishedRef.current);\n    return function () {\n      return registry.draggable.unregister(publishedRef.current);\n    };\n  }, [registry.draggable]);\n  useIsomorphicLayoutEffect(function () {\n    if (isFirstPublishRef.current) {\n      isFirstPublishRef.current = false;\n      return;\n    }\n    var last = publishedRef.current;\n    publishedRef.current = entry;\n    registry.draggable.update(entry, last);\n  }, [entry, registry.draggable]);\n}\nfunction useValidation$1(props, contextId, getRef) {\n  useDevSetupWarning(function () {\n    function prefix(id) {\n      return \"Draggable[id: \" + id + \"]: \";\n    }\n    var id = props.draggableId;\n    !id ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Draggable requires a draggableId') : invariant(false) : void 0;\n    !(typeof id === 'string') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Draggable requires a [string] draggableId.\\n      Provided: [type: \" + typeof id + \"] (value: \" + id + \")\") : invariant(false) : void 0;\n    !isInteger(props.index) ? process.env.NODE_ENV !== \"production\" ? invariant(false, prefix(id) + \" requires an integer index prop\") : invariant(false) : void 0;\n    if (props.mapped.type === 'DRAGGING') {\n      return;\n    }\n    checkIsValidInnerRef(getRef());\n    if (props.isEnabled) {\n      !findDragHandle(contextId, id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, prefix(id) + \" Unable to find drag handle\") : invariant(false) : void 0;\n    }\n  });\n}\nfunction useClonePropValidation(isClone) {\n  useDev(function () {\n    var initialRef = useRef(isClone);\n    useDevSetupWarning(function () {\n      !(isClone === initialRef.current) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Draggable isClone prop value changed during component life') : invariant(false) : void 0;\n    }, [isClone]);\n  });\n}\nfunction preventHtml5Dnd(event) {\n  event.preventDefault();\n}\nfunction Draggable(props) {\n  var ref = useRef(null);\n  var setRef = useCallback(function (el) {\n    ref.current = el;\n  }, []);\n  var getRef = useCallback(function () {\n    return ref.current;\n  }, []);\n  var _useRequiredContext = useRequiredContext(AppContext),\n    contextId = _useRequiredContext.contextId,\n    dragHandleUsageInstructionsId = _useRequiredContext.dragHandleUsageInstructionsId,\n    registry = _useRequiredContext.registry;\n  var _useRequiredContext2 = useRequiredContext(DroppableContext),\n    type = _useRequiredContext2.type,\n    droppableId = _useRequiredContext2.droppableId;\n  var descriptor = useMemo(function () {\n    return {\n      id: props.draggableId,\n      index: props.index,\n      type: type,\n      droppableId: droppableId\n    };\n  }, [props.draggableId, props.index, type, droppableId]);\n  var children = props.children,\n    draggableId = props.draggableId,\n    isEnabled = props.isEnabled,\n    shouldRespectForcePress = props.shouldRespectForcePress,\n    canDragInteractiveElements = props.canDragInteractiveElements,\n    isClone = props.isClone,\n    mapped = props.mapped,\n    dropAnimationFinishedAction = props.dropAnimationFinished;\n  useValidation$1(props, contextId, getRef);\n  useClonePropValidation(isClone);\n  if (!isClone) {\n    var forPublisher = useMemo(function () {\n      return {\n        descriptor: descriptor,\n        registry: registry,\n        getDraggableRef: getRef,\n        canDragInteractiveElements: canDragInteractiveElements,\n        shouldRespectForcePress: shouldRespectForcePress,\n        isEnabled: isEnabled\n      };\n    }, [descriptor, registry, getRef, canDragInteractiveElements, shouldRespectForcePress, isEnabled]);\n    useDraggablePublisher(forPublisher);\n  }\n  var dragHandleProps = useMemo(function () {\n    return isEnabled ? {\n      tabIndex: 0,\n      role: 'button',\n      'aria-describedby': dragHandleUsageInstructionsId,\n      'data-rbd-drag-handle-draggable-id': draggableId,\n      'data-rbd-drag-handle-context-id': contextId,\n      draggable: false,\n      onDragStart: preventHtml5Dnd\n    } : null;\n  }, [contextId, dragHandleUsageInstructionsId, draggableId, isEnabled]);\n  var onMoveEnd = useCallback(function (event) {\n    if (mapped.type !== 'DRAGGING') {\n      return;\n    }\n    if (!mapped.dropping) {\n      return;\n    }\n    if (event.propertyName !== 'transform') {\n      return;\n    }\n    dropAnimationFinishedAction();\n  }, [dropAnimationFinishedAction, mapped]);\n  var provided = useMemo(function () {\n    var style = getStyle$1(mapped);\n    var onTransitionEnd = mapped.type === 'DRAGGING' && mapped.dropping ? onMoveEnd : null;\n    var result = {\n      innerRef: setRef,\n      draggableProps: {\n        'data-rbd-draggable-context-id': contextId,\n        'data-rbd-draggable-id': draggableId,\n        style: style,\n        onTransitionEnd: onTransitionEnd\n      },\n      dragHandleProps: dragHandleProps\n    };\n    return result;\n  }, [contextId, dragHandleProps, draggableId, mapped, onMoveEnd, setRef]);\n  var rubric = useMemo(function () {\n    return {\n      draggableId: descriptor.id,\n      type: descriptor.type,\n      source: {\n        index: descriptor.index,\n        droppableId: descriptor.droppableId\n      }\n    };\n  }, [descriptor.droppableId, descriptor.id, descriptor.index, descriptor.type]);\n  return children(provided, mapped.snapshot, rubric);\n}\nvar isStrictEqual = function (a, b) {\n  return a === b;\n};\nvar whatIsDraggedOverFromResult = function (result) {\n  var combine = result.combine,\n    destination = result.destination;\n  if (destination) {\n    return destination.droppableId;\n  }\n  if (combine) {\n    return combine.droppableId;\n  }\n  return null;\n};\nvar getCombineWithFromResult = function getCombineWithFromResult(result) {\n  return result.combine ? result.combine.draggableId : null;\n};\nvar getCombineWithFromImpact = function getCombineWithFromImpact(impact) {\n  return impact.at && impact.at.type === 'COMBINE' ? impact.at.combine.draggableId : null;\n};\nfunction getDraggableSelector() {\n  var memoizedOffset = memoizeOne(function (x, y) {\n    return {\n      x: x,\n      y: y\n    };\n  });\n  var getMemoizedSnapshot = memoizeOne(function (mode, isClone, draggingOver, combineWith, dropping) {\n    return {\n      isDragging: true,\n      isClone: isClone,\n      isDropAnimating: Boolean(dropping),\n      dropAnimation: dropping,\n      mode: mode,\n      draggingOver: draggingOver,\n      combineWith: combineWith,\n      combineTargetFor: null\n    };\n  });\n  var getMemoizedProps = memoizeOne(function (offset, mode, dimension, isClone, draggingOver, combineWith, forceShouldAnimate) {\n    return {\n      mapped: {\n        type: 'DRAGGING',\n        dropping: null,\n        draggingOver: draggingOver,\n        combineWith: combineWith,\n        mode: mode,\n        offset: offset,\n        dimension: dimension,\n        forceShouldAnimate: forceShouldAnimate,\n        snapshot: getMemoizedSnapshot(mode, isClone, draggingOver, combineWith, null)\n      }\n    };\n  });\n  var selector = function selector(state, ownProps) {\n    if (state.isDragging) {\n      if (state.critical.draggable.id !== ownProps.draggableId) {\n        return null;\n      }\n      var offset = state.current.client.offset;\n      var dimension = state.dimensions.draggables[ownProps.draggableId];\n      var draggingOver = whatIsDraggedOver(state.impact);\n      var combineWith = getCombineWithFromImpact(state.impact);\n      var forceShouldAnimate = state.forceShouldAnimate;\n      return getMemoizedProps(memoizedOffset(offset.x, offset.y), state.movementMode, dimension, ownProps.isClone, draggingOver, combineWith, forceShouldAnimate);\n    }\n    if (state.phase === 'DROP_ANIMATING') {\n      var completed = state.completed;\n      if (completed.result.draggableId !== ownProps.draggableId) {\n        return null;\n      }\n      var isClone = ownProps.isClone;\n      var _dimension = state.dimensions.draggables[ownProps.draggableId];\n      var result = completed.result;\n      var mode = result.mode;\n      var _draggingOver = whatIsDraggedOverFromResult(result);\n      var _combineWith = getCombineWithFromResult(result);\n      var duration = state.dropDuration;\n      var dropping = {\n        duration: duration,\n        curve: curves.drop,\n        moveTo: state.newHomeClientOffset,\n        opacity: _combineWith ? combine.opacity.drop : null,\n        scale: _combineWith ? combine.scale.drop : null\n      };\n      return {\n        mapped: {\n          type: 'DRAGGING',\n          offset: state.newHomeClientOffset,\n          dimension: _dimension,\n          dropping: dropping,\n          draggingOver: _draggingOver,\n          combineWith: _combineWith,\n          mode: mode,\n          forceShouldAnimate: null,\n          snapshot: getMemoizedSnapshot(mode, isClone, _draggingOver, _combineWith, dropping)\n        }\n      };\n    }\n    return null;\n  };\n  return selector;\n}\nfunction getSecondarySnapshot(combineTargetFor) {\n  return {\n    isDragging: false,\n    isDropAnimating: false,\n    isClone: false,\n    dropAnimation: null,\n    mode: null,\n    draggingOver: null,\n    combineTargetFor: combineTargetFor,\n    combineWith: null\n  };\n}\nvar atRest = {\n  mapped: {\n    type: 'SECONDARY',\n    offset: origin,\n    combineTargetFor: null,\n    shouldAnimateDisplacement: true,\n    snapshot: getSecondarySnapshot(null)\n  }\n};\nfunction getSecondarySelector() {\n  var memoizedOffset = memoizeOne(function (x, y) {\n    return {\n      x: x,\n      y: y\n    };\n  });\n  var getMemoizedSnapshot = memoizeOne(getSecondarySnapshot);\n  var getMemoizedProps = memoizeOne(function (offset, combineTargetFor, shouldAnimateDisplacement) {\n    if (combineTargetFor === void 0) {\n      combineTargetFor = null;\n    }\n    return {\n      mapped: {\n        type: 'SECONDARY',\n        offset: offset,\n        combineTargetFor: combineTargetFor,\n        shouldAnimateDisplacement: shouldAnimateDisplacement,\n        snapshot: getMemoizedSnapshot(combineTargetFor)\n      }\n    };\n  });\n  var getFallback = function getFallback(combineTargetFor) {\n    return combineTargetFor ? getMemoizedProps(origin, combineTargetFor, true) : null;\n  };\n  var getProps = function getProps(ownId, draggingId, impact, afterCritical) {\n    var visualDisplacement = impact.displaced.visible[ownId];\n    var isAfterCriticalInVirtualList = Boolean(afterCritical.inVirtualList && afterCritical.effected[ownId]);\n    var combine = tryGetCombine(impact);\n    var combineTargetFor = combine && combine.draggableId === ownId ? draggingId : null;\n    if (!visualDisplacement) {\n      if (!isAfterCriticalInVirtualList) {\n        return getFallback(combineTargetFor);\n      }\n      if (impact.displaced.invisible[ownId]) {\n        return null;\n      }\n      var change = negate(afterCritical.displacedBy.point);\n      var _offset = memoizedOffset(change.x, change.y);\n      return getMemoizedProps(_offset, combineTargetFor, true);\n    }\n    if (isAfterCriticalInVirtualList) {\n      return getFallback(combineTargetFor);\n    }\n    var displaceBy = impact.displacedBy.point;\n    var offset = memoizedOffset(displaceBy.x, displaceBy.y);\n    return getMemoizedProps(offset, combineTargetFor, visualDisplacement.shouldAnimate);\n  };\n  var selector = function selector(state, ownProps) {\n    if (state.isDragging) {\n      if (state.critical.draggable.id === ownProps.draggableId) {\n        return null;\n      }\n      return getProps(ownProps.draggableId, state.critical.draggable.id, state.impact, state.afterCritical);\n    }\n    if (state.phase === 'DROP_ANIMATING') {\n      var completed = state.completed;\n      if (completed.result.draggableId === ownProps.draggableId) {\n        return null;\n      }\n      return getProps(ownProps.draggableId, completed.result.draggableId, completed.impact, completed.afterCritical);\n    }\n    return null;\n  };\n  return selector;\n}\nvar makeMapStateToProps = function makeMapStateToProps() {\n  var draggingSelector = getDraggableSelector();\n  var secondarySelector = getSecondarySelector();\n  var selector = function selector(state, ownProps) {\n    return draggingSelector(state, ownProps) || secondarySelector(state, ownProps) || atRest;\n  };\n  return selector;\n};\nvar mapDispatchToProps = {\n  dropAnimationFinished: dropAnimationFinished\n};\nvar ConnectedDraggable = connect(makeMapStateToProps, mapDispatchToProps, null, {\n  context: StoreContext,\n  pure: true,\n  areStatePropsEqual: isStrictEqual\n})(Draggable);\nfunction PrivateDraggable(props) {\n  var droppableContext = useRequiredContext(DroppableContext);\n  var isUsingCloneFor = droppableContext.isUsingCloneFor;\n  if (isUsingCloneFor === props.draggableId && !props.isClone) {\n    return null;\n  }\n  return React.createElement(ConnectedDraggable, props);\n}\nfunction PublicDraggable(props) {\n  var isEnabled = typeof props.isDragDisabled === 'boolean' ? !props.isDragDisabled : true;\n  var canDragInteractiveElements = Boolean(props.disableInteractiveElementBlocking);\n  var shouldRespectForcePress = Boolean(props.shouldRespectForcePress);\n  return React.createElement(PrivateDraggable, _extends({}, props, {\n    isClone: false,\n    isEnabled: isEnabled,\n    canDragInteractiveElements: canDragInteractiveElements,\n    shouldRespectForcePress: shouldRespectForcePress\n  }));\n}\nfunction Droppable(props) {\n  var appContext = useContext(AppContext);\n  !appContext ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find app context') : invariant(false) : void 0;\n  var contextId = appContext.contextId,\n    isMovementAllowed = appContext.isMovementAllowed;\n  var droppableRef = useRef(null);\n  var placeholderRef = useRef(null);\n  var children = props.children,\n    droppableId = props.droppableId,\n    type = props.type,\n    mode = props.mode,\n    direction = props.direction,\n    ignoreContainerClipping = props.ignoreContainerClipping,\n    isDropDisabled = props.isDropDisabled,\n    isCombineEnabled = props.isCombineEnabled,\n    snapshot = props.snapshot,\n    useClone = props.useClone,\n    updateViewportMaxScroll = props.updateViewportMaxScroll,\n    getContainerForClone = props.getContainerForClone;\n  var getDroppableRef = useCallback(function () {\n    return droppableRef.current;\n  }, []);\n  var setDroppableRef = useCallback(function (value) {\n    droppableRef.current = value;\n  }, []);\n  var getPlaceholderRef = useCallback(function () {\n    return placeholderRef.current;\n  }, []);\n  var setPlaceholderRef = useCallback(function (value) {\n    placeholderRef.current = value;\n  }, []);\n  useValidation({\n    props: props,\n    getDroppableRef: getDroppableRef,\n    getPlaceholderRef: getPlaceholderRef\n  });\n  var onPlaceholderTransitionEnd = useCallback(function () {\n    if (isMovementAllowed()) {\n      updateViewportMaxScroll({\n        maxScroll: getMaxWindowScroll()\n      });\n    }\n  }, [isMovementAllowed, updateViewportMaxScroll]);\n  useDroppablePublisher({\n    droppableId: droppableId,\n    type: type,\n    mode: mode,\n    direction: direction,\n    isDropDisabled: isDropDisabled,\n    isCombineEnabled: isCombineEnabled,\n    ignoreContainerClipping: ignoreContainerClipping,\n    getDroppableRef: getDroppableRef\n  });\n  var placeholder = React.createElement(AnimateInOut, {\n    on: props.placeholder,\n    shouldAnimate: props.shouldAnimatePlaceholder\n  }, function (_ref) {\n    var onClose = _ref.onClose,\n      data = _ref.data,\n      animate = _ref.animate;\n    return React.createElement(Placeholder$1, {\n      placeholder: data,\n      onClose: onClose,\n      innerRef: setPlaceholderRef,\n      animate: animate,\n      contextId: contextId,\n      onTransitionEnd: onPlaceholderTransitionEnd\n    });\n  });\n  var provided = useMemo(function () {\n    return {\n      innerRef: setDroppableRef,\n      placeholder: placeholder,\n      droppableProps: {\n        'data-rbd-droppable-id': droppableId,\n        'data-rbd-droppable-context-id': contextId\n      }\n    };\n  }, [contextId, droppableId, placeholder, setDroppableRef]);\n  var isUsingCloneFor = useClone ? useClone.dragging.draggableId : null;\n  var droppableContext = useMemo(function () {\n    return {\n      droppableId: droppableId,\n      type: type,\n      isUsingCloneFor: isUsingCloneFor\n    };\n  }, [droppableId, isUsingCloneFor, type]);\n  function getClone() {\n    if (!useClone) {\n      return null;\n    }\n    var dragging = useClone.dragging,\n      render = useClone.render;\n    var node = React.createElement(PrivateDraggable, {\n      draggableId: dragging.draggableId,\n      index: dragging.source.index,\n      isClone: true,\n      isEnabled: true,\n      shouldRespectForcePress: false,\n      canDragInteractiveElements: true\n    }, function (draggableProvided, draggableSnapshot) {\n      return render(draggableProvided, draggableSnapshot, dragging);\n    });\n    return ReactDOM.createPortal(node, getContainerForClone());\n  }\n  return React.createElement(DroppableContext.Provider, {\n    value: droppableContext\n  }, children(provided, snapshot), getClone());\n}\nvar isMatchingType = function isMatchingType(type, critical) {\n  return type === critical.droppable.type;\n};\nvar getDraggable = function getDraggable(critical, dimensions) {\n  return dimensions.draggables[critical.draggable.id];\n};\nvar makeMapStateToProps$1 = function makeMapStateToProps() {\n  var idleWithAnimation = {\n    placeholder: null,\n    shouldAnimatePlaceholder: true,\n    snapshot: {\n      isDraggingOver: false,\n      draggingOverWith: null,\n      draggingFromThisWith: null,\n      isUsingPlaceholder: false\n    },\n    useClone: null\n  };\n  var idleWithoutAnimation = _extends({}, idleWithAnimation, {\n    shouldAnimatePlaceholder: false\n  });\n  var getDraggableRubric = memoizeOne(function (descriptor) {\n    return {\n      draggableId: descriptor.id,\n      type: descriptor.type,\n      source: {\n        index: descriptor.index,\n        droppableId: descriptor.droppableId\n      }\n    };\n  });\n  var getMapProps = memoizeOne(function (id, isEnabled, isDraggingOverForConsumer, isDraggingOverForImpact, dragging, renderClone) {\n    var draggableId = dragging.descriptor.id;\n    var isHome = dragging.descriptor.droppableId === id;\n    if (isHome) {\n      var useClone = renderClone ? {\n        render: renderClone,\n        dragging: getDraggableRubric(dragging.descriptor)\n      } : null;\n      var _snapshot = {\n        isDraggingOver: isDraggingOverForConsumer,\n        draggingOverWith: isDraggingOverForConsumer ? draggableId : null,\n        draggingFromThisWith: draggableId,\n        isUsingPlaceholder: true\n      };\n      return {\n        placeholder: dragging.placeholder,\n        shouldAnimatePlaceholder: false,\n        snapshot: _snapshot,\n        useClone: useClone\n      };\n    }\n    if (!isEnabled) {\n      return idleWithoutAnimation;\n    }\n    if (!isDraggingOverForImpact) {\n      return idleWithAnimation;\n    }\n    var snapshot = {\n      isDraggingOver: isDraggingOverForConsumer,\n      draggingOverWith: draggableId,\n      draggingFromThisWith: null,\n      isUsingPlaceholder: true\n    };\n    return {\n      placeholder: dragging.placeholder,\n      shouldAnimatePlaceholder: true,\n      snapshot: snapshot,\n      useClone: null\n    };\n  });\n  var selector = function selector(state, ownProps) {\n    var id = ownProps.droppableId;\n    var type = ownProps.type;\n    var isEnabled = !ownProps.isDropDisabled;\n    var renderClone = ownProps.renderClone;\n    if (state.isDragging) {\n      var critical = state.critical;\n      if (!isMatchingType(type, critical)) {\n        return idleWithoutAnimation;\n      }\n      var dragging = getDraggable(critical, state.dimensions);\n      var isDraggingOver = whatIsDraggedOver(state.impact) === id;\n      return getMapProps(id, isEnabled, isDraggingOver, isDraggingOver, dragging, renderClone);\n    }\n    if (state.phase === 'DROP_ANIMATING') {\n      var completed = state.completed;\n      if (!isMatchingType(type, completed.critical)) {\n        return idleWithoutAnimation;\n      }\n      var _dragging = getDraggable(completed.critical, state.dimensions);\n      return getMapProps(id, isEnabled, whatIsDraggedOverFromResult(completed.result) === id, whatIsDraggedOver(completed.impact) === id, _dragging, renderClone);\n    }\n    if (state.phase === 'IDLE' && state.completed && !state.shouldFlush) {\n      var _completed = state.completed;\n      if (!isMatchingType(type, _completed.critical)) {\n        return idleWithoutAnimation;\n      }\n      var wasOver = whatIsDraggedOver(_completed.impact) === id;\n      var wasCombining = Boolean(_completed.impact.at && _completed.impact.at.type === 'COMBINE');\n      var isHome = _completed.critical.droppable.id === id;\n      if (wasOver) {\n        return wasCombining ? idleWithAnimation : idleWithoutAnimation;\n      }\n      if (isHome) {\n        return idleWithAnimation;\n      }\n      return idleWithoutAnimation;\n    }\n    return idleWithoutAnimation;\n  };\n  return selector;\n};\nvar mapDispatchToProps$1 = {\n  updateViewportMaxScroll: updateViewportMaxScroll\n};\nfunction getBody() {\n  !document.body ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'document.body is not ready') : invariant(false) : void 0;\n  return document.body;\n}\nvar defaultProps = {\n  mode: 'standard',\n  type: 'DEFAULT',\n  direction: 'vertical',\n  isDropDisabled: false,\n  isCombineEnabled: false,\n  ignoreContainerClipping: false,\n  renderClone: null,\n  getContainerForClone: getBody\n};\nvar ConnectedDroppable = connect(makeMapStateToProps$1, mapDispatchToProps$1, null, {\n  context: StoreContext,\n  pure: true,\n  areStatePropsEqual: isStrictEqual\n})(Droppable);\nConnectedDroppable.defaultProps = defaultProps;\nexport { DragDropContext, PublicDraggable as Draggable, ConnectedDroppable as Droppable, resetServerContext, useKeyboardSensor, useMouseSensor, useTouchSensor };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}