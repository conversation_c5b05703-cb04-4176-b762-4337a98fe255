{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/DepartmentTBody.tsx\",\n  _s = $RefreshSig$();\nimport { IconButton, Stack, TableBody, TableCell, TableRow, Tooltip } from '@mui/material';\nimport HighlightOffIcon from '@mui/icons-material/HighlightOff';\nimport EditTwoToneIcon from '@mui/icons-material/EditTwoTone';\nimport { FormattedMessage } from 'react-intl';\nimport { deleteDepartment, getSearchDepartment } from 'store/slice/departmentSlice';\nimport { closeConfirm, openConfirm } from 'store/slice/confirmSlice';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { openSnackbar } from 'store/slice/snackbarSlice';\nimport { PERMISSIONS } from 'constants/Permission';\nimport { DATE_FORMAT } from 'constants/Common';\nimport { useAppDispatch } from 'app/hooks';\nimport { dateFormat } from 'utils/date';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DepartmentTBody = props => {\n  _s();\n  const {\n    conditions,\n    data,\n    handleOpen\n  } = props;\n  const {\n    departmentPermission\n  } = PERMISSIONS.admin;\n  const dispatch = useAppDispatch();\n  const handleDelete = department => {\n    dispatch(openConfirm({\n      open: true,\n      title: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n        id: \"warning\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 24\n      }, this),\n      content: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n        id: \"delete-record\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 26\n      }, this),\n      width: '400px',\n      handleConfirm: async () => {\n        const resultAction = await dispatch(deleteDepartment(department.id));\n        if (deleteDepartment.fulfilled.match(resultAction)) {\n          var _resultAction$payload;\n          if ((_resultAction$payload = resultAction.payload) !== null && _resultAction$payload !== void 0 && _resultAction$payload.status) {\n            dispatch(openSnackbar({\n              open: true,\n              message: resultAction.payload.result.content,\n              variant: 'alert',\n              alert: {\n                color: 'success'\n              }\n            }));\n            await dispatch(getSearchDepartment(conditions));\n          } else {\n            var _resultAction$payload2;\n            dispatch(openSnackbar({\n              open: true,\n              message: ((_resultAction$payload2 = resultAction.payload.result) === null || _resultAction$payload2 === void 0 ? void 0 : _resultAction$payload2.content) || 'Error',\n              variant: 'alert',\n              alert: {\n                color: 'error'\n              }\n            }));\n          }\n        }\n        dispatch(closeConfirm());\n      }\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(TableBody, {\n    children: data === null || data === void 0 ? void 0 : data.map((value, key) => /*#__PURE__*/_jsxDEV(TableRow, {\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"center\",\n        sx: {\n          width: '5%'\n        },\n        children: conditions.size * (conditions.page - 1) + key + 1\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          width: '20%'\n        },\n        children: value.deptId\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          width: '35%'\n        },\n        children: value.deptName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          width: '15%'\n        },\n        children: dateFormat(value.lastUpdate, DATE_FORMAT.DDMMYYYY)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          width: '15%'\n        },\n        children: value.userUpdate\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 21\n      }, this), checkAllowedPermission(departmentPermission.edit) && /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          width: '10%'\n        },\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          justifyContent: \"center\",\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n            placement: \"top\",\n            title: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: 'edit'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 65\n            }, this),\n            onClick: () => handleOpen(value),\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              \"aria-label\": \"edit\",\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(EditTwoToneIcon, {\n                sx: {\n                  fontSize: '1.1rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            placement: \"top\",\n            title: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: \"delete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 65\n            }, this),\n            onClick: () => handleDelete(value),\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              \"aria-label\": \"delete\",\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(HighlightOffIcon, {\n                sx: {\n                  fontSize: '1.1rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 25\n      }, this)]\n    }, key, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 17\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 9\n  }, this);\n};\n_s(DepartmentTBody, \"BJQ1DUn/XFEl2NsQ3DWyHAXfnkY=\", false, function () {\n  return [useAppDispatch];\n});\n_c = DepartmentTBody;\nexport default DepartmentTBody;\nvar _c;\n$RefreshReg$(_c, \"DepartmentTBody\");", "map": {"version": 3, "names": ["IconButton", "<PERSON><PERSON>", "TableBody", "TableCell", "TableRow", "<PERSON><PERSON><PERSON>", "HighlightOffIcon", "EditTwoToneIcon", "FormattedMessage", "deleteDepartment", "getSearchDepartment", "closeConfirm", "openConfirm", "checkAllowedPermission", "openSnackbar", "PERMISSIONS", "DATE_FORMAT", "useAppDispatch", "dateFormat", "jsxDEV", "_jsxDEV", "DepartmentTBody", "props", "_s", "conditions", "data", "handleOpen", "departmentPermission", "admin", "dispatch", "handleDelete", "department", "open", "title", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "width", "handleConfirm", "resultAction", "fulfilled", "match", "_resultAction$payload", "payload", "status", "message", "result", "variant", "alert", "color", "_resultAction$payload2", "children", "map", "value", "key", "align", "sx", "size", "page", "deptId", "deptName", "lastUpdate", "DDMMYYYY", "userUpdate", "edit", "direction", "justifyContent", "alignItems", "placement", "onClick", "fontSize", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/DepartmentTBody.tsx"], "sourcesContent": ["import { Icon<PERSON><PERSON><PERSON>, Stack, TableBody, TableCell, TableRow, Tooltip } from '@mui/material';\nimport HighlightOffIcon from '@mui/icons-material/HighlightOff';\nimport EditTwoToneIcon from '@mui/icons-material/EditTwoTone';\nimport { FormattedMessage } from 'react-intl';\n\nimport { deleteDepartment, getSearchDepartment } from 'store/slice/departmentSlice';\nimport { IDepartmentFilterConfig } from 'pages/administration/Config';\nimport { closeConfirm, openConfirm } from 'store/slice/confirmSlice';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { openSnackbar } from 'store/slice/snackbarSlice';\nimport { PERMISSIONS } from 'constants/Permission';\nimport { IDepartment } from 'types/department';\nimport { DATE_FORMAT } from 'constants/Common';\nimport { useAppDispatch } from 'app/hooks';\nimport { dateFormat } from 'utils/date';\n\ninterface IDepartmentTBodyProps {\n    data: IDepartment[];\n    conditions: IDepartmentFilterConfig;\n    handleOpen: (department: IDepartment) => void;\n}\n\nconst DepartmentTBody = (props: IDepartmentTBodyProps) => {\n    const { conditions, data, handleOpen } = props;\n    const { departmentPermission } = PERMISSIONS.admin;\n\n    const dispatch = useAppDispatch();\n\n    const handleDelete = (department: IDepartment) => {\n        dispatch(\n            openConfirm({\n                open: true,\n                title: <FormattedMessage id=\"warning\" />,\n                content: <FormattedMessage id=\"delete-record\" />,\n                width: '400px',\n                handleConfirm: async () => {\n                    const resultAction = await dispatch(deleteDepartment(department.id));\n                    if (deleteDepartment.fulfilled.match(resultAction)) {\n                        if (resultAction.payload?.status) {\n                            dispatch(\n                                openSnackbar({\n                                    open: true,\n                                    message: resultAction.payload.result.content,\n                                    variant: 'alert',\n                                    alert: { color: 'success' }\n                                })\n                            );\n                            await dispatch(getSearchDepartment(conditions));\n                        } else {\n                            dispatch(\n                                openSnackbar({\n                                    open: true,\n                                    message: resultAction.payload.result?.content || 'Error',\n                                    variant: 'alert',\n                                    alert: { color: 'error' }\n                                })\n                            );\n                        }\n                    }\n                    dispatch(closeConfirm());\n                }\n            })\n        );\n    };\n\n    return (\n        <TableBody>\n            {data?.map((value, key) => (\n                <TableRow key={key}>\n                    <TableCell align=\"center\" sx={{ width: '5%' }}>\n                        {conditions.size * (conditions.page - 1) + key + 1}\n                    </TableCell>\n                    <TableCell sx={{ width: '20%' }}>{value.deptId}</TableCell>\n                    <TableCell sx={{ width: '35%' }}>{value.deptName}</TableCell>\n                    <TableCell sx={{ width: '15%' }}>{dateFormat(value.lastUpdate, DATE_FORMAT.DDMMYYYY)}</TableCell>\n                    <TableCell sx={{ width: '15%' }}>{value.userUpdate}</TableCell>\n                    {checkAllowedPermission(departmentPermission.edit) && (\n                        <TableCell sx={{ width: '10%' }}>\n                            <Stack direction=\"row\" justifyContent=\"center\" alignItems=\"center\">\n                                <Tooltip placement=\"top\" title={<FormattedMessage id={'edit'} />} onClick={() => handleOpen(value)}>\n                                    <IconButton aria-label=\"edit\" size=\"small\">\n                                        <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />\n                                    </IconButton>\n                                </Tooltip>\n                                <Tooltip placement=\"top\" title={<FormattedMessage id=\"delete\" />} onClick={() => handleDelete(value)}>\n                                    <IconButton aria-label=\"delete\" size=\"small\">\n                                        <HighlightOffIcon sx={{ fontSize: '1.1rem' }} />\n                                    </IconButton>\n                                </Tooltip>\n                            </Stack>\n                        </TableCell>\n                    )}\n                </TableRow>\n            ))}\n        </TableBody>\n    );\n};\n\nexport default DepartmentTBody;\n"], "mappings": ";;AAAA,SAASA,UAAU,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,eAAe;AAC1F,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,SAASC,gBAAgB,QAAQ,YAAY;AAE7C,SAASC,gBAAgB,EAAEC,mBAAmB,QAAQ,6BAA6B;AAEnF,SAASC,YAAY,EAAEC,WAAW,QAAQ,0BAA0B;AACpE,SAASC,sBAAsB,QAAQ,qBAAqB;AAC5D,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,WAAW,QAAQ,sBAAsB;AAElD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,cAAc,QAAQ,WAAW;AAC1C,SAASC,UAAU,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQxC,MAAMC,eAAe,GAAIC,KAA4B,IAAK;EAAAC,EAAA;EACtD,MAAM;IAAEC,UAAU;IAAEC,IAAI;IAAEC;EAAW,CAAC,GAAGJ,KAAK;EAC9C,MAAM;IAAEK;EAAqB,CAAC,GAAGZ,WAAW,CAACa,KAAK;EAElD,MAAMC,QAAQ,GAAGZ,cAAc,CAAC,CAAC;EAEjC,MAAMa,YAAY,GAAIC,UAAuB,IAAK;IAC9CF,QAAQ,CACJjB,WAAW,CAAC;MACRoB,IAAI,EAAE,IAAI;MACVC,KAAK,eAAEb,OAAA,CAACZ,gBAAgB;QAAC0B,EAAE,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACxCC,OAAO,eAAEnB,OAAA,CAACZ,gBAAgB;QAAC0B,EAAE,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAChDE,KAAK,EAAE,OAAO;MACdC,aAAa,EAAE,MAAAA,CAAA,KAAY;QACvB,MAAMC,YAAY,GAAG,MAAMb,QAAQ,CAACpB,gBAAgB,CAACsB,UAAU,CAACG,EAAE,CAAC,CAAC;QACpE,IAAIzB,gBAAgB,CAACkC,SAAS,CAACC,KAAK,CAACF,YAAY,CAAC,EAAE;UAAA,IAAAG,qBAAA;UAChD,KAAAA,qBAAA,GAAIH,YAAY,CAACI,OAAO,cAAAD,qBAAA,eAApBA,qBAAA,CAAsBE,MAAM,EAAE;YAC9BlB,QAAQ,CACJf,YAAY,CAAC;cACTkB,IAAI,EAAE,IAAI;cACVgB,OAAO,EAAEN,YAAY,CAACI,OAAO,CAACG,MAAM,CAACV,OAAO;cAC5CW,OAAO,EAAE,OAAO;cAChBC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAC9B,CAAC,CACL,CAAC;YACD,MAAMvB,QAAQ,CAACnB,mBAAmB,CAACc,UAAU,CAAC,CAAC;UACnD,CAAC,MAAM;YAAA,IAAA6B,sBAAA;YACHxB,QAAQ,CACJf,YAAY,CAAC;cACTkB,IAAI,EAAE,IAAI;cACVgB,OAAO,EAAE,EAAAK,sBAAA,GAAAX,YAAY,CAACI,OAAO,CAACG,MAAM,cAAAI,sBAAA,uBAA3BA,sBAAA,CAA6Bd,OAAO,KAAI,OAAO;cACxDW,OAAO,EAAE,OAAO;cAChBC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAQ;YAC5B,CAAC,CACL,CAAC;UACL;QACJ;QACAvB,QAAQ,CAAClB,YAAY,CAAC,CAAC,CAAC;MAC5B;IACJ,CAAC,CACL,CAAC;EACL,CAAC;EAED,oBACIS,OAAA,CAAClB,SAAS;IAAAoD,QAAA,EACL7B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,GAAG,CAAC,CAACC,KAAK,EAAEC,GAAG,kBAClBrC,OAAA,CAAChB,QAAQ;MAAAkD,QAAA,gBACLlC,OAAA,CAACjB,SAAS;QAACuD,KAAK,EAAC,QAAQ;QAACC,EAAE,EAAE;UAAEnB,KAAK,EAAE;QAAK,CAAE;QAAAc,QAAA,EACzC9B,UAAU,CAACoC,IAAI,IAAIpC,UAAU,CAACqC,IAAI,GAAG,CAAC,CAAC,GAAGJ,GAAG,GAAG;MAAC;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACZlB,OAAA,CAACjB,SAAS;QAACwD,EAAE,EAAE;UAAEnB,KAAK,EAAE;QAAM,CAAE;QAAAc,QAAA,EAAEE,KAAK,CAACM;MAAM;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC3DlB,OAAA,CAACjB,SAAS;QAACwD,EAAE,EAAE;UAAEnB,KAAK,EAAE;QAAM,CAAE;QAAAc,QAAA,EAAEE,KAAK,CAACO;MAAQ;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC7DlB,OAAA,CAACjB,SAAS;QAACwD,EAAE,EAAE;UAAEnB,KAAK,EAAE;QAAM,CAAE;QAAAc,QAAA,EAAEpC,UAAU,CAACsC,KAAK,CAACQ,UAAU,EAAEhD,WAAW,CAACiD,QAAQ;MAAC;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACjGlB,OAAA,CAACjB,SAAS;QAACwD,EAAE,EAAE;UAAEnB,KAAK,EAAE;QAAM,CAAE;QAAAc,QAAA,EAAEE,KAAK,CAACU;MAAU;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAC9DzB,sBAAsB,CAACc,oBAAoB,CAACwC,IAAI,CAAC,iBAC9C/C,OAAA,CAACjB,SAAS;QAACwD,EAAE,EAAE;UAAEnB,KAAK,EAAE;QAAM,CAAE;QAAAc,QAAA,eAC5BlC,OAAA,CAACnB,KAAK;UAACmE,SAAS,EAAC,KAAK;UAACC,cAAc,EAAC,QAAQ;UAACC,UAAU,EAAC,QAAQ;UAAAhB,QAAA,gBAC9DlC,OAAA,CAACf,OAAO;YAACkE,SAAS,EAAC,KAAK;YAACtC,KAAK,eAAEb,OAAA,CAACZ,gBAAgB;cAAC0B,EAAE,EAAE;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACkC,OAAO,EAAEA,CAAA,KAAM9C,UAAU,CAAC8B,KAAK,CAAE;YAAAF,QAAA,eAC/FlC,OAAA,CAACpB,UAAU;cAAC,cAAW,MAAM;cAAC4D,IAAI,EAAC,OAAO;cAAAN,QAAA,eACtClC,OAAA,CAACb,eAAe;gBAACoD,EAAE,EAAE;kBAAEc,QAAQ,EAAE;gBAAS;cAAE;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACVlB,OAAA,CAACf,OAAO;YAACkE,SAAS,EAAC,KAAK;YAACtC,KAAK,eAAEb,OAAA,CAACZ,gBAAgB;cAAC0B,EAAE,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACkC,OAAO,EAAEA,CAAA,KAAM1C,YAAY,CAAC0B,KAAK,CAAE;YAAAF,QAAA,eACjGlC,OAAA,CAACpB,UAAU;cAAC,cAAW,QAAQ;cAAC4D,IAAI,EAAC,OAAO;cAAAN,QAAA,eACxClC,OAAA,CAACd,gBAAgB;gBAACqD,EAAE,EAAE;kBAAEc,QAAQ,EAAE;gBAAS;cAAE;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACd;IAAA,GAvBUmB,GAAG;MAAAtB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAwBR,CACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEpB,CAAC;AAACf,EAAA,CA1EIF,eAAe;EAAA,QAIAJ,cAAc;AAAA;AAAAyD,EAAA,GAJ7BrD,eAAe;AA4ErB,eAAeA,eAAe;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}