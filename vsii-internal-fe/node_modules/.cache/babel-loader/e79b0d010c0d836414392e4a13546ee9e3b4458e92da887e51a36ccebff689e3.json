{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport IconButton from '@mui/material/IconButton';\nimport Typography from '@mui/material/Typography';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_useEnhancedEffect as useEnhancedEffect, unstable_composeClasses as composeClasses } from '@mui/utils';\nimport { ClockPointer } from './ClockPointer';\nimport { useUtils } from '../internals/hooks/useUtils';\nimport { WrapperVariantContext } from '../internals/components/wrappers/WrapperVariantContext';\nimport { getHours, getMinutes } from './shared';\nimport { getClockUtilityClass } from './clockClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    clock: ['clock'],\n    wrapper: ['wrapper'],\n    squareMask: ['squareMask'],\n    pin: ['pin'],\n    amButton: ['amButton'],\n    pmButton: ['pmButton']\n  };\n  return composeClasses(slots, getClockUtilityClass, classes);\n};\nconst ClockRoot = styled('div', {\n  name: 'MuiClock',\n  slot: 'Root',\n  overridesResolver: (_, styles) => styles.root\n})(({\n  theme\n}) => ({\n  display: 'flex',\n  justifyContent: 'center',\n  alignItems: 'center',\n  margin: theme.spacing(2)\n}));\nconst ClockClock = styled('div', {\n  name: 'MuiClock',\n  slot: 'Clock',\n  overridesResolver: (_, styles) => styles.clock\n})({\n  backgroundColor: 'rgba(0,0,0,.07)',\n  borderRadius: '50%',\n  height: 220,\n  width: 220,\n  flexShrink: 0,\n  position: 'relative',\n  pointerEvents: 'none'\n});\nconst ClockWrapper = styled('div', {\n  name: 'MuiClock',\n  slot: 'Wrapper',\n  overridesResolver: (_, styles) => styles.wrapper\n})({\n  '&:focus': {\n    outline: 'none'\n  }\n});\nconst ClockSquareMask = styled('div', {\n  name: 'MuiClock',\n  slot: 'SquareMask',\n  overridesResolver: (_, styles) => styles.squareMask\n})(({\n  ownerState\n}) => _extends({\n  width: '100%',\n  height: '100%',\n  position: 'absolute',\n  pointerEvents: 'auto',\n  outline: 0,\n  // Disable scroll capabilities.\n  touchAction: 'none',\n  userSelect: 'none'\n}, ownerState.disabled ? {} : {\n  '@media (pointer: fine)': {\n    cursor: 'pointer',\n    borderRadius: '50%'\n  },\n  '&:active': {\n    cursor: 'move'\n  }\n}));\nconst ClockPin = styled('div', {\n  name: 'MuiClock',\n  slot: 'Pin',\n  overridesResolver: (_, styles) => styles.pin\n})(({\n  theme\n}) => ({\n  width: 6,\n  height: 6,\n  borderRadius: '50%',\n  backgroundColor: theme.palette.primary.main,\n  position: 'absolute',\n  top: '50%',\n  left: '50%',\n  transform: 'translate(-50%, -50%)'\n}));\nconst ClockAmButton = styled(IconButton, {\n  name: 'MuiClock',\n  slot: 'AmButton',\n  overridesResolver: (_, styles) => styles.amButton\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  zIndex: 1,\n  position: 'absolute',\n  bottom: ownerState.ampmInClock ? 64 : 8,\n  left: 8\n}, ownerState.meridiemMode === 'am' && {\n  backgroundColor: theme.palette.primary.main,\n  color: theme.palette.primary.contrastText,\n  '&:hover': {\n    backgroundColor: theme.palette.primary.light\n  }\n}));\nconst ClockPmButton = styled(IconButton, {\n  name: 'MuiClock',\n  slot: 'PmButton',\n  overridesResolver: (_, styles) => styles.pmButton\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  zIndex: 1,\n  position: 'absolute',\n  bottom: ownerState.ampmInClock ? 64 : 8,\n  right: 8\n}, ownerState.meridiemMode === 'pm' && {\n  backgroundColor: theme.palette.primary.main,\n  color: theme.palette.primary.contrastText,\n  '&:hover': {\n    backgroundColor: theme.palette.primary.light\n  }\n}));\n/**\n * @ignore - internal component.\n */\n\nexport function Clock(inProps) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiClock'\n  });\n  const {\n    ampm,\n    ampmInClock,\n    autoFocus,\n    children,\n    date,\n    getClockLabelText,\n    handleMeridiemChange,\n    isTimeDisabled,\n    meridiemMode,\n    minutesStep = 1,\n    onChange,\n    selectedId,\n    type,\n    value,\n    disabled,\n    readOnly,\n    className\n  } = props;\n  const ownerState = props;\n  const utils = useUtils();\n  const wrapperVariant = React.useContext(WrapperVariantContext);\n  const isMoving = React.useRef(false);\n  const classes = useUtilityClasses(ownerState);\n  const isSelectedTimeDisabled = isTimeDisabled(value, type);\n  const isPointerInner = !ampm && type === 'hours' && (value < 1 || value > 12);\n  const handleValueChange = (newValue, isFinish) => {\n    if (disabled || readOnly) {\n      return;\n    }\n    if (isTimeDisabled(newValue, type)) {\n      return;\n    }\n    onChange(newValue, isFinish);\n  };\n  const setTime = (event, isFinish) => {\n    let {\n      offsetX,\n      offsetY\n    } = event;\n    if (offsetX === undefined) {\n      const rect = event.target.getBoundingClientRect();\n      offsetX = event.changedTouches[0].clientX - rect.left;\n      offsetY = event.changedTouches[0].clientY - rect.top;\n    }\n    const newSelectedValue = type === 'seconds' || type === 'minutes' ? getMinutes(offsetX, offsetY, minutesStep) : getHours(offsetX, offsetY, Boolean(ampm));\n    handleValueChange(newSelectedValue, isFinish);\n  };\n  const handleTouchMove = event => {\n    isMoving.current = true;\n    setTime(event, 'shallow');\n  };\n  const handleTouchEnd = event => {\n    if (isMoving.current) {\n      setTime(event, 'finish');\n      isMoving.current = false;\n    }\n  };\n  const handleMouseMove = event => {\n    // event.buttons & PRIMARY_MOUSE_BUTTON\n    if (event.buttons > 0) {\n      setTime(event.nativeEvent, 'shallow');\n    }\n  };\n  const handleMouseUp = event => {\n    if (isMoving.current) {\n      isMoving.current = false;\n    }\n    setTime(event.nativeEvent, 'finish');\n  };\n  const hasSelected = React.useMemo(() => {\n    if (type === 'hours') {\n      return true;\n    }\n    return value % 5 === 0;\n  }, [type, value]);\n  const keyboardControlStep = type === 'minutes' ? minutesStep : 1;\n  const listboxRef = React.useRef(null); // Since this is rendered when a Popper is opened we can't use passive effects.\n  // Focusing in passive effects in Popper causes scroll jump.\n\n  useEnhancedEffect(() => {\n    if (autoFocus) {\n      // The ref not being resolved would be a bug in MUI.\n      listboxRef.current.focus();\n    }\n  }, [autoFocus]);\n  const handleKeyDown = event => {\n    // TODO: Why this early exit?\n    if (isMoving.current) {\n      return;\n    }\n    switch (event.key) {\n      case 'Home':\n        // annulate both hours and minutes\n        handleValueChange(0, 'partial');\n        event.preventDefault();\n        break;\n      case 'End':\n        handleValueChange(type === 'minutes' ? 59 : 23, 'partial');\n        event.preventDefault();\n        break;\n      case 'ArrowUp':\n        handleValueChange(value + keyboardControlStep, 'partial');\n        event.preventDefault();\n        break;\n      case 'ArrowDown':\n        handleValueChange(value - keyboardControlStep, 'partial');\n        event.preventDefault();\n        break;\n      default: // do nothing\n    }\n  };\n  return /*#__PURE__*/_jsxs(ClockRoot, {\n    className: clsx(className, classes.root),\n    children: [/*#__PURE__*/_jsxs(ClockClock, {\n      className: classes.clock,\n      children: [/*#__PURE__*/_jsx(ClockSquareMask, {\n        onTouchMove: handleTouchMove,\n        onTouchEnd: handleTouchEnd,\n        onMouseUp: handleMouseUp,\n        onMouseMove: handleMouseMove,\n        ownerState: {\n          disabled\n        },\n        className: classes.squareMask\n      }), !isSelectedTimeDisabled && /*#__PURE__*/_jsxs(React.Fragment, {\n        children: [/*#__PURE__*/_jsx(ClockPin, {\n          className: classes.pin\n        }), date && /*#__PURE__*/_jsx(ClockPointer, {\n          type: type,\n          value: value,\n          isInner: isPointerInner,\n          hasSelected: hasSelected\n        })]\n      }), /*#__PURE__*/_jsx(ClockWrapper, {\n        \"aria-activedescendant\": selectedId,\n        \"aria-label\": getClockLabelText(type, date, utils),\n        ref: listboxRef,\n        role: \"listbox\",\n        onKeyDown: handleKeyDown,\n        tabIndex: 0,\n        className: classes.wrapper,\n        children: children\n      })]\n    }), ampm && (wrapperVariant === 'desktop' || ampmInClock) && /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [/*#__PURE__*/_jsx(ClockAmButton, {\n        onClick: readOnly ? undefined : () => handleMeridiemChange('am'),\n        disabled: disabled || meridiemMode === null,\n        ownerState: ownerState,\n        className: classes.amButton,\n        children: /*#__PURE__*/_jsx(Typography, {\n          variant: \"caption\",\n          children: \"AM\"\n        })\n      }), /*#__PURE__*/_jsx(ClockPmButton, {\n        disabled: disabled || meridiemMode === null,\n        onClick: readOnly ? undefined : () => handleMeridiemChange('pm'),\n        ownerState: ownerState,\n        className: classes.pmButton,\n        children: /*#__PURE__*/_jsx(Typography, {\n          variant: \"caption\",\n          children: \"PM\"\n        })\n      })]\n    })]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}