{"ast": null, "code": "// Third party\nimport{FormattedMessage}from'react-intl';// yup\nimport{yupResolver}from'@hookform/resolvers/yup';// material-ui\nimport{Button,DialogActions,Grid,Stack}from'@mui/material';import{LoadingButton}from'@mui/lab';// project import\nimport{addOrEditBiddingReportFormDefault,addOrEditBiddingReportSchema}from'pages/sales/Config';import{FormProvider,Input,NumericFormatCustom}from'components/extended/Form';import{StatusBiddingReport}from'containers/search';import Modal from'components/extended/Modal';import{gridSpacing}from'store/constant';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AddOrEditBiddingReport=props=>{const{open,handleClose,data,isEdit,loading,hanldeAdd,hanldeEdit}=props;const{salesReport}=TEXT_CONFIG_SCREEN;const handleSubmit=values=>{const idHexString=data.idHexString;if(isEdit){hanldeEdit(values,idHexString);}else{hanldeAdd(values);}};return/*#__PURE__*/_jsx(Modal,{isOpen:open,title:isEdit?salesReport.monitorBiddingPackages+'report-edit-bidding-packages':salesReport.monitorBiddingPackages+'report-add-bidding-packages',onClose:handleClose,keepMounted:false,children:/*#__PURE__*/_jsx(FormProvider,{form:{defaultValues:addOrEditBiddingReportFormDefault,resolver:yupResolver(addOrEditBiddingReportSchema)},onSubmit:handleSubmit,formReset:data,children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:gridSpacing,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsx(Input,{required:true,name:\"type\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monitorBiddingPackages+'report-type'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsx(Input,{disabled:true,name:\"numberKHLCNT\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monitorBiddingPackages+'report-KHLCNT-number'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsx(Input,{required:true,name:\"biddingPackagesName\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monitorBiddingPackages+'report-bidding-package-name'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},disabled:true,name:\"estimatedCost\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monitorBiddingPackages+'report-budget'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsx(Input,{disabled:true,name:\"datePosting\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monitorBiddingPackages+'report-date-posting'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsx(Input,{disabled:true,name:\"timeBiddingClosing\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monitorBiddingPackages+'report-time-bidding-closing'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"bidPrice\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monitorBiddingPackages+'report-bid-price'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsx(Input,{disabled:true,name:\"formBiddingParticipation\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monitorBiddingPackages+'report-form-bidding-participation'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsx(Input,{name:\"numberTBMT\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monitorBiddingPackages+'report-TBMT-number'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsx(Input,{disabled:true,name:\"group\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monitorBiddingPackages+'report-group'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsx(Input,{required:true,name:\"company\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monitorBiddingPackages+'report-company'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsx(Input,{disabled:true,name:\"address\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monitorBiddingPackages+'report-address'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsx(Input,{disabled:true,name:\"keyword\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monitorBiddingPackages+'report-keyword'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsx(StatusBiddingReport,{select:true,label:salesReport.monitorBiddingPackages+'report-status'})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{multiline:true,rows:4},name:\"comment\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monitorBiddingPackages+'report-comment'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(DialogActions,{children:/*#__PURE__*/_jsxs(Stack,{direction:\"row\",spacing:1,justifyContent:\"flex-end\",children:[/*#__PURE__*/_jsx(Button,{color:\"error\",onClick:handleClose,children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monitorBiddingPackages+'report-cancel'})}),/*#__PURE__*/_jsx(LoadingButton,{loading:loading,variant:\"contained\",type:\"submit\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monitorBiddingPackages+'report-submit'})})]})})})]})})});};export default AddOrEditBiddingReport;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}