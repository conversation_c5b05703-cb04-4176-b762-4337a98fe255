{"ast": null, "code": "import{FormattedMessage}from'react-intl';// material-ui\nimport{TableCell,TableHead,TableRow}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const RatioJoiningProjectThead=()=>{return/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"no\"})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"id\"})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"member\"})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"table-title\"})}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"department\"})}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"joining-project\"})})]})});};export default RatioJoiningProjectThead;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}