{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/TitleCode.tsx\",\n  _s = $RefreshSig$();\nimport { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { searchFormConfig } from './Config';\nimport { Autocomplete } from 'components/extended/Form';\nimport sendRequest from 'services/ApiService';\nimport Api from 'constants/Api';\nimport { useEffect, useState } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TitleCode = props => {\n  _s();\n  const {\n    name,\n    label,\n    isDefaultAll,\n    handleChange,\n    handleClose,\n    disabled,\n    isFindAll,\n    required\n  } = props;\n  const [titleCodes, setTitleCodes] = useState([]);\n  const [listUser, setListUser] = useState([]);\n  const handleChangeTitleCode = option => {\n    const userInfoSelected = option ? listUser.filter(data => data.titleCode === option.value) : null;\n    handleChange && handleChange(userInfoSelected ? userInfoSelected[0] : null);\n  };\n  async function getTitleCodes() {\n    const response = await sendRequest(Api.skills_manage.getTitleCodes, {\n      findAll: isFindAll ? 'All' : ''\n    });\n    if (!response) return;\n    const {\n      status,\n      result\n    } = response;\n    if (status) {\n      result.content.forEach(item => {\n        let titleCodeOption = {\n          value: item.titleCode,\n          label: `[${item.titleCode}] - ${item.titleName}`\n        };\n        setTitleCodes(item => [...item, titleCodeOption]);\n        setListUser(listUser => [...listUser, item]);\n      });\n    }\n  }\n  useEffect(() => {\n    getTitleCodes();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Autocomplete, {\n      required: required,\n      options: titleCodes,\n      name: name,\n      label: label,\n      handleChange: handleChangeTitleCode,\n      handleClose: handleClose,\n      disabled: disabled,\n      isDefaultAll: isDefaultAll\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s(TitleCode, \"sflXagLRBQ5k3EV8t/JdMBSxlIg=\");\n_c = TitleCode;\nTitleCode.defaultProps = {\n  name: searchFormConfig.titleCodeSkillReport.name,\n  label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n    id: 'title-code'\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 12\n  }, this),\n  isShowAll: true,\n  disabled: false,\n  isFindAll: false\n};\nexport default TitleCode;\nvar _c;\n$RefreshReg$(_c, \"TitleCode\");", "map": {"version": 3, "names": ["FormattedMessage", "searchFormConfig", "Autocomplete", "sendRequest", "Api", "useEffect", "useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TitleCode", "props", "_s", "name", "label", "isDefaultAll", "handleChange", "handleClose", "disabled", "isFindAll", "required", "titleCodes", "setTitleCodes", "listUser", "setListUser", "handleChangeTitleCode", "option", "userInfoSelected", "filter", "data", "titleCode", "value", "getTitleCodes", "response", "skills_manage", "findAll", "status", "result", "content", "for<PERSON>ach", "item", "titleCodeOption", "<PERSON><PERSON><PERSON>", "children", "options", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "defaultProps", "titleCodeSkillReport", "id", "isShowAll", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/TitleCode.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { searchFormConfig } from './Config';\nimport { Autocomplete } from 'components/extended/Form';\nimport { IOption, IResponseList, ITitleCode, ITitleResponse } from 'types';\nimport sendRequest from 'services/ApiService';\nimport Api from 'constants/Api';\nimport { ReactNode, useEffect, useState } from 'react';\n\ninterface ITitleCodeProps {\n    name: string;\n    label: string | ReactNode;\n    isDefaultAll?: boolean;\n    disabled: boolean;\n    handleChange?: (data: any) => void;\n    handleClose?: () => void;\n    isFindAll: boolean;\n    required?: boolean;\n    isIdHexString?: boolean;\n    isUserName?: boolean;\n}\n\nconst TitleCode = (props: ITitleCodeProps) => {\n    const { name, label, isDefaultAll, handleChange, handleClose, disabled, isFindAll, required } = props;\n    const [titleCodes, setTitleCodes] = useState<IOption[]>([]);\n    const [listUser, setListUser] = useState<ITitleCode[]>([]);\n    const handleChangeTitleCode = (option: IOption) => {\n        const userInfoSelected = option ? listUser.filter((data) => data.titleCode === option.value) : null;\n        handleChange && handleChange(userInfoSelected ? userInfoSelected[0] : null);\n    };\n    async function getTitleCodes() {\n        const response: IResponseList<ITitleResponse> = await sendRequest(Api.skills_manage.getTitleCodes, {\n            findAll: isFindAll ? 'All' : ''\n        });\n        if (!response) return;\n        const { status, result } = response;\n        if (status) {\n            result.content.forEach((item: ITitleCode) => {\n                let titleCodeOption = {\n                    value: item.titleCode,\n                    label: `[${item.titleCode}] - ${item.titleName}`\n                };\n                setTitleCodes((item) => [...item, titleCodeOption]);\n                setListUser((listUser) => [...listUser, item]);\n            });\n        }\n    }\n\n    useEffect(() => {\n        getTitleCodes();\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n\n    return (\n        <>\n            <Autocomplete\n                required={required}\n                options={titleCodes}\n                name={name}\n                label={label}\n                handleChange={handleChangeTitleCode}\n                handleClose={handleClose}\n                disabled={disabled}\n                isDefaultAll={isDefaultAll}\n            />\n        </>\n    );\n};\n\nTitleCode.defaultProps = {\n    name: searchFormConfig.titleCodeSkillReport.name,\n    label: <FormattedMessage id={'title-code'} />,\n    isShowAll: true,\n    disabled: false,\n    isFindAll: false\n};\n\nexport default TitleCode;\n"], "mappings": ";;AAAA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,gBAAgB,QAAQ,UAAU;AAC3C,SAASC,YAAY,QAAQ,0BAA0B;AAEvD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,GAAG,MAAM,eAAe;AAC/B,SAAoBC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAevD,MAAMC,SAAS,GAAIC,KAAsB,IAAK;EAAAC,EAAA;EAC1C,MAAM;IAAEC,IAAI;IAAEC,KAAK;IAAEC,YAAY;IAAEC,YAAY;IAAEC,WAAW;IAAEC,QAAQ;IAAEC,SAAS;IAAEC;EAAS,CAAC,GAAGT,KAAK;EACrG,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAY,EAAE,CAAC;EAC3D,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAe,EAAE,CAAC;EAC1D,MAAMoB,qBAAqB,GAAIC,MAAe,IAAK;IAC/C,MAAMC,gBAAgB,GAAGD,MAAM,GAAGH,QAAQ,CAACK,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACC,SAAS,KAAKJ,MAAM,CAACK,KAAK,CAAC,GAAG,IAAI;IACnGf,YAAY,IAAIA,YAAY,CAACW,gBAAgB,GAAGA,gBAAgB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;EAC/E,CAAC;EACD,eAAeK,aAAaA,CAAA,EAAG;IAC3B,MAAMC,QAAuC,GAAG,MAAM/B,WAAW,CAACC,GAAG,CAAC+B,aAAa,CAACF,aAAa,EAAE;MAC/FG,OAAO,EAAEhB,SAAS,GAAG,KAAK,GAAG;IACjC,CAAC,CAAC;IACF,IAAI,CAACc,QAAQ,EAAE;IACf,MAAM;MAAEG,MAAM;MAAEC;IAAO,CAAC,GAAGJ,QAAQ;IACnC,IAAIG,MAAM,EAAE;MACRC,MAAM,CAACC,OAAO,CAACC,OAAO,CAAEC,IAAgB,IAAK;QACzC,IAAIC,eAAe,GAAG;UAClBV,KAAK,EAAES,IAAI,CAACV,SAAS;UACrBhB,KAAK,EAAE,IAAI0B,IAAI,CAACV,SAAS,OAAOU,IAAI,CAACE,SAAS;QAClD,CAAC;QACDpB,aAAa,CAAEkB,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEC,eAAe,CAAC,CAAC;QACnDjB,WAAW,CAAED,QAAQ,IAAK,CAAC,GAAGA,QAAQ,EAAEiB,IAAI,CAAC,CAAC;MAClD,CAAC,CAAC;IACN;EACJ;EAEApC,SAAS,CAAC,MAAM;IACZ4B,aAAa,CAAC,CAAC;IACf;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,oBACIzB,OAAA,CAAAE,SAAA;IAAAkC,QAAA,eACIpC,OAAA,CAACN,YAAY;MACTmB,QAAQ,EAAEA,QAAS;MACnBwB,OAAO,EAAEvB,UAAW;MACpBR,IAAI,EAAEA,IAAK;MACXC,KAAK,EAAEA,KAAM;MACbE,YAAY,EAAES,qBAAsB;MACpCR,WAAW,EAAEA,WAAY;MACzBC,QAAQ,EAAEA,QAAS;MACnBH,YAAY,EAAEA;IAAa;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B;EAAC,gBACJ,CAAC;AAEX,CAAC;AAACpC,EAAA,CA7CIF,SAAS;AAAAuC,EAAA,GAATvC,SAAS;AA+CfA,SAAS,CAACwC,YAAY,GAAG;EACrBrC,IAAI,EAAEb,gBAAgB,CAACmD,oBAAoB,CAACtC,IAAI;EAChDC,KAAK,eAAEP,OAAA,CAACR,gBAAgB;IAACqD,EAAE,EAAE;EAAa;IAAAP,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC7CK,SAAS,EAAE,IAAI;EACfnC,QAAQ,EAAE,KAAK;EACfC,SAAS,EAAE;AACf,CAAC;AAED,eAAeT,SAAS;AAAC,IAAAuC,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}