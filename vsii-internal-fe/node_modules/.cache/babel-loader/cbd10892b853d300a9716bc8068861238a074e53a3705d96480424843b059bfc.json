{"ast": null, "code": "import{useEffect,useState}from'react';import{FormProvider,Input}from'components/extended/Form';import DialogActions from'@mui/material/DialogActions';import{yupResolver}from'@hookform/resolvers/yup';import{FormattedMessage}from'react-intl';import AddIcon from'@mui/icons-material/Add';import{Button,Box}from'@mui/material';import{useForm}from'react-hook-form';import{LoadingButton}from'@mui/lab';import{defaultFieldsUploadORM,ormReportSchema}from'pages/administration/Config';import{getProjectAllForOption}from'store/slice/monthlyEffortSlice';import{Department,Months,Years}from'containers/search';import{useAppDispatch,useAppSelector}from'app/hooks';import{openSnackbar}from'store/slice/snackbarSlice';import{authSelector}from'store/slice/authSlice';import{convertMonthFromToDate}from'utils/date';import sendRequest from'services/ApiService';import Modal from'components/extended/Modal';import Api from'constants/Api';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const UploadORMReport=_ref=>{let{months,handleChangeYear,updateAfterUpload}=_ref;const[month,setMonth]=useState({fromDate:'',toDate:''});const[open,setOpen]=useState(false);const{userInfo}=useAppSelector(authSelector);const[loading,setLoading]=useState(false);const[fileUpload,setFileUpload]=useState(\"'\");const dispatch=useAppDispatch();const methods=useForm({defaultValues:defaultFieldsUploadORM,resolver:yupResolver(ormReportSchema)});const{ORMReport}=TEXT_CONFIG_SCREEN.generalReport;const handleClickOpen=()=>{setOpen(true);};const handleClose=()=>{setOpen(false);};const handleFile=e=>{const files=e.target.files;if(files!==null&&files!==void 0&&files.length){setFileUpload(files[0]);}};const handleMonthChange=value=>{const getMonth=months.filter(month=>{return month.value===value;});return setMonth(convertMonthFromToDate(getMonth[0].label));};const handleUpload=async values=>{setLoading(true);const formData=new FormData();const uploadORMReprtData={...values,file:fileUpload,uploadUser:userInfo===null||userInfo===void 0?void 0:userInfo.userName};Object.keys(uploadORMReprtData).forEach(key=>{const value=uploadORMReprtData[key];formData.append(key,value);});const response=await sendRequest(Api.monthly_efford.uploadOrmReport,formData);dispatch(openSnackbar({open:true,message:response.status?response.result.content:response.result.content.message,variant:'alert',alert:response.status?{color:'success'}:{color:'error'}}));if(response.status){setOpen(false);updateAfterUpload();methods.reset();}setLoading(false);};useEffect(()=>{dispatch(getProjectAllForOption({type:'month',value:month}));},[dispatch,month]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Button,{size:\"medium\",startIcon:/*#__PURE__*/_jsx(AddIcon,{}),onClick:handleClickOpen,children:/*#__PURE__*/_jsx(FormattedMessage,{id:ORMReport+'add-new'}),variant:\"contained\"}),/*#__PURE__*/_jsx(Modal,{title:ORMReport+'add-new-report',isOpen:open,onClose:handleClose,\"aria-labelledby\":\"alert-dialog-title\",\"aria-describedby\":\"alert-dialog-description\",children:/*#__PURE__*/_jsxs(FormProvider,{formReturn:methods,onSubmit:handleUpload,children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",flexDirection:\"column\",gap:1,children:[/*#__PURE__*/_jsx(Input,{label:/*#__PURE__*/_jsx(FormattedMessage,{id:ORMReport+'report-name'}),name:\"reportName\",required:true}),/*#__PURE__*/_jsx(Department,{isShowAll:false,required:true,name:\"department\",label:ORMReport+'department'}),/*#__PURE__*/_jsx(Years,{handleChangeYear:handleChangeYear,required:true,label:ORMReport+'year'}),/*#__PURE__*/_jsx(Months,{onChange:handleMonthChange,months:months,required:true,label:ORMReport+'month'}),/*#__PURE__*/_jsx(Input,{name:\"file\",type:\"file\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:ORMReport+'attachment'}),onChangeInput:handleFile,required:true})]}),/*#__PURE__*/_jsxs(DialogActions,{sx:{display:'flex'},children:[/*#__PURE__*/_jsx(LoadingButton,{color:\"error\",disabled:loading,size:\"large\",onClick:handleClose,children:/*#__PURE__*/_jsx(FormattedMessage,{id:ORMReport+'cancel'})}),/*#__PURE__*/_jsx(LoadingButton,{loading:loading,disabled:loading,variant:\"contained\",size:\"large\",type:\"submit\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:ORMReport+'submit'})})]})]})})]});};export default UploadORMReport;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}