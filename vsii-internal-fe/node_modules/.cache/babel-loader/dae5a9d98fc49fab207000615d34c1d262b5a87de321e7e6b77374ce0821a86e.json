{"ast": null, "code": "import{useState,useEffect}from'react';import{IconButton,Popover,Grid,Typography,Box,MenuItem,Select,FormControl,TextField,Divider}from'@mui/material';import InfoOutlinedIcon from'@mui/icons-material/InfoOutlined';import sendRequest from'services/ApiService';import Api from'constants/Api';import{FormattedMessage,useIntl}from'react-intl';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const commonInputSx={maxWidth:100,'& .MuiInputBase-root':{height:26,borderRadius:'4px',padding:0},'& .MuiOutlinedInput-input':{padding:'0 8px',height:'100%',lineHeight:'26px',borderRadius:'4px'},'& .MuiOutlinedInput-notchedOutline':{border:'none'}};const LeaveDaysInfo=_ref=>{var _selectedLeave$value;let{memberId,leaveData:propLeaveData}=_ref;const[anchorEl,setAnchorEl]=useState(null);const[leaveData,setLeaveData]=useState(propLeaveData||null);const[leaveOptions,setLeaveOptions]=useState([]);const[selectedLeave,setSelectedLeave]=useState(null);const[isHovering,setIsHovering]=useState(false);const intl=useIntl();useEffect(()=>{if(propLeaveData){setLeaveData(propLeaveData);const options=[{label:'leave-days-wedding',value:'totalLeaveDaysWedding',days:propLeaveData.totalLeaveDaysWedding},{label:'leave-days-funeral',value:'totalLeaveDaysFuneral',days:propLeaveData.totalLeaveDaysFuneral},{label:'leave-days-maternity',value:'totalLeaveDaysMaternity',days:propLeaveData.totalLeaveDaysMaternity}];setLeaveOptions(options);setSelectedLeave(options[0]);return;}if(memberId){fetchLeaveDays();}},[memberId,propLeaveData]);useEffect(()=>{if(propLeaveData){setLeaveData(propLeaveData);const options=[{label:'leave-days-wedding',value:'totalLeaveDaysWedding',days:propLeaveData.totalLeaveDaysWedding},{label:'leave-days-funeral',value:'totalLeaveDaysFuneral',days:propLeaveData.totalLeaveDaysFuneral},{label:'leave-days-maternity',value:'totalLeaveDaysMaternity',days:propLeaveData.totalLeaveDaysMaternity}];setLeaveOptions(options);if(!selectedLeave){setSelectedLeave(options[0]);}}},[propLeaveData]);useEffect(()=>{if(!isHovering){const timeout=setTimeout(()=>{setAnchorEl(null);},400);return()=>clearTimeout(timeout);}},[isHovering]);const fetchLeaveDays=async()=>{try{const response=await sendRequest(Api.leave_day.getLeaveDaysInfo(memberId));if(response!==null&&response!==void 0&&response.status){const info=response.result.content;const options=[{label:'leave-days-wedding',value:'totalLeaveDaysWedding',days:info.totalLeaveDaysWedding},{label:'leave-days-funeral',value:'totalLeaveDaysFuneral',days:info.totalLeaveDaysFuneral},{label:'leave-days-maternity',value:'totalLeaveDaysMaternity',days:info.totalLeaveDaysMaternity}];setLeaveData(info);setLeaveOptions(options);setSelectedLeave(options[0]);}else{var _response$result,_response$result$mess,_response$result$mess2;console.error('Error fetching leave days info:',(response===null||response===void 0?void 0:(_response$result=response.result)===null||_response$result===void 0?void 0:(_response$result$mess=_response$result.messages)===null||_response$result$mess===void 0?void 0:(_response$result$mess2=_response$result$mess[0])===null||_response$result$mess2===void 0?void 0:_response$result$mess2.message)||'Unknown error');}}catch(error){console.error('Error fetching leave days info:',error);}};const handlePopoverOpen=event=>{setAnchorEl(event.currentTarget);setIsHovering(true);};const handlePopoverClose=()=>{setIsHovering(false);};const open=Boolean(anchorEl);const handleLeaveChange=event=>{const selected=leaveOptions.find(option=>option.value===event.target.value);if(selected){setSelectedLeave(selected);}};const capitalizeFirstLetter=text=>{return text.charAt(0).toUpperCase()+text.slice(1);};if(!leaveData)return null;return/*#__PURE__*/_jsxs(Box,{sx:{lineHeight:0},children:[/*#__PURE__*/_jsx(Box,{onMouseEnter:handlePopoverOpen,onMouseLeave:handlePopoverClose,children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",sx:{padding:'2px'},children:/*#__PURE__*/_jsx(InfoOutlinedIcon,{fontSize:\"small\",sx:{fontSize:'1rem'}})})}),/*#__PURE__*/_jsxs(Popover,{id:\"mouse-over-popover\",open:open,anchorEl:anchorEl,onClose:()=>setAnchorEl(null),anchorOrigin:{vertical:'bottom',horizontal:'left'},transformOrigin:{vertical:'top',horizontal:'left'},PaperProps:{onMouseEnter:()=>setIsHovering(true),onMouseLeave:handlePopoverClose,sx:{pointerEvents:'auto',mt:1,p:2,borderRadius:2,boxShadow:3,maxWidth:350}},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",mb:2,fontSize:\"1rem\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"leave-days-title\"})}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,mt:1,children:[[['leave-days-remaining',leaveData.totalLeaveDaysMonthly],['leave-days-seniority',leaveData.totalLeaveDaysSeniority],['leave-days-compensation',leaveData.totalLeaveDaysCompansation],['leave-days-annual',leaveData.totalLeaveDays],['leave-days-sick',leaveData.totalLeaveDaysSick],['leave-days-unpaid',leaveData.totalLeaveDaysUnpaid]].map((_ref2,i)=>{let[label,value]=_ref2;return/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",children:[/*#__PURE__*/_jsx(Typography,{fontSize:14,children:/*#__PURE__*/_jsx(FormattedMessage,{id:label})}),/*#__PURE__*/_jsx(TextField,{value:value,size:\"small\",sx:commonInputSx,InputProps:{readOnly:true},variant:\"outlined\",disabled:true})]})},i);}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",children:[/*#__PURE__*/_jsx(Typography,{fontSize:14,children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"leave-days-other\"})}),/*#__PURE__*/_jsx(FormControl,{size:\"small\",sx:{...commonInputSx,'& .MuiOutlinedInput-notchedOutline':{border:'1px solid #ccc',borderRadius:'4px'}},children:/*#__PURE__*/_jsx(Select,{value:(_selectedLeave$value=selectedLeave===null||selectedLeave===void 0?void 0:selectedLeave.value)!==null&&_selectedLeave$value!==void 0?_selectedLeave$value:'',onChange:handleLeaveChange,children:leaveOptions.map(option=>/*#__PURE__*/_jsx(MenuItem,{value:option.value,children:/*#__PURE__*/_jsx(FormattedMessage,{id:option.label})},option.value))})})]})}),selectedLeave&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",children:[/*#__PURE__*/_jsx(Typography,{fontSize:14,children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"number-days\",defaultMessage:\"S\\u1ED1 ng\\xE0y {leaveType}\",values:{leaveType:intl.locale==='en'?capitalizeFirstLetter(intl.formatMessage({id:selectedLeave.label})):intl.formatMessage({id:selectedLeave.label}).toLowerCase()}})}),/*#__PURE__*/_jsx(TextField,{value:selectedLeave.days,size:\"small\",sx:commonInputSx,InputProps:{readOnly:true},variant:\"outlined\",disabled:true})]})})]}),/*#__PURE__*/_jsx(Divider,{sx:{my:2}}),/*#__PURE__*/_jsx(Box,{p:1})]})]});};export default LeaveDaysInfo;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}