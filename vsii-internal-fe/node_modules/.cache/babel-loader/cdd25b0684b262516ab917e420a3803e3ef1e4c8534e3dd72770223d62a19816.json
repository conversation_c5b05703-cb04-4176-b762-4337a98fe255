{"ast": null, "code": "import { noopReturn, addUniqueItem } from '@motionone/utils';\nimport { getAnimationData } from '../data.es.js';\n\n/**\n * A list of all transformable axes. We'll use this list to generated a version\n * of each axes for each transform.\n */\nconst axes = [\"\", \"X\", \"Y\", \"Z\"];\n/**\n * An ordered array of each transformable value. By default, transform values\n * will be sorted to this order.\n */\nconst order = [\"translate\", \"scale\", \"rotate\", \"skew\"];\nconst transformAlias = {\n  x: \"translateX\",\n  y: \"translateY\",\n  z: \"translateZ\"\n};\nconst rotation = {\n  syntax: \"<angle>\",\n  initialValue: \"0deg\",\n  toDefaultUnit: v => v + \"deg\"\n};\nconst baseTransformProperties = {\n  translate: {\n    syntax: \"<length-percentage>\",\n    initialValue: \"0px\",\n    toDefaultUnit: v => v + \"px\"\n  },\n  rotate: rotation,\n  scale: {\n    syntax: \"<number>\",\n    initialValue: 1,\n    toDefaultUnit: noopReturn\n  },\n  skew: rotation\n};\nconst transformDefinitions = new Map();\nconst asTransformCssVar = name => `--motion-${name}`;\n/**\n * Generate a list of every possible transform key\n */\nconst transforms = [\"x\", \"y\", \"z\"];\norder.forEach(name => {\n  axes.forEach(axis => {\n    transforms.push(name + axis);\n    transformDefinitions.set(asTransformCssVar(name + axis), baseTransformProperties[name]);\n  });\n});\n/**\n * A function to use with Array.sort to sort transform keys by their default order.\n */\nconst compareTransformOrder = (a, b) => transforms.indexOf(a) - transforms.indexOf(b);\n/**\n * Provide a quick way to check if a string is the name of a transform\n */\nconst transformLookup = new Set(transforms);\nconst isTransform = name => transformLookup.has(name);\nconst addTransformToElement = (element, name) => {\n  // Map x to translateX etc\n  if (transformAlias[name]) name = transformAlias[name];\n  const {\n    transforms\n  } = getAnimationData(element);\n  addUniqueItem(transforms, name);\n  /**\n   * TODO: An optimisation here could be to cache the transform in element data\n   * and only update if this has changed.\n   */\n  element.style.transform = buildTransformTemplate(transforms);\n};\nconst buildTransformTemplate = transforms => transforms.sort(compareTransformOrder).reduce(transformListToString, \"\").trim();\nconst transformListToString = (template, name) => `${template} ${name}(var(${asTransformCssVar(name)}))`;\nexport { addTransformToElement, asTransformCssVar, axes, buildTransformTemplate, compareTransformOrder, isTransform, transformAlias, transformDefinitions };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}