{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"checked\", \"component\", \"components\", \"componentsProps\", \"defaultChecked\", \"disabled\", \"onBlur\", \"onChange\", \"onFocus\", \"onFocusVisible\", \"readOnly\", \"required\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '../composeClasses';\nimport useSwitch from './useSwitch';\nimport { getSwitchUnstyledUtilityClass } from './switchUnstyledClasses';\nimport { useSlotProps } from '../utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    checked,\n    disabled,\n    focusVisible,\n    readOnly\n  } = ownerState;\n  const slots = {\n    root: ['root', checked && 'checked', disabled && 'disabled', focusVisible && 'focusVisible', readOnly && 'readOnly'],\n    thumb: ['thumb'],\n    input: ['input'],\n    track: ['track']\n  };\n  return composeClasses(slots, getSwitchUnstyledUtilityClass, {});\n};\n/**\n * The foundation for building custom-styled switches.\n *\n * Demos:\n *\n * - [Unstyled switch](https://mui.com/base/react-switch/)\n *\n * API:\n *\n * - [SwitchUnstyled API](https://mui.com/base/api/switch-unstyled/)\n */\n\nconst SwitchUnstyled = /*#__PURE__*/React.forwardRef(function SwitchUnstyled(props, ref) {\n  var _ref, _components$Thumb, _components$Input, _components$Track;\n  const {\n      checked: checkedProp,\n      component,\n      components = {},\n      componentsProps = {},\n      defaultChecked,\n      disabled: disabledProp,\n      onBlur,\n      onChange,\n      onFocus,\n      onFocusVisible,\n      readOnly: readOnlyProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const useSwitchProps = {\n    checked: checkedProp,\n    defaultChecked,\n    disabled: disabledProp,\n    onBlur,\n    onChange,\n    onFocus,\n    onFocusVisible,\n    readOnly: readOnlyProp\n  };\n  const {\n    getInputProps,\n    checked,\n    disabled,\n    focusVisible,\n    readOnly\n  } = useSwitch(useSwitchProps);\n  const ownerState = _extends({}, props, {\n    checked,\n    disabled,\n    focusVisible,\n    readOnly\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = (_ref = component != null ? component : components.Root) != null ? _ref : 'span';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref\n    },\n    ownerState,\n    className: classes.root\n  });\n  const Thumb = (_components$Thumb = components.Thumb) != null ? _components$Thumb : 'span';\n  const thumbProps = useSlotProps({\n    elementType: Thumb,\n    externalSlotProps: componentsProps.thumb,\n    ownerState,\n    className: classes.thumb\n  });\n  const Input = (_components$Input = components.Input) != null ? _components$Input : 'input';\n  const inputProps = useSlotProps({\n    elementType: Input,\n    getSlotProps: getInputProps,\n    externalSlotProps: componentsProps.input,\n    ownerState,\n    className: classes.input\n  });\n  const Track = components.Track === null ? () => null : (_components$Track = components.Track) != null ? _components$Track : 'span';\n  const trackProps = useSlotProps({\n    elementType: Track,\n    externalSlotProps: componentsProps.track,\n    ownerState,\n    className: classes.track\n  });\n  return /*#__PURE__*/_jsxs(Root, _extends({}, rootProps, {\n    children: [/*#__PURE__*/_jsx(Track, _extends({}, trackProps)), /*#__PURE__*/_jsx(Thumb, _extends({}, thumbProps)), /*#__PURE__*/_jsx(Input, _extends({}, inputProps))]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? SwitchUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the Switch.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes\n  /* @typescript-to-proptypes-ignore */.shape({\n    Input: PropTypes.elementType,\n    Root: PropTypes.elementType,\n    Thumb: PropTypes.elementType,\n    Track: PropTypes.oneOfType([PropTypes.elementType, PropTypes.oneOf([null])])\n  }),\n  /**\n   * The props used for each slot inside the Switch.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    thumb: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    track: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The default checked state. Use when the component is not controlled.\n   */\n  defaultChecked: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocusVisible: PropTypes.func,\n  /**\n   * If `true`, the component is read only.\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, the `input` element is required.\n   */\n  required: PropTypes.bool\n} : void 0;\nexport default SwitchUnstyled;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}