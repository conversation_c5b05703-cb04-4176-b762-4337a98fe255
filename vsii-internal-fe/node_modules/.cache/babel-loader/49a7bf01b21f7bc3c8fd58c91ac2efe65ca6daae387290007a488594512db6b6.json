{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/pages/sales/MonthlyProductionPerformance.tsx\",\n  _s = $RefreshSig$();\n/* eslint-disable react-hooks/exhaustive-deps */\nimport React, { useCallback, useEffect, useState } from 'react';\n\n// third party\nimport { useSearchParams } from 'react-router-dom';\n\n// material-ui\n\n// project imports\nimport { store } from 'app/store';\nimport MainCard from 'components/cards/MainCard';\nimport { Table } from 'components/extended/Table';\nimport Api from 'constants/Api';\nimport { CONTRACT_TYPE_SALE_REPORT, DEPARTMENTS, SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN, UNIT_SALE_REPORT } from 'constants/Common';\nimport { PERMISSIONS } from 'constants/Permission';\nimport { AddOrEditProductionPerformance, CommentPopover, EditHeadCount, MonthlyProductionPerformanceSearch, MonthlyProductionPerformanceTBody, MonthlyProductionPerformanceThead } from 'containers/sales';\nimport { FilterCollapse } from 'containers/search';\nimport sendRequest from 'services/ApiService';\nimport { openSnackbar } from 'store/slice/snackbarSlice';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { exportDocument, getDataProductivityByMonth, getSearchParam, isEmpty, transformObject } from 'utils/common';\nimport { authSelector } from 'store/slice/authSlice';\nimport { convertMonthFromToDate, getMonthsOfYear } from 'utils/date';\nimport { monthlyProductionPerformanceFilterConfig, monthlyProductionPerformanceInfoDefault, productionPerformanceAddOrEditFormDefault, productivityHeadCountEditFormDefault } from './Config';\nimport { useAppDispatch, useAppSelector } from 'app/hooks';\nimport useConfig from 'hooks/useConfig';\n\n// ==============================|| Monthly Production Performance ||============================== //\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MonthlyProductionPerformance = () => {\n  _s();\n  // URL Params\n  const [searchParams, setSearchParams] = useSearchParams();\n  const keyParams = [SEARCH_PARAM_KEY.year];\n  const keyParamsArray = [SEARCH_PARAM_KEY.month];\n  const params = getSearchParam(keyParams, searchParams, keyParamsArray);\n  transformObject(params);\n  const {\n    locale\n  } = useConfig();\n\n  // Hooks, State, Variable\n  const defaultConditions = {\n    ...monthlyProductionPerformanceFilterConfig,\n    ...params,\n    language: locale\n  };\n  const {\n    userInfo\n  } = useAppSelector(authSelector);\n  const [loading, setLoading] = useState(false);\n  const [addOrEditLoading, setAddOrEditLoading] = useState(false);\n  const [open, setOpen] = useState(false);\n  const [openHC, setOpenHC] = useState(false);\n  const [monthlyProductionPerformanceInfo, setMonthlyProductionPerformanceInfo] = useState(monthlyProductionPerformanceInfoDefault);\n  const [conditions, setConditions] = useState(defaultConditions);\n  const [formReset, setFormReset] = useState(defaultConditions);\n  const [months, setMonths] = useState([]);\n  const [year, setYear] = useState(defaultConditions.year);\n  const [isChangeYear, setIsChangeYear] = useState(false);\n  const [productivity, setProductivity] = useState(productionPerformanceAddOrEditFormDefault);\n  const [productivityHeadCount, setProductivityHeadCount] = useState(productivityHeadCountEditFormDefault);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [commentItem, setCommentItem] = useState(null);\n  const [isEditComment, setIsEditComment] = useState(false);\n  const {\n    monthlyProductionPerformancePermission\n  } = PERMISSIONS.sale;\n  const [loadingData, setLoadingData] = useState(false);\n  const [isEdited, setIsEdited] = useState(false);\n\n  // Functions\n  const getDataTable = async () => {\n    setLoading(true);\n    const response = await sendRequest(Api.sale_productivity.getAll, {\n      ...conditions\n    });\n    if (response) {\n      const {\n        status,\n        result\n      } = response;\n      if (status) {\n        setMonthlyProductionPerformanceInfo(result.content);\n      } else {\n        setDataEmpty();\n      }\n      setLoading(false);\n    } else {\n      setDataEmpty();\n    }\n  };\n  const getDetailMonths = async (idHexString, month, standardWorkingDayOfMonth) => {\n    setLoadingData(true);\n    const params = {\n      idHexString,\n      year: conditions.year\n    };\n    const response = await sendRequest(Api.sale_productivity.getDetail, params);\n    const {\n      status,\n      result\n    } = response;\n    if (response && status) {\n      var _data$project, _data$project2, _data$productivity$;\n      setLoadingData(false);\n      const data = result.content;\n      const getMonth = months.filter(month => month.value === data.productivity[0].month);\n      let foundObject = {\n        month: 1,\n        delivered: {\n          value: 0,\n          comment: ''\n        },\n        receivable: {\n          value: 0,\n          comment: ''\n        },\n        received: {\n          value: 0,\n          comment: ''\n        },\n        financial: {\n          value: 0,\n          comment: ''\n        },\n        hcInfo: [],\n        year: year\n      };\n      data.productivity.forEach((item, index) => {\n        // eslint-disable-next-line eqeqeq\n        if (item.month == month) {\n          foundObject = data.productivity[index];\n        }\n      });\n      const {\n        fromDate,\n        toDate\n      } = convertMonthFromToDate(getMonth[0].label);\n      const standardWorkingDay = await getStandardWorkingDay(fromDate, toDate);\n      const payload = {\n        idHexString: data.idHexString,\n        year: conditions.year,\n        month: month ? foundObject.month : data.productivity[0].month,\n        standardWorkingDay: month ? standardWorkingDayOfMonth : standardWorkingDay.toString() || '1',\n        projectId: {\n          value: (_data$project = data.project) === null || _data$project === void 0 ? void 0 : _data$project.projectId,\n          label: (_data$project2 = data.project) === null || _data$project2 === void 0 ? void 0 : _data$project2.projectName\n        },\n        departmentId: data.departmentId,\n        projectName: data.project.projectName,\n        contractSize: data.contractSize.value,\n        serviceType: data.serviceType,\n        contractType: data.contractType,\n        originalContractSize: data.originalContractSize,\n        unit: data.unit || UNIT_SALE_REPORT.MAN_MONTH,\n        contractAllocation: data.contractAllocation,\n        duration: data.duration,\n        paymentTerm: data.paymentTerm || 1,\n        currency: data.exchangeRate.label,\n        exchangeRate: data.exchangeRate.value,\n        delivered: month ? foundObject.delivered.value : data === null || data === void 0 ? void 0 : data.productivity[0].delivered.value,\n        receivable: month ? foundObject.receivable.value : data === null || data === void 0 ? void 0 : data.productivity[0].receivable.value,\n        received: month ? foundObject.received.value : data === null || data === void 0 ? void 0 : data.productivity[0].received.value,\n        financial: month ? foundObject.financial.value : data === null || data === void 0 ? void 0 : data.productivity[0].financial.value,\n        deliveredCurrency: month ? foundObject.delivered.value * data.exchangeRate.value : (data === null || data === void 0 ? void 0 : data.productivity[0].delivered.value) * data.exchangeRate.value,\n        receivableCurrency: month ? foundObject.receivable.value * data.exchangeRate.value : (data === null || data === void 0 ? void 0 : data.productivity[0].receivable.value) * data.exchangeRate.value,\n        receivedCurrency: month ? foundObject.received.value * data.exchangeRate.value : (data === null || data === void 0 ? void 0 : data.productivity[0].received.value) * data.exchangeRate.value,\n        financialCurrency: month ? foundObject.financial.value * data.exchangeRate.value : (data === null || data === void 0 ? void 0 : data.productivity[0].financial.value) * data.exchangeRate.value,\n        hcInfo: month ? foundObject.hcInfo : (data === null || data === void 0 ? void 0 : (_data$productivity$ = data.productivity[0]) === null || _data$productivity$ === void 0 ? void 0 : _data$productivity$.hcInfo) || [],\n        productivity: (data === null || data === void 0 ? void 0 : data.productivity) || [],\n        lastYearProductivity: data.lastYearProductivity\n      };\n      setProductivity(payload);\n    }\n  };\n  const getStandardWorkingDay = async (fromDate, toDate) => {\n    let standardWorkingDay = 0;\n    const params = {\n      fromDate,\n      toDate\n    };\n    const response = await sendRequest(Api.sale_productivity.getStandardWorkingDay, params);\n    const {\n      status,\n      result\n    } = response;\n    if (response && status) {\n      const {\n        value\n      } = result.content;\n      standardWorkingDay = value;\n    }\n    return standardWorkingDay;\n  };\n  const onChangeYearGetStandardWorkingDay = async (payload, fromDate, toDate, month, paymentTerm) => {\n    const standardWorkingDayValue = await getStandardWorkingDay(fromDate, toDate);\n    const productivityBase = productivity.productivity ? productivity.productivity : [];\n    const productivityLastYear = productivity.lastYearProductivity ? productivity.lastYearProductivity : [];\n    const exchangeRate = payload.exchangeRate || 1;\n    let payloadBase = {\n      ...payload,\n      month: month ? month : 1,\n      standardWorkingDay: String(standardWorkingDayValue),\n      paymentTerm: paymentTerm ? paymentTerm : payload.paymentTerm\n    };\n    let productivityArray = productivity.productivity && productivity.productivity.filter(el => {\n      return el.month === month;\n    });\n    const productivityDefaultValue = {\n      ...payloadBase,\n      delivered: 0,\n      receivable: 0,\n      received: 0,\n      financial: 0,\n      deliveredCurrency: 0,\n      receivableCurrency: 0,\n      receivedCurrency: 0,\n      financialCurrency: 0,\n      hcInfo: []\n    };\n    const createProductivityObject = item => {\n      var _item$delivered, _item$receivable, _item$received, _item$financial, _item$delivered2, _item$receivable2, _item$received2, _item$financial2;\n      return {\n        ...payloadBase,\n        delivered: ((_item$delivered = item.delivered) === null || _item$delivered === void 0 ? void 0 : _item$delivered.value) || 0,\n        receivable: ((_item$receivable = item.receivable) === null || _item$receivable === void 0 ? void 0 : _item$receivable.value) || 0,\n        received: ((_item$received = item.received) === null || _item$received === void 0 ? void 0 : _item$received.value) || 0,\n        financial: ((_item$financial = item.financial) === null || _item$financial === void 0 ? void 0 : _item$financial.value) || 0,\n        deliveredCurrency: ((_item$delivered2 = item.delivered) === null || _item$delivered2 === void 0 ? void 0 : _item$delivered2.value) * exchangeRate,\n        receivableCurrency: ((_item$receivable2 = item.receivable) === null || _item$receivable2 === void 0 ? void 0 : _item$receivable2.value) * exchangeRate,\n        receivedCurrency: ((_item$received2 = item.received) === null || _item$received2 === void 0 ? void 0 : _item$received2.value) * exchangeRate,\n        financialCurrency: ((_item$financial2 = item.financial) === null || _item$financial2 === void 0 ? void 0 : _item$financial2.value) * exchangeRate,\n        hcInfo: item.hcInfo || []\n      };\n    };\n\n    // TH productivityArray trả về mảng rỗng\n    if (payload.contractType === CONTRACT_TYPE_SALE_REPORT.TM && payload.departmentId === DEPARTMENTS.SCS) {\n      var _productivityArray$0$, _productivityArray$0$2, _productivityArray$0$3, _productivityArray$0$4;\n      if (Array.isArray(productivityArray) && (productivityArray.length === 0 || ((_productivityArray$0$ = productivityArray[0].delivered) === null || _productivityArray$0$ === void 0 ? void 0 : _productivityArray$0$.value) === 0 && ((_productivityArray$0$2 = productivityArray[0].receivable) === null || _productivityArray$0$2 === void 0 ? void 0 : _productivityArray$0$2.value) === 0 && ((_productivityArray$0$3 = productivityArray[0].received) === null || _productivityArray$0$3 === void 0 ? void 0 : _productivityArray$0$3.value) === 0 && ((_productivityArray$0$4 = productivityArray[0].financial) === null || _productivityArray$0$4 === void 0 ? void 0 : _productivityArray$0$4.value) === 0)) {\n        // Month > Payment Term\n        if (month && +month > +(paymentTerm ? paymentTerm : payload.paymentTerm)) {\n          var _arr1$0$delivered, _arr1$0$delivered2, _arr1$0$delivered3, _arr1$0$delivered4;\n          let arr1 = getDataProductivityByMonth(productivityBase, +month - +(paymentTerm ? paymentTerm : payload.paymentTerm));\n          setProductivity(arr1 && arr1.length > 0 ? {\n            ...payloadBase,\n            delivered: (_arr1$0$delivered = arr1[0].delivered) === null || _arr1$0$delivered === void 0 ? void 0 : _arr1$0$delivered.value,\n            receivable: (_arr1$0$delivered2 = arr1[0].delivered) === null || _arr1$0$delivered2 === void 0 ? void 0 : _arr1$0$delivered2.value,\n            received: (_arr1$0$delivered3 = arr1[0].delivered) === null || _arr1$0$delivered3 === void 0 ? void 0 : _arr1$0$delivered3.value,\n            financial: (_arr1$0$delivered4 = arr1[0].delivered) === null || _arr1$0$delivered4 === void 0 ? void 0 : _arr1$0$delivered4.value\n          } : productivityDefaultValue);\n        }\n\n        // Month = Payment Term\n        if (month && +month === +(paymentTerm ? paymentTerm : payload.paymentTerm)) {\n          var _arr2$0$delivered, _arr2$0$delivered2, _arr2$0$delivered3, _arr2$0$delivered4;\n          let arr2 = getDataProductivityByMonth(productivityLastYear, 12);\n          setProductivity(arr2 && arr2.length > 0 ? {\n            ...payloadBase,\n            delivered: (_arr2$0$delivered = arr2[0].delivered) === null || _arr2$0$delivered === void 0 ? void 0 : _arr2$0$delivered.value,\n            receivable: (_arr2$0$delivered2 = arr2[0].delivered) === null || _arr2$0$delivered2 === void 0 ? void 0 : _arr2$0$delivered2.value,\n            received: (_arr2$0$delivered3 = arr2[0].delivered) === null || _arr2$0$delivered3 === void 0 ? void 0 : _arr2$0$delivered3.value,\n            financial: (_arr2$0$delivered4 = arr2[0].delivered) === null || _arr2$0$delivered4 === void 0 ? void 0 : _arr2$0$delivered4.value,\n            hcInfo: []\n          } : productivityDefaultValue);\n        }\n\n        // Month < Payment Term\n        if (month && +month < +(paymentTerm ? paymentTerm : payload.paymentTerm)) {\n          var _arr3$0$delivered, _arr3$0$delivered2, _arr3$0$delivered3, _arr3$0$delivered4;\n          const count = +(paymentTerm ? paymentTerm : payload.paymentTerm) - +month;\n          const countFinal = 12 - count;\n          let arr3 = getDataProductivityByMonth(productivityLastYear, countFinal);\n          setProductivity(arr3 && arr3.length > 0 ? {\n            ...payloadBase,\n            delivered: (_arr3$0$delivered = arr3[0].delivered) === null || _arr3$0$delivered === void 0 ? void 0 : _arr3$0$delivered.value,\n            receivable: (_arr3$0$delivered2 = arr3[0].delivered) === null || _arr3$0$delivered2 === void 0 ? void 0 : _arr3$0$delivered2.value,\n            received: (_arr3$0$delivered3 = arr3[0].delivered) === null || _arr3$0$delivered3 === void 0 ? void 0 : _arr3$0$delivered3.value,\n            financial: (_arr3$0$delivered4 = arr3[0].delivered) === null || _arr3$0$delivered4 === void 0 ? void 0 : _arr3$0$delivered4.value,\n            hcInfo: []\n          } : productivityDefaultValue);\n        }\n      } else {\n        productivityArray && productivityArray.length > 0 && setProductivity(createProductivityObject(productivityArray[0]));\n      }\n    } else {\n      let arr4 = getDataProductivityByMonth(productivityBase, month);\n      setProductivity(arr4 && arr4.length > 0 ? createProductivityObject(arr4[0]) : productivityDefaultValue);\n    }\n  };\n  const getHeadCountValueByMonth = async month => {\n    const params = {\n      month,\n      year: conditions.year\n    };\n    const response = await sendRequest(Api.sale_productivity.getDetailHeadCountByMonth, params);\n    if (response) {\n      const {\n        status,\n        result\n      } = response;\n      if (status) {\n        const data = result.content;\n        const isEmptyData = isEmpty(data);\n        setProductivityHeadCount({\n          ...productivityHeadCount,\n          month,\n          value: !isEmptyData ? data.value : ''\n        });\n      }\n    }\n  };\n  const setDataEmpty = () => {\n    setMonthlyProductionPerformanceInfo(monthlyProductionPerformanceInfoDefault);\n    setLoading(false);\n  };\n  const getMonthInYears = useCallback(async y => {\n    const monthInYears = await getMonthsOfYear(y);\n    return monthInYears;\n  }, []);\n\n  // Event\n  const handleChangeYear = e => {\n    const value = e.target.value;\n    setYear(value);\n    setIsChangeYear(true);\n  };\n  const handleExportDocument = () => {\n    exportDocument(Api.sale_productivity.getDownload.url, {\n      year: conditions.year,\n      userName: userInfo === null || userInfo === void 0 ? void 0 : userInfo.userName,\n      language: locale\n    });\n  };\n  const handleOpenDialog = async idHexString => {\n    getDetailMonths(idHexString);\n    setOpen(true);\n  };\n  const handleCloseDialog = () => {\n    setOpen(false);\n  };\n  const handleOpenEditHeadCountDialog = item => {\n    setProductivityHeadCount({\n      year: conditions.year,\n      month: conditions.month ? conditions.month[0] : '1',\n      value: item[0].value\n    });\n    setOpenHC(true);\n  };\n  const handleCloseEditHeadCountDialog = () => {\n    setOpenHC(false);\n  };\n  const postAddOrEditProductivity = async payload => {\n    setAddOrEditLoading(true);\n    const response = await sendRequest(Api.sale_productivity.postCreateOrUpdate, payload);\n    if (response) {\n      var _response$result, _response$result$cont, _response$result$cont2;\n      const {\n        status\n      } = response;\n      setAddOrEditLoading(false);\n      store.dispatch(openSnackbar({\n        open: true,\n        message: status ? 'update-success' : 'exist-project-name',\n        variant: 'alert',\n        alert: {\n          color: status ? 'success' : 'warning'\n        }\n      }));\n      getDetailMonths((_response$result = response.result) === null || _response$result === void 0 ? void 0 : (_response$result$cont = _response$result.content) === null || _response$result$cont === void 0 ? void 0 : (_response$result$cont2 = _response$result$cont.message) === null || _response$result$cont2 === void 0 ? void 0 : _response$result$cont2.idHexString, payload.month, payload.standardWorkingDay);\n      getDataTable();\n    } else {\n      setAddOrEditLoading(false);\n    }\n  };\n  const postEditHeadCount = async payload => {\n    setAddOrEditLoading(true);\n    const response = await sendRequest(Api.sale_productivity.postUpdateHeadCount, payload);\n    if (response) {\n      const {\n        status\n      } = response;\n      if (status) {\n        setAddOrEditLoading(false);\n        handleCloseEditHeadCountDialog();\n        store.dispatch(openSnackbar({\n          open: true,\n          message: 'update-success',\n          variant: 'alert',\n          alert: {\n            color: 'success'\n          }\n        }));\n        getDataTable();\n      } else {\n        setAddOrEditLoading(false);\n      }\n    } else {\n      setAddOrEditLoading(false);\n    }\n  };\n  const postEditComment = async payload => {\n    const response = await sendRequest(Api.sale_productivity.postComment, {\n      ...payload,\n      months: conditions.month,\n      year: conditions.year\n    });\n    if (response) {\n      const result = response.result.content;\n      const departmentNews = monthlyProductionPerformanceInfo.departments.map(department => ({\n        ...department,\n        data: department.data.map(el => el.idHexString === result.idHexString ? result : el)\n      }));\n      setMonthlyProductionPerformanceInfo({\n        ...monthlyProductionPerformanceInfo,\n        departments: departmentNews\n      });\n      store.dispatch(openSnackbar({\n        open: true,\n        message: 'update-success',\n        variant: 'alert',\n        alert: {\n          color: 'success'\n        }\n      }));\n      setIsEditComment(false);\n    }\n  };\n  const handleOpenComment = (event, item) => {\n    setAnchorEl(event.currentTarget);\n    setCommentItem(item);\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n    setIsEditComment(false);\n  };\n\n  // Handle submit\n  const handleSearch = value => {\n    transformObject(value);\n    setSearchParams(value);\n    setConditions(value);\n  };\n  const handleEditProductivity = productivityEdit => {\n    postAddOrEditProductivity(productivityEdit);\n  };\n  const handleEditHeadCount = headCountEdit => {\n    postEditHeadCount(headCountEdit);\n  };\n  const handleEditComment = comment => {\n    postEditComment(comment);\n  };\n  const dispatch = useAppDispatch();\n  const handleConFirmEditRows = async orderList => {\n    const res = await sendRequest(Api.flexible_report.editArrangement, orderList);\n    dispatch(openSnackbar({\n      open: true,\n      message: res.status ? 'update-success' : 'update-fail',\n      variant: 'alert',\n      alert: {\n        color: res.status ? 'success' : 'error'\n      }\n    }));\n    if (res.status) {\n      setIsEdited(false);\n      getDataTable();\n    }\n  };\n  // Effect\n  useEffect(() => {\n    getDataTable();\n  }, [conditions]);\n  useEffect(() => {\n    getMonthInYears(year).then(items => {\n      setMonths(items);\n      if (items.length > 0 && isChangeYear) setFormReset({\n        ...formReset,\n        year,\n        month: []\n      });\n    });\n  }, [year]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(FilterCollapse, {\n      handleExport: checkAllowedPermission(monthlyProductionPerformancePermission.download) ? handleExportDocument : undefined,\n      downloadLabel: TEXT_CONFIG_SCREEN.salesReport.monthlyProductionPerformance + '-download-report',\n      children: /*#__PURE__*/_jsxDEV(MonthlyProductionPerformanceSearch, {\n        conditions: formReset,\n        months: months,\n        handleChangeYear: handleChangeYear,\n        handleSearch: handleSearch\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 525,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(MainCard, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        heads: /*#__PURE__*/_jsxDEV(MonthlyProductionPerformanceThead, {\n          months: monthlyProductionPerformanceInfo.months,\n          count: monthlyProductionPerformanceInfo.departments.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 25\n        }, this),\n        isLoading: loading,\n        data: monthlyProductionPerformanceInfo.departments,\n        children: /*#__PURE__*/_jsxDEV(MonthlyProductionPerformanceTBody, {\n          info: monthlyProductionPerformanceInfo,\n          handleOpen: handleOpenDialog,\n          handleOpenComment: handleOpenComment,\n          handleOpenEditHeadCount: handleOpenEditHeadCountDialog,\n          editComment: handleEditComment,\n          setIsEdited: setIsEdited,\n          isEdited: isEdited,\n          handleConFirmEdit: handleConFirmEditRows\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 13\n    }, this), open && /*#__PURE__*/_jsxDEV(AddOrEditProductionPerformance, {\n      open: open,\n      loading: addOrEditLoading,\n      months: months,\n      handleClose: handleCloseDialog,\n      productivity: productivity,\n      editProductivity: handleEditProductivity,\n      onChangeYearGetStandardWorkingDay: onChangeYearGetStandardWorkingDay,\n      loadingData: loadingData\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 564,\n      columnNumber: 17\n    }, this), openHC && /*#__PURE__*/_jsxDEV(EditHeadCount, {\n      open: openHC,\n      loading: addOrEditLoading,\n      headCount: productivityHeadCount,\n      months: months,\n      handleClose: handleCloseEditHeadCountDialog,\n      getDetailByMonth: getHeadCountValueByMonth,\n      editHeadCount: handleEditHeadCount\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 578,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(CommentPopover, {\n      item: commentItem,\n      anchorEl: anchorEl,\n      handleClose: handleClose,\n      isEdit: isEditComment,\n      setIsEdit: setIsEditComment,\n      editComment: handleEditComment\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 590,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(MonthlyProductionPerformance, \"Sh0t3IQjUZEnObMIeF5rRseixz8=\", false, function () {\n  return [useSearchParams, useConfig, useAppSelector, useAppDispatch];\n});\n_c = MonthlyProductionPerformance;\nexport default MonthlyProductionPerformance;\nvar _c;\n$RefreshReg$(_c, \"MonthlyProductionPerformance\");", "map": {"version": 3, "names": ["React", "useCallback", "useEffect", "useState", "useSearchParams", "store", "MainCard", "Table", "Api", "CONTRACT_TYPE_SALE_REPORT", "DEPARTMENTS", "SEARCH_PARAM_KEY", "TEXT_CONFIG_SCREEN", "UNIT_SALE_REPORT", "PERMISSIONS", "AddOrEditProductionPerformance", "CommentPopover", "EditHeadCount", "MonthlyProductionPerformanceSearch", "MonthlyProductionPerformanceTBody", "MonthlyProductionPerformanceThead", "FilterCollapse", "sendRequest", "openSnackbar", "checkAllowedPermission", "exportDocument", "getDataProductivityByMonth", "getSearchParam", "isEmpty", "transformObject", "authSelector", "convertMonthFromToDate", "getMonthsOfYear", "monthlyProductionPerformanceFilterConfig", "monthlyProductionPerformanceInfoDefault", "productionPerformanceAddOrEditFormDefault", "productivityHeadCountEditFormDefault", "useAppDispatch", "useAppSelector", "useConfig", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MonthlyProductionPerformance", "_s", "searchParams", "setSearchParams", "keyParams", "year", "keyParamsArray", "month", "params", "locale", "defaultConditions", "language", "userInfo", "loading", "setLoading", "addOrEditLoading", "setAddOrEditLoading", "open", "<PERSON><PERSON><PERSON>", "openHC", "setOpenHC", "monthlyProductionPerformanceInfo", "setMonthlyProductionPerformanceInfo", "conditions", "setConditions", "formReset", "setFormReset", "months", "setMonths", "setYear", "isChangeYear", "setIsChangeYear", "productivity", "setProductivity", "productivityHeadCount", "setProductivityHeadCount", "anchorEl", "setAnchorEl", "commentItem", "setCommentItem", "isEditComment", "setIsEditComment", "monthlyProductionPerformancePermission", "sale", "loadingData", "setLoadingData", "isEdited", "setIsEdited", "getDataTable", "response", "sale_productivity", "getAll", "status", "result", "content", "setDataEmpty", "getDetailMonths", "idHexString", "standardWorkingDayOfMonth", "getDetail", "_data$project", "_data$project2", "_data$productivity$", "data", "getMonth", "filter", "value", "foundObject", "delivered", "comment", "receivable", "received", "financial", "hcInfo", "for<PERSON>ach", "item", "index", "fromDate", "toDate", "label", "standardWorkingDay", "getStandardWorkingDay", "payload", "toString", "projectId", "project", "projectName", "departmentId", "contractSize", "serviceType", "contractType", "originalContractSize", "unit", "MAN_MONTH", "contractAllocation", "duration", "paymentTerm", "currency", "exchangeRate", "deliveredCurrency", "receivableCurrency", "received<PERSON><PERSON>rency", "financialCurrency", "lastYearProductivity", "onChangeYearGetStandardWorkingDay", "standardWorkingDayValue", "productivityBase", "productivityLastYear", "payloadBase", "String", "productivityArray", "el", "productivityDefaultValue", "createProductivityObject", "_item$delivered", "_item$receivable", "_item$received", "_item$financial", "_item$delivered2", "_item$receivable2", "_item$received2", "_item$financial2", "TM", "SCS", "_productivityArray$0$", "_productivityArray$0$2", "_productivityArray$0$3", "_productivityArray$0$4", "Array", "isArray", "length", "_arr1$0$delivered", "_arr1$0$delivered2", "_arr1$0$delivered3", "_arr1$0$delivered4", "arr1", "_arr2$0$delivered", "_arr2$0$delivered2", "_arr2$0$delivered3", "_arr2$0$delivered4", "arr2", "_arr3$0$delivered", "_arr3$0$delivered2", "_arr3$0$delivered3", "_arr3$0$delivered4", "count", "countFinal", "arr3", "arr4", "getHeadCountValueByMonth", "getDetailHeadCountByMonth", "isEmptyData", "getMonthInYears", "y", "monthInYears", "handleChangeYear", "e", "target", "handleExportDocument", "getDownload", "url", "userName", "handleOpenDialog", "handleCloseDialog", "handleOpenEditHeadCountDialog", "handleCloseEditHeadCountDialog", "postAddOrEditProductivity", "postCreateOrUpdate", "_response$result", "_response$result$cont", "_response$result$cont2", "dispatch", "message", "variant", "alert", "color", "postEditHeadCount", "postUpdateHeadCount", "postEditComment", "postComment", "departmentNews", "departments", "map", "department", "handleOpenComment", "event", "currentTarget", "handleClose", "handleSearch", "handleEditProductivity", "productivityEdit", "handleEditHeadCount", "headCountEdit", "handleEditComment", "handleConFirmEditRows", "orderList", "res", "flexible_report", "editArrangement", "then", "items", "children", "handleExport", "download", "undefined", "downloadLabel", "salesReport", "monthlyProductionPerformance", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "heads", "isLoading", "info", "handleOpen", "handleOpenEditHeadCount", "editComment", "handleConFirmEdit", "editProductivity", "headCount", "getDetailByMonth", "editHeadCount", "isEdit", "setIsEdit", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/pages/sales/MonthlyProductionPerformance.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\nimport React, { useCallback, useEffect, useState } from 'react';\n\n// third party\nimport { useSearchParams } from 'react-router-dom';\n\n// material-ui\nimport { PopoverVirtualElement, SelectChangeEvent } from '@mui/material';\n\n// project imports\nimport { store } from 'app/store';\nimport MainCard from 'components/cards/MainCard';\nimport { Table } from 'components/extended/Table';\nimport Api from 'constants/Api';\nimport { CONTRACT_TYPE_SALE_REPORT, DEPARTMENTS, SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN, UNIT_SALE_REPORT } from 'constants/Common';\nimport { PERMISSIONS } from 'constants/Permission';\nimport {\n    AddOrEditProductionPerformance,\n    CommentPopover,\n    EditHeadCount,\n    MonthlyProductionPerformanceSearch,\n    MonthlyProductionPerformanceTBody,\n    MonthlyProductionPerformanceThead\n} from 'containers/sales';\nimport { FilterCollapse } from 'containers/search';\nimport sendRequest from 'services/ApiService';\nimport { openSnackbar } from 'store/slice/snackbarSlice';\nimport {\n    IAddOrEditProductivityResponse,\n    ICommentForm,\n    ICommentItem,\n    IDataHCByMonth,\n    IDepartmentPerformance,\n    IDepartmentPerformanceData,\n    IEditCommentResponse,\n    IHeadCountValueByMonthDetailResponse,\n    ILastYearProductivity,\n    IMonthlyProductionPerformanceAddOrEditForm,\n    IMonthlyProductionPerformanceInfo,\n    IMonthlyProductionPerformanceResponse,\n    IOption,\n    IProductivity,\n    IProductivityHeadCountEditForm,\n    IResponseList,\n    ISaleTotal\n} from 'types';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { exportDocument, getDataProductivityByMonth, getSearchParam, isEmpty, transformObject } from 'utils/common';\nimport { authSelector } from 'store/slice/authSlice';\nimport { convertMonthFromToDate, getMonthsOfYear } from 'utils/date';\nimport {\n    IMonthlyProductionPerformanceFilterConfig,\n    monthlyProductionPerformanceFilterConfig,\n    monthlyProductionPerformanceInfoDefault,\n    productionPerformanceAddOrEditFormDefault,\n    productivityHeadCountEditFormDefault\n} from './Config';\nimport { useAppDispatch, useAppSelector } from 'app/hooks';\nimport useConfig from 'hooks/useConfig';\n\n// ==============================|| Monthly Production Performance ||============================== //\n\nconst MonthlyProductionPerformance = () => {\n    // URL Params\n    const [searchParams, setSearchParams] = useSearchParams();\n    const keyParams = [SEARCH_PARAM_KEY.year];\n    const keyParamsArray = [SEARCH_PARAM_KEY.month];\n    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams, keyParamsArray);\n    transformObject(params);\n    const { locale } = useConfig();\n\n    // Hooks, State, Variable\n    const defaultConditions = { ...monthlyProductionPerformanceFilterConfig, ...params, language: locale };\n    const { userInfo } = useAppSelector(authSelector);\n    const [loading, setLoading] = useState<boolean>(false);\n    const [addOrEditLoading, setAddOrEditLoading] = useState<boolean>(false);\n    const [open, setOpen] = useState<boolean>(false);\n    const [openHC, setOpenHC] = useState<boolean>(false);\n    const [monthlyProductionPerformanceInfo, setMonthlyProductionPerformanceInfo] = useState<IMonthlyProductionPerformanceInfo>(\n        monthlyProductionPerformanceInfoDefault\n    );\n\n    const [conditions, setConditions] = useState<IMonthlyProductionPerformanceFilterConfig>(defaultConditions);\n    const [formReset, setFormReset] = useState<IMonthlyProductionPerformanceFilterConfig>(defaultConditions);\n    const [months, setMonths] = useState<IOption[]>([]);\n    const [year, setYear] = useState<number>(defaultConditions.year);\n    const [isChangeYear, setIsChangeYear] = useState<boolean>(false);\n    const [productivity, setProductivity] = useState<IMonthlyProductionPerformanceAddOrEditForm>(productionPerformanceAddOrEditFormDefault);\n    const [productivityHeadCount, setProductivityHeadCount] = useState<IProductivityHeadCountEditForm>(\n        productivityHeadCountEditFormDefault\n    );\n    const [anchorEl, setAnchorEl] = useState<\n        Element | (() => Element) | PopoverVirtualElement | (() => PopoverVirtualElement) | null | undefined\n    >(null);\n    const [commentItem, setCommentItem] = useState<ICommentItem | null>(null);\n    const [isEditComment, setIsEditComment] = useState<boolean>(false);\n    const { monthlyProductionPerformancePermission } = PERMISSIONS.sale;\n    const [loadingData, setLoadingData] = useState<boolean>(false);\n    const [isEdited, setIsEdited] = useState(false);\n\n    // Functions\n    const getDataTable = async () => {\n        setLoading(true);\n        const response: IResponseList<IMonthlyProductionPerformanceResponse> = await sendRequest(Api.sale_productivity.getAll, {\n            ...conditions\n        });\n        if (response) {\n            const { status, result } = response;\n            if (status) {\n                setMonthlyProductionPerformanceInfo(result.content as IMonthlyProductionPerformanceInfo);\n            } else {\n                setDataEmpty();\n            }\n            setLoading(false);\n        } else {\n            setDataEmpty();\n        }\n    };\n\n    const getDetailMonths = async (idHexString: string, month?: any, standardWorkingDayOfMonth?: string) => {\n        setLoadingData(true);\n        const params = { idHexString, year: conditions.year };\n        const response = await sendRequest(Api.sale_productivity.getDetail, params);\n        const { status, result } = response;\n        if (response && status) {\n            setLoadingData(false);\n            const data = result.content;\n            const getMonth = months.filter((month) => month.value === data.productivity[0].month);\n\n            let foundObject = {\n                month: 1,\n                delivered: {\n                    value: 0,\n                    comment: ''\n                },\n                receivable: {\n                    value: 0,\n                    comment: ''\n                },\n                received: {\n                    value: 0,\n                    comment: ''\n                },\n                financial: {\n                    value: 0,\n                    comment: ''\n                },\n                hcInfo: [],\n                year: year\n            };\n\n            data.productivity.forEach((item: ILastYearProductivity, index: number) => {\n                // eslint-disable-next-line eqeqeq\n                if (item.month == month) {\n                    foundObject = data.productivity[index];\n                }\n            });\n            const { fromDate, toDate } = convertMonthFromToDate(getMonth[0].label);\n            const standardWorkingDay = await getStandardWorkingDay(fromDate, toDate);\n            const payload = {\n                idHexString: data.idHexString,\n                year: conditions.year,\n                month: month ? foundObject.month : data.productivity[0].month,\n                standardWorkingDay: month ? standardWorkingDayOfMonth : standardWorkingDay.toString() || '1',\n                projectId: { value: data.project?.projectId, label: data.project?.projectName },\n                departmentId: data.departmentId,\n                projectName: data.project.projectName,\n                contractSize: data.contractSize.value,\n                serviceType: data.serviceType,\n                contractType: data.contractType,\n                originalContractSize: data.originalContractSize,\n                unit: data.unit || UNIT_SALE_REPORT.MAN_MONTH,\n                contractAllocation: data.contractAllocation,\n                duration: data.duration,\n                paymentTerm: data.paymentTerm || 1,\n                currency: data.exchangeRate.label,\n                exchangeRate: data.exchangeRate.value,\n                delivered: month ? foundObject.delivered.value : data?.productivity[0].delivered.value,\n                receivable: month ? foundObject.receivable.value : data?.productivity[0].receivable.value,\n                received: month ? foundObject.received.value : data?.productivity[0].received.value,\n                financial: month ? foundObject.financial.value : data?.productivity[0].financial.value,\n                deliveredCurrency: month\n                    ? foundObject.delivered.value * data.exchangeRate.value\n                    : data?.productivity[0].delivered.value * data.exchangeRate.value,\n                receivableCurrency: month\n                    ? foundObject.receivable.value * data.exchangeRate.value\n                    : data?.productivity[0].receivable.value * data.exchangeRate.value,\n                receivedCurrency: month\n                    ? foundObject.received.value * data.exchangeRate.value\n                    : data?.productivity[0].received.value * data.exchangeRate.value,\n                financialCurrency: month\n                    ? foundObject.financial.value * data.exchangeRate.value\n                    : data?.productivity[0].financial.value * data.exchangeRate.value,\n                hcInfo: month ? foundObject.hcInfo : data?.productivity[0]?.hcInfo || [],\n                productivity: data?.productivity || [],\n                lastYearProductivity: data.lastYearProductivity\n            };\n            setProductivity(payload);\n        }\n    };\n\n    const getStandardWorkingDay = async (fromDate: string, toDate: string) => {\n        let standardWorkingDay = 0;\n        const params = { fromDate, toDate };\n        const response = await sendRequest(Api.sale_productivity.getStandardWorkingDay, params);\n        const { status, result } = response;\n        if (response && status) {\n            const { value } = result.content;\n            standardWorkingDay = value;\n        }\n        return standardWorkingDay;\n    };\n\n    const onChangeYearGetStandardWorkingDay = async (\n        payload: IMonthlyProductionPerformanceAddOrEditForm,\n        fromDate: string,\n        toDate: string,\n        month?: number,\n        paymentTerm?: string\n    ) => {\n        const standardWorkingDayValue = await getStandardWorkingDay(fromDate, toDate);\n        const productivityBase = productivity.productivity ? productivity.productivity : [];\n        const productivityLastYear = productivity.lastYearProductivity ? productivity.lastYearProductivity : [];\n        const exchangeRate = payload.exchangeRate || 1;\n\n        let payloadBase = {\n            ...payload,\n            month: month ? month : 1,\n            standardWorkingDay: String(standardWorkingDayValue),\n            paymentTerm: paymentTerm ? paymentTerm : payload.paymentTerm\n        };\n        let productivityArray =\n            productivity.productivity &&\n            productivity.productivity.filter((el: IProductivity) => {\n                return el.month === month;\n            });\n\n        const productivityDefaultValue = {\n            ...payloadBase,\n            delivered: 0,\n            receivable: 0,\n            received: 0,\n            financial: 0,\n            deliveredCurrency: 0,\n            receivableCurrency: 0,\n            receivedCurrency: 0,\n            financialCurrency: 0,\n            hcInfo: []\n        };\n        const createProductivityObject = (item: any) => {\n            return {\n                ...payloadBase,\n                delivered: item.delivered?.value || 0,\n                receivable: item.receivable?.value || 0,\n                received: item.received?.value || 0,\n                financial: item.financial?.value || 0,\n                deliveredCurrency: item.delivered?.value * exchangeRate,\n                receivableCurrency: item.receivable?.value * exchangeRate,\n                receivedCurrency: item.received?.value * exchangeRate,\n                financialCurrency: item.financial?.value * exchangeRate,\n                hcInfo: item.hcInfo || []\n            };\n        };\n\n        // TH productivityArray trả về mảng rỗng\n        if (payload.contractType === CONTRACT_TYPE_SALE_REPORT.TM && payload.departmentId === DEPARTMENTS.SCS) {\n            if (\n                Array.isArray(productivityArray) &&\n                (productivityArray.length === 0 ||\n                    (productivityArray[0].delivered?.value === 0 &&\n                        productivityArray[0].receivable?.value === 0 &&\n                        productivityArray[0].received?.value === 0 &&\n                        productivityArray[0].financial?.value === 0))\n            ) {\n                // Month > Payment Term\n                if (month && +month > +(paymentTerm ? paymentTerm : payload.paymentTerm)) {\n                    let arr1 = getDataProductivityByMonth(productivityBase, +month - +(paymentTerm ? paymentTerm : payload.paymentTerm));\n\n                    setProductivity(\n                        arr1 && arr1.length > 0\n                            ? {\n                                  ...payloadBase,\n                                  delivered: arr1[0].delivered?.value,\n                                  receivable: arr1[0].delivered?.value,\n                                  received: arr1[0].delivered?.value,\n                                  financial: arr1[0].delivered?.value\n                              }\n                            : productivityDefaultValue\n                    );\n                }\n\n                // Month = Payment Term\n                if (month && +month === +(paymentTerm ? paymentTerm : payload.paymentTerm)) {\n                    let arr2 = getDataProductivityByMonth(productivityLastYear, 12);\n\n                    setProductivity(\n                        arr2 && arr2.length > 0\n                            ? {\n                                  ...payloadBase,\n                                  delivered: arr2[0].delivered?.value,\n                                  receivable: arr2[0].delivered?.value,\n                                  received: arr2[0].delivered?.value,\n                                  financial: arr2[0].delivered?.value,\n                                  hcInfo: []\n                              }\n                            : productivityDefaultValue\n                    );\n                }\n\n                // Month < Payment Term\n                if (month && +month < +(paymentTerm ? paymentTerm : payload.paymentTerm)) {\n                    const count = +(paymentTerm ? paymentTerm : payload.paymentTerm) - +month;\n                    const countFinal = 12 - count;\n                    let arr3 = getDataProductivityByMonth(productivityLastYear, countFinal);\n\n                    setProductivity(\n                        arr3 && arr3.length > 0\n                            ? {\n                                  ...payloadBase,\n                                  delivered: arr3[0].delivered?.value,\n                                  receivable: arr3[0].delivered?.value,\n                                  received: arr3[0].delivered?.value,\n                                  financial: arr3[0].delivered?.value,\n                                  hcInfo: []\n                              }\n                            : productivityDefaultValue\n                    );\n                }\n            } else {\n                productivityArray && productivityArray.length > 0 && setProductivity(createProductivityObject(productivityArray[0]));\n            }\n        } else {\n            let arr4 = getDataProductivityByMonth(productivityBase, month!);\n            setProductivity(arr4 && arr4.length > 0 ? createProductivityObject(arr4[0]) : productivityDefaultValue);\n        }\n    };\n\n    const getHeadCountValueByMonth = async (month: string) => {\n        const params = { month, year: conditions.year };\n        const response: IResponseList<IHeadCountValueByMonthDetailResponse> = await sendRequest(\n            Api.sale_productivity.getDetailHeadCountByMonth,\n            params\n        );\n        if (response) {\n            const { status, result } = response;\n\n            if (status) {\n                const data = result.content as IDataHCByMonth;\n                const isEmptyData = isEmpty(data);\n                setProductivityHeadCount({\n                    ...productivityHeadCount,\n                    month,\n                    value: !isEmptyData ? data.value : ''\n                });\n            }\n        }\n    };\n\n    const setDataEmpty = () => {\n        setMonthlyProductionPerformanceInfo(monthlyProductionPerformanceInfoDefault);\n        setLoading(false);\n    };\n\n    const getMonthInYears = useCallback(async (y: number) => {\n        const monthInYears = await getMonthsOfYear(y);\n        return monthInYears;\n    }, []);\n\n    // Event\n    const handleChangeYear = (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => {\n        const value = e.target.value;\n        setYear(value as number);\n        setIsChangeYear(true);\n    };\n\n    const handleExportDocument = () => {\n        exportDocument(Api.sale_productivity.getDownload.url, { year: conditions.year, userName: userInfo?.userName, language: locale });\n    };\n\n    const handleOpenDialog = async (idHexString: string) => {\n        getDetailMonths(idHexString);\n        setOpen(true);\n    };\n\n    const handleCloseDialog = () => {\n        setOpen(false);\n    };\n\n    const handleOpenEditHeadCountDialog = (item: IDataHCByMonth[]) => {\n        setProductivityHeadCount({\n            year: conditions.year,\n            month: conditions.month ? conditions.month[0] : '1',\n            value: item[0].value\n        });\n        setOpenHC(true);\n    };\n\n    const handleCloseEditHeadCountDialog = () => {\n        setOpenHC(false);\n    };\n\n    const postAddOrEditProductivity = async (payload: IMonthlyProductionPerformanceAddOrEditForm) => {\n        setAddOrEditLoading(true);\n        const response: IResponseList<IAddOrEditProductivityResponse> = await sendRequest(\n            Api.sale_productivity.postCreateOrUpdate,\n            payload\n        );\n        if (response) {\n            const { status } = response;\n            setAddOrEditLoading(false);\n            store.dispatch(\n                openSnackbar({\n                    open: true,\n                    message: status ? 'update-success' : 'exist-project-name',\n                    variant: 'alert',\n                    alert: { color: status ? 'success' : 'warning' }\n                })\n            );\n            getDetailMonths(response.result?.content?.message?.idHexString, payload.month, payload.standardWorkingDay);\n            getDataTable();\n        } else {\n            setAddOrEditLoading(false);\n        }\n    };\n\n    const postEditHeadCount = async (payload: IProductivityHeadCountEditForm) => {\n        setAddOrEditLoading(true);\n        const response: IResponseList<IAddOrEditProductivityResponse> = await sendRequest(\n            Api.sale_productivity.postUpdateHeadCount,\n            payload\n        );\n        if (response) {\n            const { status } = response;\n            if (status) {\n                setAddOrEditLoading(false);\n                handleCloseEditHeadCountDialog();\n                store.dispatch(openSnackbar({ open: true, message: 'update-success', variant: 'alert', alert: { color: 'success' } }));\n                getDataTable();\n            } else {\n                setAddOrEditLoading(false);\n            }\n        } else {\n            setAddOrEditLoading(false);\n        }\n    };\n\n    const postEditComment = async (payload?: ICommentForm) => {\n        const response: IResponseList<IEditCommentResponse> = await sendRequest(Api.sale_productivity.postComment, {\n            ...payload,\n            months: conditions.month,\n            year: conditions.year\n        });\n\n        if (response) {\n            const result = response.result.content;\n            const departmentNews = monthlyProductionPerformanceInfo.departments.map((department: IDepartmentPerformance) => ({\n                ...department,\n                data: department.data.map((el: IDepartmentPerformanceData) => (el.idHexString === result.idHexString ? result : el))\n            }));\n            setMonthlyProductionPerformanceInfo({ ...monthlyProductionPerformanceInfo, departments: departmentNews });\n            store.dispatch(openSnackbar({ open: true, message: 'update-success', variant: 'alert', alert: { color: 'success' } }));\n            setIsEditComment(false);\n        }\n    };\n\n    const handleOpenComment = (event: React.MouseEvent<Element>, item: ICommentItem) => {\n        setAnchorEl(event.currentTarget);\n        setCommentItem(item);\n    };\n\n    const handleClose = () => {\n        setAnchorEl(null);\n        setIsEditComment(false);\n    };\n\n    // Handle submit\n    const handleSearch = (value: IMonthlyProductionPerformanceFilterConfig) => {\n        transformObject(value);\n        setSearchParams(value as any);\n        setConditions(value);\n    };\n\n    const handleEditProductivity = (productivityEdit: IMonthlyProductionPerformanceAddOrEditForm) => {\n        postAddOrEditProductivity(productivityEdit);\n    };\n\n    const handleEditHeadCount = (headCountEdit: IProductivityHeadCountEditForm) => {\n        postEditHeadCount(headCountEdit);\n    };\n\n    const handleEditComment = (comment?: ICommentForm) => {\n        postEditComment(comment);\n    };\n    const dispatch = useAppDispatch();\n    const handleConFirmEditRows = async (orderList: ISaleTotal[]) => {\n        const res = await sendRequest(Api.flexible_report.editArrangement, orderList);\n        dispatch(\n            openSnackbar({\n                open: true,\n                message: res.status ? 'update-success' : 'update-fail',\n                variant: 'alert',\n                alert: { color: res.status ? 'success' : 'error' }\n            })\n        );\n        if (res.status) {\n            setIsEdited(false);\n            getDataTable();\n        }\n    };\n    // Effect\n    useEffect(() => {\n        getDataTable();\n    }, [conditions]);\n\n    useEffect(() => {\n        getMonthInYears(year).then((items: IOption[]) => {\n            setMonths(items);\n            if (items.length > 0 && isChangeYear) setFormReset({ ...formReset, year, month: [] });\n        });\n    }, [year]);\n\n    return (\n        <>\n            {/* Filter */}\n            <FilterCollapse\n                handleExport={checkAllowedPermission(monthlyProductionPerformancePermission.download) ? handleExportDocument : undefined}\n                downloadLabel={TEXT_CONFIG_SCREEN.salesReport.monthlyProductionPerformance + '-download-report'}\n            >\n                <MonthlyProductionPerformanceSearch\n                    conditions={formReset}\n                    months={months}\n                    handleChangeYear={handleChangeYear}\n                    handleSearch={handleSearch}\n                />\n            </FilterCollapse>\n\n            {/* Table */}\n            <MainCard>\n                <Table\n                    heads={\n                        <MonthlyProductionPerformanceThead\n                            months={monthlyProductionPerformanceInfo.months}\n                            count={monthlyProductionPerformanceInfo.departments.length}\n                        />\n                    }\n                    isLoading={loading}\n                    data={monthlyProductionPerformanceInfo.departments}\n                >\n                    <MonthlyProductionPerformanceTBody\n                        info={monthlyProductionPerformanceInfo}\n                        handleOpen={handleOpenDialog}\n                        handleOpenComment={handleOpenComment}\n                        handleOpenEditHeadCount={handleOpenEditHeadCountDialog}\n                        editComment={handleEditComment}\n                        setIsEdited={setIsEdited}\n                        isEdited={isEdited}\n                        handleConFirmEdit={handleConFirmEditRows}\n                    />\n                </Table>\n            </MainCard>\n\n            {/* Edit Production Performance Dialog */}\n            {open && (\n                <AddOrEditProductionPerformance\n                    open={open}\n                    loading={addOrEditLoading}\n                    months={months}\n                    handleClose={handleCloseDialog}\n                    productivity={productivity}\n                    editProductivity={handleEditProductivity}\n                    onChangeYearGetStandardWorkingDay={onChangeYearGetStandardWorkingDay}\n                    loadingData={loadingData}\n                />\n            )}\n\n            {/* Edit HeadCount Dialog */}\n            {openHC && (\n                <EditHeadCount\n                    open={openHC}\n                    loading={addOrEditLoading}\n                    headCount={productivityHeadCount}\n                    months={months}\n                    handleClose={handleCloseEditHeadCountDialog}\n                    getDetailByMonth={getHeadCountValueByMonth}\n                    editHeadCount={handleEditHeadCount}\n                />\n            )}\n\n            {/* Comment */}\n            <CommentPopover\n                item={commentItem!}\n                anchorEl={anchorEl}\n                handleClose={handleClose}\n                isEdit={isEditComment}\n                setIsEdit={setIsEditComment}\n                editComment={handleEditComment}\n            />\n        </>\n    );\n};\n\nexport default MonthlyProductionPerformance;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;;AAE/D;AACA,SAASC,eAAe,QAAQ,kBAAkB;;AAElD;;AAGA;AACA,SAASC,KAAK,QAAQ,WAAW;AACjC,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,KAAK,QAAQ,2BAA2B;AACjD,OAAOC,GAAG,MAAM,eAAe;AAC/B,SAASC,yBAAyB,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAEC,gBAAgB,QAAQ,kBAAkB;AACjI,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SACIC,8BAA8B,EAC9BC,cAAc,EACdC,aAAa,EACbC,kCAAkC,EAClCC,iCAAiC,EACjCC,iCAAiC,QAC9B,kBAAkB;AACzB,SAASC,cAAc,QAAQ,mBAAmB;AAClD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,YAAY,QAAQ,2BAA2B;AAoBxD,SAASC,sBAAsB,QAAQ,qBAAqB;AAC5D,SAASC,cAAc,EAAEC,0BAA0B,EAAEC,cAAc,EAAEC,OAAO,EAAEC,eAAe,QAAQ,cAAc;AACnH,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,sBAAsB,EAAEC,eAAe,QAAQ,YAAY;AACpE,SAEIC,wCAAwC,EACxCC,uCAAuC,EACvCC,yCAAyC,EACzCC,oCAAoC,QACjC,UAAU;AACjB,SAASC,cAAc,EAAEC,cAAc,QAAQ,WAAW;AAC1D,OAAOC,SAAS,MAAM,iBAAiB;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,4BAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3C,eAAe,CAAC,CAAC;EACzD,MAAM4C,SAAS,GAAG,CAACrC,gBAAgB,CAACsC,IAAI,CAAC;EACzC,MAAMC,cAAc,GAAG,CAACvC,gBAAgB,CAACwC,KAAK,CAAC;EAC/C,MAAMC,MAA8B,GAAGzB,cAAc,CAACqB,SAAS,EAAEF,YAAY,EAAEI,cAAc,CAAC;EAC9FrB,eAAe,CAACuB,MAAM,CAAC;EACvB,MAAM;IAAEC;EAAO,CAAC,GAAGd,SAAS,CAAC,CAAC;;EAE9B;EACA,MAAMe,iBAAiB,GAAG;IAAE,GAAGrB,wCAAwC;IAAE,GAAGmB,MAAM;IAAEG,QAAQ,EAAEF;EAAO,CAAC;EACtG,MAAM;IAAEG;EAAS,CAAC,GAAGlB,cAAc,CAACR,YAAY,CAAC;EACjD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAU,KAAK,CAAC;EACtD,MAAM,CAACwD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzD,QAAQ,CAAU,KAAK,CAAC;EACxE,MAAM,CAAC0D,IAAI,EAAEC,OAAO,CAAC,GAAG3D,QAAQ,CAAU,KAAK,CAAC;EAChD,MAAM,CAAC4D,MAAM,EAAEC,SAAS,CAAC,GAAG7D,QAAQ,CAAU,KAAK,CAAC;EACpD,MAAM,CAAC8D,gCAAgC,EAAEC,mCAAmC,CAAC,GAAG/D,QAAQ,CACpF+B,uCACJ,CAAC;EAED,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAA4CmD,iBAAiB,CAAC;EAC1G,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGnE,QAAQ,CAA4CmD,iBAAiB,CAAC;EACxG,MAAM,CAACiB,MAAM,EAAEC,SAAS,CAAC,GAAGrE,QAAQ,CAAY,EAAE,CAAC;EACnD,MAAM,CAAC8C,IAAI,EAAEwB,OAAO,CAAC,GAAGtE,QAAQ,CAASmD,iBAAiB,CAACL,IAAI,CAAC;EAChE,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAGxE,QAAQ,CAAU,KAAK,CAAC;EAChE,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAA6CgC,yCAAyC,CAAC;EACvI,MAAM,CAAC2C,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG5E,QAAQ,CAC9DiC,oCACJ,CAAC;EACD,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG9E,QAAQ,CAEtC,IAAI,CAAC;EACP,MAAM,CAAC+E,WAAW,EAAEC,cAAc,CAAC,GAAGhF,QAAQ,CAAsB,IAAI,CAAC;EACzE,MAAM,CAACiF,aAAa,EAAEC,gBAAgB,CAAC,GAAGlF,QAAQ,CAAU,KAAK,CAAC;EAClE,MAAM;IAAEmF;EAAuC,CAAC,GAAGxE,WAAW,CAACyE,IAAI;EACnE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtF,QAAQ,CAAU,KAAK,CAAC;EAC9D,MAAM,CAACuF,QAAQ,EAAEC,WAAW,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACA,MAAMyF,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7BlC,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMmC,QAA8D,GAAG,MAAMvE,WAAW,CAACd,GAAG,CAACsF,iBAAiB,CAACC,MAAM,EAAE;MACnH,GAAG5B;IACP,CAAC,CAAC;IACF,IAAI0B,QAAQ,EAAE;MACV,MAAM;QAAEG,MAAM;QAAEC;MAAO,CAAC,GAAGJ,QAAQ;MACnC,IAAIG,MAAM,EAAE;QACR9B,mCAAmC,CAAC+B,MAAM,CAACC,OAA4C,CAAC;MAC5F,CAAC,MAAM;QACHC,YAAY,CAAC,CAAC;MAClB;MACAzC,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,MAAM;MACHyC,YAAY,CAAC,CAAC;IAClB;EACJ,CAAC;EAED,MAAMC,eAAe,GAAG,MAAAA,CAAOC,WAAmB,EAAElD,KAAW,EAAEmD,yBAAkC,KAAK;IACpGb,cAAc,CAAC,IAAI,CAAC;IACpB,MAAMrC,MAAM,GAAG;MAAEiD,WAAW;MAAEpD,IAAI,EAAEkB,UAAU,CAAClB;IAAK,CAAC;IACrD,MAAM4C,QAAQ,GAAG,MAAMvE,WAAW,CAACd,GAAG,CAACsF,iBAAiB,CAACS,SAAS,EAAEnD,MAAM,CAAC;IAC3E,MAAM;MAAE4C,MAAM;MAAEC;IAAO,CAAC,GAAGJ,QAAQ;IACnC,IAAIA,QAAQ,IAAIG,MAAM,EAAE;MAAA,IAAAQ,aAAA,EAAAC,cAAA,EAAAC,mBAAA;MACpBjB,cAAc,CAAC,KAAK,CAAC;MACrB,MAAMkB,IAAI,GAAGV,MAAM,CAACC,OAAO;MAC3B,MAAMU,QAAQ,GAAGrC,MAAM,CAACsC,MAAM,CAAE1D,KAAK,IAAKA,KAAK,CAAC2D,KAAK,KAAKH,IAAI,CAAC/B,YAAY,CAAC,CAAC,CAAC,CAACzB,KAAK,CAAC;MAErF,IAAI4D,WAAW,GAAG;QACd5D,KAAK,EAAE,CAAC;QACR6D,SAAS,EAAE;UACPF,KAAK,EAAE,CAAC;UACRG,OAAO,EAAE;QACb,CAAC;QACDC,UAAU,EAAE;UACRJ,KAAK,EAAE,CAAC;UACRG,OAAO,EAAE;QACb,CAAC;QACDE,QAAQ,EAAE;UACNL,KAAK,EAAE,CAAC;UACRG,OAAO,EAAE;QACb,CAAC;QACDG,SAAS,EAAE;UACPN,KAAK,EAAE,CAAC;UACRG,OAAO,EAAE;QACb,CAAC;QACDI,MAAM,EAAE,EAAE;QACVpE,IAAI,EAAEA;MACV,CAAC;MAED0D,IAAI,CAAC/B,YAAY,CAAC0C,OAAO,CAAC,CAACC,IAA2B,EAAEC,KAAa,KAAK;QACtE;QACA,IAAID,IAAI,CAACpE,KAAK,IAAIA,KAAK,EAAE;UACrB4D,WAAW,GAAGJ,IAAI,CAAC/B,YAAY,CAAC4C,KAAK,CAAC;QAC1C;MACJ,CAAC,CAAC;MACF,MAAM;QAAEC,QAAQ;QAAEC;MAAO,CAAC,GAAG3F,sBAAsB,CAAC6E,QAAQ,CAAC,CAAC,CAAC,CAACe,KAAK,CAAC;MACtE,MAAMC,kBAAkB,GAAG,MAAMC,qBAAqB,CAACJ,QAAQ,EAAEC,MAAM,CAAC;MACxE,MAAMI,OAAO,GAAG;QACZzB,WAAW,EAAEM,IAAI,CAACN,WAAW;QAC7BpD,IAAI,EAAEkB,UAAU,CAAClB,IAAI;QACrBE,KAAK,EAAEA,KAAK,GAAG4D,WAAW,CAAC5D,KAAK,GAAGwD,IAAI,CAAC/B,YAAY,CAAC,CAAC,CAAC,CAACzB,KAAK;QAC7DyE,kBAAkB,EAAEzE,KAAK,GAAGmD,yBAAyB,GAAGsB,kBAAkB,CAACG,QAAQ,CAAC,CAAC,IAAI,GAAG;QAC5FC,SAAS,EAAE;UAAElB,KAAK,GAAAN,aAAA,GAAEG,IAAI,CAACsB,OAAO,cAAAzB,aAAA,uBAAZA,aAAA,CAAcwB,SAAS;UAAEL,KAAK,GAAAlB,cAAA,GAAEE,IAAI,CAACsB,OAAO,cAAAxB,cAAA,uBAAZA,cAAA,CAAcyB;QAAY,CAAC;QAC/EC,YAAY,EAAExB,IAAI,CAACwB,YAAY;QAC/BD,WAAW,EAAEvB,IAAI,CAACsB,OAAO,CAACC,WAAW;QACrCE,YAAY,EAAEzB,IAAI,CAACyB,YAAY,CAACtB,KAAK;QACrCuB,WAAW,EAAE1B,IAAI,CAAC0B,WAAW;QAC7BC,YAAY,EAAE3B,IAAI,CAAC2B,YAAY;QAC/BC,oBAAoB,EAAE5B,IAAI,CAAC4B,oBAAoB;QAC/CC,IAAI,EAAE7B,IAAI,CAAC6B,IAAI,IAAI3H,gBAAgB,CAAC4H,SAAS;QAC7CC,kBAAkB,EAAE/B,IAAI,CAAC+B,kBAAkB;QAC3CC,QAAQ,EAAEhC,IAAI,CAACgC,QAAQ;QACvBC,WAAW,EAAEjC,IAAI,CAACiC,WAAW,IAAI,CAAC;QAClCC,QAAQ,EAAElC,IAAI,CAACmC,YAAY,CAACnB,KAAK;QACjCmB,YAAY,EAAEnC,IAAI,CAACmC,YAAY,CAAChC,KAAK;QACrCE,SAAS,EAAE7D,KAAK,GAAG4D,WAAW,CAACC,SAAS,CAACF,KAAK,GAAGH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE/B,YAAY,CAAC,CAAC,CAAC,CAACoC,SAAS,CAACF,KAAK;QACtFI,UAAU,EAAE/D,KAAK,GAAG4D,WAAW,CAACG,UAAU,CAACJ,KAAK,GAAGH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE/B,YAAY,CAAC,CAAC,CAAC,CAACsC,UAAU,CAACJ,KAAK;QACzFK,QAAQ,EAAEhE,KAAK,GAAG4D,WAAW,CAACI,QAAQ,CAACL,KAAK,GAAGH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE/B,YAAY,CAAC,CAAC,CAAC,CAACuC,QAAQ,CAACL,KAAK;QACnFM,SAAS,EAAEjE,KAAK,GAAG4D,WAAW,CAACK,SAAS,CAACN,KAAK,GAAGH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE/B,YAAY,CAAC,CAAC,CAAC,CAACwC,SAAS,CAACN,KAAK;QACtFiC,iBAAiB,EAAE5F,KAAK,GAClB4D,WAAW,CAACC,SAAS,CAACF,KAAK,GAAGH,IAAI,CAACmC,YAAY,CAAChC,KAAK,GACrD,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE/B,YAAY,CAAC,CAAC,CAAC,CAACoC,SAAS,CAACF,KAAK,IAAGH,IAAI,CAACmC,YAAY,CAAChC,KAAK;QACrEkC,kBAAkB,EAAE7F,KAAK,GACnB4D,WAAW,CAACG,UAAU,CAACJ,KAAK,GAAGH,IAAI,CAACmC,YAAY,CAAChC,KAAK,GACtD,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE/B,YAAY,CAAC,CAAC,CAAC,CAACsC,UAAU,CAACJ,KAAK,IAAGH,IAAI,CAACmC,YAAY,CAAChC,KAAK;QACtEmC,gBAAgB,EAAE9F,KAAK,GACjB4D,WAAW,CAACI,QAAQ,CAACL,KAAK,GAAGH,IAAI,CAACmC,YAAY,CAAChC,KAAK,GACpD,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE/B,YAAY,CAAC,CAAC,CAAC,CAACuC,QAAQ,CAACL,KAAK,IAAGH,IAAI,CAACmC,YAAY,CAAChC,KAAK;QACpEoC,iBAAiB,EAAE/F,KAAK,GAClB4D,WAAW,CAACK,SAAS,CAACN,KAAK,GAAGH,IAAI,CAACmC,YAAY,CAAChC,KAAK,GACrD,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE/B,YAAY,CAAC,CAAC,CAAC,CAACwC,SAAS,CAACN,KAAK,IAAGH,IAAI,CAACmC,YAAY,CAAChC,KAAK;QACrEO,MAAM,EAAElE,KAAK,GAAG4D,WAAW,CAACM,MAAM,GAAG,CAAAV,IAAI,aAAJA,IAAI,wBAAAD,mBAAA,GAAJC,IAAI,CAAE/B,YAAY,CAAC,CAAC,CAAC,cAAA8B,mBAAA,uBAArBA,mBAAA,CAAuBW,MAAM,KAAI,EAAE;QACxEzC,YAAY,EAAE,CAAA+B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE/B,YAAY,KAAI,EAAE;QACtCuE,oBAAoB,EAAExC,IAAI,CAACwC;MAC/B,CAAC;MACDtE,eAAe,CAACiD,OAAO,CAAC;IAC5B;EACJ,CAAC;EAED,MAAMD,qBAAqB,GAAG,MAAAA,CAAOJ,QAAgB,EAAEC,MAAc,KAAK;IACtE,IAAIE,kBAAkB,GAAG,CAAC;IAC1B,MAAMxE,MAAM,GAAG;MAAEqE,QAAQ;MAAEC;IAAO,CAAC;IACnC,MAAM7B,QAAQ,GAAG,MAAMvE,WAAW,CAACd,GAAG,CAACsF,iBAAiB,CAAC+B,qBAAqB,EAAEzE,MAAM,CAAC;IACvF,MAAM;MAAE4C,MAAM;MAAEC;IAAO,CAAC,GAAGJ,QAAQ;IACnC,IAAIA,QAAQ,IAAIG,MAAM,EAAE;MACpB,MAAM;QAAEc;MAAM,CAAC,GAAGb,MAAM,CAACC,OAAO;MAChC0B,kBAAkB,GAAGd,KAAK;IAC9B;IACA,OAAOc,kBAAkB;EAC7B,CAAC;EAED,MAAMwB,iCAAiC,GAAG,MAAAA,CACtCtB,OAAmD,EACnDL,QAAgB,EAChBC,MAAc,EACdvE,KAAc,EACdyF,WAAoB,KACnB;IACD,MAAMS,uBAAuB,GAAG,MAAMxB,qBAAqB,CAACJ,QAAQ,EAAEC,MAAM,CAAC;IAC7E,MAAM4B,gBAAgB,GAAG1E,YAAY,CAACA,YAAY,GAAGA,YAAY,CAACA,YAAY,GAAG,EAAE;IACnF,MAAM2E,oBAAoB,GAAG3E,YAAY,CAACuE,oBAAoB,GAAGvE,YAAY,CAACuE,oBAAoB,GAAG,EAAE;IACvG,MAAML,YAAY,GAAGhB,OAAO,CAACgB,YAAY,IAAI,CAAC;IAE9C,IAAIU,WAAW,GAAG;MACd,GAAG1B,OAAO;MACV3E,KAAK,EAAEA,KAAK,GAAGA,KAAK,GAAG,CAAC;MACxByE,kBAAkB,EAAE6B,MAAM,CAACJ,uBAAuB,CAAC;MACnDT,WAAW,EAAEA,WAAW,GAAGA,WAAW,GAAGd,OAAO,CAACc;IACrD,CAAC;IACD,IAAIc,iBAAiB,GACjB9E,YAAY,CAACA,YAAY,IACzBA,YAAY,CAACA,YAAY,CAACiC,MAAM,CAAE8C,EAAiB,IAAK;MACpD,OAAOA,EAAE,CAACxG,KAAK,KAAKA,KAAK;IAC7B,CAAC,CAAC;IAEN,MAAMyG,wBAAwB,GAAG;MAC7B,GAAGJ,WAAW;MACdxC,SAAS,EAAE,CAAC;MACZE,UAAU,EAAE,CAAC;MACbC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE,CAAC;MACZ2B,iBAAiB,EAAE,CAAC;MACpBC,kBAAkB,EAAE,CAAC;MACrBC,gBAAgB,EAAE,CAAC;MACnBC,iBAAiB,EAAE,CAAC;MACpB7B,MAAM,EAAE;IACZ,CAAC;IACD,MAAMwC,wBAAwB,GAAItC,IAAS,IAAK;MAAA,IAAAuC,eAAA,EAAAC,gBAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,eAAA,EAAAC,gBAAA;MAC5C,OAAO;QACH,GAAGb,WAAW;QACdxC,SAAS,EAAE,EAAA8C,eAAA,GAAAvC,IAAI,CAACP,SAAS,cAAA8C,eAAA,uBAAdA,eAAA,CAAgBhD,KAAK,KAAI,CAAC;QACrCI,UAAU,EAAE,EAAA6C,gBAAA,GAAAxC,IAAI,CAACL,UAAU,cAAA6C,gBAAA,uBAAfA,gBAAA,CAAiBjD,KAAK,KAAI,CAAC;QACvCK,QAAQ,EAAE,EAAA6C,cAAA,GAAAzC,IAAI,CAACJ,QAAQ,cAAA6C,cAAA,uBAAbA,cAAA,CAAelD,KAAK,KAAI,CAAC;QACnCM,SAAS,EAAE,EAAA6C,eAAA,GAAA1C,IAAI,CAACH,SAAS,cAAA6C,eAAA,uBAAdA,eAAA,CAAgBnD,KAAK,KAAI,CAAC;QACrCiC,iBAAiB,EAAE,EAAAmB,gBAAA,GAAA3C,IAAI,CAACP,SAAS,cAAAkD,gBAAA,uBAAdA,gBAAA,CAAgBpD,KAAK,IAAGgC,YAAY;QACvDE,kBAAkB,EAAE,EAAAmB,iBAAA,GAAA5C,IAAI,CAACL,UAAU,cAAAiD,iBAAA,uBAAfA,iBAAA,CAAiBrD,KAAK,IAAGgC,YAAY;QACzDG,gBAAgB,EAAE,EAAAmB,eAAA,GAAA7C,IAAI,CAACJ,QAAQ,cAAAiD,eAAA,uBAAbA,eAAA,CAAetD,KAAK,IAAGgC,YAAY;QACrDI,iBAAiB,EAAE,EAAAmB,gBAAA,GAAA9C,IAAI,CAACH,SAAS,cAAAiD,gBAAA,uBAAdA,gBAAA,CAAgBvD,KAAK,IAAGgC,YAAY;QACvDzB,MAAM,EAAEE,IAAI,CAACF,MAAM,IAAI;MAC3B,CAAC;IACL,CAAC;;IAED;IACA,IAAIS,OAAO,CAACQ,YAAY,KAAK7H,yBAAyB,CAAC6J,EAAE,IAAIxC,OAAO,CAACK,YAAY,KAAKzH,WAAW,CAAC6J,GAAG,EAAE;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACnG,IACIC,KAAK,CAACC,OAAO,CAACnB,iBAAiB,CAAC,KAC/BA,iBAAiB,CAACoB,MAAM,KAAK,CAAC,IAC1B,EAAAN,qBAAA,GAAAd,iBAAiB,CAAC,CAAC,CAAC,CAAC1C,SAAS,cAAAwD,qBAAA,uBAA9BA,qBAAA,CAAgC1D,KAAK,MAAK,CAAC,IACxC,EAAA2D,sBAAA,GAAAf,iBAAiB,CAAC,CAAC,CAAC,CAACxC,UAAU,cAAAuD,sBAAA,uBAA/BA,sBAAA,CAAiC3D,KAAK,MAAK,CAAC,IAC5C,EAAA4D,sBAAA,GAAAhB,iBAAiB,CAAC,CAAC,CAAC,CAACvC,QAAQ,cAAAuD,sBAAA,uBAA7BA,sBAAA,CAA+B5D,KAAK,MAAK,CAAC,IAC1C,EAAA6D,sBAAA,GAAAjB,iBAAiB,CAAC,CAAC,CAAC,CAACtC,SAAS,cAAAuD,sBAAA,uBAA9BA,sBAAA,CAAgC7D,KAAK,MAAK,CAAE,CAAC,EACvD;QACE;QACA,IAAI3D,KAAK,IAAI,CAACA,KAAK,GAAG,EAAEyF,WAAW,GAAGA,WAAW,GAAGd,OAAO,CAACc,WAAW,CAAC,EAAE;UAAA,IAAAmC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA;UACtE,IAAIC,IAAI,GAAGzJ,0BAA0B,CAAC4H,gBAAgB,EAAE,CAACnG,KAAK,GAAG,EAAEyF,WAAW,GAAGA,WAAW,GAAGd,OAAO,CAACc,WAAW,CAAC,CAAC;UAEpH/D,eAAe,CACXsG,IAAI,IAAIA,IAAI,CAACL,MAAM,GAAG,CAAC,GACjB;YACI,GAAGtB,WAAW;YACdxC,SAAS,GAAA+D,iBAAA,GAAEI,IAAI,CAAC,CAAC,CAAC,CAACnE,SAAS,cAAA+D,iBAAA,uBAAjBA,iBAAA,CAAmBjE,KAAK;YACnCI,UAAU,GAAA8D,kBAAA,GAAEG,IAAI,CAAC,CAAC,CAAC,CAACnE,SAAS,cAAAgE,kBAAA,uBAAjBA,kBAAA,CAAmBlE,KAAK;YACpCK,QAAQ,GAAA8D,kBAAA,GAAEE,IAAI,CAAC,CAAC,CAAC,CAACnE,SAAS,cAAAiE,kBAAA,uBAAjBA,kBAAA,CAAmBnE,KAAK;YAClCM,SAAS,GAAA8D,kBAAA,GAAEC,IAAI,CAAC,CAAC,CAAC,CAACnE,SAAS,cAAAkE,kBAAA,uBAAjBA,kBAAA,CAAmBpE;UAClC,CAAC,GACD8C,wBACV,CAAC;QACL;;QAEA;QACA,IAAIzG,KAAK,IAAI,CAACA,KAAK,KAAK,EAAEyF,WAAW,GAAGA,WAAW,GAAGd,OAAO,CAACc,WAAW,CAAC,EAAE;UAAA,IAAAwC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA;UACxE,IAAIC,IAAI,GAAG9J,0BAA0B,CAAC6H,oBAAoB,EAAE,EAAE,CAAC;UAE/D1E,eAAe,CACX2G,IAAI,IAAIA,IAAI,CAACV,MAAM,GAAG,CAAC,GACjB;YACI,GAAGtB,WAAW;YACdxC,SAAS,GAAAoE,iBAAA,GAAEI,IAAI,CAAC,CAAC,CAAC,CAACxE,SAAS,cAAAoE,iBAAA,uBAAjBA,iBAAA,CAAmBtE,KAAK;YACnCI,UAAU,GAAAmE,kBAAA,GAAEG,IAAI,CAAC,CAAC,CAAC,CAACxE,SAAS,cAAAqE,kBAAA,uBAAjBA,kBAAA,CAAmBvE,KAAK;YACpCK,QAAQ,GAAAmE,kBAAA,GAAEE,IAAI,CAAC,CAAC,CAAC,CAACxE,SAAS,cAAAsE,kBAAA,uBAAjBA,kBAAA,CAAmBxE,KAAK;YAClCM,SAAS,GAAAmE,kBAAA,GAAEC,IAAI,CAAC,CAAC,CAAC,CAACxE,SAAS,cAAAuE,kBAAA,uBAAjBA,kBAAA,CAAmBzE,KAAK;YACnCO,MAAM,EAAE;UACZ,CAAC,GACDuC,wBACV,CAAC;QACL;;QAEA;QACA,IAAIzG,KAAK,IAAI,CAACA,KAAK,GAAG,EAAEyF,WAAW,GAAGA,WAAW,GAAGd,OAAO,CAACc,WAAW,CAAC,EAAE;UAAA,IAAA6C,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA;UACtE,MAAMC,KAAK,GAAG,EAAEjD,WAAW,GAAGA,WAAW,GAAGd,OAAO,CAACc,WAAW,CAAC,GAAG,CAACzF,KAAK;UACzE,MAAM2I,UAAU,GAAG,EAAE,GAAGD,KAAK;UAC7B,IAAIE,IAAI,GAAGrK,0BAA0B,CAAC6H,oBAAoB,EAAEuC,UAAU,CAAC;UAEvEjH,eAAe,CACXkH,IAAI,IAAIA,IAAI,CAACjB,MAAM,GAAG,CAAC,GACjB;YACI,GAAGtB,WAAW;YACdxC,SAAS,GAAAyE,iBAAA,GAAEM,IAAI,CAAC,CAAC,CAAC,CAAC/E,SAAS,cAAAyE,iBAAA,uBAAjBA,iBAAA,CAAmB3E,KAAK;YACnCI,UAAU,GAAAwE,kBAAA,GAAEK,IAAI,CAAC,CAAC,CAAC,CAAC/E,SAAS,cAAA0E,kBAAA,uBAAjBA,kBAAA,CAAmB5E,KAAK;YACpCK,QAAQ,GAAAwE,kBAAA,GAAEI,IAAI,CAAC,CAAC,CAAC,CAAC/E,SAAS,cAAA2E,kBAAA,uBAAjBA,kBAAA,CAAmB7E,KAAK;YAClCM,SAAS,GAAAwE,kBAAA,GAAEG,IAAI,CAAC,CAAC,CAAC,CAAC/E,SAAS,cAAA4E,kBAAA,uBAAjBA,kBAAA,CAAmB9E,KAAK;YACnCO,MAAM,EAAE;UACZ,CAAC,GACDuC,wBACV,CAAC;QACL;MACJ,CAAC,MAAM;QACHF,iBAAiB,IAAIA,iBAAiB,CAACoB,MAAM,GAAG,CAAC,IAAIjG,eAAe,CAACgF,wBAAwB,CAACH,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;MACxH;IACJ,CAAC,MAAM;MACH,IAAIsC,IAAI,GAAGtK,0BAA0B,CAAC4H,gBAAgB,EAAEnG,KAAM,CAAC;MAC/D0B,eAAe,CAACmH,IAAI,IAAIA,IAAI,CAAClB,MAAM,GAAG,CAAC,GAAGjB,wBAAwB,CAACmC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAGpC,wBAAwB,CAAC;IAC3G;EACJ,CAAC;EAED,MAAMqC,wBAAwB,GAAG,MAAO9I,KAAa,IAAK;IACtD,MAAMC,MAAM,GAAG;MAAED,KAAK;MAAEF,IAAI,EAAEkB,UAAU,CAAClB;IAAK,CAAC;IAC/C,MAAM4C,QAA6D,GAAG,MAAMvE,WAAW,CACnFd,GAAG,CAACsF,iBAAiB,CAACoG,yBAAyB,EAC/C9I,MACJ,CAAC;IACD,IAAIyC,QAAQ,EAAE;MACV,MAAM;QAAEG,MAAM;QAAEC;MAAO,CAAC,GAAGJ,QAAQ;MAEnC,IAAIG,MAAM,EAAE;QACR,MAAMW,IAAI,GAAGV,MAAM,CAACC,OAAyB;QAC7C,MAAMiG,WAAW,GAAGvK,OAAO,CAAC+E,IAAI,CAAC;QACjC5B,wBAAwB,CAAC;UACrB,GAAGD,qBAAqB;UACxB3B,KAAK;UACL2D,KAAK,EAAE,CAACqF,WAAW,GAAGxF,IAAI,CAACG,KAAK,GAAG;QACvC,CAAC,CAAC;MACN;IACJ;EACJ,CAAC;EAED,MAAMX,YAAY,GAAGA,CAAA,KAAM;IACvBjC,mCAAmC,CAAChC,uCAAuC,CAAC;IAC5EwB,UAAU,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAM0I,eAAe,GAAGnM,WAAW,CAAC,MAAOoM,CAAS,IAAK;IACrD,MAAMC,YAAY,GAAG,MAAMtK,eAAe,CAACqK,CAAC,CAAC;IAC7C,OAAOC,YAAY;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,gBAAgB,GAAIC,CAAoE,IAAK;IAC/F,MAAM1F,KAAK,GAAG0F,CAAC,CAACC,MAAM,CAAC3F,KAAK;IAC5BrC,OAAO,CAACqC,KAAe,CAAC;IACxBnC,eAAe,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM+H,oBAAoB,GAAGA,CAAA,KAAM;IAC/BjL,cAAc,CAACjB,GAAG,CAACsF,iBAAiB,CAAC6G,WAAW,CAACC,GAAG,EAAE;MAAE3J,IAAI,EAAEkB,UAAU,CAAClB,IAAI;MAAE4J,QAAQ,EAAErJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqJ,QAAQ;MAAEtJ,QAAQ,EAAEF;IAAO,CAAC,CAAC;EACpI,CAAC;EAED,MAAMyJ,gBAAgB,GAAG,MAAOzG,WAAmB,IAAK;IACpDD,eAAe,CAACC,WAAW,CAAC;IAC5BvC,OAAO,CAAC,IAAI,CAAC;EACjB,CAAC;EAED,MAAMiJ,iBAAiB,GAAGA,CAAA,KAAM;IAC5BjJ,OAAO,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,MAAMkJ,6BAA6B,GAAIzF,IAAsB,IAAK;IAC9DxC,wBAAwB,CAAC;MACrB9B,IAAI,EAAEkB,UAAU,CAAClB,IAAI;MACrBE,KAAK,EAAEgB,UAAU,CAAChB,KAAK,GAAGgB,UAAU,CAAChB,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG;MACnD2D,KAAK,EAAES,IAAI,CAAC,CAAC,CAAC,CAACT;IACnB,CAAC,CAAC;IACF9C,SAAS,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMiJ,8BAA8B,GAAGA,CAAA,KAAM;IACzCjJ,SAAS,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMkJ,yBAAyB,GAAG,MAAOpF,OAAmD,IAAK;IAC7FlE,mBAAmB,CAAC,IAAI,CAAC;IACzB,MAAMiC,QAAuD,GAAG,MAAMvE,WAAW,CAC7Ed,GAAG,CAACsF,iBAAiB,CAACqH,kBAAkB,EACxCrF,OACJ,CAAC;IACD,IAAIjC,QAAQ,EAAE;MAAA,IAAAuH,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACV,MAAM;QAAEtH;MAAO,CAAC,GAAGH,QAAQ;MAC3BjC,mBAAmB,CAAC,KAAK,CAAC;MAC1BvD,KAAK,CAACkN,QAAQ,CACVhM,YAAY,CAAC;QACTsC,IAAI,EAAE,IAAI;QACV2J,OAAO,EAAExH,MAAM,GAAG,gBAAgB,GAAG,oBAAoB;QACzDyH,OAAO,EAAE,OAAO;QAChBC,KAAK,EAAE;UAAEC,KAAK,EAAE3H,MAAM,GAAG,SAAS,GAAG;QAAU;MACnD,CAAC,CACL,CAAC;MACDI,eAAe,EAAAgH,gBAAA,GAACvH,QAAQ,CAACI,MAAM,cAAAmH,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBlH,OAAO,cAAAmH,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BG,OAAO,cAAAF,sBAAA,uBAAjCA,sBAAA,CAAmCjH,WAAW,EAAEyB,OAAO,CAAC3E,KAAK,EAAE2E,OAAO,CAACF,kBAAkB,CAAC;MAC1GhC,YAAY,CAAC,CAAC;IAClB,CAAC,MAAM;MACHhC,mBAAmB,CAAC,KAAK,CAAC;IAC9B;EACJ,CAAC;EAED,MAAMgK,iBAAiB,GAAG,MAAO9F,OAAuC,IAAK;IACzElE,mBAAmB,CAAC,IAAI,CAAC;IACzB,MAAMiC,QAAuD,GAAG,MAAMvE,WAAW,CAC7Ed,GAAG,CAACsF,iBAAiB,CAAC+H,mBAAmB,EACzC/F,OACJ,CAAC;IACD,IAAIjC,QAAQ,EAAE;MACV,MAAM;QAAEG;MAAO,CAAC,GAAGH,QAAQ;MAC3B,IAAIG,MAAM,EAAE;QACRpC,mBAAmB,CAAC,KAAK,CAAC;QAC1BqJ,8BAA8B,CAAC,CAAC;QAChC5M,KAAK,CAACkN,QAAQ,CAAChM,YAAY,CAAC;UAAEsC,IAAI,EAAE,IAAI;UAAE2J,OAAO,EAAE,gBAAgB;UAAEC,OAAO,EAAE,OAAO;UAAEC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE,CAAC,CAAC,CAAC;QACtH/H,YAAY,CAAC,CAAC;MAClB,CAAC,MAAM;QACHhC,mBAAmB,CAAC,KAAK,CAAC;MAC9B;IACJ,CAAC,MAAM;MACHA,mBAAmB,CAAC,KAAK,CAAC;IAC9B;EACJ,CAAC;EAED,MAAMkK,eAAe,GAAG,MAAOhG,OAAsB,IAAK;IACtD,MAAMjC,QAA6C,GAAG,MAAMvE,WAAW,CAACd,GAAG,CAACsF,iBAAiB,CAACiI,WAAW,EAAE;MACvG,GAAGjG,OAAO;MACVvD,MAAM,EAAEJ,UAAU,CAAChB,KAAK;MACxBF,IAAI,EAAEkB,UAAU,CAAClB;IACrB,CAAC,CAAC;IAEF,IAAI4C,QAAQ,EAAE;MACV,MAAMI,MAAM,GAAGJ,QAAQ,CAACI,MAAM,CAACC,OAAO;MACtC,MAAM8H,cAAc,GAAG/J,gCAAgC,CAACgK,WAAW,CAACC,GAAG,CAAEC,UAAkC,KAAM;QAC7G,GAAGA,UAAU;QACbxH,IAAI,EAAEwH,UAAU,CAACxH,IAAI,CAACuH,GAAG,CAAEvE,EAA8B,IAAMA,EAAE,CAACtD,WAAW,KAAKJ,MAAM,CAACI,WAAW,GAAGJ,MAAM,GAAG0D,EAAG;MACvH,CAAC,CAAC,CAAC;MACHzF,mCAAmC,CAAC;QAAE,GAAGD,gCAAgC;QAAEgK,WAAW,EAAED;MAAe,CAAC,CAAC;MACzG3N,KAAK,CAACkN,QAAQ,CAAChM,YAAY,CAAC;QAAEsC,IAAI,EAAE,IAAI;QAAE2J,OAAO,EAAE,gBAAgB;QAAEC,OAAO,EAAE,OAAO;QAAEC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAAE,CAAC,CAAC,CAAC;MACtHtI,gBAAgB,CAAC,KAAK,CAAC;IAC3B;EACJ,CAAC;EAED,MAAM+I,iBAAiB,GAAGA,CAACC,KAAgC,EAAE9G,IAAkB,KAAK;IAChFtC,WAAW,CAACoJ,KAAK,CAACC,aAAa,CAAC;IAChCnJ,cAAc,CAACoC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMgH,WAAW,GAAGA,CAAA,KAAM;IACtBtJ,WAAW,CAAC,IAAI,CAAC;IACjBI,gBAAgB,CAAC,KAAK,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMmJ,YAAY,GAAI1H,KAAgD,IAAK;IACvEjF,eAAe,CAACiF,KAAK,CAAC;IACtB/D,eAAe,CAAC+D,KAAY,CAAC;IAC7B1C,aAAa,CAAC0C,KAAK,CAAC;EACxB,CAAC;EAED,MAAM2H,sBAAsB,GAAIC,gBAA4D,IAAK;IAC7FxB,yBAAyB,CAACwB,gBAAgB,CAAC;EAC/C,CAAC;EAED,MAAMC,mBAAmB,GAAIC,aAA6C,IAAK;IAC3EhB,iBAAiB,CAACgB,aAAa,CAAC;EACpC,CAAC;EAED,MAAMC,iBAAiB,GAAI5H,OAAsB,IAAK;IAClD6G,eAAe,CAAC7G,OAAO,CAAC;EAC5B,CAAC;EACD,MAAMsG,QAAQ,GAAGlL,cAAc,CAAC,CAAC;EACjC,MAAMyM,qBAAqB,GAAG,MAAOC,SAAuB,IAAK;IAC7D,MAAMC,GAAG,GAAG,MAAM1N,WAAW,CAACd,GAAG,CAACyO,eAAe,CAACC,eAAe,EAAEH,SAAS,CAAC;IAC7ExB,QAAQ,CACJhM,YAAY,CAAC;MACTsC,IAAI,EAAE,IAAI;MACV2J,OAAO,EAAEwB,GAAG,CAAChJ,MAAM,GAAG,gBAAgB,GAAG,aAAa;MACtDyH,OAAO,EAAE,OAAO;MAChBC,KAAK,EAAE;QAAEC,KAAK,EAAEqB,GAAG,CAAChJ,MAAM,GAAG,SAAS,GAAG;MAAQ;IACrD,CAAC,CACL,CAAC;IACD,IAAIgJ,GAAG,CAAChJ,MAAM,EAAE;MACZL,WAAW,CAAC,KAAK,CAAC;MAClBC,YAAY,CAAC,CAAC;IAClB;EACJ,CAAC;EACD;EACA1F,SAAS,CAAC,MAAM;IACZ0F,YAAY,CAAC,CAAC;EAClB,CAAC,EAAE,CAACzB,UAAU,CAAC,CAAC;EAEhBjE,SAAS,CAAC,MAAM;IACZkM,eAAe,CAACnJ,IAAI,CAAC,CAACkM,IAAI,CAAEC,KAAgB,IAAK;MAC7C5K,SAAS,CAAC4K,KAAK,CAAC;MAChB,IAAIA,KAAK,CAACtE,MAAM,GAAG,CAAC,IAAIpG,YAAY,EAAEJ,YAAY,CAAC;QAAE,GAAGD,SAAS;QAAEpB,IAAI;QAAEE,KAAK,EAAE;MAAG,CAAC,CAAC;IACzF,CAAC,CAAC;EACN,CAAC,EAAE,CAACF,IAAI,CAAC,CAAC;EAEV,oBACIR,OAAA,CAAAE,SAAA;IAAA0M,QAAA,gBAEI5M,OAAA,CAACpB,cAAc;MACXiO,YAAY,EAAE9N,sBAAsB,CAAC8D,sCAAsC,CAACiK,QAAQ,CAAC,GAAG7C,oBAAoB,GAAG8C,SAAU;MACzHC,aAAa,EAAE7O,kBAAkB,CAAC8O,WAAW,CAACC,4BAA4B,GAAG,kBAAmB;MAAAN,QAAA,eAEhG5M,OAAA,CAACvB,kCAAkC;QAC/BiD,UAAU,EAAEE,SAAU;QACtBE,MAAM,EAAEA,MAAO;QACfgI,gBAAgB,EAAEA,gBAAiB;QACnCiC,YAAY,EAAEA;MAAa;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC,eAGjBtN,OAAA,CAACnC,QAAQ;MAAA+O,QAAA,eACL5M,OAAA,CAAClC,KAAK;QACFyP,KAAK,eACDvN,OAAA,CAACrB,iCAAiC;UAC9BmD,MAAM,EAAEN,gCAAgC,CAACM,MAAO;UAChDsH,KAAK,EAAE5H,gCAAgC,CAACgK,WAAW,CAACnD;QAAO;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CACJ;QACDE,SAAS,EAAExM,OAAQ;QACnBkD,IAAI,EAAE1C,gCAAgC,CAACgK,WAAY;QAAAoB,QAAA,eAEnD5M,OAAA,CAACtB,iCAAiC;UAC9B+O,IAAI,EAAEjM,gCAAiC;UACvCkM,UAAU,EAAErD,gBAAiB;UAC7BsB,iBAAiB,EAAEA,iBAAkB;UACrCgC,uBAAuB,EAAEpD,6BAA8B;UACvDqD,WAAW,EAAExB,iBAAkB;UAC/BlJ,WAAW,EAAEA,WAAY;UACzBD,QAAQ,EAAEA,QAAS;UACnB4K,iBAAiB,EAAExB;QAAsB;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGVlM,IAAI,iBACDpB,OAAA,CAAC1B,8BAA8B;MAC3B8C,IAAI,EAAEA,IAAK;MACXJ,OAAO,EAAEE,gBAAiB;MAC1BY,MAAM,EAAEA,MAAO;MACfgK,WAAW,EAAExB,iBAAkB;MAC/BnI,YAAY,EAAEA,YAAa;MAC3B2L,gBAAgB,EAAE9B,sBAAuB;MACzCrF,iCAAiC,EAAEA,iCAAkC;MACrE5D,WAAW,EAAEA;IAAY;MAAAoK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CACJ,EAGAhM,MAAM,iBACHtB,OAAA,CAACxB,aAAa;MACV4C,IAAI,EAAEE,MAAO;MACbN,OAAO,EAAEE,gBAAiB;MAC1B6M,SAAS,EAAE1L,qBAAsB;MACjCP,MAAM,EAAEA,MAAO;MACfgK,WAAW,EAAEtB,8BAA+B;MAC5CwD,gBAAgB,EAAExE,wBAAyB;MAC3CyE,aAAa,EAAE/B;IAAoB;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACJ,eAGDtN,OAAA,CAACzB,cAAc;MACXuG,IAAI,EAAErC,WAAa;MACnBF,QAAQ,EAAEA,QAAS;MACnBuJ,WAAW,EAAEA,WAAY;MACzBoC,MAAM,EAAEvL,aAAc;MACtBwL,SAAS,EAAEvL,gBAAiB;MAC5BgL,WAAW,EAAExB;IAAkB;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAAA,eACJ,CAAC;AAEX,CAAC;AAAClN,EAAA,CAzhBID,4BAA4B;EAAA,QAEUxC,eAAe,EAKpCmC,SAAS,EAIPD,cAAc,EAoalBD,cAAc;AAAA;AAAAwO,EAAA,GA/a7BjO,4BAA4B;AA2hBlC,eAAeA,4BAA4B;AAAC,IAAAiO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}