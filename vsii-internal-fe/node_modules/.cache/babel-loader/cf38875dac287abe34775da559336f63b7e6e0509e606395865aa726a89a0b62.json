{"ast": null, "code": "import{useEffect,useState}from'react';import FiberManualRecordIcon from'@mui/icons-material/FiberManualRecord';import EditTwoToneIcon from'@mui/icons-material/EditTwoTone';import{Typography,Grid,IconButton}from'@mui/material';import{FormattedMessage,useIntl}from'react-intl';import{useTheme}from'@mui/material/styles';import{useMediaQuery}from'@mui/system';import Chart from'react-apexcharts';import{effortPlanUpdateStatusColors}from'pages/monthly-effort/Config';import SkeletonSummaryCard from'components/cards/Skeleton/SummaryCard';import{checkAllowedPermission}from'utils/authorization';import{PERMISSIONS}from'constants/Permission';import UpdateEffortPlan from'./UpdateEffortPlan';import MainCard from'components/cards/MainCard';import{gridSpacing}from'store/constant';import{TEXT_CONFIG_SCREEN}from'constants/Common';// =========================|| EFFORT PLAN UPDATE STATUS CHART CARD ||========================= //\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const EffortPlanUpdateStatusCard=_ref=>{let{isLoading,data,conditions,fetchData}=_ref;const[filterDepartment,setFilterDepartment]=useState([]);const[open,setOpen]=useState(false);const theme=useTheme();const intl=useIntl();const{monthlyEffort}=PERMISSIONS.report;const{Summary}=TEXT_CONFIG_SCREEN.monthlyEffort;const handleOpenDialog=()=>{setOpen(true);};const isLargScreen=useMediaQuery(theme.breakpoints.between('xs','xl'));const handleCloseDialog=()=>{setOpen(false);};const handleChangeFilterDepartment=departments=>{setFilterDepartment(departments);};useEffect(()=>{setFilterDepartment(data===null||data===void 0?void 0:data.filter(item=>item.show));},[data]);return isLoading?/*#__PURE__*/_jsx(SkeletonSummaryCard,{}):/*#__PURE__*/_jsxs(MainCard,{sx:{marginBottom:theme.spacing(gridSpacing),overflow:'unset',height:'100%'},contentSX:{height:'100%',justifyContent:'center',alignItem:'center',display:'flex'},title:/*#__PURE__*/_jsx(FormattedMessage,{id:Summary+'update-effort-plan'}),secondary:checkAllowedPermission(monthlyEffort.editEffortPlan)&&/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleOpenDialog,children:/*#__PURE__*/_jsx(EditTwoToneIcon,{fontSize:\"small\"})}),children:[/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,sx:{marginTop:theme.spacing(gridSpacing)},children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:12,sx:{alignItems:'center'},children:/*#__PURE__*/_jsx(Chart,{options:{colors:effortPlanUpdateStatusColors.map(x=>x.color||'#ffffff'),chart:{type:'bar',stacked:true,stackType:'100%',toolbar:{show:false}},legend:{show:false},xaxis:{categories:filterDepartment.map(item=>item.department),labels:{style:{fontWeight:600}}},dataLabels:{formatter:function(value){return value.toFixed(1)+'%';}},yaxis:{tickAmount:5,labels:{formatter:value=>{return\"\".concat(value);}}}},series:effortPlanUpdateStatusColors.map(cate=>({name:\"\".concat(intl.formatMessage({id:cate.name}),\" \").concat(conditions.month),data:filterDepartment.map(item=>cate.name==='send-report'?item.sentReport:item.notSentReport)})),type:\"bar\",height:isLargScreen?240:''})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:12,children:/*#__PURE__*/_jsx(Grid,{container:true,alignItems:\"left\",flexDirection:\"column-reverse\",children:effortPlanUpdateStatusColors.map((item,index)=>/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:12,children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,alignItems:\"center\",justifyContent:\"center\",children:[/*#__PURE__*/_jsx(Grid,{item:true,children:/*#__PURE__*/_jsx(FiberManualRecordIcon,{sx:{color:item.color||'#ffffff'}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:true,zeroMinWidth:true,children:/*#__PURE__*/_jsx(Grid,{container:true,spacing:1,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:true,zeroMinWidth:true,children:/*#__PURE__*/_jsxs(Typography,{align:\"left\",variant:\"body2\",children:[/*#__PURE__*/_jsx(FormattedMessage,{id:\"\".concat(Summary).concat(item.name)}),\" \",conditions.month]})})})})]})},index))})})]}),open?/*#__PURE__*/_jsx(UpdateEffortPlan,{open:open,data:data,handleClose:handleCloseDialog,filterDepartment:filterDepartment,handleChangeFilterDepartment:handleChangeFilterDepartment,fetchData:fetchData}):null]});};export default EffortPlanUpdateStatusCard;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}