{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/UserSearch.tsx\";\nimport { FormattedMessage } from 'react-intl';\n\n// material-ui\nimport { Grid } from '@mui/material';\n\n// project imports\nimport { Button } from 'components';\nimport { Label } from 'components/extended/Form';\nimport { userFilterConfig, userFilterSchema } from 'pages/administration/Config';\nimport { SearchForm, MemberCode, Department, Status, Username, Contractor } from '../search';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserSearch = props => {\n  const {\n    formReset,\n    handleSearch\n  } = props;\n  const {\n    manage_user\n  } = TEXT_CONFIG_SCREEN.administration;\n  return /*#__PURE__*/_jsxDEV(SearchForm, {\n    defaultValues: userFilterConfig,\n    formSchema: userFilterSchema,\n    handleSubmit: handleSearch,\n    formReset: formReset,\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      alignItems: \"center\",\n      spacing: 2,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2,\n        children: /*#__PURE__*/_jsxDEV(MemberCode, {\n          label: manage_user + 'member-code'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2,\n        children: /*#__PURE__*/_jsxDEV(Username, {\n          label: manage_user + 'username'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2,\n        children: /*#__PURE__*/_jsxDEV(Department, {\n          label: manage_user + 'dept'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2,\n        children: /*#__PURE__*/_jsxDEV(Contractor, {\n          label: manage_user + 'contractor'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2,\n        children: /*#__PURE__*/_jsxDEV(Status, {\n          isShowAll: false,\n          label: manage_user + 'status'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2,\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          label: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          size: \"medium\",\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: manage_user + 'search'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 67\n          }, this),\n          variant: \"contained\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 9\n  }, this);\n};\n_c = UserSearch;\nexport default UserSearch;\nvar _c;\n$RefreshReg$(_c, \"UserSearch\");", "map": {"version": 3, "names": ["FormattedMessage", "Grid", "<PERSON><PERSON>", "Label", "userFilterConfig", "userFilterSchema", "SearchForm", "MemberCode", "Department", "Status", "Username", "Contractor", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "UserSearch", "props", "formReset", "handleSearch", "manage_user", "administration", "defaultValues", "formSchema", "handleSubmit", "children", "container", "alignItems", "spacing", "item", "xs", "lg", "label", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isShowAll", "type", "size", "id", "variant", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/UserSearch.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\n\n// material-ui\nimport { Grid } from '@mui/material';\n\n// project imports\nimport { Button } from 'components';\nimport { Label } from 'components/extended/Form';\nimport { IUserFilterConfig, userFilterConfig, userFilterSchema } from 'pages/administration/Config';\nimport { SearchForm, MemberCode, Department, Status, Username, Contractor } from '../search';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\n\ninterface IUserSearchProps {\n    formReset: IUserFilterConfig;\n    handleSearch: (value: any) => void;\n}\n\nconst UserSearch = (props: IUserSearchProps) => {\n    const { formReset, handleSearch } = props;\n\n    const { manage_user } = TEXT_CONFIG_SCREEN.administration;\n\n    return (\n        <SearchForm defaultValues={userFilterConfig} formSchema={userFilterSchema} handleSubmit={handleSearch} formReset={formReset}>\n            <Grid container alignItems=\"center\" spacing={2}>\n                <Grid item xs={12} lg={2}>\n                    <MemberCode label={manage_user + 'member-code'} />\n                </Grid>\n                <Grid item xs={12} lg={2}>\n                    <Username label={manage_user + 'username'} />\n                </Grid>\n                <Grid item xs={12} lg={2}>\n                    <Department label={manage_user + 'dept'} />\n                </Grid>\n                <Grid item xs={12} lg={2}>\n                    <Contractor label={manage_user + 'contractor'} />\n                </Grid>\n                <Grid item xs={12} lg={2}>\n                    <Status isShowAll={false} label={manage_user + 'status'} />\n                </Grid>\n                <Grid item xs={12} lg={2}>\n                    <Label label=\"&nbsp;\" />\n                    <Button type=\"submit\" size=\"medium\" children={<FormattedMessage id={manage_user + 'search'} />} variant=\"contained\" />\n                </Grid>\n            </Grid>\n        </SearchForm>\n    );\n};\n\nexport default UserSearch;\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,IAAI,QAAQ,eAAe;;AAEpC;AACA,SAASC,MAAM,QAAQ,YAAY;AACnC,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAA4BC,gBAAgB,EAAEC,gBAAgB,QAAQ,6BAA6B;AACnG,SAASC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,WAAW;AAC5F,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOtD,MAAMC,UAAU,GAAIC,KAAuB,IAAK;EAC5C,MAAM;IAAEC,SAAS;IAAEC;EAAa,CAAC,GAAGF,KAAK;EAEzC,MAAM;IAAEG;EAAY,CAAC,GAAGP,kBAAkB,CAACQ,cAAc;EAEzD,oBACIN,OAAA,CAACR,UAAU;IAACe,aAAa,EAAEjB,gBAAiB;IAACkB,UAAU,EAAEjB,gBAAiB;IAACkB,YAAY,EAAEL,YAAa;IAACD,SAAS,EAAEA,SAAU;IAAAO,QAAA,eACxHV,OAAA,CAACb,IAAI;MAACwB,SAAS;MAACC,UAAU,EAAC,QAAQ;MAACC,OAAO,EAAE,CAAE;MAAAH,QAAA,gBAC3CV,OAAA,CAACb,IAAI;QAAC2B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,eACrBV,OAAA,CAACP,UAAU;UAACwB,KAAK,EAAEZ,WAAW,GAAG;QAAc;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACPrB,OAAA,CAACb,IAAI;QAAC2B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,eACrBV,OAAA,CAACJ,QAAQ;UAACqB,KAAK,EAAEZ,WAAW,GAAG;QAAW;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACPrB,OAAA,CAACb,IAAI;QAAC2B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,eACrBV,OAAA,CAACN,UAAU;UAACuB,KAAK,EAAEZ,WAAW,GAAG;QAAO;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,eACPrB,OAAA,CAACb,IAAI;QAAC2B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,eACrBV,OAAA,CAACH,UAAU;UAACoB,KAAK,EAAEZ,WAAW,GAAG;QAAa;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACPrB,OAAA,CAACb,IAAI;QAAC2B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,eACrBV,OAAA,CAACL,MAAM;UAAC2B,SAAS,EAAE,KAAM;UAACL,KAAK,EAAEZ,WAAW,GAAG;QAAS;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACPrB,OAAA,CAACb,IAAI;QAAC2B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,gBACrBV,OAAA,CAACX,KAAK;UAAC4B,KAAK,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxBrB,OAAA,CAACZ,MAAM;UAACmC,IAAI,EAAC,QAAQ;UAACC,IAAI,EAAC,QAAQ;UAACd,QAAQ,eAAEV,OAAA,CAACd,gBAAgB;YAACuC,EAAE,EAAEpB,WAAW,GAAG;UAAS;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACK,OAAO,EAAC;QAAW;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAErB,CAAC;AAACM,EAAA,GA9BI1B,UAAU;AAgChB,eAAeA,UAAU;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}