{"ast": null, "code": "import * as React from 'react';\nimport { useValidation } from './useValidation';\nimport { useLocalizationContext } from '../useUtils';\nimport { parseNonNullablePickerDate } from '../../utils/date-utils';\nexport const validateDate = _ref => {\n  let {\n    props,\n    value,\n    adapter\n  } = _ref;\n  const now = adapter.utils.date();\n  const date = adapter.utils.date(value);\n  const minDate = parseNonNullablePickerDate(adapter.utils, props.minDate, adapter.defaultDates.minDate);\n  const maxDate = parseNonNullablePickerDate(adapter.utils, props.maxDate, adapter.defaultDates.maxDate);\n  if (date === null) {\n    return null;\n  }\n  switch (true) {\n    case !adapter.utils.isValid(value):\n      return 'invalidDate';\n    case Boolean(props.shouldDisableDate && props.shouldDisableDate(date)):\n      return 'shouldDisableDate';\n    case Boolean(props.disableFuture && adapter.utils.isAfterDay(date, now)):\n      return 'disableFuture';\n    case Boolean(props.disablePast && adapter.utils.isBeforeDay(date, now)):\n      return 'disablePast';\n    case Boolean(minDate && adapter.utils.isBeforeDay(date, minDate)):\n      return 'minDate';\n    case Boolean(maxDate && adapter.utils.isAfterDay(date, maxDate)):\n      return 'maxDate';\n    default:\n      return null;\n  }\n};\nexport const useIsDayDisabled = _ref2 => {\n  let {\n    shouldDisableDate,\n    minDate,\n    maxDate,\n    disableFuture,\n    disablePast\n  } = _ref2;\n  const adapter = useLocalizationContext();\n  return React.useCallback(day => validateDate({\n    adapter,\n    value: day,\n    props: {\n      shouldDisableDate,\n      minDate,\n      maxDate,\n      disableFuture,\n      disablePast\n    }\n  }) !== null, [adapter, shouldDisableDate, minDate, maxDate, disableFuture, disablePast]);\n};\nexport const isSameDateError = (a, b) => a === b;\nexport const useDateValidation = props => useValidation(props, validateDate, isSameDateError);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}