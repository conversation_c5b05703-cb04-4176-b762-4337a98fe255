{"ast": null, "code": "import { cssVariableRegex } from '../../render/dom/utils/css-variables-conversion.mjs';\nimport { mix } from '../../utils/mix.mjs';\nimport { complex } from '../../value/types/complex/index.mjs';\nconst varToken = \"_$css\";\nconst correctBoxShadow = {\n  correct: (latest, {\n    treeScale,\n    projectionDelta\n  }) => {\n    const original = latest;\n    /**\n     * We need to first strip and store CSS variables from the string.\n     */\n    const containsCSSVariables = latest.includes(\"var(\");\n    const cssVariables = [];\n    if (containsCSSVariables) {\n      latest = latest.replace(cssVariableRegex, match => {\n        cssVariables.push(match);\n        return varToken;\n      });\n    }\n    const shadow = complex.parse(latest);\n    // TODO: Doesn't support multiple shadows\n    if (shadow.length > 5) return original;\n    const template = complex.createTransformer(latest);\n    const offset = typeof shadow[0] !== \"number\" ? 1 : 0;\n    // Calculate the overall context scale\n    const xScale = projectionDelta.x.scale * treeScale.x;\n    const yScale = projectionDelta.y.scale * treeScale.y;\n    shadow[0 + offset] /= xScale;\n    shadow[1 + offset] /= yScale;\n    /**\n     * Ideally we'd correct x and y scales individually, but because blur and\n     * spread apply to both we have to take a scale average and apply that instead.\n     * We could potentially improve the outcome of this by incorporating the ratio between\n     * the two scales.\n     */\n    const averageScale = mix(xScale, yScale, 0.5);\n    // Blur\n    if (typeof shadow[2 + offset] === \"number\") shadow[2 + offset] /= averageScale;\n    // Spread\n    if (typeof shadow[3 + offset] === \"number\") shadow[3 + offset] /= averageScale;\n    let output = template(shadow);\n    if (containsCSSVariables) {\n      let i = 0;\n      output = output.replace(varToken, () => {\n        const cssVariable = cssVariables[i];\n        i++;\n        return cssVariable;\n      });\n    }\n    return output;\n  }\n};\nexport { correctBoxShadow };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}