{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/DeleteOrmReport.tsx\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport HighlightOffIcon from '@mui/icons-material/HighlightOff';\nimport DialogActions from '@mui/material/DialogActions';\nimport { ButtonBase, Typography } from '@mui/material';\nimport { FormattedMessage } from 'react-intl';\nimport { LoadingButton } from '@mui/lab';\nimport { Box } from '@mui/system';\nimport { useIntl } from 'react-intl';\nimport { openSnackbar } from 'store/slice/snackbarSlice';\nimport sendRequest from 'services/ApiService';\nimport Modal from 'components/extended/Modal';\nimport { useAppDispatch } from 'app/hooks';\nimport Api from 'constants/Api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DeleteOrmReport = ({\n  id,\n  updateTable\n}) => {\n  _s();\n  const [open, setOpen] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const dispatch = useAppDispatch();\n  const handleClickOpen = () => {\n    setOpen(true);\n  };\n  const handleClose = () => {\n    setOpen(false);\n  };\n  const handleDeleteOrmReport = async () => {\n    try {\n      setLoading(true);\n      const response = await sendRequest(Api.monthly_efford.deleteOrmReport(id));\n      dispatch(openSnackbar({\n        open: true,\n        message: response.status ? 'Deleted ORM Report Successfully' : response.result.content.message,\n        variant: 'alert',\n        alert: response.status ? {\n          color: 'success'\n        } : {\n          color: 'error'\n        }\n      }));\n    } catch (error) {\n      dispatch(openSnackbar({\n        open: true,\n        message: 'Deleted This ORM Report Fail',\n        variant: 'alert',\n        alert: {\n          color: 'error'\n        }\n      }));\n    } finally {\n      setLoading(false);\n      setOpen(false);\n    }\n    updateTable();\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(ButtonBase, {\n      onClick: handleClickOpen,\n      color: \"inherit\",\n      children: /*#__PURE__*/_jsxDEV(HighlightOffIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: useIntl().formatMessage({\n        id: 'warning'\n      }),\n      isOpen: open,\n      onClose: handleClose,\n      \"aria-labelledby\": \"alert-dialog-title\",\n      \"aria-describedby\": \"alert-dialog-description\",\n      maxWidth: \"xs\",\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          fontSize: 16,\n          fontWeight: 500,\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: \"confirm-delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          display: 'flex'\n        },\n        children: [/*#__PURE__*/_jsxDEV(LoadingButton, {\n          color: \"error\",\n          disabled: loading,\n          size: \"large\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: \"cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(LoadingButton, {\n          loading: loading,\n          disabled: loading,\n          variant: \"contained\",\n          size: \"large\",\n          type: \"submit\",\n          onClick: handleDeleteOrmReport,\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: \"confirm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(DeleteOrmReport, \"0itQYrF/6GsHTq4yX4HNKHTBo9c=\", false, function () {\n  return [useAppDispatch, useIntl];\n});\n_c = DeleteOrmReport;\nexport default DeleteOrmReport;\nvar _c;\n$RefreshReg$(_c, \"DeleteOrmReport\");", "map": {"version": 3, "names": ["useState", "HighlightOffIcon", "DialogActions", "ButtonBase", "Typography", "FormattedMessage", "LoadingButton", "Box", "useIntl", "openSnackbar", "sendRequest", "Modal", "useAppDispatch", "Api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DeleteOrmReport", "id", "updateTable", "_s", "open", "<PERSON><PERSON><PERSON>", "loading", "setLoading", "dispatch", "handleClickOpen", "handleClose", "handleDeleteOrmReport", "response", "monthly_efford", "deleteOrmReport", "message", "status", "result", "content", "variant", "alert", "color", "error", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "formatMessage", "isOpen", "onClose", "max<PERSON><PERSON><PERSON>", "sx", "mb", "fontSize", "fontWeight", "display", "disabled", "size", "type", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/DeleteOrmReport.tsx"], "sourcesContent": ["import { useState } from 'react';\r\n\r\nimport HighlightOffIcon from '@mui/icons-material/HighlightOff';\r\nimport DialogActions from '@mui/material/DialogActions';\r\nimport { ButtonBase, Typography } from '@mui/material';\r\nimport { FormattedMessage } from 'react-intl';\r\nimport { LoadingButton } from '@mui/lab';\r\nimport { Box } from '@mui/system';\r\nimport { useIntl } from 'react-intl';\r\n\r\nimport { openSnackbar } from 'store/slice/snackbarSlice';\r\nimport sendRequest from 'services/ApiService';\r\nimport Modal from 'components/extended/Modal';\r\nimport { useAppDispatch } from 'app/hooks';\r\nimport Api from 'constants/Api';\r\n\r\ntype UploadORMReportProps = {\r\n    id: string;\r\n    updateTable: () => void;\r\n};\r\n\r\nconst DeleteOrmReport = ({ id, updateTable }: UploadORMReportProps) => {\r\n    const [open, setOpen] = useState(false);\r\n    const [loading, setLoading] = useState(false);\r\n    const dispatch = useAppDispatch();\r\n\r\n    const handleClickOpen = () => {\r\n        setOpen(true);\r\n    };\r\n\r\n    const handleClose = () => {\r\n        setOpen(false);\r\n    };\r\n    const handleDeleteOrmReport = async () => {\r\n        try {\r\n            setLoading(true);\r\n            const response = await sendRequest(Api.monthly_efford.deleteOrmReport(id));\r\n            dispatch(\r\n                openSnackbar({\r\n                    open: true,\r\n                    message: response.status ? 'Deleted ORM Report Successfully' : response.result.content.message,\r\n                    variant: 'alert',\r\n                    alert: response.status ? { color: 'success' } : { color: 'error' }\r\n                })\r\n            );\r\n        } catch (error) {\r\n            dispatch(\r\n                openSnackbar({\r\n                    open: true,\r\n                    message: 'Deleted This ORM Report Fail',\r\n                    variant: 'alert',\r\n                    alert: { color: 'error' }\r\n                })\r\n            );\r\n        } finally {\r\n            setLoading(false);\r\n            setOpen(false);\r\n        }\r\n        updateTable();\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <ButtonBase onClick={handleClickOpen} color=\"inherit\">\r\n                <HighlightOffIcon />\r\n            </ButtonBase>\r\n            <Modal\r\n                title={useIntl().formatMessage({ id: 'warning' })}\r\n                isOpen={open}\r\n                onClose={handleClose}\r\n                aria-labelledby=\"alert-dialog-title\"\r\n                aria-describedby=\"alert-dialog-description\"\r\n                maxWidth=\"xs\"\r\n            >\r\n                <Box sx={{ mb: 2 }}>\r\n                    <Typography fontSize={16} fontWeight={500}>\r\n                        <FormattedMessage id=\"confirm-delete\" />\r\n                    </Typography>\r\n                </Box>\r\n\r\n                <DialogActions sx={{ display: 'flex' }}>\r\n                    <LoadingButton color=\"error\" disabled={loading} size=\"large\" onClick={handleClose}>\r\n                        <FormattedMessage id=\"cancel\" />\r\n                    </LoadingButton>\r\n\r\n                    <LoadingButton\r\n                        loading={loading}\r\n                        disabled={loading}\r\n                        variant=\"contained\"\r\n                        size=\"large\"\r\n                        type=\"submit\"\r\n                        onClick={handleDeleteOrmReport}\r\n                    >\r\n                        <FormattedMessage id=\"confirm\" />\r\n                    </LoadingButton>\r\n                </DialogActions>\r\n            </Modal>\r\n        </>\r\n    );\r\n};\r\n\r\nexport default DeleteOrmReport;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAEhC,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,UAAU,EAAEC,UAAU,QAAQ,eAAe;AACtD,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,aAAa,QAAQ,UAAU;AACxC,SAASC,GAAG,QAAQ,aAAa;AACjC,SAASC,OAAO,QAAQ,YAAY;AAEpC,SAASC,YAAY,QAAQ,2BAA2B;AACxD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,KAAK,MAAM,2BAA2B;AAC7C,SAASC,cAAc,QAAQ,WAAW;AAC1C,OAAOC,GAAG,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOhC,MAAMC,eAAe,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAkC,CAAC,KAAK;EAAAC,EAAA;EACnE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM0B,QAAQ,GAAGd,cAAc,CAAC,CAAC;EAEjC,MAAMe,eAAe,GAAGA,CAAA,KAAM;IAC1BJ,OAAO,CAAC,IAAI,CAAC;EACjB,CAAC;EAED,MAAMK,WAAW,GAAGA,CAAA,KAAM;IACtBL,OAAO,CAAC,KAAK,CAAC;EAClB,CAAC;EACD,MAAMM,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACAJ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMK,QAAQ,GAAG,MAAMpB,WAAW,CAACG,GAAG,CAACkB,cAAc,CAACC,eAAe,CAACb,EAAE,CAAC,CAAC;MAC1EO,QAAQ,CACJjB,YAAY,CAAC;QACTa,IAAI,EAAE,IAAI;QACVW,OAAO,EAAEH,QAAQ,CAACI,MAAM,GAAG,iCAAiC,GAAGJ,QAAQ,CAACK,MAAM,CAACC,OAAO,CAACH,OAAO;QAC9FI,OAAO,EAAE,OAAO;QAChBC,KAAK,EAAER,QAAQ,CAACI,MAAM,GAAG;UAAEK,KAAK,EAAE;QAAU,CAAC,GAAG;UAAEA,KAAK,EAAE;QAAQ;MACrE,CAAC,CACL,CAAC;IACL,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZd,QAAQ,CACJjB,YAAY,CAAC;QACTa,IAAI,EAAE,IAAI;QACVW,OAAO,EAAE,8BAA8B;QACvCI,OAAO,EAAE,OAAO;QAChBC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAQ;MAC5B,CAAC,CACL,CAAC;IACL,CAAC,SAAS;MACNd,UAAU,CAAC,KAAK,CAAC;MACjBF,OAAO,CAAC,KAAK,CAAC;IAClB;IACAH,WAAW,CAAC,CAAC;EACjB,CAAC;EAED,oBACIL,OAAA,CAAAE,SAAA;IAAAwB,QAAA,gBACI1B,OAAA,CAACZ,UAAU;MAACuC,OAAO,EAAEf,eAAgB;MAACY,KAAK,EAAC,SAAS;MAAAE,QAAA,eACjD1B,OAAA,CAACd,gBAAgB;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eACb/B,OAAA,CAACJ,KAAK;MACFoC,KAAK,EAAEvC,OAAO,CAAC,CAAC,CAACwC,aAAa,CAAC;QAAE7B,EAAE,EAAE;MAAU,CAAC,CAAE;MAClD8B,MAAM,EAAE3B,IAAK;MACb4B,OAAO,EAAEtB,WAAY;MACrB,mBAAgB,oBAAoB;MACpC,oBAAiB,0BAA0B;MAC3CuB,QAAQ,EAAC,IAAI;MAAAV,QAAA,gBAEb1B,OAAA,CAACR,GAAG;QAAC6C,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAZ,QAAA,eACf1B,OAAA,CAACX,UAAU;UAACkD,QAAQ,EAAE,EAAG;UAACC,UAAU,EAAE,GAAI;UAAAd,QAAA,eACtC1B,OAAA,CAACV,gBAAgB;YAACc,EAAE,EAAC;UAAgB;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAEN/B,OAAA,CAACb,aAAa;QAACkD,EAAE,EAAE;UAAEI,OAAO,EAAE;QAAO,CAAE;QAAAf,QAAA,gBACnC1B,OAAA,CAACT,aAAa;UAACiC,KAAK,EAAC,OAAO;UAACkB,QAAQ,EAAEjC,OAAQ;UAACkC,IAAI,EAAC,OAAO;UAAChB,OAAO,EAAEd,WAAY;UAAAa,QAAA,eAC9E1B,OAAA,CAACV,gBAAgB;YAACc,EAAE,EAAC;UAAQ;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eAEhB/B,OAAA,CAACT,aAAa;UACVkB,OAAO,EAAEA,OAAQ;UACjBiC,QAAQ,EAAEjC,OAAQ;UAClBa,OAAO,EAAC,WAAW;UACnBqB,IAAI,EAAC,OAAO;UACZC,IAAI,EAAC,QAAQ;UACbjB,OAAO,EAAEb,qBAAsB;UAAAY,QAAA,eAE/B1B,OAAA,CAACV,gBAAgB;YAACc,EAAE,EAAC;UAAS;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAAA,eACV,CAAC;AAEX,CAAC;AAACzB,EAAA,CA9EIH,eAAe;EAAA,QAGAN,cAAc,EA2CZJ,OAAO;AAAA;AAAAoD,EAAA,GA9CxB1C,eAAe;AAgFrB,eAAeA,eAAe;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}