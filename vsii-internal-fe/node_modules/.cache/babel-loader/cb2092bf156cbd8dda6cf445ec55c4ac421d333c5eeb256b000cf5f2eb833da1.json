{"ast": null, "code": "import React,{useCallback,useEffect,useState}from'react';// project imports\nimport{getAllProductReport,getProjectReportOption,productReportSelector,syncDataProjectReport}from'store/slice/productReportSlice';import{ProductReportSearch,ProductReportThead,ProductReportTbody}from'containers/product-report';import ProductDetail from'containers/product-report/ProductDetail';import{closeConfirm,openConfirm}from'store/slice/confirmSlice';import{Table,TableFooter}from'components/extended/Table';import{getSearchParam,transformObject}from'utils/common';import{useAppDispatch,useAppSelector}from'app/hooks';import{openSnackbar}from'store/slice/snackbarSlice';import{SEARCH_PARAM_KEY,TEXT_CONFIG_SCREEN}from'constants/Common';import{FilterCollapse}from'containers/search';import MainCard from'components/cards/MainCard';import HTTP_CODE from'constants/HttpCode';// third party\nimport{useSearchParams}from'react-router-dom';import{FormattedMessage}from'react-intl';// ==============================|| Product Report ||============================== //\nimport{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const ProductReport=()=>{// URL Params\nconst[searchParams,setSearchParams]=useSearchParams();const keyParams=[SEARCH_PARAM_KEY.page,SEARCH_PARAM_KEY.size,SEARCH_PARAM_KEY.projectId];const params=getSearchParam(keyParams,searchParams);transformObject(params);// delete unnecessary key value\nconst defaultConditions={page:params.page||1,size:params.size||10,projectId:{value:params.projectId,label:''}||null};const[conditions,setConditions]=useState(defaultConditions);const[openDetailModal,setOpenDetailModal]=useState(false);const dispatch=useAppDispatch();const{productReport,pagination,loading}=useAppSelector(productReportSelector);const{ProductReport}=TEXT_CONFIG_SCREEN.generalReport;const fetchData=useCallback(()=>{var _conditions$projectId,_conditions$projectId2;dispatch(getAllProductReport({...conditions,page:conditions.page,projectId:(_conditions$projectId=conditions.projectId)!==null&&_conditions$projectId!==void 0&&_conditions$projectId.value?+((_conditions$projectId2=conditions.projectId)===null||_conditions$projectId2===void 0?void 0:_conditions$projectId2.value):null}));},[conditions,dispatch]);// Event\nconst handleChangePage=(event,newPage)=>{setConditions({...conditions,page:newPage+1});setSearchParams({...params,page:newPage+1});};const handleChangeRowsPerPage=event=>{setConditions({...conditions,page:conditions.page,size:parseInt(event.target.value,10)});setSearchParams({...params,page:conditions.page,size:parseInt(event.target.value,10)});};// Handle submit\nconst handleSearch=_ref=>{let{projectId}=_ref;setSearchParams({page:'1',size:conditions.size.toString(),...(projectId!==null&&projectId!==void 0&&projectId.value?{projectId:projectId.value.toString()}:{})});setConditions({...conditions,projectId:projectId||null,page:1});};const handleSyncData=()=>{dispatch(openConfirm({open:true,title:/*#__PURE__*/_jsx(FormattedMessage,{id:\"warning\"}),content:/*#__PURE__*/_jsx(FormattedMessage,{id:\"confirm-synchronize\"}),width:'400px',handleConfirm:async()=>{const resultAction=await dispatch(syncDataProjectReport());if(syncDataProjectReport.fulfilled.match(resultAction)){Promise.all([fetchData(),dispatch(getProjectReportOption())]);dispatch(openSnackbar({open:true,message:'sync-successfully-message',variant:'alert',close:true,alert:{color:'success'}}));}else if(syncDataProjectReport.rejected.match(resultAction)){dispatch(openSnackbar({open:true,message:resultAction.error.code==='ERR_BAD_REQUEST'?HTTP_CODE[404]:resultAction.error.message,variant:'alert',close:true,alert:{color:'error'}}));}dispatch(closeConfirm());}}));};// Effect\nuseEffect(()=>{fetchData();},[fetchData]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FilterCollapse,{handleSynchronize:handleSyncData,syncLabel:ProductReport+'synchronize',children:/*#__PURE__*/_jsx(ProductReportSearch,{projectDefault:defaultConditions.projectId,handleSearch:handleSearch})}),/*#__PURE__*/_jsx(MainCard,{children:/*#__PURE__*/_jsx(Table,{heads:/*#__PURE__*/_jsx(ProductReportThead,{}),isLoading:loading[getAllProductReport.typePrefix],data:productReport,children:/*#__PURE__*/_jsx(ProductReportTbody,{setOpenDetailModal:setOpenDetailModal,page:conditions.page-1,size:conditions.size})})}),!loading[getAllProductReport.typePrefix]&&/*#__PURE__*/_jsx(TableFooter,{pagination:{total:pagination.totalElement,page:conditions.page-1,size:conditions.size},onPageChange:handleChangePage,onRowsPerPageChange:handleChangeRowsPerPage}),/*#__PURE__*/_jsx(ProductDetail,{isOpen:openDetailModal,handleClose:()=>setOpenDetailModal(false)})]});};export default ProductReport;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}