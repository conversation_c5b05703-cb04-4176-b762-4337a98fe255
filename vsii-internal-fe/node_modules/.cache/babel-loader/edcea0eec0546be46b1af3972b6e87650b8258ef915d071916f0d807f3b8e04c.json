{"ast": null, "code": ";\n(function (root, factory) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Check if typed arrays are supported\n    if (typeof ArrayBuffer != 'function') {\n      return;\n    }\n\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var WordArray = C_lib.WordArray;\n\n    // Reference original init\n    var superInit = WordArray.init;\n\n    // Augment WordArray.init to handle typed arrays\n    var subInit = WordArray.init = function (typedArray) {\n      // Convert buffers to uint8\n      if (typedArray instanceof ArrayBuffer) {\n        typedArray = new Uint8Array(typedArray);\n      }\n\n      // Convert other array views to uint8\n      if (typedArray instanceof Int8Array || typeof Uint8ClampedArray !== \"undefined\" && typedArray instanceof Uint8ClampedArray || typedArray instanceof Int16Array || typedArray instanceof Uint16Array || typedArray instanceof Int32Array || typedArray instanceof Uint32Array || typedArray instanceof Float32Array || typedArray instanceof Float64Array) {\n        typedArray = new Uint8Array(typedArray.buffer, typedArray.byteOffset, typedArray.byteLength);\n      }\n\n      // Handle Uint8Array\n      if (typedArray instanceof Uint8Array) {\n        // Shortcut\n        var typedArrayByteLength = typedArray.byteLength;\n\n        // Extract bytes\n        var words = [];\n        for (var i = 0; i < typedArrayByteLength; i++) {\n          words[i >>> 2] |= typedArray[i] << 24 - i % 4 * 8;\n        }\n\n        // Initialize this word array\n        superInit.call(this, words, typedArrayByteLength);\n      } else {\n        // Else call normal init\n        superInit.apply(this, arguments);\n      }\n    };\n    subInit.prototype = WordArray;\n  })();\n  return CryptoJS.lib.WordArray;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}