{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/InputType.tsx\";\nimport React from 'react';\nimport { TYPE_HOLIDAY_OPTIONS, TYPE_INPUT } from 'constants/Common';\nimport { Select } from 'components/extended/Form';\nimport { searchFormConfig } from './Config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst InputType = ({\n  select,\n  required,\n  label,\n  name,\n  handleChange,\n  disabled\n}) => {\n  return /*#__PURE__*/_jsxDEV(Select, {\n    isMultipleLanguage: true,\n    required: required,\n    selects: !select ? TYPE_INPUT : TYPE_HOLIDAY_OPTIONS,\n    name: name || searchFormConfig.holidayType.name,\n    label: label,\n    handleChange: handleChange,\n    disabled: disabled\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 9\n  }, this);\n};\n_c = InputType;\nexport default InputType;\nvar _c;\n$RefreshReg$(_c, \"InputType\");", "map": {"version": 3, "names": ["React", "TYPE_HOLIDAY_OPTIONS", "TYPE_INPUT", "Select", "searchFormConfig", "jsxDEV", "_jsxDEV", "InputType", "select", "required", "label", "name", "handleChange", "disabled", "isMultipleLanguage", "selects", "holidayType", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/InputType.tsx"], "sourcesContent": ["import React from 'react';\nimport { SelectChangeEvent } from '@mui/material';\n\nimport { TYPE_HOLIDAY_OPTIONS, TYPE_INPUT } from 'constants/Common';\nimport { Select } from 'components/extended/Form';\nimport { searchFormConfig } from './Config';\n\ntype Props = {\n    select?: boolean | null;\n    required?: boolean;\n    label?: string;\n    name?: string;\n    handleChange?: (e: SelectChangeEvent<unknown>) => void;\n    disabled?: boolean;\n};\n\nconst InputType: React.FC<Props> = ({ select, required, label, name, handleChange, disabled }) => {\n    return (\n        <Select\n            isMultipleLanguage\n            required={required}\n            selects={!select ? TYPE_INPUT : TYPE_HOLIDAY_OPTIONS}\n            name={name || searchFormConfig.holidayType.name}\n            label={label}\n            handleChange={handleChange}\n            disabled={disabled}\n        />\n    );\n};\n\nexport default InputType;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAGzB,SAASC,oBAAoB,EAAEC,UAAU,QAAQ,kBAAkB;AACnE,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,gBAAgB,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAW5C,MAAMC,SAA0B,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,KAAK;EAAEC,IAAI;EAAEC,YAAY;EAAEC;AAAS,CAAC,KAAK;EAC9F,oBACIP,OAAA,CAACH,MAAM;IACHW,kBAAkB;IAClBL,QAAQ,EAAEA,QAAS;IACnBM,OAAO,EAAE,CAACP,MAAM,GAAGN,UAAU,GAAGD,oBAAqB;IACrDU,IAAI,EAAEA,IAAI,IAAIP,gBAAgB,CAACY,WAAW,CAACL,IAAK;IAChDD,KAAK,EAAEA,KAAM;IACbE,YAAY,EAAEA,YAAa;IAC3BC,QAAQ,EAAEA;EAAS;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtB,CAAC;AAEV,CAAC;AAACC,EAAA,GAZId,SAA0B;AAchC,eAAeA,SAAS;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}