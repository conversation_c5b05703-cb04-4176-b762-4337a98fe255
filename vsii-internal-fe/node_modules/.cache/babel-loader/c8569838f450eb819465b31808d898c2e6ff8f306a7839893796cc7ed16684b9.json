{"ast": null, "code": "import{FormattedMessage}from'react-intl';// project imports\nimport{searchFormConfig}from'./Config';import{Select}from'components/extended/Form';import{EMAIL_TYPE_OPTIONS,DEFAULT_VALUE_OPTION_SELECT}from'constants/Common';import{jsx as _jsx,Fragment as _Fragment}from\"react/jsx-runtime\";const EmailType=props=>{const{required,handleChange,disabled}=props;return/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(Select,{isMultipleLanguage:true,required:required,disabled:disabled,handleChange:handleChange,selects:[DEFAULT_VALUE_OPTION_SELECT,...EMAIL_TYPE_OPTIONS],name:searchFormConfig.emailType.name,label:/*#__PURE__*/_jsx(FormattedMessage,{id:searchFormConfig.emailType.label})})});};export default EmailType;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}