{"ast": null, "code": "import{useEffect}from'react';import{Grid,Typography}from'@mui/material';import ErrorIcon from'@mui/icons-material/Error';import{FormattedMessage}from'react-intl';import{costMonitoringFilterConfig,costMonitoringFilterSchema}from'pages/cost-monitoring/Config';import{costAndEffortMonitoringSelector,getCostMonitoringProjectOption}from'store/slice/costAndEffortMonitoringSlice';import{TEXT_CONFIG_SCREEN,TEXT_INPUT_COLOR_EFFORT_INCURRED}from'constants/Common';import{Autocomplete,Label}from'components/extended/Form';import{searchFormConfig}from'containers/search/Config';import ColorNoteTooltip from'components/ColorNoteTooltip';import{useAppDispatch,useAppSelector}from'app/hooks';import{Weeks,SearchForm,Years}from'../search';import{Button}from'components';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const WeeklyCostMonitoringSearch=_ref=>{let{formReset,weeks,handleChangeYear,handleSearch}=_ref;const{projectOptions}=useAppSelector(costAndEffortMonitoringSelector);const{weeklyMonitoring}=TEXT_CONFIG_SCREEN.costAndEffortMonitoring;const dispatch=useAppDispatch();const handleChangeWeek=week=>{dispatch(getCostMonitoringProjectOption({type:'week',value:week,color:true}));};useEffect(()=>{dispatch(getCostMonitoringProjectOption({type:'week',value:costMonitoringFilterConfig.week,color:true}));},[dispatch,formReset]);return/*#__PURE__*/_jsx(SearchForm,{defaultValues:costMonitoringFilterConfig,formSchema:costMonitoringFilterSchema,handleSubmit:handleSearch,formReset:formReset,children:/*#__PURE__*/_jsxs(Grid,{container:true,alignItems:\"center\",spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:3,children:/*#__PURE__*/_jsx(Years,{handleChangeYear:handleChangeYear,ignoreDefault:true,label:weeklyMonitoring+'year'})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:3,children:/*#__PURE__*/_jsx(Weeks,{weeks:weeks,onChange:handleChangeWeek,label:weeklyMonitoring+'weeks'})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:3,children:/*#__PURE__*/_jsx(Autocomplete,{options:projectOptions,name:searchFormConfig.project.name,label:/*#__PURE__*/_jsxs(Typography,{display:\"flex\",gap:0.5,children:[/*#__PURE__*/_jsx(FormattedMessage,{id:weeklyMonitoring+'projects'}),/*#__PURE__*/_jsx(ColorNoteTooltip,{notes:TEXT_INPUT_COLOR_EFFORT_INCURRED,children:/*#__PURE__*/_jsx(ErrorIcon,{sx:{fontSize:15}})})]}),groupBy:option=>option.typeCode,isDefaultAll:true,isDisableClearable:true})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,lg:3,children:[/*#__PURE__*/_jsx(Label,{label:\"\\xA0\"}),/*#__PURE__*/_jsx(Button,{type:\"submit\",size:\"medium\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:weeklyMonitoring+'search'}),variant:\"contained\"})]})]})});};export default WeeklyCostMonitoringSearch;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}