{"ast": null, "code": "import{But<PERSON><PERSON>ase,TableCell,TableRow,Tooltip}from'@mui/material';import{Stack}from'@mui/system';import{PERMISSIONS}from'constants/Permission';import React,{useEffect,useMemo,useState}from'react';import{checkAllowedPermission}from'utils/authorization';import HighlightOffIcon from'@mui/icons-material/HighlightOff';import EditTwoToneIcon from'@mui/icons-material/EditTwoTone';import DoneIcon from'@mui/icons-material/Done';import{FormattedMessage}from'react-intl';import{Input}from'components/extended/Form';import{useFormContext}from'react-hook-form';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const EditLanguageRow=_ref=>{let{item,conditions,index,setDataEditLanguage,activeRowIndex,handleclickEdit,handleCancelEdit}=_ref;const{flexibleReportingConfigPermission}=PERMISSIONS.admin;const[isEdited,setisEdited]=useState(false);const methods=useFormContext();const newText=useMemo(()=>{var _item$languageConfigs,_item$languageConfigs2;return((_item$languageConfigs=item.languageConfigs)===null||_item$languageConfigs===void 0?void 0:(_item$languageConfigs2=_item$languageConfigs.find(item=>item.languageCode===conditions.code))===null||_item$languageConfigs2===void 0?void 0:_item$languageConfigs2.newText)||'';},[item,conditions]);useEffect(()=>{if(isEdited){methods.reset({newText:newText,note:item.note||'',languageCode:conditions.code});}// eslint-disable-next-line react-hooks/exhaustive-deps\n},[isEdited,item]);return/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:conditions.size*(conditions.page-1)+index+1}),/*#__PURE__*/_jsx(TableCell,{children:item.screenName}),/*#__PURE__*/_jsx(TableCell,{children:item.defaultTextNameENG}),isEdited?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Input,{name:\"newText\"})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Input,{name:\"note\"})}),/*#__PURE__*/_jsx(TableCell,{children:checkAllowedPermission(flexibleReportingConfigPermission.textConfig.edit)&&/*#__PURE__*/_jsxs(Stack,{direction:\"row\",justifyContent:\"center\",alignItems:\"center\",children:[/*#__PURE__*/_jsx(Tooltip,{title:/*#__PURE__*/_jsx(FormattedMessage,{id:\"cancel\"}),children:/*#__PURE__*/_jsx(ButtonBase,{onClick:()=>{setisEdited(false);handleCancelEdit();},children:/*#__PURE__*/_jsx(HighlightOffIcon,{sx:{fontSize:18}})})}),/*#__PURE__*/_jsx(Tooltip,{title:/*#__PURE__*/_jsx(FormattedMessage,{id:\"confirm\"}),children:/*#__PURE__*/_jsx(ButtonBase,{type:\"submit\",children:/*#__PURE__*/_jsx(DoneIcon,{sx:{fontSize:18}})})})]})})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(TableCell,{children:newText}),/*#__PURE__*/_jsx(TableCell,{sx:{whiteSpace:'pre-line'},children:item.note}),/*#__PURE__*/_jsx(TableCell,{children:checkAllowedPermission(flexibleReportingConfigPermission.textConfig.edit)&&/*#__PURE__*/_jsx(Stack,{direction:\"row\",justifyContent:\"center\",alignItems:\"center\",children:/*#__PURE__*/_jsx(Tooltip,{placement:\"top\",title:/*#__PURE__*/_jsx(FormattedMessage,{id:'edit'}),children:/*#__PURE__*/_jsx(ButtonBase,{onClick:()=>{setisEdited(true);handleclickEdit();setDataEditLanguage(item);},disabled:activeRowIndex!==null&&activeRowIndex!==index,children:/*#__PURE__*/_jsx(EditTwoToneIcon,{sx:{fontSize:'1.1rem'}})})})})})]})]});};export default EditLanguageRow;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}