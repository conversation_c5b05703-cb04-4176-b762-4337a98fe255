{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/product-report/BacklogDetailTbody.tsx\";\n// material-ui\nimport { TableCell, TableRow } from '@mui/material';\n\n//project import\nimport ProductReportStatusChip from 'components/extended/ProductReportStatusChip';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BacklogDetailTbody = props => {\n  const {\n    data\n  } = props;\n  return /*#__PURE__*/_jsxDEV(TableRow, {\n    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n      sx: {\n        width: '55%',\n        px: '3px'\n      },\n      children: data.taskName\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n      sx: {\n        width: '30%',\n        px: '3px'\n      },\n      children: data.assigneeName\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n      sx: {\n        width: '15%',\n        px: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(ProductReportStatusChip, {\n        status: data.status\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 9\n  }, this);\n};\n_c = BacklogDetailTbody;\nexport default BacklogDetailTbody;\nvar _c;\n$RefreshReg$(_c, \"BacklogDetailTbody\");", "map": {"version": 3, "names": ["TableCell", "TableRow", "ProductReportStatusChip", "jsxDEV", "_jsxDEV", "BacklogDetailTbody", "props", "data", "children", "sx", "width", "px", "taskName", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "assignee<PERSON>ame", "status", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/product-report/BacklogDetailTbody.tsx"], "sourcesContent": ["// material-ui\nimport { TableCell, TableRow } from '@mui/material';\n\n//project import\nimport ProductReportStatusChip from 'components/extended/ProductReportStatusChip';\nimport { IRequirement } from 'types';\n\ninterface IBacklogDetailTbodyProps {\n    data: IRequirement['tasks'][number];\n}\n\nconst BacklogDetailTbody = (props: IBacklogDetailTbodyProps) => {\n    const { data } = props;\n\n    return (\n        <TableRow>\n            <TableCell sx={{ width: '55%', px: '3px' }}>{data.taskName}</TableCell>\n            <TableCell sx={{ width: '30%', px: '3px' }}>{data.assigneeName}</TableCell>\n            <TableCell sx={{ width: '15%', px: 0 }}>\n                <ProductReportStatusChip status={data.status} />\n            </TableCell>\n        </TableRow>\n    );\n};\n\nexport default BacklogDetailTbody;\n"], "mappings": ";AAAA;AACA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,eAAe;;AAEnD;AACA,OAAOC,uBAAuB,MAAM,6CAA6C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOlF,MAAMC,kBAAkB,GAAIC,KAA+B,IAAK;EAC5D,MAAM;IAAEC;EAAK,CAAC,GAAGD,KAAK;EAEtB,oBACIF,OAAA,CAACH,QAAQ;IAAAO,QAAA,gBACLJ,OAAA,CAACJ,SAAS;MAACS,EAAE,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEC,EAAE,EAAE;MAAM,CAAE;MAAAH,QAAA,EAAED,IAAI,CAACK;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACvEZ,OAAA,CAACJ,SAAS;MAACS,EAAE,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEC,EAAE,EAAE;MAAM,CAAE;MAAAH,QAAA,EAAED,IAAI,CAACU;IAAY;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAC3EZ,OAAA,CAACJ,SAAS;MAACS,EAAE,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,eACnCJ,OAAA,CAACF,uBAAuB;QAACgB,MAAM,EAAEX,IAAI,CAACW;MAAO;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEnB,CAAC;AAACG,EAAA,GAZId,kBAAkB;AAcxB,eAAeA,kBAAkB;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}