{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/SaleTableTBody.tsx\",\n  _s = $RefreshSig$();\nimport { FormattedMessage } from 'react-intl';\n\n// mui import\nimport { IconButton, Stack, TableCell, TableRow, Tooltip, useTheme } from '@mui/material';\n\n// projects import\nimport { DeleteTwoToneIcon } from 'assets/images/icons';\nimport { Role } from 'containers/search';\nimport { Input, NumericFormatCustom } from 'components/extended/Form';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SaleTableTBody = props => {\n  _s();\n  const {\n    index,\n    remove\n  } = props;\n  const theme = useTheme();\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(TableRow, {\n      sx: {\n        '& .MuiFormControl-root': {\n          height: '50px'\n        },\n        '&.MuiTableRow-root': {\n          '& td': {\n            borderColor: 'transparent',\n            '&.MuiTableCell-root:nth-of-type(2) .MuiFormHelperText-root': {\n              position: 'absolute'\n            }\n          }\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        children: index + 1\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          '& .MuiInputBase-root': {\n            width: '110px'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Role, {\n          isShowName: `hcInfo.${index}.role`,\n          isShowLabel: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          '& .MuiFormControl-root': {\n            marginTop: '10px'\n          },\n          [theme.breakpoints.down('sm')]: {\n            '& .MuiInputBase-inputSizeSmall': {\n              width: '70px'\n            }\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          textFieldProps: {\n            InputProps: {\n              inputComponent: NumericFormatCustom\n            }\n          },\n          name: `hcInfo.${index}.rate`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          '& .MuiFormControl-root': {\n            marginTop: '10px'\n          },\n          [theme.breakpoints.down('sm')]: {\n            '& .MuiInputBase-inputSizeSmall': {\n              width: '70px'\n            }\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          textFieldProps: {\n            InputProps: {\n              inputComponent: NumericFormatCustom\n            }\n          },\n          name: `hcInfo.${index}.rateUSD`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          '& .MuiFormControl-root': {\n            marginTop: '10px'\n          },\n          [theme.breakpoints.down('sm')]: {\n            '& .MuiInputBase-inputSizeSmall': {\n              width: '70px'\n            }\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          textFieldProps: {\n            InputProps: {\n              inputComponent: NumericFormatCustom\n            }\n          },\n          name: `hcInfo.${index}.quantity`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          '& .MuiFormControl-root': {\n            marginTop: '10px'\n          },\n          [theme.breakpoints.down('sm')]: {\n            '& .MuiInputBase-inputSizeSmall': {\n              width: '90px'\n            }\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          textFieldProps: {\n            InputProps: {\n              inputComponent: NumericFormatCustom\n            }\n          },\n          disabled: true,\n          name: `hcInfo.${index}.amount`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          justifyContent: \"center\",\n          alignItems: \"center\",\n          children: /*#__PURE__*/_jsxDEV(Tooltip, {\n            placement: \"top\",\n            title: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: \"delete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 57\n            }, this),\n            onClick: () => remove(index),\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              \"aria-label\": \"delete\",\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(DeleteTwoToneIcon, {\n                sx: {\n                  fontSize: '1.1rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s(SaleTableTBody, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function () {\n  return [useTheme];\n});\n_c = SaleTableTBody;\nexport default SaleTableTBody;\nvar _c;\n$RefreshReg$(_c, \"SaleTableTBody\");", "map": {"version": 3, "names": ["FormattedMessage", "IconButton", "<PERSON><PERSON>", "TableCell", "TableRow", "<PERSON><PERSON><PERSON>", "useTheme", "DeleteTwoToneIcon", "Role", "Input", "NumericFormatCustom", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SaleTableTBody", "props", "_s", "index", "remove", "theme", "children", "sx", "height", "borderColor", "position", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "isShowName", "isShowLabel", "marginTop", "breakpoints", "down", "textFieldProps", "InputProps", "inputComponent", "name", "disabled", "direction", "justifyContent", "alignItems", "placement", "title", "id", "onClick", "size", "fontSize", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/SaleTableTBody.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\n\n// mui import\nimport { IconButton, Stack, TableCell, TableRow, Tooltip, useTheme } from '@mui/material';\n\n// projects import\nimport { DeleteTwoToneIcon } from 'assets/images/icons';\nimport { Role } from 'containers/search';\nimport { Input, NumericFormatCustom } from 'components/extended/Form';\nimport { IProductivityHcInfo } from 'types';\n\ntype ISaleTableTBodyprops = {\n    item: IProductivityHcInfo;\n    index: number;\n    remove: (index: number) => void;\n};\nconst SaleTableTBody = (props: ISaleTableTBodyprops) => {\n    const { index, remove } = props;\n    const theme = useTheme();\n\n    return (\n        <>\n            <TableRow\n                sx={{\n                    '& .MuiFormControl-root': { height: '50px' },\n                    '&.MuiTableRow-root': {\n                        '& td': {\n                            borderColor: 'transparent',\n                            '&.MuiTableCell-root:nth-of-type(2) .MuiFormHelperText-root': {\n                                position: 'absolute'\n                            }\n                        }\n                    }\n                }}\n            >\n                <TableCell>{index + 1}</TableCell>\n                <TableCell sx={{ '& .MuiInputBase-root': { width: '110px' } }}>\n                    <Role isShowName={`hcInfo.${index}.role`} isShowLabel={true} />\n                </TableCell>\n                <TableCell\n                    sx={{\n                        '& .MuiFormControl-root': { marginTop: '10px' },\n                        [theme.breakpoints.down('sm')]: {\n                            '& .MuiInputBase-inputSizeSmall': {\n                                width: '70px'\n                            }\n                        }\n                    }}\n                >\n                    <Input\n                        textFieldProps={{\n                            InputProps: {\n                                inputComponent: NumericFormatCustom as any\n                            }\n                        }}\n                        name={`hcInfo.${index}.rate`}\n                    />\n                </TableCell>\n                <TableCell\n                    sx={{\n                        '& .MuiFormControl-root': { marginTop: '10px' },\n                        [theme.breakpoints.down('sm')]: {\n                            '& .MuiInputBase-inputSizeSmall': {\n                                width: '70px'\n                            }\n                        }\n                    }}\n                >\n                    <Input\n                        textFieldProps={{\n                            InputProps: {\n                                inputComponent: NumericFormatCustom as any\n                            }\n                        }}\n                        name={`hcInfo.${index}.rateUSD`}\n                    />\n                </TableCell>\n                <TableCell\n                    sx={{\n                        '& .MuiFormControl-root': { marginTop: '10px' },\n                        [theme.breakpoints.down('sm')]: {\n                            '& .MuiInputBase-inputSizeSmall': {\n                                width: '70px'\n                            }\n                        }\n                    }}\n                >\n                    <Input\n                        textFieldProps={{\n                            InputProps: {\n                                inputComponent: NumericFormatCustom as any\n                            }\n                        }}\n                        name={`hcInfo.${index}.quantity`}\n                    />\n                </TableCell>\n                <TableCell\n                    sx={{\n                        '& .MuiFormControl-root': { marginTop: '10px' },\n                        [theme.breakpoints.down('sm')]: {\n                            '& .MuiInputBase-inputSizeSmall': {\n                                width: '90px'\n                            }\n                        }\n                    }}\n                >\n                    <Input\n                        textFieldProps={{\n                            InputProps: {\n                                inputComponent: NumericFormatCustom as any\n                            }\n                        }}\n                        disabled\n                        name={`hcInfo.${index}.amount`}\n                    />\n                </TableCell>\n                <TableCell>\n                    <Stack direction=\"row\" justifyContent=\"center\" alignItems=\"center\">\n                        <Tooltip placement=\"top\" title={<FormattedMessage id=\"delete\" />} onClick={() => remove(index)}>\n                            <IconButton aria-label=\"delete\" size=\"small\">\n                                <DeleteTwoToneIcon sx={{ fontSize: '1.1rem' }} />\n                            </IconButton>\n                        </Tooltip>\n                    </Stack>\n                </TableCell>\n            </TableRow>\n        </>\n    );\n};\n\nexport default SaleTableTBody;\n"], "mappings": ";;AAAA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,UAAU,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,eAAe;;AAEzF;AACA,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,EAAEC,mBAAmB,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQtE,MAAMC,cAAc,GAAIC,KAA2B,IAAK;EAAAC,EAAA;EACpD,MAAM;IAAEC,KAAK;IAAEC;EAAO,CAAC,GAAGH,KAAK;EAC/B,MAAMI,KAAK,GAAGd,QAAQ,CAAC,CAAC;EAExB,oBACIM,OAAA,CAAAE,SAAA;IAAAO,QAAA,eACIT,OAAA,CAACR,QAAQ;MACLkB,EAAE,EAAE;QACA,wBAAwB,EAAE;UAAEC,MAAM,EAAE;QAAO,CAAC;QAC5C,oBAAoB,EAAE;UAClB,MAAM,EAAE;YACJC,WAAW,EAAE,aAAa;YAC1B,4DAA4D,EAAE;cAC1DC,QAAQ,EAAE;YACd;UACJ;QACJ;MACJ,CAAE;MAAAJ,QAAA,gBAEFT,OAAA,CAACT,SAAS;QAAAkB,QAAA,EAAEH,KAAK,GAAG;MAAC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAClCjB,OAAA,CAACT,SAAS;QAACmB,EAAE,EAAE;UAAE,sBAAsB,EAAE;YAAEQ,KAAK,EAAE;UAAQ;QAAE,CAAE;QAAAT,QAAA,eAC1DT,OAAA,CAACJ,IAAI;UAACuB,UAAU,EAAE,UAAUb,KAAK,OAAQ;UAACc,WAAW,EAAE;QAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eACZjB,OAAA,CAACT,SAAS;QACNmB,EAAE,EAAE;UACA,wBAAwB,EAAE;YAAEW,SAAS,EAAE;UAAO,CAAC;UAC/C,CAACb,KAAK,CAACc,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG;YAC5B,gCAAgC,EAAE;cAC9BL,KAAK,EAAE;YACX;UACJ;QACJ,CAAE;QAAAT,QAAA,eAEFT,OAAA,CAACH,KAAK;UACF2B,cAAc,EAAE;YACZC,UAAU,EAAE;cACRC,cAAc,EAAE5B;YACpB;UACJ,CAAE;UACF6B,IAAI,EAAE,UAAUrB,KAAK;QAAQ;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACZjB,OAAA,CAACT,SAAS;QACNmB,EAAE,EAAE;UACA,wBAAwB,EAAE;YAAEW,SAAS,EAAE;UAAO,CAAC;UAC/C,CAACb,KAAK,CAACc,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG;YAC5B,gCAAgC,EAAE;cAC9BL,KAAK,EAAE;YACX;UACJ;QACJ,CAAE;QAAAT,QAAA,eAEFT,OAAA,CAACH,KAAK;UACF2B,cAAc,EAAE;YACZC,UAAU,EAAE;cACRC,cAAc,EAAE5B;YACpB;UACJ,CAAE;UACF6B,IAAI,EAAE,UAAUrB,KAAK;QAAW;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACZjB,OAAA,CAACT,SAAS;QACNmB,EAAE,EAAE;UACA,wBAAwB,EAAE;YAAEW,SAAS,EAAE;UAAO,CAAC;UAC/C,CAACb,KAAK,CAACc,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG;YAC5B,gCAAgC,EAAE;cAC9BL,KAAK,EAAE;YACX;UACJ;QACJ,CAAE;QAAAT,QAAA,eAEFT,OAAA,CAACH,KAAK;UACF2B,cAAc,EAAE;YACZC,UAAU,EAAE;cACRC,cAAc,EAAE5B;YACpB;UACJ,CAAE;UACF6B,IAAI,EAAE,UAAUrB,KAAK;QAAY;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACZjB,OAAA,CAACT,SAAS;QACNmB,EAAE,EAAE;UACA,wBAAwB,EAAE;YAAEW,SAAS,EAAE;UAAO,CAAC;UAC/C,CAACb,KAAK,CAACc,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG;YAC5B,gCAAgC,EAAE;cAC9BL,KAAK,EAAE;YACX;UACJ;QACJ,CAAE;QAAAT,QAAA,eAEFT,OAAA,CAACH,KAAK;UACF2B,cAAc,EAAE;YACZC,UAAU,EAAE;cACRC,cAAc,EAAE5B;YACpB;UACJ,CAAE;UACF8B,QAAQ;UACRD,IAAI,EAAE,UAAUrB,KAAK;QAAU;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACZjB,OAAA,CAACT,SAAS;QAAAkB,QAAA,eACNT,OAAA,CAACV,KAAK;UAACuC,SAAS,EAAC,KAAK;UAACC,cAAc,EAAC,QAAQ;UAACC,UAAU,EAAC,QAAQ;UAAAtB,QAAA,eAC9DT,OAAA,CAACP,OAAO;YAACuC,SAAS,EAAC,KAAK;YAACC,KAAK,eAAEjC,OAAA,CAACZ,gBAAgB;cAAC8C,EAAE,EAAC;YAAQ;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACkB,OAAO,EAAEA,CAAA,KAAM5B,MAAM,CAACD,KAAK,CAAE;YAAAG,QAAA,eAC3FT,OAAA,CAACX,UAAU;cAAC,cAAW,QAAQ;cAAC+C,IAAI,EAAC,OAAO;cAAA3B,QAAA,eACxCT,OAAA,CAACL,iBAAiB;gBAACe,EAAE,EAAE;kBAAE2B,QAAQ,EAAE;gBAAS;cAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC,gBACb,CAAC;AAEX,CAAC;AAACZ,EAAA,CAhHIF,cAAc;EAAA,QAEFT,QAAQ;AAAA;AAAA4C,EAAA,GAFpBnC,cAAc;AAkHpB,eAAeA,cAAc;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}