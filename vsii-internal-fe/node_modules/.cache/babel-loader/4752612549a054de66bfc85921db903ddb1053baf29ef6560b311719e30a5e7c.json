{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/ButtonExport.tsx\";\n// material-ui\nimport { Button, Stack, Typography } from '@mui/material';\n\n// project imports\nimport { exportDocument } from 'utils/common';\nimport { Download } from './icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ButtonExport = props => {\n  const {\n    label,\n    url,\n    query\n  } = props;\n  const handleDownload = () => {\n    exportDocument(url, query);\n  };\n  return /*#__PURE__*/_jsxDEV(Button, {\n    onClick: handleDownload,\n    children: /*#__PURE__*/_jsxDEV(Stack, {\n      alignItems: \"center\",\n      direction: \"row\",\n      gap: 1,\n      children: [/*#__PURE__*/_jsxDEV(Download, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        fontWeight: 500,\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 9\n  }, this);\n};\n_c = ButtonExport;\nexport default ButtonExport;\nvar _c;\n$RefreshReg$(_c, \"ButtonExport\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Typography", "exportDocument", "Download", "jsxDEV", "_jsxDEV", "ButtonExport", "props", "label", "url", "query", "handleDownload", "onClick", "children", "alignItems", "direction", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontWeight", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/ButtonExport.tsx"], "sourcesContent": ["// material-ui\nimport { Button, Stack, Typography } from '@mui/material';\n\n// project imports\nimport { exportDocument } from 'utils/common';\nimport { Download } from './icons';\n\ninterface IButtonExportProps {\n    label: string;\n    url: string;\n    query: any;\n}\n\nconst ButtonExport = (props: IButtonExportProps) => {\n    const { label, url, query } = props;\n\n    const handleDownload = () => {\n        exportDocument(url, query);\n    };\n\n    return (\n        <Button onClick={handleDownload}>\n            <Stack alignItems=\"center\" direction=\"row\" gap={1}>\n                <Download />\n                <Typography fontWeight={500}>{label}</Typography>\n            </Stack>\n        </Button>\n    );\n};\n\nexport default ButtonExport;\n"], "mappings": ";AAAA;AACA,SAASA,MAAM,EAAEC,KAAK,EAAEC,UAAU,QAAQ,eAAe;;AAEzD;AACA,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,QAAQ,QAAQ,SAAS;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQnC,MAAMC,YAAY,GAAIC,KAAyB,IAAK;EAChD,MAAM;IAAEC,KAAK;IAAEC,GAAG;IAAEC;EAAM,CAAC,GAAGH,KAAK;EAEnC,MAAMI,cAAc,GAAGA,CAAA,KAAM;IACzBT,cAAc,CAACO,GAAG,EAAEC,KAAK,CAAC;EAC9B,CAAC;EAED,oBACIL,OAAA,CAACN,MAAM;IAACa,OAAO,EAAED,cAAe;IAAAE,QAAA,eAC5BR,OAAA,CAACL,KAAK;MAACc,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,KAAK;MAACC,GAAG,EAAE,CAAE;MAAAH,QAAA,gBAC9CR,OAAA,CAACF,QAAQ;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACZf,OAAA,CAACJ,UAAU;QAACoB,UAAU,EAAE,GAAI;QAAAR,QAAA,EAAEL;MAAK;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEjB,CAAC;AAACE,EAAA,GAfIhB,YAAY;AAiBlB,eAAeA,YAAY;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}