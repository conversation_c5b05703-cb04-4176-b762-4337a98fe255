{"ast": null, "code": "import { createProjectionNode } from './create-projection-node.mjs';\nimport { addDomEvent } from '../../events/use-dom-event.mjs';\nconst DocumentProjectionNode = createProjectionNode({\n  attachResizeListener: (ref, notify) => addDomEvent(ref, \"resize\", notify),\n  measureScroll: () => ({\n    x: document.documentElement.scrollLeft || document.body.scrollLeft,\n    y: document.documentElement.scrollTop || document.body.scrollTop\n  }),\n  checkIsScrollRoot: () => true\n});\nexport { DocumentProjectionNode };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}