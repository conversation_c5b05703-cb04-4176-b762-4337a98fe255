{"ast": null, "code": "import { repeat, getMagnitude } from '../utils';\nexport function ToRawPrecision(x, minPrecision, maxPrecision) {\n  var p = maxPrecision;\n  var m;\n  var e;\n  var xFinal;\n  if (x === 0) {\n    m = repeat('0', p);\n    e = 0;\n    xFinal = 0;\n  } else {\n    var xToString = x.toString();\n    // If xToString is formatted as scientific notation, the number is either very small or very\n    // large. If the precision of the formatted string is lower that requested max precision, we\n    // should still infer them from the formatted string, otherwise the formatted result might have\n    // precision loss (e.g. 1e41 will not have 0 in every trailing digits).\n    var xToStringExponentIndex = xToString.indexOf('e');\n    var _a = xToString.split('e'),\n      xToStringMantissa = _a[0],\n      xToStringExponent = _a[1];\n    var xToStringMantissaWithoutDecimalPoint = xToStringMantissa.replace('.', '');\n    if (xToStringExponentIndex >= 0 && xToStringMantissaWithoutDecimalPoint.length <= p) {\n      e = +xToStringExponent;\n      m = xToStringMantissaWithoutDecimalPoint + repeat('0', p - xToStringMantissaWithoutDecimalPoint.length);\n      xFinal = x;\n    } else {\n      e = getMagnitude(x);\n      var decimalPlaceOffset = e - p + 1;\n      // n is the integer containing the required precision digits. To derive the formatted string,\n      // we will adjust its decimal place in the logic below.\n      var n = Math.round(adjustDecimalPlace(x, decimalPlaceOffset));\n      // The rounding caused the change of magnitude, so we should increment `e` by 1.\n      if (adjustDecimalPlace(n, p - 1) >= 10) {\n        e = e + 1;\n        // Divide n by 10 to swallow one precision.\n        n = Math.floor(n / 10);\n      }\n      m = n.toString();\n      // Equivalent of n * 10 ** (e - p + 1)\n      xFinal = adjustDecimalPlace(n, p - 1 - e);\n    }\n  }\n  var int;\n  if (e >= p - 1) {\n    m = m + repeat('0', e - p + 1);\n    int = e + 1;\n  } else if (e >= 0) {\n    m = \"\".concat(m.slice(0, e + 1), \".\").concat(m.slice(e + 1));\n    int = e + 1;\n  } else {\n    m = \"0.\".concat(repeat('0', -e - 1)).concat(m);\n    int = 1;\n  }\n  if (m.indexOf('.') >= 0 && maxPrecision > minPrecision) {\n    var cut = maxPrecision - minPrecision;\n    while (cut > 0 && m[m.length - 1] === '0') {\n      m = m.slice(0, -1);\n      cut--;\n    }\n    if (m[m.length - 1] === '.') {\n      m = m.slice(0, -1);\n    }\n  }\n  return {\n    formattedString: m,\n    roundedNumber: xFinal,\n    integerDigitsCount: int\n  };\n  // x / (10 ** magnitude), but try to preserve as much floating point precision as possible.\n  function adjustDecimalPlace(x, magnitude) {\n    return magnitude < 0 ? x * Math.pow(10, -magnitude) : x / Math.pow(10, magnitude);\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}