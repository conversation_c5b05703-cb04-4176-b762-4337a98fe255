{"ast": null, "code": "import { getPickersLocalization } from './utils/getPickersLocalization'; // This object is not Partial<PickersLocaleText> because it is the default values\n\nconst enUSPickers = {\n  // Calendar navigation\n  previousMonth: 'Previous month',\n  nextMonth: 'Next month',\n  // View navigation\n  openPreviousView: 'open previous view',\n  openNextView: 'open next view',\n  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'year view is open, switch to calendar view' : 'calendar view is open, switch to year view',\n  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `text input view is open, go to ${viewType} view` : `${viewType} view is open, go to text input view`,\n  // DateRange placeholders\n  start: 'Start',\n  end: 'End',\n  // Action bar\n  cancelButtonLabel: 'Cancel',\n  clearButtonLabel: 'Clear',\n  okButtonLabel: 'OK',\n  todayButtonLabel: 'Today',\n  // Toolbar titles\n  datePickerDefaultToolbarTitle: 'Select date',\n  dateTimePickerDefaultToolbarTitle: 'Select date & time',\n  timePickerDefaultToolbarTitle: 'Select time',\n  dateRangePickerDefaultToolbarTitle: 'Select date range',\n  // Clock labels\n  clockLabelText: (view, time, adapter) => `Select ${view}. ${time === null ? 'No time selected' : `Selected time is ${adapter.format(time, 'fullTime')}`}`,\n  hoursClockNumberText: hours => `${hours} hours`,\n  minutesClockNumberText: minutes => `${minutes} minutes`,\n  secondsClockNumberText: seconds => `${seconds} seconds`,\n  // Open picker labels\n  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Choose date, selected date is ${utils.format(utils.date(rawValue), 'fullDate')}` : 'Choose date',\n  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Choose time, selected time is ${utils.format(utils.date(rawValue), 'fullTime')}` : 'Choose time',\n  // Table labels\n  timeTableLabel: 'pick time',\n  dateTableLabel: 'pick date'\n};\nexport const DEFAULT_LOCALE = enUSPickers;\nexport const enUS = getPickersLocalization(enUSPickers);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}