{"ast": null, "code": "import { useEffect } from 'react';\nimport { VisualElementDragControls } from './VisualElementDragControls.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\n\n/**\n * A hook that allows an element to be dragged.\n *\n * @internal\n */\nfunction useDrag(props) {\n  const {\n    dragControls: groupDragControls,\n    visualElement\n  } = props;\n  const dragControls = useConstant(() => new VisualElementDragControls(visualElement));\n  // If we've been provided a DragControls for manual control over the drag gesture,\n  // subscribe this component to it on mount.\n  useEffect(() => groupDragControls && groupDragControls.subscribe(dragControls), [dragControls, groupDragControls]);\n  // Apply the event listeners to the element\n  useEffect(() => dragControls.addListeners(), [dragControls]);\n}\nexport { useDrag };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}