{"ast": null, "code": "import{useSearchParams}from'react-router-dom';import{useCallback,useEffect,useState}from'react';import{useIntl}from'react-intl';import{getNBMByMember,nonBillableMonitoringSelector}from'store/slice/nonBillableMonitoringSlice';import{openCommentDialog,isCommentedSelector,changeCommented}from'store/slice/commentSlice';import{convertWeekFromToDate,getNumberOfWeek}from'utils/date';import{nonBillByMemberDefault}from'../Config';import{exportDocument,transformObject}from'utils/common';import{checkAllowedPermission}from'utils/authorization';import{useAppDispatch,useAppSelector}from'app/hooks';import{FilterCollapse}from'containers/search';import{PERMISSIONS}from'constants/Permission';import{Table}from'components/extended/Table';import MainCard from'components/cards/MainCard';import{REPORT_TYPE,TEXT_CONFIG_SCREEN}from'constants/Common';import Api from'constants/Api';import{NonBillByMemberNote,NonBillByMemberSearch,NonBillByMemberThead,NonBillByMemberTotal,NonBillByMemberTBody}from'containers/non-billable-monitoring';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const NonBillByMemberTab=_ref=>{let{weeks,defaultConditions,formReset,params,handleChangeYear,getWeekandYearWhenSearch}=_ref;const[conditions,setConditions]=useState(defaultConditions);const{NBMByMember,loading}=useAppSelector(nonBillableMonitoringSelector);const{nBMByMember}=TEXT_CONFIG_SCREEN.nonBillablemonitoring;const dispatch=useAppDispatch();const intl=useIntl();const[,setSearchParams]=useSearchParams();const{nonBillable}=PERMISSIONS.report;const isCommented=useAppSelector(isCommentedSelector);const handleOpenCommentDialog=(userId,subTitle)=>{const updatedConditions={...conditions,userId,reportType:REPORT_TYPE.RP_NON_BILLABLE_MONITORING};const titleDetail=conditions!==null&&conditions!==void 0&&conditions.week?\"\".concat(getNumberOfWeek(conditions.week),\" - \").concat(conditions.year):'';dispatch(openCommentDialog({conditions:updatedConditions,titleDetail:userId?subTitle:intl.formatMessage({id:'week'})+' '+titleDetail}));};const handleExportDocument=()=>{exportDocument(Api.non_billable_monitoring.getDownload.url,{...convertWeekFromToDate(conditions.week),weekNumber:getNumberOfWeek(conditions.week),year:conditions.year});};// Handle submit\nconst handleSearch=value=>{transformObject(value);setSearchParams({...params,...value});setConditions(value);getWeekandYearWhenSearch===null||getWeekandYearWhenSearch===void 0?void 0:getWeekandYearWhenSearch(value.week,value.year);};const getTableData=useCallback(()=>{const weekSelected=convertWeekFromToDate(conditions.week);dispatch(getNBMByMember({...weekSelected,...transformObject({...conditions},['tab','week']),reportType:REPORT_TYPE.RP_NON_BILLABLE_MONITORING}));},[conditions,dispatch]);useEffect(getTableData,[getTableData]);useEffect(()=>{if(isCommented){getTableData();dispatch(changeCommented(false));}},[isCommented,dispatch,getTableData]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FilterCollapse,{downloadLabel:nBMByMember+'download',commentLabel:nBMByMember+'commnents',handleExport:checkAllowedPermission(nonBillable.download)?handleExportDocument:undefined,handleOpenCommentDialog:checkAllowedPermission(nonBillable.comment)?handleOpenCommentDialog:undefined,children:/*#__PURE__*/_jsx(NonBillByMemberSearch,{conditions:formReset,weeks:weeks,handleChangeYear:handleChangeYear,handleSearch:handleSearch})}),/*#__PURE__*/_jsx(NonBillByMemberNote,{}),/*#__PURE__*/_jsx(NonBillByMemberTotal,{nonBillByMember:NBMByMember,isLoading:loading[getNBMByMember.typePrefix]}),/*#__PURE__*/_jsx(MainCard,{children:/*#__PURE__*/_jsx(Table,{heads:/*#__PURE__*/_jsx(NonBillByMemberThead,{}),isLoading:loading[getNBMByMember.typePrefix],data:NBMByMember?NBMByMember.data:nonBillByMemberDefault.data,children:/*#__PURE__*/_jsx(NonBillByMemberTBody,{handleOpenCommentDialog:handleOpenCommentDialog,data:NBMByMember!==null&&NBMByMember!==void 0&&NBMByMember.data?NBMByMember.data:nonBillByMemberDefault.data})})})]});};export default NonBillByMemberTab;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}