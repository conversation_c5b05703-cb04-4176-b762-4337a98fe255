{"ast": null, "code": "// React imports\nimport{useEffect,useState}from'react';// Project imports\nimport{Select}from'components/extended/Form';// Types\nimport{DEFAULT_VALUE_OPTION_SELECT,GROUP_ID_APPROVER}from'constants/Common';import sendRequest from'services/ApiService';import Api from'constants/Api';import{jsx as _jsx}from\"react/jsx-runtime\";const DirectApprover=props=>{const{name,required,disabled,onChange,label,idHexString}=props;const[managers,setManagers]=useState([DEFAULT_VALUE_OPTION_SELECT]);async function fetchManagers(){const response=await sendRequest(Api.member.getAll,{groupId:GROUP_ID_APPROVER.MANAGEMENT_LEVEL,size:1000});if(response!==null&&response!==void 0&&response.status){const{result}=response;const arrOptions=[DEFAULT_VALUE_OPTION_SELECT];result.content.filter(user=>user.idHexString!==idHexString).forEach(user=>{const managerOption={value:user.idHexString,label:user.fullName||\"\".concat(user.firstName,\" \").concat(user.lastName).trim()};arrOptions.push(managerOption);});setManagers(arrOptions);}}const handleChange=e=>{const value=e.target.value;onChange&&onChange(value);};useEffect(()=>{fetchManagers();},[]);return/*#__PURE__*/_jsx(Select,{disabled:disabled,required:required,selects:managers,handleChange:handleChange,name:name,label:label});};DirectApprover.defaultProps={required:false,disabled:false};export default DirectApprover;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}