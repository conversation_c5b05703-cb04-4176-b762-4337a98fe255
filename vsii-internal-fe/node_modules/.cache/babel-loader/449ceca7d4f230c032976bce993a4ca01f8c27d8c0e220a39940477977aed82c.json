{"ast": null, "code": "import{FormattedMessage}from'react-intl';import{TableCell,TableHead,TableRow}from'@mui/material';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ColumnConfigTHead=()=>{const{column_config}=TEXT_CONFIG_SCREEN.administration.flexibleReport;return/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{sx:{width:'5%'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:column_config+'no'})}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'15%'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:column_config+'report-name'})}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'20%'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:column_config+'column-name'})}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'15%'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:column_config+'input-type'})}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'15%'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:column_config+'calculate'})}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",sx:{width:'10%'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:column_config+'actions'})})]})});};export default ColumnConfigTHead;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}