{"ast": null, "code": "import { getPickersLocalization } from './utils/getPickersLocalization';\nconst svSEPickers = {\n  // Calendar navigation\n  previousMonth: 'Föregående månad',\n  nextMonth: '<PERSON><PERSON><PERSON> månad',\n  // View navigation\n  openPreviousView: 'öppna föregående vy',\n  openNextView: 'öppna nästa vy',\n  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'årsvyn är öppen, byt till kalendervy' : 'kalendervyn är öppen, byt till årsvy',\n  // inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `text input view is open, go to ${viewType} view` : `${viewType} view is open, go to text input view`,\n  // DateRange placeholders\n  start: 'Start',\n  end: 'Slut',\n  // Action bar\n  cancelButtonLabel: 'Avbryt',\n  clearButtonLabel: 'Rensa',\n  okButtonLabel: 'OK',\n  todayButtonLabel: 'Idag',\n  // Toolbar titles\n  // datePickerDefaultToolbarTitle: 'Select date',\n  // dateTimePickerDefaultToolbarTitle: 'Select date & time',\n  // timePickerDefaultToolbarTitle: 'Select time',\n  // dateRangePickerDefaultToolbarTitle: 'Select date range',\n  // Clock labels\n  clockLabelText: (view, time, adapter) => \"Select \".concat(view, \". \").concat(time === null ? 'Ingen tid vald' : \"Vald tid \\xE4r \".concat(adapter.format(time, 'fullTime'))),\n  hoursClockNumberText: hours => \"\".concat(hours, \" timmar\"),\n  minutesClockNumberText: minutes => \"\".concat(minutes, \" minuter\"),\n  secondsClockNumberText: seconds => \"\".concat(seconds, \" sekunder\"),\n  // Open picker labels\n  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? \"V\\xE4lj datum, valt datum \\xE4r \".concat(utils.format(utils.date(rawValue), 'fullDate')) : 'Välj datum',\n  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? \"V\\xE4lj tid, vald tid \\xE4r \".concat(utils.format(utils.date(rawValue), 'fullTime')) : 'Välj tid',\n  // Table labels\n  timeTableLabel: 'välj tid',\n  dateTableLabel: 'välj datum'\n};\nexport const svSE = getPickersLocalization(svSEPickers);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}