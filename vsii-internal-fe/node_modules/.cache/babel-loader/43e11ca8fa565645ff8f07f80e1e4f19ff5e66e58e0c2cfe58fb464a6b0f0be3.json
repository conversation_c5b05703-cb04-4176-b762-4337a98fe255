{"ast": null, "code": "import { FormatNumericToString } from './FormatNumericToString';\nimport { SameValue } from '../262';\nimport { ComputeExponent } from './ComputeExponent';\nimport formatToParts from './format_to_parts';\n/**\n * https://tc39.es/ecma402/#sec-formatnumberstring\n */\nexport function PartitionNumberPattern(numberFormat, x, _a) {\n  var _b;\n  var getInternalSlots = _a.getInternalSlots;\n  var internalSlots = getInternalSlots(numberFormat);\n  var pl = internalSlots.pl,\n    dataLocaleData = internalSlots.dataLocaleData,\n    numberingSystem = internalSlots.numberingSystem;\n  var symbols = dataLocaleData.numbers.symbols[numberingSystem] || dataLocaleData.numbers.symbols[dataLocaleData.numbers.nu[0]];\n  var magnitude = 0;\n  var exponent = 0;\n  var n;\n  if (isNaN(x)) {\n    n = symbols.nan;\n  } else if (x == Number.POSITIVE_INFINITY || x == Number.NEGATIVE_INFINITY) {\n    n = symbols.infinity;\n  } else {\n    if (!SameValue(x, -0)) {\n      if (!isFinite(x)) {\n        throw new Error('Input must be a mathematical value');\n      }\n      if (internalSlots.style == 'percent') {\n        x *= 100;\n      }\n      ;\n      _b = ComputeExponent(numberFormat, x, {\n        getInternalSlots: getInternalSlots\n      }), exponent = _b[0], magnitude = _b[1];\n      // Preserve more precision by doing multiplication when exponent is negative.\n      x = exponent < 0 ? x * Math.pow(10, -exponent) : x / Math.pow(10, exponent);\n    }\n    var formatNumberResult = FormatNumericToString(internalSlots, x);\n    n = formatNumberResult.formattedString;\n    x = formatNumberResult.roundedNumber;\n  }\n  // Based on https://tc39.es/ecma402/#sec-getnumberformatpattern\n  // We need to do this before `x` is rounded.\n  var sign;\n  var signDisplay = internalSlots.signDisplay;\n  switch (signDisplay) {\n    case 'never':\n      sign = 0;\n      break;\n    case 'auto':\n      if (SameValue(x, 0) || x > 0 || isNaN(x)) {\n        sign = 0;\n      } else {\n        sign = -1;\n      }\n      break;\n    case 'always':\n      if (SameValue(x, 0) || x > 0 || isNaN(x)) {\n        sign = 1;\n      } else {\n        sign = -1;\n      }\n      break;\n    default:\n      // x === 0 -> x is 0 or x is -0\n      if (x === 0 || isNaN(x)) {\n        sign = 0;\n      } else if (x > 0) {\n        sign = 1;\n      } else {\n        sign = -1;\n      }\n  }\n  return formatToParts({\n    roundedNumber: x,\n    formattedString: n,\n    exponent: exponent,\n    magnitude: magnitude,\n    sign: sign\n  }, internalSlots.dataLocaleData, pl, internalSlots);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}