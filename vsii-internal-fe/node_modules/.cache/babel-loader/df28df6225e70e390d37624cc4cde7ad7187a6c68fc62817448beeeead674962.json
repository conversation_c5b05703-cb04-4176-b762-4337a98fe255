{"ast": null, "code": "import { invariant } from 'hey-listen';\nimport { cubicBezier } from '../../easing/cubic-bezier.mjs';\nimport { noop } from '../../utils/noop.mjs';\nimport { easeIn, easeInOut, easeOut } from '../../easing/ease.mjs';\nimport { circIn, circInOut, circOut } from '../../easing/circ.mjs';\nimport { backIn, backInOut, backOut } from '../../easing/back.mjs';\nimport { anticipate } from '../../easing/anticipate.mjs';\nconst easingLookup = {\n  linear: noop,\n  easeIn,\n  easeInOut,\n  easeOut,\n  circIn,\n  circInOut,\n  circOut,\n  backIn,\n  backInOut,\n  backOut,\n  anticipate\n};\nconst easingDefinitionToFunction = definition => {\n  if (Array.isArray(definition)) {\n    // If cubic bezier definition, create bezier curve\n    invariant(definition.length === 4, \"Cubic bezier arrays must contain four numerical values.\");\n    const [x1, y1, x2, y2] = definition;\n    return cubicBezier(x1, y1, x2, y2);\n  } else if (typeof definition === \"string\") {\n    // Else lookup from table\n    invariant(easingLookup[definition] !== undefined, \"Invalid easing type '\".concat(definition, \"'\"));\n    return easingLookup[definition];\n  }\n  return definition;\n};\nconst isEasingArray = ease => {\n  return Array.isArray(ease) && typeof ease[0] !== \"number\";\n};\nexport { easingDefinitionToFunction, isEasingArray };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}