{"ast": null, "code": "/*!\n * perfect-scrollbar v1.5.3\n * Copyright 2021 <PERSON><PERSON><PERSON>, MDBootstrap and Contributors\n * Licensed under MIT\n */\n\nfunction get(element) {\n  return getComputedStyle(element);\n}\nfunction set(element, obj) {\n  for (var key in obj) {\n    var val = obj[key];\n    if (typeof val === 'number') {\n      val = val + \"px\";\n    }\n    element.style[key] = val;\n  }\n  return element;\n}\nfunction div(className) {\n  var div = document.createElement('div');\n  div.className = className;\n  return div;\n}\nvar elMatches = typeof Element !== 'undefined' && (Element.prototype.matches || Element.prototype.webkitMatchesSelector || Element.prototype.mozMatchesSelector || Element.prototype.msMatchesSelector);\nfunction matches(element, query) {\n  if (!elMatches) {\n    throw new Error('No element matching method supported');\n  }\n  return elMatches.call(element, query);\n}\nfunction remove(element) {\n  if (element.remove) {\n    element.remove();\n  } else {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element);\n    }\n  }\n}\nfunction queryChildren(element, selector) {\n  return Array.prototype.filter.call(element.children, function (child) {\n    return matches(child, selector);\n  });\n}\nvar cls = {\n  main: 'ps',\n  rtl: 'ps__rtl',\n  element: {\n    thumb: function (x) {\n      return \"ps__thumb-\" + x;\n    },\n    rail: function (x) {\n      return \"ps__rail-\" + x;\n    },\n    consuming: 'ps__child--consume'\n  },\n  state: {\n    focus: 'ps--focus',\n    clicking: 'ps--clicking',\n    active: function (x) {\n      return \"ps--active-\" + x;\n    },\n    scrolling: function (x) {\n      return \"ps--scrolling-\" + x;\n    }\n  }\n};\n\n/*\n * Helper methods\n */\nvar scrollingClassTimeout = {\n  x: null,\n  y: null\n};\nfunction addScrollingClass(i, x) {\n  var classList = i.element.classList;\n  var className = cls.state.scrolling(x);\n  if (classList.contains(className)) {\n    clearTimeout(scrollingClassTimeout[x]);\n  } else {\n    classList.add(className);\n  }\n}\nfunction removeScrollingClass(i, x) {\n  scrollingClassTimeout[x] = setTimeout(function () {\n    return i.isAlive && i.element.classList.remove(cls.state.scrolling(x));\n  }, i.settings.scrollingThreshold);\n}\nfunction setScrollingClassInstantly(i, x) {\n  addScrollingClass(i, x);\n  removeScrollingClass(i, x);\n}\nvar EventElement = function EventElement(element) {\n  this.element = element;\n  this.handlers = {};\n};\nvar prototypeAccessors = {\n  isEmpty: {\n    configurable: true\n  }\n};\nEventElement.prototype.bind = function bind(eventName, handler) {\n  if (typeof this.handlers[eventName] === 'undefined') {\n    this.handlers[eventName] = [];\n  }\n  this.handlers[eventName].push(handler);\n  this.element.addEventListener(eventName, handler, false);\n};\nEventElement.prototype.unbind = function unbind(eventName, target) {\n  var this$1 = this;\n  this.handlers[eventName] = this.handlers[eventName].filter(function (handler) {\n    if (target && handler !== target) {\n      return true;\n    }\n    this$1.element.removeEventListener(eventName, handler, false);\n    return false;\n  });\n};\nEventElement.prototype.unbindAll = function unbindAll() {\n  for (var name in this.handlers) {\n    this.unbind(name);\n  }\n};\nprototypeAccessors.isEmpty.get = function () {\n  var this$1 = this;\n  return Object.keys(this.handlers).every(function (key) {\n    return this$1.handlers[key].length === 0;\n  });\n};\nObject.defineProperties(EventElement.prototype, prototypeAccessors);\nvar EventManager = function EventManager() {\n  this.eventElements = [];\n};\nEventManager.prototype.eventElement = function eventElement(element) {\n  var ee = this.eventElements.filter(function (ee) {\n    return ee.element === element;\n  })[0];\n  if (!ee) {\n    ee = new EventElement(element);\n    this.eventElements.push(ee);\n  }\n  return ee;\n};\nEventManager.prototype.bind = function bind(element, eventName, handler) {\n  this.eventElement(element).bind(eventName, handler);\n};\nEventManager.prototype.unbind = function unbind(element, eventName, handler) {\n  var ee = this.eventElement(element);\n  ee.unbind(eventName, handler);\n  if (ee.isEmpty) {\n    // remove\n    this.eventElements.splice(this.eventElements.indexOf(ee), 1);\n  }\n};\nEventManager.prototype.unbindAll = function unbindAll() {\n  this.eventElements.forEach(function (e) {\n    return e.unbindAll();\n  });\n  this.eventElements = [];\n};\nEventManager.prototype.once = function once(element, eventName, handler) {\n  var ee = this.eventElement(element);\n  var onceHandler = function (evt) {\n    ee.unbind(eventName, onceHandler);\n    handler(evt);\n  };\n  ee.bind(eventName, onceHandler);\n};\nfunction createEvent(name) {\n  if (typeof window.CustomEvent === 'function') {\n    return new CustomEvent(name);\n  } else {\n    var evt = document.createEvent('CustomEvent');\n    evt.initCustomEvent(name, false, false, undefined);\n    return evt;\n  }\n}\nfunction processScrollDiff(i, axis, diff, useScrollingClass, forceFireReachEvent) {\n  if (useScrollingClass === void 0) useScrollingClass = true;\n  if (forceFireReachEvent === void 0) forceFireReachEvent = false;\n  var fields;\n  if (axis === 'top') {\n    fields = ['contentHeight', 'containerHeight', 'scrollTop', 'y', 'up', 'down'];\n  } else if (axis === 'left') {\n    fields = ['contentWidth', 'containerWidth', 'scrollLeft', 'x', 'left', 'right'];\n  } else {\n    throw new Error('A proper axis should be provided');\n  }\n  processScrollDiff$1(i, diff, fields, useScrollingClass, forceFireReachEvent);\n}\nfunction processScrollDiff$1(i, diff, ref, useScrollingClass, forceFireReachEvent) {\n  var contentHeight = ref[0];\n  var containerHeight = ref[1];\n  var scrollTop = ref[2];\n  var y = ref[3];\n  var up = ref[4];\n  var down = ref[5];\n  if (useScrollingClass === void 0) useScrollingClass = true;\n  if (forceFireReachEvent === void 0) forceFireReachEvent = false;\n  var element = i.element;\n\n  // reset reach\n  i.reach[y] = null;\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] < 1) {\n    i.reach[y] = 'start';\n  }\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] > i[contentHeight] - i[containerHeight] - 1) {\n    i.reach[y] = 'end';\n  }\n  if (diff) {\n    element.dispatchEvent(createEvent(\"ps-scroll-\" + y));\n    if (diff < 0) {\n      element.dispatchEvent(createEvent(\"ps-scroll-\" + up));\n    } else if (diff > 0) {\n      element.dispatchEvent(createEvent(\"ps-scroll-\" + down));\n    }\n    if (useScrollingClass) {\n      setScrollingClassInstantly(i, y);\n    }\n  }\n  if (i.reach[y] && (diff || forceFireReachEvent)) {\n    element.dispatchEvent(createEvent(\"ps-\" + y + \"-reach-\" + i.reach[y]));\n  }\n}\nfunction toInt(x) {\n  return parseInt(x, 10) || 0;\n}\nfunction isEditable(el) {\n  return matches(el, 'input,[contenteditable]') || matches(el, 'select,[contenteditable]') || matches(el, 'textarea,[contenteditable]') || matches(el, 'button,[contenteditable]');\n}\nfunction outerWidth(element) {\n  var styles = get(element);\n  return toInt(styles.width) + toInt(styles.paddingLeft) + toInt(styles.paddingRight) + toInt(styles.borderLeftWidth) + toInt(styles.borderRightWidth);\n}\nvar env = {\n  isWebKit: typeof document !== 'undefined' && 'WebkitAppearance' in document.documentElement.style,\n  supportsTouch: typeof window !== 'undefined' && ('ontouchstart' in window || 'maxTouchPoints' in window.navigator && window.navigator.maxTouchPoints > 0 || window.DocumentTouch && document instanceof window.DocumentTouch),\n  supportsIePointer: typeof navigator !== 'undefined' && navigator.msMaxTouchPoints,\n  isChrome: typeof navigator !== 'undefined' && /Chrome/i.test(navigator && navigator.userAgent)\n};\nfunction updateGeometry(i) {\n  var element = i.element;\n  var roundedScrollTop = Math.floor(element.scrollTop);\n  var rect = element.getBoundingClientRect();\n  i.containerWidth = Math.round(rect.width);\n  i.containerHeight = Math.round(rect.height);\n  i.contentWidth = element.scrollWidth;\n  i.contentHeight = element.scrollHeight;\n  if (!element.contains(i.scrollbarXRail)) {\n    // clean up and append\n    queryChildren(element, cls.element.rail('x')).forEach(function (el) {\n      return remove(el);\n    });\n    element.appendChild(i.scrollbarXRail);\n  }\n  if (!element.contains(i.scrollbarYRail)) {\n    // clean up and append\n    queryChildren(element, cls.element.rail('y')).forEach(function (el) {\n      return remove(el);\n    });\n    element.appendChild(i.scrollbarYRail);\n  }\n  if (!i.settings.suppressScrollX && i.containerWidth + i.settings.scrollXMarginOffset < i.contentWidth) {\n    i.scrollbarXActive = true;\n    i.railXWidth = i.containerWidth - i.railXMarginWidth;\n    i.railXRatio = i.containerWidth / i.railXWidth;\n    i.scrollbarXWidth = getThumbSize(i, toInt(i.railXWidth * i.containerWidth / i.contentWidth));\n    i.scrollbarXLeft = toInt((i.negativeScrollAdjustment + element.scrollLeft) * (i.railXWidth - i.scrollbarXWidth) / (i.contentWidth - i.containerWidth));\n  } else {\n    i.scrollbarXActive = false;\n  }\n  if (!i.settings.suppressScrollY && i.containerHeight + i.settings.scrollYMarginOffset < i.contentHeight) {\n    i.scrollbarYActive = true;\n    i.railYHeight = i.containerHeight - i.railYMarginHeight;\n    i.railYRatio = i.containerHeight / i.railYHeight;\n    i.scrollbarYHeight = getThumbSize(i, toInt(i.railYHeight * i.containerHeight / i.contentHeight));\n    i.scrollbarYTop = toInt(roundedScrollTop * (i.railYHeight - i.scrollbarYHeight) / (i.contentHeight - i.containerHeight));\n  } else {\n    i.scrollbarYActive = false;\n  }\n  if (i.scrollbarXLeft >= i.railXWidth - i.scrollbarXWidth) {\n    i.scrollbarXLeft = i.railXWidth - i.scrollbarXWidth;\n  }\n  if (i.scrollbarYTop >= i.railYHeight - i.scrollbarYHeight) {\n    i.scrollbarYTop = i.railYHeight - i.scrollbarYHeight;\n  }\n  updateCss(element, i);\n  if (i.scrollbarXActive) {\n    element.classList.add(cls.state.active('x'));\n  } else {\n    element.classList.remove(cls.state.active('x'));\n    i.scrollbarXWidth = 0;\n    i.scrollbarXLeft = 0;\n    element.scrollLeft = i.isRtl === true ? i.contentWidth : 0;\n  }\n  if (i.scrollbarYActive) {\n    element.classList.add(cls.state.active('y'));\n  } else {\n    element.classList.remove(cls.state.active('y'));\n    i.scrollbarYHeight = 0;\n    i.scrollbarYTop = 0;\n    element.scrollTop = 0;\n  }\n}\nfunction getThumbSize(i, thumbSize) {\n  if (i.settings.minScrollbarLength) {\n    thumbSize = Math.max(thumbSize, i.settings.minScrollbarLength);\n  }\n  if (i.settings.maxScrollbarLength) {\n    thumbSize = Math.min(thumbSize, i.settings.maxScrollbarLength);\n  }\n  return thumbSize;\n}\nfunction updateCss(element, i) {\n  var xRailOffset = {\n    width: i.railXWidth\n  };\n  var roundedScrollTop = Math.floor(element.scrollTop);\n  if (i.isRtl) {\n    xRailOffset.left = i.negativeScrollAdjustment + element.scrollLeft + i.containerWidth - i.contentWidth;\n  } else {\n    xRailOffset.left = element.scrollLeft;\n  }\n  if (i.isScrollbarXUsingBottom) {\n    xRailOffset.bottom = i.scrollbarXBottom - roundedScrollTop;\n  } else {\n    xRailOffset.top = i.scrollbarXTop + roundedScrollTop;\n  }\n  set(i.scrollbarXRail, xRailOffset);\n  var yRailOffset = {\n    top: roundedScrollTop,\n    height: i.railYHeight\n  };\n  if (i.isScrollbarYUsingRight) {\n    if (i.isRtl) {\n      yRailOffset.right = i.contentWidth - (i.negativeScrollAdjustment + element.scrollLeft) - i.scrollbarYRight - i.scrollbarYOuterWidth - 9;\n    } else {\n      yRailOffset.right = i.scrollbarYRight - element.scrollLeft;\n    }\n  } else {\n    if (i.isRtl) {\n      yRailOffset.left = i.negativeScrollAdjustment + element.scrollLeft + i.containerWidth * 2 - i.contentWidth - i.scrollbarYLeft - i.scrollbarYOuterWidth;\n    } else {\n      yRailOffset.left = i.scrollbarYLeft + element.scrollLeft;\n    }\n  }\n  set(i.scrollbarYRail, yRailOffset);\n  set(i.scrollbarX, {\n    left: i.scrollbarXLeft,\n    width: i.scrollbarXWidth - i.railBorderXWidth\n  });\n  set(i.scrollbarY, {\n    top: i.scrollbarYTop,\n    height: i.scrollbarYHeight - i.railBorderYWidth\n  });\n}\nfunction clickRail(i) {\n  var element = i.element;\n  i.event.bind(i.scrollbarY, 'mousedown', function (e) {\n    return e.stopPropagation();\n  });\n  i.event.bind(i.scrollbarYRail, 'mousedown', function (e) {\n    var positionTop = e.pageY - window.pageYOffset - i.scrollbarYRail.getBoundingClientRect().top;\n    var direction = positionTop > i.scrollbarYTop ? 1 : -1;\n    i.element.scrollTop += direction * i.containerHeight;\n    updateGeometry(i);\n    e.stopPropagation();\n  });\n  i.event.bind(i.scrollbarX, 'mousedown', function (e) {\n    return e.stopPropagation();\n  });\n  i.event.bind(i.scrollbarXRail, 'mousedown', function (e) {\n    var positionLeft = e.pageX - window.pageXOffset - i.scrollbarXRail.getBoundingClientRect().left;\n    var direction = positionLeft > i.scrollbarXLeft ? 1 : -1;\n    i.element.scrollLeft += direction * i.containerWidth;\n    updateGeometry(i);\n    e.stopPropagation();\n  });\n}\nfunction dragThumb(i) {\n  bindMouseScrollHandler(i, ['containerWidth', 'contentWidth', 'pageX', 'railXWidth', 'scrollbarX', 'scrollbarXWidth', 'scrollLeft', 'x', 'scrollbarXRail']);\n  bindMouseScrollHandler(i, ['containerHeight', 'contentHeight', 'pageY', 'railYHeight', 'scrollbarY', 'scrollbarYHeight', 'scrollTop', 'y', 'scrollbarYRail']);\n}\nfunction bindMouseScrollHandler(i, ref) {\n  var containerHeight = ref[0];\n  var contentHeight = ref[1];\n  var pageY = ref[2];\n  var railYHeight = ref[3];\n  var scrollbarY = ref[4];\n  var scrollbarYHeight = ref[5];\n  var scrollTop = ref[6];\n  var y = ref[7];\n  var scrollbarYRail = ref[8];\n  var element = i.element;\n  var startingScrollTop = null;\n  var startingMousePageY = null;\n  var scrollBy = null;\n  function mouseMoveHandler(e) {\n    if (e.touches && e.touches[0]) {\n      e[pageY] = e.touches[0].pageY;\n    }\n    element[scrollTop] = startingScrollTop + scrollBy * (e[pageY] - startingMousePageY);\n    addScrollingClass(i, y);\n    updateGeometry(i);\n    e.stopPropagation();\n    if (e.type.startsWith('touch') && e.changedTouches.length > 1) {\n      e.preventDefault();\n    }\n  }\n  function mouseUpHandler() {\n    removeScrollingClass(i, y);\n    i[scrollbarYRail].classList.remove(cls.state.clicking);\n    i.event.unbind(i.ownerDocument, 'mousemove', mouseMoveHandler);\n  }\n  function bindMoves(e, touchMode) {\n    startingScrollTop = element[scrollTop];\n    if (touchMode && e.touches) {\n      e[pageY] = e.touches[0].pageY;\n    }\n    startingMousePageY = e[pageY];\n    scrollBy = (i[contentHeight] - i[containerHeight]) / (i[railYHeight] - i[scrollbarYHeight]);\n    if (!touchMode) {\n      i.event.bind(i.ownerDocument, 'mousemove', mouseMoveHandler);\n      i.event.once(i.ownerDocument, 'mouseup', mouseUpHandler);\n      e.preventDefault();\n    } else {\n      i.event.bind(i.ownerDocument, 'touchmove', mouseMoveHandler);\n    }\n    i[scrollbarYRail].classList.add(cls.state.clicking);\n    e.stopPropagation();\n  }\n  i.event.bind(i[scrollbarY], 'mousedown', function (e) {\n    bindMoves(e);\n  });\n  i.event.bind(i[scrollbarY], 'touchstart', function (e) {\n    bindMoves(e, true);\n  });\n}\nfunction keyboard(i) {\n  var element = i.element;\n  var elementHovered = function () {\n    return matches(element, ':hover');\n  };\n  var scrollbarFocused = function () {\n    return matches(i.scrollbarX, ':focus') || matches(i.scrollbarY, ':focus');\n  };\n  function shouldPreventDefault(deltaX, deltaY) {\n    var scrollTop = Math.floor(element.scrollTop);\n    if (deltaX === 0) {\n      if (!i.scrollbarYActive) {\n        return false;\n      }\n      if (scrollTop === 0 && deltaY > 0 || scrollTop >= i.contentHeight - i.containerHeight && deltaY < 0) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n    var scrollLeft = element.scrollLeft;\n    if (deltaY === 0) {\n      if (!i.scrollbarXActive) {\n        return false;\n      }\n      if (scrollLeft === 0 && deltaX < 0 || scrollLeft >= i.contentWidth - i.containerWidth && deltaX > 0) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n    return true;\n  }\n  i.event.bind(i.ownerDocument, 'keydown', function (e) {\n    if (e.isDefaultPrevented && e.isDefaultPrevented() || e.defaultPrevented) {\n      return;\n    }\n    if (!elementHovered() && !scrollbarFocused()) {\n      return;\n    }\n    var activeElement = document.activeElement ? document.activeElement : i.ownerDocument.activeElement;\n    if (activeElement) {\n      if (activeElement.tagName === 'IFRAME') {\n        activeElement = activeElement.contentDocument.activeElement;\n      } else {\n        // go deeper if element is a webcomponent\n        while (activeElement.shadowRoot) {\n          activeElement = activeElement.shadowRoot.activeElement;\n        }\n      }\n      if (isEditable(activeElement)) {\n        return;\n      }\n    }\n    var deltaX = 0;\n    var deltaY = 0;\n    switch (e.which) {\n      case 37:\n        // left\n        if (e.metaKey) {\n          deltaX = -i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = -i.containerWidth;\n        } else {\n          deltaX = -30;\n        }\n        break;\n      case 38:\n        // up\n        if (e.metaKey) {\n          deltaY = i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = 30;\n        }\n        break;\n      case 39:\n        // right\n        if (e.metaKey) {\n          deltaX = i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = i.containerWidth;\n        } else {\n          deltaX = 30;\n        }\n        break;\n      case 40:\n        // down\n        if (e.metaKey) {\n          deltaY = -i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = -i.containerHeight;\n        } else {\n          deltaY = -30;\n        }\n        break;\n      case 32:\n        // space bar\n        if (e.shiftKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = -i.containerHeight;\n        }\n        break;\n      case 33:\n        // page up\n        deltaY = i.containerHeight;\n        break;\n      case 34:\n        // page down\n        deltaY = -i.containerHeight;\n        break;\n      case 36:\n        // home\n        deltaY = i.contentHeight;\n        break;\n      case 35:\n        // end\n        deltaY = -i.contentHeight;\n        break;\n      default:\n        return;\n    }\n    if (i.settings.suppressScrollX && deltaX !== 0) {\n      return;\n    }\n    if (i.settings.suppressScrollY && deltaY !== 0) {\n      return;\n    }\n    element.scrollTop -= deltaY;\n    element.scrollLeft += deltaX;\n    updateGeometry(i);\n    if (shouldPreventDefault(deltaX, deltaY)) {\n      e.preventDefault();\n    }\n  });\n}\nfunction wheel(i) {\n  var element = i.element;\n  function shouldPreventDefault(deltaX, deltaY) {\n    var roundedScrollTop = Math.floor(element.scrollTop);\n    var isTop = element.scrollTop === 0;\n    var isBottom = roundedScrollTop + element.offsetHeight === element.scrollHeight;\n    var isLeft = element.scrollLeft === 0;\n    var isRight = element.scrollLeft + element.offsetWidth === element.scrollWidth;\n    var hitsBound;\n\n    // pick axis with primary direction\n    if (Math.abs(deltaY) > Math.abs(deltaX)) {\n      hitsBound = isTop || isBottom;\n    } else {\n      hitsBound = isLeft || isRight;\n    }\n    return hitsBound ? !i.settings.wheelPropagation : true;\n  }\n  function getDeltaFromEvent(e) {\n    var deltaX = e.deltaX;\n    var deltaY = -1 * e.deltaY;\n    if (typeof deltaX === 'undefined' || typeof deltaY === 'undefined') {\n      // OS X Safari\n      deltaX = -1 * e.wheelDeltaX / 6;\n      deltaY = e.wheelDeltaY / 6;\n    }\n    if (e.deltaMode && e.deltaMode === 1) {\n      // Firefox in deltaMode 1: Line scrolling\n      deltaX *= 10;\n      deltaY *= 10;\n    }\n    if (deltaX !== deltaX && deltaY !== deltaY /* NaN checks */) {\n      // IE in some mouse drivers\n      deltaX = 0;\n      deltaY = e.wheelDelta;\n    }\n    if (e.shiftKey) {\n      // reverse axis with shift key\n      return [-deltaY, -deltaX];\n    }\n    return [deltaX, deltaY];\n  }\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    // FIXME: this is a workaround for <select> issue in FF and IE #571\n    if (!env.isWebKit && element.querySelector('select:focus')) {\n      return true;\n    }\n    if (!element.contains(target)) {\n      return false;\n    }\n    var cursor = target;\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n      var style = get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        var maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (cursor.scrollTop > 0 && deltaY < 0 || cursor.scrollTop < maxScrollTop && deltaY > 0) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        var maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (cursor.scrollLeft > 0 && deltaX < 0 || cursor.scrollLeft < maxScrollLeft && deltaX > 0) {\n            return true;\n          }\n        }\n      }\n      cursor = cursor.parentNode;\n    }\n    return false;\n  }\n  function mousewheelHandler(e) {\n    var ref = getDeltaFromEvent(e);\n    var deltaX = ref[0];\n    var deltaY = ref[1];\n    if (shouldBeConsumedByChild(e.target, deltaX, deltaY)) {\n      return;\n    }\n    var shouldPrevent = false;\n    if (!i.settings.useBothWheelAxes) {\n      // deltaX will only be used for horizontal scrolling and deltaY will\n      // only be used for vertical scrolling - this is the default\n      element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      element.scrollLeft += deltaX * i.settings.wheelSpeed;\n    } else if (i.scrollbarYActive && !i.scrollbarXActive) {\n      // only vertical scrollbar is active and useBothWheelAxes option is\n      // active, so let's scroll vertical bar using both mouse wheel axes\n      if (deltaY) {\n        element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      } else {\n        element.scrollTop += deltaX * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    } else if (i.scrollbarXActive && !i.scrollbarYActive) {\n      // useBothWheelAxes and only horizontal bar is active, so use both\n      // wheel axes for horizontal bar\n      if (deltaX) {\n        element.scrollLeft += deltaX * i.settings.wheelSpeed;\n      } else {\n        element.scrollLeft -= deltaY * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    }\n    updateGeometry(i);\n    shouldPrevent = shouldPrevent || shouldPreventDefault(deltaX, deltaY);\n    if (shouldPrevent && !e.ctrlKey) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n  }\n  if (typeof window.onwheel !== 'undefined') {\n    i.event.bind(element, 'wheel', mousewheelHandler);\n  } else if (typeof window.onmousewheel !== 'undefined') {\n    i.event.bind(element, 'mousewheel', mousewheelHandler);\n  }\n}\nfunction touch(i) {\n  if (!env.supportsTouch && !env.supportsIePointer) {\n    return;\n  }\n  var element = i.element;\n  function shouldPrevent(deltaX, deltaY) {\n    var scrollTop = Math.floor(element.scrollTop);\n    var scrollLeft = element.scrollLeft;\n    var magnitudeX = Math.abs(deltaX);\n    var magnitudeY = Math.abs(deltaY);\n    if (magnitudeY > magnitudeX) {\n      // user is perhaps trying to swipe up/down the page\n\n      if (deltaY < 0 && scrollTop === i.contentHeight - i.containerHeight || deltaY > 0 && scrollTop === 0) {\n        // set prevent for mobile Chrome refresh\n        return window.scrollY === 0 && deltaY > 0 && env.isChrome;\n      }\n    } else if (magnitudeX > magnitudeY) {\n      // user is perhaps trying to swipe left/right across the page\n\n      if (deltaX < 0 && scrollLeft === i.contentWidth - i.containerWidth || deltaX > 0 && scrollLeft === 0) {\n        return true;\n      }\n    }\n    return true;\n  }\n  function applyTouchMove(differenceX, differenceY) {\n    element.scrollTop -= differenceY;\n    element.scrollLeft -= differenceX;\n    updateGeometry(i);\n  }\n  var startOffset = {};\n  var startTime = 0;\n  var speed = {};\n  var easingLoop = null;\n  function getTouch(e) {\n    if (e.targetTouches) {\n      return e.targetTouches[0];\n    } else {\n      // Maybe IE pointer\n      return e;\n    }\n  }\n  function shouldHandle(e) {\n    if (e.pointerType && e.pointerType === 'pen' && e.buttons === 0) {\n      return false;\n    }\n    if (e.targetTouches && e.targetTouches.length === 1) {\n      return true;\n    }\n    if (e.pointerType && e.pointerType !== 'mouse' && e.pointerType !== e.MSPOINTER_TYPE_MOUSE) {\n      return true;\n    }\n    return false;\n  }\n  function touchStart(e) {\n    if (!shouldHandle(e)) {\n      return;\n    }\n    var touch = getTouch(e);\n    startOffset.pageX = touch.pageX;\n    startOffset.pageY = touch.pageY;\n    startTime = new Date().getTime();\n    if (easingLoop !== null) {\n      clearInterval(easingLoop);\n    }\n  }\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    if (!element.contains(target)) {\n      return false;\n    }\n    var cursor = target;\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n      var style = get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        var maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (cursor.scrollTop > 0 && deltaY < 0 || cursor.scrollTop < maxScrollTop && deltaY > 0) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        var maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (cursor.scrollLeft > 0 && deltaX < 0 || cursor.scrollLeft < maxScrollLeft && deltaX > 0) {\n            return true;\n          }\n        }\n      }\n      cursor = cursor.parentNode;\n    }\n    return false;\n  }\n  function touchMove(e) {\n    if (shouldHandle(e)) {\n      var touch = getTouch(e);\n      var currentOffset = {\n        pageX: touch.pageX,\n        pageY: touch.pageY\n      };\n      var differenceX = currentOffset.pageX - startOffset.pageX;\n      var differenceY = currentOffset.pageY - startOffset.pageY;\n      if (shouldBeConsumedByChild(e.target, differenceX, differenceY)) {\n        return;\n      }\n      applyTouchMove(differenceX, differenceY);\n      startOffset = currentOffset;\n      var currentTime = new Date().getTime();\n      var timeGap = currentTime - startTime;\n      if (timeGap > 0) {\n        speed.x = differenceX / timeGap;\n        speed.y = differenceY / timeGap;\n        startTime = currentTime;\n      }\n      if (shouldPrevent(differenceX, differenceY)) {\n        e.preventDefault();\n      }\n    }\n  }\n  function touchEnd() {\n    if (i.settings.swipeEasing) {\n      clearInterval(easingLoop);\n      easingLoop = setInterval(function () {\n        if (i.isInitialized) {\n          clearInterval(easingLoop);\n          return;\n        }\n        if (!speed.x && !speed.y) {\n          clearInterval(easingLoop);\n          return;\n        }\n        if (Math.abs(speed.x) < 0.01 && Math.abs(speed.y) < 0.01) {\n          clearInterval(easingLoop);\n          return;\n        }\n        if (!i.element) {\n          clearInterval(easingLoop);\n          return;\n        }\n        applyTouchMove(speed.x * 30, speed.y * 30);\n        speed.x *= 0.8;\n        speed.y *= 0.8;\n      }, 10);\n    }\n  }\n  if (env.supportsTouch) {\n    i.event.bind(element, 'touchstart', touchStart);\n    i.event.bind(element, 'touchmove', touchMove);\n    i.event.bind(element, 'touchend', touchEnd);\n  } else if (env.supportsIePointer) {\n    if (window.PointerEvent) {\n      i.event.bind(element, 'pointerdown', touchStart);\n      i.event.bind(element, 'pointermove', touchMove);\n      i.event.bind(element, 'pointerup', touchEnd);\n    } else if (window.MSPointerEvent) {\n      i.event.bind(element, 'MSPointerDown', touchStart);\n      i.event.bind(element, 'MSPointerMove', touchMove);\n      i.event.bind(element, 'MSPointerUp', touchEnd);\n    }\n  }\n}\nvar defaultSettings = function () {\n  return {\n    handlers: ['click-rail', 'drag-thumb', 'keyboard', 'wheel', 'touch'],\n    maxScrollbarLength: null,\n    minScrollbarLength: null,\n    scrollingThreshold: 1000,\n    scrollXMarginOffset: 0,\n    scrollYMarginOffset: 0,\n    suppressScrollX: false,\n    suppressScrollY: false,\n    swipeEasing: true,\n    useBothWheelAxes: false,\n    wheelPropagation: true,\n    wheelSpeed: 1\n  };\n};\nvar handlers = {\n  'click-rail': clickRail,\n  'drag-thumb': dragThumb,\n  keyboard: keyboard,\n  wheel: wheel,\n  touch: touch\n};\nvar PerfectScrollbar = function PerfectScrollbar(element, userSettings) {\n  var this$1 = this;\n  if (userSettings === void 0) userSettings = {};\n  if (typeof element === 'string') {\n    element = document.querySelector(element);\n  }\n  if (!element || !element.nodeName) {\n    throw new Error('no element is specified to initialize PerfectScrollbar');\n  }\n  this.element = element;\n  element.classList.add(cls.main);\n  this.settings = defaultSettings();\n  for (var key in userSettings) {\n    this.settings[key] = userSettings[key];\n  }\n  this.containerWidth = null;\n  this.containerHeight = null;\n  this.contentWidth = null;\n  this.contentHeight = null;\n  var focus = function () {\n    return element.classList.add(cls.state.focus);\n  };\n  var blur = function () {\n    return element.classList.remove(cls.state.focus);\n  };\n  this.isRtl = get(element).direction === 'rtl';\n  if (this.isRtl === true) {\n    element.classList.add(cls.rtl);\n  }\n  this.isNegativeScroll = function () {\n    var originalScrollLeft = element.scrollLeft;\n    var result = null;\n    element.scrollLeft = -1;\n    result = element.scrollLeft < 0;\n    element.scrollLeft = originalScrollLeft;\n    return result;\n  }();\n  this.negativeScrollAdjustment = this.isNegativeScroll ? element.scrollWidth - element.clientWidth : 0;\n  this.event = new EventManager();\n  this.ownerDocument = element.ownerDocument || document;\n  this.scrollbarXRail = div(cls.element.rail('x'));\n  element.appendChild(this.scrollbarXRail);\n  this.scrollbarX = div(cls.element.thumb('x'));\n  this.scrollbarXRail.appendChild(this.scrollbarX);\n  this.scrollbarX.setAttribute('tabindex', 0);\n  this.event.bind(this.scrollbarX, 'focus', focus);\n  this.event.bind(this.scrollbarX, 'blur', blur);\n  this.scrollbarXActive = null;\n  this.scrollbarXWidth = null;\n  this.scrollbarXLeft = null;\n  var railXStyle = get(this.scrollbarXRail);\n  this.scrollbarXBottom = parseInt(railXStyle.bottom, 10);\n  if (isNaN(this.scrollbarXBottom)) {\n    this.isScrollbarXUsingBottom = false;\n    this.scrollbarXTop = toInt(railXStyle.top);\n  } else {\n    this.isScrollbarXUsingBottom = true;\n  }\n  this.railBorderXWidth = toInt(railXStyle.borderLeftWidth) + toInt(railXStyle.borderRightWidth);\n  // Set rail to display:block to calculate margins\n  set(this.scrollbarXRail, {\n    display: 'block'\n  });\n  this.railXMarginWidth = toInt(railXStyle.marginLeft) + toInt(railXStyle.marginRight);\n  set(this.scrollbarXRail, {\n    display: ''\n  });\n  this.railXWidth = null;\n  this.railXRatio = null;\n  this.scrollbarYRail = div(cls.element.rail('y'));\n  element.appendChild(this.scrollbarYRail);\n  this.scrollbarY = div(cls.element.thumb('y'));\n  this.scrollbarYRail.appendChild(this.scrollbarY);\n  this.scrollbarY.setAttribute('tabindex', 0);\n  this.event.bind(this.scrollbarY, 'focus', focus);\n  this.event.bind(this.scrollbarY, 'blur', blur);\n  this.scrollbarYActive = null;\n  this.scrollbarYHeight = null;\n  this.scrollbarYTop = null;\n  var railYStyle = get(this.scrollbarYRail);\n  this.scrollbarYRight = parseInt(railYStyle.right, 10);\n  if (isNaN(this.scrollbarYRight)) {\n    this.isScrollbarYUsingRight = false;\n    this.scrollbarYLeft = toInt(railYStyle.left);\n  } else {\n    this.isScrollbarYUsingRight = true;\n  }\n  this.scrollbarYOuterWidth = this.isRtl ? outerWidth(this.scrollbarY) : null;\n  this.railBorderYWidth = toInt(railYStyle.borderTopWidth) + toInt(railYStyle.borderBottomWidth);\n  set(this.scrollbarYRail, {\n    display: 'block'\n  });\n  this.railYMarginHeight = toInt(railYStyle.marginTop) + toInt(railYStyle.marginBottom);\n  set(this.scrollbarYRail, {\n    display: ''\n  });\n  this.railYHeight = null;\n  this.railYRatio = null;\n  this.reach = {\n    x: element.scrollLeft <= 0 ? 'start' : element.scrollLeft >= this.contentWidth - this.containerWidth ? 'end' : null,\n    y: element.scrollTop <= 0 ? 'start' : element.scrollTop >= this.contentHeight - this.containerHeight ? 'end' : null\n  };\n  this.isAlive = true;\n  this.settings.handlers.forEach(function (handlerName) {\n    return handlers[handlerName](this$1);\n  });\n  this.lastScrollTop = Math.floor(element.scrollTop); // for onScroll only\n  this.lastScrollLeft = element.scrollLeft; // for onScroll only\n  this.event.bind(this.element, 'scroll', function (e) {\n    return this$1.onScroll(e);\n  });\n  updateGeometry(this);\n};\nPerfectScrollbar.prototype.update = function update() {\n  if (!this.isAlive) {\n    return;\n  }\n\n  // Recalcuate negative scrollLeft adjustment\n  this.negativeScrollAdjustment = this.isNegativeScroll ? this.element.scrollWidth - this.element.clientWidth : 0;\n\n  // Recalculate rail margins\n  set(this.scrollbarXRail, {\n    display: 'block'\n  });\n  set(this.scrollbarYRail, {\n    display: 'block'\n  });\n  this.railXMarginWidth = toInt(get(this.scrollbarXRail).marginLeft) + toInt(get(this.scrollbarXRail).marginRight);\n  this.railYMarginHeight = toInt(get(this.scrollbarYRail).marginTop) + toInt(get(this.scrollbarYRail).marginBottom);\n\n  // Hide scrollbars not to affect scrollWidth and scrollHeight\n  set(this.scrollbarXRail, {\n    display: 'none'\n  });\n  set(this.scrollbarYRail, {\n    display: 'none'\n  });\n  updateGeometry(this);\n  processScrollDiff(this, 'top', 0, false, true);\n  processScrollDiff(this, 'left', 0, false, true);\n  set(this.scrollbarXRail, {\n    display: ''\n  });\n  set(this.scrollbarYRail, {\n    display: ''\n  });\n};\nPerfectScrollbar.prototype.onScroll = function onScroll(e) {\n  if (!this.isAlive) {\n    return;\n  }\n  updateGeometry(this);\n  processScrollDiff(this, 'top', this.element.scrollTop - this.lastScrollTop);\n  processScrollDiff(this, 'left', this.element.scrollLeft - this.lastScrollLeft);\n  this.lastScrollTop = Math.floor(this.element.scrollTop);\n  this.lastScrollLeft = this.element.scrollLeft;\n};\nPerfectScrollbar.prototype.destroy = function destroy() {\n  if (!this.isAlive) {\n    return;\n  }\n  this.event.unbindAll();\n  remove(this.scrollbarX);\n  remove(this.scrollbarY);\n  remove(this.scrollbarXRail);\n  remove(this.scrollbarYRail);\n  this.removePsClasses();\n\n  // unset elements\n  this.element = null;\n  this.scrollbarX = null;\n  this.scrollbarY = null;\n  this.scrollbarXRail = null;\n  this.scrollbarYRail = null;\n  this.isAlive = false;\n};\nPerfectScrollbar.prototype.removePsClasses = function removePsClasses() {\n  this.element.className = this.element.className.split(' ').filter(function (name) {\n    return !name.match(/^ps([-_].+|)$/);\n  }).join(' ');\n};\nexport default PerfectScrollbar;\n//# sourceMappingURL=perfect-scrollbar.esm.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}