{"ast": null, "code": "import { mix } from './mix.es.js';\nimport { progress } from './progress.es.js';\nfunction fillOffset(offset, remaining) {\n  const min = offset[offset.length - 1];\n  for (let i = 1; i <= remaining; i++) {\n    const offsetProgress = progress(0, remaining, i);\n    offset.push(mix(min, 1, offsetProgress));\n  }\n}\nfunction defaultOffset(length) {\n  const offset = [0];\n  fillOffset(offset, length - 1);\n  return offset;\n}\nexport { defaultOffset, fillOffset };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}