{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useDispatch, useSelector } from 'react-redux';\n// Use throughout your app instead of plain `useDispatch` and `useSelector`\nexport const useAppDispatch = () => {\n  _s();\n  return useDispatch();\n};\n_s(useAppDispatch, \"jI3HA1r1Cumjdbu14H7G+TUj798=\", false, function () {\n  return [useDispatch];\n});\nexport const useAppSelector = useSelector;", "map": {"version": 3, "names": ["useDispatch", "useSelector", "useAppDispatch", "_s", "useAppSelector"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/app/hooks.ts"], "sourcesContent": ["import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';\nimport type { RootState, AppDispatch } from './store';\n\n// Use throughout your app instead of plain `useDispatch` and `useSelector`\nexport const useAppDispatch = () => useDispatch<AppDispatch>();\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;\n"], "mappings": ";AAAA,SAA+BA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAG5E;AACA,OAAO,MAAMC,cAAc,GAAGA,CAAA;EAAAC,EAAA;EAAA,OAAMH,WAAW,CAAc,CAAC;AAAA;AAACG,EAAA,CAAlDD,cAAc;EAAA,QAASF,WAAW;AAAA;AAC/C,OAAO,MAAMI,cAA+C,GAAGH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}