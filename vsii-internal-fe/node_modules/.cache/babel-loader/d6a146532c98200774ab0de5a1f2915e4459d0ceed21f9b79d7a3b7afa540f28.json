{"ast": null, "code": "// material-ui\nimport{TableCell,TableRow}from'@mui/material';//project import\nimport ProductReportStatusChip from'components/extended/ProductReportStatusChip';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const BacklogDetailTbody=props=>{const{data}=props;return/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{sx:{width:'55%',px:'3px'},children:data.taskName}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'30%',px:'3px'},children:data.assigneeName}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'15%',px:0},children:/*#__PURE__*/_jsx(ProductReportStatusChip,{status:data.status})})]});};export default BacklogDetailTbody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}