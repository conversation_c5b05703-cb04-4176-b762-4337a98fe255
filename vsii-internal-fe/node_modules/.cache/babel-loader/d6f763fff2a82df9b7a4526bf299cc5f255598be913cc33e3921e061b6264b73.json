{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/StatusRequestsChecking.tsx\";\nimport { FormattedMessage } from 'react-intl';\nimport { Select } from 'components/extended/Form';\nimport { DEFAULT_VALUE_OPTION, STATUS_REQUESTS_CHECKING, DEFAULT_VALUE_OPTION_SELECT } from 'constants/Common';\nimport { searchFormConfig } from './Config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatusRequestsChecking = props => {\n  const {\n    isShowAll,\n    required,\n    label\n  } = props;\n  return /*#__PURE__*/_jsxDEV(Select, {\n    required: required,\n    isMultipleLanguage: true,\n    selects: !isShowAll ? [DEFAULT_VALUE_OPTION, ...STATUS_REQUESTS_CHECKING] : [DEFAULT_VALUE_OPTION_SELECT, ...STATUS_REQUESTS_CHECKING],\n    name: searchFormConfig.statusRequestsChecking.name,\n    label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n      id: label || searchFormConfig.statusRequestsChecking.label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 20\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 9\n  }, this);\n};\n_c = StatusRequestsChecking;\nStatusRequestsChecking.defaultProps = {\n  isShowAll: false\n};\nexport default StatusRequestsChecking;\nvar _c;\n$RefreshReg$(_c, \"StatusRequestsChecking\");", "map": {"version": 3, "names": ["FormattedMessage", "Select", "DEFAULT_VALUE_OPTION", "STATUS_REQUESTS_CHECKING", "DEFAULT_VALUE_OPTION_SELECT", "searchFormConfig", "jsxDEV", "_jsxDEV", "StatusRequestsChecking", "props", "isShowAll", "required", "label", "isMultipleLanguage", "selects", "name", "statusRequestsChecking", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "defaultProps", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/StatusRequestsChecking.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\nimport { Select } from 'components/extended/Form';\nimport { DEFAULT_VALUE_OPTION, STATUS_REQUESTS_CHECKING, DEFAULT_VALUE_OPTION_SELECT } from 'constants/Common';\nimport { searchFormConfig } from './Config';\n\ninterface IStatusRequestsChecking {\n    isShowAll: boolean;\n    required?: boolean;\n    label?: string;\n}\n\nconst StatusRequestsChecking = (props: IStatusRequestsChecking) => {\n    const { isShowAll, required, label } = props;\n    return (\n        <Select\n            required={required}\n            isMultipleLanguage\n            selects={\n                !isShowAll\n                    ? [DEFAULT_VALUE_OPTION, ...STATUS_REQUESTS_CHECKING]\n                    : [DEFAULT_VALUE_OPTION_SELECT, ...STATUS_REQUESTS_CHECKING]\n            }\n            name={searchFormConfig.statusRequestsChecking.name}\n            label={<FormattedMessage id={label || searchFormConfig.statusRequestsChecking.label} />}\n        />\n    );\n};\n\nStatusRequestsChecking.defaultProps = {\n    isShowAll: false\n};\n\nexport default StatusRequestsChecking;\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,oBAAoB,EAAEC,wBAAwB,EAAEC,2BAA2B,QAAQ,kBAAkB;AAC9G,SAASC,gBAAgB,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ5C,MAAMC,sBAAsB,GAAIC,KAA8B,IAAK;EAC/D,MAAM;IAAEC,SAAS;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAGH,KAAK;EAC5C,oBACIF,OAAA,CAACN,MAAM;IACHU,QAAQ,EAAEA,QAAS;IACnBE,kBAAkB;IAClBC,OAAO,EACH,CAACJ,SAAS,GACJ,CAACR,oBAAoB,EAAE,GAAGC,wBAAwB,CAAC,GACnD,CAACC,2BAA2B,EAAE,GAAGD,wBAAwB,CAClE;IACDY,IAAI,EAAEV,gBAAgB,CAACW,sBAAsB,CAACD,IAAK;IACnDH,KAAK,eAAEL,OAAA,CAACP,gBAAgB;MAACiB,EAAE,EAAEL,KAAK,IAAIP,gBAAgB,CAACW,sBAAsB,CAACJ;IAAM;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3F,CAAC;AAEV,CAAC;AAACC,EAAA,GAfId,sBAAsB;AAiB5BA,sBAAsB,CAACe,YAAY,GAAG;EAClCb,SAAS,EAAE;AACf,CAAC;AAED,eAAeF,sBAAsB;AAAC,IAAAc,EAAA;AAAAE,YAAA,CAAAF,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}