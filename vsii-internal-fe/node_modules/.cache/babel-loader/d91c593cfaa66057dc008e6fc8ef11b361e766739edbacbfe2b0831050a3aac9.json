{"ast": null, "code": "import { number as locale } from './locale';\nimport isAbsent from './util/isAbsent';\nimport BaseSchema from './schema';\nlet isNaN = value => value != +value;\nexport function create() {\n  return new NumberSchema();\n}\nexport default class NumberSchema extends BaseSchema {\n  constructor() {\n    super({\n      type: 'number'\n    });\n    this.withMutation(() => {\n      this.transform(function (value) {\n        let parsed = value;\n        if (typeof parsed === 'string') {\n          parsed = parsed.replace(/\\s/g, '');\n          if (parsed === '') return NaN; // don't use parseFloat to avoid positives on alpha-numeric strings\n\n          parsed = +parsed;\n        }\n        if (this.isType(parsed)) return parsed;\n        return parseFloat(parsed);\n      });\n    });\n  }\n  _typeCheck(value) {\n    if (value instanceof Number) value = value.valueOf();\n    return typeof value === 'number' && !isNaN(value);\n  }\n  min(min) {\n    let message = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : locale.min;\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        min\n      },\n      test(value) {\n        return isAbsent(value) || value >= this.resolve(min);\n      }\n    });\n  }\n  max(max) {\n    let message = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : locale.max;\n    return this.test({\n      message,\n      name: 'max',\n      exclusive: true,\n      params: {\n        max\n      },\n      test(value) {\n        return isAbsent(value) || value <= this.resolve(max);\n      }\n    });\n  }\n  lessThan(less) {\n    let message = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : locale.lessThan;\n    return this.test({\n      message,\n      name: 'max',\n      exclusive: true,\n      params: {\n        less\n      },\n      test(value) {\n        return isAbsent(value) || value < this.resolve(less);\n      }\n    });\n  }\n  moreThan(more) {\n    let message = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : locale.moreThan;\n    return this.test({\n      message,\n      name: 'min',\n      exclusive: true,\n      params: {\n        more\n      },\n      test(value) {\n        return isAbsent(value) || value > this.resolve(more);\n      }\n    });\n  }\n  positive() {\n    let msg = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : locale.positive;\n    return this.moreThan(0, msg);\n  }\n  negative() {\n    let msg = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : locale.negative;\n    return this.lessThan(0, msg);\n  }\n  integer() {\n    let message = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : locale.integer;\n    return this.test({\n      name: 'integer',\n      message,\n      test: val => isAbsent(val) || Number.isInteger(val)\n    });\n  }\n  truncate() {\n    return this.transform(value => !isAbsent(value) ? value | 0 : value);\n  }\n  round(method) {\n    var _method;\n    let avail = ['ceil', 'floor', 'round', 'trunc'];\n    method = ((_method = method) == null ? void 0 : _method.toLowerCase()) || 'round'; // this exists for symemtry with the new Math.trunc\n\n    if (method === 'trunc') return this.truncate();\n    if (avail.indexOf(method.toLowerCase()) === -1) throw new TypeError('Only valid options for round() are: ' + avail.join(', '));\n    return this.transform(value => !isAbsent(value) ? Math[method](value) : value);\n  }\n}\ncreate.prototype = NumberSchema.prototype; //\n// Number Interfaces\n//", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}