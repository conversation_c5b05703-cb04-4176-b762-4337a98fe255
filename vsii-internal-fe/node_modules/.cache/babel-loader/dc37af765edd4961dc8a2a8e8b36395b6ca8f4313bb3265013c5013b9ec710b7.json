{"ast": null, "code": "import { registerSource } from '../../internals/index.js';\nimport { useDragDropManager } from '../useDragDropManager.js';\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect.js';\nimport { useDragSource } from './useDragSource.js';\nimport { useDragType } from './useDragType.js';\nexport function useRegisteredDragSource(spec, monitor, connector) {\n  const manager = useDragDropManager();\n  const handler = useDragSource(spec, monitor, connector);\n  const itemType = useDragType(spec);\n  useIsomorphicLayoutEffect(function registerDragSource() {\n    if (itemType != null) {\n      const [handlerId, unregister] = registerSource(itemType, handler, manager);\n      monitor.receiveHandlerId(handlerId);\n      connector.receiveHandlerId(handlerId);\n      return unregister;\n    }\n    return;\n  }, [manager, monitor, connector, handler, itemType]);\n}\n\n//# sourceMappingURL=useRegisteredDragSource.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}