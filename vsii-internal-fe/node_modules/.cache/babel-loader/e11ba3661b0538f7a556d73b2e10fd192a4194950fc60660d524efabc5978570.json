{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport verifyPlainObject from '../utils/verifyPlainObject';\nexport function defaultMergeProps(stateProps, dispatchProps, ownProps) {\n  return _extends({}, ownProps, stateProps, dispatchProps);\n}\nexport function wrapMergePropsFunc(mergeProps) {\n  return function initMergePropsProxy(dispatch, _ref) {\n    var displayName = _ref.displayName,\n      pure = _ref.pure,\n      areMergedPropsEqual = _ref.areMergedPropsEqual;\n    var hasRunOnce = false;\n    var mergedProps;\n    return function mergePropsProxy(stateProps, dispatchProps, ownProps) {\n      var nextMergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n      if (hasRunOnce) {\n        if (!pure || !areMergedPropsEqual(nextMergedProps, mergedProps)) mergedProps = nextMergedProps;\n      } else {\n        hasRunOnce = true;\n        mergedProps = nextMergedProps;\n        if (process.env.NODE_ENV !== 'production') verifyPlainObject(mergedProps, displayName, 'mergeProps');\n      }\n      return mergedProps;\n    };\n  };\n}\nexport function whenMergePropsIsFunction(mergeProps) {\n  return typeof mergeProps === 'function' ? wrapMergePropsFunc(mergeProps) : undefined;\n}\nexport function whenMergePropsIsOmitted(mergeProps) {\n  return !mergeProps ? function () {\n    return defaultMergeProps;\n  } : undefined;\n}\nexport default [whenMergePropsIsFunction, whenMergePropsIsOmitted];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}