{"ast": null, "code": "/* eslint-disable @typescript-eslint/no-unused-vars */\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The YearPicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { YearPicker } from '@mui/x-date-pickers'`\", \"or `import { YearPicker } from '@mui/x-date-pickers/YearPicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n\n/**\n * @ignore - do not document.\n */\nconst YearPicker = function DeprecatedYearPicker() {\n  warn();\n  return null;\n};\nexport default YearPicker;\nexport const yearPickerClasses = {};\nexport const getYearPickerUtilityClass = slot => {\n  warn();\n  return '';\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}