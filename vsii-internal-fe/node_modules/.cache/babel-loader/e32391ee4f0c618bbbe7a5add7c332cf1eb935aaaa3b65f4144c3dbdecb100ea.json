{"ast": null, "code": "/* eslint-disable prettier/prettier *//* eslint-disable react-hooks/exhaustive-deps */import{useEffect,useState}from'react';// react-hook-form\nimport{useFieldArray,useForm}from'react-hook-form';import{FormattedMessage}from'react-intl';import{yupResolver}from'@hookform/resolvers/yup';// material-ui\nimport{LoadingButton}from'@mui/lab';import{Button,DialogActions,Grid,Stack}from'@mui/material';// project imports\nimport{DatePicker,FormProvider,Input,NumericFormatCustom,PercentageFormat}from'components/extended/Form';import Modal from'components/extended/Modal';import{Table}from'components/extended/Table';import{TabPanel}from'components/extended/Tabs';import{E_IS_LOGTIME,FIELD_BY_TAB_ONGOING,MONEY_PLACEHOLDER,PERCENT_PLACEHOLDER,editOnGoingTabs}from'constants/Common';import TabCustom from'containers/TabCustom';import{Member,ProductionPerformance,SalePipelineStatus,SalePipelineType}from'containers/search';import{editOnGoingDefaultValue,editOnGoingSchema}from'pages/sales/Config';import{gridSpacing}from'store/constant';import{getTabValueByFieldError,isEmpty}from'utils/common';import{dateFormat}from'utils/date';import OnGoingHCTBody from'./OnGoingHCTBody';import OnGoingHCThead from'./OnGoingHCThead';import{TEXT_CONFIG_SCREEN}from'constants/Common';// third party\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const EditOnGoing=props=>{var _project$hcInfo2,_project$hcInfo3;const{open,project,loading,isEdit,handleClose,year,postEditOnGoing}=props;const{salesReport}=TEXT_CONFIG_SCREEN;const[tabValue,setTabValue]=useState(0);const[projectOption,setProjectOption]=useState(null);const[userContactOption,setUserContactOption]=useState(null);const handleSubmit=values=>{var _values$monthlyHCList,_values$projectInfo,_values$projectInfo2,_values$projectInfo3;const filledHCInfo=values===null||values===void 0?void 0:(_values$monthlyHCList=values.monthlyHCList)===null||_values$monthlyHCList===void 0?void 0:_values$monthlyHCList.map((item,index)=>{return{year:values.projectInfo.year,month:+(item===null||item===void 0?void 0:item.month),hcMonthly:+(item===null||item===void 0?void 0:item.hcMonthly)};});const payload={...values,year,projectInfo:{...values.projectInfo,productionPerformanceIdHexString:values.projectInfo.productionPerformanceIdHexString.value,contractDueDate:dateFormat((_values$projectInfo=values.projectInfo)===null||_values$projectInfo===void 0?void 0:_values$projectInfo.contractDueDate),contractDurationFrom:dateFormat((_values$projectInfo2=values.projectInfo)===null||_values$projectInfo2===void 0?void 0:_values$projectInfo2.contractDurationFrom),contractDurationTo:dateFormat((_values$projectInfo3=values.projectInfo)===null||_values$projectInfo3===void 0?void 0:_values$projectInfo3.contractDurationTo)},otherInfo:{...values.otherInfo,contact:userContactOption?userContactOption:values.otherInfo.contact===null?null:project.otherInfo.contact},hcInfo:{...values.hcInfo,monthlyHCList:filledHCInfo}};delete payload.monthlyHCList;postEditOnGoing(payload);};const handleChangeTab=(event,value)=>{setTabValue(value);};const handleChangeUser=userSelected=>{userSelected&&setUserContactOption({idHexString:userSelected===null||userSelected===void 0?void 0:userSelected.idHexString,firstName:userSelected===null||userSelected===void 0?void 0:userSelected.firstName,lastName:userSelected===null||userSelected===void 0?void 0:userSelected.lastName});};const handleChangeProject=optionSelected=>{var _optionSelected$durat,_optionSelected$durat2;setProjectOption({projectName:optionSelected===null||optionSelected===void 0?void 0:optionSelected.project.projectName,serviceType:(optionSelected===null||optionSelected===void 0?void 0:optionSelected.serviceType)||null,contractType:(optionSelected===null||optionSelected===void 0?void 0:optionSelected.contractType)||null,contractDurationFrom:(optionSelected===null||optionSelected===void 0?void 0:(_optionSelected$durat=optionSelected.duration)===null||_optionSelected$durat===void 0?void 0:_optionSelected$durat.fromDate)||null,contractDurationTo:(optionSelected===null||optionSelected===void 0?void 0:(_optionSelected$durat2=optionSelected.duration)===null||_optionSelected$durat2===void 0?void 0:_optionSelected$durat2.toDate)||null,monthlyHCList:optionSelected.dataHcInfos||null});};const methods=useForm({defaultValues:{...editOnGoingDefaultValue,projectInfo:{...editOnGoingDefaultValue.projectInfo,year:year},monthlyHCList:[editOnGoingDefaultValue.hcInfo.monthlyHCList]},resolver:yupResolver(editOnGoingSchema),mode:'all'});useEffect(()=>{var _project$hcInfo;isEdit&&methods.reset({...project,projectInfo:{...project.projectInfo,productionPerformanceIdHexString:{value:project.projectInfo.productionPerformanceIdHexString,label:project.projectInfo.projectName}},otherInfo:{...project.otherInfo,contact:project.otherInfo.contact.idHexString?{value:project.otherInfo.contact.idHexString,label:\"\".concat(project.otherInfo.contact.firstName,\" \").concat(project.otherInfo.contact.lastName)}:null},monthlyHCList:project===null||project===void 0?void 0:(_project$hcInfo=project.hcInfo)===null||_project$hcInfo===void 0?void 0:_project$hcInfo.monthlyHCList});},[isEdit]);useEffect(()=>{projectOption&&methods.reset({...methods.getValues(),projectInfo:{...methods.getValues().projectInfo,serviceType:projectOption!==null&&projectOption!==void 0&&projectOption.serviceType?projectOption===null||projectOption===void 0?void 0:projectOption.serviceType:'',contractType:projectOption!==null&&projectOption!==void 0&&projectOption.contractType?projectOption===null||projectOption===void 0?void 0:projectOption.contractType:'',projectName:projectOption===null||projectOption===void 0?void 0:projectOption.projectName,contractDurationFrom:projectOption===null||projectOption===void 0?void 0:projectOption.contractDurationFrom,contractDurationTo:projectOption===null||projectOption===void 0?void 0:projectOption.contractDurationTo}});},[projectOption]);const{errors}=methods.formState;const{fields:monthlyHCListValues}=useFieldArray({control:methods.control,name:'monthlyHCList'});const focusErrors=()=>{const tabNumber=getTabValueByFieldError(errors,FIELD_BY_TAB_ONGOING);setTabValue(tabNumber);};useEffect(()=>{!isEmpty(errors)&&focusErrors();// eslint-disable-next-line react-hooks/exhaustive-deps\n},[errors]);return/*#__PURE__*/_jsx(Modal,{isOpen:open,title:'on-going-edit-on-going-detail',onClose:handleClose,keepMounted:false,maxWidth:\"md\",children:/*#__PURE__*/_jsxs(FormProvider,{formReturn:methods,onSubmit:handleSubmit,children:[/*#__PURE__*/_jsx(TabCustom,{value:tabValue,tabs:editOnGoingTabs,handleChange:handleChangeTab}),/*#__PURE__*/_jsx(TabPanel,{value:tabValue,index:0,children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:gridSpacing,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"projectInfo.year\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-year'}),disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"projectInfo.contractNo\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-contract-no'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"projectInfo.customer\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-customer'}),disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"projectInfo.probability\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-probability'}),textFieldProps:{placeholder:PERCENT_PLACEHOLDER,InputProps:{inputComponent:PercentageFormat}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(ProductionPerformance,{name:\"projectInfo.productionPerformanceIdHexString\",required:true,label:salesReport.salesOnGoing+'-project-name',disabled:true,handleChange:handleChangeProject,requestParams:{year:year}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(SalePipelineStatus,{name:\"projectInfo.status\",required:true,label:salesReport.salesOnGoing+'-sale-pipeline-status'})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(SalePipelineType,{name:\"projectInfo.type\",disabled:true,label:salesReport.salesOnGoing+'-sale-pipeline-type',required:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"projectInfo.revenuePercent\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:\"revenue\"}),disabled:true,textFieldProps:{placeholder:PERCENT_PLACEHOLDER,InputProps:{inputComponent:PercentageFormat}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(DatePicker,{name:\"projectInfo.contractDueDate\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-contract-date'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"projectInfo.serviceType\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-service-type'}),disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(DatePicker,{name:\"projectInfo.contractDurationFrom\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-contract-duration-from'}),disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"projectInfo.contractType\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-contract-type'}),disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(DatePicker,{name:\"projectInfo.contractDurationTo\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-contract-duration-to'}),disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"projectInfo.warrantyTime\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-warranty-time'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"projectInfo.note\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-note'}),textFieldProps:{multiline:true,rows:5}})})]})}),/*#__PURE__*/_jsx(TabPanel,{value:tabValue,index:1,children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:gridSpacing,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"financialInfo.currency\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-currency'}),disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"financialInfo.exchangeRate\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-exchange-rate'}),disabled:true,textFieldProps:{placeholder:MONEY_PLACEHOLDER,InputProps:{inputComponent:NumericFormatCustom}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"financialInfo.sizeVND\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-size-vnd'}),disabled:true,textFieldProps:{placeholder:MONEY_PLACEHOLDER,InputProps:{inputComponent:NumericFormatCustom}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"financialInfo.sizeUSD\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-size-usd'}),disabled:true,textFieldProps:{placeholder:MONEY_PLACEHOLDER,InputProps:{inputComponent:NumericFormatCustom}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"financialInfo.managementRevenueAllocated\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-management-revenue-allocated'}),disabled:true,textFieldProps:{placeholder:MONEY_PLACEHOLDER,InputProps:{inputComponent:NumericFormatCustom}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"financialInfo.newSaleUSD\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-new-sale-usd'}),disabled:true,textFieldProps:{placeholder:MONEY_PLACEHOLDER,InputProps:{inputComponent:NumericFormatCustom}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"financialInfo.accountRevenueAllocatedVNDValue\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-accountant-revenue-allocatedVND'}),textFieldProps:{placeholder:MONEY_PLACEHOLDER,InputProps:{inputComponent:NumericFormatCustom}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"financialInfo.acctReceivables\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-acct-receivables'}),disabled:true,textFieldProps:{placeholder:MONEY_PLACEHOLDER,InputProps:{inputComponent:NumericFormatCustom}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"financialInfo.licenseFee\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-license-fee'}),textFieldProps:{placeholder:MONEY_PLACEHOLDER,InputProps:{inputComponent:NumericFormatCustom}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"financialInfo.netEarn\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-net-earn'}),disabled:true,textFieldProps:{placeholder:MONEY_PLACEHOLDER,InputProps:{inputComponent:NumericFormatCustom}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"financialInfo.quarterLicense1st\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-quarter-license-1st'}),textFieldProps:{placeholder:MONEY_PLACEHOLDER,InputProps:{inputComponent:NumericFormatCustom}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"financialInfo.paid\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-paid'}),disabled:true,textFieldProps:{placeholder:MONEY_PLACEHOLDER,InputProps:{inputComponent:NumericFormatCustom}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"financialInfo.quarterLicense2nd\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-quarter-license-2nd'}),textFieldProps:{placeholder:MONEY_PLACEHOLDER,InputProps:{inputComponent:NumericFormatCustom}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"financialInfo.remain\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-remain'}),disabled:true,textFieldProps:{placeholder:MONEY_PLACEHOLDER,InputProps:{inputComponent:NumericFormatCustom}}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:12,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:5.9,children:/*#__PURE__*/_jsx(Input,{name:\"financialInfo.quarterLicense3rd\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-quarter-license-3rd'}),textFieldProps:{placeholder:MONEY_PLACEHOLDER,InputProps:{inputComponent:NumericFormatCustom}}})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"financialInfo.quarterLicense4th\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-quarter-license-4th'}),textFieldProps:{placeholder:MONEY_PLACEHOLDER,InputProps:{inputComponent:NumericFormatCustom}}})})]})}),/*#__PURE__*/_jsxs(TabPanel,{value:tabValue,index:2,children:[/*#__PURE__*/_jsxs(Grid,{container:true,spacing:gridSpacing,sx:{mb:gridSpacing},children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"hcInfo.billableHcs\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-billable-hcs'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"hcInfo.quarter1st\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-quarter-1st'}),disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"hcInfo.hcs\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-hcs'}),disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"hcInfo.quarter2nd\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-quarter-2nd'}),disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"hcInfo.teamLeadHcs\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-team-lead-hcs'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"hcInfo.quarter3rd\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-quarter-3rd'}),disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"hcInfo.seniorHcs\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-senior-hcs'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"hcInfo.quarter4th\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-quarter-4th'}),disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"hcInfo.middleHcs\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-senior-hcs'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"hcInfo.totalNewSale\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-total-new-sale'}),disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"hcInfo.juniorHcs\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-junior-hcs'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"hcInfo.totalBillable\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-total-billable'}),disabled:true})})]}),/*#__PURE__*/_jsx(Table,{height:\"auto\",data:project===null||project===void 0?void 0:(_project$hcInfo2=project.hcInfo)===null||_project$hcInfo2===void 0?void 0:_project$hcInfo2.monthlyHCList,heads:/*#__PURE__*/_jsx(OnGoingHCThead,{hcInfo:project===null||project===void 0?void 0:(_project$hcInfo3=project.hcInfo)===null||_project$hcInfo3===void 0?void 0:_project$hcInfo3.monthlyHCList}),children:/*#__PURE__*/_jsx(OnGoingHCTBody,{hcInfo:monthlyHCListValues})})]}),/*#__PURE__*/_jsx(TabPanel,{value:tabValue,index:3,children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:gridSpacing,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Member,{name:\"otherInfo.contact\",isLogTime:E_IS_LOGTIME.YES,isDefaultAll:true,label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-contact'}),isIdHexString:true,handleChange:handleChangeUser})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"otherInfo.phoneNumber\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-phone-number'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"otherInfo.presaleFolder\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-presale-folder'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"otherInfo.emailAddress\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-email-address'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"otherInfo.customerContact\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-customer-contact'})})})]})}),/*#__PURE__*/_jsx(DialogActions,{children:/*#__PURE__*/_jsxs(Stack,{direction:\"row\",spacing:1,justifyContent:\"flex-end\",children:[/*#__PURE__*/_jsx(Button,{color:\"error\",onClick:handleClose,children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"cancel\"})}),/*#__PURE__*/_jsx(LoadingButton,{variant:\"contained\",type:\"submit\",loading:loading,children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"submit\"})})]})})]})});};export default EditOnGoing;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}