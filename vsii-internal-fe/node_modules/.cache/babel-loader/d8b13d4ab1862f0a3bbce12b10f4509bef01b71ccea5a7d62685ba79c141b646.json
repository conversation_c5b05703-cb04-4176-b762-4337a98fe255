{"ast": null, "code": "// material-ui\nimport{Grid}from'@mui/material';// project imports\nimport{monthlyProductionPerformanceFilterConfig,monthlyProductionPerformanceFilterSchema}from'pages/sales/Config';import{SearchForm,SalesYear}from'../search';import{E_SCREEN_SALES_YEAR}from'constants/Common';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx}from\"react/jsx-runtime\";const SummarySearch=props=>{const{conditions,handleChangeYear,handleSearch}=props;const handleYearChange=e=>{const value={year:Number(e.target.value)};handleChangeYear(e);handleSearch(value);};return/*#__PURE__*/_jsx(SearchForm,{defaultValues:monthlyProductionPerformanceFilterConfig,formSchema:monthlyProductionPerformanceFilterSchema,handleSubmit:handleSearch,formReset:conditions,children:/*#__PURE__*/_jsx(Grid,{container:true,alignItems:\"center\",spacing:2,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:2,children:/*#__PURE__*/_jsx(SalesYear,{handleChangeYear:handleYearChange,screen:E_SCREEN_SALES_YEAR.SALES_PIPELINE_SUMMARY,label:TEXT_CONFIG_SCREEN.salesReport.summary+'-year'})})})});};export default SummarySearch;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}