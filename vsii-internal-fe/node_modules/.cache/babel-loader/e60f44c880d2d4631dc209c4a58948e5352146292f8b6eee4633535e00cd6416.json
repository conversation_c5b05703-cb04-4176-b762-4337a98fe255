{"ast": null, "code": "import { animations } from '../../motion/features/animations.mjs';\nimport { gestureAnimations } from '../../motion/features/gestures.mjs';\nimport { createDomVisualElement } from './create-visual-element.mjs';\n\n/**\n * @public\n */\nconst domAnimation = {\n  renderer: createDomVisualElement,\n  ...animations,\n  ...gestureAnimations\n};\nexport { domAnimation };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}