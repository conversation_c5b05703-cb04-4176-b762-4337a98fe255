{"ast": null, "code": "import { useState, useEffect } from 'react';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { getOrigin, checkTargetForNewValues } from '../../render/utils/setters.mjs';\nimport { animateVisualElement } from '../../render/utils/animation.mjs';\nimport { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { VisualElement } from '../../render/VisualElement.mjs';\nconst createObject = () => ({});\nclass StateVisualElement extends VisualElement {\n  build() {}\n  measureInstanceViewportBox() {\n    return createBox();\n  }\n  resetTransform() {}\n  restoreTransform() {}\n  removeValueFromRenderState() {}\n  renderInstance() {}\n  scrapeMotionValuesFromProps() {\n    return createObject();\n  }\n  getBaseTargetFromProps() {\n    return undefined;\n  }\n  readValueFromInstance(_state, key, options) {\n    return options.initialState[key] || 0;\n  }\n  sortInstanceNodePosition() {\n    return 0;\n  }\n  makeTargetAnimatableFromInstance({\n    transition,\n    transitionEnd,\n    ...target\n  }) {\n    const origin = getOrigin(target, transition || {}, this);\n    checkTargetForNewValues(this, target, origin);\n    return {\n      transition,\n      transitionEnd,\n      ...target\n    };\n  }\n}\nconst useVisualState = makeUseVisualState({\n  scrapeMotionValuesFromProps: createObject,\n  createRenderState: createObject\n});\n/**\n * This is not an officially supported API and may be removed\n * on any version.\n */\nfunction useAnimatedState(initialState) {\n  const [animationState, setAnimationState] = useState(initialState);\n  const visualState = useVisualState({}, false);\n  const element = useConstant(() => {\n    return new StateVisualElement({\n      props: {},\n      visualState\n    }, {\n      initialState\n    });\n  });\n  useEffect(() => {\n    element.mount({});\n    return () => element.unmount();\n  }, [element]);\n  useEffect(() => {\n    element.setProps({\n      onUpdate: v => {\n        setAnimationState({\n          ...v\n        });\n      }\n    });\n  }, [setAnimationState, element]);\n  const startAnimation = useConstant(() => animationDefinition => {\n    return animateVisualElement(element, animationDefinition);\n  });\n  return [animationState, startAnimation];\n}\nexport { useAnimatedState };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}