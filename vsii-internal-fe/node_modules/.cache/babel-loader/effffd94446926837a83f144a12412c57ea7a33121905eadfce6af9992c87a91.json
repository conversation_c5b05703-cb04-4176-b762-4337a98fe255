{"ast": null, "code": "export function ApplyUnsignedRoundingMode(x, r1, r2, unsignedRoundingMode) {\n  if (x === r1) return r1;\n  if (unsignedRoundingMode === undefined) {\n    throw new Error('unsignedRoundingMode is mandatory');\n  }\n  if (unsignedRoundingMode === 'zero') {\n    return r1;\n  }\n  if (unsignedRoundingMode === 'infinity') {\n    return r2;\n  }\n  var d1 = x - r1;\n  var d2 = r2 - x;\n  if (d1 < d2) {\n    return r1;\n  }\n  if (d2 < d1) {\n    return r2;\n  }\n  if (d1 !== d2) {\n    throw new Error('Unexpected error');\n  }\n  if (unsignedRoundingMode === 'half-zero') {\n    return r1;\n  }\n  if (unsignedRoundingMode === 'half-infinity') {\n    return r2;\n  }\n  if (unsignedRoundingMode !== 'half-even') {\n    throw new Error(\"Unexpected value for unsignedRoundingMode: \".concat(unsignedRoundingMode));\n  }\n  var cardinality = r1 / (r2 - r1) % 2;\n  if (cardinality === 0) {\n    return r1;\n  }\n  return r2;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}