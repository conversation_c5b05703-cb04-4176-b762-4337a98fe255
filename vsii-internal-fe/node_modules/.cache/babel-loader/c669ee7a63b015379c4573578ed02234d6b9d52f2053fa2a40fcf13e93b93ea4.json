{"ast": null, "code": "import { HTML5BackendImpl } from './HTML5BackendImpl.js';\nimport * as _NativeTypes from './NativeTypes.js';\nexport { getEmptyImage } from './getEmptyImage.js';\nexport { _NativeTypes as NativeTypes };\nexport const HTML5Backend = function createBackend(manager, context, options) {\n  return new HTML5BackendImpl(manager, context, options);\n};\n\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}