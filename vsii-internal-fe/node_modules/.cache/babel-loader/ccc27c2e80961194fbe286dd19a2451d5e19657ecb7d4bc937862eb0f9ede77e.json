{"ast": null, "code": "var copyObject = require('./_copyObject'),\n  keys = require('./keys');\n\n/**\n * The base implementation of `_.assign` without support for multiple sources\n * or `customizer` functions.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @returns {Object} Returns `object`.\n */\nfunction baseAssign(object, source) {\n  return object && copyObject(source, keys(source), object);\n}\nmodule.exports = baseAssign;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}