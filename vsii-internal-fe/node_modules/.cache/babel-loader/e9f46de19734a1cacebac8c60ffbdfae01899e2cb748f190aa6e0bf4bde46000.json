{"ast": null, "code": "import { isFunction, defaults, isCubicBezier, progress } from '@motionone/utils';\nimport { supports } from './feature-detection.es.js';\n\n// Create a linear easing point for every x second\nconst resolution = 0.015;\nconst generateLinearEasingPoints = (easing, duration) => {\n  let points = \"\";\n  const numPoints = Math.round(duration / resolution);\n  for (let i = 0; i < numPoints; i++) {\n    points += easing(progress(0, numPoints - 1, i)) + \", \";\n  }\n  return points.substring(0, points.length - 2);\n};\nconst convertEasing = (easing, duration) => {\n  if (isFunction(easing)) {\n    return supports.linearEasing() ? `linear(${generateLinearEasingPoints(easing, duration)})` : defaults.easing;\n  } else {\n    return isCubicBezier(easing) ? cubicBezierAsString(easing) : easing;\n  }\n};\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\nexport { convertEasing, cubicBezierAsString, generateLinearEasingPoints };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}