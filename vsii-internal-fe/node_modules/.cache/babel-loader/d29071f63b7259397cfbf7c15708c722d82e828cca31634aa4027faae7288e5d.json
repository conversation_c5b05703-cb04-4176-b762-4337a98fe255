{"ast": null, "code": "/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The PickersDay component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { PickersDay } from '@mui/x-date-pickers'`\", \"or `import { PickersDay } from '@mui/x-date-pickers/PickersDay'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n\n/**\n * @ignore - do not document.\n */\nconst PickersDay = /*#__PURE__*/React.forwardRef(function DeprecatedPickersDay() {\n  warn();\n  return null;\n});\nexport default PickersDay;\nexport const pickersDayClasses = {};\nexport const getPickersDayUtilityClass = slot => {\n  warn();\n  return '';\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}