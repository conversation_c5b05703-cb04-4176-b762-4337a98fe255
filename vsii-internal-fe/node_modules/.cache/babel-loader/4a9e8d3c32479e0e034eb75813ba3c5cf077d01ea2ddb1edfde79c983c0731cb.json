{"ast": null, "code": "export class DragSourceImpl {\n  beginDrag() {\n    const spec = this.spec;\n    const monitor = this.monitor;\n    let result = null;\n    if (typeof spec.item === 'object') {\n      result = spec.item;\n    } else if (typeof spec.item === 'function') {\n      result = spec.item(monitor);\n    } else {\n      result = {};\n    }\n    return result !== null && result !== void 0 ? result : null;\n  }\n  canDrag() {\n    const spec = this.spec;\n    const monitor = this.monitor;\n    if (typeof spec.canDrag === 'boolean') {\n      return spec.canDrag;\n    } else if (typeof spec.canDrag === 'function') {\n      return spec.canDrag(monitor);\n    } else {\n      return true;\n    }\n  }\n  isDragging(globalMonitor, target) {\n    const spec = this.spec;\n    const monitor = this.monitor;\n    const {\n      isDragging\n    } = spec;\n    return isDragging ? isDragging(monitor) : target === globalMonitor.getSourceId();\n  }\n  endDrag() {\n    const spec = this.spec;\n    const monitor = this.monitor;\n    const connector = this.connector;\n    const {\n      end\n    } = spec;\n    if (end) {\n      end(monitor.getItem(), monitor);\n    }\n    connector.reconnect();\n  }\n  constructor(spec, monitor, connector) {\n    this.spec = spec;\n    this.monitor = monitor;\n    this.connector = connector;\n  }\n}\n\n//# sourceMappingURL=DragSourceImpl.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}