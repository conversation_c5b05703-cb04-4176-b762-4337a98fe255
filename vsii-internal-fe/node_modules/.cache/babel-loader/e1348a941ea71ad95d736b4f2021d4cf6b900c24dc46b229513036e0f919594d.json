{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"BackdropComponent\", \"BackdropProps\", \"classes\", \"className\", \"closeAfterTransition\", \"children\", \"container\", \"component\", \"components\", \"componentsProps\", \"disableAutoFocus\", \"disableEnforceFocus\", \"disableEscapeKeyDown\", \"disablePortal\", \"disableRestoreFocus\", \"disableScrollLock\", \"hideBackdrop\", \"keepMounted\", \"onBackdropClick\", \"onClose\", \"onTransitionEnter\", \"onTransitionExited\", \"open\", \"slotProps\", \"slots\", \"theme\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport elementAcceptingRef from '@mui/utils/elementAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport FocusTrap from '../Unstable_TrapFocus';\nimport Portal from '../Portal';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Backdrop from '../Backdrop';\nimport useModal from './useModal';\nimport { getModalUtilityClass } from './modalClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    open,\n    exited,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', !open && exited && 'hidden'],\n    backdrop: ['backdrop']\n  };\n  return composeClasses(slots, getModalUtilityClass, classes);\n};\nconst ModalRoot = styled('div', {\n  name: 'MuiModal',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.open && ownerState.exited && styles.hidden];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  position: 'fixed',\n  zIndex: (theme.vars || theme).zIndex.modal,\n  right: 0,\n  bottom: 0,\n  top: 0,\n  left: 0\n}, !ownerState.open && ownerState.exited && {\n  visibility: 'hidden'\n}));\nconst ModalBackdrop = styled(Backdrop, {\n  name: 'MuiModal',\n  slot: 'Backdrop',\n  overridesResolver: (props, styles) => {\n    return styles.backdrop;\n  }\n})({\n  zIndex: -1\n});\n\n/**\n * Modal is a lower-level construct that is leveraged by the following components:\n *\n * - [Dialog](/material-ui/api/dialog/)\n * - [Drawer](/material-ui/api/drawer/)\n * - [Menu](/material-ui/api/menu/)\n * - [Popover](/material-ui/api/popover/)\n *\n * If you are creating a modal dialog, you probably want to use the [Dialog](/material-ui/api/dialog/) component\n * rather than directly using Modal.\n *\n * This component shares many concepts with [react-overlays](https://react-bootstrap.github.io/react-overlays/#modals).\n */\nconst Modal = /*#__PURE__*/React.forwardRef(function Modal(inProps, ref) {\n  var _ref, _slots$root, _ref2, _slots$backdrop, _slotProps$root, _slotProps$backdrop;\n  const props = useDefaultProps({\n    name: 'MuiModal',\n    props: inProps\n  });\n  const {\n      BackdropComponent = ModalBackdrop,\n      BackdropProps,\n      className,\n      closeAfterTransition = false,\n      children,\n      container,\n      component,\n      components = {},\n      componentsProps = {},\n      disableAutoFocus = false,\n      disableEnforceFocus = false,\n      disableEscapeKeyDown = false,\n      disablePortal = false,\n      disableRestoreFocus = false,\n      disableScrollLock = false,\n      hideBackdrop = false,\n      keepMounted = false,\n      onBackdropClick,\n      open,\n      slotProps,\n      slots\n      // eslint-disable-next-line react/prop-types\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const propsWithDefaults = _extends({}, props, {\n    closeAfterTransition,\n    disableAutoFocus,\n    disableEnforceFocus,\n    disableEscapeKeyDown,\n    disablePortal,\n    disableRestoreFocus,\n    disableScrollLock,\n    hideBackdrop,\n    keepMounted\n  });\n  const {\n    getRootProps,\n    getBackdropProps,\n    getTransitionProps,\n    portalRef,\n    isTopModal,\n    exited,\n    hasTransition\n  } = useModal(_extends({}, propsWithDefaults, {\n    rootRef: ref\n  }));\n  const ownerState = _extends({}, propsWithDefaults, {\n    exited\n  });\n  const classes = useUtilityClasses(ownerState);\n  const childProps = {};\n  if (children.props.tabIndex === undefined) {\n    childProps.tabIndex = '-1';\n  }\n\n  // It's a Transition like component\n  if (hasTransition) {\n    const {\n      onEnter,\n      onExited\n    } = getTransitionProps();\n    childProps.onEnter = onEnter;\n    childProps.onExited = onExited;\n  }\n  const RootSlot = (_ref = (_slots$root = slots == null ? void 0 : slots.root) != null ? _slots$root : components.Root) != null ? _ref : ModalRoot;\n  const BackdropSlot = (_ref2 = (_slots$backdrop = slots == null ? void 0 : slots.backdrop) != null ? _slots$backdrop : components.Backdrop) != null ? _ref2 : BackdropComponent;\n  const rootSlotProps = (_slotProps$root = slotProps == null ? void 0 : slotProps.root) != null ? _slotProps$root : componentsProps.root;\n  const backdropSlotProps = (_slotProps$backdrop = slotProps == null ? void 0 : slotProps.backdrop) != null ? _slotProps$backdrop : componentsProps.backdrop;\n  const rootProps = useSlotProps({\n    elementType: RootSlot,\n    externalSlotProps: rootSlotProps,\n    externalForwardedProps: other,\n    getSlotProps: getRootProps,\n    additionalProps: {\n      ref,\n      as: component\n    },\n    ownerState,\n    className: clsx(className, rootSlotProps == null ? void 0 : rootSlotProps.className, classes == null ? void 0 : classes.root, !ownerState.open && ownerState.exited && (classes == null ? void 0 : classes.hidden))\n  });\n  const backdropProps = useSlotProps({\n    elementType: BackdropSlot,\n    externalSlotProps: backdropSlotProps,\n    additionalProps: BackdropProps,\n    getSlotProps: otherHandlers => {\n      return getBackdropProps(_extends({}, otherHandlers, {\n        onClick: e => {\n          if (onBackdropClick) {\n            onBackdropClick(e);\n          }\n          if (otherHandlers != null && otherHandlers.onClick) {\n            otherHandlers.onClick(e);\n          }\n        }\n      }));\n    },\n    className: clsx(backdropSlotProps == null ? void 0 : backdropSlotProps.className, BackdropProps == null ? void 0 : BackdropProps.className, classes == null ? void 0 : classes.backdrop),\n    ownerState\n  });\n  if (!keepMounted && !open && (!hasTransition || exited)) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(Portal, {\n    ref: portalRef,\n    container: container,\n    disablePortal: disablePortal,\n    children: /*#__PURE__*/_jsxs(RootSlot, _extends({}, rootProps, {\n      children: [!hideBackdrop && BackdropComponent ? /*#__PURE__*/_jsx(BackdropSlot, _extends({}, backdropProps)) : null, /*#__PURE__*/_jsx(FocusTrap, {\n        disableEnforceFocus: disableEnforceFocus,\n        disableAutoFocus: disableAutoFocus,\n        disableRestoreFocus: disableRestoreFocus,\n        isEnabled: isTopModal,\n        open: open,\n        children: /*#__PURE__*/React.cloneElement(children, childProps)\n      })]\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Modal.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A backdrop component. This prop enables custom backdrop rendering.\n   * @deprecated Use `slots.backdrop` instead. While this prop currently works, it will be removed in the next major version.\n   * Use the `slots.backdrop` prop to make your application ready for the next version of Material UI.\n   * @default styled(Backdrop, {\n   *   name: 'MuiModal',\n   *   slot: 'Backdrop',\n   *   overridesResolver: (props, styles) => {\n   *     return styles.backdrop;\n   *   },\n   * })({\n   *   zIndex: -1,\n   * })\n   */\n  BackdropComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Backdrop`](/material-ui/api/backdrop/) element.\n   * @deprecated Use `slotProps.backdrop` instead.\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * When set to true the Modal waits until a nested Transition is completed before closing.\n   * @default false\n   */\n  closeAfterTransition: PropTypes.bool,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Backdrop: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * An HTML element or function that returns one.\n   * The `container` will have the portal children appended to it.\n   *\n   * You can also provide a callback, which is called in a React layout effect.\n   * This lets you set the container from a ref, and also makes server-side rendering possible.\n   *\n   * By default, it uses the body of the top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * If `true`, the modal will not automatically shift focus to itself when it opens, and\n   * replace it to the last focused element when it closes.\n   * This also works correctly with any modal children that have the `disableAutoFocus` prop.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableAutoFocus: PropTypes.bool,\n  /**\n   * If `true`, the modal will not prevent focus from leaving the modal while open.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableEnforceFocus: PropTypes.bool,\n  /**\n   * If `true`, hitting escape will not fire the `onClose` callback.\n   * @default false\n   */\n  disableEscapeKeyDown: PropTypes.bool,\n  /**\n   * The `children` will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool,\n  /**\n   * If `true`, the modal will not restore focus to previously focused element once\n   * modal is hidden or unmounted.\n   * @default false\n   */\n  disableRestoreFocus: PropTypes.bool,\n  /**\n   * Disable the scroll lock behavior.\n   * @default false\n   */\n  disableScrollLock: PropTypes.bool,\n  /**\n   * If `true`, the backdrop is not rendered.\n   * @default false\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Always keep the children in the DOM.\n   * This prop can be useful in SEO situation or\n   * when you want to maximize the responsiveness of the Modal.\n   * @default false\n   */\n  keepMounted: PropTypes.bool,\n  /**\n   * Callback fired when the backdrop is clicked.\n   * @deprecated Use the `onClose` prop with the `reason` argument to handle the `backdropClick` events.\n   */\n  onBackdropClick: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * A function called when a transition enters.\n   */\n  onTransitionEnter: PropTypes.func,\n  /**\n   * A function called when a transition has exited.\n   */\n  onTransitionExited: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The props used for each slot inside the Modal.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Modal.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Modal;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}