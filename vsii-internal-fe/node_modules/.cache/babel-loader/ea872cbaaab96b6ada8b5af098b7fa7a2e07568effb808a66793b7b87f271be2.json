{"ast": null, "code": "import React from'react';import{Box,Grid,Tooltip,useTheme,Typography}from'@mui/material';import{FormattedMessage}from'react-intl';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ColorNoteTooltip=_ref=>{let{notes,children,width}=_ref;const theme=useTheme();return/*#__PURE__*/_jsx(Tooltip,{slotProps:{tooltip:{sx:{backgroundColor:'#a6a6a6'}}},title:/*#__PURE__*/_jsx(Grid,{container:true,sx:{[theme.breakpoints.up('md')]:{width:width?width:'120px'}},children:notes.map((type,index)=>/*#__PURE__*/_jsxs(Grid,{container:true,spacing:1,sx:{alignItems:'center',padding:'5px'},children:[/*#__PURE__*/_jsx(Grid,{item:true,children:/*#__PURE__*/_jsx(Box,{style:{backgroundColor:type.color,width:15,height:10,display:'flex',justifyContent:'center',alignItems:'center',borderRadius:'4px'}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:true,zeroMinWidth:true,children:/*#__PURE__*/_jsx(Typography,{sx:{color:'white'},align:\"left\",variant:\"body2\",fontSize:10,children:/*#__PURE__*/_jsx(FormattedMessage,{id:type.label})})})]},index))}),placement:\"top\",children:/*#__PURE__*/_jsx(Box,{children:children})});};export default ColorNoteTooltip;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}