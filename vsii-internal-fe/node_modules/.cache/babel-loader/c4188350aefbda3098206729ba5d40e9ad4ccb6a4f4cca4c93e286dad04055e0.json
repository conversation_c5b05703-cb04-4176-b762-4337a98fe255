{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/pages/non-billable-monitoring/non-bill-by-member/index.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport { getSearchParam, setLocalStorageSearchTime, transformObject } from 'utils/common';\nimport { checkAllowedPermission, checkAllowedTab } from 'utils/authorization';\nimport { SEARCH_PARAM_KEY, nonBillMonitoringTabs } from 'constants/Common';\nimport { openDeniedPermission } from 'store/slice/deniedPermissionSlice';\nimport { nonBillConfig } from '../Config';\nimport WarningNonBillByMember from './WarningNBMMember';\nimport { TabPanel } from 'components/extended/Tabs';\nimport { getWeeksPeriodsInYear } from 'utils/date';\nimport NonBillByMemberTab from './NBMByMember';\nimport { useAppDispatch } from 'app/hooks';\nimport { TabCustom } from 'containers';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// ==============================|| NonBill By Member ||============================== //\n/**\n *  URL Params\n *  tab\n *  year\n *  week\n *  departmentId\n *  ====== tab 0 - By Member ======\n *  timeStatus\n */\n\nconst NonBillByMember = () => {\n  _s();\n  const [weeks, setWeeks] = useState([]);\n  const [searchParams, setSearchParams] = useSearchParams();\n  const keyParams = [SEARCH_PARAM_KEY.tab, SEARCH_PARAM_KEY.year, SEARCH_PARAM_KEY.week, SEARCH_PARAM_KEY.departmentId, SEARCH_PARAM_KEY.timeStatus];\n  const params = getSearchParam(keyParams, searchParams);\n  transformObject(params);\n  const defaultConditions = {\n    ...nonBillConfig,\n    ...params\n  };\n  const dispatch = useAppDispatch();\n  const [formReset, setFormReset] = useState(defaultConditions);\n  const [tabValue, setTabValue] = useState(0);\n  const [weekYearforSearch, setWeekYearForSearch] = useState({\n    week: defaultConditions.week,\n    year: defaultConditions.year\n  });\n  const getWeekandYearWhenSearch = (weekSearch, yearSearch) => {\n    setWeekYearForSearch({\n      week: weekSearch,\n      year: yearSearch\n    });\n    // lưu thời gian vào localStorage\n    setLocalStorageSearchTime({\n      week: weekSearch,\n      year: yearSearch\n    });\n  };\n  const handleChangeTab = (event, newTabValue) => {\n    setTabValue(newTabValue);\n    setFormReset({\n      ...nonBillConfig\n    });\n    setSearchParams({\n      tab: newTabValue,\n      ...weekYearforSearch\n    });\n  };\n  const setWeeksFunc = async (year, week) => {\n    const items = getWeeksPeriodsInYear(year);\n    setWeeks(items);\n    if (items.length > 0) {\n      setFormReset(prev => ({\n        ...prev,\n        year,\n        week: week || items[0].value\n      }));\n    }\n  };\n  const handleChangeYear = e => {\n    const {\n      value\n    } = e.target;\n    setWeeksFunc(Number(value));\n  };\n  useEffect(() => {\n    let allowedTabs = checkAllowedTab(nonBillMonitoringTabs, params.tab);\n    const deniedTabs = allowedTabs.filter(item => {\n      const permission_key = nonBillMonitoringTabs.find(tab => tab.value === item).permission_key;\n      return permission_key && !checkAllowedPermission(permission_key) && item === params.tab;\n    });\n    if (deniedTabs.length) {\n      dispatch(openDeniedPermission(true));\n    }\n    setTabValue(allowedTabs[0]);\n    if (Number.isInteger(allowedTabs[0])) {\n      setSearchParams(prev => ({\n        ...transformObject(getSearchParam(keyParams, prev)),\n        tab: allowedTabs[0].toString()\n      }));\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [dispatch]);\n  useEffect(() => {\n    if (Number.isInteger(defaultConditions.year)) {\n      setWeeksFunc(defaultConditions.year, defaultConditions.week);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [tabValue]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(TabCustom, {\n      value: tabValue,\n      handleChange: handleChangeTab,\n      tabs: nonBillMonitoringTabs\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: tabValue,\n      index: 0,\n      children: /*#__PURE__*/_jsxDEV(NonBillByMemberTab, {\n        weeks: weeks,\n        params: params,\n        formReset: formReset,\n        handleChangeYear: handleChangeYear,\n        defaultConditions: defaultConditions,\n        getWeekandYearWhenSearch: getWeekandYearWhenSearch\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: tabValue,\n      index: 1,\n      children: /*#__PURE__*/_jsxDEV(WarningNonBillByMember, {\n        weeks: weeks,\n        params: params,\n        formReset: formReset,\n        handleChangeYear: handleChangeYear,\n        defaultConditions: defaultConditions,\n        getWeekandYearWhenSearch: getWeekandYearWhenSearch\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(NonBillByMember, \"rPRjOZCKznhKTkm7uNyQpAm6goE=\", false, function () {\n  return [useSearchParams, useAppDispatch];\n});\n_c = NonBillByMember;\nexport default NonBillByMember;\nvar _c;\n$RefreshReg$(_c, \"NonBillByMember\");", "map": {"version": 3, "names": ["useEffect", "useState", "useSearchParams", "getSearchParam", "setLocalStorageSearchTime", "transformObject", "checkAllowedPermission", "checkAllowedTab", "SEARCH_PARAM_KEY", "nonBillMonitoringTabs", "openDeniedPermission", "nonBillConfig", "WarningNonBillByMember", "TabPanel", "getWeeksPeriodsInYear", "NonBillByMemberTab", "useAppDispatch", "TabCustom", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "NonBillByMember", "_s", "weeks", "setWeeks", "searchParams", "setSearchParams", "keyParams", "tab", "year", "week", "departmentId", "timeStatus", "params", "defaultConditions", "dispatch", "formReset", "setFormReset", "tabValue", "setTabValue", "weekYearforSearch", "setWeekYearForSearch", "getWeekandYearWhenSearch", "weekSearch", "yearSearch", "handleChangeTab", "event", "newTabValue", "setWeeksFunc", "items", "length", "prev", "value", "handleChangeYear", "e", "target", "Number", "allowedTabs", "deniedTabs", "filter", "item", "permission_key", "find", "isInteger", "toString", "children", "handleChange", "tabs", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/pages/non-billable-monitoring/non-bill-by-member/index.tsx"], "sourcesContent": ["import { SyntheticEvent, useEffect, useState } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport { SelectChangeEvent } from '@mui/material';\n\nimport { getSearchParam, setLocalStorageSearchTime, transformObject } from 'utils/common';\nimport { checkAllowedPermission, checkAllowedTab } from 'utils/authorization';\nimport { SEARCH_PARAM_KEY, nonBillMonitoringTabs } from 'constants/Common';\nimport { openDeniedPermission } from 'store/slice/deniedPermissionSlice';\nimport { INonBillConfig, nonBillConfig } from '../Config';\nimport WarningNonBillByMember from './WarningNBMMember';\nimport { TabPanel } from 'components/extended/Tabs';\nimport { getWeeksPeriodsInYear } from 'utils/date';\nimport NonBillByMemberTab from './NBMByMember';\nimport { useAppDispatch } from 'app/hooks';\nimport { TabCustom } from 'containers';\nimport { IOption } from 'types';\n\n// ==============================|| NonBill By Member ||============================== //\n/**\n *  URL Params\n *  tab\n *  year\n *  week\n *  departmentId\n *  ====== tab 0 - By Member ======\n *  timeStatus\n */\n\nconst NonBillByMember = () => {\n    const [weeks, setWeeks] = useState<IOption[]>([]);\n\n    const [searchParams, setSearchParams] = useSearchParams();\n\n    const keyParams = [\n        SEARCH_PARAM_KEY.tab,\n        SEARCH_PARAM_KEY.year,\n        SEARCH_PARAM_KEY.week,\n        SEARCH_PARAM_KEY.departmentId,\n        SEARCH_PARAM_KEY.timeStatus\n    ];\n\n    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);\n\n    transformObject(params);\n\n    const defaultConditions = {\n        ...nonBillConfig,\n        ...params\n    };\n\n    const dispatch = useAppDispatch();\n\n    const [formReset, setFormReset] = useState<INonBillConfig>(defaultConditions);\n    const [tabValue, setTabValue] = useState(0);\n\n    const [weekYearforSearch, setWeekYearForSearch] = useState({\n        week: defaultConditions.week,\n        year: defaultConditions.year\n    });\n    const getWeekandYearWhenSearch = (weekSearch: string, yearSearch: string | number) => {\n        setWeekYearForSearch({ week: weekSearch, year: yearSearch as number });\n        // lưu thời gian vào localStorage\n        setLocalStorageSearchTime({ week: weekSearch, year: yearSearch });\n    };\n\n    const handleChangeTab = (event: SyntheticEvent, newTabValue: number) => {\n        setTabValue(newTabValue);\n        setFormReset({ ...nonBillConfig });\n        setSearchParams({ tab: newTabValue, ...weekYearforSearch } as any);\n    };\n\n    const setWeeksFunc = async (year: number, week?: string | number) => {\n        const items = getWeeksPeriodsInYear(year);\n\n        setWeeks(items);\n        if (items.length > 0) {\n            setFormReset((prev) => ({ ...prev, year, week: week || items[0].value }));\n        }\n    };\n\n    const handleChangeYear = (e: SelectChangeEvent<unknown>) => {\n        const { value } = e.target;\n\n        setWeeksFunc(Number(value));\n    };\n\n    useEffect(() => {\n        let allowedTabs = checkAllowedTab(nonBillMonitoringTabs, params.tab);\n        const deniedTabs = allowedTabs.filter((item) => {\n            const permission_key = nonBillMonitoringTabs.find((tab) => tab.value === item)!.permission_key;\n            return permission_key && !checkAllowedPermission(permission_key) && item === params.tab;\n        });\n\n        if (deniedTabs.length) {\n            dispatch(openDeniedPermission(true));\n        }\n\n        setTabValue(allowedTabs[0]);\n        if (Number.isInteger(allowedTabs[0])) {\n            setSearchParams((prev) => ({\n                ...transformObject(getSearchParam(keyParams, prev)),\n                tab: allowedTabs[0].toString()\n            }));\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [dispatch]);\n\n    useEffect(() => {\n        if (Number.isInteger(defaultConditions.year)) {\n            setWeeksFunc(defaultConditions.year, defaultConditions.week);\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [tabValue]);\n\n    return (\n        <>\n            <TabCustom value={tabValue} handleChange={handleChangeTab} tabs={nonBillMonitoringTabs} />\n\n            <TabPanel value={tabValue} index={0}>\n                <NonBillByMemberTab\n                    weeks={weeks}\n                    params={params}\n                    formReset={formReset}\n                    handleChangeYear={handleChangeYear}\n                    defaultConditions={defaultConditions}\n                    getWeekandYearWhenSearch={getWeekandYearWhenSearch}\n                />\n            </TabPanel>\n\n            <TabPanel value={tabValue} index={1}>\n                <WarningNonBillByMember\n                    weeks={weeks}\n                    params={params}\n                    formReset={formReset}\n                    handleChangeYear={handleChangeYear}\n                    defaultConditions={defaultConditions}\n                    getWeekandYearWhenSearch={getWeekandYearWhenSearch}\n                />\n            </TabPanel>\n        </>\n    );\n};\n\nexport default NonBillByMember;\n"], "mappings": ";;AAAA,SAAyBA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3D,SAASC,eAAe,QAAQ,kBAAkB;AAGlD,SAASC,cAAc,EAAEC,yBAAyB,EAAEC,eAAe,QAAQ,cAAc;AACzF,SAASC,sBAAsB,EAAEC,eAAe,QAAQ,qBAAqB;AAC7E,SAASC,gBAAgB,EAAEC,qBAAqB,QAAQ,kBAAkB;AAC1E,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,SAAyBC,aAAa,QAAQ,WAAW;AACzD,OAAOC,sBAAsB,MAAM,oBAAoB;AACvD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,qBAAqB,QAAQ,YAAY;AAClD,OAAOC,kBAAkB,MAAM,eAAe;AAC9C,SAASC,cAAc,QAAQ,WAAW;AAC1C,SAASC,SAAS,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAY,EAAE,CAAC;EAEjD,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,eAAe,CAAC,CAAC;EAEzD,MAAM0B,SAAS,GAAG,CACdpB,gBAAgB,CAACqB,GAAG,EACpBrB,gBAAgB,CAACsB,IAAI,EACrBtB,gBAAgB,CAACuB,IAAI,EACrBvB,gBAAgB,CAACwB,YAAY,EAC7BxB,gBAAgB,CAACyB,UAAU,CAC9B;EAED,MAAMC,MAA8B,GAAG/B,cAAc,CAACyB,SAAS,EAAEF,YAAY,CAAC;EAE9ErB,eAAe,CAAC6B,MAAM,CAAC;EAEvB,MAAMC,iBAAiB,GAAG;IACtB,GAAGxB,aAAa;IAChB,GAAGuB;EACP,CAAC;EAED,MAAME,QAAQ,GAAGpB,cAAc,CAAC,CAAC;EAEjC,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAiBkC,iBAAiB,CAAC;EAC7E,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC;EAE3C,MAAM,CAACwC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzC,QAAQ,CAAC;IACvD8B,IAAI,EAAEI,iBAAiB,CAACJ,IAAI;IAC5BD,IAAI,EAAEK,iBAAiB,CAACL;EAC5B,CAAC,CAAC;EACF,MAAMa,wBAAwB,GAAGA,CAACC,UAAkB,EAAEC,UAA2B,KAAK;IAClFH,oBAAoB,CAAC;MAAEX,IAAI,EAAEa,UAAU;MAAEd,IAAI,EAAEe;IAAqB,CAAC,CAAC;IACtE;IACAzC,yBAAyB,CAAC;MAAE2B,IAAI,EAAEa,UAAU;MAAEd,IAAI,EAAEe;IAAW,CAAC,CAAC;EACrE,CAAC;EAED,MAAMC,eAAe,GAAGA,CAACC,KAAqB,EAAEC,WAAmB,KAAK;IACpER,WAAW,CAACQ,WAAW,CAAC;IACxBV,YAAY,CAAC;MAAE,GAAG3B;IAAc,CAAC,CAAC;IAClCgB,eAAe,CAAC;MAAEE,GAAG,EAAEmB,WAAW;MAAE,GAAGP;IAAkB,CAAQ,CAAC;EACtE,CAAC;EAED,MAAMQ,YAAY,GAAG,MAAAA,CAAOnB,IAAY,EAAEC,IAAsB,KAAK;IACjE,MAAMmB,KAAK,GAAGpC,qBAAqB,CAACgB,IAAI,CAAC;IAEzCL,QAAQ,CAACyB,KAAK,CAAC;IACf,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAClBb,YAAY,CAAEc,IAAI,KAAM;QAAE,GAAGA,IAAI;QAAEtB,IAAI;QAAEC,IAAI,EAAEA,IAAI,IAAImB,KAAK,CAAC,CAAC,CAAC,CAACG;MAAM,CAAC,CAAC,CAAC;IAC7E;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAIC,CAA6B,IAAK;IACxD,MAAM;MAAEF;IAAM,CAAC,GAAGE,CAAC,CAACC,MAAM;IAE1BP,YAAY,CAACQ,MAAM,CAACJ,KAAK,CAAC,CAAC;EAC/B,CAAC;EAEDrD,SAAS,CAAC,MAAM;IACZ,IAAI0D,WAAW,GAAGnD,eAAe,CAACE,qBAAqB,EAAEyB,MAAM,CAACL,GAAG,CAAC;IACpE,MAAM8B,UAAU,GAAGD,WAAW,CAACE,MAAM,CAAEC,IAAI,IAAK;MAC5C,MAAMC,cAAc,GAAGrD,qBAAqB,CAACsD,IAAI,CAAElC,GAAG,IAAKA,GAAG,CAACwB,KAAK,KAAKQ,IAAI,CAAC,CAAEC,cAAc;MAC9F,OAAOA,cAAc,IAAI,CAACxD,sBAAsB,CAACwD,cAAc,CAAC,IAAID,IAAI,KAAK3B,MAAM,CAACL,GAAG;IAC3F,CAAC,CAAC;IAEF,IAAI8B,UAAU,CAACR,MAAM,EAAE;MACnBf,QAAQ,CAAC1B,oBAAoB,CAAC,IAAI,CAAC,CAAC;IACxC;IAEA8B,WAAW,CAACkB,WAAW,CAAC,CAAC,CAAC,CAAC;IAC3B,IAAID,MAAM,CAACO,SAAS,CAACN,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;MAClC/B,eAAe,CAAEyB,IAAI,KAAM;QACvB,GAAG/C,eAAe,CAACF,cAAc,CAACyB,SAAS,EAAEwB,IAAI,CAAC,CAAC;QACnDvB,GAAG,EAAE6B,WAAW,CAAC,CAAC,CAAC,CAACO,QAAQ,CAAC;MACjC,CAAC,CAAC,CAAC;IACP;IACA;EACJ,CAAC,EAAE,CAAC7B,QAAQ,CAAC,CAAC;EAEdpC,SAAS,CAAC,MAAM;IACZ,IAAIyD,MAAM,CAACO,SAAS,CAAC7B,iBAAiB,CAACL,IAAI,CAAC,EAAE;MAC1CmB,YAAY,CAACd,iBAAiB,CAACL,IAAI,EAAEK,iBAAiB,CAACJ,IAAI,CAAC;IAChE;IACA;EACJ,CAAC,EAAE,CAACQ,QAAQ,CAAC,CAAC;EAEd,oBACIpB,OAAA,CAAAE,SAAA;IAAA6C,QAAA,gBACI/C,OAAA,CAACF,SAAS;MAACoC,KAAK,EAAEd,QAAS;MAAC4B,YAAY,EAAErB,eAAgB;MAACsB,IAAI,EAAE3D;IAAsB;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE1FrD,OAAA,CAACN,QAAQ;MAACwC,KAAK,EAAEd,QAAS;MAACkC,KAAK,EAAE,CAAE;MAAAP,QAAA,eAChC/C,OAAA,CAACJ,kBAAkB;QACfS,KAAK,EAAEA,KAAM;QACbU,MAAM,EAAEA,MAAO;QACfG,SAAS,EAAEA,SAAU;QACrBiB,gBAAgB,EAAEA,gBAAiB;QACnCnB,iBAAiB,EAAEA,iBAAkB;QACrCQ,wBAAwB,EAAEA;MAAyB;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAEXrD,OAAA,CAACN,QAAQ;MAACwC,KAAK,EAAEd,QAAS;MAACkC,KAAK,EAAE,CAAE;MAAAP,QAAA,eAChC/C,OAAA,CAACP,sBAAsB;QACnBY,KAAK,EAAEA,KAAM;QACbU,MAAM,EAAEA,MAAO;QACfG,SAAS,EAAEA,SAAU;QACrBiB,gBAAgB,EAAEA,gBAAiB;QACnCnB,iBAAiB,EAAEA,iBAAkB;QACrCQ,wBAAwB,EAAEA;MAAyB;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA,eACb,CAAC;AAEX,CAAC;AAACjD,EAAA,CAjHID,eAAe;EAAA,QAGuBpB,eAAe,EAmBtCc,cAAc;AAAA;AAAA0D,EAAA,GAtB7BpD,eAAe;AAmHrB,eAAeA,eAAe;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}