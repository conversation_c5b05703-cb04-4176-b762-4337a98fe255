{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/AddOrEditBiddingOtherInfo.tsx\";\n// material-ui\nimport { Grid } from '@mui/material';\n\n// project imports\nimport { Input } from 'components/extended/Form';\nimport { E_IS_LOGTIME, E_BIDDING_STATUS } from 'constants/Common';\nimport { Member } from 'containers/search';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AddOrEditBiddingOtherInfo = props => {\n  const {\n    handleChangeUserContact,\n    status\n  } = props;\n  const {\n    salesReport\n  } = TEXT_CONFIG_SCREEN;\n  return /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 2,\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(Member, {\n        isLogTime: E_IS_LOGTIME.YES,\n        isDefaultAll: true,\n        name: \"otherInfo.contact\",\n        label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.allSalesPineline + '-contact'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 28\n        }, this),\n        isIdHexString: true,\n        handleChange: handleChangeUserContact,\n        disabled: status === E_BIDDING_STATUS.CONTRACT\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.allSalesPineline + '-phone-number'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 28\n        }, this),\n        name: \"otherInfo.phoneNumber\",\n        disabled: status === E_BIDDING_STATUS.CONTRACT\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.allSalesPineline + '-presale-folder'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 28\n        }, this),\n        name: \"otherInfo.presaleFolder\",\n        disabled: status === E_BIDDING_STATUS.CONTRACT\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.allSalesPineline + '-email-address'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 28\n        }, this),\n        name: \"otherInfo.emailAddress\",\n        disabled: status === E_BIDDING_STATUS.CONTRACT\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.allSalesPineline + '-customer-contact'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 28\n        }, this) // thieu\n        ,\n        name: \"otherInfo.customerContact\",\n        disabled: status === E_BIDDING_STATUS.CONTRACT\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 9\n  }, this);\n};\n_c = AddOrEditBiddingOtherInfo;\nexport default AddOrEditBiddingOtherInfo;\nvar _c;\n$RefreshReg$(_c, \"AddOrEditBiddingOtherInfo\");", "map": {"version": 3, "names": ["Grid", "Input", "E_IS_LOGTIME", "E_BIDDING_STATUS", "Member", "FormattedMessage", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "AddOrEditBiddingOtherInfo", "props", "handleChangeUserContact", "status", "salesReport", "container", "spacing", "children", "item", "xs", "lg", "isLogTime", "YES", "isDefaultAll", "name", "label", "id", "allSalesPineline", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isIdHexString", "handleChange", "disabled", "CONTRACT", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/AddOrEditBiddingOtherInfo.tsx"], "sourcesContent": ["// material-ui\nimport { Grid } from '@mui/material';\n\n// project imports\nimport { Input } from 'components/extended/Form';\nimport { E_IS_LOGTIME, E_BIDDING_STATUS } from 'constants/Common';\nimport { Member } from 'containers/search';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\nimport { IMember } from 'types/member';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\n\ninterface IAddOrEditBiddingOtherInfo {\n    handleChangeUserContact: (userSelected: IMember) => void;\n    status?: string;\n}\n\nconst AddOrEditBiddingOtherInfo = (props: IAddOrEditBiddingOtherInfo) => {\n    const { handleChangeUserContact, status } = props;\n\n    const { salesReport } = TEXT_CONFIG_SCREEN;\n\n    return (\n        <Grid container spacing={2}>\n            <Grid item xs={12} lg={6}>\n                <Member\n                    isLogTime={E_IS_LOGTIME.YES}\n                    isDefaultAll\n                    name=\"otherInfo.contact\"\n                    label={<FormattedMessage id={salesReport.allSalesPineline + '-contact'} />}\n                    isIdHexString\n                    handleChange={handleChangeUserContact}\n                    disabled={status === E_BIDDING_STATUS.CONTRACT}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <Input\n                    label={<FormattedMessage id={salesReport.allSalesPineline + '-phone-number'} />}\n                    name=\"otherInfo.phoneNumber\"\n                    disabled={status === E_BIDDING_STATUS.CONTRACT}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <Input\n                    label={<FormattedMessage id={salesReport.allSalesPineline + '-presale-folder'} />}\n                    name=\"otherInfo.presaleFolder\"\n                    disabled={status === E_BIDDING_STATUS.CONTRACT}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <Input\n                    label={<FormattedMessage id={salesReport.allSalesPineline + '-email-address'} />}\n                    name=\"otherInfo.emailAddress\"\n                    disabled={status === E_BIDDING_STATUS.CONTRACT}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <Input\n                    label={<FormattedMessage id={salesReport.allSalesPineline + '-customer-contact'} />} // thieu\n                    name=\"otherInfo.customerContact\"\n                    disabled={status === E_BIDDING_STATUS.CONTRACT}\n                />\n            </Grid>\n        </Grid>\n    );\n};\n\nexport default AddOrEditBiddingOtherInfo;\n"], "mappings": ";AAAA;AACA,SAASA,IAAI,QAAQ,eAAe;;AAEpC;AACA,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,YAAY,EAAEC,gBAAgB,QAAQ,kBAAkB;AACjE,SAASC,MAAM,QAAQ,mBAAmB;;AAE1C;AACA,SAASC,gBAAgB,QAAQ,YAAY;AAE7C,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOtD,MAAMC,yBAAyB,GAAIC,KAAiC,IAAK;EACrE,MAAM;IAAEC,uBAAuB;IAAEC;EAAO,CAAC,GAAGF,KAAK;EAEjD,MAAM;IAAEG;EAAY,CAAC,GAAGP,kBAAkB;EAE1C,oBACIE,OAAA,CAACR,IAAI;IAACc,SAAS;IAACC,OAAO,EAAE,CAAE;IAAAC,QAAA,gBACvBR,OAAA,CAACR,IAAI;MAACiB,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrBR,OAAA,CAACJ,MAAM;QACHgB,SAAS,EAAElB,YAAY,CAACmB,GAAI;QAC5BC,YAAY;QACZC,IAAI,EAAC,mBAAmB;QACxBC,KAAK,eAAEhB,OAAA,CAACH,gBAAgB;UAACoB,EAAE,EAAEZ,WAAW,CAACa,gBAAgB,GAAG;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3EC,aAAa;QACbC,YAAY,EAAErB,uBAAwB;QACtCsB,QAAQ,EAAErB,MAAM,KAAKT,gBAAgB,CAAC+B;MAAS;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPtB,OAAA,CAACR,IAAI;MAACiB,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrBR,OAAA,CAACP,KAAK;QACFuB,KAAK,eAAEhB,OAAA,CAACH,gBAAgB;UAACoB,EAAE,EAAEZ,WAAW,CAACa,gBAAgB,GAAG;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAChFP,IAAI,EAAC,uBAAuB;QAC5BU,QAAQ,EAAErB,MAAM,KAAKT,gBAAgB,CAAC+B;MAAS;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPtB,OAAA,CAACR,IAAI;MAACiB,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrBR,OAAA,CAACP,KAAK;QACFuB,KAAK,eAAEhB,OAAA,CAACH,gBAAgB;UAACoB,EAAE,EAAEZ,WAAW,CAACa,gBAAgB,GAAG;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAClFP,IAAI,EAAC,yBAAyB;QAC9BU,QAAQ,EAAErB,MAAM,KAAKT,gBAAgB,CAAC+B;MAAS;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPtB,OAAA,CAACR,IAAI;MAACiB,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrBR,OAAA,CAACP,KAAK;QACFuB,KAAK,eAAEhB,OAAA,CAACH,gBAAgB;UAACoB,EAAE,EAAEZ,WAAW,CAACa,gBAAgB,GAAG;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACjFP,IAAI,EAAC,wBAAwB;QAC7BU,QAAQ,EAAErB,MAAM,KAAKT,gBAAgB,CAAC+B;MAAS;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPtB,OAAA,CAACR,IAAI;MAACiB,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrBR,OAAA,CAACP,KAAK;QACFuB,KAAK,eAAEhB,OAAA,CAACH,gBAAgB;UAACoB,EAAE,EAAEZ,WAAW,CAACa,gBAAgB,GAAG;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE,CAAC;QAAA;QACrFP,IAAI,EAAC,2BAA2B;QAChCU,QAAQ,EAAErB,MAAM,KAAKT,gBAAgB,CAAC+B;MAAS;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEf,CAAC;AAACK,EAAA,GAhDI1B,yBAAyB;AAkD/B,eAAeA,yBAAyB;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}