{"ast": null, "code": "import{Link,TableBody,TableCell,TableRow}from'@mui/material';import{FormattedMessage}from'react-intl';import{DATE_FORMAT}from'constants/Common';import{dateFormat}from'utils/date';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const BiddingTrackingTBody=props=>{const{pageNumber,pageSize,biddingTracking}=props;return/*#__PURE__*/_jsx(TableBody,{children:biddingTracking===null||biddingTracking===void 0?void 0:biddingTracking.map((bidding,key)=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{sx:{width:'3%',px:'6px'},children:pageSize*pageNumber+key+1}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'6%',px:'6px'},children:bidding.type}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'7%',px:'6px'},children:bidding.khlcntNumber}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'20%',px:'6px'},children:bidding.packageName}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'7%',px:'6px'},children:dateFormat(bidding.postingDate,undefined,false)}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'7%',px:'6px'},children:dateFormat(bidding.biddingClosingTime,DATE_FORMAT.HHmmssDDMMYYYY,false)}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'6%',px:'6px'},children:bidding.biddingParticipationForm?/*#__PURE__*/_jsx(FormattedMessage,{id:\"\".concat(bidding.biddingParticipationForm)}):''}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'7%',px:'6px'},children:bidding.tbmtNumber}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'6%',px:'6px'},children:bidding.group}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'11%',px:'6px'},children:bidding.company}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'8%',px:'6px'},children:bidding.address}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'7%',px:'6px'},children:bidding.keyword}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'5%',px:'6px'},children:bidding.link?/*#__PURE__*/_jsx(Link,{sx:{color:'black',':hover':{color:'#1E88E5',cursor:'pointer'},textDecoration:'underline'},href:bidding.link,target:\"_blank\",underline:\"hover\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"click-here\"})}):null})]},key))});};export default BiddingTrackingTBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}