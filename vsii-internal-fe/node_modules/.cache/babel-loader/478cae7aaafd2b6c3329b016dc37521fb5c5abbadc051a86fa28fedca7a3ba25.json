{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/addorEditFlexibleReportConfig/condition/index.tsx\",\n  _s = $RefreshSig$();\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport { Box, ButtonBase, Chip, Divider, Typography } from '@mui/material';\nimport HighlightOffIcon from '@mui/icons-material/HighlightOff';\nimport { useFieldArray, useFormContext } from 'react-hook-form';\nimport { FormattedMessage } from 'react-intl';\nimport AddConditions from './AddConditions';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Condition = ({\n  columnsToSum\n}) => {\n  _s();\n  const {\n    Flexible_reporting_configuration\n  } = TEXT_CONFIG_SCREEN.administration.flexibleReport;\n  const methods = useFormContext();\n  const {\n    fields: condition,\n    append,\n    remove\n  } = useFieldArray({\n    control: methods.control,\n    name: 'conditions'\n  });\n  const handleOrCondition = () => {\n    append({\n      conditions: []\n    });\n  };\n  const handleDeleteCondition = index => {\n    remove(index);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    mt: 2,\n    children: /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        minWidth: \"100%\",\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: '#333',\n            display: 'flex',\n            gap: 1,\n            fontWeight: 600\n          },\n          variant: \"h3\",\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: Flexible_reporting_configuration + 'conditions'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        sx: {\n          overflowX: 'auto'\n        },\n        minWidth: \"100%\",\n        children: condition.map((item, index) => /*#__PURE__*/_jsxDEV(Box, {\n          maxWidth: 600,\n          children: [/*#__PURE__*/_jsxDEV(Divider, {\n            sx: {\n              minWidth: '130%',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"inline-flex\",\n              justifyContent: \"space-between\",\n              width: \"100%\",\n              gap: 2,\n              children: [/*#__PURE__*/_jsxDEV(AddConditions, {\n                nestingIndex: index,\n                columnsToSum: columnsToSum\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 37\n              }, this), index !== 0 && /*#__PURE__*/_jsxDEV(ButtonBase, {\n                onClick: () => handleDeleteCondition(index),\n                children: /*#__PURE__*/_jsxDEV(HighlightOffIcon, {\n                  sx: {\n                    fontSize: 25\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 60,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 41\n              }, this), index === condition.length - 1 ? /*#__PURE__*/_jsxDEV(ButtonBase, {\n                onClick: handleOrCondition,\n                children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                  sx: {\n                    fontSize: 25\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 41\n              }, this) : /*#__PURE__*/_jsxDEV(Divider, {\n                orientation: \"vertical\",\n                variant: \"middle\",\n                flexItem: true,\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Or\",\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 69,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 33\n            }, this)\n          }, void 0, false)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 25\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 9\n  }, this);\n};\n_s(Condition, \"jCH+sjJue8YTjOE0HKmlZdAYWqc=\", false, function () {\n  return [useFormContext, useFieldArray];\n});\n_c = Condition;\nexport default Condition;\nvar _c;\n$RefreshReg$(_c, \"Condition\");", "map": {"version": 3, "names": ["AddCircleOutlineIcon", "Box", "ButtonBase", "Chip", "Divider", "Typography", "HighlightOffIcon", "useFieldArray", "useFormContext", "FormattedMessage", "AddConditions", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Condition", "columnsToSum", "_s", "Flexible_reporting_configuration", "administration", "flexibleReport", "methods", "fields", "condition", "append", "remove", "control", "name", "handleOrCondition", "conditions", "handleDeleteCondition", "index", "mt", "children", "min<PERSON><PERSON><PERSON>", "sx", "color", "display", "gap", "fontWeight", "variant", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "overflowX", "map", "item", "max<PERSON><PERSON><PERSON>", "mb", "justifyContent", "width", "nestingIndex", "onClick", "fontSize", "length", "orientation", "flexItem", "label", "size", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/addorEditFlexibleReportConfig/condition/index.tsx"], "sourcesContent": ["import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport { Box, ButtonBase, Chip, Divider, Typography } from '@mui/material';\nimport HighlightOffIcon from '@mui/icons-material/HighlightOff';\nimport { useFieldArray, useFormContext } from 'react-hook-form';\nimport { FormattedMessage } from 'react-intl';\n\nimport { IConditionTypes } from 'types/flexible-report';\nimport AddConditions from './AddConditions';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\n\ninterface IConditionProps {\n    columnsToSum?: IConditionTypes[];\n}\n\nconst Condition = ({ columnsToSum }: IConditionProps) => {\n    const { Flexible_reporting_configuration } = TEXT_CONFIG_SCREEN.administration.flexibleReport;\n    const methods = useFormContext();\n\n    const {\n        fields: condition,\n        append,\n        remove\n    } = useFieldArray({\n        control: methods.control,\n        name: 'conditions'\n    });\n\n    const handleOrCondition = () => {\n        append({ conditions: [] });\n    };\n\n    const handleDeleteCondition = (index: number) => {\n        remove(index);\n    };\n\n    return (\n        <Box mt={2}>\n            <>\n                <Box minWidth=\"100%\">\n                    <Typography sx={{ color: '#333', display: 'flex', gap: 1, fontWeight: 600 }} variant=\"h3\">\n                        <FormattedMessage id={Flexible_reporting_configuration + 'conditions'} />\n                    </Typography>\n                </Box>\n\n                <Box\n                    display=\"flex\"\n                    sx={{\n                        overflowX: 'auto'\n                    }}\n                    minWidth=\"100%\"\n                >\n                    {condition.map((item, index) => (\n                        <Box key={item.id} maxWidth={600}>\n                            <Divider sx={{ minWidth: '130%', mb: 2 }}></Divider>\n                            <>\n                                <Box display=\"inline-flex\" justifyContent=\"space-between\" width=\"100%\" gap={2}>\n                                    <AddConditions nestingIndex={index} columnsToSum={columnsToSum} />\n                                    {index !== 0 && (\n                                        <ButtonBase onClick={() => handleDeleteCondition(index)}>\n                                            <HighlightOffIcon sx={{ fontSize: 25 }} />\n                                        </ButtonBase>\n                                    )}\n                                    {index === condition.length - 1 ? (\n                                        <ButtonBase onClick={handleOrCondition}>\n                                            <AddCircleOutlineIcon sx={{ fontSize: 25 }} />\n                                        </ButtonBase>\n                                    ) : (\n                                        <Divider orientation=\"vertical\" variant=\"middle\" flexItem>\n                                            <Chip label=\"Or\" size=\"small\" />\n                                        </Divider>\n                                    )}\n                                </Box>\n                            </>\n                        </Box>\n                    ))}\n                </Box>\n            </>\n        </Box>\n    );\n};\n\nexport default Condition;\n"], "mappings": ";;AAAA,OAAOA,oBAAoB,MAAM,sCAAsC;AACvE,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,OAAO,EAAEC,UAAU,QAAQ,eAAe;AAC1E,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,SAASC,aAAa,EAAEC,cAAc,QAAQ,iBAAiB;AAC/D,SAASC,gBAAgB,QAAQ,YAAY;AAG7C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAMtD,MAAMC,SAAS,GAAGA,CAAC;EAAEC;AAA8B,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAM;IAAEC;EAAiC,CAAC,GAAGR,kBAAkB,CAACS,cAAc,CAACC,cAAc;EAC7F,MAAMC,OAAO,GAAGd,cAAc,CAAC,CAAC;EAEhC,MAAM;IACFe,MAAM,EAAEC,SAAS;IACjBC,MAAM;IACNC;EACJ,CAAC,GAAGnB,aAAa,CAAC;IACdoB,OAAO,EAAEL,OAAO,CAACK,OAAO;IACxBC,IAAI,EAAE;EACV,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC5BJ,MAAM,CAAC;MAAEK,UAAU,EAAE;IAAG,CAAC,CAAC;EAC9B,CAAC;EAED,MAAMC,qBAAqB,GAAIC,KAAa,IAAK;IAC7CN,MAAM,CAACM,KAAK,CAAC;EACjB,CAAC;EAED,oBACInB,OAAA,CAACZ,GAAG;IAACgC,EAAE,EAAE,CAAE;IAAAC,QAAA,eACPrB,OAAA,CAAAE,SAAA;MAAAmB,QAAA,gBACIrB,OAAA,CAACZ,GAAG;QAACkC,QAAQ,EAAC,MAAM;QAAAD,QAAA,eAChBrB,OAAA,CAACR,UAAU;UAAC+B,EAAE,EAAE;YAAEC,KAAK,EAAE,MAAM;YAAEC,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE,CAAC;YAAEC,UAAU,EAAE;UAAI,CAAE;UAACC,OAAO,EAAC,IAAI;UAAAP,QAAA,eACrFrB,OAAA,CAACJ,gBAAgB;YAACiC,EAAE,EAAEvB,gCAAgC,GAAG;UAAa;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAENjC,OAAA,CAACZ,GAAG;QACAqC,OAAO,EAAC,MAAM;QACdF,EAAE,EAAE;UACAW,SAAS,EAAE;QACf,CAAE;QACFZ,QAAQ,EAAC,MAAM;QAAAD,QAAA,EAEdV,SAAS,CAACwB,GAAG,CAAC,CAACC,IAAI,EAAEjB,KAAK,kBACvBnB,OAAA,CAACZ,GAAG;UAAeiD,QAAQ,EAAE,GAAI;UAAAhB,QAAA,gBAC7BrB,OAAA,CAACT,OAAO;YAACgC,EAAE,EAAE;cAAED,QAAQ,EAAE,MAAM;cAAEgB,EAAE,EAAE;YAAE;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACpDjC,OAAA,CAAAE,SAAA;YAAAmB,QAAA,eACIrB,OAAA,CAACZ,GAAG;cAACqC,OAAO,EAAC,aAAa;cAACc,cAAc,EAAC,eAAe;cAACC,KAAK,EAAC,MAAM;cAACd,GAAG,EAAE,CAAE;cAAAL,QAAA,gBAC1ErB,OAAA,CAACH,aAAa;gBAAC4C,YAAY,EAAEtB,KAAM;gBAACf,YAAY,EAAEA;cAAa;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACjEd,KAAK,KAAK,CAAC,iBACRnB,OAAA,CAACX,UAAU;gBAACqD,OAAO,EAAEA,CAAA,KAAMxB,qBAAqB,CAACC,KAAK,CAAE;gBAAAE,QAAA,eACpDrB,OAAA,CAACP,gBAAgB;kBAAC8B,EAAE,EAAE;oBAAEoB,QAAQ,EAAE;kBAAG;gBAAE;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CACf,EACAd,KAAK,KAAKR,SAAS,CAACiC,MAAM,GAAG,CAAC,gBAC3B5C,OAAA,CAACX,UAAU;gBAACqD,OAAO,EAAE1B,iBAAkB;gBAAAK,QAAA,eACnCrB,OAAA,CAACb,oBAAoB;kBAACoC,EAAE,EAAE;oBAAEoB,QAAQ,EAAE;kBAAG;gBAAE;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,gBAEbjC,OAAA,CAACT,OAAO;gBAACsD,WAAW,EAAC,UAAU;gBAACjB,OAAO,EAAC,QAAQ;gBAACkB,QAAQ;gBAAAzB,QAAA,eACrDrB,OAAA,CAACV,IAAI;kBAACyD,KAAK,EAAC,IAAI;kBAACC,IAAI,EAAC;gBAAO;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CACZ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC,gBACR,CAAC;QAAA,GApBGG,IAAI,CAACP,EAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqBZ,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA,eACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEd,CAAC;AAAC5B,EAAA,CAjEIF,SAAS;EAAA,QAEKR,cAAc,EAM1BD,aAAa;AAAA;AAAAuD,EAAA,GARf9C,SAAS;AAmEf,eAAeA,SAAS;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}