{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { useThemeProps } from '@mui/material/styles';\nimport { useDefaultDates, useUtils } from '../internals/hooks/useUtils';\nimport { parsePickerInputValue, parseNonNullablePickerDate } from '../internals/utils/date-utils';\nexport function useDateTimePickerDefaultizedProps(props, name) {\n  var _themeProps$ampm, _themeProps$minDateTi, _themeProps$maxDateTi, _themeProps$minDateTi2, _themeProps$maxDateTi2;\n\n  // This is technically unsound if the type parameters appear in optional props.\n  // Optional props can be filled by `useThemeProps` with types that don't match the type parameters.\n  const themeProps = useThemeProps({\n    props,\n    name\n  });\n  const utils = useUtils();\n  const defaultDates = useDefaultDates();\n  const ampm = (_themeProps$ampm = themeProps.ampm) != null ? _themeProps$ampm : utils.is12HourCycleInCurrentLocale();\n  if (themeProps.orientation != null && themeProps.orientation !== 'portrait') {\n    throw new Error('We are not supporting custom orientation for DateTimePicker yet :(');\n  }\n  return _extends({\n    ampm,\n    orientation: 'portrait',\n    openTo: 'day',\n    views: ['year', 'day', 'hours', 'minutes'],\n    ampmInClock: true,\n    acceptRegex: ampm ? /[\\dap]/gi : /\\d/gi,\n    disableMaskedInput: false,\n    inputFormat: ampm ? utils.formats.keyboardDateTime12h : utils.formats.keyboardDateTime24h,\n    disableIgnoringDatePartForTimeValidation: Boolean(themeProps.minDateTime || themeProps.maxDateTime),\n    disablePast: false,\n    disableFuture: false\n  }, themeProps, {\n    minDate: parseNonNullablePickerDate(utils, (_themeProps$minDateTi = themeProps.minDateTime) != null ? _themeProps$minDateTi : themeProps.minDate, defaultDates.minDate),\n    maxDate: parseNonNullablePickerDate(utils, (_themeProps$maxDateTi = themeProps.maxDateTime) != null ? _themeProps$maxDateTi : themeProps.maxDate, defaultDates.maxDate),\n    minTime: (_themeProps$minDateTi2 = themeProps.minDateTime) != null ? _themeProps$minDateTi2 : themeProps.minTime,\n    maxTime: (_themeProps$maxDateTi2 = themeProps.maxDateTime) != null ? _themeProps$maxDateTi2 : themeProps.maxTime\n  });\n}\nexport const dateTimePickerValueManager = {\n  emptyValue: null,\n  getTodayValue: utils => utils.date(),\n  parseInput: parsePickerInputValue,\n  areValuesEqual: (utils, a, b) => utils.isEqual(a, b)\n};\nexport const resolveViewTypeFromView = view => {\n  switch (view) {\n    case 'year':\n    case 'month':\n    case 'day':\n      return 'calendar';\n    default:\n      return 'clock';\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}