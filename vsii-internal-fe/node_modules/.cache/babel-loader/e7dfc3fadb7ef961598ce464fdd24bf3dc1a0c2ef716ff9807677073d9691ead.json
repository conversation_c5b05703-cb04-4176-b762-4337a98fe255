{"ast": null, "code": "import { sync } from '../frameloop/index.mjs';\nimport { useState, useCallback } from 'react';\nimport { useIsMounted } from './use-is-mounted.mjs';\nfunction useForceUpdate() {\n  const isMounted = useIsMounted();\n  const [forcedRenderCount, setForcedRenderCount] = useState(0);\n  const forceRender = useCallback(() => {\n    isMounted.current && setForcedRenderCount(forcedRenderCount + 1);\n  }, [forcedRenderCount]);\n  /**\n   * Defer this to the end of the next animation frame in case there are multiple\n   * synchronous calls.\n   */\n  const deferredForceRender = useCallback(() => sync.postRender(forceRender), [forceRender]);\n  return [deferredForceRender, forcedRenderCount];\n}\nexport { useForceUpdate };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}