{"ast": null, "code": "import { IntlFormatError } from './error';\nimport { filterProps, getNamedFormat } from './utils';\nvar NUMBER_FORMAT_OPTIONS = ['style', 'currency', 'unit', 'unitDisplay', 'useGrouping', 'minimumIntegerDigits', 'minimumFractionDigits', 'maximumFractionDigits', 'minimumSignificantDigits', 'maximumSignificantDigits',\n// ES2020 NumberFormat\n'compactDisplay', 'currencyDisplay', 'currencySign', 'notation', 'signDisplay', 'unit', 'unitDisplay', 'numberingSystem',\n// ES2023 NumberFormat\n'trailingZeroDisplay', 'roundingPriority', 'roundingIncrement', 'roundingMode'];\nexport function getFormatter(_a, getNumberFormat, options) {\n  var locale = _a.locale,\n    formats = _a.formats,\n    onError = _a.onError;\n  if (options === void 0) {\n    options = {};\n  }\n  var format = options.format;\n  var defaults = format && getNamedFormat(formats, 'number', format, onError) || {};\n  var filteredOptions = filterProps(options, NUMBER_FORMAT_OPTIONS, defaults);\n  return getNumberFormat(locale, filteredOptions);\n}\nexport function formatNumber(config, getNumberFormat, value, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  try {\n    return getFormatter(config, getNumberFormat, options).format(value);\n  } catch (e) {\n    config.onError(new IntlFormatError('Error formatting number.', config.locale, e));\n  }\n  return String(value);\n}\nexport function formatNumberToParts(config, getNumberFormat, value, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  try {\n    return getFormatter(config, getNumberFormat, options).formatToParts(value);\n  } catch (e) {\n    config.onError(new IntlFormatError('Error formatting number.', config.locale, e));\n  }\n  return [];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}