{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/SupplierCheckingThead.tsx\";\n// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SupplierCheckingThead = () => {\n  const {\n    salesReport\n  } = TEXT_CONFIG_SCREEN;\n  return /*#__PURE__*/_jsxDEV(TableHead, {\n    children: /*#__PURE__*/_jsxDEV(TableRow, {\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.salesLead + 'supplier-no'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.salesLead + 'supplier-supplier-name'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.salesLead + 'supplier-technology'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.salesLead + 'supplier-quantity'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.salesLead + 'supplier-from-date'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.salesLead + 'supplier-to-date'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.salesLead + 'supplier-unit-price'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.salesLead + 'supplier-pic-user-name'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.salesLead + 'supplier-work-type'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.salesLead + 'supplier-note'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"center\",\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.salesLead + 'supplier-action'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 9\n  }, this);\n};\n_c = SupplierCheckingThead;\nexport default SupplierCheckingThead;\nvar _c;\n$RefreshReg$(_c, \"SupplierCheckingThead\");", "map": {"version": 3, "names": ["TableCell", "TableHead", "TableRow", "TEXT_CONFIG_SCREEN", "FormattedMessage", "jsxDEV", "_jsxDEV", "SupplierCheckingThead", "salesReport", "children", "id", "salesLead", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "align", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/SupplierCheckingThead.tsx"], "sourcesContent": ["// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\n\nconst SupplierCheckingThead = () => {\n    const { salesReport } = TEXT_CONFIG_SCREEN;\n    return (\n        <TableHead>\n            <TableRow>\n                <TableCell>\n                    <FormattedMessage id={salesReport.salesLead + 'supplier-no'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.salesLead + 'supplier-supplier-name'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.salesLead + 'supplier-technology'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.salesLead + 'supplier-quantity'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.salesLead + 'supplier-from-date'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.salesLead + 'supplier-to-date'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.salesLead + 'supplier-unit-price'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.salesLead + 'supplier-pic-user-name'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.salesLead + 'supplier-work-type'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.salesLead + 'supplier-note'} />\n                </TableCell>\n\n                <TableCell align=\"center\">\n                    <FormattedMessage id={salesReport.salesLead + 'supplier-action'} />\n                </TableCell>\n            </TableRow>\n        </TableHead>\n    );\n};\n\nexport default SupplierCheckingThead;\n"], "mappings": ";AAAA;AACA,SAASA,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC9D,SAASC,kBAAkB,QAAQ,kBAAkB;;AAErD;AACA,SAASC,gBAAgB,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAChC,MAAM;IAAEC;EAAY,CAAC,GAAGL,kBAAkB;EAC1C,oBACIG,OAAA,CAACL,SAAS;IAAAQ,QAAA,eACNH,OAAA,CAACJ,QAAQ;MAAAO,QAAA,gBACLH,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,SAAS,GAAG;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,SAAS,GAAG;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,SAAS,GAAG;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,SAAS,GAAG;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,SAAS,GAAG;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,SAAS,GAAG;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,SAAS,GAAG;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,SAAS,GAAG;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,SAAS,GAAG;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,SAAS,GAAG;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eAEZT,OAAA,CAACN,SAAS;QAACgB,KAAK,EAAC,QAAQ;QAAAP,QAAA,eACrBH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,SAAS,GAAG;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEpB,CAAC;AAACE,EAAA,GA1CIV,qBAAqB;AA4C3B,eAAeA,qBAAqB;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}