{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"disabled\", \"id\", \"loading\", \"loadingIndicator\", \"loadingPosition\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { chainPropTypes } from '@mui/utils';\nimport { capitalize, unstable_useId as useId } from '@mui/material/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport Button from '@mui/material/Button';\nimport CircularProgress from '@mui/material/CircularProgress';\nimport loadingButtonClasses, { getLoadingButtonUtilityClass } from './loadingButtonClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    loading,\n    loadingPosition,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', loading && 'loading'],\n    startIcon: [loading && \"startIconLoading\".concat(capitalize(loadingPosition))],\n    endIcon: [loading && \"endIconLoading\".concat(capitalize(loadingPosition))],\n    loadingIndicator: ['loadingIndicator', loading && \"loadingIndicator\".concat(capitalize(loadingPosition))]\n  };\n  const composedClasses = composeClasses(slots, getLoadingButtonUtilityClass, classes);\n  return _extends({}, classes, composedClasses);\n}; // TODO use `import { rootShouldForwardProp } from '../styles/styled';` once move to core\n\nconst rootShouldForwardProp = prop => prop !== 'ownerState' && prop !== 'theme' && prop !== 'sx' && prop !== 'as' && prop !== 'classes';\nconst LoadingButtonRoot = styled(Button, {\n  shouldForwardProp: prop => rootShouldForwardProp(prop) || prop === 'classes',\n  name: 'MuiLoadingButton',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    return [styles.root, styles.startIconLoadingStart && {\n      [\"& .\".concat(loadingButtonClasses.startIconLoadingStart)]: styles.startIconLoadingStart\n    }, styles.endIconLoadingEnd && {\n      [\"& .\".concat(loadingButtonClasses.endIconLoadingEnd)]: styles.endIconLoadingEnd\n    }];\n  }\n})(_ref => {\n  let {\n    ownerState,\n    theme\n  } = _ref;\n  return _extends({\n    [\"& .\".concat(loadingButtonClasses.startIconLoadingStart, \", & .\").concat(loadingButtonClasses.endIconLoadingEnd)]: {\n      transition: theme.transitions.create(['opacity'], {\n        duration: theme.transitions.duration.short\n      }),\n      opacity: 0\n    }\n  }, ownerState.loadingPosition === 'center' && {\n    transition: theme.transitions.create(['background-color', 'box-shadow', 'border-color'], {\n      duration: theme.transitions.duration.short\n    }),\n    [\"&.\".concat(loadingButtonClasses.loading)]: {\n      color: 'transparent'\n    }\n  }, ownerState.loadingPosition === 'start' && ownerState.fullWidth && {\n    [\"& .\".concat(loadingButtonClasses.startIconLoadingStart, \", & .\").concat(loadingButtonClasses.endIconLoadingEnd)]: {\n      transition: theme.transitions.create(['opacity'], {\n        duration: theme.transitions.duration.short\n      }),\n      opacity: 0,\n      marginRight: -8\n    }\n  }, ownerState.loadingPosition === 'end' && ownerState.fullWidth && {\n    [\"& .\".concat(loadingButtonClasses.startIconLoadingStart, \", & .\").concat(loadingButtonClasses.endIconLoadingEnd)]: {\n      transition: theme.transitions.create(['opacity'], {\n        duration: theme.transitions.duration.short\n      }),\n      opacity: 0,\n      marginLeft: -8\n    }\n  });\n});\nconst LoadingButtonLoadingIndicator = styled('div', {\n  name: 'MuiLoadingButton',\n  slot: 'LoadingIndicator',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.loadingIndicator, styles[\"loadingIndicator\".concat(capitalize(ownerState.loadingPosition))]];\n  }\n})(_ref2 => {\n  let {\n    theme,\n    ownerState\n  } = _ref2;\n  return _extends({\n    position: 'absolute',\n    visibility: 'visible',\n    display: 'flex'\n  }, ownerState.loadingPosition === 'start' && (ownerState.variant === 'outlined' || ownerState.variant === 'contained') && {\n    left: ownerState.size === 'small' ? 10 : 14\n  }, ownerState.loadingPosition === 'start' && ownerState.variant === 'text' && {\n    left: 6\n  }, ownerState.loadingPosition === 'center' && {\n    left: '50%',\n    transform: 'translate(-50%)',\n    color: (theme.vars || theme).palette.action.disabled\n  }, ownerState.loadingPosition === 'end' && (ownerState.variant === 'outlined' || ownerState.variant === 'contained') && {\n    right: ownerState.size === 'small' ? 10 : 14\n  }, ownerState.loadingPosition === 'end' && ownerState.variant === 'text' && {\n    right: 6\n  }, ownerState.loadingPosition === 'start' && ownerState.fullWidth && {\n    position: 'relative',\n    left: -10\n  }, ownerState.loadingPosition === 'end' && ownerState.fullWidth && {\n    position: 'relative',\n    right: -10\n  });\n});\nconst LoadingButton = /*#__PURE__*/React.forwardRef(function LoadingButton(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiLoadingButton'\n  });\n  const {\n      children,\n      disabled = false,\n      id: idProp,\n      loading = false,\n      loadingIndicator: loadingIndicatorProp,\n      loadingPosition = 'center',\n      variant = 'text'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const id = useId(idProp);\n  const loadingIndicator = loadingIndicatorProp != null ? loadingIndicatorProp : /*#__PURE__*/_jsx(CircularProgress, {\n    \"aria-labelledby\": id,\n    color: \"inherit\",\n    size: 16\n  });\n  const ownerState = _extends({}, props, {\n    disabled,\n    loading,\n    loadingIndicator,\n    loadingPosition,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const loadingButtonLoadingIndicator = loading ? /*#__PURE__*/_jsx(LoadingButtonLoadingIndicator, {\n    className: classes.loadingIndicator,\n    ownerState: ownerState,\n    children: loadingIndicator\n  }) : null;\n  return /*#__PURE__*/_jsxs(LoadingButtonRoot, _extends({\n    disabled: disabled || loading,\n    id: id,\n    ref: ref\n  }, other, {\n    variant: variant,\n    classes: classes,\n    ownerState: ownerState,\n    children: [ownerState.loadingPosition === 'end' ? children : loadingButtonLoadingIndicator, ownerState.loadingPosition === 'end' ? loadingButtonLoadingIndicator : children]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? LoadingButton.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  id: PropTypes.string,\n  /**\n   * If `true`, the loading indicator is shown.\n   * @default false\n   */\n  loading: PropTypes.bool,\n  /**\n   * Element placed before the children if the button is in loading state.\n   * The node should contain an element with `role=\"progressbar\"` with an accessible name.\n   * By default we render a `CircularProgress` that is labelled by the button itself.\n   * @default <CircularProgress color=\"inherit\" size={16} />\n   */\n  loadingIndicator: PropTypes.node,\n  /**\n   * The loading indicator can be positioned on the start, end, or the center of the button.\n   * @default 'center'\n   */\n  loadingPosition: chainPropTypes(PropTypes.oneOf(['start', 'end', 'center']), props => {\n    if (props.loadingPosition === 'start' && !props.startIcon) {\n      return new Error(\"MUI: The loadingPosition=\\\"start\\\" should be used in combination with startIcon.\");\n    }\n    if (props.loadingPosition === 'end' && !props.endIcon) {\n      return new Error(\"MUI: The loadingPosition=\\\"end\\\" should be used in combination with endIcon.\");\n    }\n    return null;\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'text'\n   */\n  variant: PropTypes\n  /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['contained', 'outlined', 'text']), PropTypes.string])\n} : void 0;\nexport default LoadingButton;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}