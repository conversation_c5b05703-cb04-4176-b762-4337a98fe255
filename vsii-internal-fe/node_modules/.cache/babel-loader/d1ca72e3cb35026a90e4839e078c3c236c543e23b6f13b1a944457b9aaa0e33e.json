{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/extended/Form/PercentageFormat.tsx\";\nimport React from 'react';\nimport { NumericFormat } from 'react-number-format';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PercentageFormat = /*#__PURE__*/React.forwardRef(_c = function NumberFormatCustom(props, ref) {\n  const {\n    onChange,\n    ...other\n  } = props;\n  return /*#__PURE__*/_jsxDEV(NumericFormat, {\n    ...other,\n    getInputRef: ref,\n    onValueChange: values => {\n      onChange({\n        target: {\n          name: props.name,\n          value: values.value\n        }\n      });\n    },\n    thousandSeparator: true,\n    valueIsNumericString: true,\n    suffix: \"%\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 9\n  }, this);\n});\n_c2 = PercentageFormat;\nexport default PercentageFormat;\nvar _c, _c2;\n$RefreshReg$(_c, \"PercentageFormat$React.forwardRef\");\n$RefreshReg$(_c2, \"PercentageFormat\");", "map": {"version": 3, "names": ["React", "NumericFormat", "jsxDEV", "_jsxDEV", "PercentageFormat", "forwardRef", "_c", "NumberFormatCustom", "props", "ref", "onChange", "other", "getInputRef", "onValueChange", "values", "target", "name", "value", "thousandSeparator", "valueIsNumericString", "suffix", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/extended/Form/PercentageFormat.tsx"], "sourcesContent": ["import React from 'react';\nimport { NumericFormat, NumericFormatProps } from 'react-number-format';\n\ninterface CustomProps {\n    onChange: (event: { target: { name: string; value: string } }) => void;\n    name: string;\n}\n\nconst PercentageFormat = React.forwardRef<NumericFormatProps, CustomProps>(function NumberFormatCustom(props, ref) {\n    const { onChange, ...other } = props;\n\n    return (\n        <NumericFormat\n            {...other}\n            getInputRef={ref}\n            onValueChange={(values) => {\n                onChange({\n                    target: {\n                        name: props.name,\n                        value: values.value\n                    }\n                });\n            }}\n            thousandSeparator\n            valueIsNumericString\n            suffix=\"%\"\n        />\n    );\n});\n\nexport default PercentageFormat;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAA4B,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOxE,MAAMC,gBAAgB,gBAAGJ,KAAK,CAACK,UAAU,CAAAC,EAAA,GAAkC,SAASC,kBAAkBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/G,MAAM;IAAEC,QAAQ;IAAE,GAAGC;EAAM,CAAC,GAAGH,KAAK;EAEpC,oBACIL,OAAA,CAACF,aAAa;IAAA,GACNU,KAAK;IACTC,WAAW,EAAEH,GAAI;IACjBI,aAAa,EAAGC,MAAM,IAAK;MACvBJ,QAAQ,CAAC;QACLK,MAAM,EAAE;UACJC,IAAI,EAAER,KAAK,CAACQ,IAAI;UAChBC,KAAK,EAAEH,MAAM,CAACG;QAClB;MACJ,CAAC,CAAC;IACN,CAAE;IACFC,iBAAiB;IACjBC,oBAAoB;IACpBC,MAAM,EAAC;EAAG;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEV,CAAC,CAAC;AAACC,GAAA,GApBGrB,gBAAgB;AAsBtB,eAAeA,gBAAgB;AAAC,IAAAE,EAAA,EAAAmB,GAAA;AAAAC,YAAA,CAAApB,EAAA;AAAAoB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}