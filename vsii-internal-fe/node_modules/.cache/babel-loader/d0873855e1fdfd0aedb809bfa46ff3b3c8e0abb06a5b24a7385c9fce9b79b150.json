{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport SpeedDial from '@mui/material/SpeedDial';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet warnedOnce = false;\n/**\n * @ignore - do not document.\n */\n\nexport default /*#__PURE__*/React.forwardRef(function DeprecatedSpeedDial(props, ref) {\n  if (!warnedOnce) {\n    console.warn(['MUI: The SpeedDial component was moved from the lab to the core.', '', \"You should use `import { SpeedDial } from '@mui/material'`\", \"or `import SpeedDial from '@mui/material/SpeedDial'`\"].join('\\n'));\n    warnedOnce = true;\n  }\n  return /*#__PURE__*/_jsx(SpeedDial, _extends({\n    ref: ref\n  }, props));\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}