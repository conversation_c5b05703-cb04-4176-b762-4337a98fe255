{"ast": null, "code": "const METHOD = {\n  GET: 'GET',\n  POST: 'POST',\n  PUT: 'PUT',\n  DELETE: 'DELETE',\n  PATCH: 'PATCH'\n};\nconst Api = {\n  auth: {\n    login: (isLdap = false) => ({\n      method: METHOD.POST,\n      url: `login?ldap=${isLdap}`,\n      dontNeedToken: true,\n      isAuth: true\n    }),\n    refreshToken: (isLdap = 'false') => ({\n      method: METHOD.POST,\n      url: `refreshToken?ldap=${isLdap}`,\n      dontNeedToken: true,\n      isAuth: true\n    }),\n    me: {\n      method: METHOD.GET,\n      url: 'me',\n      dontNeedToken: false\n    },\n    changePassword: userId => ({\n      method: METHOD.PUT,\n      url: `changePassword/${userId}`,\n      dontNeedToken: false,\n      isAuth: true\n    }),\n    register: {\n      method: METHOD.POST,\n      url: 'register',\n      dontNeedToken: true,\n      isAuth: true\n    },\n    forgot: email => ({\n      method: METHOD.POST,\n      url: `forgotPassword?email=${email}`,\n      dontNeedToken: true,\n      isAuth: true\n    }),\n    createPassword: {\n      method: METHOD.POST,\n      url: 'createPassword',\n      dontNeedToken: true,\n      isAuth: true\n    },\n    verifyToken: {\n      method: METHOD.POST,\n      url: '/verifyToken',\n      dontNeedToken: true,\n      isAuth: true\n    },\n    getAllpackageAccount: {\n      method: METHOD.GET,\n      url: 'package-account/findAll',\n      dontNeedToken: true,\n      isAuth: true\n    }\n  },\n  master: {\n    getAllTitle: {\n      method: METHOD.GET,\n      url: 'master/titleAll',\n      dontNeedToken: false\n    },\n    getAllRank: {\n      method: METHOD.GET,\n      url: 'master/rankAll',\n      dontNeedToken: false\n    },\n    getAllGroup: {\n      method: METHOD.GET,\n      url: 'master/groupAll',\n      dontNeedToken: false\n    },\n    getProjectType: fixCost => ({\n      method: METHOD.GET,\n      url: 'master/projectTypeAll' + (fixCost === undefined ? '' : `?fixCost=${fixCost}`),\n      dontNeedToken: false\n    }),\n    getProjectTypeNonBillableConfig: nonBillable => ({\n      method: METHOD.GET,\n      url: 'project/type/findByBillAbleType' + (nonBillable === undefined ? '' : `?billable=nonbillable`),\n      dontNeedToken: false\n    }),\n    getProjectAll: fixCost => ({\n      method: METHOD.GET,\n      url: `master/projectAll?fixCost=${fixCost || false}`,\n      dontNeedToken: false\n    }),\n    getProjectAllWithoutFixcost: {\n      method: METHOD.GET,\n      url: 'master/projectAll',\n      dontNeedToken: false\n    },\n    findAllUserLoginTime: {\n      method: METHOD.GET,\n      url: 'master/findUserLogtime',\n      dontNeedToken: false\n    },\n    getFunctionAll: {\n      method: METHOD.GET,\n      url: 'master/functionAll',\n      dontNeedToken: false\n    },\n    getProductionPerformanceAll: {\n      method: METHOD.GET,\n      url: 'master/productionPerformanceAllVND',\n      dontNeedToken: false\n    },\n    getAllLanguage: {\n      method: METHOD.GET,\n      url: 'master/languageAll',\n      dontNeedToken: false\n    }\n  },\n  synchronize: {\n    getDownloadTemplate: {\n      method: METHOD.GET,\n      url: 'master/excel/download',\n      dontNeedToken: false\n    },\n    postImportExcel: {\n      method: METHOD.POST,\n      url: 'sync/manual/import/excel',\n      dontNeedToken: false\n    }\n  },\n  weekly_efford: {\n    getMember: {\n      method: METHOD.GET,\n      url: 'week/member',\n      dontNeedToken: false\n    },\n    getProject: {\n      method: METHOD.GET,\n      url: 'week/project',\n      dontNeedToken: false\n    },\n    getProjectDetail: {\n      method: METHOD.GET,\n      url: 'week/projectDetail',\n      dontNeedToken: false\n    },\n    postVerified: {\n      method: METHOD.POST,\n      url: 'week/projectDetailVerified',\n      dontNeedToken: false\n    },\n    getDownload: {\n      method: METHOD.GET,\n      url: 'week/download',\n      dontNeedToken: false\n    },\n    getWeeklyEffortProject: {\n      method: METHOD.GET,\n      url: 'week/getAllProject',\n      dontNeedToken: false\n    }\n  },\n  monthly_efford: {\n    postUpdateEffordPlan: {\n      method: METHOD.POST,\n      url: 'month/saveOrUpdateEffortPlan',\n      dontNeedToken: false\n    },\n    getSummary: {\n      method: METHOD.GET,\n      url: 'month/summary',\n      dontNeedToken: false\n    },\n    getProject: {\n      method: METHOD.GET,\n      url: 'month/project',\n      dontNeedToken: false\n    },\n    getMember: {\n      method: METHOD.GET,\n      url: 'month/member',\n      dontNeedToken: false\n    },\n    getMemberProject: {\n      method: METHOD.GET,\n      url: 'month/memberProject',\n      dontNeedToken: false\n    },\n    getDownload: {\n      method: METHOD.GET,\n      url: 'month/download',\n      dontNeedToken: false\n    },\n    // mr/\n    getProjectReports: {\n      method: METHOD.GET,\n      url: 'project/findAllReportProjects',\n      dontNeedToken: false\n    },\n    addProjectReports: {\n      method: METHOD.POST,\n      url: 'project/create',\n      dontNeedToken: false\n    },\n    editProjectReports: id => ({\n      method: METHOD.PUT,\n      url: `project/${id}`,\n      dontNeedToken: false\n    }),\n    getReportById: id => ({\n      method: METHOD.GET,\n      url: `project/reportProject/${id}`,\n      dontNeedToken: false\n    }),\n    geProjectDetailById: {\n      method: METHOD.GET,\n      url: `project/getProjectWithTotalHC`,\n      dontNeedToken: false\n    },\n    // project reports\n    getDownloadReport: {\n      method: METHOD.GET,\n      url: 'project/downloadReport',\n      dontNeedToken: false\n    },\n    deleteReport: id => ({\n      method: METHOD.DELETE,\n      url: `project/${id}`,\n      dontNeedToken: false\n    }),\n    getOMReport: {\n      method: METHOD.GET,\n      url: 'orm/getAll',\n      dontNeedToken: false\n    },\n    uploadOrmReport: {\n      method: METHOD.POST,\n      url: 'orm/uploadDocument',\n      dontNeedToken: false\n    },\n    deleteOrmReport: id => ({\n      method: METHOD.DELETE,\n      url: `orm/${id}`,\n      dontNeedToken: false\n    }),\n    downloadOrmReport: id => ({\n      method: METHOD.GET,\n      url: `orm/downloadDocument/${id}`,\n      dontNeedToken: false\n    })\n  },\n  list_project_team: {\n    getListProjectTeam: {\n      method: METHOD.GET,\n      url: 'project/team',\n      dontNeedToken: false\n    },\n    getRatioJoiningProject: {\n      method: METHOD.GET,\n      url: 'project/ratio/join',\n      dontNeedToken: false\n    },\n    getDownload: {\n      method: METHOD.GET,\n      url: 'project/download',\n      dontNeedToken: false\n    }\n  },\n  non_billable_monitoring: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'nonbill/nonBillAble',\n      dontNeedToken: false\n    },\n    getDownload: {\n      method: METHOD.GET,\n      url: 'nonbill/download',\n      dontNeedToken: false\n    },\n    getCostByWeek: {\n      method: METHOD.GET,\n      url: 'nonbill/costByWeek',\n      dontNeedToken: false\n    },\n    getWarningNonbillable: {\n      method: METHOD.GET,\n      url: 'nonbill/warningNonbillable',\n      dontNeedToken: false\n    },\n    findAllConfig: {\n      method: METHOD.GET,\n      url: 'nonBillableConfig/findAllConfig',\n      dontNeedToken: false\n    }\n  },\n  monthly_project_cost: {\n    getSummary: fixCost => ({\n      method: METHOD.GET,\n      url: `monthlyProjectCost/summary?fixCost=${fixCost || false}`,\n      dontNeedToken: false\n    }),\n    getDownload: {\n      method: METHOD.GET,\n      url: 'monthlyProjectCost/download',\n      dontNeedToken: false\n    },\n    getDetailReportByMonth: {\n      method: METHOD.GET,\n      url: 'monthlyProjectCost/detailMonth',\n      dontNeedToken: false\n    },\n    getMonthlyCost: {\n      method: METHOD.GET,\n      url: 'monthlyProjectCost/monthlyCost',\n      dontNeedToken: false\n    },\n    getFindProjectCost: {\n      method: METHOD.GET,\n      url: 'monthlyProjectCost/findProjectCost',\n      dontNeedToken: false\n    },\n    getDownloadTemplateMonthlyCost: {\n      method: METHOD.GET,\n      url: 'monthlyProjectCost/downloadTemplate',\n      dontNeedToken: false\n    },\n    postImportTemplateMonthlyCost: {\n      method: METHOD.POST,\n      url: 'monthlyProjectCost/importTemplate',\n      upload: true,\n      dontNeedToken: false\n    },\n    postSaveOrUpdateActualCost: {\n      method: METHOD.POST,\n      url: 'monthlyProjectCost/saveOrUpdateActualCost',\n      dontNeedToken: false\n    },\n    deleteActualCost: {\n      method: METHOD.DELETE,\n      url: 'monthlyProjectCost/delete',\n      dontNeedToken: false\n    }\n  },\n  cost_monitoring: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'month/costMonitoring/index',\n      dontNeedToken: false\n    },\n    getDownload: {\n      method: METHOD.GET,\n      url: 'month/costMonitoring/download',\n      dontNeedToken: false\n    },\n    getEffortByWeek: {\n      method: METHOD.GET,\n      url: 'month/costMonitoring/effortByWeek',\n      dontNeedToken: false\n    }\n  },\n  member: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'user/findAllUser',\n      dontNeedToken: false\n    },\n    postSaveOrUpdate: {\n      method: METHOD.POST,\n      url: 'user/saveOrUpdateUser',\n      dontNeedToken: false\n    },\n    resetPassowrd: userId => ({\n      method: METHOD.PUT,\n      url: `user/resetPassword/${userId}`,\n      dontNeedToken: false\n    })\n  },\n  department: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'getAllDepartment',\n      dontNeedToken: false\n    },\n    search: {\n      method: METHOD.GET,\n      url: 'search',\n      dontNeedToken: false\n    },\n    create: {\n      method: METHOD.POST,\n      url: 'create',\n      dontNeedToken: false\n    },\n    edit: departmentId => ({\n      method: METHOD.PUT,\n      url: `${departmentId}`,\n      dontNeedToken: false\n    }),\n    delete: departmentId => ({\n      method: METHOD.DELETE,\n      url: `${departmentId}`,\n      dontNeedToken: false\n    })\n  },\n  project: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'project/getAllProject',\n      dontNeedToken: false\n    },\n    getDetail: {\n      method: METHOD.GET,\n      url: 'project/getInfoProjectDetail',\n      dontNeedToken: false\n    },\n    getQuotaUpdateHistory: {\n      method: METHOD.GET,\n      url: 'project/getInfoProjectQuotaHistory',\n      dontNeedToken: false\n    },\n    saveOrUpdate: {\n      method: METHOD.POST,\n      url: 'project/saveOrUpdate',\n      dontNeedToken: false\n    },\n    saveOrUpdateProjectUser: {\n      method: METHOD.POST,\n      url: 'project/saveOrUpdateProjectUser',\n      dontNeedToken: false\n    },\n    deleteProjectUser: {\n      method: METHOD.DELETE,\n      url: 'project/deleteProjectUser',\n      dontNeedToken: false\n    },\n    getDownload: {\n      method: METHOD.GET,\n      url: 'project/export',\n      dontNeedToken: false\n    },\n    typeConfig: {\n      search: {\n        method: METHOD.GET,\n        url: 'project/type/search',\n        dontNeedToken: false\n      },\n      create: {\n        method: METHOD.POST,\n        url: 'project/type/create',\n        dontNeedToken: false\n      },\n      edit: projectTypeId => ({\n        method: METHOD.PUT,\n        url: `project/type/${projectTypeId}`,\n        dontNeedToken: false\n      }),\n      delete: projectTypeId => ({\n        method: METHOD.DELETE,\n        url: `project/type/${projectTypeId}`,\n        dontNeedToken: false\n      })\n    }\n  },\n  nonBillableConfig: {\n    search: {\n      method: METHOD.GET,\n      url: 'nonBillableConfig/findAll',\n      dontNeedToken: false\n    },\n    edit: configId => ({\n      method: METHOD.PUT,\n      url: `nonBillableConfig/update/${configId}`,\n      dontNeedToken: false\n    })\n  },\n  title: {\n    search: {\n      method: METHOD.GET,\n      url: 'title/search',\n      dontNeedToken: false\n    },\n    create: {\n      method: METHOD.POST,\n      url: 'title/create',\n      dontNeedToken: false\n    },\n    edit: titleId => ({\n      method: METHOD.PUT,\n      url: `title/${titleId}`,\n      dontNeedToken: false\n    }),\n    delete: titleId => ({\n      method: METHOD.DELETE,\n      url: `title/${titleId}`,\n      dontNeedToken: false\n    })\n  },\n  special_hours: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'special/getAllSpecialHour',\n      dontNeedToken: false\n    },\n    postSaveOrUpdateSpecialHours: {\n      method: METHOD.POST,\n      url: 'special/saveOrUpdate',\n      dontNeedToken: false\n    }\n  },\n  system_config: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'master/systemConfigAll',\n      dontNeedToken: false\n    },\n    postUpdateConfig: {\n      method: METHOD.POST,\n      url: 'systemConfig/saveOrUpdate',\n      dontNeedToken: false\n    }\n  },\n  email_config: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'master/emailConfigAll',\n      dontNeedToken: false\n    },\n    postSaveOrUpdateEmailConfig: {\n      method: METHOD.POST,\n      url: 'email/saveOrUpdate',\n      dontNeedToken: false\n    }\n  },\n  cv_config_technology: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'tech/getAll',\n      dontNeedToken: false\n    },\n    postSaveOrUpdateCVConfig: {\n      method: METHOD.POST,\n      url: 'tech/createOrUpdate',\n      dontNeedToken: false\n    },\n    deleteTechnology: {\n      method: METHOD.DELETE,\n      url: 'tech/delete',\n      dontNeedToken: false\n    }\n  },\n  cv_config_language: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'language/getAll',\n      dontNeedToken: false\n    },\n    postSaveOrUpdateCVConfig: {\n      method: METHOD.POST,\n      url: 'language/saveOrUpdate',\n      dontNeedToken: false\n    },\n    deleteLanguage: {\n      method: METHOD.DELETE,\n      url: 'language/delete',\n      dontNeedToken: false\n    }\n  },\n  cv_config_reference: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'references/getAll',\n      dontNeedToken: false\n    },\n    postSaveOrUpdateCVConfig: {\n      method: METHOD.POST,\n      url: 'references/saveOrUpdate',\n      dontNeedToken: false\n    },\n    deleteReference: {\n      method: METHOD.DELETE,\n      url: 'references/delete',\n      dontNeedToken: false\n    }\n  },\n  exchange_rate_config: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'exchange-rate',\n      dontNeedToken: false\n    },\n    postSaveOrUpdateExchangeRateConfig: {\n      method: METHOD.POST,\n      url: 'exchange-rate/saveOrUpdate',\n      dontNeedToken: false\n    },\n    deleteExchangeRate: {\n      method: METHOD.DELETE,\n      url: 'exchange-rate/delete',\n      dontNeedToken: false\n    }\n  },\n  holiday: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'holiday/getAll',\n      dontNeedToken: false\n    },\n    postSaveOrUpdateHoliday: {\n      method: METHOD.POST,\n      url: 'holiday/saveOrUpdate',\n      dontNeedToken: false\n    },\n    delete: {\n      method: METHOD.DELETE,\n      url: 'holiday/delete',\n      dontNeedToken: false\n    }\n  },\n  rank: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'rank/rankAll',\n      dontNeedToken: false\n    },\n    postUpdateRank: {\n      method: METHOD.POST,\n      url: 'rank/createOrUpdateRank',\n      dontNeedToken: false\n    }\n  },\n  group: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'group/getGroupAll',\n      dontNeedToken: false\n    },\n    postSaveOrUpdate: {\n      method: METHOD.POST,\n      url: 'group/saveOrUpdate',\n      dontNeedToken: false\n    }\n  },\n  sale_productivity: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'sales/productivityProject',\n      dontNeedToken: false\n    },\n    getDetail: {\n      method: METHOD.GET,\n      url: 'sales/productivityProject/getDetail',\n      dontNeedToken: false\n    },\n    postCreateOrUpdate: {\n      method: METHOD.POST,\n      url: 'sales/productivityProject/createOrUpdate',\n      dontNeedToken: false\n    },\n    getDownload: {\n      method: METHOD.GET,\n      url: 'sales/productivityProject/download',\n      dontNeedToken: false\n    },\n    postUpdateHeadCount: {\n      method: METHOD.POST,\n      url: 'sales/productivityProject/updateHeadcount',\n      dontNeedToken: false\n    },\n    getDetailHeadCountByMonth: {\n      method: METHOD.GET,\n      url: 'sales/productivityProject/findHeadcount',\n      dontNeedToken: false\n    },\n    postComment: {\n      method: METHOD.POST,\n      url: 'sales/productivityProject/comment',\n      dontNeedToken: false\n    },\n    getStandardWorkingDay: {\n      method: METHOD.GET,\n      url: 'sales/productivityProject/standardWorkingDay',\n      dontNeedToken: false\n    },\n    getExchangeRate: {\n      method: METHOD.GET,\n      url: 'sales/productivityProject/exchangeRate',\n      dontNeedToken: false\n    }\n  },\n  comment: {\n    getFindComment: {\n      method: METHOD.GET,\n      url: 'comment/findComment',\n      dontNeedToken: false\n    },\n    postSaveOrUpdateComment: {\n      method: METHOD.POST,\n      url: 'comment/saveOrUpdate',\n      dontNeedToken: false\n    },\n    getFindCommentDetail: {\n      method: METHOD.GET,\n      url: 'commentDetail/findComment',\n      dontNeedToken: false\n    },\n    postSaveOrUpdateCommentDetail: {\n      method: METHOD.POST,\n      url: 'commentDetail/saveOrUpdate',\n      dontNeedToken: false\n    }\n  },\n  sale_list: {\n    getRequestAll: {\n      method: METHOD.GET,\n      url: 'sales/list/requests/getAll',\n      dontNeedToken: false\n    },\n    getSupplierAll: {\n      method: METHOD.GET,\n      url: 'sales/list/supplier/getAll',\n      dontNeedToken: false\n    },\n    postRequestsCreateOrUpdate: {\n      method: METHOD.POST,\n      url: 'sales/list/requests/createOrUpdate',\n      dontNeedToken: false\n    },\n    deleteRequests: id => ({\n      method: METHOD.DELETE,\n      url: `sales/list/requests/delete?id=${id}`,\n      dontNeedToken: false\n    }),\n    getDownload: {\n      method: METHOD.GET,\n      url: 'sales/list/download',\n      dontNeedToken: false\n    },\n    postSupplierCreateOrUpdate: {\n      method: METHOD.POST,\n      url: 'sales/list/supplier/createOrUpdate',\n      dontNeedToken: false\n    },\n    deleteSupplier: id => ({\n      method: METHOD.DELETE,\n      url: `sales/list/supplier/delete?id=${id}`,\n      dontNeedToken: false\n    }),\n    getSaleSummary: {\n      method: METHOD.GET,\n      url: 'sales/summary',\n      dontNeedToken: false\n    },\n    getDownloadSalePineLine: {\n      method: METHOD.GET,\n      url: 'sales/download',\n      dontNeedToken: false\n    },\n    getTotalSummary: {\n      method: METHOD.GET,\n      url: 'sales/summary-total',\n      dontNeedToken: false\n    }\n  },\n  working_calendar: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'calendar/filterWorkingCalender',\n      dontNeedToken: false\n    },\n    getType: {\n      method: METHOD.GET,\n      url: 'calendar/getTypeWorkingCalendar',\n      dontNeedToken: false\n    },\n    postSaveOrUpdate: {\n      method: METHOD.POST,\n      url: 'calendar/saveOrUpdate',\n      dontNeedToken: false\n    },\n    getDownload: {\n      method: METHOD.GET,\n      url: 'calendar/download',\n      dontNeedToken: false\n    },\n    postUpdateStatus: {\n      method: METHOD.POST,\n      url: 'calendar/updateStatus',\n      dontNeedToken: false\n    },\n    getClosingDate: {\n      method: METHOD.GET,\n      url: 'calendar/closing-dates',\n      dontNeedToken: false\n    },\n    postUpdateClosingDate: {\n      method: METHOD.POST,\n      url: 'calendar/closing-date/update',\n      dontNeedToken: false\n    },\n    postVerifyClosingDate: {\n      method: METHOD.POST,\n      url: 'calendar/closing-date/verify',\n      dontNeedToken: false\n    },\n    findOnsite: {\n      method: METHOD.GET,\n      url: 'calendar/onsite',\n      dontNeedToken: false\n    },\n    updateComment: {\n      method: METHOD.PUT,\n      url: 'calendar/updateComment',\n      dontNeedToken: false\n    }\n  },\n  manage_leaves: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'leaves',\n      dontNeedToken: false\n    },\n    getLeaveDetail: idHexString => ({\n      method: METHOD.GET,\n      url: `leaves/detail/${idHexString}`,\n      dontNeedToken: false\n    }),\n    getDetailUser: {\n      method: METHOD.GET,\n      url: 'leaves/detail-user',\n      dontNeedToken: false\n    },\n    getDetailUserId: idHexString => {\n      return {\n        method: METHOD.GET,\n        url: `leaves/detail/${idHexString}`,\n        dontNeedToken: false\n      };\n    },\n    putApproved: idHexString => {\n      return {\n        method: METHOD.PUT,\n        url: `leaves/approved/${idHexString}`,\n        dontNeedToken: false\n      };\n    },\n    putReject: {\n      method: METHOD.PUT,\n      url: `leaves/reject`,\n      dontNeedToken: false\n    },\n    postCreate: {\n      method: METHOD.POST,\n      url: 'leaves',\n      dontNeedToken: false\n    },\n    putUpdate: idHexString => {\n      return {\n        method: METHOD.PUT,\n        url: `leaves/${idHexString}`,\n        dontNeedToken: false\n      };\n    },\n    deleteLeave: idHexString => {\n      return {\n        method: METHOD.DELETE,\n        url: `leaves/delete/${idHexString}`,\n        dontNeedToken: false\n      };\n    },\n    downloadLeave: idHexString => {\n      return {\n        method: METHOD.GET,\n        url: `leaves/download/${idHexString}`,\n        dontNeedToken: false\n      };\n    }\n  },\n  manage_ot: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'overTimeTicket/search',\n      dontNeedToken: false\n    },\n    putReject: {\n      method: METHOD.PUT,\n      url: `overTimeTicket/reject`,\n      dontNeedToken: false\n    },\n    deleteOT: idHexString => {\n      return {\n        method: METHOD.DELETE,\n        url: `overTimeTicket/delete/${idHexString}`,\n        dontNeedToken: false\n      };\n    }\n  },\n  manage_resignation: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'resignation',\n      dontNeedToken: false\n    },\n    putApproved: idHexString => {\n      return {\n        method: METHOD.PUT,\n        url: `resignation/approved/${idHexString}`,\n        dontNeedToken: false\n      };\n    },\n    postCreate: {\n      method: METHOD.POST,\n      url: 'resignation',\n      dontNeedToken: false\n    },\n    putUpdate: idHexString => {\n      return {\n        method: METHOD.PUT,\n        url: `resignation/${idHexString}`,\n        dontNeedToken: false\n      };\n    }\n  },\n  sale_pipeline: {\n    getListYear: {\n      method: METHOD.GET,\n      url: 'sales/sale-pipeline/listYear',\n      dontNeedToken: false\n    }\n  },\n  sale_pipeline_on_going: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'sales/onGoing',\n      dontNeedToken: false\n    },\n    saveOrUpdate: {\n      method: METHOD.POST,\n      url: 'sales/onGoing/saveOrUpdate',\n      dontNeedToken: false\n    },\n    comment: {\n      method: METHOD.POST,\n      url: 'sales/onGoing/updateComment',\n      dontNeedToken: false\n    },\n    getTotal: {\n      method: METHOD.GET,\n      url: 'sales/onGoing/total-ongoing',\n      dontNeedToken: false\n    }\n  },\n  sale_pipe_line_bidding: {\n    getBidding: {\n      method: METHOD.GET,\n      url: 'sales/sale-pipeline/bidding',\n      dontNeedToken: false\n    },\n    getMonthlyBillable: {\n      method: METHOD.GET,\n      url: 'sales/sale-pipeline/monthlyBillable',\n      dontNeedToken: false\n    },\n    estimateHCInfo: {\n      method: METHOD.POST,\n      url: 'sales/sale-pipeline/estimateHCInfo',\n      dontNeedToken: false\n    },\n    postAddOrEditBidding: {\n      method: METHOD.POST,\n      url: 'sales/sale-pipeline/bidding/createOrUpdate',\n      dontNeedToken: false\n    },\n    getDetailBidding: {\n      method: METHOD.GET,\n      url: 'sales/sale-pipeline/bidding/details',\n      dontNeedToken: false\n    },\n    deleteBidding: {\n      method: METHOD.DELETE,\n      url: 'sales/sale-pipeline/bidding',\n      dontNeedToken: false\n    },\n    comment: {\n      method: METHOD.POST,\n      url: 'sales/sale-pipeline/bidding/updateComment',\n      dontNeedToken: false\n    },\n    getTotal: {\n      method: METHOD.GET,\n      url: 'sales/sale-pipeline/total-bidding',\n      dontNeedToken: false\n    },\n    getTotal: {\n      method: METHOD.GET,\n      url: 'sales/sale-pipeline/total-bidding',\n      dontNeedToken: false\n    }\n  },\n  budgeting_plan: {\n    getAll: {\n      method: METHOD.GET,\n      url: 'sales/budgeting-plan',\n      dontNeedToken: false\n    },\n    editBudgetingPlan: {\n      method: METHOD.POST,\n      url: 'sales/budgeting-plan/saveOrUpdate',\n      dontNeedToken: false\n    },\n    getTotal: {\n      method: METHOD.GET,\n      url: 'sales/budgeting-plan/total-budgeting-plan',\n      dontNeedToken: false\n    },\n    getTotal: {\n      method: METHOD.GET,\n      url: 'sales/budgeting-plan/total-budgeting-plan',\n      dontNeedToken: false\n    }\n  },\n  skills_manage: {\n    getReport: {\n      method: METHOD.GET,\n      url: 'skill/report',\n      dontNeedToken: false\n    },\n    getDownload: {\n      method: METHOD.GET,\n      url: 'skill/report/download',\n      dontNeedToken: false\n    },\n    getTitleCodes: {\n      method: METHOD.GET,\n      url: 'master/titleAll',\n      dontNeedToken: false\n    },\n    getTechAll: {\n      method: METHOD.GET,\n      url: 'master/techAll'\n    },\n    getSkillsUpdate: {\n      method: METHOD.GET,\n      url: 'skill/skillsUpdate',\n      dontNeedToken: false\n    },\n    getDetailCv: {\n      method: METHOD.GET,\n      url: 'skill/skillsUpdate/getDetail',\n      dontNeedToken: false\n    },\n    getReferences: {\n      method: METHOD.GET,\n      url: 'skill/skillsUpdate/references',\n      dontNeedToken: false\n    },\n    getTechnologies: {\n      method: METHOD.GET,\n      url: 'skill/skillsUpdate/technologyAll',\n      dontNeedToken: false\n    },\n    getSkillByTechnology: {\n      method: METHOD.GET,\n      url: 'skill/skillsUpdate/skillByTech',\n      dontNeedToken: false\n    },\n    createSkillsUpdate: {\n      method: METHOD.POST,\n      url: 'skill/skillsUpdate/create',\n      dontNeedToken: false\n    },\n    updateCV: {\n      method: METHOD.PATCH,\n      url: 'skill/skillsUpdate/update',\n      dontNeedToken: false\n    },\n    viewPDF: {\n      method: METHOD.GET,\n      url: 'skill/skillsUpdate/viewPdf',\n      dontNeedToken: false\n    },\n    downloadCV: {\n      method: METHOD.GET,\n      url: 'skill/skillsUpdate/download',\n      dontNeedToken: false\n    }\n  },\n  product: {\n    getProductReportOptions: {\n      method: METHOD.GET,\n      url: 'project/projectReport/getAllProject',\n      dontNeedToken: false\n    },\n    getReport: {\n      method: METHOD.GET,\n      url: 'project/projectReport',\n      dontNeedToken: false\n    },\n    getReportByProjectId: projectId => ({\n      method: METHOD.GET,\n      url: `project/projectReport/${projectId}`,\n      dontNeedToken: false\n    })\n  },\n  leave_day: {\n    getAll: {\n      method: METHOD.GET,\n      url: '/manageLeaveDays',\n      dontNeedToken: false\n    },\n    getLeaveDaysInfo: idHexString => ({\n      method: METHOD.GET,\n      url: `/manageLeaveDays/${idHexString}`,\n      dontNeedToken: false\n    }),\n    postSaveOrUpdate: {\n      method: METHOD.PUT,\n      url: '/manageLeaveDays',\n      dontNeedToken: false\n    }\n  },\n  monitor_bidding: {\n    getBiddingTracking: {\n      method: METHOD.GET,\n      url: '/biddingTracking/getBiddingTracking',\n      dontNeedToken: false\n    },\n    getDownload: {\n      method: METHOD.GET,\n      url: 'monitorBidding/download',\n      dontNeedToken: false,\n      responseType: 'blob'\n    },\n    getBiddingReport: {\n      method: METHOD.GET,\n      url: '/monitorBidding/getBiddingReport',\n      dontNeedToken: false\n    },\n    createOrUpdateBiddingReportpost: {\n      method: METHOD.POST,\n      url: '/monitorBidding/createBiddingReport',\n      dontNeedToken: false\n    },\n    updateBiddingReportpost: id => ({\n      method: METHOD.PUT,\n      url: `/monitorBidding/updateBiddingReport/${id}`,\n      dontNeedToken: false\n    }),\n    postSynchronize: token => ({\n      method: METHOD.POST,\n      url: `/biddingTracking/postBiddingTracking?token=${token}`,\n      dontNeedToken: false\n    })\n  },\n  flexible_report: {\n    get: {\n      method: METHOD.GET,\n      url: '/flexible-report',\n      dontNeedToken: false\n    },\n    getAllReports: {\n      method: METHOD.GET,\n      url: '/flexible-report/config',\n      dontNeedToken: false\n    },\n    getConditionTypes: {\n      method: METHOD.GET,\n      url: '/flexible-columns/findAllByReport',\n      dontNeedToken: false\n    },\n    createConfig: {\n      method: METHOD.POST,\n      url: '/flexible-report/config/create',\n      dontNeedToken: false\n    },\n    editConfig: id => ({\n      method: METHOD.PUT,\n      url: `/flexible-report/config/update/${id}`,\n      dontNeedToken: false\n    }),\n    deleteConfig: id => ({\n      method: METHOD.DELETE,\n      url: `/flexible-report/config/${id}`,\n      dontNeedToken: false\n    }),\n    getConditionOptions: {\n      method: METHOD.GET,\n      url: '/type/all',\n      dontNeedToken: false\n    },\n    editArrangement: {\n      method: METHOD.PUT,\n      url: 'flexible-report/update-index',\n      dontNeedToken: false\n    }\n  },\n  flexible_columns: {\n    get: {\n      method: METHOD.GET,\n      url: '/flexible-columns/columns',\n      dontNeedToken: false\n    },\n    edit: {\n      method: METHOD.PUT,\n      url: '/flexible-columns',\n      dontNeedToken: false\n    }\n  },\n  flexible_textConfig: {\n    get: {\n      method: METHOD.GET,\n      url: '/flexible-text-config/get-all',\n      dontNeedToken: false\n    },\n    edit: {\n      method: METHOD.PUT,\n      url: '/flexible-text-config/update',\n      dontNeedToken: false\n    },\n    configByLanguage: {\n      method: METHOD.GET,\n      url: '/flexible-text-config/config-by-language',\n      dontNeedToken: true\n    },\n    getLanguage: {\n      method: METHOD.GET,\n      url: '/flexible-language',\n      dontNeedToken: false\n    },\n    addLanguage: {\n      method: METHOD.POST,\n      url: '/flexible-language/create',\n      dontNeedToken: false\n    },\n    deleteLanguage: id => ({\n      method: METHOD.DELETE,\n      url: `/flexible-language/${id}`,\n      dontNeedToken: false\n    })\n  },\n  overtime_report: {\n    postOvertimeTicket: {\n      method: METHOD.POST,\n      url: 'overTimeTicket',\n      dontNeedToken: false\n    },\n    getDetailOvertimeTicket: idHexString => ({\n      method: METHOD.GET,\n      url: `overTimeTicket/detail/${idHexString}`,\n      dontNeedToken: false\n    }),\n    updateDetailOvertimeTicket: idHexString => ({\n      method: METHOD.PUT,\n      url: `overTimeTicket/update/${idHexString}`,\n      dontNeedToken: false\n    }),\n    approvedOvertimeTicket: idHexString => ({\n      method: METHOD.PUT,\n      url: `overTimeTicket/approved/${idHexString}`,\n      dontNeedToken: false\n    })\n  }\n};\nexport default Api;", "map": {"version": 3, "names": ["METHOD", "GET", "POST", "PUT", "DELETE", "PATCH", "Api", "auth", "login", "isLdap", "method", "url", "dontNeedToken", "isAuth", "refreshToken", "me", "changePassword", "userId", "register", "forgot", "email", "createPassword", "verifyToken", "getAllpackageAccount", "master", "getAllTitle", "getAllRank", "getAllGroup", "getProjectType", "fixCost", "undefined", "getProjectTypeNonBillableConfig", "nonBillable", "getProjectAll", "getProjectAllWithoutFixcost", "findAllUserLoginTime", "getFunctionAll", "getProductionPerformanceAll", "getAllLanguage", "synchronize", "getDownloadTemplate", "postImportExcel", "weekly_efford", "getMember", "getProject", "getProjectDetail", "postVerified", "getDownload", "getWeeklyEffortProject", "monthly_efford", "postUpdateEffordPlan", "getSummary", "getMemberProject", "getProjectReports", "addProjectReports", "editProjectReports", "id", "getReportById", "geProjectDetailById", "getDownloadReport", "deleteReport", "getOMReport", "uploadOrmReport", "deleteOrmReport", "downloadOrmReport", "list_project_team", "getListProjectTeam", "getRatioJoiningProject", "non_billable_monitoring", "getAll", "getCostByWeek", "getWarningNonbillable", "findAllConfig", "monthly_project_cost", "getDetailReportByMonth", "getMonthlyCost", "getFindProjectCost", "getDownloadTemplateMonthlyCost", "postImportTemplateMonthlyCost", "upload", "postSaveOrUpdateActualCost", "deleteActualCost", "cost_monitoring", "getEffortByWeek", "member", "postSaveOrUpdate", "resetPassowrd", "department", "search", "create", "edit", "departmentId", "delete", "project", "getDetail", "getQuotaUpdateHistory", "saveOrUpdate", "saveOrUpdateProjectUser", "deleteProjectUser", "typeConfig", "projectTypeId", "nonBillableConfig", "configId", "title", "titleId", "special_hours", "postSaveOrUpdateSpecialHours", "system_config", "postUpdateConfig", "email_config", "postSaveOrUpdateEmailConfig", "cv_config_technology", "postSaveOrUpdateCVConfig", "deleteTechnology", "cv_config_language", "deleteLanguage", "cv_config_reference", "deleteReference", "exchange_rate_config", "postSaveOrUpdateExchangeRateConfig", "deleteExchangeRate", "holiday", "postSaveOrUpdateHoliday", "rank", "postUpdateRank", "group", "sale_productivity", "postCreateOrUpdate", "postUpdateHeadCount", "getDetailHeadCountByMonth", "postComment", "getStandardWorkingDay", "getExchangeRate", "comment", "getFindComment", "postSaveOrUpdateComment", "getFindCommentDetail", "postSaveOrUpdateCommentDetail", "sale_list", "getRequestAll", "getSupplierAll", "postRequestsCreateOrUpdate", "deleteRequests", "postSupplierCreateOrUpdate", "deleteSupplier", "getSaleSummary", "getDownloadSalePineLine", "getTotalSummary", "working_calendar", "getType", "postUpdateStatus", "getClosingDate", "postUpdateClosingDate", "postVerifyClosingDate", "findOnsite", "updateComment", "manage_leaves", "getLeaveDetail", "idHexString", "getDetailUser", "getDetailUserId", "putApproved", "putReject", "postCreate", "putUpdate", "deleteLeave", "downloadLeave", "manage_ot", "deleteOT", "manage_resignation", "sale_pipeline", "getListYear", "sale_pipeline_on_going", "getTotal", "sale_pipe_line_bidding", "getBidding", "getMonthlyBillable", "estimateHCInfo", "postAddOrEditBidding", "getDetailBidding", "deleteBidding", "budgeting_plan", "editBudgetingPlan", "skills_manage", "getReport", "getTitleCodes", "getTechAll", "getSkillsUpdate", "getDetailCv", "getReferences", "getTechnologies", "getSkillByTechnology", "createSkillsUpdate", "updateCV", "viewPDF", "downloadCV", "product", "getProductReportOptions", "getReportByProjectId", "projectId", "leave_day", "getLeaveDaysInfo", "monitor_bidding", "getBiddingTracking", "responseType", "getBiddingReport", "createOrUpdateBiddingReportpost", "updateBiddingReportpost", "postSynchronize", "token", "flexible_report", "get", "getAllReports", "getConditionTypes", "createConfig", "editConfig", "deleteConfig", "getConditionOptions", "editArrangement", "flexible_columns", "flexible_textConfig", "configByLanguage", "getLanguage", "addLanguage", "overtime_report", "postOvertimeTicket", "getDetailOvertimeTicket", "updateDetailOvertimeTicket", "approvedOvertimeTicket"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/constants/Api.ts"], "sourcesContent": ["const METHOD = {\n    GET: 'GET',\n    POST: 'POST',\n    PUT: 'PUT',\n    DELETE: 'DELETE',\n    PATCH: 'PATCH'\n};\n\nconst Api = {\n    auth: {\n        login: (isLdap: boolean = false) => ({\n            method: METHOD.POST,\n            url: `login?ldap=${isLdap}`,\n            dontNeedToken: true,\n            isAuth: true\n        }),\n        refreshToken: (isLdap: string = 'false') => ({\n            method: METHOD.POST,\n            url: `refreshToken?ldap=${isLdap}`,\n            dontNeedToken: true,\n            isAuth: true\n        }),\n        me: {\n            method: METHOD.GET,\n            url: 'me',\n            dontNeedToken: false\n        },\n        changePassword: (userId: string) => ({\n            method: METHOD.PUT,\n            url: `changePassword/${userId}`,\n            dontNeedToken: false,\n            isAuth: true\n        }),\n        register: {\n            method: METHOD.POST,\n            url: 'register',\n            dontNeedToken: true,\n            isAuth: true\n        },\n        forgot: (email: string) => ({\n            method: METHOD.POST,\n            url: `forgotPassword?email=${email}`,\n            dontNeedToken: true,\n            isAuth: true\n        }),\n        createPassword: {\n            method: METHOD.POST,\n            url: 'createPassword',\n            dontNeedToken: true,\n            isAuth: true\n        },\n        verifyToken: {\n            method: METHOD.POST,\n            url: '/verifyToken',\n            dontNeedToken: true,\n            isAuth: true\n        },\n        getAllpackageAccount: {\n            method: METHOD.GET,\n            url: 'package-account/findAll',\n            dontNeedToken: true,\n            isAuth: true\n        }\n    },\n    master: {\n        getAllTitle: {\n            method: METHOD.GET,\n            url: 'master/titleAll',\n            dontNeedToken: false\n        },\n        getAllRank: {\n            method: METHOD.GET,\n            url: 'master/rankAll',\n            dontNeedToken: false\n        },\n        getAllGroup: {\n            method: METHOD.GET,\n            url: 'master/groupAll',\n            dontNeedToken: false\n        },\n        getProjectType: (fixCost?: boolean) => ({\n            method: METHOD.GET,\n            url: 'master/projectTypeAll' + (fixCost === undefined ? '' : `?fixCost=${fixCost}`),\n            dontNeedToken: false\n        }),\n        getProjectTypeNonBillableConfig: (nonBillable?: boolean) => ({\n            method: METHOD.GET,\n            url: 'project/type/findByBillAbleType' + (nonBillable === undefined ? '' : `?billable=nonbillable`),\n            dontNeedToken: false\n        }),\n        getProjectAll: (fixCost?: boolean) => ({\n            method: METHOD.GET,\n            url: `master/projectAll?fixCost=${fixCost || false}`,\n            dontNeedToken: false\n        }),\n        getProjectAllWithoutFixcost: {\n            method: METHOD.GET,\n            url: 'master/projectAll',\n            dontNeedToken: false\n        },\n        findAllUserLoginTime: {\n            method: METHOD.GET,\n            url: 'master/findUserLogtime',\n            dontNeedToken: false\n        },\n        getFunctionAll: {\n            method: METHOD.GET,\n            url: 'master/functionAll',\n            dontNeedToken: false\n        },\n        getProductionPerformanceAll: {\n            method: METHOD.GET,\n            url: 'master/productionPerformanceAllVND',\n            dontNeedToken: false\n        },\n        getAllLanguage: {\n            method: METHOD.GET,\n            url: 'master/languageAll',\n            dontNeedToken: false\n        }\n    },\n    synchronize: {\n        getDownloadTemplate: {\n            method: METHOD.GET,\n            url: 'master/excel/download',\n            dontNeedToken: false\n        },\n        postImportExcel: {\n            method: METHOD.POST,\n            url: 'sync/manual/import/excel',\n            dontNeedToken: false\n        }\n    },\n    weekly_efford: {\n        getMember: {\n            method: METHOD.GET,\n            url: 'week/member',\n            dontNeedToken: false\n        },\n        getProject: {\n            method: METHOD.GET,\n            url: 'week/project',\n            dontNeedToken: false\n        },\n        getProjectDetail: {\n            method: METHOD.GET,\n            url: 'week/projectDetail',\n            dontNeedToken: false\n        },\n        postVerified: {\n            method: METHOD.POST,\n            url: 'week/projectDetailVerified',\n            dontNeedToken: false\n        },\n        getDownload: {\n            method: METHOD.GET,\n            url: 'week/download',\n            dontNeedToken: false\n        },\n        getWeeklyEffortProject: {\n            method: METHOD.GET,\n            url: 'week/getAllProject',\n            dontNeedToken: false\n        }\n    },\n    monthly_efford: {\n        postUpdateEffordPlan: {\n            method: METHOD.POST,\n            url: 'month/saveOrUpdateEffortPlan',\n            dontNeedToken: false\n        },\n        getSummary: {\n            method: METHOD.GET,\n            url: 'month/summary',\n            dontNeedToken: false\n        },\n        getProject: {\n            method: METHOD.GET,\n            url: 'month/project',\n            dontNeedToken: false\n        },\n        getMember: {\n            method: METHOD.GET,\n            url: 'month/member',\n            dontNeedToken: false\n        },\n        getMemberProject: {\n            method: METHOD.GET,\n            url: 'month/memberProject',\n            dontNeedToken: false\n        },\n\n        getDownload: {\n            method: METHOD.GET,\n            url: 'month/download',\n            dontNeedToken: false\n        },\n\n        // mr/\n        getProjectReports: {\n            method: METHOD.GET,\n            url: 'project/findAllReportProjects',\n            dontNeedToken: false\n        },\n        addProjectReports: {\n            method: METHOD.POST,\n            url: 'project/create',\n            dontNeedToken: false\n        },\n        editProjectReports: (id: string) => ({\n            method: METHOD.PUT,\n            url: `project/${id}`,\n            dontNeedToken: false\n        }),\n        getReportById: (id: string) => ({\n            method: METHOD.GET,\n            url: `project/reportProject/${id}`,\n            dontNeedToken: false\n        }),\n\n        geProjectDetailById: {\n            method: METHOD.GET,\n            url: `project/getProjectWithTotalHC`,\n            dontNeedToken: false\n        },\n        // project reports\n        getDownloadReport: {\n            method: METHOD.GET,\n            url: 'project/downloadReport',\n            dontNeedToken: false\n        },\n        deleteReport: (id: string) => ({\n            method: METHOD.DELETE,\n            url: `project/${id}`,\n            dontNeedToken: false\n        }),\n        getOMReport: {\n            method: METHOD.GET,\n            url: 'orm/getAll',\n            dontNeedToken: false\n        },\n        uploadOrmReport: {\n            method: METHOD.POST,\n            url: 'orm/uploadDocument',\n            dontNeedToken: false\n        },\n        deleteOrmReport: (id: string) => ({\n            method: METHOD.DELETE,\n            url: `orm/${id}`,\n            dontNeedToken: false\n        }),\n        downloadOrmReport: (id: string) => ({\n            method: METHOD.GET,\n            url: `orm/downloadDocument/${id}`,\n            dontNeedToken: false\n        })\n    },\n    list_project_team: {\n        getListProjectTeam: {\n            method: METHOD.GET,\n            url: 'project/team',\n            dontNeedToken: false\n        },\n        getRatioJoiningProject: {\n            method: METHOD.GET,\n            url: 'project/ratio/join',\n            dontNeedToken: false\n        },\n        getDownload: {\n            method: METHOD.GET,\n            url: 'project/download',\n            dontNeedToken: false\n        }\n    },\n    non_billable_monitoring: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'nonbill/nonBillAble',\n            dontNeedToken: false\n        },\n        getDownload: {\n            method: METHOD.GET,\n            url: 'nonbill/download',\n            dontNeedToken: false\n        },\n        getCostByWeek: {\n            method: METHOD.GET,\n            url: 'nonbill/costByWeek',\n            dontNeedToken: false\n        },\n        getWarningNonbillable: {\n            method: METHOD.GET,\n            url: 'nonbill/warningNonbillable',\n            dontNeedToken: false\n        },\n        findAllConfig: {\n            method: METHOD.GET,\n            url: 'nonBillableConfig/findAllConfig',\n            dontNeedToken: false\n        }\n    },\n    monthly_project_cost: {\n        getSummary: (fixCost?: boolean) => ({\n            method: METHOD.GET,\n            url: `monthlyProjectCost/summary?fixCost=${fixCost || false}`,\n            dontNeedToken: false\n        }),\n        getDownload: {\n            method: METHOD.GET,\n            url: 'monthlyProjectCost/download',\n            dontNeedToken: false\n        },\n        getDetailReportByMonth: {\n            method: METHOD.GET,\n            url: 'monthlyProjectCost/detailMonth',\n            dontNeedToken: false\n        },\n        getMonthlyCost: {\n            method: METHOD.GET,\n            url: 'monthlyProjectCost/monthlyCost',\n            dontNeedToken: false\n        },\n        getFindProjectCost: {\n            method: METHOD.GET,\n            url: 'monthlyProjectCost/findProjectCost',\n            dontNeedToken: false\n        },\n        getDownloadTemplateMonthlyCost: {\n            method: METHOD.GET,\n            url: 'monthlyProjectCost/downloadTemplate',\n            dontNeedToken: false\n        },\n        postImportTemplateMonthlyCost: {\n            method: METHOD.POST,\n            url: 'monthlyProjectCost/importTemplate',\n            upload: true,\n            dontNeedToken: false\n        },\n        postSaveOrUpdateActualCost: {\n            method: METHOD.POST,\n            url: 'monthlyProjectCost/saveOrUpdateActualCost',\n            dontNeedToken: false\n        },\n        deleteActualCost: {\n            method: METHOD.DELETE,\n            url: 'monthlyProjectCost/delete',\n            dontNeedToken: false\n        }\n    },\n    cost_monitoring: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'month/costMonitoring/index',\n            dontNeedToken: false\n        },\n        getDownload: {\n            method: METHOD.GET,\n            url: 'month/costMonitoring/download',\n            dontNeedToken: false\n        },\n        getEffortByWeek: {\n            method: METHOD.GET,\n            url: 'month/costMonitoring/effortByWeek',\n            dontNeedToken: false\n        }\n    },\n    member: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'user/findAllUser',\n            dontNeedToken: false\n        },\n        postSaveOrUpdate: {\n            method: METHOD.POST,\n            url: 'user/saveOrUpdateUser',\n            dontNeedToken: false\n        },\n        resetPassowrd: (userId: string) => ({\n            method: METHOD.PUT,\n            url: `user/resetPassword/${userId}`,\n            dontNeedToken: false\n        })\n    },\n    department: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'getAllDepartment',\n            dontNeedToken: false\n        },\n        search: {\n            method: METHOD.GET,\n            url: 'search',\n            dontNeedToken: false\n        },\n        create: {\n            method: METHOD.POST,\n            url: 'create',\n            dontNeedToken: false\n        },\n        edit: (departmentId: string) => ({\n            method: METHOD.PUT,\n            url: `${departmentId}`,\n            dontNeedToken: false\n        }),\n        delete: (departmentId: string) => ({\n            method: METHOD.DELETE,\n            url: `${departmentId}`,\n            dontNeedToken: false\n        })\n    },\n    project: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'project/getAllProject',\n            dontNeedToken: false\n        },\n        getDetail: {\n            method: METHOD.GET,\n            url: 'project/getInfoProjectDetail',\n            dontNeedToken: false\n        },\n        getQuotaUpdateHistory: {\n            method: METHOD.GET,\n            url: 'project/getInfoProjectQuotaHistory',\n            dontNeedToken: false\n        },\n        saveOrUpdate: {\n            method: METHOD.POST,\n            url: 'project/saveOrUpdate',\n            dontNeedToken: false\n        },\n        saveOrUpdateProjectUser: {\n            method: METHOD.POST,\n            url: 'project/saveOrUpdateProjectUser',\n            dontNeedToken: false\n        },\n        deleteProjectUser: {\n            method: METHOD.DELETE,\n            url: 'project/deleteProjectUser',\n            dontNeedToken: false\n        },\n        getDownload: {\n            method: METHOD.GET,\n            url: 'project/export',\n            dontNeedToken: false\n        },\n        typeConfig: {\n            search: {\n                method: METHOD.GET,\n                url: 'project/type/search',\n                dontNeedToken: false\n            },\n            create: {\n                method: METHOD.POST,\n                url: 'project/type/create',\n                dontNeedToken: false\n            },\n            edit: (projectTypeId: string) => ({\n                method: METHOD.PUT,\n                url: `project/type/${projectTypeId}`,\n                dontNeedToken: false\n            }),\n            delete: (projectTypeId: string) => ({\n                method: METHOD.DELETE,\n                url: `project/type/${projectTypeId}`,\n                dontNeedToken: false\n            })\n        }\n    },\n    nonBillableConfig: {\n        search: {\n            method: METHOD.GET,\n            url: 'nonBillableConfig/findAll',\n            dontNeedToken: false\n        },\n        edit: (configId: string) => ({\n            method: METHOD.PUT,\n            url: `nonBillableConfig/update/${configId}`,\n            dontNeedToken: false\n        })\n    },\n    title: {\n        search: {\n            method: METHOD.GET,\n            url: 'title/search',\n            dontNeedToken: false\n        },\n        create: {\n            method: METHOD.POST,\n            url: 'title/create',\n            dontNeedToken: false\n        },\n        edit: (titleId: string) => ({\n            method: METHOD.PUT,\n            url: `title/${titleId}`,\n            dontNeedToken: false\n        }),\n        delete: (titleId: string) => ({\n            method: METHOD.DELETE,\n            url: `title/${titleId}`,\n            dontNeedToken: false\n        })\n    },\n    special_hours: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'special/getAllSpecialHour',\n            dontNeedToken: false\n        },\n        postSaveOrUpdateSpecialHours: {\n            method: METHOD.POST,\n            url: 'special/saveOrUpdate',\n            dontNeedToken: false\n        }\n    },\n    system_config: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'master/systemConfigAll',\n            dontNeedToken: false\n        },\n        postUpdateConfig: {\n            method: METHOD.POST,\n            url: 'systemConfig/saveOrUpdate',\n            dontNeedToken: false\n        }\n    },\n    email_config: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'master/emailConfigAll',\n            dontNeedToken: false\n        },\n        postSaveOrUpdateEmailConfig: {\n            method: METHOD.POST,\n            url: 'email/saveOrUpdate',\n            dontNeedToken: false\n        }\n    },\n    cv_config_technology: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'tech/getAll',\n            dontNeedToken: false\n        },\n        postSaveOrUpdateCVConfig: {\n            method: METHOD.POST,\n            url: 'tech/createOrUpdate',\n            dontNeedToken: false\n        },\n        deleteTechnology: {\n            method: METHOD.DELETE,\n            url: 'tech/delete',\n            dontNeedToken: false\n        }\n    },\n    cv_config_language: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'language/getAll',\n            dontNeedToken: false\n        },\n        postSaveOrUpdateCVConfig: {\n            method: METHOD.POST,\n            url: 'language/saveOrUpdate',\n            dontNeedToken: false\n        },\n        deleteLanguage: {\n            method: METHOD.DELETE,\n            url: 'language/delete',\n            dontNeedToken: false\n        }\n    },\n    cv_config_reference: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'references/getAll',\n            dontNeedToken: false\n        },\n        postSaveOrUpdateCVConfig: {\n            method: METHOD.POST,\n            url: 'references/saveOrUpdate',\n            dontNeedToken: false\n        },\n        deleteReference: {\n            method: METHOD.DELETE,\n            url: 'references/delete',\n            dontNeedToken: false\n        }\n    },\n    exchange_rate_config: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'exchange-rate',\n            dontNeedToken: false\n        },\n        postSaveOrUpdateExchangeRateConfig: {\n            method: METHOD.POST,\n            url: 'exchange-rate/saveOrUpdate',\n            dontNeedToken: false\n        },\n        deleteExchangeRate: {\n            method: METHOD.DELETE,\n            url: 'exchange-rate/delete',\n            dontNeedToken: false\n        }\n    },\n    holiday: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'holiday/getAll',\n            dontNeedToken: false\n        },\n        postSaveOrUpdateHoliday: {\n            method: METHOD.POST,\n            url: 'holiday/saveOrUpdate',\n            dontNeedToken: false\n        },\n        delete: {\n            method: METHOD.DELETE,\n            url: 'holiday/delete',\n            dontNeedToken: false\n        }\n    },\n    rank: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'rank/rankAll',\n            dontNeedToken: false\n        },\n        postUpdateRank: {\n            method: METHOD.POST,\n            url: 'rank/createOrUpdateRank',\n            dontNeedToken: false\n        }\n    },\n    group: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'group/getGroupAll',\n            dontNeedToken: false\n        },\n        postSaveOrUpdate: {\n            method: METHOD.POST,\n            url: 'group/saveOrUpdate',\n            dontNeedToken: false\n        }\n    },\n    sale_productivity: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'sales/productivityProject',\n            dontNeedToken: false\n        },\n        getDetail: {\n            method: METHOD.GET,\n            url: 'sales/productivityProject/getDetail',\n            dontNeedToken: false\n        },\n        postCreateOrUpdate: {\n            method: METHOD.POST,\n            url: 'sales/productivityProject/createOrUpdate',\n            dontNeedToken: false\n        },\n        getDownload: {\n            method: METHOD.GET,\n            url: 'sales/productivityProject/download',\n            dontNeedToken: false\n        },\n        postUpdateHeadCount: {\n            method: METHOD.POST,\n            url: 'sales/productivityProject/updateHeadcount',\n            dontNeedToken: false\n        },\n        getDetailHeadCountByMonth: {\n            method: METHOD.GET,\n            url: 'sales/productivityProject/findHeadcount',\n            dontNeedToken: false\n        },\n        postComment: {\n            method: METHOD.POST,\n            url: 'sales/productivityProject/comment',\n            dontNeedToken: false\n        },\n        getStandardWorkingDay: {\n            method: METHOD.GET,\n            url: 'sales/productivityProject/standardWorkingDay',\n            dontNeedToken: false\n        },\n        getExchangeRate: {\n            method: METHOD.GET,\n            url: 'sales/productivityProject/exchangeRate',\n            dontNeedToken: false\n        }\n    },\n    comment: {\n        getFindComment: {\n            method: METHOD.GET,\n            url: 'comment/findComment',\n            dontNeedToken: false\n        },\n        postSaveOrUpdateComment: {\n            method: METHOD.POST,\n            url: 'comment/saveOrUpdate',\n            dontNeedToken: false\n        },\n        getFindCommentDetail: {\n            method: METHOD.GET,\n            url: 'commentDetail/findComment',\n            dontNeedToken: false\n        },\n        postSaveOrUpdateCommentDetail: {\n            method: METHOD.POST,\n            url: 'commentDetail/saveOrUpdate',\n            dontNeedToken: false\n        }\n    },\n    sale_list: {\n        getRequestAll: {\n            method: METHOD.GET,\n            url: 'sales/list/requests/getAll',\n            dontNeedToken: false\n        },\n        getSupplierAll: {\n            method: METHOD.GET,\n            url: 'sales/list/supplier/getAll',\n            dontNeedToken: false\n        },\n        postRequestsCreateOrUpdate: {\n            method: METHOD.POST,\n            url: 'sales/list/requests/createOrUpdate',\n            dontNeedToken: false\n        },\n        deleteRequests: (id: string) => ({\n            method: METHOD.DELETE,\n            url: `sales/list/requests/delete?id=${id}`,\n            dontNeedToken: false\n        }),\n        getDownload: {\n            method: METHOD.GET,\n            url: 'sales/list/download',\n            dontNeedToken: false\n        },\n        postSupplierCreateOrUpdate: {\n            method: METHOD.POST,\n            url: 'sales/list/supplier/createOrUpdate',\n            dontNeedToken: false\n        },\n        deleteSupplier: (id: string) => ({\n            method: METHOD.DELETE,\n            url: `sales/list/supplier/delete?id=${id}`,\n            dontNeedToken: false\n        }),\n        getSaleSummary: {\n            method: METHOD.GET,\n            url: 'sales/summary',\n            dontNeedToken: false\n        },\n        getDownloadSalePineLine: {\n            method: METHOD.GET,\n            url: 'sales/download',\n            dontNeedToken: false\n        },\n        getTotalSummary: {\n            method: METHOD.GET,\n            url: 'sales/summary-total',\n            dontNeedToken: false\n        }\n    },\n    working_calendar: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'calendar/filterWorkingCalender',\n            dontNeedToken: false\n        },\n        getType: {\n            method: METHOD.GET,\n            url: 'calendar/getTypeWorkingCalendar',\n            dontNeedToken: false\n        },\n        postSaveOrUpdate: {\n            method: METHOD.POST,\n            url: 'calendar/saveOrUpdate',\n            dontNeedToken: false\n        },\n        getDownload: {\n            method: METHOD.GET,\n            url: 'calendar/download',\n            dontNeedToken: false\n        },\n        postUpdateStatus: {\n            method: METHOD.POST,\n            url: 'calendar/updateStatus',\n            dontNeedToken: false\n        },\n        getClosingDate: {\n            method: METHOD.GET,\n            url: 'calendar/closing-dates',\n            dontNeedToken: false\n        },\n        postUpdateClosingDate: {\n            method: METHOD.POST,\n            url: 'calendar/closing-date/update',\n            dontNeedToken: false\n        },\n        postVerifyClosingDate: {\n            method: METHOD.POST,\n            url: 'calendar/closing-date/verify',\n            dontNeedToken: false\n        },\n        findOnsite: {\n            method: METHOD.GET,\n            url: 'calendar/onsite',\n            dontNeedToken: false\n        },\n        updateComment: {\n            method: METHOD.PUT,\n            url: 'calendar/updateComment',\n            dontNeedToken: false\n        }\n    },\n    manage_leaves: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'leaves',\n            dontNeedToken: false\n        },\n        getLeaveDetail: (idHexString: string) => ({\n            method: METHOD.GET,\n            url: `leaves/detail/${idHexString}`,\n            dontNeedToken: false\n        }),\n        getDetailUser: {\n            method: METHOD.GET,\n            url: 'leaves/detail-user',\n            dontNeedToken: false\n        },\n        getDetailUserId: (idHexString: string) => {\n            return { method: METHOD.GET, url: `leaves/detail/${idHexString}`, dontNeedToken: false };\n        },\n        putApproved: (idHexString: string) => {\n            return {\n                method: METHOD.PUT,\n                url: `leaves/approved/${idHexString}`,\n                dontNeedToken: false\n            };\n        },\n        putReject: {\n            method: METHOD.PUT,\n            url: `leaves/reject`,\n            dontNeedToken: false\n        },\n        postCreate: {\n            method: METHOD.POST,\n            url: 'leaves',\n            dontNeedToken: false\n        },\n        putUpdate: (idHexString: string) => {\n            return {\n                method: METHOD.PUT,\n                url: `leaves/${idHexString}`,\n                dontNeedToken: false\n            };\n        },\n        deleteLeave: (idHexString: string) => {\n            return {\n                method: METHOD.DELETE,\n                url: `leaves/delete/${idHexString}`,\n                dontNeedToken: false\n            };\n        },\n        downloadLeave: (idHexString: string) => {\n            return {\n                method: METHOD.GET,\n                url: `leaves/download/${idHexString}`,\n                dontNeedToken: false\n            };\n        }\n    },\n    manage_ot: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'overTimeTicket/search',\n            dontNeedToken: false\n        },\n        putReject: {\n            method: METHOD.PUT,\n            url: `overTimeTicket/reject`,\n            dontNeedToken: false\n        },\n        deleteOT: (idHexString: string) => {\n            return {\n                method: METHOD.DELETE,\n                url: `overTimeTicket/delete/${idHexString}`,\n                dontNeedToken: false\n            };\n        }\n    },\n    manage_resignation: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'resignation',\n            dontNeedToken: false\n        },\n        putApproved: (idHexString: string) => {\n            return {\n                method: METHOD.PUT,\n                url: `resignation/approved/${idHexString}`,\n                dontNeedToken: false\n            };\n        },\n        postCreate: {\n            method: METHOD.POST,\n            url: 'resignation',\n            dontNeedToken: false\n        },\n        putUpdate: (idHexString: string) => {\n            return {\n                method: METHOD.PUT,\n                url: `resignation/${idHexString}`,\n                dontNeedToken: false\n            };\n        }\n    },\n    sale_pipeline: {\n        getListYear: {\n            method: METHOD.GET,\n            url: 'sales/sale-pipeline/listYear',\n            dontNeedToken: false\n        }\n    },\n    sale_pipeline_on_going: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'sales/onGoing',\n            dontNeedToken: false\n        },\n        saveOrUpdate: {\n            method: METHOD.POST,\n            url: 'sales/onGoing/saveOrUpdate',\n            dontNeedToken: false\n        },\n        comment: {\n            method: METHOD.POST,\n            url: 'sales/onGoing/updateComment',\n            dontNeedToken: false\n        },\n        getTotal: {\n            method: METHOD.GET,\n            url: 'sales/onGoing/total-ongoing',\n            dontNeedToken: false\n        }\n    },\n    sale_pipe_line_bidding: {\n        getBidding: {\n            method: METHOD.GET,\n            url: 'sales/sale-pipeline/bidding',\n            dontNeedToken: false\n        },\n        getMonthlyBillable: {\n            method: METHOD.GET,\n            url: 'sales/sale-pipeline/monthlyBillable',\n            dontNeedToken: false\n        },\n        estimateHCInfo: {\n            method: METHOD.POST,\n            url: 'sales/sale-pipeline/estimateHCInfo',\n            dontNeedToken: false\n        },\n        postAddOrEditBidding: {\n            method: METHOD.POST,\n            url: 'sales/sale-pipeline/bidding/createOrUpdate',\n            dontNeedToken: false\n        },\n        getDetailBidding: {\n            method: METHOD.GET,\n            url: 'sales/sale-pipeline/bidding/details',\n            dontNeedToken: false\n        },\n        deleteBidding: {\n            method: METHOD.DELETE,\n            url: 'sales/sale-pipeline/bidding',\n            dontNeedToken: false\n        },\n        comment: {\n            method: METHOD.POST,\n            url: 'sales/sale-pipeline/bidding/updateComment',\n            dontNeedToken: false\n        },\n        getTotal: {\n            method: METHOD.GET,\n            url: 'sales/sale-pipeline/total-bidding',\n            dontNeedToken: false\n        },\n        getTotal: {\n            method: METHOD.GET,\n            url: 'sales/sale-pipeline/total-bidding',\n            dontNeedToken: false\n        }\n    },\n    budgeting_plan: {\n        getAll: {\n            method: METHOD.GET,\n            url: 'sales/budgeting-plan',\n            dontNeedToken: false\n        },\n        editBudgetingPlan: {\n            method: METHOD.POST,\n            url: 'sales/budgeting-plan/saveOrUpdate',\n            dontNeedToken: false\n        },\n        getTotal: {\n            method: METHOD.GET,\n            url: 'sales/budgeting-plan/total-budgeting-plan',\n            dontNeedToken: false\n        },\n        getTotal: {\n            method: METHOD.GET,\n            url: 'sales/budgeting-plan/total-budgeting-plan',\n            dontNeedToken: false\n        }\n    },\n    skills_manage: {\n        getReport: {\n            method: METHOD.GET,\n            url: 'skill/report',\n            dontNeedToken: false\n        },\n        getDownload: {\n            method: METHOD.GET,\n            url: 'skill/report/download',\n            dontNeedToken: false\n        },\n        getTitleCodes: {\n            method: METHOD.GET,\n            url: 'master/titleAll',\n            dontNeedToken: false\n        },\n        getTechAll: {\n            method: METHOD.GET,\n            url: 'master/techAll'\n        },\n        getSkillsUpdate: {\n            method: METHOD.GET,\n            url: 'skill/skillsUpdate',\n            dontNeedToken: false\n        },\n        getDetailCv: {\n            method: METHOD.GET,\n            url: 'skill/skillsUpdate/getDetail',\n            dontNeedToken: false\n        },\n        getReferences: {\n            method: METHOD.GET,\n            url: 'skill/skillsUpdate/references',\n            dontNeedToken: false\n        },\n        getTechnologies: {\n            method: METHOD.GET,\n            url: 'skill/skillsUpdate/technologyAll',\n            dontNeedToken: false\n        },\n        getSkillByTechnology: {\n            method: METHOD.GET,\n            url: 'skill/skillsUpdate/skillByTech',\n            dontNeedToken: false\n        },\n        createSkillsUpdate: {\n            method: METHOD.POST,\n            url: 'skill/skillsUpdate/create',\n            dontNeedToken: false\n        },\n        updateCV: {\n            method: METHOD.PATCH,\n            url: 'skill/skillsUpdate/update',\n            dontNeedToken: false\n        },\n        viewPDF: {\n            method: METHOD.GET,\n            url: 'skill/skillsUpdate/viewPdf',\n            dontNeedToken: false\n        },\n        downloadCV: {\n            method: METHOD.GET,\n            url: 'skill/skillsUpdate/download',\n            dontNeedToken: false\n        }\n    },\n    product: {\n        getProductReportOptions: {\n            method: METHOD.GET,\n            url: 'project/projectReport/getAllProject',\n            dontNeedToken: false\n        },\n        getReport: {\n            method: METHOD.GET,\n            url: 'project/projectReport',\n            dontNeedToken: false\n        },\n        getReportByProjectId: (projectId: number) => ({\n            method: METHOD.GET,\n            url: `project/projectReport/${projectId}`,\n            dontNeedToken: false\n        })\n    },\n    leave_day: {\n        getAll: {\n            method: METHOD.GET,\n            url: '/manageLeaveDays',\n            dontNeedToken: false\n        },\n        getLeaveDaysInfo: (idHexString: string) => ({\n            method: METHOD.GET,\n            url: `/manageLeaveDays/${idHexString}`,\n            dontNeedToken: false\n        }),\n        postSaveOrUpdate: {\n            method: METHOD.PUT,\n            url: '/manageLeaveDays',\n            dontNeedToken: false\n        }\n    },\n    monitor_bidding: {\n        getBiddingTracking: {\n            method: METHOD.GET,\n            url: '/biddingTracking/getBiddingTracking',\n            dontNeedToken: false\n        },\n        getDownload: {\n            method: METHOD.GET,\n            url: 'monitorBidding/download',\n            dontNeedToken: false,\n            responseType: 'blob'\n        },\n        getBiddingReport: {\n            method: METHOD.GET,\n            url: '/monitorBidding/getBiddingReport',\n            dontNeedToken: false\n        },\n        createOrUpdateBiddingReportpost: {\n            method: METHOD.POST,\n            url: '/monitorBidding/createBiddingReport',\n            dontNeedToken: false\n        },\n        updateBiddingReportpost: (id: string) => ({\n            method: METHOD.PUT,\n            url: `/monitorBidding/updateBiddingReport/${id}`,\n            dontNeedToken: false\n        }),\n        postSynchronize: (token: string) => ({\n            method: METHOD.POST,\n            url: `/biddingTracking/postBiddingTracking?token=${token}`,\n            dontNeedToken: false\n        })\n    },\n    flexible_report: {\n        get: {\n            method: METHOD.GET,\n            url: '/flexible-report',\n            dontNeedToken: false\n        },\n        getAllReports: {\n            method: METHOD.GET,\n            url: '/flexible-report/config',\n            dontNeedToken: false\n        },\n        getConditionTypes: {\n            method: METHOD.GET,\n            url: '/flexible-columns/findAllByReport',\n            dontNeedToken: false\n        },\n        createConfig: {\n            method: METHOD.POST,\n            url: '/flexible-report/config/create',\n            dontNeedToken: false\n        },\n        editConfig: (id: string) => ({\n            method: METHOD.PUT,\n            url: `/flexible-report/config/update/${id}`,\n            dontNeedToken: false\n        }),\n        deleteConfig: (id: string) => ({\n            method: METHOD.DELETE,\n            url: `/flexible-report/config/${id}`,\n            dontNeedToken: false\n        }),\n        getConditionOptions: {\n            method: METHOD.GET,\n            url: '/type/all',\n            dontNeedToken: false\n        },\n        editArrangement: {\n            method: METHOD.PUT,\n            url: 'flexible-report/update-index',\n            dontNeedToken: false\n        }\n    },\n    flexible_columns: {\n        get: {\n            method: METHOD.GET,\n            url: '/flexible-columns/columns',\n            dontNeedToken: false\n        },\n        edit: {\n            method: METHOD.PUT,\n            url: '/flexible-columns',\n            dontNeedToken: false\n        }\n    },\n    flexible_textConfig: {\n        get: {\n            method: METHOD.GET,\n            url: '/flexible-text-config/get-all',\n            dontNeedToken: false\n        },\n        edit: {\n            method: METHOD.PUT,\n            url: '/flexible-text-config/update',\n            dontNeedToken: false\n        },\n        configByLanguage: {\n            method: METHOD.GET,\n            url: '/flexible-text-config/config-by-language',\n            dontNeedToken: true\n        },\n        getLanguage: {\n            method: METHOD.GET,\n            url: '/flexible-language',\n            dontNeedToken: false\n        },\n        addLanguage: {\n            method: METHOD.POST,\n            url: '/flexible-language/create',\n            dontNeedToken: false\n        },\n        deleteLanguage: (id: string) => ({\n            method: METHOD.DELETE,\n            url: `/flexible-language/${id}`,\n            dontNeedToken: false\n        })\n    },\n    overtime_report: {\n        postOvertimeTicket: {\n            method: METHOD.POST,\n            url: 'overTimeTicket',\n            dontNeedToken: false\n        },\n        getDetailOvertimeTicket: (idHexString: string) => ({\n            method: METHOD.GET,\n            url: `overTimeTicket/detail/${idHexString}`,\n            dontNeedToken: false\n        }),\n        updateDetailOvertimeTicket: (idHexString: string) => ({\n            method: METHOD.PUT,\n            url: `overTimeTicket/update/${idHexString}`,\n            dontNeedToken: false\n        }),\n        approvedOvertimeTicket: (idHexString: string) => ({\n            method: METHOD.PUT,\n            url: `overTimeTicket/approved/${idHexString}`,\n            dontNeedToken: false\n        })\n    }\n};\n\nexport default Api;\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG;EACXC,GAAG,EAAE,KAAK;EACVC,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,KAAK;EACVC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE;AACX,CAAC;AAED,MAAMC,GAAG,GAAG;EACRC,IAAI,EAAE;IACFC,KAAK,EAAEA,CAACC,MAAe,GAAG,KAAK,MAAM;MACjCC,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,cAAcF,MAAM,EAAE;MAC3BG,aAAa,EAAE,IAAI;MACnBC,MAAM,EAAE;IACZ,CAAC,CAAC;IACFC,YAAY,EAAEA,CAACL,MAAc,GAAG,OAAO,MAAM;MACzCC,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,qBAAqBF,MAAM,EAAE;MAClCG,aAAa,EAAE,IAAI;MACnBC,MAAM,EAAE;IACZ,CAAC,CAAC;IACFE,EAAE,EAAE;MACAL,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,IAAI;MACTC,aAAa,EAAE;IACnB,CAAC;IACDI,cAAc,EAAGC,MAAc,KAAM;MACjCP,MAAM,EAAEV,MAAM,CAACG,GAAG;MAClBQ,GAAG,EAAE,kBAAkBM,MAAM,EAAE;MAC/BL,aAAa,EAAE,KAAK;MACpBC,MAAM,EAAE;IACZ,CAAC,CAAC;IACFK,QAAQ,EAAE;MACNR,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,UAAU;MACfC,aAAa,EAAE,IAAI;MACnBC,MAAM,EAAE;IACZ,CAAC;IACDM,MAAM,EAAGC,KAAa,KAAM;MACxBV,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,wBAAwBS,KAAK,EAAE;MACpCR,aAAa,EAAE,IAAI;MACnBC,MAAM,EAAE;IACZ,CAAC,CAAC;IACFQ,cAAc,EAAE;MACZX,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,gBAAgB;MACrBC,aAAa,EAAE,IAAI;MACnBC,MAAM,EAAE;IACZ,CAAC;IACDS,WAAW,EAAE;MACTZ,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,cAAc;MACnBC,aAAa,EAAE,IAAI;MACnBC,MAAM,EAAE;IACZ,CAAC;IACDU,oBAAoB,EAAE;MAClBb,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,yBAAyB;MAC9BC,aAAa,EAAE,IAAI;MACnBC,MAAM,EAAE;IACZ;EACJ,CAAC;EACDW,MAAM,EAAE;IACJC,WAAW,EAAE;MACTf,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,iBAAiB;MACtBC,aAAa,EAAE;IACnB,CAAC;IACDc,UAAU,EAAE;MACRhB,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,gBAAgB;MACrBC,aAAa,EAAE;IACnB,CAAC;IACDe,WAAW,EAAE;MACTjB,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,iBAAiB;MACtBC,aAAa,EAAE;IACnB,CAAC;IACDgB,cAAc,EAAGC,OAAiB,KAAM;MACpCnB,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,uBAAuB,IAAIkB,OAAO,KAAKC,SAAS,GAAG,EAAE,GAAG,YAAYD,OAAO,EAAE,CAAC;MACnFjB,aAAa,EAAE;IACnB,CAAC,CAAC;IACFmB,+BAA+B,EAAGC,WAAqB,KAAM;MACzDtB,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,iCAAiC,IAAIqB,WAAW,KAAKF,SAAS,GAAG,EAAE,GAAG,uBAAuB,CAAC;MACnGlB,aAAa,EAAE;IACnB,CAAC,CAAC;IACFqB,aAAa,EAAGJ,OAAiB,KAAM;MACnCnB,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,6BAA6BkB,OAAO,IAAI,KAAK,EAAE;MACpDjB,aAAa,EAAE;IACnB,CAAC,CAAC;IACFsB,2BAA2B,EAAE;MACzBxB,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,mBAAmB;MACxBC,aAAa,EAAE;IACnB,CAAC;IACDuB,oBAAoB,EAAE;MAClBzB,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,wBAAwB;MAC7BC,aAAa,EAAE;IACnB,CAAC;IACDwB,cAAc,EAAE;MACZ1B,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,oBAAoB;MACzBC,aAAa,EAAE;IACnB,CAAC;IACDyB,2BAA2B,EAAE;MACzB3B,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,oCAAoC;MACzCC,aAAa,EAAE;IACnB,CAAC;IACD0B,cAAc,EAAE;MACZ5B,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,oBAAoB;MACzBC,aAAa,EAAE;IACnB;EACJ,CAAC;EACD2B,WAAW,EAAE;IACTC,mBAAmB,EAAE;MACjB9B,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,uBAAuB;MAC5BC,aAAa,EAAE;IACnB,CAAC;IACD6B,eAAe,EAAE;MACb/B,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,0BAA0B;MAC/BC,aAAa,EAAE;IACnB;EACJ,CAAC;EACD8B,aAAa,EAAE;IACXC,SAAS,EAAE;MACPjC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,aAAa;MAClBC,aAAa,EAAE;IACnB,CAAC;IACDgC,UAAU,EAAE;MACRlC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,cAAc;MACnBC,aAAa,EAAE;IACnB,CAAC;IACDiC,gBAAgB,EAAE;MACdnC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,oBAAoB;MACzBC,aAAa,EAAE;IACnB,CAAC;IACDkC,YAAY,EAAE;MACVpC,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,4BAA4B;MACjCC,aAAa,EAAE;IACnB,CAAC;IACDmC,WAAW,EAAE;MACTrC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,eAAe;MACpBC,aAAa,EAAE;IACnB,CAAC;IACDoC,sBAAsB,EAAE;MACpBtC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,oBAAoB;MACzBC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDqC,cAAc,EAAE;IACZC,oBAAoB,EAAE;MAClBxC,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,8BAA8B;MACnCC,aAAa,EAAE;IACnB,CAAC;IACDuC,UAAU,EAAE;MACRzC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,eAAe;MACpBC,aAAa,EAAE;IACnB,CAAC;IACDgC,UAAU,EAAE;MACRlC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,eAAe;MACpBC,aAAa,EAAE;IACnB,CAAC;IACD+B,SAAS,EAAE;MACPjC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,cAAc;MACnBC,aAAa,EAAE;IACnB,CAAC;IACDwC,gBAAgB,EAAE;MACd1C,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,qBAAqB;MAC1BC,aAAa,EAAE;IACnB,CAAC;IAEDmC,WAAW,EAAE;MACTrC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,gBAAgB;MACrBC,aAAa,EAAE;IACnB,CAAC;IAED;IACAyC,iBAAiB,EAAE;MACf3C,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,+BAA+B;MACpCC,aAAa,EAAE;IACnB,CAAC;IACD0C,iBAAiB,EAAE;MACf5C,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,gBAAgB;MACrBC,aAAa,EAAE;IACnB,CAAC;IACD2C,kBAAkB,EAAGC,EAAU,KAAM;MACjC9C,MAAM,EAAEV,MAAM,CAACG,GAAG;MAClBQ,GAAG,EAAE,WAAW6C,EAAE,EAAE;MACpB5C,aAAa,EAAE;IACnB,CAAC,CAAC;IACF6C,aAAa,EAAGD,EAAU,KAAM;MAC5B9C,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,yBAAyB6C,EAAE,EAAE;MAClC5C,aAAa,EAAE;IACnB,CAAC,CAAC;IAEF8C,mBAAmB,EAAE;MACjBhD,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,+BAA+B;MACpCC,aAAa,EAAE;IACnB,CAAC;IACD;IACA+C,iBAAiB,EAAE;MACfjD,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,wBAAwB;MAC7BC,aAAa,EAAE;IACnB,CAAC;IACDgD,YAAY,EAAGJ,EAAU,KAAM;MAC3B9C,MAAM,EAAEV,MAAM,CAACI,MAAM;MACrBO,GAAG,EAAE,WAAW6C,EAAE,EAAE;MACpB5C,aAAa,EAAE;IACnB,CAAC,CAAC;IACFiD,WAAW,EAAE;MACTnD,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,YAAY;MACjBC,aAAa,EAAE;IACnB,CAAC;IACDkD,eAAe,EAAE;MACbpD,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,oBAAoB;MACzBC,aAAa,EAAE;IACnB,CAAC;IACDmD,eAAe,EAAGP,EAAU,KAAM;MAC9B9C,MAAM,EAAEV,MAAM,CAACI,MAAM;MACrBO,GAAG,EAAE,OAAO6C,EAAE,EAAE;MAChB5C,aAAa,EAAE;IACnB,CAAC,CAAC;IACFoD,iBAAiB,EAAGR,EAAU,KAAM;MAChC9C,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,wBAAwB6C,EAAE,EAAE;MACjC5C,aAAa,EAAE;IACnB,CAAC;EACL,CAAC;EACDqD,iBAAiB,EAAE;IACfC,kBAAkB,EAAE;MAChBxD,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,cAAc;MACnBC,aAAa,EAAE;IACnB,CAAC;IACDuD,sBAAsB,EAAE;MACpBzD,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,oBAAoB;MACzBC,aAAa,EAAE;IACnB,CAAC;IACDmC,WAAW,EAAE;MACTrC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,kBAAkB;MACvBC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDwD,uBAAuB,EAAE;IACrBC,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,qBAAqB;MAC1BC,aAAa,EAAE;IACnB,CAAC;IACDmC,WAAW,EAAE;MACTrC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,kBAAkB;MACvBC,aAAa,EAAE;IACnB,CAAC;IACD0D,aAAa,EAAE;MACX5D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,oBAAoB;MACzBC,aAAa,EAAE;IACnB,CAAC;IACD2D,qBAAqB,EAAE;MACnB7D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,4BAA4B;MACjCC,aAAa,EAAE;IACnB,CAAC;IACD4D,aAAa,EAAE;MACX9D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,iCAAiC;MACtCC,aAAa,EAAE;IACnB;EACJ,CAAC;EACD6D,oBAAoB,EAAE;IAClBtB,UAAU,EAAGtB,OAAiB,KAAM;MAChCnB,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,sCAAsCkB,OAAO,IAAI,KAAK,EAAE;MAC7DjB,aAAa,EAAE;IACnB,CAAC,CAAC;IACFmC,WAAW,EAAE;MACTrC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,6BAA6B;MAClCC,aAAa,EAAE;IACnB,CAAC;IACD8D,sBAAsB,EAAE;MACpBhE,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,gCAAgC;MACrCC,aAAa,EAAE;IACnB,CAAC;IACD+D,cAAc,EAAE;MACZjE,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,gCAAgC;MACrCC,aAAa,EAAE;IACnB,CAAC;IACDgE,kBAAkB,EAAE;MAChBlE,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,oCAAoC;MACzCC,aAAa,EAAE;IACnB,CAAC;IACDiE,8BAA8B,EAAE;MAC5BnE,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,qCAAqC;MAC1CC,aAAa,EAAE;IACnB,CAAC;IACDkE,6BAA6B,EAAE;MAC3BpE,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,mCAAmC;MACxCoE,MAAM,EAAE,IAAI;MACZnE,aAAa,EAAE;IACnB,CAAC;IACDoE,0BAA0B,EAAE;MACxBtE,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,2CAA2C;MAChDC,aAAa,EAAE;IACnB,CAAC;IACDqE,gBAAgB,EAAE;MACdvE,MAAM,EAAEV,MAAM,CAACI,MAAM;MACrBO,GAAG,EAAE,2BAA2B;MAChCC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDsE,eAAe,EAAE;IACbb,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,4BAA4B;MACjCC,aAAa,EAAE;IACnB,CAAC;IACDmC,WAAW,EAAE;MACTrC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,+BAA+B;MACpCC,aAAa,EAAE;IACnB,CAAC;IACDuE,eAAe,EAAE;MACbzE,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,mCAAmC;MACxCC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDwE,MAAM,EAAE;IACJf,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,kBAAkB;MACvBC,aAAa,EAAE;IACnB,CAAC;IACDyE,gBAAgB,EAAE;MACd3E,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,uBAAuB;MAC5BC,aAAa,EAAE;IACnB,CAAC;IACD0E,aAAa,EAAGrE,MAAc,KAAM;MAChCP,MAAM,EAAEV,MAAM,CAACG,GAAG;MAClBQ,GAAG,EAAE,sBAAsBM,MAAM,EAAE;MACnCL,aAAa,EAAE;IACnB,CAAC;EACL,CAAC;EACD2E,UAAU,EAAE;IACRlB,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,kBAAkB;MACvBC,aAAa,EAAE;IACnB,CAAC;IACD4E,MAAM,EAAE;MACJ9E,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,QAAQ;MACbC,aAAa,EAAE;IACnB,CAAC;IACD6E,MAAM,EAAE;MACJ/E,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,QAAQ;MACbC,aAAa,EAAE;IACnB,CAAC;IACD8E,IAAI,EAAGC,YAAoB,KAAM;MAC7BjF,MAAM,EAAEV,MAAM,CAACG,GAAG;MAClBQ,GAAG,EAAE,GAAGgF,YAAY,EAAE;MACtB/E,aAAa,EAAE;IACnB,CAAC,CAAC;IACFgF,MAAM,EAAGD,YAAoB,KAAM;MAC/BjF,MAAM,EAAEV,MAAM,CAACI,MAAM;MACrBO,GAAG,EAAE,GAAGgF,YAAY,EAAE;MACtB/E,aAAa,EAAE;IACnB,CAAC;EACL,CAAC;EACDiF,OAAO,EAAE;IACLxB,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,uBAAuB;MAC5BC,aAAa,EAAE;IACnB,CAAC;IACDkF,SAAS,EAAE;MACPpF,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,8BAA8B;MACnCC,aAAa,EAAE;IACnB,CAAC;IACDmF,qBAAqB,EAAE;MACnBrF,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,oCAAoC;MACzCC,aAAa,EAAE;IACnB,CAAC;IACDoF,YAAY,EAAE;MACVtF,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,sBAAsB;MAC3BC,aAAa,EAAE;IACnB,CAAC;IACDqF,uBAAuB,EAAE;MACrBvF,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,iCAAiC;MACtCC,aAAa,EAAE;IACnB,CAAC;IACDsF,iBAAiB,EAAE;MACfxF,MAAM,EAAEV,MAAM,CAACI,MAAM;MACrBO,GAAG,EAAE,2BAA2B;MAChCC,aAAa,EAAE;IACnB,CAAC;IACDmC,WAAW,EAAE;MACTrC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,gBAAgB;MACrBC,aAAa,EAAE;IACnB,CAAC;IACDuF,UAAU,EAAE;MACRX,MAAM,EAAE;QACJ9E,MAAM,EAAEV,MAAM,CAACC,GAAG;QAClBU,GAAG,EAAE,qBAAqB;QAC1BC,aAAa,EAAE;MACnB,CAAC;MACD6E,MAAM,EAAE;QACJ/E,MAAM,EAAEV,MAAM,CAACE,IAAI;QACnBS,GAAG,EAAE,qBAAqB;QAC1BC,aAAa,EAAE;MACnB,CAAC;MACD8E,IAAI,EAAGU,aAAqB,KAAM;QAC9B1F,MAAM,EAAEV,MAAM,CAACG,GAAG;QAClBQ,GAAG,EAAE,gBAAgByF,aAAa,EAAE;QACpCxF,aAAa,EAAE;MACnB,CAAC,CAAC;MACFgF,MAAM,EAAGQ,aAAqB,KAAM;QAChC1F,MAAM,EAAEV,MAAM,CAACI,MAAM;QACrBO,GAAG,EAAE,gBAAgByF,aAAa,EAAE;QACpCxF,aAAa,EAAE;MACnB,CAAC;IACL;EACJ,CAAC;EACDyF,iBAAiB,EAAE;IACfb,MAAM,EAAE;MACJ9E,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,2BAA2B;MAChCC,aAAa,EAAE;IACnB,CAAC;IACD8E,IAAI,EAAGY,QAAgB,KAAM;MACzB5F,MAAM,EAAEV,MAAM,CAACG,GAAG;MAClBQ,GAAG,EAAE,4BAA4B2F,QAAQ,EAAE;MAC3C1F,aAAa,EAAE;IACnB,CAAC;EACL,CAAC;EACD2F,KAAK,EAAE;IACHf,MAAM,EAAE;MACJ9E,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,cAAc;MACnBC,aAAa,EAAE;IACnB,CAAC;IACD6E,MAAM,EAAE;MACJ/E,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,cAAc;MACnBC,aAAa,EAAE;IACnB,CAAC;IACD8E,IAAI,EAAGc,OAAe,KAAM;MACxB9F,MAAM,EAAEV,MAAM,CAACG,GAAG;MAClBQ,GAAG,EAAE,SAAS6F,OAAO,EAAE;MACvB5F,aAAa,EAAE;IACnB,CAAC,CAAC;IACFgF,MAAM,EAAGY,OAAe,KAAM;MAC1B9F,MAAM,EAAEV,MAAM,CAACI,MAAM;MACrBO,GAAG,EAAE,SAAS6F,OAAO,EAAE;MACvB5F,aAAa,EAAE;IACnB,CAAC;EACL,CAAC;EACD6F,aAAa,EAAE;IACXpC,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,2BAA2B;MAChCC,aAAa,EAAE;IACnB,CAAC;IACD8F,4BAA4B,EAAE;MAC1BhG,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,sBAAsB;MAC3BC,aAAa,EAAE;IACnB;EACJ,CAAC;EACD+F,aAAa,EAAE;IACXtC,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,wBAAwB;MAC7BC,aAAa,EAAE;IACnB,CAAC;IACDgG,gBAAgB,EAAE;MACdlG,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,2BAA2B;MAChCC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDiG,YAAY,EAAE;IACVxC,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,uBAAuB;MAC5BC,aAAa,EAAE;IACnB,CAAC;IACDkG,2BAA2B,EAAE;MACzBpG,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,oBAAoB;MACzBC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDmG,oBAAoB,EAAE;IAClB1C,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,aAAa;MAClBC,aAAa,EAAE;IACnB,CAAC;IACDoG,wBAAwB,EAAE;MACtBtG,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,qBAAqB;MAC1BC,aAAa,EAAE;IACnB,CAAC;IACDqG,gBAAgB,EAAE;MACdvG,MAAM,EAAEV,MAAM,CAACI,MAAM;MACrBO,GAAG,EAAE,aAAa;MAClBC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDsG,kBAAkB,EAAE;IAChB7C,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,iBAAiB;MACtBC,aAAa,EAAE;IACnB,CAAC;IACDoG,wBAAwB,EAAE;MACtBtG,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,uBAAuB;MAC5BC,aAAa,EAAE;IACnB,CAAC;IACDuG,cAAc,EAAE;MACZzG,MAAM,EAAEV,MAAM,CAACI,MAAM;MACrBO,GAAG,EAAE,iBAAiB;MACtBC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDwG,mBAAmB,EAAE;IACjB/C,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,mBAAmB;MACxBC,aAAa,EAAE;IACnB,CAAC;IACDoG,wBAAwB,EAAE;MACtBtG,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,yBAAyB;MAC9BC,aAAa,EAAE;IACnB,CAAC;IACDyG,eAAe,EAAE;MACb3G,MAAM,EAAEV,MAAM,CAACI,MAAM;MACrBO,GAAG,EAAE,mBAAmB;MACxBC,aAAa,EAAE;IACnB;EACJ,CAAC;EACD0G,oBAAoB,EAAE;IAClBjD,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,eAAe;MACpBC,aAAa,EAAE;IACnB,CAAC;IACD2G,kCAAkC,EAAE;MAChC7G,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,4BAA4B;MACjCC,aAAa,EAAE;IACnB,CAAC;IACD4G,kBAAkB,EAAE;MAChB9G,MAAM,EAAEV,MAAM,CAACI,MAAM;MACrBO,GAAG,EAAE,sBAAsB;MAC3BC,aAAa,EAAE;IACnB;EACJ,CAAC;EACD6G,OAAO,EAAE;IACLpD,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,gBAAgB;MACrBC,aAAa,EAAE;IACnB,CAAC;IACD8G,uBAAuB,EAAE;MACrBhH,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,sBAAsB;MAC3BC,aAAa,EAAE;IACnB,CAAC;IACDgF,MAAM,EAAE;MACJlF,MAAM,EAAEV,MAAM,CAACI,MAAM;MACrBO,GAAG,EAAE,gBAAgB;MACrBC,aAAa,EAAE;IACnB;EACJ,CAAC;EACD+G,IAAI,EAAE;IACFtD,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,cAAc;MACnBC,aAAa,EAAE;IACnB,CAAC;IACDgH,cAAc,EAAE;MACZlH,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,yBAAyB;MAC9BC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDiH,KAAK,EAAE;IACHxD,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,mBAAmB;MACxBC,aAAa,EAAE;IACnB,CAAC;IACDyE,gBAAgB,EAAE;MACd3E,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,oBAAoB;MACzBC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDkH,iBAAiB,EAAE;IACfzD,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,2BAA2B;MAChCC,aAAa,EAAE;IACnB,CAAC;IACDkF,SAAS,EAAE;MACPpF,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,qCAAqC;MAC1CC,aAAa,EAAE;IACnB,CAAC;IACDmH,kBAAkB,EAAE;MAChBrH,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,0CAA0C;MAC/CC,aAAa,EAAE;IACnB,CAAC;IACDmC,WAAW,EAAE;MACTrC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,oCAAoC;MACzCC,aAAa,EAAE;IACnB,CAAC;IACDoH,mBAAmB,EAAE;MACjBtH,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,2CAA2C;MAChDC,aAAa,EAAE;IACnB,CAAC;IACDqH,yBAAyB,EAAE;MACvBvH,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,yCAAyC;MAC9CC,aAAa,EAAE;IACnB,CAAC;IACDsH,WAAW,EAAE;MACTxH,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,mCAAmC;MACxCC,aAAa,EAAE;IACnB,CAAC;IACDuH,qBAAqB,EAAE;MACnBzH,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,8CAA8C;MACnDC,aAAa,EAAE;IACnB,CAAC;IACDwH,eAAe,EAAE;MACb1H,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,wCAAwC;MAC7CC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDyH,OAAO,EAAE;IACLC,cAAc,EAAE;MACZ5H,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,qBAAqB;MAC1BC,aAAa,EAAE;IACnB,CAAC;IACD2H,uBAAuB,EAAE;MACrB7H,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,sBAAsB;MAC3BC,aAAa,EAAE;IACnB,CAAC;IACD4H,oBAAoB,EAAE;MAClB9H,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,2BAA2B;MAChCC,aAAa,EAAE;IACnB,CAAC;IACD6H,6BAA6B,EAAE;MAC3B/H,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,4BAA4B;MACjCC,aAAa,EAAE;IACnB;EACJ,CAAC;EACD8H,SAAS,EAAE;IACPC,aAAa,EAAE;MACXjI,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,4BAA4B;MACjCC,aAAa,EAAE;IACnB,CAAC;IACDgI,cAAc,EAAE;MACZlI,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,4BAA4B;MACjCC,aAAa,EAAE;IACnB,CAAC;IACDiI,0BAA0B,EAAE;MACxBnI,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,oCAAoC;MACzCC,aAAa,EAAE;IACnB,CAAC;IACDkI,cAAc,EAAGtF,EAAU,KAAM;MAC7B9C,MAAM,EAAEV,MAAM,CAACI,MAAM;MACrBO,GAAG,EAAE,iCAAiC6C,EAAE,EAAE;MAC1C5C,aAAa,EAAE;IACnB,CAAC,CAAC;IACFmC,WAAW,EAAE;MACTrC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,qBAAqB;MAC1BC,aAAa,EAAE;IACnB,CAAC;IACDmI,0BAA0B,EAAE;MACxBrI,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,oCAAoC;MACzCC,aAAa,EAAE;IACnB,CAAC;IACDoI,cAAc,EAAGxF,EAAU,KAAM;MAC7B9C,MAAM,EAAEV,MAAM,CAACI,MAAM;MACrBO,GAAG,EAAE,iCAAiC6C,EAAE,EAAE;MAC1C5C,aAAa,EAAE;IACnB,CAAC,CAAC;IACFqI,cAAc,EAAE;MACZvI,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,eAAe;MACpBC,aAAa,EAAE;IACnB,CAAC;IACDsI,uBAAuB,EAAE;MACrBxI,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,gBAAgB;MACrBC,aAAa,EAAE;IACnB,CAAC;IACDuI,eAAe,EAAE;MACbzI,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,qBAAqB;MAC1BC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDwI,gBAAgB,EAAE;IACd/E,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,gCAAgC;MACrCC,aAAa,EAAE;IACnB,CAAC;IACDyI,OAAO,EAAE;MACL3I,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,iCAAiC;MACtCC,aAAa,EAAE;IACnB,CAAC;IACDyE,gBAAgB,EAAE;MACd3E,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,uBAAuB;MAC5BC,aAAa,EAAE;IACnB,CAAC;IACDmC,WAAW,EAAE;MACTrC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,mBAAmB;MACxBC,aAAa,EAAE;IACnB,CAAC;IACD0I,gBAAgB,EAAE;MACd5I,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,uBAAuB;MAC5BC,aAAa,EAAE;IACnB,CAAC;IACD2I,cAAc,EAAE;MACZ7I,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,wBAAwB;MAC7BC,aAAa,EAAE;IACnB,CAAC;IACD4I,qBAAqB,EAAE;MACnB9I,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,8BAA8B;MACnCC,aAAa,EAAE;IACnB,CAAC;IACD6I,qBAAqB,EAAE;MACnB/I,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,8BAA8B;MACnCC,aAAa,EAAE;IACnB,CAAC;IACD8I,UAAU,EAAE;MACRhJ,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,iBAAiB;MACtBC,aAAa,EAAE;IACnB,CAAC;IACD+I,aAAa,EAAE;MACXjJ,MAAM,EAAEV,MAAM,CAACG,GAAG;MAClBQ,GAAG,EAAE,wBAAwB;MAC7BC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDgJ,aAAa,EAAE;IACXvF,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,QAAQ;MACbC,aAAa,EAAE;IACnB,CAAC;IACDiJ,cAAc,EAAGC,WAAmB,KAAM;MACtCpJ,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,iBAAiBmJ,WAAW,EAAE;MACnClJ,aAAa,EAAE;IACnB,CAAC,CAAC;IACFmJ,aAAa,EAAE;MACXrJ,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,oBAAoB;MACzBC,aAAa,EAAE;IACnB,CAAC;IACDoJ,eAAe,EAAGF,WAAmB,IAAK;MACtC,OAAO;QAAEpJ,MAAM,EAAEV,MAAM,CAACC,GAAG;QAAEU,GAAG,EAAE,iBAAiBmJ,WAAW,EAAE;QAAElJ,aAAa,EAAE;MAAM,CAAC;IAC5F,CAAC;IACDqJ,WAAW,EAAGH,WAAmB,IAAK;MAClC,OAAO;QACHpJ,MAAM,EAAEV,MAAM,CAACG,GAAG;QAClBQ,GAAG,EAAE,mBAAmBmJ,WAAW,EAAE;QACrClJ,aAAa,EAAE;MACnB,CAAC;IACL,CAAC;IACDsJ,SAAS,EAAE;MACPxJ,MAAM,EAAEV,MAAM,CAACG,GAAG;MAClBQ,GAAG,EAAE,eAAe;MACpBC,aAAa,EAAE;IACnB,CAAC;IACDuJ,UAAU,EAAE;MACRzJ,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,QAAQ;MACbC,aAAa,EAAE;IACnB,CAAC;IACDwJ,SAAS,EAAGN,WAAmB,IAAK;MAChC,OAAO;QACHpJ,MAAM,EAAEV,MAAM,CAACG,GAAG;QAClBQ,GAAG,EAAE,UAAUmJ,WAAW,EAAE;QAC5BlJ,aAAa,EAAE;MACnB,CAAC;IACL,CAAC;IACDyJ,WAAW,EAAGP,WAAmB,IAAK;MAClC,OAAO;QACHpJ,MAAM,EAAEV,MAAM,CAACI,MAAM;QACrBO,GAAG,EAAE,iBAAiBmJ,WAAW,EAAE;QACnClJ,aAAa,EAAE;MACnB,CAAC;IACL,CAAC;IACD0J,aAAa,EAAGR,WAAmB,IAAK;MACpC,OAAO;QACHpJ,MAAM,EAAEV,MAAM,CAACC,GAAG;QAClBU,GAAG,EAAE,mBAAmBmJ,WAAW,EAAE;QACrClJ,aAAa,EAAE;MACnB,CAAC;IACL;EACJ,CAAC;EACD2J,SAAS,EAAE;IACPlG,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,uBAAuB;MAC5BC,aAAa,EAAE;IACnB,CAAC;IACDsJ,SAAS,EAAE;MACPxJ,MAAM,EAAEV,MAAM,CAACG,GAAG;MAClBQ,GAAG,EAAE,uBAAuB;MAC5BC,aAAa,EAAE;IACnB,CAAC;IACD4J,QAAQ,EAAGV,WAAmB,IAAK;MAC/B,OAAO;QACHpJ,MAAM,EAAEV,MAAM,CAACI,MAAM;QACrBO,GAAG,EAAE,yBAAyBmJ,WAAW,EAAE;QAC3ClJ,aAAa,EAAE;MACnB,CAAC;IACL;EACJ,CAAC;EACD6J,kBAAkB,EAAE;IAChBpG,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,aAAa;MAClBC,aAAa,EAAE;IACnB,CAAC;IACDqJ,WAAW,EAAGH,WAAmB,IAAK;MAClC,OAAO;QACHpJ,MAAM,EAAEV,MAAM,CAACG,GAAG;QAClBQ,GAAG,EAAE,wBAAwBmJ,WAAW,EAAE;QAC1ClJ,aAAa,EAAE;MACnB,CAAC;IACL,CAAC;IACDuJ,UAAU,EAAE;MACRzJ,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,aAAa;MAClBC,aAAa,EAAE;IACnB,CAAC;IACDwJ,SAAS,EAAGN,WAAmB,IAAK;MAChC,OAAO;QACHpJ,MAAM,EAAEV,MAAM,CAACG,GAAG;QAClBQ,GAAG,EAAE,eAAemJ,WAAW,EAAE;QACjClJ,aAAa,EAAE;MACnB,CAAC;IACL;EACJ,CAAC;EACD8J,aAAa,EAAE;IACXC,WAAW,EAAE;MACTjK,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,8BAA8B;MACnCC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDgK,sBAAsB,EAAE;IACpBvG,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,eAAe;MACpBC,aAAa,EAAE;IACnB,CAAC;IACDoF,YAAY,EAAE;MACVtF,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,4BAA4B;MACjCC,aAAa,EAAE;IACnB,CAAC;IACDyH,OAAO,EAAE;MACL3H,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,6BAA6B;MAClCC,aAAa,EAAE;IACnB,CAAC;IACDiK,QAAQ,EAAE;MACNnK,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,6BAA6B;MAClCC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDkK,sBAAsB,EAAE;IACpBC,UAAU,EAAE;MACRrK,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,6BAA6B;MAClCC,aAAa,EAAE;IACnB,CAAC;IACDoK,kBAAkB,EAAE;MAChBtK,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,qCAAqC;MAC1CC,aAAa,EAAE;IACnB,CAAC;IACDqK,cAAc,EAAE;MACZvK,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,oCAAoC;MACzCC,aAAa,EAAE;IACnB,CAAC;IACDsK,oBAAoB,EAAE;MAClBxK,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,4CAA4C;MACjDC,aAAa,EAAE;IACnB,CAAC;IACDuK,gBAAgB,EAAE;MACdzK,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,qCAAqC;MAC1CC,aAAa,EAAE;IACnB,CAAC;IACDwK,aAAa,EAAE;MACX1K,MAAM,EAAEV,MAAM,CAACI,MAAM;MACrBO,GAAG,EAAE,6BAA6B;MAClCC,aAAa,EAAE;IACnB,CAAC;IACDyH,OAAO,EAAE;MACL3H,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,2CAA2C;MAChDC,aAAa,EAAE;IACnB,CAAC;IACDiK,QAAQ,EAAE;MACNnK,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,mCAAmC;MACxCC,aAAa,EAAE;IACnB,CAAC;IACDiK,QAAQ,EAAE;MACNnK,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,mCAAmC;MACxCC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDyK,cAAc,EAAE;IACZhH,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,sBAAsB;MAC3BC,aAAa,EAAE;IACnB,CAAC;IACD0K,iBAAiB,EAAE;MACf5K,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,mCAAmC;MACxCC,aAAa,EAAE;IACnB,CAAC;IACDiK,QAAQ,EAAE;MACNnK,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,2CAA2C;MAChDC,aAAa,EAAE;IACnB,CAAC;IACDiK,QAAQ,EAAE;MACNnK,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,2CAA2C;MAChDC,aAAa,EAAE;IACnB;EACJ,CAAC;EACD2K,aAAa,EAAE;IACXC,SAAS,EAAE;MACP9K,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,cAAc;MACnBC,aAAa,EAAE;IACnB,CAAC;IACDmC,WAAW,EAAE;MACTrC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,uBAAuB;MAC5BC,aAAa,EAAE;IACnB,CAAC;IACD6K,aAAa,EAAE;MACX/K,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,iBAAiB;MACtBC,aAAa,EAAE;IACnB,CAAC;IACD8K,UAAU,EAAE;MACRhL,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE;IACT,CAAC;IACDgL,eAAe,EAAE;MACbjL,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,oBAAoB;MACzBC,aAAa,EAAE;IACnB,CAAC;IACDgL,WAAW,EAAE;MACTlL,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,8BAA8B;MACnCC,aAAa,EAAE;IACnB,CAAC;IACDiL,aAAa,EAAE;MACXnL,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,+BAA+B;MACpCC,aAAa,EAAE;IACnB,CAAC;IACDkL,eAAe,EAAE;MACbpL,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,kCAAkC;MACvCC,aAAa,EAAE;IACnB,CAAC;IACDmL,oBAAoB,EAAE;MAClBrL,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,gCAAgC;MACrCC,aAAa,EAAE;IACnB,CAAC;IACDoL,kBAAkB,EAAE;MAChBtL,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,2BAA2B;MAChCC,aAAa,EAAE;IACnB,CAAC;IACDqL,QAAQ,EAAE;MACNvL,MAAM,EAAEV,MAAM,CAACK,KAAK;MACpBM,GAAG,EAAE,2BAA2B;MAChCC,aAAa,EAAE;IACnB,CAAC;IACDsL,OAAO,EAAE;MACLxL,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,4BAA4B;MACjCC,aAAa,EAAE;IACnB,CAAC;IACDuL,UAAU,EAAE;MACRzL,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,6BAA6B;MAClCC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDwL,OAAO,EAAE;IACLC,uBAAuB,EAAE;MACrB3L,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,qCAAqC;MAC1CC,aAAa,EAAE;IACnB,CAAC;IACD4K,SAAS,EAAE;MACP9K,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,uBAAuB;MAC5BC,aAAa,EAAE;IACnB,CAAC;IACD0L,oBAAoB,EAAGC,SAAiB,KAAM;MAC1C7L,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,yBAAyB4L,SAAS,EAAE;MACzC3L,aAAa,EAAE;IACnB,CAAC;EACL,CAAC;EACD4L,SAAS,EAAE;IACPnI,MAAM,EAAE;MACJ3D,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,kBAAkB;MACvBC,aAAa,EAAE;IACnB,CAAC;IACD6L,gBAAgB,EAAG3C,WAAmB,KAAM;MACxCpJ,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,oBAAoBmJ,WAAW,EAAE;MACtClJ,aAAa,EAAE;IACnB,CAAC,CAAC;IACFyE,gBAAgB,EAAE;MACd3E,MAAM,EAAEV,MAAM,CAACG,GAAG;MAClBQ,GAAG,EAAE,kBAAkB;MACvBC,aAAa,EAAE;IACnB;EACJ,CAAC;EACD8L,eAAe,EAAE;IACbC,kBAAkB,EAAE;MAChBjM,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,qCAAqC;MAC1CC,aAAa,EAAE;IACnB,CAAC;IACDmC,WAAW,EAAE;MACTrC,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,yBAAyB;MAC9BC,aAAa,EAAE,KAAK;MACpBgM,YAAY,EAAE;IAClB,CAAC;IACDC,gBAAgB,EAAE;MACdnM,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,kCAAkC;MACvCC,aAAa,EAAE;IACnB,CAAC;IACDkM,+BAA+B,EAAE;MAC7BpM,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,qCAAqC;MAC1CC,aAAa,EAAE;IACnB,CAAC;IACDmM,uBAAuB,EAAGvJ,EAAU,KAAM;MACtC9C,MAAM,EAAEV,MAAM,CAACG,GAAG;MAClBQ,GAAG,EAAE,uCAAuC6C,EAAE,EAAE;MAChD5C,aAAa,EAAE;IACnB,CAAC,CAAC;IACFoM,eAAe,EAAGC,KAAa,KAAM;MACjCvM,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,8CAA8CsM,KAAK,EAAE;MAC1DrM,aAAa,EAAE;IACnB,CAAC;EACL,CAAC;EACDsM,eAAe,EAAE;IACbC,GAAG,EAAE;MACDzM,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,kBAAkB;MACvBC,aAAa,EAAE;IACnB,CAAC;IACDwM,aAAa,EAAE;MACX1M,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,yBAAyB;MAC9BC,aAAa,EAAE;IACnB,CAAC;IACDyM,iBAAiB,EAAE;MACf3M,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,mCAAmC;MACxCC,aAAa,EAAE;IACnB,CAAC;IACD0M,YAAY,EAAE;MACV5M,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,gCAAgC;MACrCC,aAAa,EAAE;IACnB,CAAC;IACD2M,UAAU,EAAG/J,EAAU,KAAM;MACzB9C,MAAM,EAAEV,MAAM,CAACG,GAAG;MAClBQ,GAAG,EAAE,kCAAkC6C,EAAE,EAAE;MAC3C5C,aAAa,EAAE;IACnB,CAAC,CAAC;IACF4M,YAAY,EAAGhK,EAAU,KAAM;MAC3B9C,MAAM,EAAEV,MAAM,CAACI,MAAM;MACrBO,GAAG,EAAE,2BAA2B6C,EAAE,EAAE;MACpC5C,aAAa,EAAE;IACnB,CAAC,CAAC;IACF6M,mBAAmB,EAAE;MACjB/M,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,WAAW;MAChBC,aAAa,EAAE;IACnB,CAAC;IACD8M,eAAe,EAAE;MACbhN,MAAM,EAAEV,MAAM,CAACG,GAAG;MAClBQ,GAAG,EAAE,8BAA8B;MACnCC,aAAa,EAAE;IACnB;EACJ,CAAC;EACD+M,gBAAgB,EAAE;IACdR,GAAG,EAAE;MACDzM,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,2BAA2B;MAChCC,aAAa,EAAE;IACnB,CAAC;IACD8E,IAAI,EAAE;MACFhF,MAAM,EAAEV,MAAM,CAACG,GAAG;MAClBQ,GAAG,EAAE,mBAAmB;MACxBC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDgN,mBAAmB,EAAE;IACjBT,GAAG,EAAE;MACDzM,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,+BAA+B;MACpCC,aAAa,EAAE;IACnB,CAAC;IACD8E,IAAI,EAAE;MACFhF,MAAM,EAAEV,MAAM,CAACG,GAAG;MAClBQ,GAAG,EAAE,8BAA8B;MACnCC,aAAa,EAAE;IACnB,CAAC;IACDiN,gBAAgB,EAAE;MACdnN,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,0CAA0C;MAC/CC,aAAa,EAAE;IACnB,CAAC;IACDkN,WAAW,EAAE;MACTpN,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,oBAAoB;MACzBC,aAAa,EAAE;IACnB,CAAC;IACDmN,WAAW,EAAE;MACTrN,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,2BAA2B;MAChCC,aAAa,EAAE;IACnB,CAAC;IACDuG,cAAc,EAAG3D,EAAU,KAAM;MAC7B9C,MAAM,EAAEV,MAAM,CAACI,MAAM;MACrBO,GAAG,EAAE,sBAAsB6C,EAAE,EAAE;MAC/B5C,aAAa,EAAE;IACnB,CAAC;EACL,CAAC;EACDoN,eAAe,EAAE;IACbC,kBAAkB,EAAE;MAChBvN,MAAM,EAAEV,MAAM,CAACE,IAAI;MACnBS,GAAG,EAAE,gBAAgB;MACrBC,aAAa,EAAE;IACnB,CAAC;IACDsN,uBAAuB,EAAGpE,WAAmB,KAAM;MAC/CpJ,MAAM,EAAEV,MAAM,CAACC,GAAG;MAClBU,GAAG,EAAE,yBAAyBmJ,WAAW,EAAE;MAC3ClJ,aAAa,EAAE;IACnB,CAAC,CAAC;IACFuN,0BAA0B,EAAGrE,WAAmB,KAAM;MAClDpJ,MAAM,EAAEV,MAAM,CAACG,GAAG;MAClBQ,GAAG,EAAE,yBAAyBmJ,WAAW,EAAE;MAC3ClJ,aAAa,EAAE;IACnB,CAAC,CAAC;IACFwN,sBAAsB,EAAGtE,WAAmB,KAAM;MAC9CpJ,MAAM,EAAEV,MAAM,CAACG,GAAG;MAClBQ,GAAG,EAAE,2BAA2BmJ,WAAW,EAAE;MAC7ClJ,aAAa,EAAE;IACnB,CAAC;EACL;AACJ,CAAC;AAED,eAAeN,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}