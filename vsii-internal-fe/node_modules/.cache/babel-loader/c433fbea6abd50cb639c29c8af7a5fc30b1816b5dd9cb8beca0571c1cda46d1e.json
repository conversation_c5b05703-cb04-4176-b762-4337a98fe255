{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"component\", \"columns\", \"spacing\", \"defaultColumns\", \"defaultHeight\", \"defaultSpacing\"];\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { flushSync } from 'react-dom';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { createUnarySpacing, getValue, handleBreakpoints, unstable_resolveBreakpointValues as resolveBreakpointValues } from '@mui/system';\nimport { deepmerge, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport { getMasonryUtilityClass } from './masonryClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const parseToNumber = val => {\n  return Number(val.replace('px', ''));\n};\nconst lineBreakStyle = {\n  flexBasis: '100%',\n  width: 0,\n  margin: 0,\n  padding: 0\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getMasonryUtilityClass, classes);\n};\nexport const getStyle = ({\n  ownerState,\n  theme\n}) => {\n  let styles = {\n    width: '100%',\n    display: 'flex',\n    flexFlow: 'column wrap',\n    alignContent: 'flex-start',\n    boxSizing: 'border-box',\n    '& > *': {\n      boxSizing: 'border-box'\n    }\n  };\n  const stylesSSR = {}; // Only applicable for Server-Side Rendering\n\n  if (ownerState.isSSR) {\n    const orderStyleSSR = {};\n    const defaultSpacing = parseToNumber(theme.spacing(ownerState.defaultSpacing));\n    for (let i = 1; i <= ownerState.defaultColumns; i += 1) {\n      orderStyleSSR[`&:nth-of-type(${ownerState.defaultColumns}n+${i % ownerState.defaultColumns})`] = {\n        order: i\n      };\n    }\n    stylesSSR.height = ownerState.defaultHeight;\n    stylesSSR.margin = -(defaultSpacing / 2);\n    stylesSSR['& > *'] = _extends({}, styles['& > *'], orderStyleSSR, {\n      margin: defaultSpacing / 2,\n      width: `calc(${(100 / ownerState.defaultColumns).toFixed(2)}% - ${defaultSpacing}px)`\n    });\n    return _extends({}, styles, stylesSSR);\n  }\n  const spacingValues = resolveBreakpointValues({\n    values: ownerState.spacing,\n    breakpoints: theme.breakpoints.values\n  });\n  const transformer = createUnarySpacing(theme);\n  const spacingStyleFromPropValue = propValue => {\n    let spacing; // in case of string/number value\n\n    if (typeof propValue === 'string' && !Number.isNaN(Number(propValue)) || typeof propValue === 'number') {\n      const themeSpacingValue = Number(propValue);\n      spacing = getValue(transformer, themeSpacingValue);\n    } else {\n      spacing = propValue;\n    }\n    return _extends({\n      margin: `calc(0px - (${spacing} / 2))`,\n      '& > *': {\n        margin: `calc(${spacing} / 2)`\n      }\n    }, ownerState.maxColumnHeight && {\n      height: typeof spacing === 'number' ? Math.ceil(ownerState.maxColumnHeight + parseToNumber(spacing)) : `calc(${ownerState.maxColumnHeight}px + ${spacing})`\n    });\n  };\n  styles = deepmerge(styles, handleBreakpoints({\n    theme\n  }, spacingValues, spacingStyleFromPropValue));\n  const columnValues = resolveBreakpointValues({\n    values: ownerState.columns,\n    breakpoints: theme.breakpoints.values\n  });\n  const columnStyleFromPropValue = propValue => {\n    const columnValue = Number(propValue);\n    const width = `${(100 / columnValue).toFixed(2)}%`;\n    const spacing = typeof spacingValues === 'string' && !Number.isNaN(Number(spacingValues)) || typeof spacingValues === 'number' ? getValue(transformer, Number(spacingValues)) : '0px';\n    return {\n      '& > *': {\n        width: `calc(${width} - ${spacing})`\n      }\n    };\n  };\n  styles = deepmerge(styles, handleBreakpoints({\n    theme\n  }, columnValues, columnStyleFromPropValue)); // configure width for responsive spacing values\n\n  if (typeof spacingValues === 'object') {\n    styles = deepmerge(styles, handleBreakpoints({\n      theme\n    }, spacingValues, (propValue, breakpoint) => {\n      if (breakpoint) {\n        const themeSpacingValue = Number(propValue);\n        const lastBreakpoint = Object.keys(columnValues).pop();\n        const spacing = getValue(transformer, themeSpacingValue);\n        const column = typeof columnValues === 'object' ? columnValues[breakpoint] || columnValues[lastBreakpoint] : columnValues;\n        const width = `${(100 / column).toFixed(2)}%`;\n        return {\n          '& > *': {\n            width: `calc(${width} - ${spacing})`\n          }\n        };\n      }\n      return null;\n    }));\n  }\n  return styles;\n};\nconst MasonryRoot = styled('div', {\n  name: 'MuiMasonry',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    return [styles.root];\n  }\n})(getStyle);\nconst Masonry = /*#__PURE__*/React.forwardRef(function Masonry(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiMasonry'\n  });\n  const {\n      children,\n      className,\n      component = 'div',\n      columns = 4,\n      spacing = 1,\n      defaultColumns,\n      defaultHeight,\n      defaultSpacing\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const masonryRef = React.useRef();\n  const [maxColumnHeight, setMaxColumnHeight] = React.useState();\n  const isSSR = !maxColumnHeight && defaultHeight && defaultColumns !== undefined && defaultSpacing !== undefined;\n  const [numberOfLineBreaks, setNumberOfLineBreaks] = React.useState(isSSR ? defaultColumns - 1 : 0);\n  const ownerState = _extends({}, props, {\n    spacing,\n    columns,\n    maxColumnHeight,\n    defaultColumns,\n    defaultHeight,\n    defaultSpacing,\n    isSSR\n  });\n  const classes = useUtilityClasses(ownerState);\n  const handleResize = masonryChildren => {\n    if (!masonryRef.current || !masonryChildren || masonryChildren.length === 0) {\n      return;\n    }\n    const masonry = masonryRef.current;\n    const masonryFirstChild = masonryRef.current.firstChild;\n    const parentWidth = masonry.clientWidth;\n    const firstChildWidth = masonryFirstChild.clientWidth;\n    if (parentWidth === 0 || firstChildWidth === 0) {\n      return;\n    }\n    const firstChildComputedStyle = window.getComputedStyle(masonryFirstChild);\n    const firstChildMarginLeft = parseToNumber(firstChildComputedStyle.marginLeft);\n    const firstChildMarginRight = parseToNumber(firstChildComputedStyle.marginRight);\n    const currentNumberOfColumns = Math.round(parentWidth / (firstChildWidth + firstChildMarginLeft + firstChildMarginRight));\n    const columnHeights = new Array(currentNumberOfColumns).fill(0);\n    let skip = false;\n    masonry.childNodes.forEach(child => {\n      if (child.nodeType !== Node.ELEMENT_NODE || child.dataset.class === 'line-break' || skip) {\n        return;\n      }\n      const childComputedStyle = window.getComputedStyle(child);\n      const childMarginTop = parseToNumber(childComputedStyle.marginTop);\n      const childMarginBottom = parseToNumber(childComputedStyle.marginBottom); // if any one of children isn't rendered yet, masonry's height shouldn't be computed yet\n\n      const childHeight = parseToNumber(childComputedStyle.height) ? Math.ceil(parseToNumber(childComputedStyle.height)) + childMarginTop + childMarginBottom : 0;\n      if (childHeight === 0) {\n        skip = true;\n        return;\n      } // if there is a nested image that isn't rendered yet, masonry's height shouldn't be computed yet\n\n      for (let i = 0; i < child.childNodes.length; i += 1) {\n        const nestedChild = child.childNodes[i];\n        if (nestedChild.tagName === 'IMG' && nestedChild.clientHeight === 0) {\n          skip = true;\n          break;\n        }\n      }\n      if (!skip) {\n        // find the current shortest column (where the current item will be placed)\n        const currentMinColumnIndex = columnHeights.indexOf(Math.min(...columnHeights));\n        columnHeights[currentMinColumnIndex] += childHeight;\n        const order = currentMinColumnIndex + 1;\n        child.style.order = order;\n      }\n    });\n    if (!skip) {\n      // In React 18, state updates in a ResizeObserver's callback are happening after the paint which causes flickering\n      // when doing some visual updates in it. Using flushSync ensures that the dom will be painted after the states updates happen\n      // Related issue - https://github.com/facebook/react/issues/24331\n      flushSync(() => {\n        setMaxColumnHeight(Math.max(...columnHeights));\n        setNumberOfLineBreaks(currentNumberOfColumns > 0 ? currentNumberOfColumns - 1 : 0);\n      });\n    }\n  };\n  const observer = React.useRef(typeof ResizeObserver === 'undefined' ? undefined : new ResizeObserver(handleResize));\n  React.useEffect(() => {\n    const resizeObserver = observer.current; // IE and old browsers are not supported\n\n    if (resizeObserver === undefined) {\n      return undefined;\n    }\n    if (masonryRef.current) {\n      masonryRef.current.childNodes.forEach(childNode => {\n        resizeObserver.observe(childNode);\n      });\n    }\n    return () => resizeObserver ? resizeObserver.disconnect() : {};\n  }, [columns, spacing, children]);\n  const handleRef = useForkRef(ref, masonryRef); //  columns are likely to have different heights and hence can start to merge;\n  //  a line break at the end of each column prevents columns from merging\n\n  const lineBreaks = new Array(numberOfLineBreaks).fill('').map((_, index) => /*#__PURE__*/_jsx(\"span\", {\n    \"data-class\": \"line-break\",\n    style: _extends({}, lineBreakStyle, {\n      order: index + 1\n    })\n  }, index));\n  return /*#__PURE__*/_jsxs(MasonryRoot, _extends({\n    as: component,\n    className: clsx(classes.root, className),\n    ref: handleRef,\n    ownerState: ownerState\n  }, other, {\n    children: [children, lineBreaks]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Masonry.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n\n  /**\n   * The content of the component.\n   */\n  children: PropTypes\n  /* @typescript-to-proptypes-ignore */.node.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Number of columns.\n   * @default 4\n   */\n  columns: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The default number of columns of the component. This is provided for server-side rendering.\n   */\n  defaultColumns: PropTypes.number,\n  /**\n   * The default height of the component in px. This is provided for server-side rendering.\n   */\n  defaultHeight: PropTypes.number,\n  /**\n   * The default spacing of the component. Like `spacing`, it is a factor of the theme's spacing. This is provided for server-side rendering.\n   */\n  defaultSpacing: PropTypes.number,\n  /**\n   * Defines the space between children. It is a factor of the theme's spacing.\n   * @default 1\n   */\n  spacing: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * Allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Masonry;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}