{"ast": null, "code": "import * as React from 'react';\nexport const useFocusManagement = _ref => {\n  let {\n    autoFocus,\n    openView\n  } = _ref;\n  const [focusedView, setFocusedView] = React.useState(autoFocus ? openView : null);\n  const setFocusedViewCallback = React.useCallback(view => newHasFocus => {\n    if (newHasFocus) {\n      setFocusedView(view);\n    } else {\n      setFocusedView(prevFocusedView => view === prevFocusedView ? null : prevFocusedView);\n    }\n  }, []);\n  return {\n    focusedView,\n    setFocusedView: setFocusedViewCallback\n  };\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}