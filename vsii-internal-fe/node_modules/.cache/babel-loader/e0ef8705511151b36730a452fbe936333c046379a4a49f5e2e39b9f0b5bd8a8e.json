{"ast": null, "code": "/**\n * https://tc39.es/ecma402/#sec-canonicalizetimezonename\n * @param tz\n */\nexport function CanonicalizeTimeZoneName(tz, _a) {\n  var zoneNames = _a.zoneNames,\n    uppercaseLinks = _a.uppercaseLinks;\n  var uppercasedTz = tz.toUpperCase();\n  var uppercasedZones = zoneNames.reduce(function (all, z) {\n    all[z.toUpperCase()] = z;\n    return all;\n  }, {});\n  var ianaTimeZone = uppercaseLinks[uppercasedTz] || uppercasedZones[uppercasedTz];\n  if (ianaTimeZone === 'Etc/UTC' || ianaTimeZone === 'Etc/GMT') {\n    return 'UTC';\n  }\n  return ianaTimeZone;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}