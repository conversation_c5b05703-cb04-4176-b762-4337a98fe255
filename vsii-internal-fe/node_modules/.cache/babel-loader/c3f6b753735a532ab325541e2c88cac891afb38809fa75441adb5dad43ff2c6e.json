{"ast": null, "code": "var rafSchd = function rafSchd(fn) {\n  var lastArgs = [];\n  var frameId = null;\n  var wrapperFn = function wrapperFn() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    lastArgs = args;\n    if (frameId) {\n      return;\n    }\n    frameId = requestAnimationFrame(function () {\n      frameId = null;\n      fn.apply(void 0, lastArgs);\n    });\n  };\n  wrapperFn.cancel = function () {\n    if (!frameId) {\n      return;\n    }\n    cancelAnimationFrame(frameId);\n    frameId = null;\n  };\n  return wrapperFn;\n};\nexport default rafSchd;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}