{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/pages/administration/TitleConfig.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport AddOrEditTitleConfig from 'containers/administration/AddOrEditTitleConfig';\nimport { TitleConfigTBody, TitleConfigTHead } from 'containers/administration';\nimport { getSearchTitle, titleConfigSelector } from 'store/slice/titleSlice';\nimport TitleConfigSearch from 'containers/administration/TitleConfigSearch';\nimport { getSearchParam, transformObject } from 'utils/common';\nimport { Table, TableFooter } from 'components/extended/Table';\nimport { titleFilterConfig } from './Config';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { useAppDispatch, useAppSelector } from 'app/hooks';\nimport { searchFormConfig } from 'containers/search/Config';\nimport { SEARCH_PARAM_KEY } from 'constants/Common';\nimport { FilterCollapse } from 'containers/search';\nimport { PERMISSIONS } from 'constants/Permission';\nimport MainCard from 'components/cards/MainCard';\nimport { TableToolbar } from 'containers';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TitleConfig = () => {\n  _s();\n  var _titles$pagination;\n  const [searchParams, setSearchParams] = useSearchParams();\n  const params = getSearchParam([searchFormConfig.titleCode.name, SEARCH_PARAM_KEY.page, SEARCH_PARAM_KEY.size], searchParams);\n  transformObject(params);\n  const [conditions, setConditions] = useState({\n    ...titleFilterConfig,\n    ...params\n  });\n  const [title, setTitle] = useState();\n  const [open, setOpen] = useState(false);\n  const {\n    titles,\n    loading\n  } = useAppSelector(titleConfigSelector);\n  const dispatch = useAppDispatch();\n  const handleChangePage = (_, newPage) => {\n    setConditions({\n      ...conditions,\n      page: newPage + 1\n    });\n    setSearchParams({\n      ...params,\n      page: newPage + 1\n    });\n  };\n  const handleChangeRowsPerPage = event => {\n    setConditions(prev => ({\n      ...prev,\n      page: 1,\n      size: parseInt(event.target.value, 10)\n    }));\n    setSearchParams(params => ({\n      ...params,\n      page: 1,\n      size: parseInt(event.target.value, 10)\n    }));\n  };\n  const handleOpenDialog = item => {\n    setTitle(item);\n    setOpen(true);\n  };\n  const handleCloseDialog = () => {\n    setOpen(false);\n    setTitle(undefined);\n  };\n  useEffect(() => {\n    dispatch(getSearchTitle(conditions));\n  }, [dispatch, conditions]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(FilterCollapse, {\n      children: /*#__PURE__*/_jsxDEV(TitleConfigSearch, {\n        conditions: conditions,\n        setConditions: setConditions\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(MainCard, {\n      children: [checkAllowedPermission(PERMISSIONS.admin.titleConfigPermission.add) && /*#__PURE__*/_jsxDEV(TableToolbar, {\n        handleOpen: handleOpenDialog\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 89\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        heads: /*#__PURE__*/_jsxDEV(TitleConfigTHead, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 31\n        }, this),\n        isLoading: loading[getSearchTitle.typePrefix],\n        data: titles === null || titles === void 0 ? void 0 : titles.content,\n        children: /*#__PURE__*/_jsxDEV(TitleConfigTBody, {\n          conditions: conditions,\n          data: (titles === null || titles === void 0 ? void 0 : titles.content) || [],\n          handleOpen: handleOpenDialog\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 13\n    }, this), !loading[getSearchTitle.typePrefix] && /*#__PURE__*/_jsxDEV(TableFooter, {\n      pagination: {\n        total: (titles === null || titles === void 0 ? void 0 : (_titles$pagination = titles.pagination) === null || _titles$pagination === void 0 ? void 0 : _titles$pagination.totalElement) || 0,\n        page: conditions.page - 1,\n        size: conditions.size\n      },\n      onPageChange: handleChangePage,\n      onRowsPerPageChange: handleChangeRowsPerPage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 17\n    }, this), open && /*#__PURE__*/_jsxDEV(AddOrEditTitleConfig, {\n      open: open,\n      title: title,\n      conditions: conditions,\n      handleClose: handleCloseDialog\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 22\n    }, this)]\n  }, void 0, true);\n};\n_s(TitleConfig, \"bQOkFmgNbOj83YKXEiG40lcG+GQ=\", false, function () {\n  return [useSearchParams, useAppSelector, useAppDispatch];\n});\n_c = TitleConfig;\nexport default TitleConfig;\nvar _c;\n$RefreshReg$(_c, \"TitleConfig\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useSearchParams", "AddOrEditTitleConfig", "TitleConfigTBody", "TitleConfigTHead", "getSearchTitle", "titleConfigSelector", "TitleConfigSearch", "getSearchParam", "transformObject", "Table", "TableFooter", "titleFilterConfig", "checkAllowedPermission", "useAppDispatch", "useAppSelector", "searchFormConfig", "SEARCH_PARAM_KEY", "FilterCollapse", "PERMISSIONS", "MainCard", "TableToolbar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TitleConfig", "_s", "_titles$pagination", "searchParams", "setSearchParams", "params", "titleCode", "name", "page", "size", "conditions", "setConditions", "title", "setTitle", "open", "<PERSON><PERSON><PERSON>", "titles", "loading", "dispatch", "handleChangePage", "_", "newPage", "handleChangeRowsPerPage", "event", "prev", "parseInt", "target", "value", "handleOpenDialog", "item", "handleCloseDialog", "undefined", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "admin", "titleConfigPermission", "add", "handleOpen", "heads", "isLoading", "typePrefix", "data", "content", "pagination", "total", "totalElement", "onPageChange", "onRowsPerPageChange", "handleClose", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/pages/administration/TitleConfig.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useSearchParams } from 'react-router-dom';\n\nimport AddOrEditTitleConfig from 'containers/administration/AddOrEditTitleConfig';\nimport { TitleConfigTBody, TitleConfigTHead } from 'containers/administration';\nimport { getSearchTitle, titleConfigSelector } from 'store/slice/titleSlice';\nimport TitleConfigSearch from 'containers/administration/TitleConfigSearch';\nimport { getSearchParam, transformObject } from 'utils/common';\nimport { Table, TableFooter } from 'components/extended/Table';\nimport { ITitleFilterConfig, titleFilterConfig } from './Config';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { useAppDispatch, useAppSelector } from 'app/hooks';\nimport { searchFormConfig } from 'containers/search/Config';\nimport { SEARCH_PARAM_KEY } from 'constants/Common';\nimport { FilterCollapse } from 'containers/search';\nimport { PERMISSIONS } from 'constants/Permission';\nimport MainCard from 'components/cards/MainCard';\nimport { ITitleConfig } from 'types/titleConfig';\nimport { TableToolbar } from 'containers';\n\nconst TitleConfig = () => {\n    const [searchParams, setSearchParams] = useSearchParams();\n\n    const params: { [key: string]: any } = getSearchParam(\n        [searchFormConfig.titleCode.name, SEARCH_PARAM_KEY.page, SEARCH_PARAM_KEY.size],\n        searchParams\n    );\n\n    transformObject(params);\n\n    const [conditions, setConditions] = useState<ITitleFilterConfig>({ ...titleFilterConfig, ...params });\n    const [title, setTitle] = useState<ITitleConfig>();\n    const [open, setOpen] = useState<boolean>(false);\n\n    const { titles, loading } = useAppSelector(titleConfigSelector);\n\n    const dispatch = useAppDispatch();\n\n    const handleChangePage = (_: any, newPage: number) => {\n        setConditions({ ...conditions, page: newPage + 1 });\n        setSearchParams({ ...params, page: newPage + 1 } as any);\n    };\n\n    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n        setConditions((prev) => ({ ...prev, page: 1, size: parseInt(event.target.value, 10) }));\n        setSearchParams((params) => ({ ...params, page: 1, size: parseInt(event.target.value, 10) } as any));\n    };\n\n    const handleOpenDialog = (item?: ITitleConfig) => {\n        setTitle(item);\n        setOpen(true);\n    };\n\n    const handleCloseDialog = () => {\n        setOpen(false);\n        setTitle(undefined);\n    };\n\n    useEffect(() => {\n        dispatch(getSearchTitle(conditions));\n    }, [dispatch, conditions]);\n\n    return (\n        <>\n            {/* Search form  */}\n            <FilterCollapse>\n                <TitleConfigSearch conditions={conditions} setConditions={setConditions} />\n            </FilterCollapse>\n\n            {/* Table and Toolbar */}\n            <MainCard>\n                {checkAllowedPermission(PERMISSIONS.admin.titleConfigPermission.add) && <TableToolbar handleOpen={handleOpenDialog} />}\n                <Table heads={<TitleConfigTHead />} isLoading={loading[getSearchTitle.typePrefix]} data={titles?.content}>\n                    <TitleConfigTBody conditions={conditions} data={titles?.content || []} handleOpen={handleOpenDialog} />\n                </Table>\n            </MainCard>\n\n            {/* Pagination  */}\n            {!loading[getSearchTitle.typePrefix] && (\n                <TableFooter\n                    pagination={{ total: titles?.pagination?.totalElement || 0, page: conditions.page - 1, size: conditions.size }}\n                    onPageChange={handleChangePage}\n                    onRowsPerPageChange={handleChangeRowsPerPage}\n                />\n            )}\n\n            {open && <AddOrEditTitleConfig open={open} title={title} conditions={conditions} handleClose={handleCloseDialog} />}\n        </>\n    );\n};\n\nexport default TitleConfig;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,eAAe,QAAQ,kBAAkB;AAElD,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,2BAA2B;AAC9E,SAASC,cAAc,EAAEC,mBAAmB,QAAQ,wBAAwB;AAC5E,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,SAASC,cAAc,EAAEC,eAAe,QAAQ,cAAc;AAC9D,SAASC,KAAK,EAAEC,WAAW,QAAQ,2BAA2B;AAC9D,SAA6BC,iBAAiB,QAAQ,UAAU;AAChE,SAASC,sBAAsB,QAAQ,qBAAqB;AAC5D,SAASC,cAAc,EAAEC,cAAc,QAAQ,WAAW;AAC1D,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,WAAW,QAAQ,sBAAsB;AAClD,OAAOC,QAAQ,MAAM,2BAA2B;AAEhD,SAASC,YAAY,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1C,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,kBAAA;EACtB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7B,eAAe,CAAC,CAAC;EAEzD,MAAM8B,MAA8B,GAAGvB,cAAc,CACjD,CAACQ,gBAAgB,CAACgB,SAAS,CAACC,IAAI,EAAEhB,gBAAgB,CAACiB,IAAI,EAAEjB,gBAAgB,CAACkB,IAAI,CAAC,EAC/EN,YACJ,CAAC;EAEDpB,eAAe,CAACsB,MAAM,CAAC;EAEvB,MAAM,CAACK,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAqB;IAAE,GAAGY,iBAAiB;IAAE,GAAGmB;EAAO,CAAC,CAAC;EACrG,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAe,CAAC;EAClD,MAAM,CAACwC,IAAI,EAAEC,OAAO,CAAC,GAAGzC,QAAQ,CAAU,KAAK,CAAC;EAEhD,MAAM;IAAE0C,MAAM;IAAEC;EAAQ,CAAC,GAAG5B,cAAc,CAACT,mBAAmB,CAAC;EAE/D,MAAMsC,QAAQ,GAAG9B,cAAc,CAAC,CAAC;EAEjC,MAAM+B,gBAAgB,GAAGA,CAACC,CAAM,EAAEC,OAAe,KAAK;IAClDV,aAAa,CAAC;MAAE,GAAGD,UAAU;MAAEF,IAAI,EAAEa,OAAO,GAAG;IAAE,CAAC,CAAC;IACnDjB,eAAe,CAAC;MAAE,GAAGC,MAAM;MAAEG,IAAI,EAAEa,OAAO,GAAG;IAAE,CAAQ,CAAC;EAC5D,CAAC;EAED,MAAMC,uBAAuB,GAAIC,KAAgE,IAAK;IAClGZ,aAAa,CAAEa,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEhB,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAEgB,QAAQ,CAACF,KAAK,CAACG,MAAM,CAACC,KAAK,EAAE,EAAE;IAAE,CAAC,CAAC,CAAC;IACvFvB,eAAe,CAAEC,MAAM,KAAM;MAAE,GAAGA,MAAM;MAAEG,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAEgB,QAAQ,CAACF,KAAK,CAACG,MAAM,CAACC,KAAK,EAAE,EAAE;IAAE,CAAC,CAAQ,CAAC;EACxG,CAAC;EAED,MAAMC,gBAAgB,GAAIC,IAAmB,IAAK;IAC9ChB,QAAQ,CAACgB,IAAI,CAAC;IACdd,OAAO,CAAC,IAAI,CAAC;EACjB,CAAC;EAED,MAAMe,iBAAiB,GAAGA,CAAA,KAAM;IAC5Bf,OAAO,CAAC,KAAK,CAAC;IACdF,QAAQ,CAACkB,SAAS,CAAC;EACvB,CAAC;EAED1D,SAAS,CAAC,MAAM;IACZ6C,QAAQ,CAACvC,cAAc,CAAC+B,UAAU,CAAC,CAAC;EACxC,CAAC,EAAE,CAACQ,QAAQ,EAAER,UAAU,CAAC,CAAC;EAE1B,oBACIb,OAAA,CAAAE,SAAA;IAAAiC,QAAA,gBAEInC,OAAA,CAACL,cAAc;MAAAwC,QAAA,eACXnC,OAAA,CAAChB,iBAAiB;QAAC6B,UAAU,EAAEA,UAAW;QAACC,aAAa,EAAEA;MAAc;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,eAGjBvC,OAAA,CAACH,QAAQ;MAAAsC,QAAA,GACJ7C,sBAAsB,CAACM,WAAW,CAAC4C,KAAK,CAACC,qBAAqB,CAACC,GAAG,CAAC,iBAAI1C,OAAA,CAACF,YAAY;QAAC6C,UAAU,EAAEZ;MAAiB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtHvC,OAAA,CAACb,KAAK;QAACyD,KAAK,eAAE5C,OAAA,CAACnB,gBAAgB;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACM,SAAS,EAAEzB,OAAO,CAACtC,cAAc,CAACgE,UAAU,CAAE;QAACC,IAAI,EAAE5B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE6B,OAAQ;QAAAb,QAAA,eACrGnC,OAAA,CAACpB,gBAAgB;UAACiC,UAAU,EAAEA,UAAW;UAACkC,IAAI,EAAE,CAAA5B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE6B,OAAO,KAAI,EAAG;UAACL,UAAU,EAAEZ;QAAiB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGV,CAACnB,OAAO,CAACtC,cAAc,CAACgE,UAAU,CAAC,iBAChC9C,OAAA,CAACZ,WAAW;MACR6D,UAAU,EAAE;QAAEC,KAAK,EAAE,CAAA/B,MAAM,aAANA,MAAM,wBAAAd,kBAAA,GAANc,MAAM,CAAE8B,UAAU,cAAA5C,kBAAA,uBAAlBA,kBAAA,CAAoB8C,YAAY,KAAI,CAAC;QAAExC,IAAI,EAAEE,UAAU,CAACF,IAAI,GAAG,CAAC;QAAEC,IAAI,EAAEC,UAAU,CAACD;MAAK,CAAE;MAC/GwC,YAAY,EAAE9B,gBAAiB;MAC/B+B,mBAAmB,EAAE5B;IAAwB;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CACJ,EAEAtB,IAAI,iBAAIjB,OAAA,CAACrB,oBAAoB;MAACsC,IAAI,EAAEA,IAAK;MAACF,KAAK,EAAEA,KAAM;MAACF,UAAU,EAAEA,UAAW;MAACyC,WAAW,EAAErB;IAAkB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACrH,CAAC;AAEX,CAAC;AAACnC,EAAA,CArEID,WAAW;EAAA,QAC2BzB,eAAe,EAa3Bc,cAAc,EAEzBD,cAAc;AAAA;AAAAgE,EAAA,GAhB7BpD,WAAW;AAuEjB,eAAeA,WAAW;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}