{"ast": null, "code": "import{yupResolver}from'@hookform/resolvers/yup';import{FormattedMessage}from'react-intl';import{useEffect}from'react';import{useForm}from'react-hook-form';// material-ui\nimport{LoadingButton}from'@mui/lab';import{Button,DialogActions,Grid}from'@mui/material';// project imports\nimport{FormProvider,Input,NumericFormatCustom}from'components/extended/Form';import Modal from'components/extended/Modal';import{saveOrUpdateLeaveDayConfig,saveOrUpdateLeaveDaySchema}from'pages/manage-leave-days/config';import{gridSpacing}from'store/constant';// ==============================|| EDIT LEAVE DAY ||============================== //\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AddOrEditLeaveDay=_ref=>{let{leaveDay,loading,open,handleClose,editLeaveDay,setLeaveDay}=_ref;const handleSubmit=values=>{editLeaveDay(values);};const defaultValues={...saveOrUpdateLeaveDayConfig,leaveDaysInformation:{...saveOrUpdateLeaveDayConfig.leaveDaysInformation},member:\"\".concat(saveOrUpdateLeaveDayConfig.lastName,\" \").concat(saveOrUpdateLeaveDayConfig.firstName)};// useForm\nconst methods=useForm({defaultValues,mode:'all',resolver:yupResolver(saveOrUpdateLeaveDaySchema)});useEffect(()=>{methods.reset({...leaveDay,leaveDaysInformation:{...leaveDay.leaveDaysInformation},member:\"\".concat(leaveDay.lastName,\" \").concat(leaveDay.firstName)});// eslint-disable-next-line react-hooks/exhaustive-deps\n},[leaveDay]);return/*#__PURE__*/_jsx(Modal,{isOpen:open,title:'update-leave-days',onClose:handleClose,keepMounted:false,children:/*#__PURE__*/_jsxs(FormProvider,{formReturn:methods,onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(Grid,{container:true,spacing:gridSpacing,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"member\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:\"member\"}),disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"titleCode\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:\"table-title\"}),disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"departmentId\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:\"department\"}),disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"leaveDaysInformation.totalLeaveDays\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:\"total-leave-days\"}),disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"leaveDaysInformation.totalLeaveDaysLastYear\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:\"total-leave-days-last-year\"})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"leaveDaysInformation.totalLeaveDayThisYear\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:\"total-leave-days-this-year\"})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom}},name:\"leaveDaysInformation.totalLeaveDaysUsed\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:\"number-leave-days-used\"})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"leaveDaysInformation.totalLeaveDaysRemain\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:\"remaining-leave-days\"}),disabled:true})})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{color:\"error\",onClick:handleClose,children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"cancel\"})}),/*#__PURE__*/_jsx(LoadingButton,{loading:loading,variant:\"contained\",type:\"submit\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"submit\"})})]})]})});};export default AddOrEditLeaveDay;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}