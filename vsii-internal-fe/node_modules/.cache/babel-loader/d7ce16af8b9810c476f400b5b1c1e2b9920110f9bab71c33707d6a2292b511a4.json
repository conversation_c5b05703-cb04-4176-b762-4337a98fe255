{"ast": null, "code": "// third party\nimport{FormattedMessage}from'react-intl';// material-ui\n// project imports\nimport{Select}from'components/extended/Form';import{DEFAULT_VALUE_OPTION_SELECT,ROLE_TYPE}from'constants/Common';import{searchFormConfig}from'./Config';import{jsx as _jsx}from\"react/jsx-runtime\";const Role=props=>{const{disabled,handleChange,isShowName,isShowLabel}=props;return/*#__PURE__*/_jsx(Select,{disabled:disabled,selects:[DEFAULT_VALUE_OPTION_SELECT,...ROLE_TYPE],handleChange:handleChange,name:!isShowName?searchFormConfig.roleType.name:isShowName,label:!isShowLabel&&/*#__PURE__*/_jsx(FormattedMessage,{id:searchFormConfig.roleType.label})});};export default Role;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}