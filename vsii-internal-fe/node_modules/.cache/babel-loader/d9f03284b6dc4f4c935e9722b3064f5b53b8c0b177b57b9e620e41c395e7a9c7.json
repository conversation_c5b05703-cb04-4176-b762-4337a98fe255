{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/Period.tsx\";\nimport { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { Select } from 'components/extended/Form';\nimport { PERIOD } from 'constants/Common';\nimport { searchFormConfig } from './Config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Period = ({\n  handleChangePeriod\n}) => {\n  const handleChange = e => {\n    handleChangePeriod(e);\n  };\n  return /*#__PURE__*/_jsxDEV(Select, {\n    isMultipleLanguage: true,\n    handleChange: handleChange,\n    selects: PERIOD,\n    name: searchFormConfig.period.name,\n    label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n      id: searchFormConfig.period.label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 20\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 9\n  }, this);\n};\n_c = Period;\nexport default Period;\nvar _c;\n$RefreshReg$(_c, \"Period\");", "map": {"version": 3, "names": ["FormattedMessage", "Select", "PERIOD", "searchFormConfig", "jsxDEV", "_jsxDEV", "Period", "handleChangePeriod", "handleChange", "e", "isMultipleLanguage", "selects", "name", "period", "label", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/Period.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { Select } from 'components/extended/Form';\nimport { PERIOD } from 'constants/Common';\nimport { searchFormConfig } from './Config';\ninterface IPeriodProps {\n    handleChangePeriod: (period: any) => void;\n}\n\nconst Period = ({ handleChangePeriod }: IPeriodProps) => {\n    const handleChange = (e: any) => {\n        handleChangePeriod(e);\n    };\n\n    return (\n        <Select\n            isMultipleLanguage\n            handleChange={handleChange}\n            selects={PERIOD}\n            name={searchFormConfig.period.name}\n            label={<FormattedMessage id={searchFormConfig.period.label} />}\n        />\n    );\n};\n\nexport default Period;\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,MAAM,QAAQ,kBAAkB;AACzC,SAASC,gBAAgB,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAK5C,MAAMC,MAAM,GAAGA,CAAC;EAAEC;AAAiC,CAAC,KAAK;EACrD,MAAMC,YAAY,GAAIC,CAAM,IAAK;IAC7BF,kBAAkB,CAACE,CAAC,CAAC;EACzB,CAAC;EAED,oBACIJ,OAAA,CAACJ,MAAM;IACHS,kBAAkB;IAClBF,YAAY,EAAEA,YAAa;IAC3BG,OAAO,EAAET,MAAO;IAChBU,IAAI,EAAET,gBAAgB,CAACU,MAAM,CAACD,IAAK;IACnCE,KAAK,eAAET,OAAA,CAACL,gBAAgB;MAACe,EAAE,EAAEZ,gBAAgB,CAACU,MAAM,CAACC;IAAM;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClE,CAAC;AAEV,CAAC;AAACC,EAAA,GAdId,MAAM;AAgBZ,eAAeA,MAAM;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}