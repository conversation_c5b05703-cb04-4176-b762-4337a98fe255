{"ast": null, "code": "import { px } from '../../value/types/numbers/units.mjs';\nfunction pixelsToPercent(pixels, axis) {\n  if (axis.max === axis.min) return 0;\n  return pixels / (axis.max - axis.min) * 100;\n}\n/**\n * We always correct borderRadius as a percentage rather than pixels to reduce paints.\n * For example, if you are projecting a box that is 100px wide with a 10px borderRadius\n * into a box that is 200px wide with a 20px borderRadius, that is actually a 10%\n * borderRadius in both states. If we animate between the two in pixels that will trigger\n * a paint each time. If we animate between the two in percentage we'll avoid a paint.\n */\nconst correctBorderRadius = {\n  correct: (latest, node) => {\n    if (!node.target) return latest;\n    /**\n     * If latest is a string, if it's a percentage we can return immediately as it's\n     * going to be stretched appropriately. Otherwise, if it's a pixel, convert it to a number.\n     */\n    if (typeof latest === \"string\") {\n      if (px.test(latest)) {\n        latest = parseFloat(latest);\n      } else {\n        return latest;\n      }\n    }\n    /**\n     * If latest is a number, it's a pixel value. We use the current viewportBox to calculate that\n     * pixel value as a percentage of each axis\n     */\n    const x = pixelsToPercent(latest, node.target.x);\n    const y = pixelsToPercent(latest, node.target.y);\n    return \"\".concat(x, \"% \").concat(y, \"%\");\n  }\n};\nexport { correctBorderRadius, pixelsToPercent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}