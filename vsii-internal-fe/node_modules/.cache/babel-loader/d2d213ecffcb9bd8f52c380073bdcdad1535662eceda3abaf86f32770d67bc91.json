{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport function getPickersYearUtilityClass(slot) {\n  // TODO v6: Rename 'PrivatePickersYear' to 'MuiPickersYear' to follow convention\n  return generateUtilityClass('PrivatePickersYear', slot);\n} // TODO v6: Rename 'PrivatePickersYear' to 'MuiPickersYear' to follow convention\n\nexport const pickersYearClasses = generateUtilityClasses('PrivatePickersYear', ['root', 'modeDesktop', 'modeMobile', 'yearButton', 'selected', 'disabled']);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}