{"ast": null, "code": "// material-ui\nimport{Link,Typography,Stack}from'@mui/material';// ==============================|| AUTH FOOTER ||============================== //\nimport{jsxs as _jsxs,jsx as _jsx}from\"react/jsx-runtime\";const AuthFooter=()=>{const year=new Date().getFullYear();return/*#__PURE__*/_jsx(Stack,{direction:\"row\",justifyContent:\"center\",children:/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle2\",component:Link,href:\"https://vsi-international.com/\",target:\"_blank\",underline:\"hover\",sx:{cursor:'pointer',color:'black'},children:[\"\\xA9 \",year,\" - Powered by VSII\"]})});};export default AuthFooter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}