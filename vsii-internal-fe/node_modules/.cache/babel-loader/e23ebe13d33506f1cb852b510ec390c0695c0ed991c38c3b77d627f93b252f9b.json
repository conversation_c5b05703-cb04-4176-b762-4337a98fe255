{"ast": null, "code": "import{FormattedMessage}from'react-intl';// yup\nimport{yupResolver}from'@hookform/resolvers/yup';// material-ui\nimport{LoadingButton}from'@mui/lab';import{Button,DialogActions,Grid,Stack}from'@mui/material';import{useAppDispatch,useAppSelector}from'app/hooks';// project imports\nimport{DatePicker,FormProvider,Input}from'components/extended/Form';import Modal from'components/extended/Modal';import{HeadCount,Member}from'containers/search';import{saveOrUpdateProjectUserConfig,saveOrUpdateProjectUserSchema}from'pages/administration/Config';import{gridSpacing}from'store/constant';import{editLoadingProjectUserSelector,projectDetailSelector,saveOrUpdateProjectUser}from'store/slice/projectSlice';import{openSnackbar}from'store/slice/snackbarSlice';import{dateFormat}from'utils/date';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AddOrEditProjectUser=props=>{const dispatch=useAppDispatch();const projectDetail=useAppSelector(projectDetailSelector);const{open,handleClose,user,isEdit,setUser,handleCloseMember}=props;const editLoadingProjectUser=useAppSelector(editLoadingProjectUserSelector);const handleChangeMember=userSelected=>{userSelected&&setUser({...user,userId:{value:userSelected.userId,label:\"\".concat(userSelected.firstName,\" \").concat(userSelected.lastName)},userName:userSelected.userName,memberCode:userSelected.memberCode,lastName:userSelected.lastName,firstName:userSelected.firstName});};const handleSubmit=values=>{const{userId,fromDate,toDate}=values;if(values.userName===''){dispatch(openSnackbar({open:true,message:'Please enter User',variant:'alert',alert:{color:'error'}}));}else{dispatch(saveOrUpdateProjectUser({...values,projectId:+values.projectId,userId:+userId.value,fromDate:dateFormat(fromDate),toDate:toDate?dateFormat(toDate):toDate,isEdit}));!editLoadingProjectUser&&handleClose();}};return/*#__PURE__*/_jsx(Modal,{isOpen:open,title:isEdit?'edit-headcount':'add-headcount',onClose:handleClose,keepMounted:false,children:/*#__PURE__*/_jsx(FormProvider,{form:{defaultValues:saveOrUpdateProjectUserConfig,resolver:yupResolver(saveOrUpdateProjectUserSchema)},onSubmit:handleSubmit,formReset:{...user,userId:!!user.userId?{value:isEdit?user.userId:user.userId.value,label:\"\".concat(user.firstName,\" \").concat(user.lastName)}:user.userId,projectId:projectDetail===null||projectDetail===void 0?void 0:projectDetail.project.projectId,projectName:projectDetail===null||projectDetail===void 0?void 0:projectDetail.project.projectName,projectType:projectDetail===null||projectDetail===void 0?void 0:projectDetail.project.typeCode},children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:gridSpacing,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Member,{label:\"User\",handleChange:handleChangeMember,handleClose:handleCloseMember,disabled:isEdit,isShowAll:false,isFindAll:true,isIdHexString:true,required:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"memberCode\",label:\"Member Code\",disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"userName\",label:\"Username\",disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"firstName\",label:\"First name\",disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{name:\"lastName\",label:\"Last name\",disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(HeadCount,{required:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(DatePicker,{required:true,name:\"fromDate\",label:\"From date\"})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(DatePicker,{name:\"toDate\",label:\"To date\"})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(DialogActions,{children:/*#__PURE__*/_jsxs(Stack,{direction:\"row\",spacing:1,justifyContent:\"flex-end\",children:[/*#__PURE__*/_jsx(Button,{color:\"error\",onClick:handleClose,children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"cancel\"})}),/*#__PURE__*/_jsx(LoadingButton,{loading:editLoadingProjectUser,variant:\"contained\",type:\"submit\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"submit\"})})]})})})]})})});};export default AddOrEditProjectUser;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}