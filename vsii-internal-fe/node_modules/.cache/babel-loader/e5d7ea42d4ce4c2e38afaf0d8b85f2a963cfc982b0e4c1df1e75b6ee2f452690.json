{"ast": null, "code": "const getOptions = (options, key) =>\n/**\n * TODO: Make test for this\n * Always return a new object otherwise delay is overwritten by results of stagger\n * and this results in no stagger\n */\noptions[key] ? Object.assign(Object.assign({}, options), options[key]) : Object.assign({}, options);\nexport { getOptions };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}