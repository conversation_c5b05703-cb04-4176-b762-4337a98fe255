{"ast": null, "code": "export const findClosestEnabledDate = ({\n  date,\n  disableFuture,\n  disablePast,\n  maxDate,\n  minDate,\n  isDateDisabled,\n  utils\n}) => {\n  const today = utils.startOfDay(utils.date());\n  if (disablePast && utils.isBefore(minDate, today)) {\n    minDate = today;\n  }\n  if (disableFuture && utils.isAfter(maxDate, today)) {\n    maxDate = today;\n  }\n  let forward = date;\n  let backward = date;\n  if (utils.isBefore(date, minDate)) {\n    forward = utils.date(minDate);\n    backward = null;\n  }\n  if (utils.isAfter(date, maxDate)) {\n    if (backward) {\n      backward = utils.date(maxDate);\n    }\n    forward = null;\n  }\n  while (forward || backward) {\n    if (forward && utils.isAfter(forward, maxDate)) {\n      forward = null;\n    }\n    if (backward && utils.isBefore(backward, minDate)) {\n      backward = null;\n    }\n    if (forward) {\n      if (!isDateDisabled(forward)) {\n        return forward;\n      }\n      forward = utils.addDays(forward, 1);\n    }\n    if (backward) {\n      if (!isDateDisabled(backward)) {\n        return backward;\n      }\n      backward = utils.addDays(backward, -1);\n    }\n  }\n  return null;\n};\nexport const parsePickerInputValue = (utils, value) => {\n  const parsedValue = utils.date(value);\n  return utils.isValid(parsedValue) ? parsedValue : null;\n};\nexport const parseNonNullablePickerDate = (utils, value, defaultValue) => {\n  if (value == null) {\n    return defaultValue;\n  }\n  const parsedValue = utils.date(value);\n  const isDateValid = utils.isValid(parsedValue);\n  if (isDateValid) {\n    return parsedValue;\n  }\n  return defaultValue;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}