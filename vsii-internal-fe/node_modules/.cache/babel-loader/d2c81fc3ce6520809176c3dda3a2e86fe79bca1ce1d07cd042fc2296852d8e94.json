{"ast": null, "code": "import { BestFitMatcher } from './BestFitMatcher';\nimport { LookupMatcher } from './LookupMatcher';\nimport { UnicodeExtensionValue } from './UnicodeExtensionValue';\nimport { invariant } from './utils';\n/**\n * https://tc39.es/ecma402/#sec-resolvelocale\n */\nexport function ResolveLocale(availableLocales, requestedLocales, options, relevantExtensionKeys, localeData, getDefaultLocale) {\n  var matcher = options.localeMatcher;\n  var r;\n  if (matcher === 'lookup') {\n    r = LookupMatcher(Array.from(availableLocales), requestedLocales, getDefaultLocale);\n  } else {\n    r = BestFitMatcher(Array.from(availableLocales), requestedLocales, getDefaultLocale);\n  }\n  var foundLocale = r.locale;\n  var result = {\n    locale: '',\n    dataLocale: foundLocale\n  };\n  var supportedExtension = '-u';\n  for (var _i = 0, relevantExtensionKeys_1 = relevantExtensionKeys; _i < relevantExtensionKeys_1.length; _i++) {\n    var key = relevantExtensionKeys_1[_i];\n    invariant(foundLocale in localeData, \"Missing locale data for \".concat(foundLocale));\n    var foundLocaleData = localeData[foundLocale];\n    invariant(typeof foundLocaleData === 'object' && foundLocaleData !== null, \"locale data \".concat(key, \" must be an object\"));\n    var keyLocaleData = foundLocaleData[key];\n    invariant(Array.isArray(keyLocaleData), \"keyLocaleData for \".concat(key, \" must be an array\"));\n    var value = keyLocaleData[0];\n    invariant(typeof value === 'string' || value === null, \"value must be string or null but got \".concat(typeof value, \" in key \").concat(key));\n    var supportedExtensionAddition = '';\n    if (r.extension) {\n      var requestedValue = UnicodeExtensionValue(r.extension, key);\n      if (requestedValue !== undefined) {\n        if (requestedValue !== '') {\n          if (~keyLocaleData.indexOf(requestedValue)) {\n            value = requestedValue;\n            supportedExtensionAddition = \"-\".concat(key, \"-\").concat(value);\n          }\n        } else if (~requestedValue.indexOf('true')) {\n          value = 'true';\n          supportedExtensionAddition = \"-\".concat(key);\n        }\n      }\n    }\n    if (key in options) {\n      var optionsValue = options[key];\n      invariant(typeof optionsValue === 'string' || typeof optionsValue === 'undefined' || optionsValue === null, 'optionsValue must be String, Undefined or Null');\n      if (~keyLocaleData.indexOf(optionsValue)) {\n        if (optionsValue !== value) {\n          value = optionsValue;\n          supportedExtensionAddition = '';\n        }\n      }\n    }\n    result[key] = value;\n    supportedExtension += supportedExtensionAddition;\n  }\n  if (supportedExtension.length > 2) {\n    var privateIndex = foundLocale.indexOf('-x-');\n    if (privateIndex === -1) {\n      foundLocale = foundLocale + supportedExtension;\n    } else {\n      var preExtension = foundLocale.slice(0, privateIndex);\n      var postExtension = foundLocale.slice(privateIndex, foundLocale.length);\n      foundLocale = preExtension + supportedExtension + postExtension;\n    }\n    foundLocale = Intl.getCanonicalLocales(foundLocale)[0];\n  }\n  result.locale = foundLocale;\n  return result;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}