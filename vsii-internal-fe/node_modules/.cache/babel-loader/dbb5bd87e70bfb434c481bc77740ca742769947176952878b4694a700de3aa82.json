{"ast": null, "code": "/*\n * Copyright 2015, Yahoo Inc.\n * Copyrights licensed under the New BSD License.\n * See the accompanying LICENSE file for terms.\n */\nimport * as React from 'react';\nimport useIntl from './useIntl';\nvar FormattedPlural = function (props) {\n  var _a = useIntl(),\n    formatPlural = _a.formatPlural,\n    Text = _a.textComponent;\n  var value = props.value,\n    other = props.other,\n    children = props.children;\n  var pluralCategory = formatPlural(value, props);\n  var formattedPlural = props[pluralCategory] || other;\n  if (typeof children === 'function') {\n    return children(formattedPlural);\n  }\n  if (Text) {\n    return React.createElement(Text, null, formattedPlural);\n  }\n  // Work around @types/react where React.FC cannot return string\n  return formattedPlural;\n};\nFormattedPlural.displayName = 'FormattedPlural';\nexport default FormattedPlural;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}