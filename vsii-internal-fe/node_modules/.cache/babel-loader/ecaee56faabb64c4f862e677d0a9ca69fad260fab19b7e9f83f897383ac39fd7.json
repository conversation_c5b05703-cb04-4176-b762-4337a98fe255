{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"displayStaticWrapperAs\", \"onAccept\", \"onClear\", \"onCancel\", \"onDismiss\", \"onSetToday\", \"open\", \"children\", \"components\", \"componentsProps\", \"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/material';\nimport clsx from 'clsx';\nimport { DIALOG_WIDTH } from '../../constants/dimensions';\nimport { WrapperVariantContext } from '../wrappers/WrapperVariantContext';\nimport { getStaticWrapperUtilityClass } from './pickerStaticWrapperClasses';\nimport { PickersActionBar } from '../../../PickersActionBar';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    content: ['content']\n  };\n  return composeClasses(slots, getStaticWrapperUtilityClass, classes);\n};\nconst PickerStaticWrapperRoot = styled('div', {\n  name: 'MuiPickerStaticWrapper',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  display: 'flex',\n  flexDirection: 'column'\n});\nconst PickerStaticWrapperContent = styled('div', {\n  name: 'MuiPickerStaticWrapper',\n  slot: 'Content',\n  overridesResolver: (props, styles) => styles.content\n})(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  minWidth: DIALOG_WIDTH,\n  display: 'flex',\n  flexDirection: 'column',\n  backgroundColor: theme.palette.background.paper\n}));\nfunction PickerStaticWrapper(inProps) {\n  var _components$ActionBar;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiPickerStaticWrapper'\n  });\n  const {\n      displayStaticWrapperAs,\n      onAccept,\n      onClear,\n      onCancel,\n      onSetToday,\n      children,\n      components,\n      componentsProps,\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const classes = useUtilityClasses(props);\n  const ActionBar = (_components$ActionBar = components == null ? void 0 : components.ActionBar) != null ? _components$ActionBar : PickersActionBar;\n  const PaperContent = (components == null ? void 0 : components.PaperContent) || React.Fragment;\n  return /*#__PURE__*/_jsx(WrapperVariantContext.Provider, {\n    value: displayStaticWrapperAs,\n    children: /*#__PURE__*/_jsxs(PickerStaticWrapperRoot, _extends({\n      className: clsx(classes.root, className)\n    }, other, {\n      children: [/*#__PURE__*/_jsx(PickerStaticWrapperContent, {\n        className: classes.content,\n        children: /*#__PURE__*/_jsx(PaperContent, _extends({}, componentsProps == null ? void 0 : componentsProps.paperContent, {\n          children: children\n        }))\n      }), /*#__PURE__*/_jsx(ActionBar, _extends({\n        onAccept: onAccept,\n        onClear: onClear,\n        onCancel: onCancel,\n        onSetToday: onSetToday,\n        actions: displayStaticWrapperAs === 'desktop' ? [] : ['cancel', 'accept']\n      }, componentsProps == null ? void 0 : componentsProps.actionBar))]\n    }))\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? PickerStaticWrapper.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * Overrideable components.\n   * @default {}\n   */\n  components: PropTypes.object,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  componentsProps: PropTypes.object,\n  /**\n   * Force static wrapper inner components to be rendered in mobile or desktop mode.\n   */\n  displayStaticWrapperAs: PropTypes.oneOf(['desktop', 'mobile']).isRequired,\n  onAccept: PropTypes.func.isRequired,\n  onCancel: PropTypes.func.isRequired,\n  onClear: PropTypes.func.isRequired,\n  onDismiss: PropTypes.func.isRequired,\n  onSetToday: PropTypes.func.isRequired,\n  open: PropTypes.bool.isRequired\n} : void 0;\nexport { PickerStaticWrapper };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}