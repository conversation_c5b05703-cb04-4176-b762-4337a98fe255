{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/extended/Form/Input.tsx\",\n  _s = $RefreshSig$();\nimport React, { memo } from 'react';\nimport { FormattedMessage } from 'react-intl';\n\n// material-ui\nimport { TextField } from '@mui/material';\n\n// react-hook-form\nimport { Controller, useFormContext } from 'react-hook-form';\n\n// project imports\nimport { removeExtraSpace } from 'utils/common';\nimport Label from './Label';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Input = props => {\n  _s();\n  const {\n    name,\n    label,\n    disabled,\n    textFieldProps,\n    required,\n    type,\n    onChangeInput,\n    placeholder,\n    sx,\n    styleLabel\n  } = props;\n  const methods = useFormContext();\n  return /*#__PURE__*/_jsxDEV(Controller, {\n    name: name,\n    control: methods.control,\n    render: ({\n      field: {\n        value,\n        ref,\n        onChange,\n        ...field\n      },\n      fieldState: {\n        error\n      }\n    }) => {\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          name: name,\n          label: label,\n          required: required,\n          sx: styleLabel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          type: type,\n          id: name,\n          ...field,\n          value: value,\n          size: \"small\",\n          disabled: disabled,\n          onBlur: e => {\n            return onChange(removeExtraSpace(value));\n          },\n          onChange: e => {\n            onChange(e);\n            onChangeInput === null || onChangeInput === void 0 ? void 0 : onChangeInput(e);\n          },\n          fullWidth: true,\n          placeholder: placeholder,\n          error: !!error,\n          helperText: error && /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: error.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 50\n          }, this),\n          inputRef: ref,\n          sx: sx,\n          ...textFieldProps\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true);\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 9\n  }, this);\n};\n_s(Input, \"u7sAMcQCpiWJQxXuJ6yWLOYZ4cg=\", false, function () {\n  return [useFormContext];\n});\n_c = Input;\nexport default _c2 = /*#__PURE__*/memo(Input);\nvar _c, _c2;\n$RefreshReg$(_c, \"Input\");\n$RefreshReg$(_c2, \"%default%\");", "map": {"version": 3, "names": ["React", "memo", "FormattedMessage", "TextField", "Controller", "useFormContext", "removeExtraSpace", "Label", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Input", "props", "_s", "name", "label", "disabled", "textFieldProps", "required", "type", "onChangeInput", "placeholder", "sx", "styleLabel", "methods", "control", "render", "field", "value", "ref", "onChange", "fieldState", "error", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "size", "onBlur", "e", "fullWidth", "helperText", "message", "inputRef", "_c", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/extended/Form/Input.tsx"], "sourcesContent": ["import React, { ChangeEvent, ReactNode, memo } from 'react';\nimport { FormattedMessage } from 'react-intl';\n\n// material-ui\nimport { SxProps, TextField, TextFieldProps } from '@mui/material';\n\n// react-hook-form\nimport { Controller, useFormContext } from 'react-hook-form';\n\n// project imports\nimport { removeExtraSpace } from 'utils/common';\nimport Label from './Label';\n\ninterface IInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'id' | 'className'> {\n    name: string;\n    label?: string | ReactNode;\n    disabled?: boolean;\n    textFieldProps?: TextFieldProps;\n    required?: boolean;\n    onChangeInput?: React.ChangeEventHandler<HTMLInputElement> | undefined;\n    type?: React.HTMLInputTypeAttribute;\n    placeholder?: string;\n    sx?: SxProps<any>;\n    styleLabel?: SxProps;\n}\n\nconst Input = (props: IInputProps): JSX.Element => {\n    const { name, label, disabled, textFieldProps, required, type, onChangeInput, placeholder, sx, styleLabel } = props;\n    const methods = useFormContext();\n\n    return (\n        <Controller\n            name={name}\n            control={methods.control}\n            render={({ field: { value, ref, onChange, ...field }, fieldState: { error } }) => {\n                return (\n                    <>\n                        <Label name={name} label={label} required={required} sx={styleLabel} />\n                        <TextField\n                            type={type}\n                            id={name}\n                            {...field}\n                            value={value}\n                            size=\"small\"\n                            disabled={disabled}\n                            onBlur={(e) => {\n                                return onChange(removeExtraSpace(value));\n                            }}\n                            onChange={(e: ChangeEvent<HTMLInputElement>) => {\n                                onChange(e);\n                                onChangeInput?.(e);\n                            }}\n                            fullWidth\n                            placeholder={placeholder}\n                            error={!!error}\n                            helperText={error && <FormattedMessage id={error.message} />}\n                            inputRef={ref}\n                            sx={sx}\n                            {...textFieldProps}\n                        />\n                    </>\n                );\n            }}\n        />\n    );\n};\n\nexport default memo(Input);\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAA4BC,IAAI,QAAQ,OAAO;AAC3D,SAASC,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAAkBC,SAAS,QAAwB,eAAe;;AAElE;AACA,SAASC,UAAU,EAAEC,cAAc,QAAQ,iBAAiB;;AAE5D;AACA,SAASC,gBAAgB,QAAQ,cAAc;AAC/C,OAAOC,KAAK,MAAM,SAAS;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAe5B,MAAMC,KAAK,GAAIC,KAAkB,IAAkB;EAAAC,EAAA;EAC/C,MAAM;IAAEC,IAAI;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,cAAc;IAAEC,QAAQ;IAAEC,IAAI;IAAEC,aAAa;IAAEC,WAAW;IAAEC,EAAE;IAAEC;EAAW,CAAC,GAAGX,KAAK;EACnH,MAAMY,OAAO,GAAGpB,cAAc,CAAC,CAAC;EAEhC,oBACII,OAAA,CAACL,UAAU;IACPW,IAAI,EAAEA,IAAK;IACXW,OAAO,EAAED,OAAO,CAACC,OAAQ;IACzBC,MAAM,EAAEA,CAAC;MAAEC,KAAK,EAAE;QAAEC,KAAK;QAAEC,GAAG;QAAEC,QAAQ;QAAE,GAAGH;MAAM,CAAC;MAAEI,UAAU,EAAE;QAAEC;MAAM;IAAE,CAAC,KAAK;MAC9E,oBACIxB,OAAA,CAAAE,SAAA;QAAAuB,QAAA,gBACIzB,OAAA,CAACF,KAAK;UAACQ,IAAI,EAAEA,IAAK;UAACC,KAAK,EAAEA,KAAM;UAACG,QAAQ,EAAEA,QAAS;UAACI,EAAE,EAAEC;QAAW;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvE7B,OAAA,CAACN,SAAS;UACNiB,IAAI,EAAEA,IAAK;UACXmB,EAAE,EAAExB,IAAK;UAAA,GACLa,KAAK;UACTC,KAAK,EAAEA,KAAM;UACbW,IAAI,EAAC,OAAO;UACZvB,QAAQ,EAAEA,QAAS;UACnBwB,MAAM,EAAGC,CAAC,IAAK;YACX,OAAOX,QAAQ,CAACzB,gBAAgB,CAACuB,KAAK,CAAC,CAAC;UAC5C,CAAE;UACFE,QAAQ,EAAGW,CAAgC,IAAK;YAC5CX,QAAQ,CAACW,CAAC,CAAC;YACXrB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAGqB,CAAC,CAAC;UACtB,CAAE;UACFC,SAAS;UACTrB,WAAW,EAAEA,WAAY;UACzBW,KAAK,EAAE,CAAC,CAACA,KAAM;UACfW,UAAU,EAAEX,KAAK,iBAAIxB,OAAA,CAACP,gBAAgB;YAACqC,EAAE,EAAEN,KAAK,CAACY;UAAQ;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7DQ,QAAQ,EAAEhB,GAAI;UACdP,EAAE,EAAEA,EAAG;UAAA,GACHL;QAAc;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA,eACJ,CAAC;IAEX;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACxB,EAAA,CAvCIF,KAAK;EAAA,QAESP,cAAc;AAAA;AAAA0C,EAAA,GAF5BnC,KAAK;AAyCX,eAAAoC,GAAA,gBAAe/C,IAAI,CAACW,KAAK,CAAC;AAAC,IAAAmC,EAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAF,EAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}