{"ast": null, "code": "import { filterProps } from './utils';\nimport { IntlFormatError } from './error';\nimport { ErrorCode, FormatError } from 'intl-messageformat';\nvar PLURAL_FORMAT_OPTIONS = ['type'];\nexport function formatPlural(_a, getPluralRules, value, options) {\n  var locale = _a.locale,\n    onError = _a.onError;\n  if (options === void 0) {\n    options = {};\n  }\n  if (!Intl.PluralRules) {\n    onError(new FormatError(\"Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-pluralrules\\\"\\n\", ErrorCode.MISSING_INTL_API));\n  }\n  var filteredOptions = filterProps(options, PLURAL_FORMAT_OPTIONS);\n  try {\n    return getPluralRules(locale, filteredOptions).select(value);\n  } catch (e) {\n    onError(new IntlFormatError('Error formatting plural.', locale, e));\n  }\n  return 'other';\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}