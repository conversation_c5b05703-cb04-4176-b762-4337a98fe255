{"ast": null, "code": "import{createAsyncThunk,createSlice}from'@reduxjs/toolkit';import sendRequest from'services/ApiService';import Api from'constants/Api';// interface\n// initial state\nconst initialState={loading:{}};export const resetPassowrd=createAsyncThunk('Api.member.resetPassowrd.url',async params=>{const response=await sendRequest(Api.member.resetPassowrd(params.userId));return response;});const memberSlice=createSlice({name:'member',initialState:initialState,reducers:{},extraReducers:builder=>{builder.addCase(resetPassowrd.pending,state=>{state.loading[resetPassowrd.typePrefix]=true;});builder.addCase(resetPassowrd.fulfilled,(state,action)=>{state.loading[resetPassowrd.typePrefix]=false;});builder.addCase(resetPassowrd.rejected,state=>{state.loading[resetPassowrd.typePrefix]=false;});}});// export const { } = memberSlice.actions;\n// selectors\nexport const memberSelector=state=>state.member;// reducers\nexport default memberSlice.reducer;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}