{"ast": null, "code": "export default function HTMLElementType(props, propName, componentName, location, propFullName) {\n  if (process.env.NODE_ENV === 'production') {\n    return null;\n  }\n  const propValue = props[propName];\n  const safePropName = propFullName || propName;\n  if (propValue == null) {\n    return null;\n  }\n  if (propValue && propValue.nodeType !== 1) {\n    return new Error(`Invalid ${location} \\`${safePropName}\\` supplied to \\`${componentName}\\`. ` + `Expected an HTMLElement.`);\n  }\n  return null;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}