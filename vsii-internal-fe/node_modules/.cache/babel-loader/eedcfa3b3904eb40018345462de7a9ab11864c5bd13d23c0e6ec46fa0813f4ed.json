{"ast": null, "code": "export const CLOCK_WIDTH = 220;\nexport const CLOCK_HOUR_WIDTH = 36;\nconst clockCenter = {\n  x: CLOCK_WIDTH / 2,\n  y: CLOCK_WIDTH / 2\n};\nconst baseClockPoint = {\n  x: clockCenter.x,\n  y: 0\n};\nconst cx = baseClockPoint.x - clockCenter.x;\nconst cy = baseClockPoint.y - clockCenter.y;\nconst rad2deg = rad => rad * (180 / Math.PI);\nconst getAngleValue = (step, offsetX, offsetY) => {\n  const x = offsetX - clockCenter.x;\n  const y = offsetY - clockCenter.y;\n  const atan = Math.atan2(cx, cy) - Math.atan2(x, y);\n  let deg = rad2deg(atan);\n  deg = Math.round(deg / step) * step;\n  deg %= 360;\n  const value = Math.floor(deg / step) || 0;\n  const delta = x ** 2 + y ** 2;\n  const distance = Math.sqrt(delta);\n  return {\n    value,\n    distance\n  };\n};\nexport const getMinutes = (offsetX, offsetY, step = 1) => {\n  const angleStep = step * 6;\n  let {\n    value\n  } = getAngleValue(angleStep, offsetX, offsetY);\n  value = value * step % 60;\n  return value;\n};\nexport const getHours = (offsetX, offsetY, ampm) => {\n  const {\n    value,\n    distance\n  } = getAngleValue(30, offsetX, offsetY);\n  let hour = value || 12;\n  if (!ampm) {\n    if (distance < CLOCK_WIDTH / 2 - CLOCK_HOUR_WIDTH) {\n      hour += 12;\n      hour %= 24;\n    }\n  } else {\n    hour %= 12;\n  }\n  return hour;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}