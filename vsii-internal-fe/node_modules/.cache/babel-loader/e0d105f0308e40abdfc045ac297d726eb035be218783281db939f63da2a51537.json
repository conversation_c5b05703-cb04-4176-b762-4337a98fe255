{"ast": null, "code": "// material-ui\nimport{Grid}from'@mui/material';// project imports\nimport{Button}from'components';import{Label}from'components/extended/Form';import{ProductionPerformance,SalePipelineStatus,SalePipelineType,SearchForm,SalesYear}from'containers/search';import{onGoingConfig,onGoingSchema}from'pages/sales/Config';import{E_SCREEN_SALES_YEAR}from'constants/Common';// third party\nimport{FormattedMessage}from'react-intl';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const OnGoingSearch=props=>{const{formReset,handleSearch,handleChangeYear,handleChangeSalePipelineType,handleChangeStatus,handleChangeProject}=props;const{salesReport}=TEXT_CONFIG_SCREEN;return/*#__PURE__*/_jsx(SearchForm,{defaultValues:onGoingConfig,formSchema:onGoingSchema,handleSubmit:handleSearch,formReset:formReset,children:/*#__PURE__*/_jsxs(Grid,{container:true,alignItems:\"center\",spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:2.4,children:/*#__PURE__*/_jsx(SalePipelineType,{isShowAll:true,handleChangeSalePipelineType:handleChangeSalePipelineType,label:salesReport.salesOnGoing+'-sale-pipeline-type'})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:2.4,children:/*#__PURE__*/_jsx(SalesYear,{handleChangeYear:handleChangeYear,screen:E_SCREEN_SALES_YEAR.ON_GOING,label:salesReport.salesOnGoing+'-year'})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:2.4,children:/*#__PURE__*/_jsx(ProductionPerformance,{isDefaultAll:false,handleChange:handleChangeProject,requestParams:formReset,label:salesReport.salesOnGoing+'-project'})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:2.4,children:/*#__PURE__*/_jsx(SalePipelineStatus,{isShowAll:true,handleChangeStatus:handleChangeStatus,label:salesReport.salesOnGoing+'-sale-pipeline-status'})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,lg:2.4,children:[/*#__PURE__*/_jsx(Label,{label:\"\\xA0\"}),/*#__PURE__*/_jsx(Button,{type:\"submit\",size:\"medium\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.salesOnGoing+'-search'}),variant:\"contained\"})]})]})});};export default OnGoingSearch;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}