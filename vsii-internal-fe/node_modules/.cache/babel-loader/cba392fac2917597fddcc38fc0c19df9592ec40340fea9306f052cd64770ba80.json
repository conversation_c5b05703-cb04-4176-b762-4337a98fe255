{"ast": null, "code": "import { isMouseEvent } from './utils/event-type.mjs';\nimport { AnimationType } from '../render/utils/types.mjs';\nimport { usePointerEvent } from '../events/use-pointer-event.mjs';\nimport { isDragActive } from './drag/utils/lock.mjs';\nfunction createHoverEvent(visualElement, isActive, callback) {\n  return (event, info) => {\n    if (!isMouseEvent(event) || isDragActive()) return;\n    /**\n     * Ensure we trigger animations before firing event callback\n     */\n    if (visualElement.animationState) {\n      visualElement.animationState.setActive(AnimationType.Hover, isActive);\n    }\n    callback && callback(event, info);\n  };\n}\nfunction useHoverGesture(_ref) {\n  let {\n    onHoverStart,\n    onHoverEnd,\n    whileHover,\n    visualElement\n  } = _ref;\n  usePointerEvent(visualElement, \"pointerenter\", onHoverStart || whileHover ? createHoverEvent(visualElement, true, onHoverStart) : undefined, {\n    passive: !onHoverStart\n  });\n  usePointerEvent(visualElement, \"pointerleave\", onHoverEnd || whileHover ? createHoverEvent(visualElement, false, onHoverEnd) : undefined, {\n    passive: !onHoverEnd\n  });\n}\nexport { useHoverGesture };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}