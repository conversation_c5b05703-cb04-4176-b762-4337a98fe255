{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nimport { useCollectedProps } from '../useCollectedProps.js';\nimport { useOptionalFactory } from '../useOptionalFactory.js';\nimport { useConnectDragPreview, useConnectDragSource } from './connectors.js';\nimport { useDragSourceConnector } from './useDragSourceConnector.js';\nimport { useDragSourceMonitor } from './useDragSourceMonitor.js';\nimport { useRegisteredDragSource } from './useRegisteredDragSource.js';\n/**\n * useDragSource hook\n * @param sourceSpec The drag source specification (object or function, function preferred)\n * @param deps The memoization deps array to use when evaluating spec changes\n */\nexport function useDrag(specArg, deps) {\n  const spec = useOptionalFactory(specArg, deps);\n  invariant(!spec.begin, `useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)`);\n  const monitor = useDragSourceMonitor();\n  const connector = useDragSourceConnector(spec.options, spec.previewOptions);\n  useRegisteredDragSource(spec, monitor, connector);\n  return [useCollectedProps(spec.collect, monitor, connector), useConnectDragSource(connector), useConnectDragPreview(connector)];\n}\n\n//# sourceMappingURL=useDrag.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}