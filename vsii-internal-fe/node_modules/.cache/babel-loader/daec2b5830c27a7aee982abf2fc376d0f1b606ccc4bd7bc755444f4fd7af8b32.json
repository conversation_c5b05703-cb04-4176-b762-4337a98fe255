{"ast": null, "code": "import { getPickersLocalization } from './utils/getPickersLocalization'; // Translation map for Clock Label\n\nconst timeViews = {\n  hours: 'часы',\n  minutes: 'минуты',\n  seconds: 'секунды'\n}; // maps PickersToolbar[\"viewType\"] to its translation\n\nconst viewTypes = {\n  calendar: 'календарный',\n  clock: 'часовой'\n};\nconst ruRUPickers = {\n  // Calendar navigation\n  previousMonth: 'Предыдущий месяц',\n  nextMonth: 'Следующий месяц',\n  // View navigation\n  openPreviousView: 'открыть предыдущий вид',\n  openNextView: 'открыть следующий вид',\n  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'открыт годовой вид, переключить на календарный вид' : 'открыт календарный вид, переключить на годовой вид',\n  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `Открыт текстовый вид, перейти на ${viewTypes[viewType]} вид` : `Открыт ${viewTypes[viewType]} вид, перейти на текстовый вид`,\n  // DateRange placeholders\n  start: 'Начало',\n  end: 'Конец',\n  // Action bar\n  cancelButtonLabel: 'Отмена',\n  clearButtonLabel: 'Очистить',\n  okButtonLabel: 'Ок',\n  todayButtonLabel: 'Сегодня',\n  // Toolbar titles\n  datePickerDefaultToolbarTitle: 'Выбрать дату',\n  dateTimePickerDefaultToolbarTitle: 'Выбрать дату и время',\n  timePickerDefaultToolbarTitle: 'Выбрать время',\n  dateRangePickerDefaultToolbarTitle: 'Выбрать период',\n  // Clock labels\n  clockLabelText: (view, time, adapter) => `Выбрать ${timeViews[view]}. ${time === null ? 'Время не выбрано' : `Выбрано время ${adapter.format(time, 'fullTime')}`}`,\n  hoursClockNumberText: hours => `${hours} часов`,\n  minutesClockNumberText: minutes => `${minutes} минут`,\n  secondsClockNumberText: seconds => `${seconds} секунд`,\n  // Open picker labels\n  openDatePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Выберите дату, выбрана дата ${utils.format(value, 'fullDate')}` : 'Выберите дату',\n  openTimePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Выберите время, выбрано время ${utils.format(value, 'fullTime')}` : 'Выберите время',\n  // Table labels\n  timeTableLabel: 'выбрать время',\n  dateTableLabel: 'выбрать дату'\n};\nexport const ruRU = getPickersLocalization(ruRUPickers);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}