{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/RankThead.tsx\";\nimport { FormattedMessage } from 'react-intl';\n\n// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\n\n// project imports\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { PERMISSIONS } from 'constants/Permission';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ManageRankThead = () => {\n  const {\n    rankPermission\n  } = PERMISSIONS.admin;\n  const {\n    Manage_rank\n  } = TEXT_CONFIG_SCREEN.administration;\n  return /*#__PURE__*/_jsxDEV(TableHead, {\n    children: /*#__PURE__*/_jsxDEV(TableRow, {\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: Manage_rank + 'no'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: Manage_rank + 'rank-name'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: Manage_rank + 'note'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: Manage_rank + 'last-update'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: Manage_rank + 'user-update'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this), checkAllowedPermission(rankPermission.edit) && /*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"center\",\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: Manage_rank + 'action'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 9\n  }, this);\n};\n_c = ManageRankThead;\nexport default ManageRankThead;\nvar _c;\n$RefreshReg$(_c, \"ManageRankThead\");", "map": {"version": 3, "names": ["FormattedMessage", "TableCell", "TableHead", "TableRow", "checkAllowedPermission", "PERMISSIONS", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "ManageRankThead", "rankPermission", "admin", "Manage_rank", "administration", "children", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "edit", "align", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/RankThead.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\n\n// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\n\n// project imports\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { PERMISSIONS } from 'constants/Permission';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\n\nconst ManageRankThead = () => {\n    const { rankPermission } = PERMISSIONS.admin;\n\n    const { Manage_rank } = TEXT_CONFIG_SCREEN.administration;\n\n    return (\n        <TableHead>\n            <TableRow>\n                <TableCell>\n                    <FormattedMessage id={Manage_rank + 'no'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={Manage_rank + 'rank-name'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={Manage_rank + 'note'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={Manage_rank + 'last-update'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={Manage_rank + 'user-update'} />\n                </TableCell>\n                {checkAllowedPermission(rankPermission.edit) && (\n                    <TableCell align=\"center\">\n                        <FormattedMessage id={Manage_rank + 'action'} />\n                    </TableCell>\n                )}\n            </TableRow>\n        </TableHead>\n    );\n};\n\nexport default ManageRankThead;\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;;AAE9D;AACA,SAASC,sBAAsB,QAAQ,qBAAqB;AAC5D,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC1B,MAAM;IAAEC;EAAe,CAAC,GAAGL,WAAW,CAACM,KAAK;EAE5C,MAAM;IAAEC;EAAY,CAAC,GAAGN,kBAAkB,CAACO,cAAc;EAEzD,oBACIL,OAAA,CAACN,SAAS;IAAAY,QAAA,eACNN,OAAA,CAACL,QAAQ;MAAAW,QAAA,gBACLN,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,WAAW,GAAG;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACZX,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,WAAW,GAAG;QAAY;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACZX,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,WAAW,GAAG;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eACZX,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,WAAW,GAAG;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eACZX,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,WAAW,GAAG;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,EACXf,sBAAsB,CAACM,cAAc,CAACU,IAAI,CAAC,iBACxCZ,OAAA,CAACP,SAAS;QAACoB,KAAK,EAAC,QAAQ;QAAAP,QAAA,eACrBN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,WAAW,GAAG;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CACd;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEpB,CAAC;AAACG,EAAA,GA/BIb,eAAe;AAiCrB,eAAeA,eAAe;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}