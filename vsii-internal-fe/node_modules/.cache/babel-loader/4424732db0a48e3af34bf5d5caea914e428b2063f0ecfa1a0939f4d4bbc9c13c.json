{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/MonthlyBillable.tsx\";\n// third party\nimport { FormattedMessage } from 'react-intl';\n\n// material-ui\n\n// project imports\nimport { Select } from 'components/extended/Form';\nimport { DEFAULT_VALUE_OPTION_SELECT, MONTHLY_BILLABLE } from 'constants/Common';\nimport { searchFormConfig } from './Config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MonthlyBillable = props => {\n  const {\n    disabled,\n    handleChange,\n    required\n  } = props;\n  return /*#__PURE__*/_jsxDEV(Select, {\n    required: required,\n    disabled: disabled,\n    selects: [DEFAULT_VALUE_OPTION_SELECT, ...MONTHLY_BILLABLE],\n    handleChange: handleChange,\n    name: searchFormConfig.month.name,\n    label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n      id: searchFormConfig.month.label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 20\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 9\n  }, this);\n};\n_c = MonthlyBillable;\nexport default MonthlyBillable;\nvar _c;\n$RefreshReg$(_c, \"MonthlyBillable\");", "map": {"version": 3, "names": ["FormattedMessage", "Select", "DEFAULT_VALUE_OPTION_SELECT", "MONTHLY_BILLABLE", "searchFormConfig", "jsxDEV", "_jsxDEV", "MonthlyBillable", "props", "disabled", "handleChange", "required", "selects", "name", "month", "label", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/MonthlyBillable.tsx"], "sourcesContent": ["// third party\nimport { FormattedMessage } from 'react-intl';\n\n// material-ui\nimport { SelectChangeEvent } from '@mui/material';\n\n// project imports\nimport { Select } from 'components/extended/Form';\nimport { DEFAULT_VALUE_OPTION_SELECT, MONTHLY_BILLABLE } from 'constants/Common';\nimport { searchFormConfig } from './Config';\n\ninterface IMonthlyBillableProps {\n    disabled?: boolean;\n    handleChange?: (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => void;\n    required?: boolean;\n}\n\nconst MonthlyBillable = (props: IMonthlyBillableProps) => {\n    const { disabled, handleChange, required } = props;\n    return (\n        <Select\n            required={required}\n            disabled={disabled}\n            selects={[DEFAULT_VALUE_OPTION_SELECT, ...MONTHLY_BILLABLE]}\n            handleChange={handleChange}\n            name={searchFormConfig.month.name}\n            label={<FormattedMessage id={searchFormConfig.month.label} />}\n        />\n    );\n};\n\nexport default MonthlyBillable;\n"], "mappings": ";AAAA;AACA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;;AAGA;AACA,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,2BAA2B,EAAEC,gBAAgB,QAAQ,kBAAkB;AAChF,SAASC,gBAAgB,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ5C,MAAMC,eAAe,GAAIC,KAA4B,IAAK;EACtD,MAAM;IAAEC,QAAQ;IAAEC,YAAY;IAAEC;EAAS,CAAC,GAAGH,KAAK;EAClD,oBACIF,OAAA,CAACL,MAAM;IACHU,QAAQ,EAAEA,QAAS;IACnBF,QAAQ,EAAEA,QAAS;IACnBG,OAAO,EAAE,CAACV,2BAA2B,EAAE,GAAGC,gBAAgB,CAAE;IAC5DO,YAAY,EAAEA,YAAa;IAC3BG,IAAI,EAAET,gBAAgB,CAACU,KAAK,CAACD,IAAK;IAClCE,KAAK,eAAET,OAAA,CAACN,gBAAgB;MAACgB,EAAE,EAAEZ,gBAAgB,CAACU,KAAK,CAACC;IAAM;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjE,CAAC;AAEV,CAAC;AAACC,EAAA,GAZId,eAAe;AAcrB,eAAeA,eAAe;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}