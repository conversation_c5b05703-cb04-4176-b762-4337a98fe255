{"ast": null, "code": "/**\n * https://tc39.es/ecma262/#sec-tostring\n */\nexport function ToString(o) {\n  // Only symbol is irregular...\n  if (typeof o === 'symbol') {\n    throw TypeError('Cannot convert a Symbol value to a string');\n  }\n  return String(o);\n}\n/**\n * https://tc39.es/ecma262/#sec-tonumber\n * @param val\n */\nexport function ToNumber(val) {\n  if (val === undefined) {\n    return NaN;\n  }\n  if (val === null) {\n    return +0;\n  }\n  if (typeof val === 'boolean') {\n    return val ? 1 : +0;\n  }\n  if (typeof val === 'number') {\n    return val;\n  }\n  if (typeof val === 'symbol' || typeof val === 'bigint') {\n    throw new TypeError('Cannot convert symbol/bigint to number');\n  }\n  return Number(val);\n}\n/**\n * https://tc39.es/ecma262/#sec-tointeger\n * @param n\n */\nfunction ToInteger(n) {\n  var number = ToNumber(n);\n  if (isNaN(number) || SameValue(number, -0)) {\n    return 0;\n  }\n  if (isFinite(number)) {\n    return number;\n  }\n  var integer = Math.floor(Math.abs(number));\n  if (number < 0) {\n    integer = -integer;\n  }\n  if (SameValue(integer, -0)) {\n    return 0;\n  }\n  return integer;\n}\n/**\n * https://tc39.es/ecma262/#sec-timeclip\n * @param time\n */\nexport function TimeClip(time) {\n  if (!isFinite(time)) {\n    return NaN;\n  }\n  if (Math.abs(time) > 8.64 * 1e15) {\n    return NaN;\n  }\n  return ToInteger(time);\n}\n/**\n * https://tc39.es/ecma262/#sec-toobject\n * @param arg\n */\nexport function ToObject(arg) {\n  if (arg == null) {\n    throw new TypeError('undefined/null cannot be converted to object');\n  }\n  return Object(arg);\n}\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#sec-samevalue\n * @param x\n * @param y\n */\nexport function SameValue(x, y) {\n  if (Object.is) {\n    return Object.is(x, y);\n  }\n  // SameValue algorithm\n  if (x === y) {\n    // Steps 1-5, 7-10\n    // Steps 6.b-6.e: +0 != -0\n    return x !== 0 || 1 / x === 1 / y;\n  }\n  // Step 6.a: NaN == NaN\n  return x !== x && y !== y;\n}\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#sec-arraycreate\n * @param len\n */\nexport function ArrayCreate(len) {\n  return new Array(len);\n}\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#sec-hasownproperty\n * @param o\n * @param prop\n */\nexport function HasOwnProperty(o, prop) {\n  return Object.prototype.hasOwnProperty.call(o, prop);\n}\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#sec-type\n * @param x\n */\nexport function Type(x) {\n  if (x === null) {\n    return 'Null';\n  }\n  if (typeof x === 'undefined') {\n    return 'Undefined';\n  }\n  if (typeof x === 'function' || typeof x === 'object') {\n    return 'Object';\n  }\n  if (typeof x === 'number') {\n    return 'Number';\n  }\n  if (typeof x === 'boolean') {\n    return 'Boolean';\n  }\n  if (typeof x === 'string') {\n    return 'String';\n  }\n  if (typeof x === 'symbol') {\n    return 'Symbol';\n  }\n  if (typeof x === 'bigint') {\n    return 'BigInt';\n  }\n}\nvar MS_PER_DAY = 86400000;\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#eqn-modulo\n * @param x\n * @param y\n * @return k of the same sign as y\n */\nfunction mod(x, y) {\n  return x - Math.floor(x / y) * y;\n}\n/**\n * https://tc39.es/ecma262/#eqn-Day\n * @param t\n */\nexport function Day(t) {\n  return Math.floor(t / MS_PER_DAY);\n}\n/**\n * https://tc39.es/ecma262/#sec-week-day\n * @param t\n */\nexport function WeekDay(t) {\n  return mod(Day(t) + 4, 7);\n}\n/**\n * https://tc39.es/ecma262/#sec-year-number\n * @param y\n */\nexport function DayFromYear(y) {\n  return Date.UTC(y, 0) / MS_PER_DAY;\n}\n/**\n * https://tc39.es/ecma262/#sec-year-number\n * @param y\n */\nexport function TimeFromYear(y) {\n  return Date.UTC(y, 0);\n}\n/**\n * https://tc39.es/ecma262/#sec-year-number\n * @param t\n */\nexport function YearFromTime(t) {\n  return new Date(t).getUTCFullYear();\n}\nexport function DaysInYear(y) {\n  if (y % 4 !== 0) {\n    return 365;\n  }\n  if (y % 100 !== 0) {\n    return 366;\n  }\n  if (y % 400 !== 0) {\n    return 365;\n  }\n  return 366;\n}\nexport function DayWithinYear(t) {\n  return Day(t) - DayFromYear(YearFromTime(t));\n}\nexport function InLeapYear(t) {\n  return DaysInYear(YearFromTime(t)) === 365 ? 0 : 1;\n}\n/**\n * https://tc39.es/ecma262/#sec-month-number\n * @param t\n */\nexport function MonthFromTime(t) {\n  var dwy = DayWithinYear(t);\n  var leap = InLeapYear(t);\n  if (dwy >= 0 && dwy < 31) {\n    return 0;\n  }\n  if (dwy < 59 + leap) {\n    return 1;\n  }\n  if (dwy < 90 + leap) {\n    return 2;\n  }\n  if (dwy < 120 + leap) {\n    return 3;\n  }\n  if (dwy < 151 + leap) {\n    return 4;\n  }\n  if (dwy < 181 + leap) {\n    return 5;\n  }\n  if (dwy < 212 + leap) {\n    return 6;\n  }\n  if (dwy < 243 + leap) {\n    return 7;\n  }\n  if (dwy < 273 + leap) {\n    return 8;\n  }\n  if (dwy < 304 + leap) {\n    return 9;\n  }\n  if (dwy < 334 + leap) {\n    return 10;\n  }\n  if (dwy < 365 + leap) {\n    return 11;\n  }\n  throw new Error('Invalid time');\n}\nexport function DateFromTime(t) {\n  var dwy = DayWithinYear(t);\n  var mft = MonthFromTime(t);\n  var leap = InLeapYear(t);\n  if (mft === 0) {\n    return dwy + 1;\n  }\n  if (mft === 1) {\n    return dwy - 30;\n  }\n  if (mft === 2) {\n    return dwy - 58 - leap;\n  }\n  if (mft === 3) {\n    return dwy - 89 - leap;\n  }\n  if (mft === 4) {\n    return dwy - 119 - leap;\n  }\n  if (mft === 5) {\n    return dwy - 150 - leap;\n  }\n  if (mft === 6) {\n    return dwy - 180 - leap;\n  }\n  if (mft === 7) {\n    return dwy - 211 - leap;\n  }\n  if (mft === 8) {\n    return dwy - 242 - leap;\n  }\n  if (mft === 9) {\n    return dwy - 272 - leap;\n  }\n  if (mft === 10) {\n    return dwy - 303 - leap;\n  }\n  if (mft === 11) {\n    return dwy - 333 - leap;\n  }\n  throw new Error('Invalid time');\n}\nvar HOURS_PER_DAY = 24;\nvar MINUTES_PER_HOUR = 60;\nvar SECONDS_PER_MINUTE = 60;\nvar MS_PER_SECOND = 1e3;\nvar MS_PER_MINUTE = MS_PER_SECOND * SECONDS_PER_MINUTE;\nvar MS_PER_HOUR = MS_PER_MINUTE * MINUTES_PER_HOUR;\nexport function HourFromTime(t) {\n  return mod(Math.floor(t / MS_PER_HOUR), HOURS_PER_DAY);\n}\nexport function MinFromTime(t) {\n  return mod(Math.floor(t / MS_PER_MINUTE), MINUTES_PER_HOUR);\n}\nexport function SecFromTime(t) {\n  return mod(Math.floor(t / MS_PER_SECOND), SECONDS_PER_MINUTE);\n}\nfunction IsCallable(fn) {\n  return typeof fn === 'function';\n}\n/**\n * The abstract operation OrdinaryHasInstance implements\n * the default algorithm for determining if an object O\n * inherits from the instance object inheritance path\n * provided by constructor C.\n * @param C class\n * @param O object\n * @param internalSlots internalSlots\n */\nexport function OrdinaryHasInstance(C, O, internalSlots) {\n  if (!IsCallable(C)) {\n    return false;\n  }\n  if (internalSlots === null || internalSlots === void 0 ? void 0 : internalSlots.boundTargetFunction) {\n    var BC = internalSlots === null || internalSlots === void 0 ? void 0 : internalSlots.boundTargetFunction;\n    return O instanceof BC;\n  }\n  if (typeof O !== 'object') {\n    return false;\n  }\n  var P = C.prototype;\n  if (typeof P !== 'object') {\n    throw new TypeError('OrdinaryHasInstance called on an object with an invalid prototype property.');\n  }\n  return Object.prototype.isPrototypeOf.call(P, O);\n}\nexport function msFromTime(t) {\n  return mod(t, MS_PER_SECOND);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}