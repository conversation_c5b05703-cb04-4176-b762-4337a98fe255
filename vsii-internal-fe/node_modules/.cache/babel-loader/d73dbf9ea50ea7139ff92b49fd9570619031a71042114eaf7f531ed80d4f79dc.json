{"ast": null, "code": "import{useState}from'react';import{FormattedMessage}from'react-intl';// materia-ui\nimport{Grid,Typography}from'@mui/material';import{useTheme}from'@mui/material/styles';// project imports\nimport OnsiteDetailModal from'./OnsiteDetailModal';import MainCard from'components/cards/MainCard';import{gridSpacing}from'store/constant';// assets\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const RegisterWorkingCalendarType=props=>{const{typeList,month,year}=props;const[isOpennOnsiteDetailModal,setOpennOnsiteDetailModal]=useState(false);const theme=useTheme();return/*#__PURE__*/_jsxs(MainCard,{sx:{'& div .MuiCardContent-root':{padding:'20px 24px !important'},marginBottom:theme.spacing(gridSpacing)},children:[/*#__PURE__*/_jsx(Grid,{container:true,direction:\"row\",justifyContent:\"space-between\",alignItems:\"center\",spacing:2,sx:{overflowX:'auto'},children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:12,children:/*#__PURE__*/_jsx(Grid,{container:true,spacing:2,children:typeList.map((item,index)=>/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:2.4,children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:1,sx:{alignItems:'center',cursor:item.key==='OS'?'pointer':undefined},onClick:()=>item.key==='OS'?setOpennOnsiteDetailModal(true):undefined,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:4,lg:2.4,children:/*#__PURE__*/_jsx(\"div\",{style:{backgroundColor:item.color,display:'flex',justifyContent:'center',alignItems:'center',padding:'2px 10px',borderRadius:'4px'},children:/*#__PURE__*/_jsx(Typography,{style:{color:'black'},children:item.key})})},index),/*#__PURE__*/_jsx(Grid,{item:true,xs:8,zeroMinWidth:true,children:/*#__PURE__*/_jsxs(Typography,{align:\"left\",variant:\"body2\",children:[/*#__PURE__*/_jsx(FormattedMessage,{id:item.value}),\" \",/*#__PURE__*/_jsxs(\"span\",{style:{fontWeight:'800'},children:[\"(\",item.total,\")\"]})]})})]})},index))})})}),/*#__PURE__*/_jsx(OnsiteDetailModal,{isOpen:isOpennOnsiteDetailModal,handleClose:()=>setOpennOnsiteDetailModal(false),month:Number(month),year:year})]});};export default RegisterWorkingCalendarType;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}