{"ast": null, "code": "// Safari 6 and 6.1 for desktop, iPad, and iPhone are the only browsers that\n// have WebKitMutationObserver but not un-prefixed MutationObserver.\n// Must use `global` or `self` instead of `window` to work in both frames and web\n// workers. `global` is a provision of <PERSON><PERSON><PERSON><PERSON>, Mr, Mrs, or Mo<PERSON>.\n/* globals self */const scope = typeof global !== 'undefined' ? global : self;\nconst BrowserMutationObserver = scope.MutationObserver || scope.WebKitMutationObserver;\nexport function makeRequestCallFromTimer(callback) {\n  return function requestCall() {\n    // We dispatch a timeout with a specified delay of 0 for engines that\n    // can reliably accommodate that request. This will usually be snapped\n    // to a 4 milisecond delay, but once we're flushing, there's no delay\n    // between events.\n    const timeoutHandle = setTimeout(handleTimer, 0);\n    // However, since this timer gets frequently dropped in Firefox\n    // workers, we enlist an interval handle that will try to fire\n    // an event 20 times per second until it succeeds.\n    const intervalHandle = setInterval(handleTimer, 50);\n    function handleTimer() {\n      // Whichever timer succeeds will cancel both timers and\n      // execute the callback.\n      clearTimeout(timeoutHandle);\n      clearInterval(intervalHandle);\n      callback();\n    }\n  };\n}\n// To request a high priority event, we induce a mutation observer by toggling\n// the text of a text node between \"1\" and \"-1\".\nexport function makeRequestCallFromMutationObserver(callback) {\n  let toggle = 1;\n  const observer = new BrowserMutationObserver(callback);\n  const node = document.createTextNode('');\n  observer.observe(node, {\n    characterData: true\n  });\n  return function requestCall() {\n    toggle = -toggle;\n    node.data = toggle;\n  };\n}\nexport const makeRequestCall = typeof BrowserMutationObserver === 'function' ?\n// reliably everywhere they are implemented.\n// They are implemented in all modern browsers.\n//\n// - Android 4-4.3\n// - Chrome 26-34\n// - Firefox 14-29\n// - Internet Explorer 11\n// - iPad Safari 6-7.1\n// - iPhone Safari 7-7.1\n// - Safari 6-7\nmakeRequestCallFromMutationObserver :\n// task queue, are implemented in Internet Explorer 10, Safari 5.0-1, and Opera\n// 11-12, and in web workers in many engines.\n// Although message channels yield to any queued rendering and IO tasks, they\n// would be better than imposing the 4ms delay of timers.\n// However, they do not work reliably in Internet Explorer or Safari.\n// Internet Explorer 10 is the only browser that has setImmediate but does\n// not have MutationObservers.\n// Although setImmediate yields to the browser's renderer, it would be\n// preferrable to falling back to setTimeout since it does not have\n// the minimum 4ms penalty.\n// Unfortunately there appears to be a bug in Internet Explorer 10 Mobile (and\n// Desktop to a lesser extent) that renders both setImmediate and\n// MessageChannel useless for the purposes of ASAP.\n// https://github.com/kriskowal/q/issues/396\n// Timers are implemented universally.\n// We fall back to timers in workers in most engines, and in foreground\n// contexts in the following browsers.\n// However, note that even this simple case requires nuances to operate in a\n// broad spectrum of browsers.\n//\n// - Firefox 3-13\n// - Internet Explorer 6-9\n// - iPad Safari 4.3\n// - Lynx 2.8.7\nmakeRequestCallFromTimer;\n\n//# sourceMappingURL=makeRequestCall.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}