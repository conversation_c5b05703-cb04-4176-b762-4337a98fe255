{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/extended/Confirm.tsx\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { useAppDispatch, useAppSelector } from 'app/hooks';\n\n// material ui\nimport { LoadingButton } from '@mui/lab';\nimport { Box, Button, Dialog, Stack, Typography } from '@mui/material';\n\n// project imports\nimport MainCard from 'components/cards/MainCard';\nimport { closeConfirm, confirmSelector } from 'store/slice/confirmSlice';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Confirm = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const confirm = useAppSelector(confirmSelector);\n  const dispatch = useAppDispatch();\n  const handleConfirm = async () => {\n    setLoading(true);\n    await confirm.handleConfirm();\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    keepMounted: false,\n    open: confirm.open,\n    onClose: () => dispatch(closeConfirm()),\n    maxWidth: \"xs\",\n    sx: {\n      '& .MuiDialog-paper': {\n        p: 0,\n        width: confirm.width\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(MainCard, {\n      title: confirm.title,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: '20px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          children: confirm.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 1,\n        justifyContent: \"end\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          color: \"error\",\n          onClick: () => dispatch(closeConfirm()),\n          disabled: loading,\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: \"cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(LoadingButton, {\n          variant: \"contained\",\n          type: \"submit\",\n          loading: loading,\n          onClick: handleConfirm,\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: \"confirm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 9\n  }, this);\n};\n_s(Confirm, \"gJIt2aJy3K5oFbUx1AxVLET1lQc=\", false, function () {\n  return [useAppSelector, useAppDispatch];\n});\n_c = Confirm;\nexport default Confirm;\nvar _c;\n$RefreshReg$(_c, \"Confirm\");", "map": {"version": 3, "names": ["useState", "useAppDispatch", "useAppSelector", "LoadingButton", "Box", "<PERSON><PERSON>", "Dialog", "<PERSON><PERSON>", "Typography", "MainCard", "closeConfirm", "confirmSelector", "FormattedMessage", "jsxDEV", "_jsxDEV", "Confirm", "_s", "loading", "setLoading", "confirm", "dispatch", "handleConfirm", "keepMounted", "open", "onClose", "max<PERSON><PERSON><PERSON>", "sx", "p", "width", "children", "title", "mb", "variant", "content", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "direction", "spacing", "justifyContent", "color", "onClick", "disabled", "id", "type", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/extended/Confirm.tsx"], "sourcesContent": ["import { useState } from 'react';\nimport { useAppDispatch, useAppSelector } from 'app/hooks';\n\n// material ui\nimport { LoadingButton } from '@mui/lab';\nimport { Box, Button, Dialog, Stack, Typography } from '@mui/material';\n\n// project imports\nimport MainCard from 'components/cards/MainCard';\nimport { closeConfirm, confirmSelector } from 'store/slice/confirmSlice';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\n\nconst Confirm = () => {\n    const [loading, setLoading] = useState<boolean>(false);\n\n    const confirm = useAppSelector(confirmSelector);\n    const dispatch = useAppDispatch();\n\n    const handleConfirm = async () => {\n        setLoading(true);\n        await confirm.handleConfirm();\n        setLoading(false);\n    };\n\n    return (\n        <Dialog\n            keepMounted={false}\n            open={confirm.open}\n            onClose={() => dispatch(closeConfirm())}\n            maxWidth=\"xs\"\n            sx={{\n                '& .MuiDialog-paper': { p: 0, width: confirm.width }\n            }}\n        >\n            <MainCard title={confirm.title}>\n                <Box sx={{ mb: '20px' }}>\n                    <Typography variant=\"subtitle1\">{confirm.content}</Typography>\n                </Box>\n                <Stack direction=\"row\" spacing={1} justifyContent=\"end\">\n                    <Button color=\"error\" onClick={() => dispatch(closeConfirm())} disabled={loading}>\n                        <FormattedMessage id=\"cancel\" />\n                    </Button>\n                    <LoadingButton variant=\"contained\" type=\"submit\" loading={loading} onClick={handleConfirm}>\n                        <FormattedMessage id=\"confirm\" />\n                    </LoadingButton>\n                </Stack>\n            </MainCard>\n        </Dialog>\n    );\n};\nexport default Confirm;\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,cAAc,EAAEC,cAAc,QAAQ,WAAW;;AAE1D;AACA,SAASC,aAAa,QAAQ,UAAU;AACxC,SAASC,GAAG,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,QAAQ,eAAe;;AAEtE;AACA,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,YAAY,EAAEC,eAAe,QAAQ,0BAA0B;;AAExE;AACA,SAASC,gBAAgB,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAU,KAAK,CAAC;EAEtD,MAAMmB,OAAO,GAAGjB,cAAc,CAACS,eAAe,CAAC;EAC/C,MAAMS,QAAQ,GAAGnB,cAAc,CAAC,CAAC;EAEjC,MAAMoB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9BH,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMC,OAAO,CAACE,aAAa,CAAC,CAAC;IAC7BH,UAAU,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,oBACIJ,OAAA,CAACR,MAAM;IACHgB,WAAW,EAAE,KAAM;IACnBC,IAAI,EAAEJ,OAAO,CAACI,IAAK;IACnBC,OAAO,EAAEA,CAAA,KAAMJ,QAAQ,CAACV,YAAY,CAAC,CAAC,CAAE;IACxCe,QAAQ,EAAC,IAAI;IACbC,EAAE,EAAE;MACA,oBAAoB,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,KAAK,EAAET,OAAO,CAACS;MAAM;IACvD,CAAE;IAAAC,QAAA,eAEFf,OAAA,CAACL,QAAQ;MAACqB,KAAK,EAAEX,OAAO,CAACW,KAAM;MAAAD,QAAA,gBAC3Bf,OAAA,CAACV,GAAG;QAACsB,EAAE,EAAE;UAAEK,EAAE,EAAE;QAAO,CAAE;QAAAF,QAAA,eACpBf,OAAA,CAACN,UAAU;UAACwB,OAAO,EAAC,WAAW;UAAAH,QAAA,EAAEV,OAAO,CAACc;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eACNvB,OAAA,CAACP,KAAK;QAAC+B,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAACC,cAAc,EAAC,KAAK;QAAAX,QAAA,gBACnDf,OAAA,CAACT,MAAM;UAACoC,KAAK,EAAC,OAAO;UAACC,OAAO,EAAEA,CAAA,KAAMtB,QAAQ,CAACV,YAAY,CAAC,CAAC,CAAE;UAACiC,QAAQ,EAAE1B,OAAQ;UAAAY,QAAA,eAC7Ef,OAAA,CAACF,gBAAgB;YAACgC,EAAE,EAAC;UAAQ;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACTvB,OAAA,CAACX,aAAa;UAAC6B,OAAO,EAAC,WAAW;UAACa,IAAI,EAAC,QAAQ;UAAC5B,OAAO,EAAEA,OAAQ;UAACyB,OAAO,EAAErB,aAAc;UAAAQ,QAAA,eACtFf,OAAA,CAACF,gBAAgB;YAACgC,EAAE,EAAC;UAAS;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEjB,CAAC;AAACrB,EAAA,CArCID,OAAO;EAAA,QAGOb,cAAc,EACbD,cAAc;AAAA;AAAA6C,EAAA,GAJ7B/B,OAAO;AAsCb,eAAeA,OAAO;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}