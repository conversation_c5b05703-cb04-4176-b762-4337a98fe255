{"ast": null, "code": "import React from'react';import{FormattedMessage}from'react-intl';import{isEmpty}from'lodash';// material-ui\nimport{FormHelperText,MenuItem,Select as MuiSelect,styled,Typography}from'@mui/material';// react-hook-form\nimport{Controller,useFormContext}from'react-hook-form';// project imports\nimport Label from'./Label';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SelectWrapper=styled('div')({position:'relative',width:'100%'});const Select=props=>{const{name,label,handleChange,handleChangeFullOption,selects,disabled,isMultipleLanguage,required,isControl=true,valueSelect,inRow,selectWidth,sx,placeholder,...other}=props;const methods=useFormContext();// Events\nconst handleChangeSelect=event=>{handleChange&&handleChange(event);};return isControl?/*#__PURE__*/_jsx(Controller,{name:name,control:methods.control,render:_ref=>{let{field:{value,ref,onChange,...field},fieldState:{error}}=_ref;return/*#__PURE__*/_jsxs(SelectWrapper,{sx:inRow?{display:'flex',flexDirection:'row',alignItems:'center',gap:5}:undefined,children:[/*#__PURE__*/_jsx(Label,{name:name,label:label,required:required}),/*#__PURE__*/_jsx(MuiSelectStyle,{...field,...other,disabled:disabled,displayEmpty:true,size:\"small\",onChange:event=>{handleChangeSelect(event);onChange(event.target.value);},error:!!error,fullWidth:true,value:value,MenuProps:MenuProps,renderValue:isEmpty(value)&&placeholder?()=>/*#__PURE__*/_jsx(Typography,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:placeholder})}):undefined,ref:ref,sx:selectWidth?{width:selectWidth,...sx}:sx,children:selects===null||selects===void 0?void 0:selects.map((option,key)=>/*#__PURE__*/_jsx(MenuItem,{value:option.value,disabled:option===null||option===void 0?void 0:option.disabled,onClick:()=>handleChangeFullOption&&handleChangeFullOption(option),children:isMultipleLanguage||!option.value?/*#__PURE__*/_jsx(FormattedMessage,{id:option.label}):option.label},key))}),/*#__PURE__*/_jsx(FormHelperText,{sx:{color:'#f44336'},children:error&&/*#__PURE__*/_jsx(FormattedMessage,{id:error.message})})]});}}):/*#__PURE__*/_jsxs(SelectWrapper,{children:[/*#__PURE__*/_jsx(Label,{name:name,label:label,required:required}),/*#__PURE__*/_jsx(MuiSelectStyle,{...other,disabled:disabled,displayEmpty:true,size:\"small\",onChange:handleChangeSelect,fullWidth:true,value:valueSelect,MenuProps:MenuProps,sx:sx,renderValue:isEmpty(valueSelect)&&placeholder?()=>/*#__PURE__*/_jsx(Typography,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:placeholder})}):undefined,children:selects===null||selects===void 0?void 0:selects.map((option,key)=>/*#__PURE__*/_jsx(MenuItem,{value:option.value,disabled:option===null||option===void 0?void 0:option.disabled,onClick:()=>handleChangeFullOption&&handleChangeFullOption(option),children:isMultipleLanguage||!option.value?/*#__PURE__*/_jsx(FormattedMessage,{id:option.label}):option.label},key))})]});};const MenuProps={PaperProps:{style:{maxHeight:250}}};const MuiSelectStyle=styled(MuiSelect)({});export default Select;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}