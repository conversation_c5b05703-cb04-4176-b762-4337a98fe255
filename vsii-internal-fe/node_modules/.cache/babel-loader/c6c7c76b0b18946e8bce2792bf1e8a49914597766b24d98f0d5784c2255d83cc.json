{"ast": null, "code": "function n(n) {\n  for (var r = arguments.length, t = Array(r > 1 ? r - 1 : 0), e = 1; e < r; e++) t[e - 1] = arguments[e];\n  if (\"production\" !== process.env.NODE_ENV) {\n    var i = Y[n],\n      o = i ? \"function\" == typeof i ? i.apply(null, t) : i : \"unknown error nr: \" + n;\n    throw Error(\"[Immer] \" + o);\n  }\n  throw Error(\"[Immer] minified error nr: \" + n + (t.length ? \" \" + t.map(function (n) {\n    return \"'\" + n + \"'\";\n  }).join(\",\") : \"\") + \". Find the full error at: https://bit.ly/3cXEKWf\");\n}\nfunction r(n) {\n  return !!n && !!n[Q];\n}\nfunction t(n) {\n  var r;\n  return !!n && (function (n) {\n    if (!n || \"object\" != typeof n) return !1;\n    var r = Object.getPrototypeOf(n);\n    if (null === r) return !0;\n    var t = Object.hasOwnProperty.call(r, \"constructor\") && r.constructor;\n    return t === Object || \"function\" == typeof t && Function.toString.call(t) === Z;\n  }(n) || Array.isArray(n) || !!n[L] || !!(null === (r = n.constructor) || void 0 === r ? void 0 : r[L]) || s(n) || v(n));\n}\nfunction e(t) {\n  return r(t) || n(23, t), t[Q].t;\n}\nfunction i(n, r, t) {\n  void 0 === t && (t = !1), 0 === o(n) ? (t ? Object.keys : nn)(n).forEach(function (e) {\n    t && \"symbol\" == typeof e || r(e, n[e], n);\n  }) : n.forEach(function (t, e) {\n    return r(e, t, n);\n  });\n}\nfunction o(n) {\n  var r = n[Q];\n  return r ? r.i > 3 ? r.i - 4 : r.i : Array.isArray(n) ? 1 : s(n) ? 2 : v(n) ? 3 : 0;\n}\nfunction u(n, r) {\n  return 2 === o(n) ? n.has(r) : Object.prototype.hasOwnProperty.call(n, r);\n}\nfunction a(n, r) {\n  return 2 === o(n) ? n.get(r) : n[r];\n}\nfunction f(n, r, t) {\n  var e = o(n);\n  2 === e ? n.set(r, t) : 3 === e ? n.add(t) : n[r] = t;\n}\nfunction c(n, r) {\n  return n === r ? 0 !== n || 1 / n == 1 / r : n != n && r != r;\n}\nfunction s(n) {\n  return X && n instanceof Map;\n}\nfunction v(n) {\n  return q && n instanceof Set;\n}\nfunction p(n) {\n  return n.o || n.t;\n}\nfunction l(n) {\n  if (Array.isArray(n)) return Array.prototype.slice.call(n);\n  var r = rn(n);\n  delete r[Q];\n  for (var t = nn(r), e = 0; e < t.length; e++) {\n    var i = t[e],\n      o = r[i];\n    !1 === o.writable && (o.writable = !0, o.configurable = !0), (o.get || o.set) && (r[i] = {\n      configurable: !0,\n      writable: !0,\n      enumerable: o.enumerable,\n      value: n[i]\n    });\n  }\n  return Object.create(Object.getPrototypeOf(n), r);\n}\nfunction d(n, e) {\n  return void 0 === e && (e = !1), y(n) || r(n) || !t(n) || (o(n) > 1 && (n.set = n.add = n.clear = n.delete = h), Object.freeze(n), e && i(n, function (n, r) {\n    return d(r, !0);\n  }, !0)), n;\n}\nfunction h() {\n  n(2);\n}\nfunction y(n) {\n  return null == n || \"object\" != typeof n || Object.isFrozen(n);\n}\nfunction b(r) {\n  var t = tn[r];\n  return t || n(18, r), t;\n}\nfunction m(n, r) {\n  tn[n] || (tn[n] = r);\n}\nfunction _() {\n  return \"production\" === process.env.NODE_ENV || U || n(0), U;\n}\nfunction j(n, r) {\n  r && (b(\"Patches\"), n.u = [], n.s = [], n.v = r);\n}\nfunction g(n) {\n  O(n), n.p.forEach(S), n.p = null;\n}\nfunction O(n) {\n  n === U && (U = n.l);\n}\nfunction w(n) {\n  return U = {\n    p: [],\n    l: U,\n    h: n,\n    m: !0,\n    _: 0\n  };\n}\nfunction S(n) {\n  var r = n[Q];\n  0 === r.i || 1 === r.i ? r.j() : r.g = !0;\n}\nfunction P(r, e) {\n  e._ = e.p.length;\n  var i = e.p[0],\n    o = void 0 !== r && r !== i;\n  return e.h.O || b(\"ES5\").S(e, r, o), o ? (i[Q].P && (g(e), n(4)), t(r) && (r = M(e, r), e.l || x(e, r)), e.u && b(\"Patches\").M(i[Q].t, r, e.u, e.s)) : r = M(e, i, []), g(e), e.u && e.v(e.u, e.s), r !== H ? r : void 0;\n}\nfunction M(n, r, t) {\n  if (y(r)) return r;\n  var e = r[Q];\n  if (!e) return i(r, function (i, o) {\n    return A(n, e, r, i, o, t);\n  }, !0), r;\n  if (e.A !== n) return r;\n  if (!e.P) return x(n, e.t, !0), e.t;\n  if (!e.I) {\n    e.I = !0, e.A._--;\n    var o = 4 === e.i || 5 === e.i ? e.o = l(e.k) : e.o,\n      u = o,\n      a = !1;\n    3 === e.i && (u = new Set(o), o.clear(), a = !0), i(u, function (r, i) {\n      return A(n, e, o, r, i, t, a);\n    }), x(n, o, !1), t && n.u && b(\"Patches\").N(e, t, n.u, n.s);\n  }\n  return e.o;\n}\nfunction A(e, i, o, a, c, s, v) {\n  if (\"production\" !== process.env.NODE_ENV && c === o && n(5), r(c)) {\n    var p = M(e, c, s && i && 3 !== i.i && !u(i.R, a) ? s.concat(a) : void 0);\n    if (f(o, a, p), !r(p)) return;\n    e.m = !1;\n  } else v && o.add(c);\n  if (t(c) && !y(c)) {\n    if (!e.h.D && e._ < 1) return;\n    M(e, c), i && i.A.l || x(e, c);\n  }\n}\nfunction x(n, r, t) {\n  void 0 === t && (t = !1), !n.l && n.h.D && n.m && d(r, t);\n}\nfunction z(n, r) {\n  var t = n[Q];\n  return (t ? p(t) : n)[r];\n}\nfunction I(n, r) {\n  if (r in n) for (var t = Object.getPrototypeOf(n); t;) {\n    var e = Object.getOwnPropertyDescriptor(t, r);\n    if (e) return e;\n    t = Object.getPrototypeOf(t);\n  }\n}\nfunction k(n) {\n  n.P || (n.P = !0, n.l && k(n.l));\n}\nfunction E(n) {\n  n.o || (n.o = l(n.t));\n}\nfunction N(n, r, t) {\n  var e = s(r) ? b(\"MapSet\").F(r, t) : v(r) ? b(\"MapSet\").T(r, t) : n.O ? function (n, r) {\n    var t = Array.isArray(n),\n      e = {\n        i: t ? 1 : 0,\n        A: r ? r.A : _(),\n        P: !1,\n        I: !1,\n        R: {},\n        l: r,\n        t: n,\n        k: null,\n        o: null,\n        j: null,\n        C: !1\n      },\n      i = e,\n      o = en;\n    t && (i = [e], o = on);\n    var u = Proxy.revocable(i, o),\n      a = u.revoke,\n      f = u.proxy;\n    return e.k = f, e.j = a, f;\n  }(r, t) : b(\"ES5\").J(r, t);\n  return (t ? t.A : _()).p.push(e), e;\n}\nfunction R(e) {\n  return r(e) || n(22, e), function n(r) {\n    if (!t(r)) return r;\n    var e,\n      u = r[Q],\n      c = o(r);\n    if (u) {\n      if (!u.P && (u.i < 4 || !b(\"ES5\").K(u))) return u.t;\n      u.I = !0, e = D(r, c), u.I = !1;\n    } else e = D(r, c);\n    return i(e, function (r, t) {\n      u && a(u.t, r) === t || f(e, r, n(t));\n    }), 3 === c ? new Set(e) : e;\n  }(e);\n}\nfunction D(n, r) {\n  switch (r) {\n    case 2:\n      return new Map(n);\n    case 3:\n      return Array.from(n);\n  }\n  return l(n);\n}\nfunction F() {\n  function t(n, r) {\n    var t = s[n];\n    return t ? t.enumerable = r : s[n] = t = {\n      configurable: !0,\n      enumerable: r,\n      get: function () {\n        var r = this[Q];\n        return \"production\" !== process.env.NODE_ENV && f(r), en.get(r, n);\n      },\n      set: function (r) {\n        var t = this[Q];\n        \"production\" !== process.env.NODE_ENV && f(t), en.set(t, n, r);\n      }\n    }, t;\n  }\n  function e(n) {\n    for (var r = n.length - 1; r >= 0; r--) {\n      var t = n[r][Q];\n      if (!t.P) switch (t.i) {\n        case 5:\n          a(t) && k(t);\n          break;\n        case 4:\n          o(t) && k(t);\n      }\n    }\n  }\n  function o(n) {\n    for (var r = n.t, t = n.k, e = nn(t), i = e.length - 1; i >= 0; i--) {\n      var o = e[i];\n      if (o !== Q) {\n        var a = r[o];\n        if (void 0 === a && !u(r, o)) return !0;\n        var f = t[o],\n          s = f && f[Q];\n        if (s ? s.t !== a : !c(f, a)) return !0;\n      }\n    }\n    var v = !!r[Q];\n    return e.length !== nn(r).length + (v ? 0 : 1);\n  }\n  function a(n) {\n    var r = n.k;\n    if (r.length !== n.t.length) return !0;\n    var t = Object.getOwnPropertyDescriptor(r, r.length - 1);\n    if (t && !t.get) return !0;\n    for (var e = 0; e < r.length; e++) if (!r.hasOwnProperty(e)) return !0;\n    return !1;\n  }\n  function f(r) {\n    r.g && n(3, JSON.stringify(p(r)));\n  }\n  var s = {};\n  m(\"ES5\", {\n    J: function (n, r) {\n      var e = Array.isArray(n),\n        i = function (n, r) {\n          if (n) {\n            for (var e = Array(r.length), i = 0; i < r.length; i++) Object.defineProperty(e, \"\" + i, t(i, !0));\n            return e;\n          }\n          var o = rn(r);\n          delete o[Q];\n          for (var u = nn(o), a = 0; a < u.length; a++) {\n            var f = u[a];\n            o[f] = t(f, n || !!o[f].enumerable);\n          }\n          return Object.create(Object.getPrototypeOf(r), o);\n        }(e, n),\n        o = {\n          i: e ? 5 : 4,\n          A: r ? r.A : _(),\n          P: !1,\n          I: !1,\n          R: {},\n          l: r,\n          t: n,\n          k: i,\n          o: null,\n          g: !1,\n          C: !1\n        };\n      return Object.defineProperty(i, Q, {\n        value: o,\n        writable: !0\n      }), i;\n    },\n    S: function (n, t, o) {\n      o ? r(t) && t[Q].A === n && e(n.p) : (n.u && function n(r) {\n        if (r && \"object\" == typeof r) {\n          var t = r[Q];\n          if (t) {\n            var e = t.t,\n              o = t.k,\n              f = t.R,\n              c = t.i;\n            if (4 === c) i(o, function (r) {\n              r !== Q && (void 0 !== e[r] || u(e, r) ? f[r] || n(o[r]) : (f[r] = !0, k(t)));\n            }), i(e, function (n) {\n              void 0 !== o[n] || u(o, n) || (f[n] = !1, k(t));\n            });else if (5 === c) {\n              if (a(t) && (k(t), f.length = !0), o.length < e.length) for (var s = o.length; s < e.length; s++) f[s] = !1;else for (var v = e.length; v < o.length; v++) f[v] = !0;\n              for (var p = Math.min(o.length, e.length), l = 0; l < p; l++) o.hasOwnProperty(l) || (f[l] = !0), void 0 === f[l] && n(o[l]);\n            }\n          }\n        }\n      }(n.p[0]), e(n.p));\n    },\n    K: function (n) {\n      return 4 === n.i ? o(n) : a(n);\n    }\n  });\n}\nfunction T() {\n  function e(n) {\n    if (!t(n)) return n;\n    if (Array.isArray(n)) return n.map(e);\n    if (s(n)) return new Map(Array.from(n.entries()).map(function (n) {\n      return [n[0], e(n[1])];\n    }));\n    if (v(n)) return new Set(Array.from(n).map(e));\n    var r = Object.create(Object.getPrototypeOf(n));\n    for (var i in n) r[i] = e(n[i]);\n    return u(n, L) && (r[L] = n[L]), r;\n  }\n  function f(n) {\n    return r(n) ? e(n) : n;\n  }\n  var c = \"add\";\n  m(\"Patches\", {\n    $: function (r, t) {\n      return t.forEach(function (t) {\n        for (var i = t.path, u = t.op, f = r, s = 0; s < i.length - 1; s++) {\n          var v = o(f),\n            p = i[s];\n          \"string\" != typeof p && \"number\" != typeof p && (p = \"\" + p), 0 !== v && 1 !== v || \"__proto__\" !== p && \"constructor\" !== p || n(24), \"function\" == typeof f && \"prototype\" === p && n(24), \"object\" != typeof (f = a(f, p)) && n(15, i.join(\"/\"));\n        }\n        var l = o(f),\n          d = e(t.value),\n          h = i[i.length - 1];\n        switch (u) {\n          case \"replace\":\n            switch (l) {\n              case 2:\n                return f.set(h, d);\n              case 3:\n                n(16);\n              default:\n                return f[h] = d;\n            }\n          case c:\n            switch (l) {\n              case 1:\n                return \"-\" === h ? f.push(d) : f.splice(h, 0, d);\n              case 2:\n                return f.set(h, d);\n              case 3:\n                return f.add(d);\n              default:\n                return f[h] = d;\n            }\n          case \"remove\":\n            switch (l) {\n              case 1:\n                return f.splice(h, 1);\n              case 2:\n                return f.delete(h);\n              case 3:\n                return f.delete(t.value);\n              default:\n                return delete f[h];\n            }\n          default:\n            n(17, u);\n        }\n      }), r;\n    },\n    N: function (n, r, t, e) {\n      switch (n.i) {\n        case 0:\n        case 4:\n        case 2:\n          return function (n, r, t, e) {\n            var o = n.t,\n              s = n.o;\n            i(n.R, function (n, i) {\n              var v = a(o, n),\n                p = a(s, n),\n                l = i ? u(o, n) ? \"replace\" : c : \"remove\";\n              if (v !== p || \"replace\" !== l) {\n                var d = r.concat(n);\n                t.push(\"remove\" === l ? {\n                  op: l,\n                  path: d\n                } : {\n                  op: l,\n                  path: d,\n                  value: p\n                }), e.push(l === c ? {\n                  op: \"remove\",\n                  path: d\n                } : \"remove\" === l ? {\n                  op: c,\n                  path: d,\n                  value: f(v)\n                } : {\n                  op: \"replace\",\n                  path: d,\n                  value: f(v)\n                });\n              }\n            });\n          }(n, r, t, e);\n        case 5:\n        case 1:\n          return function (n, r, t, e) {\n            var i = n.t,\n              o = n.R,\n              u = n.o;\n            if (u.length < i.length) {\n              var a = [u, i];\n              i = a[0], u = a[1];\n              var s = [e, t];\n              t = s[0], e = s[1];\n            }\n            for (var v = 0; v < i.length; v++) if (o[v] && u[v] !== i[v]) {\n              var p = r.concat([v]);\n              t.push({\n                op: \"replace\",\n                path: p,\n                value: f(u[v])\n              }), e.push({\n                op: \"replace\",\n                path: p,\n                value: f(i[v])\n              });\n            }\n            for (var l = i.length; l < u.length; l++) {\n              var d = r.concat([l]);\n              t.push({\n                op: c,\n                path: d,\n                value: f(u[l])\n              });\n            }\n            i.length < u.length && e.push({\n              op: \"replace\",\n              path: r.concat([\"length\"]),\n              value: i.length\n            });\n          }(n, r, t, e);\n        case 3:\n          return function (n, r, t, e) {\n            var i = n.t,\n              o = n.o,\n              u = 0;\n            i.forEach(function (n) {\n              if (!o.has(n)) {\n                var i = r.concat([u]);\n                t.push({\n                  op: \"remove\",\n                  path: i,\n                  value: n\n                }), e.unshift({\n                  op: c,\n                  path: i,\n                  value: n\n                });\n              }\n              u++;\n            }), u = 0, o.forEach(function (n) {\n              if (!i.has(n)) {\n                var o = r.concat([u]);\n                t.push({\n                  op: c,\n                  path: o,\n                  value: n\n                }), e.unshift({\n                  op: \"remove\",\n                  path: o,\n                  value: n\n                });\n              }\n              u++;\n            });\n          }(n, r, t, e);\n      }\n    },\n    M: function (n, r, t, e) {\n      t.push({\n        op: \"replace\",\n        path: [],\n        value: r === H ? void 0 : r\n      }), e.push({\n        op: \"replace\",\n        path: [],\n        value: n\n      });\n    }\n  });\n}\nfunction C() {\n  function r(n, r) {\n    function t() {\n      this.constructor = n;\n    }\n    a(n, r), n.prototype = (t.prototype = r.prototype, new t());\n  }\n  function e(n) {\n    n.o || (n.R = new Map(), n.o = new Map(n.t));\n  }\n  function o(n) {\n    n.o || (n.o = new Set(), n.t.forEach(function (r) {\n      if (t(r)) {\n        var e = N(n.A.h, r, n);\n        n.p.set(r, e), n.o.add(e);\n      } else n.o.add(r);\n    }));\n  }\n  function u(r) {\n    r.g && n(3, JSON.stringify(p(r)));\n  }\n  var a = function (n, r) {\n      return (a = Object.setPrototypeOf || {\n        __proto__: []\n      } instanceof Array && function (n, r) {\n        n.__proto__ = r;\n      } || function (n, r) {\n        for (var t in r) r.hasOwnProperty(t) && (n[t] = r[t]);\n      })(n, r);\n    },\n    f = function () {\n      function n(n, r) {\n        return this[Q] = {\n          i: 2,\n          l: r,\n          A: r ? r.A : _(),\n          P: !1,\n          I: !1,\n          o: void 0,\n          R: void 0,\n          t: n,\n          k: this,\n          C: !1,\n          g: !1\n        }, this;\n      }\n      r(n, Map);\n      var o = n.prototype;\n      return Object.defineProperty(o, \"size\", {\n        get: function () {\n          return p(this[Q]).size;\n        }\n      }), o.has = function (n) {\n        return p(this[Q]).has(n);\n      }, o.set = function (n, r) {\n        var t = this[Q];\n        return u(t), p(t).has(n) && p(t).get(n) === r || (e(t), k(t), t.R.set(n, !0), t.o.set(n, r), t.R.set(n, !0)), this;\n      }, o.delete = function (n) {\n        if (!this.has(n)) return !1;\n        var r = this[Q];\n        return u(r), e(r), k(r), r.t.has(n) ? r.R.set(n, !1) : r.R.delete(n), r.o.delete(n), !0;\n      }, o.clear = function () {\n        var n = this[Q];\n        u(n), p(n).size && (e(n), k(n), n.R = new Map(), i(n.t, function (r) {\n          n.R.set(r, !1);\n        }), n.o.clear());\n      }, o.forEach = function (n, r) {\n        var t = this;\n        p(this[Q]).forEach(function (e, i) {\n          n.call(r, t.get(i), i, t);\n        });\n      }, o.get = function (n) {\n        var r = this[Q];\n        u(r);\n        var i = p(r).get(n);\n        if (r.I || !t(i)) return i;\n        if (i !== r.t.get(n)) return i;\n        var o = N(r.A.h, i, r);\n        return e(r), r.o.set(n, o), o;\n      }, o.keys = function () {\n        return p(this[Q]).keys();\n      }, o.values = function () {\n        var n,\n          r = this,\n          t = this.keys();\n        return (n = {})[V] = function () {\n          return r.values();\n        }, n.next = function () {\n          var n = t.next();\n          return n.done ? n : {\n            done: !1,\n            value: r.get(n.value)\n          };\n        }, n;\n      }, o.entries = function () {\n        var n,\n          r = this,\n          t = this.keys();\n        return (n = {})[V] = function () {\n          return r.entries();\n        }, n.next = function () {\n          var n = t.next();\n          if (n.done) return n;\n          var e = r.get(n.value);\n          return {\n            done: !1,\n            value: [n.value, e]\n          };\n        }, n;\n      }, o[V] = function () {\n        return this.entries();\n      }, n;\n    }(),\n    c = function () {\n      function n(n, r) {\n        return this[Q] = {\n          i: 3,\n          l: r,\n          A: r ? r.A : _(),\n          P: !1,\n          I: !1,\n          o: void 0,\n          t: n,\n          k: this,\n          p: new Map(),\n          g: !1,\n          C: !1\n        }, this;\n      }\n      r(n, Set);\n      var t = n.prototype;\n      return Object.defineProperty(t, \"size\", {\n        get: function () {\n          return p(this[Q]).size;\n        }\n      }), t.has = function (n) {\n        var r = this[Q];\n        return u(r), r.o ? !!r.o.has(n) || !(!r.p.has(n) || !r.o.has(r.p.get(n))) : r.t.has(n);\n      }, t.add = function (n) {\n        var r = this[Q];\n        return u(r), this.has(n) || (o(r), k(r), r.o.add(n)), this;\n      }, t.delete = function (n) {\n        if (!this.has(n)) return !1;\n        var r = this[Q];\n        return u(r), o(r), k(r), r.o.delete(n) || !!r.p.has(n) && r.o.delete(r.p.get(n));\n      }, t.clear = function () {\n        var n = this[Q];\n        u(n), p(n).size && (o(n), k(n), n.o.clear());\n      }, t.values = function () {\n        var n = this[Q];\n        return u(n), o(n), n.o.values();\n      }, t.entries = function () {\n        var n = this[Q];\n        return u(n), o(n), n.o.entries();\n      }, t.keys = function () {\n        return this.values();\n      }, t[V] = function () {\n        return this.values();\n      }, t.forEach = function (n, r) {\n        for (var t = this.values(), e = t.next(); !e.done;) n.call(r, e.value, e.value, this), e = t.next();\n      }, n;\n    }();\n  m(\"MapSet\", {\n    F: function (n, r) {\n      return new f(n, r);\n    },\n    T: function (n, r) {\n      return new c(n, r);\n    }\n  });\n}\nfunction J() {\n  F(), C(), T();\n}\nfunction K(n) {\n  return n;\n}\nfunction $(n) {\n  return n;\n}\nvar G,\n  U,\n  W = \"undefined\" != typeof Symbol && \"symbol\" == typeof Symbol(\"x\"),\n  X = \"undefined\" != typeof Map,\n  q = \"undefined\" != typeof Set,\n  B = \"undefined\" != typeof Proxy && void 0 !== Proxy.revocable && \"undefined\" != typeof Reflect,\n  H = W ? Symbol.for(\"immer-nothing\") : ((G = {})[\"immer-nothing\"] = !0, G),\n  L = W ? Symbol.for(\"immer-draftable\") : \"__$immer_draftable\",\n  Q = W ? Symbol.for(\"immer-state\") : \"__$immer_state\",\n  V = \"undefined\" != typeof Symbol && Symbol.iterator || \"@@iterator\",\n  Y = {\n    0: \"Illegal state\",\n    1: \"Immer drafts cannot have computed properties\",\n    2: \"This object has been frozen and should not be mutated\",\n    3: function (n) {\n      return \"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" + n;\n    },\n    4: \"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n    5: \"Immer forbids circular references\",\n    6: \"The first or second argument to `produce` must be a function\",\n    7: \"The third argument to `produce` must be a function or undefined\",\n    8: \"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n    9: \"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n    10: \"The given draft is already finalized\",\n    11: \"Object.defineProperty() cannot be used on an Immer draft\",\n    12: \"Object.setPrototypeOf() cannot be used on an Immer draft\",\n    13: \"Immer only supports deleting array indices\",\n    14: \"Immer only supports setting array indices and the 'length' property\",\n    15: function (n) {\n      return \"Cannot apply patch, path doesn't resolve: \" + n;\n    },\n    16: 'Sets cannot have \"replace\" patches.',\n    17: function (n) {\n      return \"Unsupported patch operation: \" + n;\n    },\n    18: function (n) {\n      return \"The plugin for '\" + n + \"' has not been loaded into Immer. To enable the plugin, import and call `enable\" + n + \"()` when initializing your application.\";\n    },\n    20: \"Cannot use proxies if Proxy, Proxy.revocable or Reflect are not available\",\n    21: function (n) {\n      return \"produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '\" + n + \"'\";\n    },\n    22: function (n) {\n      return \"'current' expects a draft, got: \" + n;\n    },\n    23: function (n) {\n      return \"'original' expects a draft, got: \" + n;\n    },\n    24: \"Patching reserved attributes like __proto__, prototype and constructor is not allowed\"\n  },\n  Z = \"\" + Object.prototype.constructor,\n  nn = \"undefined\" != typeof Reflect && Reflect.ownKeys ? Reflect.ownKeys : void 0 !== Object.getOwnPropertySymbols ? function (n) {\n    return Object.getOwnPropertyNames(n).concat(Object.getOwnPropertySymbols(n));\n  } : Object.getOwnPropertyNames,\n  rn = Object.getOwnPropertyDescriptors || function (n) {\n    var r = {};\n    return nn(n).forEach(function (t) {\n      r[t] = Object.getOwnPropertyDescriptor(n, t);\n    }), r;\n  },\n  tn = {},\n  en = {\n    get: function (n, r) {\n      if (r === Q) return n;\n      var e = p(n);\n      if (!u(e, r)) return function (n, r, t) {\n        var e,\n          i = I(r, t);\n        return i ? \"value\" in i ? i.value : null === (e = i.get) || void 0 === e ? void 0 : e.call(n.k) : void 0;\n      }(n, e, r);\n      var i = e[r];\n      return n.I || !t(i) ? i : i === z(n.t, r) ? (E(n), n.o[r] = N(n.A.h, i, n)) : i;\n    },\n    has: function (n, r) {\n      return r in p(n);\n    },\n    ownKeys: function (n) {\n      return Reflect.ownKeys(p(n));\n    },\n    set: function (n, r, t) {\n      var e = I(p(n), r);\n      if (null == e ? void 0 : e.set) return e.set.call(n.k, t), !0;\n      if (!n.P) {\n        var i = z(p(n), r),\n          o = null == i ? void 0 : i[Q];\n        if (o && o.t === t) return n.o[r] = t, n.R[r] = !1, !0;\n        if (c(t, i) && (void 0 !== t || u(n.t, r))) return !0;\n        E(n), k(n);\n      }\n      return n.o[r] === t && (void 0 !== t || r in n.o) || Number.isNaN(t) && Number.isNaN(n.o[r]) || (n.o[r] = t, n.R[r] = !0), !0;\n    },\n    deleteProperty: function (n, r) {\n      return void 0 !== z(n.t, r) || r in n.t ? (n.R[r] = !1, E(n), k(n)) : delete n.R[r], n.o && delete n.o[r], !0;\n    },\n    getOwnPropertyDescriptor: function (n, r) {\n      var t = p(n),\n        e = Reflect.getOwnPropertyDescriptor(t, r);\n      return e ? {\n        writable: !0,\n        configurable: 1 !== n.i || \"length\" !== r,\n        enumerable: e.enumerable,\n        value: t[r]\n      } : e;\n    },\n    defineProperty: function () {\n      n(11);\n    },\n    getPrototypeOf: function (n) {\n      return Object.getPrototypeOf(n.t);\n    },\n    setPrototypeOf: function () {\n      n(12);\n    }\n  },\n  on = {};\ni(en, function (n, r) {\n  on[n] = function () {\n    return arguments[0] = arguments[0][0], r.apply(this, arguments);\n  };\n}), on.deleteProperty = function (r, t) {\n  return \"production\" !== process.env.NODE_ENV && isNaN(parseInt(t)) && n(13), on.set.call(this, r, t, void 0);\n}, on.set = function (r, t, e) {\n  return \"production\" !== process.env.NODE_ENV && \"length\" !== t && isNaN(parseInt(t)) && n(14), en.set.call(this, r[0], t, e, r[0]);\n};\nvar un = function () {\n    function e(r) {\n      var e = this;\n      this.O = B, this.D = !0, this.produce = function (r, i, o) {\n        if (\"function\" == typeof r && \"function\" != typeof i) {\n          var u = i;\n          i = r;\n          var a = e;\n          return function (n) {\n            var r = this;\n            void 0 === n && (n = u);\n            for (var t = arguments.length, e = Array(t > 1 ? t - 1 : 0), o = 1; o < t; o++) e[o - 1] = arguments[o];\n            return a.produce(n, function (n) {\n              var t;\n              return (t = i).call.apply(t, [r, n].concat(e));\n            });\n          };\n        }\n        var f;\n        if (\"function\" != typeof i && n(6), void 0 !== o && \"function\" != typeof o && n(7), t(r)) {\n          var c = w(e),\n            s = N(e, r, void 0),\n            v = !0;\n          try {\n            f = i(s), v = !1;\n          } finally {\n            v ? g(c) : O(c);\n          }\n          return \"undefined\" != typeof Promise && f instanceof Promise ? f.then(function (n) {\n            return j(c, o), P(n, c);\n          }, function (n) {\n            throw g(c), n;\n          }) : (j(c, o), P(f, c));\n        }\n        if (!r || \"object\" != typeof r) {\n          if (void 0 === (f = i(r)) && (f = r), f === H && (f = void 0), e.D && d(f, !0), o) {\n            var p = [],\n              l = [];\n            b(\"Patches\").M(r, f, p, l), o(p, l);\n          }\n          return f;\n        }\n        n(21, r);\n      }, this.produceWithPatches = function (n, r) {\n        if (\"function\" == typeof n) return function (r) {\n          for (var t = arguments.length, i = Array(t > 1 ? t - 1 : 0), o = 1; o < t; o++) i[o - 1] = arguments[o];\n          return e.produceWithPatches(r, function (r) {\n            return n.apply(void 0, [r].concat(i));\n          });\n        };\n        var t,\n          i,\n          o = e.produce(n, r, function (n, r) {\n            t = n, i = r;\n          });\n        return \"undefined\" != typeof Promise && o instanceof Promise ? o.then(function (n) {\n          return [n, t, i];\n        }) : [o, t, i];\n      }, \"boolean\" == typeof (null == r ? void 0 : r.useProxies) && this.setUseProxies(r.useProxies), \"boolean\" == typeof (null == r ? void 0 : r.autoFreeze) && this.setAutoFreeze(r.autoFreeze);\n    }\n    var i = e.prototype;\n    return i.createDraft = function (e) {\n      t(e) || n(8), r(e) && (e = R(e));\n      var i = w(this),\n        o = N(this, e, void 0);\n      return o[Q].C = !0, O(i), o;\n    }, i.finishDraft = function (r, t) {\n      var e = r && r[Q];\n      \"production\" !== process.env.NODE_ENV && (e && e.C || n(9), e.I && n(10));\n      var i = e.A;\n      return j(i, t), P(void 0, i);\n    }, i.setAutoFreeze = function (n) {\n      this.D = n;\n    }, i.setUseProxies = function (r) {\n      r && !B && n(20), this.O = r;\n    }, i.applyPatches = function (n, t) {\n      var e;\n      for (e = t.length - 1; e >= 0; e--) {\n        var i = t[e];\n        if (0 === i.path.length && \"replace\" === i.op) {\n          n = i.value;\n          break;\n        }\n      }\n      e > -1 && (t = t.slice(e + 1));\n      var o = b(\"Patches\").$;\n      return r(n) ? o(n, t) : this.produce(n, function (n) {\n        return o(n, t);\n      });\n    }, e;\n  }(),\n  an = new un(),\n  fn = an.produce,\n  cn = an.produceWithPatches.bind(an),\n  sn = an.setAutoFreeze.bind(an),\n  vn = an.setUseProxies.bind(an),\n  pn = an.applyPatches.bind(an),\n  ln = an.createDraft.bind(an),\n  dn = an.finishDraft.bind(an);\nexport default fn;\nexport { un as Immer, pn as applyPatches, K as castDraft, $ as castImmutable, ln as createDraft, R as current, J as enableAllPlugins, F as enableES5, C as enableMapSet, T as enablePatches, dn as finishDraft, d as freeze, L as immerable, r as isDraft, t as isDraftable, H as nothing, e as original, fn as produce, cn as produceWithPatches, sn as setAutoFreeze, vn as setUseProxies };\n//# sourceMappingURL=immer.esm.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}