{"ast": null, "code": "import { getPickersLocalization } from './utils/getPickersLocalization'; // Translation map for Clock Label\n\nconst timeViews = {\n  hours: 'часы',\n  minutes: 'минуты',\n  seconds: 'секунды'\n}; // maps PickersToolbar[\"viewType\"] to its translation\n\nconst viewTypes = {\n  calendar: 'календарный',\n  clock: 'часовой'\n};\nconst ruRUPickers = {\n  // Calendar navigation\n  previousMonth: 'Предыдущий месяц',\n  nextMonth: 'Следующий месяц',\n  // View navigation\n  openPreviousView: 'открыть предыдущий вид',\n  openNextView: 'открыть следующий вид',\n  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'открыт годовой вид, переключить на календарный вид' : 'открыт календарный вид, переключить на годовой вид',\n  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? \"\\u041E\\u0442\\u043A\\u0440\\u044B\\u0442 \\u0442\\u0435\\u043A\\u0441\\u0442\\u043E\\u0432\\u044B\\u0439 \\u0432\\u0438\\u0434, \\u043F\\u0435\\u0440\\u0435\\u0439\\u0442\\u0438 \\u043D\\u0430 \".concat(viewTypes[viewType], \" \\u0432\\u0438\\u0434\") : \"\\u041E\\u0442\\u043A\\u0440\\u044B\\u0442 \".concat(viewTypes[viewType], \" \\u0432\\u0438\\u0434, \\u043F\\u0435\\u0440\\u0435\\u0439\\u0442\\u0438 \\u043D\\u0430 \\u0442\\u0435\\u043A\\u0441\\u0442\\u043E\\u0432\\u044B\\u0439 \\u0432\\u0438\\u0434\"),\n  // DateRange placeholders\n  start: 'Начало',\n  end: 'Конец',\n  // Action bar\n  cancelButtonLabel: 'Отмена',\n  clearButtonLabel: 'Очистить',\n  okButtonLabel: 'Ок',\n  todayButtonLabel: 'Сегодня',\n  // Toolbar titles\n  datePickerDefaultToolbarTitle: 'Выбрать дату',\n  dateTimePickerDefaultToolbarTitle: 'Выбрать дату и время',\n  timePickerDefaultToolbarTitle: 'Выбрать время',\n  dateRangePickerDefaultToolbarTitle: 'Выбрать период',\n  // Clock labels\n  clockLabelText: (view, time, adapter) => \"\\u0412\\u044B\\u0431\\u0440\\u0430\\u0442\\u044C \".concat(timeViews[view], \". \").concat(time === null ? 'Время не выбрано' : \"\\u0412\\u044B\\u0431\\u0440\\u0430\\u043D\\u043E \\u0432\\u0440\\u0435\\u043C\\u044F \".concat(adapter.format(time, 'fullTime'))),\n  hoursClockNumberText: hours => \"\".concat(hours, \" \\u0447\\u0430\\u0441\\u043E\\u0432\"),\n  minutesClockNumberText: minutes => \"\".concat(minutes, \" \\u043C\\u0438\\u043D\\u0443\\u0442\"),\n  secondsClockNumberText: seconds => \"\".concat(seconds, \" \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434\"),\n  // Open picker labels\n  openDatePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? \"\\u0412\\u044B\\u0431\\u0435\\u0440\\u0438\\u0442\\u0435 \\u0434\\u0430\\u0442\\u0443, \\u0432\\u044B\\u0431\\u0440\\u0430\\u043D\\u0430 \\u0434\\u0430\\u0442\\u0430 \".concat(utils.format(value, 'fullDate')) : 'Выберите дату',\n  openTimePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? \"\\u0412\\u044B\\u0431\\u0435\\u0440\\u0438\\u0442\\u0435 \\u0432\\u0440\\u0435\\u043C\\u044F, \\u0432\\u044B\\u0431\\u0440\\u0430\\u043D\\u043E \\u0432\\u0440\\u0435\\u043C\\u044F \".concat(utils.format(value, 'fullTime')) : 'Выберите время',\n  // Table labels\n  timeTableLabel: 'выбрать время',\n  dateTableLabel: 'выбрать дату'\n};\nexport const ruRU = getPickersLocalization(ruRUPickers);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}