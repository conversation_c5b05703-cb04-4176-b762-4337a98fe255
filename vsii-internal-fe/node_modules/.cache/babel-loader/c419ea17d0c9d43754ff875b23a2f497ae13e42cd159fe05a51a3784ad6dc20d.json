{"ast": null, "code": "import { __extends } from \"tslib\";\nvar MissingLocaleDataError = /** @class */function (_super) {\n  __extends(MissingLocaleDataError, _super);\n  function MissingLocaleDataError() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'MISSING_LOCALE_DATA';\n    return _this;\n  }\n  return MissingLocaleDataError;\n}(Error);\nexport function isMissingLocaleDataError(e) {\n  return e.type === 'MISSING_LOCALE_DATA';\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}