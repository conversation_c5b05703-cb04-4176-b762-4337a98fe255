{"ast": null, "code": "import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getInputUnstyledUtilityClass(slot) {\n  return generateUtilityClass('MuiInput', slot);\n}\nconst inputBaseClasses = generateUtilityClasses('MuiInput', ['root', 'formControl', 'focused', 'disabled', 'error', 'multiline', 'input', 'inputMultiline', 'inputTypeSearch', 'adornedStart', 'adornedEnd']);\nexport default inputBaseClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}