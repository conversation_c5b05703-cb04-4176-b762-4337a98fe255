{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '@mui/base';\nexport function getTimelineItemUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineItem', slot);\n}\nconst timelineItemClasses = generateUtilityClasses('MuiTimelineItem', ['root', 'positionLeft', 'positionRight', 'positionAlternate', 'missingOppositeContent']);\nexport default timelineItemClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}