{"ast": null, "code": "import{Suspense}from'react';// project imports\nimport Loader from'./Loader';// ==============================|| LOADABLE - LAZY LOADING ||============================== //\nimport{jsx as _jsx}from\"react/jsx-runtime\";const Loadable=Component=>props=>/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(Loader,{}),children:/*#__PURE__*/_jsx(Component,{...props})});export default Loadable;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}