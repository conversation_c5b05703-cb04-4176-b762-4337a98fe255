{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"dividers\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getDialogContentUtilityClass } from './dialogContentClasses';\nimport dialogTitleClasses from '../DialogTitle/dialogTitleClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    dividers\n  } = ownerState;\n  const slots = {\n    root: ['root', dividers && 'dividers']\n  };\n  return composeClasses(slots, getDialogContentUtilityClass, classes);\n};\nconst DialogContentRoot = styled('div', {\n  name: 'MuiDialogContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.dividers && styles.dividers];\n  }\n})(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  return _extends({\n    flex: '1 1 auto',\n    // Add iOS momentum scrolling for iOS < 13.0\n    WebkitOverflowScrolling: 'touch',\n    overflowY: 'auto',\n    padding: '20px 24px'\n  }, ownerState.dividers ? {\n    padding: '16px 24px',\n    borderTop: \"1px solid \".concat((theme.vars || theme).palette.divider),\n    borderBottom: \"1px solid \".concat((theme.vars || theme).palette.divider)\n  } : {\n    [\".\".concat(dialogTitleClasses.root, \" + &\")]: {\n      paddingTop: 0\n    }\n  });\n});\nconst DialogContent = /*#__PURE__*/React.forwardRef(function DialogContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDialogContent'\n  });\n  const {\n      className,\n      dividers = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    dividers\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(DialogContentRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? DialogContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Display the top and bottom dividers.\n   * @default false\n   */\n  dividers: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default DialogContent;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}