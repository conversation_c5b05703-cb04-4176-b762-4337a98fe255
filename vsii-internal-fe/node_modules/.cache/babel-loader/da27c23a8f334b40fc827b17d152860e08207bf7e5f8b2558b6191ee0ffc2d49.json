{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/non-billable-monitoring/WarningNonbillMemberTBody.tsx\";\n// materia-ui\nimport { TableBody, TableCell, TableRow } from '@mui/material';\n\n// project imports\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WarningNonbillMemberTBody = props => {\n  const {\n    data\n  } = props;\n  return /*#__PURE__*/_jsxDEV(TableBody, {\n    children: data.map((item, key) => /*#__PURE__*/_jsxDEV(TableRow, {\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        children: key + 1\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: item.memberCode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: item.userName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: item.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: item.dept\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"center\",\n        children: item.consecutiveWeek\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 21\n      }, this)]\n    }, key, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 17\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 9\n  }, this);\n};\n_c = WarningNonbillMemberTBody;\nexport default WarningNonbillMemberTBody;\nvar _c;\n$RefreshReg$(_c, \"WarningNonbillMemberTBody\");", "map": {"version": 3, "names": ["TableBody", "TableCell", "TableRow", "jsxDEV", "_jsxDEV", "WarningNonbillMemberTBody", "props", "data", "children", "map", "item", "key", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "memberCode", "userName", "title", "dept", "align", "consecutiveWeek", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/non-billable-monitoring/WarningNonbillMemberTBody.tsx"], "sourcesContent": ["// materia-ui\nimport { TableBody, TableCell, TableRow } from '@mui/material';\n\n// project imports\nimport { IWarningNonbillMember } from 'types';\n\ninterface WarningNonbillMemberTbodyProps {\n    data: IWarningNonbillMember[];\n}\n\nconst WarningNonbillMemberTBody = (props: WarningNonbillMemberTbodyProps) => {\n    const { data } = props;\n    return (\n        <TableBody>\n            {data.map((item, key) => (\n                <TableRow key={key}>\n                    <TableCell>{key + 1}</TableCell>\n                    <TableCell>{item.memberCode}</TableCell>\n                    <TableCell>{item.userName}</TableCell>\n                    <TableCell>{item.title}</TableCell>\n                    <TableCell>{item.dept}</TableCell>\n                    <TableCell align=\"center\">{item.consecutiveWeek}</TableCell>\n                </TableRow>\n            ))}\n        </TableBody>\n    );\n};\n\nexport default WarningNonbillMemberTBody;\n"], "mappings": ";AAAA;AACA,SAASA,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;;AAE9D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAOA,MAAMC,yBAAyB,GAAIC,KAAqC,IAAK;EACzE,MAAM;IAAEC;EAAK,CAAC,GAAGD,KAAK;EACtB,oBACIF,OAAA,CAACJ,SAAS;IAAAQ,QAAA,EACLD,IAAI,CAACE,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,kBAChBP,OAAA,CAACF,QAAQ;MAAAM,QAAA,gBACLJ,OAAA,CAACH,SAAS;QAAAO,QAAA,EAAEG,GAAG,GAAG;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAChCX,OAAA,CAACH,SAAS;QAAAO,QAAA,EAAEE,IAAI,CAACM;MAAU;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACxCX,OAAA,CAACH,SAAS;QAAAO,QAAA,EAAEE,IAAI,CAACO;MAAQ;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACtCX,OAAA,CAACH,SAAS;QAAAO,QAAA,EAAEE,IAAI,CAACQ;MAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACnCX,OAAA,CAACH,SAAS;QAAAO,QAAA,EAAEE,IAAI,CAACS;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAClCX,OAAA,CAACH,SAAS;QAACmB,KAAK,EAAC,QAAQ;QAAAZ,QAAA,EAAEE,IAAI,CAACW;MAAe;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA,GANjDJ,GAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAOR,CACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEpB,CAAC;AAACO,EAAA,GAhBIjB,yBAAyB;AAkB/B,eAAeA,yBAAyB;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}