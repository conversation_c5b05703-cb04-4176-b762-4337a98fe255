{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/cost-monitoring/MonthlyCostMonitoringSearch.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from 'react';\nimport { Grid, Typography } from '@mui/material';\nimport ErrorIcon from '@mui/icons-material/Error';\nimport { FormattedMessage } from 'react-intl';\nimport { costAndEffortMonitoringSelector, getCostMonitoringProjectOptionByFixCost } from 'store/slice/costAndEffortMonitoringSlice';\nimport { costMonitoringFilterConfig, costMonitoringFilterSchema } from 'pages/cost-monitoring/Config';\nimport { TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';\nimport { Autocomplete, Label } from 'components/extended/Form';\nimport { searchFormConfig } from 'containers/search/Config';\nimport ColorNoteTooltip from 'components/ColorNoteTooltip';\nimport { useAppDispatch, useAppSelector } from 'app/hooks';\nimport { SearchForm, Years, Months } from '../search';\nimport { Button } from 'components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MonthlyCostMonitoringSearch = ({\n  formReset,\n  months,\n  fixCost,\n  handleChangeYear,\n  handleSearch\n}) => {\n  _s();\n  const {\n    projectOptions\n  } = useAppSelector(costAndEffortMonitoringSelector);\n  const [month, setMonth] = useState('');\n  const dispatch = useAppDispatch();\n  const currentMonth = months.find(month => month.value === costMonitoringFilterConfig.month);\n  const {\n    monthlyMonitoring\n  } = TEXT_CONFIG_SCREEN.costAndEffortMonitoring;\n  const handleChangeMonth = value => {\n    const getMonth = months.find(month => month.value === value);\n    if (getMonth) {\n      setMonth(getMonth.label);\n    }\n  };\n  useEffect(() => {\n    dispatch(getCostMonitoringProjectOptionByFixCost({\n      type: 'month',\n      value: month ? month : currentMonth === null || currentMonth === void 0 ? void 0 : currentMonth.label,\n      fixCost,\n      color: true\n    }));\n  }, [month, dispatch, fixCost, currentMonth]);\n  return /*#__PURE__*/_jsxDEV(SearchForm, {\n    defaultValues: costMonitoringFilterConfig,\n    formSchema: costMonitoringFilterSchema,\n    handleSubmit: handleSearch,\n    formReset: formReset,\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      alignItems: \"center\",\n      spacing: 2,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 3,\n        children: /*#__PURE__*/_jsxDEV(Years, {\n          handleChangeYear: handleChangeYear,\n          ignoreDefault: true,\n          label: monthlyMonitoring + 'year'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 3,\n        children: /*#__PURE__*/_jsxDEV(Months, {\n          months: months,\n          onChange: handleChangeMonth,\n          isFilter: true,\n          isShow13MonthSalary: true,\n          year: formReset.year,\n          label: monthlyMonitoring + 'month'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 3,\n        children: /*#__PURE__*/_jsxDEV(Autocomplete, {\n          options: projectOptions,\n          name: searchFormConfig.project.name,\n          label: /*#__PURE__*/_jsxDEV(Typography, {\n            display: \"flex\",\n            gap: 0.5,\n            children: [/*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: monthlyMonitoring + 'project'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(ColorNoteTooltip, {\n              notes: TEXT_INPUT_COLOR_EFFORT_INCURRED,\n              children: /*#__PURE__*/_jsxDEV(ErrorIcon, {\n                sx: {\n                  fontSize: 15\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 29\n          }, this),\n          groupBy: option => option.typeCode,\n          isDefaultAll: true,\n          isDisableClearable: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 3,\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          label: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          size: \"medium\",\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: monthlyMonitoring + 'search'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 35\n          }, this),\n          variant: \"contained\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 9\n  }, this);\n};\n_s(MonthlyCostMonitoringSearch, \"tITlafBBgiTfPjmZ79c2cB1kcQE=\", false, function () {\n  return [useAppSelector, useAppDispatch];\n});\n_c = MonthlyCostMonitoringSearch;\nexport default MonthlyCostMonitoringSearch;\nvar _c;\n$RefreshReg$(_c, \"MonthlyCostMonitoringSearch\");", "map": {"version": 3, "names": ["useEffect", "useState", "Grid", "Typography", "ErrorIcon", "FormattedMessage", "costAndEffortMonitoringSelector", "getCostMonitoringProjectOptionByFixCost", "costMonitoringFilterConfig", "costMonitoringFilterSchema", "TEXT_CONFIG_SCREEN", "TEXT_INPUT_COLOR_EFFORT_INCURRED", "Autocomplete", "Label", "searchFormConfig", "ColorNoteTooltip", "useAppDispatch", "useAppSelector", "SearchForm", "Years", "Months", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "MonthlyCostMonitoringSearch", "formReset", "months", "fixCost", "handleChangeYear", "handleSearch", "_s", "projectOptions", "month", "setMonth", "dispatch", "currentMonth", "find", "value", "monthlyMonitoring", "costAndEffortMonitoring", "handleChangeMonth", "getMonth", "label", "type", "color", "defaultValues", "formSchema", "handleSubmit", "children", "container", "alignItems", "spacing", "item", "xs", "lg", "<PERSON><PERSON><PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "isFilter", "isShow13MonthSalary", "year", "options", "name", "project", "display", "gap", "id", "notes", "sx", "fontSize", "groupBy", "option", "typeCode", "isDefaultAll", "isDisableClearable", "size", "variant", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/cost-monitoring/MonthlyCostMonitoringSearch.tsx"], "sourcesContent": ["import { useEffect, useState } from 'react';\nimport { Grid, SelectChangeEvent, Typography } from '@mui/material';\nimport ErrorIcon from '@mui/icons-material/Error';\nimport { FormattedMessage } from 'react-intl';\n\nimport { costAndEffortMonitoringSelector, getCostMonitoringProjectOptionByFixCost } from 'store/slice/costAndEffortMonitoringSlice';\nimport { ICostMonitoringFilterConfig, costMonitoringFilterConfig, costMonitoringFilterSchema } from 'pages/cost-monitoring/Config';\nimport { TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';\nimport { Autocomplete, Label } from 'components/extended/Form';\nimport { searchFormConfig } from 'containers/search/Config';\nimport ColorNoteTooltip from 'components/ColorNoteTooltip';\nimport { useAppDispatch, useAppSelector } from 'app/hooks';\nimport { SearchForm, Years, Months } from '../search';\nimport { Button } from 'components';\nimport { IOption } from 'types';\n\ninterface IMonthlyCostMonitoringSearchProps {\n    formReset: ICostMonitoringFilterConfig;\n    months: IOption[];\n    handleSearch: (value: ICostMonitoringFilterConfig) => void;\n    handleChangeYear: (e: SelectChangeEvent<unknown>) => void;\n    fixCost: boolean;\n}\n\nconst MonthlyCostMonitoringSearch = ({ formReset, months, fixCost, handleChangeYear, handleSearch }: IMonthlyCostMonitoringSearchProps) => {\n    const { projectOptions } = useAppSelector(costAndEffortMonitoringSelector);\n    const [month, setMonth] = useState('');\n    const dispatch = useAppDispatch();\n    const currentMonth = months.find((month) => month.value === costMonitoringFilterConfig.month);\n\n    const { monthlyMonitoring } = TEXT_CONFIG_SCREEN.costAndEffortMonitoring;\n\n    const handleChangeMonth = (value: string) => {\n        const getMonth = months.find((month) => month.value === value);\n\n        if (getMonth) {\n            setMonth(getMonth.label);\n        }\n    };\n\n    useEffect(() => {\n        dispatch(\n            getCostMonitoringProjectOptionByFixCost({\n                type: 'month',\n                value: month ? month : (currentMonth?.label as string),\n                fixCost,\n                color: true\n            })\n        );\n    }, [month, dispatch, fixCost, currentMonth]);\n\n    return (\n        <SearchForm\n            defaultValues={costMonitoringFilterConfig}\n            formSchema={costMonitoringFilterSchema}\n            handleSubmit={handleSearch}\n            formReset={formReset}\n        >\n            <Grid container alignItems=\"center\" spacing={2}>\n                <Grid item xs={12} lg={3}>\n                    <Years handleChangeYear={handleChangeYear} ignoreDefault label={monthlyMonitoring + 'year'} />\n                </Grid>\n                <Grid item xs={12} lg={3}>\n                    <Months\n                        months={months}\n                        onChange={handleChangeMonth}\n                        isFilter\n                        isShow13MonthSalary\n                        year={formReset.year}\n                        label={monthlyMonitoring + 'month'}\n                    />\n                </Grid>\n                <Grid item xs={12} lg={3}>\n                    <Autocomplete\n                        options={projectOptions}\n                        name={searchFormConfig.project.name}\n                        label={\n                            <Typography display=\"flex\" gap={0.5}>\n                                <FormattedMessage id={monthlyMonitoring + 'project'} />\n                                <ColorNoteTooltip notes={TEXT_INPUT_COLOR_EFFORT_INCURRED}>\n                                    <ErrorIcon sx={{ fontSize: 15 }} />\n                                </ColorNoteTooltip>\n                            </Typography>\n                        }\n                        groupBy={(option: IOption) => option.typeCode}\n                        isDefaultAll\n                        isDisableClearable\n                    />\n                </Grid>\n                <Grid item xs={12} lg={3}>\n                    <Label label=\"&nbsp;\" />\n                    <Button\n                        type=\"submit\"\n                        size=\"medium\"\n                        children={<FormattedMessage id={monthlyMonitoring + 'search'} />}\n                        variant=\"contained\"\n                    />\n                </Grid>\n            </Grid>\n        </SearchForm>\n    );\n};\n\nexport default MonthlyCostMonitoringSearch;\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,IAAI,EAAqBC,UAAU,QAAQ,eAAe;AACnE,OAAOC,SAAS,MAAM,2BAA2B;AACjD,SAASC,gBAAgB,QAAQ,YAAY;AAE7C,SAASC,+BAA+B,EAAEC,uCAAuC,QAAQ,0CAA0C;AACnI,SAAsCC,0BAA0B,EAAEC,0BAA0B,QAAQ,8BAA8B;AAClI,SAASC,kBAAkB,EAAEC,gCAAgC,QAAQ,kBAAkB;AACvF,SAASC,YAAY,EAAEC,KAAK,QAAQ,0BAA0B;AAC9D,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,cAAc,EAAEC,cAAc,QAAQ,WAAW;AAC1D,SAASC,UAAU,EAAEC,KAAK,EAAEC,MAAM,QAAQ,WAAW;AACrD,SAASC,MAAM,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWpC,MAAMC,2BAA2B,GAAGA,CAAC;EAAEC,SAAS;EAAEC,MAAM;EAAEC,OAAO;EAAEC,gBAAgB;EAAEC;AAAgD,CAAC,KAAK;EAAAC,EAAA;EACvI,MAAM;IAAEC;EAAe,CAAC,GAAGd,cAAc,CAACX,+BAA+B,CAAC;EAC1E,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMiC,QAAQ,GAAGlB,cAAc,CAAC,CAAC;EACjC,MAAMmB,YAAY,GAAGT,MAAM,CAACU,IAAI,CAAEJ,KAAK,IAAKA,KAAK,CAACK,KAAK,KAAK7B,0BAA0B,CAACwB,KAAK,CAAC;EAE7F,MAAM;IAAEM;EAAkB,CAAC,GAAG5B,kBAAkB,CAAC6B,uBAAuB;EAExE,MAAMC,iBAAiB,GAAIH,KAAa,IAAK;IACzC,MAAMI,QAAQ,GAAGf,MAAM,CAACU,IAAI,CAAEJ,KAAK,IAAKA,KAAK,CAACK,KAAK,KAAKA,KAAK,CAAC;IAE9D,IAAII,QAAQ,EAAE;MACVR,QAAQ,CAACQ,QAAQ,CAACC,KAAK,CAAC;IAC5B;EACJ,CAAC;EAED1C,SAAS,CAAC,MAAM;IACZkC,QAAQ,CACJ3B,uCAAuC,CAAC;MACpCoC,IAAI,EAAE,OAAO;MACbN,KAAK,EAAEL,KAAK,GAAGA,KAAK,GAAIG,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEO,KAAgB;MACtDf,OAAO;MACPiB,KAAK,EAAE;IACX,CAAC,CACL,CAAC;EACL,CAAC,EAAE,CAACZ,KAAK,EAAEE,QAAQ,EAAEP,OAAO,EAAEQ,YAAY,CAAC,CAAC;EAE5C,oBACIZ,OAAA,CAACL,UAAU;IACP2B,aAAa,EAAErC,0BAA2B;IAC1CsC,UAAU,EAAErC,0BAA2B;IACvCsC,YAAY,EAAElB,YAAa;IAC3BJ,SAAS,EAAEA,SAAU;IAAAuB,QAAA,eAErBzB,OAAA,CAACrB,IAAI;MAAC+C,SAAS;MAACC,UAAU,EAAC,QAAQ;MAACC,OAAO,EAAE,CAAE;MAAAH,QAAA,gBAC3CzB,OAAA,CAACrB,IAAI;QAACkD,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,eACrBzB,OAAA,CAACJ,KAAK;UAACS,gBAAgB,EAAEA,gBAAiB;UAAC2B,aAAa;UAACb,KAAK,EAAEJ,iBAAiB,GAAG;QAAO;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5F,CAAC,eACPpC,OAAA,CAACrB,IAAI;QAACkD,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,eACrBzB,OAAA,CAACH,MAAM;UACHM,MAAM,EAAEA,MAAO;UACfkC,QAAQ,EAAEpB,iBAAkB;UAC5BqB,QAAQ;UACRC,mBAAmB;UACnBC,IAAI,EAAEtC,SAAS,CAACsC,IAAK;UACrBrB,KAAK,EAAEJ,iBAAiB,GAAG;QAAQ;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACPpC,OAAA,CAACrB,IAAI;QAACkD,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,eACrBzB,OAAA,CAACX,YAAY;UACToD,OAAO,EAAEjC,cAAe;UACxBkC,IAAI,EAAEnD,gBAAgB,CAACoD,OAAO,CAACD,IAAK;UACpCvB,KAAK,eACDnB,OAAA,CAACpB,UAAU;YAACgE,OAAO,EAAC,MAAM;YAACC,GAAG,EAAE,GAAI;YAAApB,QAAA,gBAChCzB,OAAA,CAAClB,gBAAgB;cAACgE,EAAE,EAAE/B,iBAAiB,GAAG;YAAU;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvDpC,OAAA,CAACR,gBAAgB;cAACuD,KAAK,EAAE3D,gCAAiC;cAAAqC,QAAA,eACtDzB,OAAA,CAACnB,SAAS;gBAACmE,EAAE,EAAE;kBAAEC,QAAQ,EAAE;gBAAG;cAAE;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CACf;UACDc,OAAO,EAAGC,MAAe,IAAKA,MAAM,CAACC,QAAS;UAC9CC,YAAY;UACZC,kBAAkB;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACPpC,OAAA,CAACrB,IAAI;QAACkD,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,gBACrBzB,OAAA,CAACV,KAAK;UAAC6B,KAAK,EAAC;QAAQ;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxBpC,OAAA,CAACF,MAAM;UACHsB,IAAI,EAAC,QAAQ;UACbmC,IAAI,EAAC,QAAQ;UACb9B,QAAQ,eAAEzB,OAAA,CAAClB,gBAAgB;YAACgE,EAAE,EAAE/B,iBAAiB,GAAG;UAAS;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACjEoB,OAAO,EAAC;QAAW;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAErB,CAAC;AAAC7B,EAAA,CA7EIN,2BAA2B;EAAA,QACFP,cAAc,EAExBD,cAAc;AAAA;AAAAgE,EAAA,GAH7BxD,2BAA2B;AA+EjC,eAAeA,2BAA2B;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}