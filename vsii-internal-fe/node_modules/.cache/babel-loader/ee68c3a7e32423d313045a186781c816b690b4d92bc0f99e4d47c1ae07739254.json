{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"badgeContent\", \"component\", \"children\", \"components\", \"componentsProps\", \"invisible\", \"max\", \"showZero\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '../composeClasses';\nimport useBadge from './useBadge';\nimport { getBadgeUnstyledUtilityClass } from './badgeUnstyledClasses';\nimport { useSlotProps } from '../utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    invisible\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    badge: ['badge', invisible && 'invisible']\n  };\n  return composeClasses(slots, getBadgeUnstyledUtilityClass, undefined);\n};\n/**\n *\n * Demos:\n *\n * - [Unstyled badge](https://mui.com/base/react-badge/)\n *\n * API:\n *\n * - [BadgeUnstyled API](https://mui.com/base/api/badge-unstyled/)\n */\n\nconst BadgeUnstyled = /*#__PURE__*/React.forwardRef(function BadgeUnstyled(props, ref) {\n  const {\n      component,\n      children,\n      components = {},\n      componentsProps = {},\n      max: maxProp = 99,\n      showZero = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    badgeContent,\n    max,\n    displayValue,\n    invisible\n  } = useBadge(_extends({}, props, {\n    max: maxProp\n  }));\n  const ownerState = _extends({}, props, {\n    badgeContent,\n    invisible,\n    max,\n    showZero\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = component || components.Root || 'span';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref\n    },\n    ownerState,\n    className: classes.root\n  });\n  const Badge = components.Badge || 'span';\n  const badgeProps = useSlotProps({\n    elementType: Badge,\n    externalSlotProps: componentsProps.badge,\n    ownerState,\n    className: classes.badge\n  });\n  return /*#__PURE__*/_jsxs(Root, _extends({}, rootProps, {\n    children: [children, /*#__PURE__*/_jsx(Badge, _extends({}, badgeProps, {\n      children: displayValue\n    }))]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? BadgeUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * The content rendered within the badge.\n   */\n  badgeContent: PropTypes.node,\n  /**\n   * The badge will be added relative to this node.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the Badge.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Badge: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The props used for each slot inside the Badge.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    badge: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * If `true`, the badge is invisible.\n   * @default false\n   */\n  invisible: PropTypes.bool,\n  /**\n   * Max count to show.\n   * @default 99\n   */\n  max: PropTypes.number,\n  /**\n   * Controls whether the badge is hidden when `badgeContent` is zero.\n   * @default false\n   */\n  showZero: PropTypes.bool\n} : void 0;\nexport default BadgeUnstyled;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}