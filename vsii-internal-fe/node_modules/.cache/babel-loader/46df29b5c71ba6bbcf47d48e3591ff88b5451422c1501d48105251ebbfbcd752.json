{"ast": null, "code": "/**\n * The MotionValue tracks the state of a single animatable\n * value. Currently, updatedAt and current are unused. The\n * long term idea is to use this to minimise the number\n * of DOM reads, and to abstract the DOM interactions here.\n */\nclass MotionValue {\n  setAnimation(animation) {\n    this.animation = animation;\n    animation === null || animation === void 0 ? void 0 : animation.finished.then(() => this.clearAnimation()).catch(() => {});\n  }\n  clearAnimation() {\n    this.animation = this.generator = undefined;\n  }\n}\nexport { MotionValue };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}