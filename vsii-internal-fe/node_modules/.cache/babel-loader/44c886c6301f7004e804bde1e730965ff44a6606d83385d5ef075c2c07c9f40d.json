{"ast": null, "code": "/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The DateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`', '', \"You should use `import { DateRangePicker } from '@mui/x-date-pickers-pro'`\", \"or `import { DateRangePicker } from '@mui/x-date-pickers-pro/DateRangePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n\n/**\n * @ignore - do not document.\n */\nconst DateRangePicker = /*#__PURE__*/React.forwardRef(function DeprecatedDateRangePicker() {\n  warn();\n  return null;\n});\nexport default DateRangePicker;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}