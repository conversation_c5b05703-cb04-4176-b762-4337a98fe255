{"ast": null, "code": "import { MotionValue } from '@motionone/types';\nconst data = new WeakMap();\nfunction getAnimationData(element) {\n  if (!data.has(element)) {\n    data.set(element, {\n      transforms: [],\n      values: new Map()\n    });\n  }\n  return data.get(element);\n}\nfunction getMotionValue(motionValues, name) {\n  if (!motionValues.has(name)) {\n    motionValues.set(name, new MotionValue());\n  }\n  return motionValues.get(name);\n}\nexport { getAnimationData, getMotionValue };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}