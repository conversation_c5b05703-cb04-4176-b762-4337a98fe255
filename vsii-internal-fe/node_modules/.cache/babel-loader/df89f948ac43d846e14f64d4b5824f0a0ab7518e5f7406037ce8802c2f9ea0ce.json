{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/Billable.tsx\";\nimport React from 'react';\n// project imports\n\nimport { BILLABLE_OPTIONS } from 'constants/Common';\nimport { Select } from 'components/extended/Form';\nimport { searchFormConfig } from './Config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Billable = ({\n  required,\n  defaultOption,\n  handleChangeBillable,\n  label\n}) => {\n  const billableOptions = defaultOption ? [defaultOption, ...BILLABLE_OPTIONS] : BILLABLE_OPTIONS;\n  return /*#__PURE__*/_jsxDEV(Select, {\n    required: required,\n    selects: billableOptions,\n    name: searchFormConfig.billable.name,\n    label: label || searchFormConfig.billable.label,\n    defaultValue: defaultOption === null || defaultOption === void 0 ? void 0 : defaultOption.value,\n    handleChange: handleChangeBillable\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 9\n  }, this);\n};\n_c = Billable;\nexport default Billable;\nvar _c;\n$RefreshReg$(_c, \"Billable\");", "map": {"version": 3, "names": ["React", "BILLABLE_OPTIONS", "Select", "searchFormConfig", "jsxDEV", "_jsxDEV", "Billable", "required", "defaultOption", "handleChangeBillable", "label", "billableOptions", "selects", "name", "billable", "defaultValue", "value", "handleChange", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/Billable.tsx"], "sourcesContent": ["import React from 'react';\n// project imports\nimport { SelectChangeEvent } from '@mui/material';\n\nimport { BILLABLE_OPTIONS } from 'constants/Common';\nimport { Select } from 'components/extended/Form';\nimport { searchFormConfig } from './Config';\n\ninterface IBillableProps {\n    required?: boolean;\n    defaultOption?: { value: string | number; label: string };\n    handleChangeBillable?: (e: SelectChangeEvent<unknown>) => void;\n    label?: string | React.ReactNode;\n}\n\nconst Billable = ({ required, defaultOption, handleChangeBillable, label }: IBillableProps) => {\n    const billableOptions = defaultOption ? [defaultOption, ...BILLABLE_OPTIONS] : BILLABLE_OPTIONS;\n\n    return (\n        <Select\n            required={required}\n            selects={billableOptions}\n            name={searchFormConfig.billable.name}\n            label={label || searchFormConfig.billable.label}\n            defaultValue={defaultOption?.value as string}\n            handleChange={handleChangeBillable}\n        />\n    );\n};\n\nexport default Billable;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB;;AAGA,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,gBAAgB,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS5C,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,aAAa;EAAEC,oBAAoB;EAAEC;AAAsB,CAAC,KAAK;EAC3F,MAAMC,eAAe,GAAGH,aAAa,GAAG,CAACA,aAAa,EAAE,GAAGP,gBAAgB,CAAC,GAAGA,gBAAgB;EAE/F,oBACII,OAAA,CAACH,MAAM;IACHK,QAAQ,EAAEA,QAAS;IACnBK,OAAO,EAAED,eAAgB;IACzBE,IAAI,EAAEV,gBAAgB,CAACW,QAAQ,CAACD,IAAK;IACrCH,KAAK,EAAEA,KAAK,IAAIP,gBAAgB,CAACW,QAAQ,CAACJ,KAAM;IAChDK,YAAY,EAAEP,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEQ,KAAgB;IAC7CC,YAAY,EAAER;EAAqB;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtC,CAAC;AAEV,CAAC;AAACC,EAAA,GAbIhB,QAAQ;AAed,eAAeA,QAAQ;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}