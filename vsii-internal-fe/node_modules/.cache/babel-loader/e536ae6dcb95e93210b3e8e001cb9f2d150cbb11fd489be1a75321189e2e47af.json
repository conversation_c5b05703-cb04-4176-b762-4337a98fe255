{"ast": null, "code": "// project imports\nimport { EApproveStatus, paginationParamDefault } from 'constants/Common';\nimport { VALIDATE_MESSAGES } from 'constants/Message';\nimport moment from 'moment';\n// third party\nimport * as yup from 'yup';\n\n// ============== Manage OT ============== //\n\n// Filter\n\nexport const manageOtDefaultValues = {\n  ...paginationParamDefault,\n  memberId: null,\n  fromDate: null,\n  toDate: null,\n  type: '',\n  dept: '',\n  status: ''\n};\nexport const manageOtSearchSchema = yup.object().shape({\n  fromDate: yup.date().nullable(),\n  toDate: yup.date().nullable().min(yup.ref('fromDate'), VALIDATE_MESSAGES.ENDDATE),\n  type: yup.string().nullable(),\n  status: yup.string().nullable()\n});\n\n// Add or edit Overtime Report\n\nexport const overtimeReportConfig = {\n  idHexString: '',\n  userIdHexString: '',\n  fullName: '',\n  department: '',\n  manager: '',\n  nextManager: '',\n  overtimeReason: '',\n  projectName: null,\n  detail: '',\n  otherReason: '',\n  overtimeRecords: [{\n    overtimeFrom: new Date(),\n    overtimeTo: new Date(),\n    overtimeHours: ''\n  }],\n  totalHours: '',\n  totalCompensateHours: '',\n  equivalentDays: '',\n  compensateType: '',\n  compensateFrom: new Date(),\n  compensateTo: new Date(),\n  status: ''\n};\nexport const overtimeReportSchema = yup.object().shape({\n  fullName: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n  department: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n  manager: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n  nextManager: yup.string().test('nextManager-validation', VALIDATE_MESSAGES.REQUIRED, function (value) {\n    const {\n      status,\n      userIdHexString\n    } = this.parent;\n    const {\n      currentUserId\n    } = this.options.context || {};\n    const isCreator = userIdHexString === currentUserId;\n    const shouldValidate = status === EApproveStatus.AWAITING_QLTT && !isCreator;\n    return shouldValidate ? !!value : true;\n  }),\n  overtimeReason: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n  projectName: yup.mixed().when('overtimeReason', {\n    is: 'overtime-reason-1',\n    then: schema => schema.required(VALIDATE_MESSAGES.REQUIRED),\n    otherwise: schema => schema.nullable()\n  }),\n  detail: yup.string(),\n  otherReason: yup.string(),\n  overtimeRecords: yup.array().of(yup.object().shape({\n    overtimeFrom: yup.date().required(VALIDATE_MESSAGES.REQUIRED),\n    overtimeTo: yup.date().required(VALIDATE_MESSAGES.REQUIRED).test('is-after', VALIDATE_MESSAGES.AFTER_DAY, function (value) {\n      const {\n        overtimeFrom\n      } = this.parent;\n      const sameOrAfter = moment(value).startOf('day') >= moment(overtimeFrom).startOf('day');\n      return !value || !overtimeFrom || sameOrAfter;\n    }),\n    overtimeHours: yup.number().typeError(VALIDATE_MESSAGES.INVALID_NUMBER).positive(VALIDATE_MESSAGES.POSITIVE_NUMBER).required(VALIDATE_MESSAGES.REQUIRED)\n  })),\n  totalHours: yup.number().typeError(VALIDATE_MESSAGES.INVALID_NUMBER).positive(VALIDATE_MESSAGES.POSITIVE_NUMBER).required(VALIDATE_MESSAGES.REQUIRED),\n  totalCompensateHours: yup.number().typeError(VALIDATE_MESSAGES.INVALID_NUMBER).positive(VALIDATE_MESSAGES.POSITIVE_NUMBER).required(VALIDATE_MESSAGES.REQUIRED),\n  equivalentDays: yup.number().typeError(VALIDATE_MESSAGES.INVALID_NUMBER).required(VALIDATE_MESSAGES.REQUIRED),\n  compensateType: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n  compensateFrom: yup.date().when('compensateType', {\n    is: 'overtime-compensate-1',\n    then: schema => schema.required(VALIDATE_MESSAGES.REQUIRED),\n    otherwise: schema => schema.nullable()\n  }),\n  compensateTo: yup.date().when('compensateType', {\n    is: 'overtime-compensate-1',\n    then: schema => schema.required(VALIDATE_MESSAGES.REQUIRED).test('is-after', VALIDATE_MESSAGES.AFTER_DAY, function (value) {\n      const {\n        compensateFrom\n      } = this.parent;\n      const sameOrAfter = moment(value).startOf('day') >= moment(compensateFrom).startOf('day');\n      return !value || !compensateFrom || sameOrAfter;\n    }),\n    otherwise: schema => schema.nullable()\n  })\n});", "map": {"version": 3, "names": ["EApproveStatus", "paginationParamDefault", "VALIDATE_MESSAGES", "moment", "yup", "manageOtDefaultValues", "memberId", "fromDate", "toDate", "type", "dept", "status", "manageOtSearchSchema", "object", "shape", "date", "nullable", "min", "ref", "ENDDATE", "string", "overtimeReportConfig", "idHexString", "userIdHexString", "fullName", "department", "manager", "nextManager", "overtimeReason", "projectName", "detail", "otherReason", "overtimeRecords", "overtimeFrom", "Date", "overtimeTo", "overtimeHours", "totalHours", "totalCompensateHours", "equivalentDays", "compensateType", "compensateFrom", "compensateTo", "overtimeReportSchema", "required", "REQUIRED", "test", "value", "parent", "currentUserId", "options", "context", "isCreator", "shouldValidate", "AWAITING_QLTT", "mixed", "when", "is", "then", "schema", "otherwise", "array", "of", "AFTER_DAY", "sameOrAfter", "startOf", "number", "typeError", "INVALID_NUMBER", "positive", "POSITIVE_NUMBER"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/pages/manage-ot-requests/Config.ts"], "sourcesContent": ["// project imports\nimport { EApproveStatus, paginationParamDefault } from 'constants/Common';\nimport { VALIDATE_MESSAGES } from 'constants/Message';\nimport moment from 'moment';\nimport { IOption, IPaginationParam } from 'types';\n\n// third party\nimport * as yup from 'yup';\n\n// ============== Manage OT ============== //\n\n// Filter\nexport interface IManageOtDefaultValues extends IPaginationParam {\n    memberId: IOption | null;\n    fromDate: Date | null;\n    toDate: Date | null;\n    type: string;\n    dept: string;\n    status: string;\n}\n\nexport const manageOtDefaultValues: IManageOtDefaultValues = {\n    ...paginationParamDefault,\n    memberId: null,\n    fromDate: null,\n    toDate: null,\n    type: '',\n    dept: '',\n    status: ''\n};\n\nexport const manageOtSearchSchema = yup.object().shape({\n    fromDate: yup.date().nullable(),\n    toDate: yup.date().nullable().min(yup.ref('fromDate'), VALIDATE_MESSAGES.ENDDATE),\n    type: yup.string().nullable(),\n    status: yup.string().nullable()\n});\n\n// Add or edit Overtime Report\n\nexport interface IOvertimeRecord {\n    overtimeFrom: Date | null;\n    overtimeTo: Date | null;\n    overtimeHours: number | string;\n}\n\nexport interface IOvertimeReport {\n    idHexString: string;\n    userIdHexString: string;\n    fullName: string;\n    department: string;\n    manager: string;\n    nextManager: string;\n    overtimeReason: string;\n    projectName: IOption | null;\n    detail: string;\n    otherReason: string;\n    overtimeRecords: IOvertimeRecord[];\n    totalHours: number | string;\n    totalCompensateHours: number | string;\n    equivalentDays: number | string;\n    compensateType: string;\n    compensateFrom: Date | null;\n    compensateTo: Date | null;\n    status: string;\n    fromDate?: Date | null;\n    toDate?: Date | null;\n}\n\nexport const overtimeReportConfig: IOvertimeReport = {\n    idHexString: '',\n    userIdHexString: '',\n    fullName: '',\n    department: '',\n    manager: '',\n    nextManager: '',\n    overtimeReason: '',\n    projectName: null,\n    detail: '',\n    otherReason: '',\n    overtimeRecords: [\n        {\n            overtimeFrom: new Date(),\n            overtimeTo: new Date(),\n            overtimeHours: ''\n        }\n    ],\n    totalHours: '',\n    totalCompensateHours: '',\n    equivalentDays: '',\n    compensateType: '',\n    compensateFrom: new Date(),\n    compensateTo: new Date(),\n    status: ''\n};\n\nexport const overtimeReportSchema = yup.object().shape({\n    fullName: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n    department: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n    manager: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n    nextManager: yup.string().test('nextManager-validation', VALIDATE_MESSAGES.REQUIRED, function (value) {\n        const { status, userIdHexString } = this.parent;\n        const { currentUserId } = this.options.context || {};\n        const isCreator = userIdHexString === currentUserId;\n        const shouldValidate = status === EApproveStatus.AWAITING_QLTT && !isCreator;\n\n        return shouldValidate ? !!value : true;\n    }),\n    overtimeReason: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n    projectName: yup.mixed().when('overtimeReason', {\n        is: 'overtime-reason-1',\n        then: (schema) => schema.required(VALIDATE_MESSAGES.REQUIRED),\n        otherwise: (schema) => schema.nullable()\n    }),\n    detail: yup.string(),\n    otherReason: yup.string(),\n    overtimeRecords: yup.array().of(\n        yup.object().shape({\n            overtimeFrom: yup.date().required(VALIDATE_MESSAGES.REQUIRED),\n            overtimeTo: yup\n                .date()\n                .required(VALIDATE_MESSAGES.REQUIRED)\n                .test('is-after', VALIDATE_MESSAGES.AFTER_DAY, function (value) {\n                    const { overtimeFrom } = this.parent;\n                    const sameOrAfter = moment(value).startOf('day') >= moment(overtimeFrom).startOf('day');\n                    return !value || !overtimeFrom || sameOrAfter;\n                }),\n            overtimeHours: yup\n                .number()\n                .typeError(VALIDATE_MESSAGES.INVALID_NUMBER)\n                .positive(VALIDATE_MESSAGES.POSITIVE_NUMBER)\n                .required(VALIDATE_MESSAGES.REQUIRED)\n        })\n    ),\n    totalHours: yup\n        .number()\n        .typeError(VALIDATE_MESSAGES.INVALID_NUMBER)\n        .positive(VALIDATE_MESSAGES.POSITIVE_NUMBER)\n        .required(VALIDATE_MESSAGES.REQUIRED),\n    totalCompensateHours: yup\n        .number()\n        .typeError(VALIDATE_MESSAGES.INVALID_NUMBER)\n        .positive(VALIDATE_MESSAGES.POSITIVE_NUMBER)\n        .required(VALIDATE_MESSAGES.REQUIRED),\n    equivalentDays: yup.number().typeError(VALIDATE_MESSAGES.INVALID_NUMBER).required(VALIDATE_MESSAGES.REQUIRED),\n    compensateType: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n    compensateFrom: yup.date().when('compensateType', {\n        is: 'overtime-compensate-1',\n        then: (schema) => schema.required(VALIDATE_MESSAGES.REQUIRED),\n        otherwise: (schema) => schema.nullable()\n    }),\n    compensateTo: yup.date().when('compensateType', {\n        is: 'overtime-compensate-1',\n        then: (schema) =>\n            schema.required(VALIDATE_MESSAGES.REQUIRED).test('is-after', VALIDATE_MESSAGES.AFTER_DAY, function (value) {\n                const { compensateFrom } = this.parent;\n                const sameOrAfter = moment(value).startOf('day') >= moment(compensateFrom).startOf('day');\n                return !value || !compensateFrom || sameOrAfter;\n            }),\n        otherwise: (schema) => schema.nullable()\n    })\n});\n\nexport interface IUserDetail {\n    idHexString: string;\n    fullName: string;\n    memberCode: string;\n    position: string;\n    department: string;\n    group: string;\n    typeContract: string;\n}\n"], "mappings": "AAAA;AACA,SAASA,cAAc,EAAEC,sBAAsB,QAAQ,kBAAkB;AACzE,SAASC,iBAAiB,QAAQ,mBAAmB;AACrD,OAAOC,MAAM,MAAM,QAAQ;AAG3B;AACA,OAAO,KAAKC,GAAG,MAAM,KAAK;;AAE1B;;AAEA;;AAUA,OAAO,MAAMC,qBAA6C,GAAG;EACzD,GAAGJ,sBAAsB;EACzBK,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,EAAE;EACRC,IAAI,EAAE,EAAE;EACRC,MAAM,EAAE;AACZ,CAAC;AAED,OAAO,MAAMC,oBAAoB,GAAGR,GAAG,CAACS,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EACnDP,QAAQ,EAAEH,GAAG,CAACW,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;EAC/BR,MAAM,EAAEJ,GAAG,CAACW,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,GAAG,CAACb,GAAG,CAACc,GAAG,CAAC,UAAU,CAAC,EAAEhB,iBAAiB,CAACiB,OAAO,CAAC;EACjFV,IAAI,EAAEL,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC;EAC7BL,MAAM,EAAEP,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACJ,QAAQ,CAAC;AAClC,CAAC,CAAC;;AAEF;;AA+BA,OAAO,MAAMK,oBAAqC,GAAG;EACjDC,WAAW,EAAE,EAAE;EACfC,eAAe,EAAE,EAAE;EACnBC,QAAQ,EAAE,EAAE;EACZC,UAAU,EAAE,EAAE;EACdC,OAAO,EAAE,EAAE;EACXC,WAAW,EAAE,EAAE;EACfC,cAAc,EAAE,EAAE;EAClBC,WAAW,EAAE,IAAI;EACjBC,MAAM,EAAE,EAAE;EACVC,WAAW,EAAE,EAAE;EACfC,eAAe,EAAE,CACb;IACIC,YAAY,EAAE,IAAIC,IAAI,CAAC,CAAC;IACxBC,UAAU,EAAE,IAAID,IAAI,CAAC,CAAC;IACtBE,aAAa,EAAE;EACnB,CAAC,CACJ;EACDC,UAAU,EAAE,EAAE;EACdC,oBAAoB,EAAE,EAAE;EACxBC,cAAc,EAAE,EAAE;EAClBC,cAAc,EAAE,EAAE;EAClBC,cAAc,EAAE,IAAIP,IAAI,CAAC,CAAC;EAC1BQ,YAAY,EAAE,IAAIR,IAAI,CAAC,CAAC;EACxBvB,MAAM,EAAE;AACZ,CAAC;AAED,OAAO,MAAMgC,oBAAoB,GAAGvC,GAAG,CAACS,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EACnDU,QAAQ,EAAEpB,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACwB,QAAQ,CAAC1C,iBAAiB,CAAC2C,QAAQ,CAAC;EAC3DpB,UAAU,EAAErB,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACwB,QAAQ,CAAC1C,iBAAiB,CAAC2C,QAAQ,CAAC;EAC7DnB,OAAO,EAAEtB,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACwB,QAAQ,CAAC1C,iBAAiB,CAAC2C,QAAQ,CAAC;EAC1DlB,WAAW,EAAEvB,GAAG,CAACgB,MAAM,CAAC,CAAC,CAAC0B,IAAI,CAAC,wBAAwB,EAAE5C,iBAAiB,CAAC2C,QAAQ,EAAE,UAAUE,KAAK,EAAE;IAClG,MAAM;MAAEpC,MAAM;MAAEY;IAAgB,CAAC,GAAG,IAAI,CAACyB,MAAM;IAC/C,MAAM;MAAEC;IAAc,CAAC,GAAG,IAAI,CAACC,OAAO,CAACC,OAAO,IAAI,CAAC,CAAC;IACpD,MAAMC,SAAS,GAAG7B,eAAe,KAAK0B,aAAa;IACnD,MAAMI,cAAc,GAAG1C,MAAM,KAAKX,cAAc,CAACsD,aAAa,IAAI,CAACF,SAAS;IAE5E,OAAOC,cAAc,GAAG,CAAC,CAACN,KAAK,GAAG,IAAI;EAC1C,CAAC,CAAC;EACFnB,cAAc,EAAExB,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACwB,QAAQ,CAAC1C,iBAAiB,CAAC2C,QAAQ,CAAC;EACjEhB,WAAW,EAAEzB,GAAG,CAACmD,KAAK,CAAC,CAAC,CAACC,IAAI,CAAC,gBAAgB,EAAE;IAC5CC,EAAE,EAAE,mBAAmB;IACvBC,IAAI,EAAGC,MAAM,IAAKA,MAAM,CAACf,QAAQ,CAAC1C,iBAAiB,CAAC2C,QAAQ,CAAC;IAC7De,SAAS,EAAGD,MAAM,IAAKA,MAAM,CAAC3C,QAAQ,CAAC;EAC3C,CAAC,CAAC;EACFc,MAAM,EAAE1B,GAAG,CAACgB,MAAM,CAAC,CAAC;EACpBW,WAAW,EAAE3B,GAAG,CAACgB,MAAM,CAAC,CAAC;EACzBY,eAAe,EAAE5B,GAAG,CAACyD,KAAK,CAAC,CAAC,CAACC,EAAE,CAC3B1D,GAAG,CAACS,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IACfmB,YAAY,EAAE7B,GAAG,CAACW,IAAI,CAAC,CAAC,CAAC6B,QAAQ,CAAC1C,iBAAiB,CAAC2C,QAAQ,CAAC;IAC7DV,UAAU,EAAE/B,GAAG,CACVW,IAAI,CAAC,CAAC,CACN6B,QAAQ,CAAC1C,iBAAiB,CAAC2C,QAAQ,CAAC,CACpCC,IAAI,CAAC,UAAU,EAAE5C,iBAAiB,CAAC6D,SAAS,EAAE,UAAUhB,KAAK,EAAE;MAC5D,MAAM;QAAEd;MAAa,CAAC,GAAG,IAAI,CAACe,MAAM;MACpC,MAAMgB,WAAW,GAAG7D,MAAM,CAAC4C,KAAK,CAAC,CAACkB,OAAO,CAAC,KAAK,CAAC,IAAI9D,MAAM,CAAC8B,YAAY,CAAC,CAACgC,OAAO,CAAC,KAAK,CAAC;MACvF,OAAO,CAAClB,KAAK,IAAI,CAACd,YAAY,IAAI+B,WAAW;IACjD,CAAC,CAAC;IACN5B,aAAa,EAAEhC,GAAG,CACb8D,MAAM,CAAC,CAAC,CACRC,SAAS,CAACjE,iBAAiB,CAACkE,cAAc,CAAC,CAC3CC,QAAQ,CAACnE,iBAAiB,CAACoE,eAAe,CAAC,CAC3C1B,QAAQ,CAAC1C,iBAAiB,CAAC2C,QAAQ;EAC5C,CAAC,CACL,CAAC;EACDR,UAAU,EAAEjC,GAAG,CACV8D,MAAM,CAAC,CAAC,CACRC,SAAS,CAACjE,iBAAiB,CAACkE,cAAc,CAAC,CAC3CC,QAAQ,CAACnE,iBAAiB,CAACoE,eAAe,CAAC,CAC3C1B,QAAQ,CAAC1C,iBAAiB,CAAC2C,QAAQ,CAAC;EACzCP,oBAAoB,EAAElC,GAAG,CACpB8D,MAAM,CAAC,CAAC,CACRC,SAAS,CAACjE,iBAAiB,CAACkE,cAAc,CAAC,CAC3CC,QAAQ,CAACnE,iBAAiB,CAACoE,eAAe,CAAC,CAC3C1B,QAAQ,CAAC1C,iBAAiB,CAAC2C,QAAQ,CAAC;EACzCN,cAAc,EAAEnC,GAAG,CAAC8D,MAAM,CAAC,CAAC,CAACC,SAAS,CAACjE,iBAAiB,CAACkE,cAAc,CAAC,CAACxB,QAAQ,CAAC1C,iBAAiB,CAAC2C,QAAQ,CAAC;EAC7GL,cAAc,EAAEpC,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACwB,QAAQ,CAAC1C,iBAAiB,CAAC2C,QAAQ,CAAC;EACjEJ,cAAc,EAAErC,GAAG,CAACW,IAAI,CAAC,CAAC,CAACyC,IAAI,CAAC,gBAAgB,EAAE;IAC9CC,EAAE,EAAE,uBAAuB;IAC3BC,IAAI,EAAGC,MAAM,IAAKA,MAAM,CAACf,QAAQ,CAAC1C,iBAAiB,CAAC2C,QAAQ,CAAC;IAC7De,SAAS,EAAGD,MAAM,IAAKA,MAAM,CAAC3C,QAAQ,CAAC;EAC3C,CAAC,CAAC;EACF0B,YAAY,EAAEtC,GAAG,CAACW,IAAI,CAAC,CAAC,CAACyC,IAAI,CAAC,gBAAgB,EAAE;IAC5CC,EAAE,EAAE,uBAAuB;IAC3BC,IAAI,EAAGC,MAAM,IACTA,MAAM,CAACf,QAAQ,CAAC1C,iBAAiB,CAAC2C,QAAQ,CAAC,CAACC,IAAI,CAAC,UAAU,EAAE5C,iBAAiB,CAAC6D,SAAS,EAAE,UAAUhB,KAAK,EAAE;MACvG,MAAM;QAAEN;MAAe,CAAC,GAAG,IAAI,CAACO,MAAM;MACtC,MAAMgB,WAAW,GAAG7D,MAAM,CAAC4C,KAAK,CAAC,CAACkB,OAAO,CAAC,KAAK,CAAC,IAAI9D,MAAM,CAACsC,cAAc,CAAC,CAACwB,OAAO,CAAC,KAAK,CAAC;MACzF,OAAO,CAAClB,KAAK,IAAI,CAACN,cAAc,IAAIuB,WAAW;IACnD,CAAC,CAAC;IACNJ,SAAS,EAAGD,MAAM,IAAKA,MAAM,CAAC3C,QAAQ,CAAC;EAC3C,CAAC;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}