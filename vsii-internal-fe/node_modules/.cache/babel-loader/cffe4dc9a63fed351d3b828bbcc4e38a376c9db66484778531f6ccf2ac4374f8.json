{"ast": null, "code": "import{useEffect,useState}from'react';import{useSearchParams}from'react-router-dom';// store\nimport{useAppDispatch,useAppSelector}from'app/hooks';// project imports\nimport MainCard from'components/cards/MainCard';import{Table,TableFooter}from'components/extended/Table';import{SEARCH_PARAM_KEY,TEXT_CONFIG_SCREEN,paginationParamDefault}from'constants/Common';import{AddOrEditProject,ManageProjectSearch,ManageProjectTBody,ManageProjectThead}from'containers/administration';import{FilterCollapse}from'containers/search';import{getAllProject,loadingSelector,projectListSelector,projectpaginationSelector,resetProjectData}from'store/slice/projectSlice';import{exportDocument,getSearchParam,transformObject}from'utils/common';import{projectSearchConfig}from'./Config';import Api from'constants/Api';import{PERMISSIONS}from'constants/Permission';import{TableToolbar}from'containers';import{checkAllowedPermission}from'utils/authorization';// ==============================|| Manage Project ||============================== //\n/**\n *  URL Params\n *  page\n *  size\n *  projectType\n *  projectId\n *  projectName\n *  projectManager\n *  status\n */import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Project=()=>{const{manage_project}=TEXT_CONFIG_SCREEN.administration;// URL Params\nconst[searchParams,setSearchParams]=useSearchParams();const keyParams=[SEARCH_PARAM_KEY.page,SEARCH_PARAM_KEY.size,SEARCH_PARAM_KEY.projectType,SEARCH_PARAM_KEY.projectId,SEARCH_PARAM_KEY.projectName,SEARCH_PARAM_KEY.status,SEARCH_PARAM_KEY.projectManager,SEARCH_PARAM_KEY.fullname];const params=getSearchParam(keyParams,searchParams);transformObject(params);// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst{projectName,fullname,...cloneParams}=params;// Hooks, State, Variable\nconst defaultConditions={...projectSearchConfig,...cloneParams,projectId:params.projectId?{value:params.projectId,label:params.projectName}:null,projectManager:params.projectManager?{value:params.projectManager,label:params.fullname}:null,projectType:params.projectType?params.projectType:'',status:params.status?params.status:''};const dispatch=useAppDispatch();const projectList=useAppSelector(projectListSelector);const loading=useAppSelector(loadingSelector);const projectPagination=useAppSelector(projectpaginationSelector);const[conditions,setConditions]=useState(defaultConditions);const[formReset]=useState(defaultConditions);const[open,setOpen]=useState(false);const[isEditProject,setIsEditProject]=useState(false);const{projectPermission}=PERMISSIONS.admin;// Function\nconst getDataTable=()=>{var _conditions$projectId,_conditions$projectMa;dispatch(getAllProject({...conditions,projectId:(_conditions$projectId=conditions.projectId)===null||_conditions$projectId===void 0?void 0:_conditions$projectId.value,projectManager:(_conditions$projectMa=conditions.projectManager)===null||_conditions$projectMa===void 0?void 0:_conditions$projectMa.value,projectAuthorization:'false',page:conditions.page+1}));};// Event\nconst handleChangePage=(event,newPage)=>{setConditions({...conditions,page:newPage});setSearchParams({...params,page:newPage});};const handleChangeRowsPerPage=event=>{setConditions({...conditions,page:paginationParamDefault.page,size:parseInt(event.target.value,10)});setSearchParams({...params,page:paginationParamDefault.page,size:parseInt(event.target.value,10)});};const handleOpenDialog=isEdit=>{if(isEdit){setIsEditProject(true);}setOpen(true);};const handleCloseDialog=()=>{dispatch(resetProjectData());setOpen(false);setIsEditProject(false);};const handleExportDocument=()=>{var _conditions$projectId2,_conditions$projectMa2;// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst{page,size,...cloneConditions}=conditions;transformObject(cloneConditions);exportDocument(Api.project.getDownload.url,{...cloneConditions,projectId:(_conditions$projectId2=conditions.projectId)===null||_conditions$projectId2===void 0?void 0:_conditions$projectId2.value,projectManager:(_conditions$projectMa2=conditions.projectManager)===null||_conditions$projectMa2===void 0?void 0:_conditions$projectMa2.value});};// Handle submit\nconst handleSearch=value=>{const{projectId,projectManager}=value;transformObject(value);const searchParams={page:paginationParamDefault.page,size:conditions.size,...value};if(projectId&&projectManager){searchParams.projectId=projectId.value;searchParams.projectName=projectId.label;searchParams.projectManager=projectManager.value;searchParams.fullname=projectManager.label;}else if(projectId){searchParams.projectId=projectId.value;searchParams.projectName=projectId.label;}else if(projectManager){searchParams.projectManager=projectManager.value;searchParams.fullname=projectManager.label;}setSearchParams(searchParams);setConditions({...value,page:paginationParamDefault.page,size:conditions.size});};// Effect\nuseEffect(()=>{getDataTable();// eslint-disable-next-line react-hooks/exhaustive-deps\n},[conditions]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FilterCollapse,{handleExport:handleExportDocument,children:/*#__PURE__*/_jsx(ManageProjectSearch,{formReset:formReset,handleSearch:handleSearch})}),/*#__PURE__*/_jsxs(MainCard,{children:[checkAllowedPermission(projectPermission.add)&&/*#__PURE__*/_jsx(TableToolbar,{handleOpen:handleOpenDialog,addLabel:manage_project+'add-new'}),/*#__PURE__*/_jsx(Table,{heads:/*#__PURE__*/_jsx(ManageProjectThead,{}),isLoading:loading,data:projectList,children:/*#__PURE__*/_jsx(ManageProjectTBody,{pageNumber:conditions.page,pageSize:conditions.size,projects:projectList,handleOpen:handleOpenDialog})})]}),!loading&&/*#__PURE__*/_jsx(TableFooter,{pagination:{total:projectPagination===null||projectPagination===void 0?void 0:projectPagination.totalElement,page:conditions.page,size:conditions.size},onPageChange:handleChangePage,onRowsPerPageChange:handleChangeRowsPerPage}),/*#__PURE__*/_jsx(AddOrEditProject,{open:open,isEdit:isEditProject,onClose:handleCloseDialog,dataTable:getDataTable})]});};export default Project;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}