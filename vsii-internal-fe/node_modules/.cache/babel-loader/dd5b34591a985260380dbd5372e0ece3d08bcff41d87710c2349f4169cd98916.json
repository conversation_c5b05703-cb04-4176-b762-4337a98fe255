{"ast": null, "code": "import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getMenuItemUnstyledUtilityClass(slot) {\n  return generateUtilityClass('MuiMenuItemUnstyled', slot);\n}\nconst menuItemUnstyledClasses = generateUtilityClasses('MuiMenuItemUnstyled', ['root', 'disabled', 'focusVisible']);\nexport default menuItemUnstyledClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}