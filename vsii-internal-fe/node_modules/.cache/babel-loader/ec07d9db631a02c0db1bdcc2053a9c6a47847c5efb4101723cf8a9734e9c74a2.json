{"ast": null, "code": "import { complex } from '../../../value/types/complex/index.mjs';\nimport { filter } from '../../../value/types/complex/filter.mjs';\nimport { getDefaultValueType } from './defaults.mjs';\nfunction getAnimatableNone(key, value) {\n  var _a;\n  let defaultValueType = getDefaultValueType(key);\n  if (defaultValueType !== filter) defaultValueType = complex;\n  // If value is not recognised as animatable, ie \"none\", create an animatable version origin based on the target\n  return (_a = defaultValueType.getAnimatableNone) === null || _a === void 0 ? void 0 : _a.call(defaultValueType, value);\n}\nexport { getAnimatableNone };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}