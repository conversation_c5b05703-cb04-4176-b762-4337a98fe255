{"ast": null, "code": "import { warning } from 'hey-listen';\nimport * as React from 'react';\nimport { useConstant } from '../utils/use-constant.mjs';\nimport { LayoutGroup } from './LayoutGroup/index.mjs';\nlet id = 0;\nconst AnimateSharedLayout = _ref => {\n  let {\n    children\n  } = _ref;\n  React.useEffect(() => {\n    warning(false, \"AnimateSharedLayout is deprecated: https://www.framer.com/docs/guide-upgrade/##shared-layout-animations\");\n  }, []);\n  return React.createElement(LayoutGroup, {\n    id: useConstant(() => \"asl-\".concat(id++))\n  }, children);\n};\nexport { AnimateSharedLayout };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}