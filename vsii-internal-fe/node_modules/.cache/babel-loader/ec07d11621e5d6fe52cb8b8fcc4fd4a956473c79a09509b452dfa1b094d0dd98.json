{"ast": null, "code": "/* eslint-disable prettier/prettier *//* eslint-disable react-hooks/exhaustive-deps */import{Fragment,useCallback,useState}from'react';// third-party\nimport{FormattedMessage}from'react-intl';// material-ui\nimport{TableCell,TableHead,TableRow,useMediaQuery,useTheme}from'@mui/material';// project import\nimport{GROUP_COLOR_MONTH}from'constants/Common';import{calculationSum}from'utils/common';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const style_group_thead_even={'& .MuiTableCell-root:nth-of-type(8n+1),.MuiTableCell-root:nth-of-type(8n+2),.MuiTableCell-root:nth-of-type(8n+3),.MuiTableCell-root:nth-of-type(8n+4)':{backgroundColor:GROUP_COLOR_MONTH}};function MonthlyProductionPerformanceThead(props){const{months,count}=props;const theme=useTheme();const[actionEl,setActionEl]=useState(null);const[projectNameEl,setProjectNameEl]=useState(null);const[serviceTypeEl,setServiceTypeEl]=useState(null);const[contractTypeEl,setContractTypeEl]=useState(null);const[dimensions,setDimensions]=useState(null);const{salesReport}=TEXT_CONFIG_SCREEN;// action\nconst actionElRef=useCallback(domNode=>{if(count>0&&domNode)setActionEl(domNode.getBoundingClientRect());},[count]);// project name\nconst projectNameElRef=useCallback(domNode=>{if(count>0&&domNode)setProjectNameEl(domNode.getBoundingClientRect());},[count]);// service type\nconst serviceTypeElRef=useCallback(domNode=>{if(count>0&&domNode)setServiceTypeEl(domNode.getBoundingClientRect());},[count]);// contract type\nconst contractTypeElRef=useCallback(domNode=>{if(count>0&&domNode)setContractTypeEl(domNode.getBoundingClientRect());},[count]);// contract size\nconst callBackRef=useCallback(domNode=>{if(count>0&&domNode)setDimensions(domNode.getBoundingClientRect());},[count]);const matches=useMediaQuery(theme.breakpoints.up('md'));return/*#__PURE__*/_jsxs(TableHead,{children:[/*#__PURE__*/_jsxs(TableRow,{sx:{'& .MuiTableCell-root:nth-of-type(even)':{backgroundColor:GROUP_COLOR_MONTH},'& .MuiTableCell-root:nth-of-type(-n+5)':{position:!!matches?'sticky':'unset',zIndex:3,backgroundColor:'#fff'}},children:[/*#__PURE__*/_jsx(TableCell,{ref:actionElRef,rowSpan:2,sx:{left:0},children:/*#__PURE__*/_jsx(\"span\",{className:\"w60px\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monthlyProductionPerformance+'-action'})})}),/*#__PURE__*/_jsx(TableCell,{ref:projectNameElRef,rowSpan:2,sx:{left:actionEl&&(actionEl===null||actionEl===void 0?void 0:actionEl.width)},children:/*#__PURE__*/_jsx(\"span\",{className:\"w150px\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monthlyProductionPerformance+'-project-name'})})}),/*#__PURE__*/_jsx(TableCell,{ref:serviceTypeElRef,rowSpan:2,sx:{left:projectNameEl&&calculationSum(actionEl===null||actionEl===void 0?void 0:actionEl.width,projectNameEl===null||projectNameEl===void 0?void 0:projectNameEl.width)},children:/*#__PURE__*/_jsx(\"span\",{className:\"w60px\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monthlyProductionPerformance+'-service-type'})})}),/*#__PURE__*/_jsx(TableCell,{ref:contractTypeElRef,rowSpan:2,sx:{left:serviceTypeEl&&calculationSum(actionEl===null||actionEl===void 0?void 0:actionEl.width,projectNameEl===null||projectNameEl===void 0?void 0:projectNameEl.width,serviceTypeEl===null||serviceTypeEl===void 0?void 0:serviceTypeEl.width)},children:/*#__PURE__*/_jsx(\"span\",{className:\"w60px\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monthlyProductionPerformance+'-contract-type'})})}),/*#__PURE__*/_jsx(TableCell,{rowSpan:2,ref:callBackRef,sx:{left:contractTypeEl&&calculationSum(actionEl===null||actionEl===void 0?void 0:actionEl.width,projectNameEl===null||projectNameEl===void 0?void 0:projectNameEl.width,serviceTypeEl===null||serviceTypeEl===void 0?void 0:serviceTypeEl.width,contractTypeEl===null||contractTypeEl===void 0?void 0:contractTypeEl.width)},children:/*#__PURE__*/_jsx(\"span\",{className:\"w60px\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monthlyProductionPerformance+'-contract-size-usd'})})}),months.map((month,index)=>/*#__PURE__*/_jsx(TableCell,{align:\"center\",rowSpan:1,colSpan:4,children:/*#__PURE__*/_jsx(FormattedMessage,{id:month})},index))]}),/*#__PURE__*/_jsx(TableRow,{sx:{'& .MuiTableCell-root':{position:!!matches?'sticky':'unset',top:dimensions&&(dimensions===null||dimensions===void 0?void 0:dimensions.height)/3},...style_group_thead_even},children:months.map((_,i)=>/*#__PURE__*/_jsxs(Fragment,{children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monthlyProductionPerformance+'-delivered'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monthlyProductionPerformance+'-receivable'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monthlyProductionPerformance+'-received'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.monthlyProductionPerformance+'-financial'})})]},i))})]});}export default MonthlyProductionPerformanceThead;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}