{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * Output Feedback block mode.\n   */\n  CryptoJS.mode.OFB = function () {\n    var OFB = CryptoJS.lib.BlockCipherMode.extend();\n    var Encryptor = OFB.Encryptor = OFB.extend({\n      processBlock: function (words, offset) {\n        // Shortcuts\n        var cipher = this._cipher;\n        var blockSize = cipher.blockSize;\n        var iv = this._iv;\n        var keystream = this._keystream;\n\n        // Generate keystream\n        if (iv) {\n          keystream = this._keystream = iv.slice(0);\n\n          // Remove IV for subsequent blocks\n          this._iv = undefined;\n        }\n        cipher.encryptBlock(keystream, 0);\n\n        // Encrypt\n        for (var i = 0; i < blockSize; i++) {\n          words[offset + i] ^= keystream[i];\n        }\n      }\n    });\n    OFB.Decryptor = Encryptor;\n    return OFB;\n  }();\n  return CryptoJS.mode.OFB;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}