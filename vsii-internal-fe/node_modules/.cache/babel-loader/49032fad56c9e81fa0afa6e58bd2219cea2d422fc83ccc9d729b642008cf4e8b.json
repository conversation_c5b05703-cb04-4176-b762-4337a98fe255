{"ast": null, "code": "/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The CalendarPickerSkeleton component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { CalendarPickerSkeleton } from '@mui/x-date-pickers'`\", \"or `import { CalendarPickerSkeleton } from '@mui/x-date-pickers/CalendarPickerSkeleton'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n\n/**\n * @ignore - do not document.\n */\nconst CalendarPickerSkeleton = /*#__PURE__*/React.forwardRef(function DeprecatedCalendarPickerSkeleton() {\n  warn();\n  return null;\n});\nexport default CalendarPickerSkeleton;\nexport const calendarPickerSkeletonClasses = {};\nexport const getCalendarPickerSkeletonUtilityClass = slot => {\n  warn();\n  return '';\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}