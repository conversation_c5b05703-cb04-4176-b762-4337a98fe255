{"ast": null, "code": "// redux\nimport{createSlice}from'@reduxjs/toolkit';// project imports\n// interface\nconst initialState={open:false,conditions:null,isCommented:false,titleDetail:''};const commentSlice=createSlice({name:'comment',initialState,reducers:{openCommentDialog(state,action){const{titleDetail,conditions}=action.payload;state.conditions=conditions;state.titleDetail=titleDetail;state.open=true;},closeCommentDialog(state){state.open=false;},changeCommented(state,action){state.isCommented=action.payload;}}});export default commentSlice.reducer;export const{openCommentDialog,closeCommentDialog,changeCommented}=commentSlice.actions;export const openSelector=state=>state.comment.open;export const isCommentedSelector=state=>state.comment.isCommented;export const conditionsSelector=state=>state.comment.conditions;export const titleDetailSelector=state=>state.comment.titleDetail;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}