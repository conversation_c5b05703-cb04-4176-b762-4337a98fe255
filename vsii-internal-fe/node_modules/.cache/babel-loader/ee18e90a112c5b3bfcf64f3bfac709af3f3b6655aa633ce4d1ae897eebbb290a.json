{"ast": null, "code": "import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getOptionUnstyledUtilityClass(slot) {\n  return generateUtilityClass('MuiOptionUnstyled', slot);\n}\nconst optionUnstyledClasses = generateUtilityClasses('MuiOptionUnstyled', ['root', 'disabled', 'selected', 'highlighted']);\nexport default optionUnstyledClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}