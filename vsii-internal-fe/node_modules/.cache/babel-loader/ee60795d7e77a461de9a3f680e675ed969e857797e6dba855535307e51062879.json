{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardActionAreaUtilityClass(slot) {\n  return generateUtilityClass('MuiCardActionArea', slot);\n}\nconst cardActionAreaClasses = generateUtilityClasses('MuiCardActionArea', ['root', 'focusVisible', 'focusHighlight']);\nexport default cardActionAreaClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}