{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '@mui/base';\nexport function getTimelineSeparatorUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineSeparator', slot);\n}\nconst timelineSeparatorClasses = generateUtilityClasses('MuiTimelineSeparator', ['root']);\nexport default timelineSeparatorClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}