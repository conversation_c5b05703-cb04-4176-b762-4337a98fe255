{"ast": null, "code": "export const VALIDATE_MESSAGES = {\n  REQUIRED: 'required',\n  INVALID_NUMBER: 'invalid-number',\n  POSITIVE_NUMBER: 'positive-number',\n  LESS_OR_EQUAL: 'less-or-equal',\n  LARGER_OR_EQUAL: 'larger-or-equal',\n  TRIM: 'trim',\n  ONE_DECIMAL: 'one-decimal',\n  OUTBOARDDATE: 'outboarddate',\n  ENDDATE: 'enddate',\n  AFTER_DAY: 'after-day',\n  DATE_FORMAT: 'date-format',\n  SPECIAL_CHARACTERS: 'special-characters',\n  NUMBER: 'number',\n  MAX_LENGTH: 'max-length',\n  FUTURE_DATES: 'future-dates',\n  ABOUT_DAYS: 'about-days',\n  SPECIAL_CHARACTERS_PARTER_NAME: 'special-characters-parter-name',\n  SPECIAL_CHARACTERS_SUPPLIER_NAME: 'special-characters-supplier-name',\n  INVALID_PASSWORD_SPECIAL_CHAR: 'invalid-password-special',\n  NO_NUMBER: 'no-number',\n  DATE_OF_EXISTENCE: 'date-of-existence',\n  INVALID_EMAIL: 'invalid-email',\n  INVALID_NAME_FILE: 'invalid-name-file',\n  RISK_FACTOR_MIN: 'risk-factor-validate-min',\n  RISK_FACTOR_MAX: 'risk-factor-validate-max',\n  PLAN_DELIVERY_MIN: 'plan-delivery-min',\n  PLAN_DELIVERY_MAX: 'plan-delivery-max',\n  TOTAL_ON_TIME_DELIVERY_MIN: 'total-on-time-delivery-min',\n  TOTAL_ON_TIME_DELIVERY_MAX: 'total-on-time-delivery-max',\n  TOTAL_ON_TIME_DELIVERY_MORE: 'total-on-time-delivery-more',\n  TASK_MGT_MIN: 'task-mgt-min',\n  TASK_MGT_MAX: 'task-mgt-max',\n  PHONE_NUMBER: 'invalid-phone-number',\n  SKILL_CV_EXIST: 'skill-cv-exist',\n  INTERGER: 'number-integer',\n  NUMBER_LEAVE_DAYS_USED: 'validate-number-leave-days-used',\n  MAX_PHONE_NUMBER: 'phone-number-max',\n  FORMAT_PHONE_NUMBER: 'phone-number-format',\n  INVALID_PASSWORD_MIN: 'invalid-password-min',\n  INVALID_PASSWORD_MAX: 'invalid-password-max',\n  REPASSWORD_NOT_MATCHING: 'repassword-not-matching',\n  INVALID_CITIZENID_MAX: 'invalid-citizenId-max',\n  INVALID_BUSINESSID_MAX: 'invalid-businessId-max',\n  INVALID_TAXCODE_MAX: 'invalid-taxCode-max',\n  INVALID_CODE_NAME: 'invalid-code-name',\n  LEAVE_SEQUENCE: 'leave-sequence',\n  LESS_THAN: 'less-than',\n  LARGER_THAN: 'larger-than'\n};", "map": {"version": 3, "names": ["VALIDATE_MESSAGES", "REQUIRED", "INVALID_NUMBER", "POSITIVE_NUMBER", "LESS_OR_EQUAL", "LARGER_OR_EQUAL", "TRIM", "ONE_DECIMAL", "OUTBOARDDATE", "ENDDATE", "AFTER_DAY", "DATE_FORMAT", "SPECIAL_CHARACTERS", "NUMBER", "MAX_LENGTH", "FUTURE_DATES", "ABOUT_DAYS", "SPECIAL_CHARACTERS_PARTER_NAME", "SPECIAL_CHARACTERS_SUPPLIER_NAME", "INVALID_PASSWORD_SPECIAL_CHAR", "NO_NUMBER", "DATE_OF_EXISTENCE", "INVALID_EMAIL", "INVALID_NAME_FILE", "RISK_FACTOR_MIN", "RISK_FACTOR_MAX", "PLAN_DELIVERY_MIN", "PLAN_DELIVERY_MAX", "TOTAL_ON_TIME_DELIVERY_MIN", "TOTAL_ON_TIME_DELIVERY_MAX", "TOTAL_ON_TIME_DELIVERY_MORE", "TASK_MGT_MIN", "TASK_MGT_MAX", "PHONE_NUMBER", "SKILL_CV_EXIST", "INTERGER", "NUMBER_LEAVE_DAYS_USED", "MAX_PHONE_NUMBER", "FORMAT_PHONE_NUMBER", "INVALID_PASSWORD_MIN", "INVALID_PASSWORD_MAX", "REPASSWORD_NOT_MATCHING", "INVALID_CITIZENID_MAX", "INVALID_BUSINESSID_MAX", "INVALID_TAXCODE_MAX", "INVALID_CODE_NAME", "LEAVE_SEQUENCE", "LESS_THAN", "LARGER_THAN"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/constants/Message.ts"], "sourcesContent": ["export const VALIDATE_MESSAGES = {\n    REQUIRED: 'required',\n    INVALID_NUMBER: 'invalid-number',\n    POSITIVE_NUMBER: 'positive-number',\n    LESS_OR_EQUAL: 'less-or-equal',\n    LARGER_OR_EQUAL: 'larger-or-equal',\n    TRIM: 'trim',\n    ONE_DECIMAL: 'one-decimal',\n    OUTBOARDDATE: 'outboarddate',\n    ENDDATE: 'enddate',\n    AFTER_DAY: 'after-day',\n    DATE_FORMAT: 'date-format',\n    SPECIAL_CHARACTERS: 'special-characters',\n    NUMBER: 'number',\n    MAX_LENGTH: 'max-length',\n    FUTURE_DATES: 'future-dates',\n    ABOUT_DAYS: 'about-days',\n    SPECIAL_CHARACTERS_PARTER_NAME: 'special-characters-parter-name',\n    SPECIAL_CHARACTERS_SUPPLIER_NAME: 'special-characters-supplier-name',\n    INVALID_PASSWORD_SPECIAL_CHAR: 'invalid-password-special',\n    NO_NUMBER: 'no-number',\n    DATE_OF_EXISTENCE: 'date-of-existence',\n    INVALID_EMAIL: 'invalid-email',\n    INVALID_NAME_FILE: 'invalid-name-file',\n    RISK_FACTOR_MIN: 'risk-factor-validate-min',\n    RISK_FACTOR_MAX: 'risk-factor-validate-max',\n    PLAN_DELIVERY_MIN: 'plan-delivery-min',\n    PLAN_DELIVERY_MAX: 'plan-delivery-max',\n    TOTAL_ON_TIME_DELIVERY_MIN: 'total-on-time-delivery-min',\n    TOTAL_ON_TIME_DELIVERY_MAX: 'total-on-time-delivery-max',\n    TOTAL_ON_TIME_DELIVERY_MORE: 'total-on-time-delivery-more',\n    TASK_MGT_MIN: 'task-mgt-min',\n    TASK_MGT_MAX: 'task-mgt-max',\n    PHONE_NUMBER: 'invalid-phone-number',\n    SKILL_CV_EXIST: 'skill-cv-exist',\n    INTERGER: 'number-integer',\n    NUMBER_LEAVE_DAYS_USED: 'validate-number-leave-days-used',\n    MAX_PHONE_NUMBER: 'phone-number-max',\n    FORMAT_PHONE_NUMBER: 'phone-number-format',\n    INVALID_PASSWORD_MIN: 'invalid-password-min',\n    INVALID_PASSWORD_MAX: 'invalid-password-max',\n    REPASSWORD_NOT_MATCHING: 'repassword-not-matching',\n    INVALID_CITIZENID_MAX: 'invalid-citizenId-max',\n    INVALID_BUSINESSID_MAX: 'invalid-businessId-max',\n    INVALID_TAXCODE_MAX: 'invalid-taxCode-max',\n    INVALID_CODE_NAME: 'invalid-code-name',\n    LEAVE_SEQUENCE: 'leave-sequence',\n    LESS_THAN: 'less-than',\n    LARGER_THAN: 'larger-than'\n};\n"], "mappings": "AAAA,OAAO,MAAMA,iBAAiB,GAAG;EAC7BC,QAAQ,EAAE,UAAU;EACpBC,cAAc,EAAE,gBAAgB;EAChCC,eAAe,EAAE,iBAAiB;EAClCC,aAAa,EAAE,eAAe;EAC9BC,eAAe,EAAE,iBAAiB;EAClCC,IAAI,EAAE,MAAM;EACZC,WAAW,EAAE,aAAa;EAC1BC,YAAY,EAAE,cAAc;EAC5BC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE,aAAa;EAC1BC,kBAAkB,EAAE,oBAAoB;EACxCC,MAAM,EAAE,QAAQ;EAChBC,UAAU,EAAE,YAAY;EACxBC,YAAY,EAAE,cAAc;EAC5BC,UAAU,EAAE,YAAY;EACxBC,8BAA8B,EAAE,gCAAgC;EAChEC,gCAAgC,EAAE,kCAAkC;EACpEC,6BAA6B,EAAE,0BAA0B;EACzDC,SAAS,EAAE,WAAW;EACtBC,iBAAiB,EAAE,mBAAmB;EACtCC,aAAa,EAAE,eAAe;EAC9BC,iBAAiB,EAAE,mBAAmB;EACtCC,eAAe,EAAE,0BAA0B;EAC3CC,eAAe,EAAE,0BAA0B;EAC3CC,iBAAiB,EAAE,mBAAmB;EACtCC,iBAAiB,EAAE,mBAAmB;EACtCC,0BAA0B,EAAE,4BAA4B;EACxDC,0BAA0B,EAAE,4BAA4B;EACxDC,2BAA2B,EAAE,6BAA6B;EAC1DC,YAAY,EAAE,cAAc;EAC5BC,YAAY,EAAE,cAAc;EAC5BC,YAAY,EAAE,sBAAsB;EACpCC,cAAc,EAAE,gBAAgB;EAChCC,QAAQ,EAAE,gBAAgB;EAC1BC,sBAAsB,EAAE,iCAAiC;EACzDC,gBAAgB,EAAE,kBAAkB;EACpCC,mBAAmB,EAAE,qBAAqB;EAC1CC,oBAAoB,EAAE,sBAAsB;EAC5CC,oBAAoB,EAAE,sBAAsB;EAC5CC,uBAAuB,EAAE,yBAAyB;EAClDC,qBAAqB,EAAE,uBAAuB;EAC9CC,sBAAsB,EAAE,wBAAwB;EAChDC,mBAAmB,EAAE,qBAAqB;EAC1CC,iBAAiB,EAAE,mBAAmB;EACtCC,cAAc,EAAE,gBAAgB;EAChCC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}