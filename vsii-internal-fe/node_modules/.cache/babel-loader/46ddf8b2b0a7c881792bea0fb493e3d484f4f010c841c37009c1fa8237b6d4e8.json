{"ast": null, "code": "import{Grid,TableCell,TableRow}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ForeignLanguagePoint=()=>{return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(TableRow,{children:/*#__PURE__*/_jsx(TableCell,{colSpan:9,sx:{color:'#2548A1 !important',fontWeight:'600',fontStyle:'italic'},children:/*#__PURE__*/_jsx(Grid,{container:true,children:/*#__PURE__*/_jsxs(Grid,{item:true,xs:7.2,children:[/*#__PURE__*/_jsxs(Grid,{container:true,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:3,children:\"Experiences:\"}),/*#__PURE__*/_jsx(Grid,{item:true,xs:3,children:\"0: 0 month\"}),/*#__PURE__*/_jsx(Grid,{item:true,xs:3,children:\"2: 6 - 12 months\"}),/*#__PURE__*/_jsx(Grid,{item:true,xs:3,children:\"4: 2 - 5 years\"})]}),/*#__PURE__*/_jsxs(Grid,{container:true,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:3}),/*#__PURE__*/_jsx(Grid,{item:true,xs:3,children:\"1: 1 - 6 months\"}),/*#__PURE__*/_jsx(Grid,{item:true,xs:3,children:\"3: 1 - 2 years\"}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:3,children:[\"5: \",'>',\" 5 years\"]})]}),/*#__PURE__*/_jsxs(Grid,{container:true,sx:{m:'10px 0'},children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:3,children:[\"Last used:\",' ']}),/*#__PURE__*/_jsx(Grid,{item:true,xs:3,children:\"Last year used\"})]}),/*#__PURE__*/_jsxs(Grid,{container:true,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:3,children:\"Expert level:\"}),/*#__PURE__*/_jsx(Grid,{item:true,xs:3,children:\"1: Beginner\"}),/*#__PURE__*/_jsx(Grid,{item:true,xs:3,children:\"3: Fluent\"}),/*#__PURE__*/_jsx(Grid,{item:true,xs:3,children:\"5: Translator\"})]}),/*#__PURE__*/_jsxs(Grid,{container:true,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:3}),/*#__PURE__*/_jsx(Grid,{item:true,xs:3,children:\"2: Conversational\"}),/*#__PURE__*/_jsx(Grid,{item:true,xs:3,children:\"4: Expert\"})]})]})})})}),/*#__PURE__*/_jsxs(TableRow,{sx:{'& td':{textAlign:'center'}},children:[/*#__PURE__*/_jsx(TableCell,{rowSpan:2}),/*#__PURE__*/_jsx(TableCell,{className:\"w-300px\",rowSpan:2}),/*#__PURE__*/_jsx(TableCell,{className:\"w-250px\",rowSpan:2,children:\"Experiences\"}),/*#__PURE__*/_jsx(TableCell,{className:\"w-250px\",rowSpan:2,children:\"Lasted used\"}),/*#__PURE__*/_jsx(TableCell,{colSpan:5,children:\"Expert level\"})]}),/*#__PURE__*/_jsxs(TableRow,{sx:{'& td':{textAlign:'center'}},children:[/*#__PURE__*/_jsx(TableCell,{children:\"1\"}),/*#__PURE__*/_jsx(TableCell,{children:\"2\"}),/*#__PURE__*/_jsx(TableCell,{children:\"3\"}),/*#__PURE__*/_jsx(TableCell,{children:\"4\"}),/*#__PURE__*/_jsx(TableCell,{children:\"5\"})]})]});};export default ForeignLanguagePoint;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}