{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/monthly-effort/AddorEditReportProjectFields/ProgressMilstone.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Box, Grid, Typography } from '@mui/material';\nimport { useFormContext } from 'react-hook-form';\nimport { FormattedMessage } from 'react-intl';\nimport { PERCENT_PLACEHOLDER, PROJECT_IMPLEMENTATION_PHASE, PROJECT_PROGRESS_ASSESSMENT, TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { Input, PercentageFormat, Select } from 'components/extended/Form';\nimport { gridSpacing } from 'store/constant';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProgressMilstone = ({\n  disableEdit\n}) => {\n  _s();\n  const methods = useFormContext();\n  const [assesment, setAssement] = useState('');\n  const {\n    project_report\n  } = TEXT_CONFIG_SCREEN.generalReport;\n  useEffect(() => {\n    if (methods.getValues('monthlyReport.progressMilestone.implPhase') === 'Deployment') {\n      setAssement('Deployment');\n    } else {\n      methods.setValue('monthlyReport.progressMilestone.progressAssesment', '');\n    }\n  }, [methods]);\n  return /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: gridSpacing,\n    paddingX: 3,\n    rowGap: 2,\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      sm: 6,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          paddingX: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            flexBasis: '40%',\n            marginRight: '16px',\n            color: '#333',\n            display: 'flex',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: project_report + 'project-implementation-phase'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: '#D02C2C'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flexBasis: '60%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            name: \"monthlyReport.progressMilestone.implPhase\",\n            selects: PROJECT_IMPLEMENTATION_PHASE,\n            handleChange: e => setAssement(e.target.value === 'Deployment' ? e.target.value : ''),\n            disabled: disableEdit\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      sm: 6,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          paddingX: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            flexBasis: '40%',\n            marginRight: '16px',\n            color: '#333',\n            display: 'flex',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: project_report + 'work-completed'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 25\n          }, this), \" \", /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: '#D02C2C'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 85\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flexBasis: '60%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            name: \"monthlyReport.progressMilestone.workCompleted\",\n            textFieldProps: {\n              placeholder: PERCENT_PLACEHOLDER,\n              InputProps: {\n                inputComponent: PercentageFormat\n              }\n            },\n            disabled: disableEdit\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 13\n    }, this), assesment && /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      sm: 6,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          paddingX: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            flexBasis: '40%',\n            marginRight: '16px',\n            color: '#333',\n            display: 'flex',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: project_report + 'project-progress-assessment'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: '#D02C2C'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flexBasis: '60%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            name: \"monthlyReport.progressMilestone.progressAssesment\",\n            selects: PROJECT_PROGRESS_ASSESSMENT,\n            disabled: disableEdit\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      sm: 6,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'start',\n          paddingX: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            flexBasis: '40%',\n            marginRight: '16px',\n            color: '#333',\n            display: 'flex',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: project_report + 'finished-main-tasks-in-month'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 25\n          }, this), ' ', /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: '#D02C2C'\n            },\n            children: \"*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flexBasis: '60%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            name: \"monthlyReport.progressMilestone.finishedTasks\",\n            textFieldProps: {\n              multiline: true,\n              rows: 3\n            },\n            disabled: disableEdit\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      sm: 6,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'start',\n          paddingX: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            flexBasis: '40%',\n            marginRight: '16px',\n            color: '#333',\n            display: 'flex',\n            gap: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: project_report + 'delayed-not-finished'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flexBasis: '60%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            name: \"monthlyReport.progressMilestone.delayNotFinishPlan\",\n            textFieldProps: {\n              multiline: true,\n              rows: 3\n            },\n            disabled: disableEdit\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      sm: 6,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'start',\n          paddingX: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            flexBasis: '40%',\n            marginRight: '16px',\n            color: '#333',\n            display: 'flex',\n            gap: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: project_report + 'completed-milestones'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flexBasis: '60%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            name: \"monthlyReport.progressMilestone.completedMilestones\",\n            textFieldProps: {\n              multiline: true,\n              rows: 3\n            },\n            disabled: disableEdit\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      sm: 6,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'start',\n          paddingX: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            flexBasis: '40%',\n            marginRight: '16px',\n            color: '#333',\n            display: 'flex',\n            gap: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: project_report + 'next-milestone'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flexBasis: '60%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            name: \"monthlyReport.progressMilestone.nextMilestone\",\n            textFieldProps: {\n              multiline: true,\n              rows: 3\n            },\n            disabled: disableEdit\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 9\n  }, this);\n};\n_s(ProgressMilstone, \"wfaN/Uu9RYR4QlGvlL666W6GyGM=\", false, function () {\n  return [useFormContext];\n});\n_c = ProgressMilstone;\nexport default ProgressMilstone;\nvar _c;\n$RefreshReg$(_c, \"ProgressMilstone\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Grid", "Typography", "useFormContext", "FormattedMessage", "PERCENT_PLACEHOLDER", "PROJECT_IMPLEMENTATION_PHASE", "PROJECT_PROGRESS_ASSESSMENT", "TEXT_CONFIG_SCREEN", "Input", "PercentageFormat", "Select", "gridSpacing", "jsxDEV", "_jsxDEV", "ProgressMilstone", "disableEdit", "_s", "methods", "assesment", "setAssement", "project_report", "general<PERSON><PERSON><PERSON>", "getV<PERSON>ues", "setValue", "container", "spacing", "paddingX", "rowGap", "children", "item", "xs", "sm", "sx", "display", "alignItems", "flexBasis", "marginRight", "color", "gap", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "selects", "handleChange", "e", "target", "value", "disabled", "textFieldProps", "placeholder", "InputProps", "inputComponent", "multiline", "rows", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/monthly-effort/AddorEditReportProjectFields/ProgressMilstone.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { Box, Grid, Typography } from '@mui/material';\r\nimport { useFormContext } from 'react-hook-form';\r\nimport { FormattedMessage } from 'react-intl';\r\n\r\nimport { PERCENT_PLACEHOLDER, PROJECT_IMPLEMENTATION_PHASE, PROJECT_PROGRESS_ASSESSMENT, TEXT_CONFIG_SCREEN } from 'constants/Common';\r\nimport { Input, PercentageFormat, Select } from 'components/extended/Form';\r\nimport { gridSpacing } from 'store/constant';\r\n\r\ninterface IProgressMilstoneProps {\r\n    disableEdit: boolean;\r\n}\r\n\r\nconst ProgressMilstone = ({ disableEdit }: IProgressMilstoneProps) => {\r\n    const methods = useFormContext();\r\n    const [assesment, setAssement] = useState('');\r\n    const { project_report } = TEXT_CONFIG_SCREEN.generalReport;\r\n\r\n    useEffect(() => {\r\n        if (methods.getValues('monthlyReport.progressMilestone.implPhase') === 'Deployment') {\r\n            setAssement('Deployment');\r\n        } else {\r\n            methods.setValue('monthlyReport.progressMilestone.progressAssesment', '');\r\n        }\r\n    }, [methods]);\r\n\r\n    return (\r\n        <Grid container spacing={gridSpacing} paddingX={3} rowGap={2}>\r\n            <Grid item xs={12} sm={6}>\r\n                <Box sx={{ display: 'flex', alignItems: 'center', paddingX: 2 }}>\r\n                    <Typography sx={{ flexBasis: '40%', marginRight: '16px', color: '#333', display: 'flex', gap: 1 }}>\r\n                        <FormattedMessage id={project_report + 'project-implementation-phase'} />\r\n                        <Typography sx={{ color: '#D02C2C' }}>*</Typography>\r\n                    </Typography>\r\n                    <Box sx={{ flexBasis: '60%' }}>\r\n                        <Select\r\n                            name=\"monthlyReport.progressMilestone.implPhase\"\r\n                            selects={PROJECT_IMPLEMENTATION_PHASE}\r\n                            handleChange={(e: any) => setAssement(e.target.value === 'Deployment' ? e.target.value : '')}\r\n                            disabled={disableEdit}\r\n                        />\r\n                    </Box>\r\n                </Box>\r\n            </Grid>\r\n\r\n            <Grid item xs={12} sm={6}>\r\n                <Box sx={{ display: 'flex', alignItems: 'center', paddingX: 2 }}>\r\n                    <Typography sx={{ flexBasis: '40%', marginRight: '16px', color: '#333', display: 'flex', gap: 1 }}>\r\n                        <FormattedMessage id={project_report + 'work-completed'} /> <Typography sx={{ color: '#D02C2C' }}>*</Typography>\r\n                    </Typography>\r\n                    <Box sx={{ flexBasis: '60%' }}>\r\n                        <Input\r\n                            name=\"monthlyReport.progressMilestone.workCompleted\"\r\n                            textFieldProps={{\r\n                                placeholder: PERCENT_PLACEHOLDER,\r\n                                InputProps: {\r\n                                    inputComponent: PercentageFormat as any\r\n                                }\r\n                            }}\r\n                            disabled={disableEdit}\r\n                        />\r\n                    </Box>\r\n                </Box>\r\n            </Grid>\r\n\r\n            {assesment && (\r\n                <Grid item xs={12} sm={6}>\r\n                    <Box sx={{ display: 'flex', alignItems: 'center', paddingX: 2 }}>\r\n                        <Typography sx={{ flexBasis: '40%', marginRight: '16px', color: '#333', display: 'flex', gap: 1 }}>\r\n                            <FormattedMessage id={project_report + 'project-progress-assessment'} />\r\n                            <Typography sx={{ color: '#D02C2C' }}>*</Typography>\r\n                        </Typography>\r\n                        <Box sx={{ flexBasis: '60%' }}>\r\n                            <Select\r\n                                name=\"monthlyReport.progressMilestone.progressAssesment\"\r\n                                selects={PROJECT_PROGRESS_ASSESSMENT}\r\n                                disabled={disableEdit}\r\n                            />\r\n                        </Box>\r\n                    </Box>\r\n                </Grid>\r\n            )}\r\n\r\n            <Grid item xs={12} sm={6}>\r\n                <Box sx={{ display: 'flex', alignItems: 'start', paddingX: 2 }}>\r\n                    <Typography sx={{ flexBasis: '40%', marginRight: '16px', color: '#333', display: 'flex', gap: 1 }}>\r\n                        <FormattedMessage id={project_report + 'finished-main-tasks-in-month'} />{' '}\r\n                        <Typography sx={{ color: '#D02C2C' }}>*</Typography>\r\n                    </Typography>\r\n                    <Box sx={{ flexBasis: '60%' }}>\r\n                        <Input\r\n                            name=\"monthlyReport.progressMilestone.finishedTasks\"\r\n                            textFieldProps={{ multiline: true, rows: 3 }}\r\n                            disabled={disableEdit}\r\n                        />\r\n                    </Box>\r\n                </Box>\r\n            </Grid>\r\n\r\n            <Grid item xs={12} sm={6}>\r\n                <Box sx={{ display: 'flex', alignItems: 'start', paddingX: 2 }}>\r\n                    <Typography sx={{ flexBasis: '40%', marginRight: '16px', color: '#333', display: 'flex', gap: 1 }}>\r\n                        <FormattedMessage id={project_report + 'delayed-not-finished'} />\r\n                    </Typography>\r\n                    <Box sx={{ flexBasis: '60%' }}>\r\n                        <Input\r\n                            name=\"monthlyReport.progressMilestone.delayNotFinishPlan\"\r\n                            textFieldProps={{ multiline: true, rows: 3 }}\r\n                            disabled={disableEdit}\r\n                        />\r\n                    </Box>\r\n                </Box>\r\n            </Grid>\r\n\r\n            <Grid item xs={12} sm={6}>\r\n                <Box sx={{ display: 'flex', alignItems: 'start', paddingX: 2 }}>\r\n                    <Typography sx={{ flexBasis: '40%', marginRight: '16px', color: '#333', display: 'flex', gap: 1 }}>\r\n                        <FormattedMessage id={project_report + 'completed-milestones'} />\r\n                    </Typography>\r\n                    <Box sx={{ flexBasis: '60%' }}>\r\n                        <Input\r\n                            name=\"monthlyReport.progressMilestone.completedMilestones\"\r\n                            textFieldProps={{ multiline: true, rows: 3 }}\r\n                            disabled={disableEdit}\r\n                        />\r\n                    </Box>\r\n                </Box>\r\n            </Grid>\r\n\r\n            <Grid item xs={12} sm={6}>\r\n                <Box sx={{ display: 'flex', alignItems: 'start', paddingX: 2 }}>\r\n                    <Typography sx={{ flexBasis: '40%', marginRight: '16px', color: '#333', display: 'flex', gap: 1 }}>\r\n                        <FormattedMessage id={project_report + 'next-milestone'} />\r\n                    </Typography>\r\n                    <Box sx={{ flexBasis: '60%' }}>\r\n                        <Input\r\n                            name=\"monthlyReport.progressMilestone.nextMilestone\"\r\n                            textFieldProps={{ multiline: true, rows: 3 }}\r\n                            disabled={disableEdit}\r\n                        />\r\n                    </Box>\r\n                </Box>\r\n            </Grid>\r\n        </Grid>\r\n    );\r\n};\r\n\r\nexport default ProgressMilstone;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,IAAI,EAAEC,UAAU,QAAQ,eAAe;AACrD,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,gBAAgB,QAAQ,YAAY;AAE7C,SAASC,mBAAmB,EAAEC,4BAA4B,EAAEC,2BAA2B,EAAEC,kBAAkB,QAAQ,kBAAkB;AACrI,SAASC,KAAK,EAAEC,gBAAgB,EAAEC,MAAM,QAAQ,0BAA0B;AAC1E,SAASC,WAAW,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM7C,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC;AAAoC,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAMC,OAAO,GAAGf,cAAc,CAAC,CAAC;EAChC,MAAM,CAACgB,SAAS,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC7C,MAAM;IAAEsB;EAAe,CAAC,GAAGb,kBAAkB,CAACc,aAAa;EAE3DxB,SAAS,CAAC,MAAM;IACZ,IAAIoB,OAAO,CAACK,SAAS,CAAC,2CAA2C,CAAC,KAAK,YAAY,EAAE;MACjFH,WAAW,CAAC,YAAY,CAAC;IAC7B,CAAC,MAAM;MACHF,OAAO,CAACM,QAAQ,CAAC,mDAAmD,EAAE,EAAE,CAAC;IAC7E;EACJ,CAAC,EAAE,CAACN,OAAO,CAAC,CAAC;EAEb,oBACIJ,OAAA,CAACb,IAAI;IAACwB,SAAS;IAACC,OAAO,EAAEd,WAAY;IAACe,QAAQ,EAAE,CAAE;IAACC,MAAM,EAAE,CAAE;IAAAC,QAAA,gBACzDf,OAAA,CAACb,IAAI;MAAC6B,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrBf,OAAA,CAACd,GAAG;QAACiC,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAER,QAAQ,EAAE;QAAE,CAAE;QAAAE,QAAA,gBAC5Df,OAAA,CAACZ,UAAU;UAAC+B,EAAE,EAAE;YAAEG,SAAS,EAAE,KAAK;YAAEC,WAAW,EAAE,MAAM;YAAEC,KAAK,EAAE,MAAM;YAAEJ,OAAO,EAAE,MAAM;YAAEK,GAAG,EAAE;UAAE,CAAE;UAAAV,QAAA,gBAC9Ff,OAAA,CAACV,gBAAgB;YAACoC,EAAE,EAAEnB,cAAc,GAAG;UAA+B;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzE9B,OAAA,CAACZ,UAAU;YAAC+B,EAAE,EAAE;cAAEK,KAAK,EAAE;YAAU,CAAE;YAAAT,QAAA,EAAC;UAAC;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACb9B,OAAA,CAACd,GAAG;UAACiC,EAAE,EAAE;YAAEG,SAAS,EAAE;UAAM,CAAE;UAAAP,QAAA,eAC1Bf,OAAA,CAACH,MAAM;YACHkC,IAAI,EAAC,2CAA2C;YAChDC,OAAO,EAAExC,4BAA6B;YACtCyC,YAAY,EAAGC,CAAM,IAAK5B,WAAW,CAAC4B,CAAC,CAACC,MAAM,CAACC,KAAK,KAAK,YAAY,GAAGF,CAAC,CAACC,MAAM,CAACC,KAAK,GAAG,EAAE,CAAE;YAC7FC,QAAQ,EAAEnC;UAAY;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEP9B,OAAA,CAACb,IAAI;MAAC6B,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrBf,OAAA,CAACd,GAAG;QAACiC,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAER,QAAQ,EAAE;QAAE,CAAE;QAAAE,QAAA,gBAC5Df,OAAA,CAACZ,UAAU;UAAC+B,EAAE,EAAE;YAAEG,SAAS,EAAE,KAAK;YAAEC,WAAW,EAAE,MAAM;YAAEC,KAAK,EAAE,MAAM;YAAEJ,OAAO,EAAE,MAAM;YAAEK,GAAG,EAAE;UAAE,CAAE;UAAAV,QAAA,gBAC9Ff,OAAA,CAACV,gBAAgB;YAACoC,EAAE,EAAEnB,cAAc,GAAG;UAAiB;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,KAAC,eAAA9B,OAAA,CAACZ,UAAU;YAAC+B,EAAE,EAAE;cAAEK,KAAK,EAAE;YAAU,CAAE;YAAAT,QAAA,EAAC;UAAC;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxG,CAAC,eACb9B,OAAA,CAACd,GAAG;UAACiC,EAAE,EAAE;YAAEG,SAAS,EAAE;UAAM,CAAE;UAAAP,QAAA,eAC1Bf,OAAA,CAACL,KAAK;YACFoC,IAAI,EAAC,+CAA+C;YACpDO,cAAc,EAAE;cACZC,WAAW,EAAEhD,mBAAmB;cAChCiD,UAAU,EAAE;gBACRC,cAAc,EAAE7C;cACpB;YACJ,CAAE;YACFyC,QAAQ,EAAEnC;UAAY;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAENzB,SAAS,iBACNL,OAAA,CAACb,IAAI;MAAC6B,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrBf,OAAA,CAACd,GAAG;QAACiC,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAER,QAAQ,EAAE;QAAE,CAAE;QAAAE,QAAA,gBAC5Df,OAAA,CAACZ,UAAU;UAAC+B,EAAE,EAAE;YAAEG,SAAS,EAAE,KAAK;YAAEC,WAAW,EAAE,MAAM;YAAEC,KAAK,EAAE,MAAM;YAAEJ,OAAO,EAAE,MAAM;YAAEK,GAAG,EAAE;UAAE,CAAE;UAAAV,QAAA,gBAC9Ff,OAAA,CAACV,gBAAgB;YAACoC,EAAE,EAAEnB,cAAc,GAAG;UAA8B;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxE9B,OAAA,CAACZ,UAAU;YAAC+B,EAAE,EAAE;cAAEK,KAAK,EAAE;YAAU,CAAE;YAAAT,QAAA,EAAC;UAAC;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACb9B,OAAA,CAACd,GAAG;UAACiC,EAAE,EAAE;YAAEG,SAAS,EAAE;UAAM,CAAE;UAAAP,QAAA,eAC1Bf,OAAA,CAACH,MAAM;YACHkC,IAAI,EAAC,mDAAmD;YACxDC,OAAO,EAAEvC,2BAA4B;YACrC4C,QAAQ,EAAEnC;UAAY;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACT,eAED9B,OAAA,CAACb,IAAI;MAAC6B,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrBf,OAAA,CAACd,GAAG;QAACiC,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,OAAO;UAAER,QAAQ,EAAE;QAAE,CAAE;QAAAE,QAAA,gBAC3Df,OAAA,CAACZ,UAAU;UAAC+B,EAAE,EAAE;YAAEG,SAAS,EAAE,KAAK;YAAEC,WAAW,EAAE,MAAM;YAAEC,KAAK,EAAE,MAAM;YAAEJ,OAAO,EAAE,MAAM;YAAEK,GAAG,EAAE;UAAE,CAAE;UAAAV,QAAA,gBAC9Ff,OAAA,CAACV,gBAAgB;YAACoC,EAAE,EAAEnB,cAAc,GAAG;UAA+B;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAAC,GAAG,eAC7E9B,OAAA,CAACZ,UAAU;YAAC+B,EAAE,EAAE;cAAEK,KAAK,EAAE;YAAU,CAAE;YAAAT,QAAA,EAAC;UAAC;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACb9B,OAAA,CAACd,GAAG;UAACiC,EAAE,EAAE;YAAEG,SAAS,EAAE;UAAM,CAAE;UAAAP,QAAA,eAC1Bf,OAAA,CAACL,KAAK;YACFoC,IAAI,EAAC,+CAA+C;YACpDO,cAAc,EAAE;cAAEI,SAAS,EAAE,IAAI;cAAEC,IAAI,EAAE;YAAE,CAAE;YAC7CN,QAAQ,EAAEnC;UAAY;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEP9B,OAAA,CAACb,IAAI;MAAC6B,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrBf,OAAA,CAACd,GAAG;QAACiC,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,OAAO;UAAER,QAAQ,EAAE;QAAE,CAAE;QAAAE,QAAA,gBAC3Df,OAAA,CAACZ,UAAU;UAAC+B,EAAE,EAAE;YAAEG,SAAS,EAAE,KAAK;YAAEC,WAAW,EAAE,MAAM;YAAEC,KAAK,EAAE,MAAM;YAAEJ,OAAO,EAAE,MAAM;YAAEK,GAAG,EAAE;UAAE,CAAE;UAAAV,QAAA,eAC9Ff,OAAA,CAACV,gBAAgB;YAACoC,EAAE,EAAEnB,cAAc,GAAG;UAAuB;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACb9B,OAAA,CAACd,GAAG;UAACiC,EAAE,EAAE;YAAEG,SAAS,EAAE;UAAM,CAAE;UAAAP,QAAA,eAC1Bf,OAAA,CAACL,KAAK;YACFoC,IAAI,EAAC,oDAAoD;YACzDO,cAAc,EAAE;cAAEI,SAAS,EAAE,IAAI;cAAEC,IAAI,EAAE;YAAE,CAAE;YAC7CN,QAAQ,EAAEnC;UAAY;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEP9B,OAAA,CAACb,IAAI;MAAC6B,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrBf,OAAA,CAACd,GAAG;QAACiC,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,OAAO;UAAER,QAAQ,EAAE;QAAE,CAAE;QAAAE,QAAA,gBAC3Df,OAAA,CAACZ,UAAU;UAAC+B,EAAE,EAAE;YAAEG,SAAS,EAAE,KAAK;YAAEC,WAAW,EAAE,MAAM;YAAEC,KAAK,EAAE,MAAM;YAAEJ,OAAO,EAAE,MAAM;YAAEK,GAAG,EAAE;UAAE,CAAE;UAAAV,QAAA,eAC9Ff,OAAA,CAACV,gBAAgB;YAACoC,EAAE,EAAEnB,cAAc,GAAG;UAAuB;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACb9B,OAAA,CAACd,GAAG;UAACiC,EAAE,EAAE;YAAEG,SAAS,EAAE;UAAM,CAAE;UAAAP,QAAA,eAC1Bf,OAAA,CAACL,KAAK;YACFoC,IAAI,EAAC,qDAAqD;YAC1DO,cAAc,EAAE;cAAEI,SAAS,EAAE,IAAI;cAAEC,IAAI,EAAE;YAAE,CAAE;YAC7CN,QAAQ,EAAEnC;UAAY;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEP9B,OAAA,CAACb,IAAI;MAAC6B,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrBf,OAAA,CAACd,GAAG;QAACiC,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,OAAO;UAAER,QAAQ,EAAE;QAAE,CAAE;QAAAE,QAAA,gBAC3Df,OAAA,CAACZ,UAAU;UAAC+B,EAAE,EAAE;YAAEG,SAAS,EAAE,KAAK;YAAEC,WAAW,EAAE,MAAM;YAAEC,KAAK,EAAE,MAAM;YAAEJ,OAAO,EAAE,MAAM;YAAEK,GAAG,EAAE;UAAE,CAAE;UAAAV,QAAA,eAC9Ff,OAAA,CAACV,gBAAgB;YAACoC,EAAE,EAAEnB,cAAc,GAAG;UAAiB;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACb9B,OAAA,CAACd,GAAG;UAACiC,EAAE,EAAE;YAAEG,SAAS,EAAE;UAAM,CAAE;UAAAP,QAAA,eAC1Bf,OAAA,CAACL,KAAK;YACFoC,IAAI,EAAC,+CAA+C;YACpDO,cAAc,EAAE;cAAEI,SAAS,EAAE,IAAI;cAAEC,IAAI,EAAE;YAAE,CAAE;YAC7CN,QAAQ,EAAEnC;UAAY;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEf,CAAC;AAAC3B,EAAA,CApIIF,gBAAgB;EAAA,QACFZ,cAAc;AAAA;AAAAuD,EAAA,GAD5B3C,gBAAgB;AAsItB,eAAeA,gBAAgB;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}