{"ast": null, "code": "/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The DesktopDatePicker component was moved from `@mui/lab` to `@mui/x-date-pickers`.', '', \"You should use `import { DesktopDatePicker } from '@mui/x-date-pickers'`\", \"or `import { DesktopDatePicker } from '@mui/x-date-pickers/DesktopDatePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n\n/**\n * @ignore - do not document.\n */\nconst DesktopDatePicker = /*#__PURE__*/React.forwardRef(function DeprecatedDesktopDatePicker() {\n  warn();\n  return null;\n});\nexport default DesktopDatePicker;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}