{"ast": null, "code": "import { LookupSupportedLocales } from '@formatjs/intl-localematcher';\nimport { ToObject } from './262';\nimport { GetOption } from './GetOption';\n/**\n * https://tc39.es/ecma402/#sec-supportedlocales\n * @param availableLocales\n * @param requestedLocales\n * @param options\n */\nexport function SupportedLocales(availableLocales, requestedLocales, options) {\n  var matcher = 'best fit';\n  if (options !== undefined) {\n    options = ToObject(options);\n    matcher = GetOption(options, 'localeMatcher', 'string', ['lookup', 'best fit'], 'best fit');\n  }\n  if (matcher === 'best fit') {\n    return LookupSupportedLocales(Array.from(availableLocales), requestedLocales);\n  }\n  return LookupSupportedLocales(Array.from(availableLocales), requestedLocales);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}