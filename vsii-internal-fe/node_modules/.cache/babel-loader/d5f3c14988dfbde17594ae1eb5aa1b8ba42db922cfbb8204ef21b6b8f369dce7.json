{"ast": null, "code": "import{createSlice}from'@reduxjs/toolkit';const initialState={show:false,isTabWrap:false};const deniedPermissionSlice=createSlice({name:'deniedPermission',initialState,reducers:{openDeniedPermission(state,action){state.show=true;state.isTabWrap=!!action.payload;},closeDeniedPermission(state){state.show=false;state.isTabWrap=false;}}});export default deniedPermissionSlice.reducer;export const{closeDeniedPermission,openDeniedPermission}=deniedPermissionSlice.actions;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}