{"ast": null, "code": "// material-ui\nimport{Table<PERSON>ell,TableHead,TableRow,Typography}from'@mui/material';import useMediaQuery from'@mui/material/useMediaQuery';import{useTheme}from'@mui/material/styles';import{FormattedMessage}from'react-intl';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function MonthlyEffortORMReportThead(_ref){let{currentYear,projectLength}=_ref;const theme=useTheme();const matches=useMediaQuery(theme.breakpoints.up('md'));const{ORMReport}=TEXT_CONFIG_SCREEN.generalReport;return/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{rowSpan:2,sx:{left:!!matches?0:'unset',zIndex:3},children:/*#__PURE__*/_jsx(Typography,{sx:theme=>({fontWeight:'bold'}),children:/*#__PURE__*/_jsx(FormattedMessage,{id:ORMReport+'no'})})}),/*#__PURE__*/_jsx(TableCell,{rowSpan:2,children:/*#__PURE__*/_jsx(Typography,{sx:theme=>({fontWeight:'bold'}),children:/*#__PURE__*/_jsx(FormattedMessage,{id:ORMReport+'report-name'})})}),/*#__PURE__*/_jsx(TableCell,{rowSpan:2,children:/*#__PURE__*/_jsx(Typography,{sx:theme=>({fontWeight:'bold'}),children:/*#__PURE__*/_jsx(FormattedMessage,{id:ORMReport+'month'})})}),/*#__PURE__*/_jsx(TableCell,{rowSpan:2,children:/*#__PURE__*/_jsx(Typography,{sx:theme=>({fontWeight:'bold'}),children:/*#__PURE__*/_jsx(FormattedMessage,{id:ORMReport+'department'})})}),/*#__PURE__*/_jsx(TableCell,{rowSpan:2,children:/*#__PURE__*/_jsx(Typography,{sx:theme=>({fontWeight:'bold'}),children:/*#__PURE__*/_jsx(FormattedMessage,{id:ORMReport+'upload-user'})})}),/*#__PURE__*/_jsx(TableCell,{rowSpan:2,children:/*#__PURE__*/_jsx(Typography,{sx:theme=>({fontWeight:'bold'}),children:/*#__PURE__*/_jsx(FormattedMessage,{id:ORMReport+'last-update'})})}),/*#__PURE__*/_jsx(TableCell,{rowSpan:2})]})});}export default MonthlyEffortORMReportThead;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}