{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { invariant } from '@formatjs/ecma402-abstract';\nimport { IntlMessageFormat } from 'intl-messageformat';\nimport { MissingTranslationError, MessageFormatError } from './error';\nimport { TYPE } from '@formatjs/icu-messageformat-parser';\nfunction setTimeZoneInOptions(opts, timeZone) {\n  return Object.keys(opts).reduce(function (all, k) {\n    all[k] = __assign({\n      timeZone: timeZone\n    }, opts[k]);\n    return all;\n  }, {});\n}\nfunction deepMergeOptions(opts1, opts2) {\n  var keys = Object.keys(__assign(__assign({}, opts1), opts2));\n  return keys.reduce(function (all, k) {\n    all[k] = __assign(__assign({}, opts1[k] || {}), opts2[k] || {});\n    return all;\n  }, {});\n}\nfunction deepMergeFormatsAndSetTimeZone(f1, timeZone) {\n  if (!timeZone) {\n    return f1;\n  }\n  var mfFormats = IntlMessageFormat.formats;\n  return __assign(__assign(__assign({}, mfFormats), f1), {\n    date: deepMergeOptions(setTimeZoneInOptions(mfFormats.date, timeZone), setTimeZoneInOptions(f1.date || {}, timeZone)),\n    time: deepMergeOptions(setTimeZoneInOptions(mfFormats.time, timeZone), setTimeZoneInOptions(f1.time || {}, timeZone))\n  });\n}\nexport var formatMessage = function (_a, state, messageDescriptor, values, opts) {\n  var locale = _a.locale,\n    formats = _a.formats,\n    messages = _a.messages,\n    defaultLocale = _a.defaultLocale,\n    defaultFormats = _a.defaultFormats,\n    fallbackOnEmptyString = _a.fallbackOnEmptyString,\n    onError = _a.onError,\n    timeZone = _a.timeZone,\n    defaultRichTextElements = _a.defaultRichTextElements;\n  if (messageDescriptor === void 0) {\n    messageDescriptor = {\n      id: ''\n    };\n  }\n  var msgId = messageDescriptor.id,\n    defaultMessage = messageDescriptor.defaultMessage;\n  // `id` is a required field of a Message Descriptor.\n  invariant(!!msgId, \"[@formatjs/intl] An `id` must be provided to format a message. You can either:\\n1. Configure your build toolchain with [babel-plugin-formatjs](https://formatjs.io/docs/tooling/babel-plugin)\\nor [@formatjs/ts-transformer](https://formatjs.io/docs/tooling/ts-transformer) OR\\n2. Configure your `eslint` config to include [eslint-plugin-formatjs](https://formatjs.io/docs/tooling/linter#enforce-id)\\nto autofix this issue\");\n  var id = String(msgId);\n  var message =\n  // In case messages is Object.create(null)\n  // e.g import('foo.json') from webpack)\n  // See https://github.com/formatjs/formatjs/issues/1914\n  messages && Object.prototype.hasOwnProperty.call(messages, id) && messages[id];\n  // IMPORTANT: Hot path if `message` is AST with a single literal node\n  if (Array.isArray(message) && message.length === 1 && message[0].type === TYPE.literal) {\n    return message[0].value;\n  }\n  // IMPORTANT: Hot path straight lookup for performance\n  if (!values && message && typeof message === 'string' && !defaultRichTextElements) {\n    return message.replace(/'\\{(.*?)\\}'/gi, \"{$1}\");\n  }\n  values = __assign(__assign({}, defaultRichTextElements), values || {});\n  formats = deepMergeFormatsAndSetTimeZone(formats, timeZone);\n  defaultFormats = deepMergeFormatsAndSetTimeZone(defaultFormats, timeZone);\n  if (!message) {\n    if (fallbackOnEmptyString === false && message === '') {\n      return message;\n    }\n    if (!defaultMessage || locale && locale.toLowerCase() !== defaultLocale.toLowerCase()) {\n      // This prevents warnings from littering the console in development\n      // when no `messages` are passed into the <IntlProvider> for the\n      // default locale.\n      onError(new MissingTranslationError(messageDescriptor, locale));\n    }\n    if (defaultMessage) {\n      try {\n        var formatter = state.getMessageFormat(defaultMessage, defaultLocale, defaultFormats, opts);\n        return formatter.format(values);\n      } catch (e) {\n        onError(new MessageFormatError(\"Error formatting default message for: \\\"\".concat(id, \"\\\", rendering default message verbatim\"), locale, messageDescriptor, e));\n        return typeof defaultMessage === 'string' ? defaultMessage : id;\n      }\n    }\n    return id;\n  }\n  // We have the translated message\n  try {\n    var formatter = state.getMessageFormat(message, locale, formats, __assign({\n      formatters: state\n    }, opts || {}));\n    return formatter.format(values);\n  } catch (e) {\n    onError(new MessageFormatError(\"Error formatting message: \\\"\".concat(id, \"\\\", using \").concat(defaultMessage ? 'default message' : 'id', \" as fallback.\"), locale, messageDescriptor, e));\n  }\n  if (defaultMessage) {\n    try {\n      var formatter = state.getMessageFormat(defaultMessage, defaultLocale, defaultFormats, opts);\n      return formatter.format(values);\n    } catch (e) {\n      onError(new MessageFormatError(\"Error formatting the default message for: \\\"\".concat(id, \"\\\", rendering message verbatim\"), locale, messageDescriptor, e));\n    }\n  }\n  if (typeof message === 'string') {\n    return message;\n  }\n  if (typeof defaultMessage === 'string') {\n    return defaultMessage;\n  }\n  return id;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}