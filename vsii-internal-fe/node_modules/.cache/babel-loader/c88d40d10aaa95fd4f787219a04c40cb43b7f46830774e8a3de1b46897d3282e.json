{"ast": null, "code": "import{useEffect}from'react';import{Navigate,useLocation}from'react-router-dom';// project imports\nimport{authSelector}from'store/slice/authSlice';import{useAppSelector}from'app/hooks';import{AuthLogin}from'containers/authentication';import{ESTATUS_LOGIN}from'constants/Common';import{DASHBOARD_PATH}from'constants/Config';import{authLayoutRef}from'layout/AuthLayout';// ================================|| AUTH - LOGIN ||================================ //\nimport{jsx as _jsx}from\"react/jsx-runtime\";const Login=()=>{const location=useLocation();const{status}=useAppSelector(authSelector);const{state}=location;useEffect(()=>{var _authLayoutRef$curren;(_authLayoutRef$curren=authLayoutRef.current)===null||_authLayoutRef$curren===void 0?void 0:_authLayoutRef$curren.setState({title:'Login'});},[]);if(status===ESTATUS_LOGIN.SUCCESS){var _state$from;return/*#__PURE__*/_jsx(Navigate,{to:state?\"\".concat(state.from.pathname).concat((_state$from=state.from)===null||_state$from===void 0?void 0:_state$from.search):DASHBOARD_PATH});}return/*#__PURE__*/_jsx(AuthLogin,{});};export default Login;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}