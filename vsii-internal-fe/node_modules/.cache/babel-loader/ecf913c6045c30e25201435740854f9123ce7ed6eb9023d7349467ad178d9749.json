{"ast": null, "code": "import equal from 'fast-deep-equal';\nimport { useCallback, useState } from 'react';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect.js';\n/**\n *\n * @param monitor The monitor to collect state from\n * @param collect The collecting function\n * @param onUpdate A method to invoke when updates occur\n */\nexport function useCollector(monitor, collect, onUpdate) {\n  const [collected, setCollected] = useState(() => collect(monitor));\n  const updateCollected = useCallback(() => {\n    const nextValue = collect(monitor);\n    // This needs to be a deep-equality check because some monitor-collected values\n    // include XYCoord objects that may be equivalent, but do not have instance equality.\n    if (!equal(collected, nextValue)) {\n      setCollected(nextValue);\n      if (onUpdate) {\n        onUpdate();\n      }\n    }\n  }, [collected, monitor, onUpdate]);\n  // update the collected properties after react renders.\n  // Note that the \"Dustbin Stress Test\" fails if this is not\n  // done when the component updates\n  useIsomorphicLayoutEffect(updateCollected);\n  return [collected, updateCollected];\n}\n\n//# sourceMappingURL=useCollector.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}