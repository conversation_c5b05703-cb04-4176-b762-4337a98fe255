{"ast": null, "code": "import { resolveElements } from '../utils/resolve-elements.es.js';\nimport { isFunction } from '@motionone/utils';\nconst thresholds = {\n  any: 0,\n  all: 1\n};\nfunction inView(elementOrSelector, onStart) {\n  let {\n    root,\n    margin: rootMargin,\n    amount = \"any\"\n  } = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  /**\n   * If this browser doesn't support IntersectionObserver, return a dummy stop function.\n   * Default triggering of onStart is tricky - it could be used for starting/stopping\n   * videos, lazy loading content etc. We could provide an option to enable a fallback, or\n   * provide a fallback callback option.\n   */\n  if (typeof IntersectionObserver === \"undefined\") {\n    return () => {};\n  }\n  const elements = resolveElements(elementOrSelector);\n  const activeIntersections = new WeakMap();\n  const onIntersectionChange = entries => {\n    entries.forEach(entry => {\n      const onEnd = activeIntersections.get(entry.target);\n      /**\n       * If there's no change to the intersection, we don't need to\n       * do anything here.\n       */\n      if (entry.isIntersecting === Boolean(onEnd)) return;\n      if (entry.isIntersecting) {\n        const newOnEnd = onStart(entry);\n        if (isFunction(newOnEnd)) {\n          activeIntersections.set(entry.target, newOnEnd);\n        } else {\n          observer.unobserve(entry.target);\n        }\n      } else if (onEnd) {\n        onEnd(entry);\n        activeIntersections.delete(entry.target);\n      }\n    });\n  };\n  const observer = new IntersectionObserver(onIntersectionChange, {\n    root,\n    rootMargin,\n    threshold: typeof amount === \"number\" ? amount : thresholds[amount]\n  });\n  elements.forEach(element => observer.observe(element));\n  return () => observer.disconnect();\n}\nexport { inView };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}