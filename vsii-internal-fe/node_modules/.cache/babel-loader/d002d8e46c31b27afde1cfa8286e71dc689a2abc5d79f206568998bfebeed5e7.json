{"ast": null, "code": "import{FormattedMessage}from'react-intl';// project imports\nimport{PERMISSIONS}from'constants/Permission';// material-ui\nimport{TableCell,TableHead,TableRow}from'@mui/material';import{checkAllowedPermission}from'utils/authorization';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TechnologyConfigThead=()=>{const{emailConfigPermission}=PERMISSIONS.admin;const{cv_config}=TEXT_CONFIG_SCREEN.administration;return/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:cv_config+'no'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:cv_config+'technology'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:cv_config+'skill'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:cv_config+'last-update'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:cv_config+'user-update'})}),checkAllowedPermission(emailConfigPermission.edit)&&/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:cv_config+'actions'})})]})});};export default TechnologyConfigThead;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}