{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { filterProps, getNamedFormat } from './utils';\nimport { IntlFormatError } from './error';\nvar DATE_TIME_FORMAT_OPTIONS = ['formatMatcher', 'timeZone', 'hour12', 'weekday', 'era', 'year', 'month', 'day', 'hour', 'minute', 'second', 'timeZoneName', 'hourCycle', 'dateStyle', 'timeStyle', 'calendar',\n// 'dayPeriod',\n'numberingSystem', 'fractionalSecondDigits'];\nexport function getFormatter(_a, type, getDateTimeFormat, options) {\n  var locale = _a.locale,\n    formats = _a.formats,\n    onError = _a.onError,\n    timeZone = _a.timeZone;\n  if (options === void 0) {\n    options = {};\n  }\n  var format = options.format;\n  var defaults = __assign(__assign({}, timeZone && {\n    timeZone: timeZone\n  }), format && getNamedFormat(formats, type, format, onError));\n  var filteredOptions = filterProps(options, DATE_TIME_FORMAT_OPTIONS, defaults);\n  if (type === 'time' && !filteredOptions.hour && !filteredOptions.minute && !filteredOptions.second && !filteredOptions.timeStyle && !filteredOptions.dateStyle) {\n    // Add default formatting options if hour, minute, or second isn't defined.\n    filteredOptions = __assign(__assign({}, filteredOptions), {\n      hour: 'numeric',\n      minute: 'numeric'\n    });\n  }\n  return getDateTimeFormat(locale, filteredOptions);\n}\nexport function formatDate(config, getDateTimeFormat) {\n  var _a = [];\n  for (var _i = 2; _i < arguments.length; _i++) {\n    _a[_i - 2] = arguments[_i];\n  }\n  var value = _a[0],\n    _b = _a[1],\n    options = _b === void 0 ? {} : _b;\n  var date = typeof value === 'string' ? new Date(value || 0) : value;\n  try {\n    return getFormatter(config, 'date', getDateTimeFormat, options).format(date);\n  } catch (e) {\n    config.onError(new IntlFormatError('Error formatting date.', config.locale, e));\n  }\n  return String(date);\n}\nexport function formatTime(config, getDateTimeFormat) {\n  var _a = [];\n  for (var _i = 2; _i < arguments.length; _i++) {\n    _a[_i - 2] = arguments[_i];\n  }\n  var value = _a[0],\n    _b = _a[1],\n    options = _b === void 0 ? {} : _b;\n  var date = typeof value === 'string' ? new Date(value || 0) : value;\n  try {\n    return getFormatter(config, 'time', getDateTimeFormat, options).format(date);\n  } catch (e) {\n    config.onError(new IntlFormatError('Error formatting time.', config.locale, e));\n  }\n  return String(date);\n}\nexport function formatDateTimeRange(config, getDateTimeFormat) {\n  var _a = [];\n  for (var _i = 2; _i < arguments.length; _i++) {\n    _a[_i - 2] = arguments[_i];\n  }\n  var from = _a[0],\n    to = _a[1],\n    _b = _a[2],\n    options = _b === void 0 ? {} : _b;\n  var timeZone = config.timeZone,\n    locale = config.locale,\n    onError = config.onError;\n  var filteredOptions = filterProps(options, DATE_TIME_FORMAT_OPTIONS, timeZone ? {\n    timeZone: timeZone\n  } : {});\n  try {\n    return getDateTimeFormat(locale, filteredOptions).formatRange(from, to);\n  } catch (e) {\n    onError(new IntlFormatError('Error formatting date time range.', config.locale, e));\n  }\n  return String(from);\n}\nexport function formatDateToParts(config, getDateTimeFormat) {\n  var _a = [];\n  for (var _i = 2; _i < arguments.length; _i++) {\n    _a[_i - 2] = arguments[_i];\n  }\n  var value = _a[0],\n    _b = _a[1],\n    options = _b === void 0 ? {} : _b;\n  var date = typeof value === 'string' ? new Date(value || 0) : value;\n  try {\n    return getFormatter(config, 'date', getDateTimeFormat, options).formatToParts(date); // TODO: remove this when https://github.com/microsoft/TypeScript/pull/50402 is merged\n  } catch (e) {\n    config.onError(new IntlFormatError('Error formatting date.', config.locale, e));\n  }\n  return [];\n}\nexport function formatTimeToParts(config, getDateTimeFormat) {\n  var _a = [];\n  for (var _i = 2; _i < arguments.length; _i++) {\n    _a[_i - 2] = arguments[_i];\n  }\n  var value = _a[0],\n    _b = _a[1],\n    options = _b === void 0 ? {} : _b;\n  var date = typeof value === 'string' ? new Date(value || 0) : value;\n  try {\n    return getFormatter(config, 'time', getDateTimeFormat, options).formatToParts(date); // TODO: remove this when https://github.com/microsoft/TypeScript/pull/50402 is merged\n  } catch (e) {\n    config.onError(new IntlFormatError('Error formatting time.', config.locale, e));\n  }\n  return [];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}