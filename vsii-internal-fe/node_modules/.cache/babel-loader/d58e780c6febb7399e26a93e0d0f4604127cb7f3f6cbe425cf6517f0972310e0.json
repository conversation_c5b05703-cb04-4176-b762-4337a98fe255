{"ast": null, "code": "import { cubicBezier, steps } from '@motionone/easing';\nimport { isFunction, isCubicBezier, noopReturn } from '@motionone/utils';\nconst namedEasings = {\n  ease: cubicBezier(0.25, 0.1, 0.25, 1.0),\n  \"ease-in\": cubicBezier(0.42, 0.0, 1.0, 1.0),\n  \"ease-in-out\": cubicBezier(0.42, 0.0, 0.58, 1.0),\n  \"ease-out\": cubicBezier(0.0, 0.0, 0.58, 1.0)\n};\nconst functionArgsRegex = /\\((.*?)\\)/;\nfunction getEasingFunction(definition) {\n  // If already an easing function, return\n  if (isFunction(definition)) return definition;\n  // If an easing curve definition, return bezier function\n  if (isCubicBezier(definition)) return cubicBezier(...definition);\n  // If we have a predefined easing function, return\n  const namedEasing = namedEasings[definition];\n  if (namedEasing) return namedEasing;\n  // If this is a steps function, attempt to create easing curve\n  if (definition.startsWith(\"steps\")) {\n    const args = functionArgsRegex.exec(definition);\n    if (args) {\n      const argsArray = args[1].split(\",\");\n      return steps(parseFloat(argsArray[0]), argsArray[1].trim());\n    }\n  }\n  return noopReturn;\n}\nexport { getEasingFunction };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}