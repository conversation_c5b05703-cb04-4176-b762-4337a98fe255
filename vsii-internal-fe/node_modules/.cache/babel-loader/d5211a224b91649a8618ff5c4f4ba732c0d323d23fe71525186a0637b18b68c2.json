{"ast": null, "code": "import * as React from 'react';\nimport { useControlled } from '@mui/material/utils';\nimport { arrayIncludes } from '../utils/utils';\nexport function useViews(_ref) {\n  let {\n    onChange,\n    onViewChange,\n    openTo,\n    view,\n    views\n  } = _ref;\n  var _views, _views2;\n  const [openView, setOpenView] = useControlled({\n    name: 'Picker',\n    state: 'view',\n    controlled: view,\n    default: openTo && arrayIncludes(views, openTo) ? openTo : views[0]\n  });\n  const previousView = (_views = views[views.indexOf(openView) - 1]) != null ? _views : null;\n  const nextView = (_views2 = views[views.indexOf(openView) + 1]) != null ? _views2 : null;\n  const changeView = React.useCallback(newView => {\n    setOpenView(newView);\n    if (onViewChange) {\n      onViewChange(newView);\n    }\n  }, [setOpenView, onViewChange]);\n  const openNext = React.useCallback(() => {\n    if (nextView) {\n      changeView(nextView);\n    }\n  }, [nextView, changeView]);\n  const handleChangeAndOpenNext = React.useCallback((date, currentViewSelectionState) => {\n    const isSelectionFinishedOnCurrentView = currentViewSelectionState === 'finish';\n    const globalSelectionState = isSelectionFinishedOnCurrentView && Boolean(nextView) ? 'partial' : currentViewSelectionState;\n    onChange(date, globalSelectionState);\n    if (isSelectionFinishedOnCurrentView) {\n      openNext();\n    }\n  }, [nextView, onChange, openNext]);\n  return {\n    handleChangeAndOpenNext,\n    nextView,\n    previousView,\n    openNext,\n    openView,\n    setOpenView: changeView\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}