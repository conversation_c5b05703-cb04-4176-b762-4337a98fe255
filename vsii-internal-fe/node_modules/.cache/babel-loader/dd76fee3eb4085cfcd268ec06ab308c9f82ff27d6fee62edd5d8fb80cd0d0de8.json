{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/layout/MenuList/NavCollapse.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from 'react';\nimport { useLocation } from 'react-router-dom';\n\n// material-ui\nimport { styled, useTheme } from '@mui/material/styles';\nimport { Box, ClickAwayListener, Collapse, List, ListItemButton, ListItemIcon, ListItemText, Paper, Popper, Typography } from '@mui/material';\n\n// project imports\nimport NavItem from './NavItem';\nimport Transitions from 'components/extended/Transitions';\nimport { useAppSelector } from 'app/hooks';\nimport useConfig from 'hooks/useConfig';\nimport { userAuthorization } from 'utils/authorization';\n\n// assets\nimport { IconChevronDown, IconChevronRight, IconChevronUp } from '@tabler/icons';\nimport FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';\n\n// mini-menu - wrapper\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PopperStyledMini = styled(Popper)(({\n  theme\n}) => ({\n  overflow: 'visible',\n  zIndex: 1202,\n  minWidth: 180,\n  '&:before': {\n    content: '\"\"',\n    backgroundColor: theme.palette.background.paper,\n    transform: 'translateY(-50%) rotate(45deg)',\n    zIndex: 120,\n    borderLeft: `1px solid ${theme.palette.divider}`,\n    borderBottom: `1px solid ${theme.palette.divider}`\n  }\n}));\n\n// ==============================|| SIDEBAR MENU LIST COLLAPSE ITEMS ||============================== //\n_c = PopperStyledMini;\nconst NavCollapse = ({\n  menu,\n  level,\n  parentId\n}) => {\n  _s();\n  var _menu$children;\n  const theme = useTheme();\n  const {\n    borderRadius\n  } = useConfig();\n  const {\n    drawerOpen\n  } = useAppSelector(state => state.menu);\n  const [open, setOpen] = useState(false);\n  const [selected, setSelected] = useState(null);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const handleClickMini = event => {\n    setAnchorEl(null);\n    if (drawerOpen) {\n      setOpen(!open);\n      setSelected(!selected ? menu.id : null);\n    } else {\n      setAnchorEl(event === null || event === void 0 ? void 0 : event.currentTarget);\n    }\n  };\n  const handleClosePopper = () => {\n    setOpen(false);\n    setSelected(null);\n    setAnchorEl(null);\n  };\n  const openMini = Boolean(anchorEl);\n  const {\n    pathname\n  } = useLocation();\n  const checkOpenForParent = (child, id) => {\n    child.forEach(item => {\n      if (item.url === pathname) {\n        setOpen(true);\n        setSelected(id);\n      }\n    });\n  };\n\n  // menu collapse for sub-levels\n  useEffect(() => {\n    setOpen(false);\n    setSelected(null);\n    if (openMini) setAnchorEl(null);\n    if (menu.children) {\n      menu.children.forEach(item => {\n        var _item$children;\n        if ((_item$children = item.children) !== null && _item$children !== void 0 && _item$children.length) {\n          checkOpenForParent(item.children, menu.id);\n        }\n        if (item.url === pathname) {\n          setSelected(menu.id);\n          setOpen(true);\n        }\n      });\n    }\n\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [pathname, menu.children]);\n\n  // menu collapse & item\n  const menus = (_menu$children = menu.children) === null || _menu$children === void 0 ? void 0 : _menu$children.map(item => {\n    const {\n      isAllowFunctions\n    } = userAuthorization(item.access);\n    switch (item.type) {\n      case 'collapse':\n        return (isAllowFunctions || !item.access) && /*#__PURE__*/_jsxDEV(NavCollapse, {\n          menu: item,\n          level: level + 1,\n          parentId: parentId\n        }, item.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 59\n        }, this);\n      case 'item':\n        return (isAllowFunctions || !item.access) && /*#__PURE__*/_jsxDEV(NavItem, {\n          item: item,\n          level: level + 1,\n          parentId: parentId\n        }, item.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 62\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"error\",\n          align: \"center\",\n          children: \"Menu Items Error\"\n        }, item.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 21\n        }, this);\n    }\n  });\n  const isSelected = selected === menu.id;\n  const Icon = menu.icon;\n  const menuIcon = menu.icon ? /*#__PURE__*/_jsxDEV(Icon, {\n    strokeWidth: 1.5,\n    size: drawerOpen ? '20px' : '24px',\n    style: {\n      color: isSelected ? theme.palette.secondary.main : theme.palette.text.primary\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 9\n  }, this) : /*#__PURE__*/_jsxDEV(FiberManualRecordIcon, {\n    sx: {\n      color: isSelected ? theme.palette.secondary.main : theme.palette.text.primary,\n      width: isSelected ? 8 : 6,\n      height: isSelected ? 8 : 6\n    },\n    fontSize: level > 0 ? 'inherit' : 'medium'\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 9\n  }, this);\n  const collapseIcon = drawerOpen ? /*#__PURE__*/_jsxDEV(IconChevronUp, {\n    stroke: 1.5,\n    size: \"16px\",\n    style: {\n      marginTop: 'auto',\n      marginBottom: 'auto'\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 157,\n    columnNumber: 9\n  }, this) : /*#__PURE__*/_jsxDEV(IconChevronRight, {\n    stroke: 1.5,\n    size: \"16px\",\n    style: {\n      marginTop: 'auto',\n      marginBottom: 'auto'\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 159,\n    columnNumber: 9\n  }, this);\n  const textColor = theme.palette.mode === 'dark' ? 'grey.400' : 'text.primary';\n  const iconSelectedColor = theme.palette.mode === 'dark' && drawerOpen ? 'text.primary' : 'secondary.main';\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n      sx: {\n        zIndex: 1201,\n        borderRadius: `${borderRadius}px`,\n        mb: 0.5,\n        pl: drawerOpen ? `${level * 24}px` : 1.25,\n        ...(drawerOpen && level === 1 && theme.palette.mode !== 'dark' && {\n          '&:hover': {\n            background: theme.palette.secondary.light\n          },\n          '&.Mui-selected': {\n            background: theme.palette.secondary.light,\n            color: iconSelectedColor,\n            '&:hover': {\n              color: iconSelectedColor,\n              background: theme.palette.secondary.light\n            }\n          }\n        }),\n        ...((!drawerOpen || level !== 1) && {\n          py: level === 1 ? 0 : 1,\n          '&:hover': {\n            bgcolor: 'transparent'\n          },\n          '&.Mui-selected': {\n            '&:hover': {\n              bgcolor: 'transparent'\n            },\n            bgcolor: 'transparent'\n          }\n        })\n      },\n      selected: isSelected,\n      ...(!drawerOpen && {\n        onMouseEnter: handleClickMini,\n        onMouseLeave: handleClosePopper\n      }),\n      onClick: handleClickMini,\n      children: [menuIcon && /*#__PURE__*/_jsxDEV(ListItemIcon, {\n        sx: {\n          minWidth: level === 1 ? 30 : 18,\n          color: isSelected ? iconSelectedColor : textColor,\n          ...(!drawerOpen && level === 1 && {\n            borderRadius: `${borderRadius}px`,\n            width: 46,\n            height: 46,\n            alignItems: 'center',\n            justifyContent: 'center',\n            '&:hover': {\n              bgcolor: theme.palette.mode === 'dark' ? theme.palette.secondary.main + 25 : 'secondary.light'\n            },\n            ...(isSelected && {\n              bgcolor: theme.palette.mode === 'dark' ? theme.palette.secondary.main + 25 : 'secondary.light',\n              '&:hover': {\n                bgcolor: theme.palette.mode === 'dark' ? theme.palette.secondary.main + 30 : 'secondary.light'\n              }\n            })\n          })\n        },\n        children: menuIcon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 21\n      }, this), (drawerOpen || !drawerOpen && level !== 1) && /*#__PURE__*/_jsxDEV(ListItemText, {\n        primary: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: isSelected ? 'h5' : 'body1',\n          color: \"inherit\",\n          sx: {\n            my: 'auto'\n          },\n          children: menu.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 21\n      }, this), openMini || open ? collapseIcon : /*#__PURE__*/_jsxDEV(IconChevronDown, {\n        stroke: 1.5,\n        size: \"16px\",\n        style: {\n          marginTop: 'auto',\n          marginBottom: 'auto'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 21\n      }, this), !drawerOpen && /*#__PURE__*/_jsxDEV(PopperStyledMini, {\n        open: openMini,\n        anchorEl: anchorEl,\n        placement: \"right-start\",\n        style: {\n          zIndex: 2001\n        },\n        modifiers: [{\n          name: 'offset',\n          options: {\n            offset: [-12, 0]\n          }\n        }],\n        children: ({\n          TransitionProps\n        }) => /*#__PURE__*/_jsxDEV(Transitions, {\n          in: openMini,\n          ...TransitionProps,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              overflow: 'hidden',\n              mt: 1.5,\n              boxShadow: theme.shadows[8],\n              backgroundImage: 'none'\n            },\n            children: /*#__PURE__*/_jsxDEV(ClickAwayListener, {\n              onClickAway: handleClosePopper,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                children: menus\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 13\n    }, this), drawerOpen && /*#__PURE__*/_jsxDEV(Collapse, {\n      in: open,\n      timeout: \"auto\",\n      unmountOnExit: true,\n      children: open && /*#__PURE__*/_jsxDEV(List, {\n        component: \"div\",\n        disablePadding: true,\n        sx: {\n          position: 'relative',\n          '&:after': {\n            content: \"''\",\n            position: 'absolute',\n            left: '32px',\n            top: 0,\n            height: '100%',\n            width: '1px',\n            opacity: theme.palette.mode === 'dark' ? 0.2 : 1,\n            background: theme.palette.mode === 'dark' ? theme.palette.dark.light : theme.palette.primary.light\n          }\n        },\n        children: menus\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 25\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true);\n};\n_s(NavCollapse, \"9jS6DmxK2r+bD2M2hktRlq9Yu9Q=\", false, function () {\n  return [useTheme, useConfig, useAppSelector, useLocation];\n});\n_c2 = NavCollapse;\nexport default NavCollapse;\nvar _c, _c2;\n$RefreshReg$(_c, \"PopperStyledMini\");\n$RefreshReg$(_c2, \"NavCollapse\");", "map": {"version": 3, "names": ["useEffect", "useState", "useLocation", "styled", "useTheme", "Box", "ClickAwayListener", "Collapse", "List", "ListItemButton", "ListItemIcon", "ListItemText", "Paper", "<PERSON><PERSON>", "Typography", "NavItem", "Transitions", "useAppSelector", "useConfig", "userAuthorization", "IconChevronDown", "IconChevronRight", "IconChevronUp", "FiberManualRecordIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PopperStyledMini", "theme", "overflow", "zIndex", "min<PERSON><PERSON><PERSON>", "content", "backgroundColor", "palette", "background", "paper", "transform", "borderLeft", "divider", "borderBottom", "_c", "NavCollapse", "menu", "level", "parentId", "_s", "_menu$children", "borderRadius", "drawerOpen", "state", "open", "<PERSON><PERSON><PERSON>", "selected", "setSelected", "anchorEl", "setAnchorEl", "handleClickMini", "event", "id", "currentTarget", "handleClosePopper", "openMini", "Boolean", "pathname", "checkOpenForParent", "child", "for<PERSON>ach", "item", "url", "children", "_item$children", "length", "menus", "map", "isAllowFunctions", "access", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "color", "align", "isSelected", "Icon", "icon", "menuIcon", "strokeWidth", "size", "style", "secondary", "main", "text", "primary", "sx", "width", "height", "fontSize", "collapseIcon", "stroke", "marginTop", "marginBottom", "textColor", "mode", "iconSelectedColor", "mb", "pl", "light", "py", "bgcolor", "onMouseEnter", "onMouseLeave", "onClick", "alignItems", "justifyContent", "my", "title", "placement", "modifiers", "name", "options", "offset", "TransitionProps", "in", "mt", "boxShadow", "shadows", "backgroundImage", "onClickAway", "timeout", "unmountOnExit", "component", "disablePadding", "position", "left", "top", "opacity", "dark", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/layout/MenuList/NavCollapse.tsx"], "sourcesContent": ["import { useEffect, useState } from 'react';\nimport { useLocation } from 'react-router-dom';\n\n// material-ui\nimport { styled, useTheme } from '@mui/material/styles';\nimport {\n    Box,\n    ClickAwayListener,\n    Collapse,\n    List,\n    ListItemButton,\n    ListItemIcon,\n    ListItemText,\n    Paper,\n    Popper,\n    Typography\n} from '@mui/material';\n\n// project imports\nimport NavItem from './NavItem';\nimport Transitions from 'components/extended/Transitions';\nimport { useAppSelector } from 'app/hooks';\nimport useConfig from 'hooks/useConfig';\nimport { NavItemType } from 'types';\nimport { userAuthorization } from 'utils/authorization';\n\n// assets\nimport { IconChevronDown, IconChevronRight, IconChevronUp } from '@tabler/icons';\nimport FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';\n\n// mini-menu - wrapper\nconst PopperStyledMini = styled(Popper)(({ theme }) => ({\n    overflow: 'visible',\n    zIndex: 1202,\n    minWidth: 180,\n    '&:before': {\n        content: '\"\"',\n        backgroundColor: theme.palette.background.paper,\n        transform: 'translateY(-50%) rotate(45deg)',\n        zIndex: 120,\n        borderLeft: `1px solid ${theme.palette.divider}`,\n        borderBottom: `1px solid ${theme.palette.divider}`\n    }\n}));\n\n// ==============================|| SIDEBAR MENU LIST COLLAPSE ITEMS ||============================== //\n\ntype VirtualElement = {\n    getBoundingClientRect: () => ClientRect | DOMRect;\n    contextElement?: Element;\n};\n\ninterface NavCollapseProps {\n    menu: NavItemType;\n    level: number;\n    parentId: string;\n}\n\nconst NavCollapse = ({ menu, level, parentId }: NavCollapseProps) => {\n    const theme = useTheme();\n\n    const { borderRadius } = useConfig();\n    const { drawerOpen } = useAppSelector((state) => state.menu);\n\n    const [open, setOpen] = useState(false);\n    const [selected, setSelected] = useState<string | null | undefined>(null);\n    const [anchorEl, setAnchorEl] = useState<VirtualElement | (() => VirtualElement) | null | undefined>(null);\n\n    const handleClickMini = (event: React.MouseEvent<HTMLAnchorElement> | React.MouseEvent<HTMLDivElement, MouseEvent> | undefined) => {\n        setAnchorEl(null);\n        if (drawerOpen) {\n            setOpen(!open);\n            setSelected(!selected ? menu.id : null);\n        } else {\n            setAnchorEl(event?.currentTarget);\n        }\n    };\n\n    const handleClosePopper = () => {\n        setOpen(false);\n        setSelected(null);\n        setAnchorEl(null);\n    };\n\n    const openMini = Boolean(anchorEl);\n    const { pathname } = useLocation();\n\n    const checkOpenForParent = (child: NavItemType[], id: string) => {\n        child.forEach((item: NavItemType) => {\n            if (item.url === pathname) {\n                setOpen(true);\n                setSelected(id);\n            }\n        });\n    };\n\n    // menu collapse for sub-levels\n    useEffect(() => {\n        setOpen(false);\n        setSelected(null);\n        if (openMini) setAnchorEl(null);\n        if (menu.children) {\n            menu.children.forEach((item: NavItemType) => {\n                if (item.children?.length) {\n                    checkOpenForParent(item.children, menu.id!);\n                }\n                if (item.url === pathname) {\n                    setSelected(menu.id);\n                    setOpen(true);\n                }\n            });\n        }\n\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [pathname, menu.children]);\n\n    // menu collapse & item\n    const menus = menu.children?.map((item) => {\n        const { isAllowFunctions } = userAuthorization(item.access);\n        switch (item.type) {\n            case 'collapse':\n                return (\n                    (isAllowFunctions || !item.access) && <NavCollapse key={item.id} menu={item} level={level + 1} parentId={parentId} />\n                );\n            case 'item':\n                return (isAllowFunctions || !item.access) && <NavItem key={item.id} item={item} level={level + 1} parentId={parentId} />;\n            default:\n                return (\n                    <Typography key={item.id} variant=\"h6\" color=\"error\" align=\"center\">\n                        Menu Items Error\n                    </Typography>\n                );\n        }\n    });\n\n    const isSelected = selected === menu.id;\n\n    const Icon = menu.icon!;\n    const menuIcon = menu.icon ? (\n        <Icon\n            strokeWidth={1.5}\n            size={drawerOpen ? '20px' : '24px'}\n            style={{ color: isSelected ? theme.palette.secondary.main : theme.palette.text.primary }}\n        />\n    ) : (\n        <FiberManualRecordIcon\n            sx={{\n                color: isSelected ? theme.palette.secondary.main : theme.palette.text.primary,\n                width: isSelected ? 8 : 6,\n                height: isSelected ? 8 : 6\n            }}\n            fontSize={level > 0 ? 'inherit' : 'medium'}\n        />\n    );\n\n    const collapseIcon = drawerOpen ? (\n        <IconChevronUp stroke={1.5} size=\"16px\" style={{ marginTop: 'auto', marginBottom: 'auto' }} />\n    ) : (\n        <IconChevronRight stroke={1.5} size=\"16px\" style={{ marginTop: 'auto', marginBottom: 'auto' }} />\n    );\n\n    const textColor = theme.palette.mode === 'dark' ? 'grey.400' : 'text.primary';\n    const iconSelectedColor = theme.palette.mode === 'dark' && drawerOpen ? 'text.primary' : 'secondary.main';\n\n    return (\n        <>\n            <ListItemButton\n                sx={{\n                    zIndex: 1201,\n                    borderRadius: `${borderRadius}px`,\n                    mb: 0.5,\n                    pl: drawerOpen ? `${level * 24}px` : 1.25,\n                    ...(drawerOpen &&\n                        level === 1 &&\n                        theme.palette.mode !== 'dark' && {\n                            '&:hover': {\n                                background: theme.palette.secondary.light\n                            },\n                            '&.Mui-selected': {\n                                background: theme.palette.secondary.light,\n                                color: iconSelectedColor,\n                                '&:hover': {\n                                    color: iconSelectedColor,\n                                    background: theme.palette.secondary.light\n                                }\n                            }\n                        }),\n                    ...((!drawerOpen || level !== 1) && {\n                        py: level === 1 ? 0 : 1,\n                        '&:hover': {\n                            bgcolor: 'transparent'\n                        },\n                        '&.Mui-selected': {\n                            '&:hover': {\n                                bgcolor: 'transparent'\n                            },\n                            bgcolor: 'transparent'\n                        }\n                    })\n                }}\n                selected={isSelected}\n                {...(!drawerOpen && { onMouseEnter: handleClickMini, onMouseLeave: handleClosePopper })}\n                onClick={handleClickMini}\n            >\n                {menuIcon && (\n                    <ListItemIcon\n                        sx={{\n                            minWidth: level === 1 ? 30 : 18,\n                            color: isSelected ? iconSelectedColor : textColor,\n                            ...(!drawerOpen &&\n                                level === 1 && {\n                                    borderRadius: `${borderRadius}px`,\n                                    width: 46,\n                                    height: 46,\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    '&:hover': {\n                                        bgcolor: theme.palette.mode === 'dark' ? theme.palette.secondary.main + 25 : 'secondary.light'\n                                    },\n                                    ...(isSelected && {\n                                        bgcolor: theme.palette.mode === 'dark' ? theme.palette.secondary.main + 25 : 'secondary.light',\n                                        '&:hover': {\n                                            bgcolor: theme.palette.mode === 'dark' ? theme.palette.secondary.main + 30 : 'secondary.light'\n                                        }\n                                    })\n                                })\n                        }}\n                    >\n                        {menuIcon}\n                    </ListItemIcon>\n                )}\n                {(drawerOpen || (!drawerOpen && level !== 1)) && (\n                    <ListItemText\n                        primary={\n                            <Typography variant={isSelected ? 'h5' : 'body1'} color=\"inherit\" sx={{ my: 'auto' }}>\n                                {menu.title}\n                            </Typography>\n                        }\n                    />\n                )}\n\n                {openMini || open ? (\n                    collapseIcon\n                ) : (\n                    <IconChevronDown stroke={1.5} size=\"16px\" style={{ marginTop: 'auto', marginBottom: 'auto' }} />\n                )}\n\n                {!drawerOpen && (\n                    <PopperStyledMini\n                        open={openMini}\n                        anchorEl={anchorEl}\n                        placement=\"right-start\"\n                        style={{\n                            zIndex: 2001\n                        }}\n                        modifiers={[\n                            {\n                                name: 'offset',\n                                options: {\n                                    offset: [-12, 0]\n                                }\n                            }\n                        ]}\n                    >\n                        {({ TransitionProps }) => (\n                            <Transitions in={openMini} {...TransitionProps}>\n                                <Paper\n                                    sx={{\n                                        overflow: 'hidden',\n                                        mt: 1.5,\n                                        boxShadow: theme.shadows[8],\n                                        backgroundImage: 'none'\n                                    }}\n                                >\n                                    <ClickAwayListener onClickAway={handleClosePopper}>\n                                        <Box>{menus}</Box>\n                                    </ClickAwayListener>\n                                </Paper>\n                            </Transitions>\n                        )}\n                    </PopperStyledMini>\n                )}\n            </ListItemButton>\n            {drawerOpen && (\n                <Collapse in={open} timeout=\"auto\" unmountOnExit>\n                    {open && (\n                        <List\n                            component=\"div\"\n                            disablePadding\n                            sx={{\n                                position: 'relative',\n                                '&:after': {\n                                    content: \"''\",\n                                    position: 'absolute',\n                                    left: '32px',\n                                    top: 0,\n                                    height: '100%',\n                                    width: '1px',\n                                    opacity: theme.palette.mode === 'dark' ? 0.2 : 1,\n                                    background: theme.palette.mode === 'dark' ? theme.palette.dark.light : theme.palette.primary.light\n                                }\n                            }}\n                        >\n                            {menus}\n                        </List>\n                    )}\n                </Collapse>\n            )}\n        </>\n    );\n};\n\nexport default NavCollapse;\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;;AAE9C;AACA,SAASC,MAAM,EAAEC,QAAQ,QAAQ,sBAAsB;AACvD,SACIC,GAAG,EACHC,iBAAiB,EACjBC,QAAQ,EACRC,IAAI,EACJC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,KAAK,EACLC,MAAM,EACNC,UAAU,QACP,eAAe;;AAEtB;AACA,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,WAAW,MAAM,iCAAiC;AACzD,SAASC,cAAc,QAAQ,WAAW;AAC1C,OAAOC,SAAS,MAAM,iBAAiB;AAEvC,SAASC,iBAAiB,QAAQ,qBAAqB;;AAEvD;AACA,SAASC,eAAe,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,eAAe;AAChF,OAAOC,qBAAqB,MAAM,uCAAuC;;AAEzE;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,gBAAgB,GAAGzB,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC;EAAEgB;AAAM,CAAC,MAAM;EACpDC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,GAAG;EACb,UAAU,EAAE;IACRC,OAAO,EAAE,IAAI;IACbC,eAAe,EAAEL,KAAK,CAACM,OAAO,CAACC,UAAU,CAACC,KAAK;IAC/CC,SAAS,EAAE,gCAAgC;IAC3CP,MAAM,EAAE,GAAG;IACXQ,UAAU,EAAE,aAAaV,KAAK,CAACM,OAAO,CAACK,OAAO,EAAE;IAChDC,YAAY,EAAE,aAAaZ,KAAK,CAACM,OAAO,CAACK,OAAO;EACpD;AACJ,CAAC,CAAC,CAAC;;AAEH;AAAAE,EAAA,GAdMd,gBAAgB;AA2BtB,MAAMe,WAAW,GAAGA,CAAC;EAAEC,IAAI;EAAEC,KAAK;EAAEC;AAA2B,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,cAAA;EACjE,MAAMnB,KAAK,GAAGzB,QAAQ,CAAC,CAAC;EAExB,MAAM;IAAE6C;EAAa,CAAC,GAAG/B,SAAS,CAAC,CAAC;EACpC,MAAM;IAAEgC;EAAW,CAAC,GAAGjC,cAAc,CAAEkC,KAAK,IAAKA,KAAK,CAACP,IAAI,CAAC;EAE5D,MAAM,CAACQ,IAAI,EAAEC,OAAO,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACqD,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAA4B,IAAI,CAAC;EACzE,MAAM,CAACuD,QAAQ,EAAEC,WAAW,CAAC,GAAGxD,QAAQ,CAA6D,IAAI,CAAC;EAE1G,MAAMyD,eAAe,GAAIC,KAAqG,IAAK;IAC/HF,WAAW,CAAC,IAAI,CAAC;IACjB,IAAIP,UAAU,EAAE;MACZG,OAAO,CAAC,CAACD,IAAI,CAAC;MACdG,WAAW,CAAC,CAACD,QAAQ,GAAGV,IAAI,CAACgB,EAAE,GAAG,IAAI,CAAC;IAC3C,CAAC,MAAM;MACHH,WAAW,CAACE,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,aAAa,CAAC;IACrC;EACJ,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC5BT,OAAO,CAAC,KAAK,CAAC;IACdE,WAAW,CAAC,IAAI,CAAC;IACjBE,WAAW,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMM,QAAQ,GAAGC,OAAO,CAACR,QAAQ,CAAC;EAClC,MAAM;IAAES;EAAS,CAAC,GAAG/D,WAAW,CAAC,CAAC;EAElC,MAAMgE,kBAAkB,GAAGA,CAACC,KAAoB,EAAEP,EAAU,KAAK;IAC7DO,KAAK,CAACC,OAAO,CAAEC,IAAiB,IAAK;MACjC,IAAIA,IAAI,CAACC,GAAG,KAAKL,QAAQ,EAAE;QACvBZ,OAAO,CAAC,IAAI,CAAC;QACbE,WAAW,CAACK,EAAE,CAAC;MACnB;IACJ,CAAC,CAAC;EACN,CAAC;;EAED;EACA5D,SAAS,CAAC,MAAM;IACZqD,OAAO,CAAC,KAAK,CAAC;IACdE,WAAW,CAAC,IAAI,CAAC;IACjB,IAAIQ,QAAQ,EAAEN,WAAW,CAAC,IAAI,CAAC;IAC/B,IAAIb,IAAI,CAAC2B,QAAQ,EAAE;MACf3B,IAAI,CAAC2B,QAAQ,CAACH,OAAO,CAAEC,IAAiB,IAAK;QAAA,IAAAG,cAAA;QACzC,KAAAA,cAAA,GAAIH,IAAI,CAACE,QAAQ,cAAAC,cAAA,eAAbA,cAAA,CAAeC,MAAM,EAAE;UACvBP,kBAAkB,CAACG,IAAI,CAACE,QAAQ,EAAE3B,IAAI,CAACgB,EAAG,CAAC;QAC/C;QACA,IAAIS,IAAI,CAACC,GAAG,KAAKL,QAAQ,EAAE;UACvBV,WAAW,CAACX,IAAI,CAACgB,EAAE,CAAC;UACpBP,OAAO,CAAC,IAAI,CAAC;QACjB;MACJ,CAAC,CAAC;IACN;;IAEA;EACJ,CAAC,EAAE,CAACY,QAAQ,EAAErB,IAAI,CAAC2B,QAAQ,CAAC,CAAC;;EAE7B;EACA,MAAMG,KAAK,IAAA1B,cAAA,GAAGJ,IAAI,CAAC2B,QAAQ,cAAAvB,cAAA,uBAAbA,cAAA,CAAe2B,GAAG,CAAEN,IAAI,IAAK;IACvC,MAAM;MAAEO;IAAiB,CAAC,GAAGzD,iBAAiB,CAACkD,IAAI,CAACQ,MAAM,CAAC;IAC3D,QAAQR,IAAI,CAACS,IAAI;MACb,KAAK,UAAU;QACX,OACI,CAACF,gBAAgB,IAAI,CAACP,IAAI,CAACQ,MAAM,kBAAKpD,OAAA,CAACkB,WAAW;UAAeC,IAAI,EAAEyB,IAAK;UAACxB,KAAK,EAAEA,KAAK,GAAG,CAAE;UAACC,QAAQ,EAAEA;QAAS,GAA1DuB,IAAI,CAACT,EAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAqD,CAAC;MAE7H,KAAK,MAAM;QACP,OAAO,CAACN,gBAAgB,IAAI,CAACP,IAAI,CAACQ,MAAM,kBAAKpD,OAAA,CAACV,OAAO;UAAesD,IAAI,EAAEA,IAAK;UAACxB,KAAK,EAAEA,KAAK,GAAG,CAAE;UAACC,QAAQ,EAAEA;QAAS,GAA1DuB,IAAI,CAACT,EAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAqD,CAAC;MAC5H;QACI,oBACIzD,OAAA,CAACX,UAAU;UAAeqE,OAAO,EAAC,IAAI;UAACC,KAAK,EAAC,OAAO;UAACC,KAAK,EAAC,QAAQ;UAAAd,QAAA,EAAC;QAEpE,GAFiBF,IAAI,CAACT,EAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEZ,CAAC;IAEzB;EACJ,CAAC,CAAC;EAEF,MAAMI,UAAU,GAAGhC,QAAQ,KAAKV,IAAI,CAACgB,EAAE;EAEvC,MAAM2B,IAAI,GAAG3C,IAAI,CAAC4C,IAAK;EACvB,MAAMC,QAAQ,GAAG7C,IAAI,CAAC4C,IAAI,gBACtB/D,OAAA,CAAC8D,IAAI;IACDG,WAAW,EAAE,GAAI;IACjBC,IAAI,EAAEzC,UAAU,GAAG,MAAM,GAAG,MAAO;IACnC0C,KAAK,EAAE;MAAER,KAAK,EAAEE,UAAU,GAAGzD,KAAK,CAACM,OAAO,CAAC0D,SAAS,CAACC,IAAI,GAAGjE,KAAK,CAACM,OAAO,CAAC4D,IAAI,CAACC;IAAQ;EAAE;IAAAjB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5F,CAAC,gBAEFzD,OAAA,CAACF,qBAAqB;IAClB0E,EAAE,EAAE;MACAb,KAAK,EAAEE,UAAU,GAAGzD,KAAK,CAACM,OAAO,CAAC0D,SAAS,CAACC,IAAI,GAAGjE,KAAK,CAACM,OAAO,CAAC4D,IAAI,CAACC,OAAO;MAC7EE,KAAK,EAAEZ,UAAU,GAAG,CAAC,GAAG,CAAC;MACzBa,MAAM,EAAEb,UAAU,GAAG,CAAC,GAAG;IAC7B,CAAE;IACFc,QAAQ,EAAEvD,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG;EAAS;IAAAkC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9C,CACJ;EAED,MAAMmB,YAAY,GAAGnD,UAAU,gBAC3BzB,OAAA,CAACH,aAAa;IAACgF,MAAM,EAAE,GAAI;IAACX,IAAI,EAAC,MAAM;IAACC,KAAK,EAAE;MAAEW,SAAS,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAO;EAAE;IAAAzB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,gBAE9FzD,OAAA,CAACJ,gBAAgB;IAACiF,MAAM,EAAE,GAAI;IAACX,IAAI,EAAC,MAAM;IAACC,KAAK,EAAE;MAAEW,SAAS,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAO;EAAE;IAAAzB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CACnG;EAED,MAAMuB,SAAS,GAAG5E,KAAK,CAACM,OAAO,CAACuE,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG,cAAc;EAC7E,MAAMC,iBAAiB,GAAG9E,KAAK,CAACM,OAAO,CAACuE,IAAI,KAAK,MAAM,IAAIxD,UAAU,GAAG,cAAc,GAAG,gBAAgB;EAEzG,oBACIzB,OAAA,CAAAE,SAAA;IAAA4C,QAAA,gBACI9C,OAAA,CAAChB,cAAc;MACXwF,EAAE,EAAE;QACAlE,MAAM,EAAE,IAAI;QACZkB,YAAY,EAAE,GAAGA,YAAY,IAAI;QACjC2D,EAAE,EAAE,GAAG;QACPC,EAAE,EAAE3D,UAAU,GAAG,GAAGL,KAAK,GAAG,EAAE,IAAI,GAAG,IAAI;QACzC,IAAIK,UAAU,IACVL,KAAK,KAAK,CAAC,IACXhB,KAAK,CAACM,OAAO,CAACuE,IAAI,KAAK,MAAM,IAAI;UAC7B,SAAS,EAAE;YACPtE,UAAU,EAAEP,KAAK,CAACM,OAAO,CAAC0D,SAAS,CAACiB;UACxC,CAAC;UACD,gBAAgB,EAAE;YACd1E,UAAU,EAAEP,KAAK,CAACM,OAAO,CAAC0D,SAAS,CAACiB,KAAK;YACzC1B,KAAK,EAAEuB,iBAAiB;YACxB,SAAS,EAAE;cACPvB,KAAK,EAAEuB,iBAAiB;cACxBvE,UAAU,EAAEP,KAAK,CAACM,OAAO,CAAC0D,SAAS,CAACiB;YACxC;UACJ;QACJ,CAAC,CAAC;QACN,IAAI,CAAC,CAAC5D,UAAU,IAAIL,KAAK,KAAK,CAAC,KAAK;UAChCkE,EAAE,EAAElE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;UACvB,SAAS,EAAE;YACPmE,OAAO,EAAE;UACb,CAAC;UACD,gBAAgB,EAAE;YACd,SAAS,EAAE;cACPA,OAAO,EAAE;YACb,CAAC;YACDA,OAAO,EAAE;UACb;QACJ,CAAC;MACL,CAAE;MACF1D,QAAQ,EAAEgC,UAAW;MAAA,IAChB,CAACpC,UAAU,IAAI;QAAE+D,YAAY,EAAEvD,eAAe;QAAEwD,YAAY,EAAEpD;MAAkB,CAAC;MACtFqD,OAAO,EAAEzD,eAAgB;MAAAa,QAAA,GAExBkB,QAAQ,iBACLhE,OAAA,CAACf,YAAY;QACTuF,EAAE,EAAE;UACAjE,QAAQ,EAAEa,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE;UAC/BuC,KAAK,EAAEE,UAAU,GAAGqB,iBAAiB,GAAGF,SAAS;UACjD,IAAI,CAACvD,UAAU,IACXL,KAAK,KAAK,CAAC,IAAI;YACXI,YAAY,EAAE,GAAGA,YAAY,IAAI;YACjCiD,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACViB,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxB,SAAS,EAAE;cACPL,OAAO,EAAEnF,KAAK,CAACM,OAAO,CAACuE,IAAI,KAAK,MAAM,GAAG7E,KAAK,CAACM,OAAO,CAAC0D,SAAS,CAACC,IAAI,GAAG,EAAE,GAAG;YACjF,CAAC;YACD,IAAIR,UAAU,IAAI;cACd0B,OAAO,EAAEnF,KAAK,CAACM,OAAO,CAACuE,IAAI,KAAK,MAAM,GAAG7E,KAAK,CAACM,OAAO,CAAC0D,SAAS,CAACC,IAAI,GAAG,EAAE,GAAG,iBAAiB;cAC9F,SAAS,EAAE;gBACPkB,OAAO,EAAEnF,KAAK,CAACM,OAAO,CAACuE,IAAI,KAAK,MAAM,GAAG7E,KAAK,CAACM,OAAO,CAAC0D,SAAS,CAACC,IAAI,GAAG,EAAE,GAAG;cACjF;YACJ,CAAC;UACL,CAAC;QACT,CAAE;QAAAvB,QAAA,EAEDkB;MAAQ;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACjB,EACA,CAAChC,UAAU,IAAK,CAACA,UAAU,IAAIL,KAAK,KAAK,CAAE,kBACxCpB,OAAA,CAACd,YAAY;QACTqF,OAAO,eACHvE,OAAA,CAACX,UAAU;UAACqE,OAAO,EAAEG,UAAU,GAAG,IAAI,GAAG,OAAQ;UAACF,KAAK,EAAC,SAAS;UAACa,EAAE,EAAE;YAAEqB,EAAE,EAAE;UAAO,CAAE;UAAA/C,QAAA,EAChF3B,IAAI,CAAC2E;QAAK;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACf;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACJ,EAEAnB,QAAQ,IAAIX,IAAI,GACbiD,YAAY,gBAEZ5E,OAAA,CAACL,eAAe;QAACkF,MAAM,EAAE,GAAI;QAACX,IAAI,EAAC,MAAM;QAACC,KAAK,EAAE;UAAEW,SAAS,EAAE,MAAM;UAAEC,YAAY,EAAE;QAAO;MAAE;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAClG,EAEA,CAAChC,UAAU,iBACRzB,OAAA,CAACG,gBAAgB;QACbwB,IAAI,EAAEW,QAAS;QACfP,QAAQ,EAAEA,QAAS;QACnBgE,SAAS,EAAC,aAAa;QACvB5B,KAAK,EAAE;UACH7D,MAAM,EAAE;QACZ,CAAE;QACF0F,SAAS,EAAE,CACP;UACIC,IAAI,EAAE,QAAQ;UACdC,OAAO,EAAE;YACLC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;UACnB;QACJ,CAAC,CACH;QAAArD,QAAA,EAEDA,CAAC;UAAEsD;QAAgB,CAAC,kBACjBpG,OAAA,CAACT,WAAW;UAAC8G,EAAE,EAAE/D,QAAS;UAAA,GAAK8D,eAAe;UAAAtD,QAAA,eAC1C9C,OAAA,CAACb,KAAK;YACFqF,EAAE,EAAE;cACAnE,QAAQ,EAAE,QAAQ;cAClBiG,EAAE,EAAE,GAAG;cACPC,SAAS,EAAEnG,KAAK,CAACoG,OAAO,CAAC,CAAC,CAAC;cAC3BC,eAAe,EAAE;YACrB,CAAE;YAAA3D,QAAA,eAEF9C,OAAA,CAACnB,iBAAiB;cAAC6H,WAAW,EAAErE,iBAAkB;cAAAS,QAAA,eAC9C9C,OAAA,CAACpB,GAAG;gBAAAkE,QAAA,EAAEG;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAChB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CACrB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAAC,EAChBhC,UAAU,iBACPzB,OAAA,CAAClB,QAAQ;MAACuH,EAAE,EAAE1E,IAAK;MAACgF,OAAO,EAAC,MAAM;MAACC,aAAa;MAAA9D,QAAA,EAC3CnB,IAAI,iBACD3B,OAAA,CAACjB,IAAI;QACD8H,SAAS,EAAC,KAAK;QACfC,cAAc;QACdtC,EAAE,EAAE;UACAuC,QAAQ,EAAE,UAAU;UACpB,SAAS,EAAE;YACPvG,OAAO,EAAE,IAAI;YACbuG,QAAQ,EAAE,UAAU;YACpBC,IAAI,EAAE,MAAM;YACZC,GAAG,EAAE,CAAC;YACNvC,MAAM,EAAE,MAAM;YACdD,KAAK,EAAE,KAAK;YACZyC,OAAO,EAAE9G,KAAK,CAACM,OAAO,CAACuE,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,CAAC;YAChDtE,UAAU,EAAEP,KAAK,CAACM,OAAO,CAACuE,IAAI,KAAK,MAAM,GAAG7E,KAAK,CAACM,OAAO,CAACyG,IAAI,CAAC9B,KAAK,GAAGjF,KAAK,CAACM,OAAO,CAAC6D,OAAO,CAACc;UACjG;QACJ,CAAE;QAAAvC,QAAA,EAEDG;MAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CACb;EAAA,eACH,CAAC;AAEX,CAAC;AAACnC,EAAA,CA5PIJ,WAAW;EAAA,QACCvC,QAAQ,EAEGc,SAAS,EACXD,cAAc,EAuBhBf,WAAW;AAAA;AAAA2I,GAAA,GA3B9BlG,WAAW;AA8PjB,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAAmG,GAAA;AAAAC,YAAA,CAAApG,EAAA;AAAAoG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}