{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nimport { END_DRAG } from './types.js';\nexport function createEndDrag(manager) {\n  return function endDrag() {\n    const monitor = manager.getMonitor();\n    const registry = manager.getRegistry();\n    verifyIsDragging(monitor);\n    const sourceId = monitor.getSourceId();\n    if (sourceId != null) {\n      const source = registry.getSource(sourceId, true);\n      source.endDrag(monitor, sourceId);\n      registry.unpinSource();\n    }\n    return {\n      type: END_DRAG\n    };\n  };\n}\nfunction verifyIsDragging(monitor) {\n  invariant(monitor.isDragging(), 'Cannot call endDrag while not dragging.');\n}\n\n//# sourceMappingURL=endDrag.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}