{"ast": null, "code": "import * as React from 'react';\nimport { useUtils } from './useUtils';\nimport { getMeridiem, convertToMeridiem } from '../utils/time-utils';\nexport function useNextMonthDisabled(month, {\n  disableFuture,\n  maxDate\n}) {\n  const utils = useUtils();\n  return React.useMemo(() => {\n    const now = utils.date();\n    const lastEnabledMonth = utils.startOfMonth(disableFuture && utils.isBefore(now, maxDate) ? now : maxDate);\n    return !utils.isAfter(lastEnabledMonth, month);\n  }, [disableFuture, maxDate, month, utils]);\n}\nexport function usePreviousMonthDisabled(month, {\n  disablePast,\n  minDate\n}) {\n  const utils = useUtils();\n  return React.useMemo(() => {\n    const now = utils.date();\n    const firstEnabledMonth = utils.startOfMonth(disablePast && utils.isAfter(now, minDate) ? now : minDate);\n    return !utils.isBefore(firstEnabledMonth, month);\n  }, [disablePast, minDate, month, utils]);\n}\nexport function useMeridiemMode(date, ampm, onChange) {\n  const utils = useUtils();\n  const meridiemMode = getMeridiem(date, utils);\n  const handleMeridiemChange = React.useCallback(mode => {\n    const timeWithMeridiem = date == null ? null : convertToMeridiem(date, mode, Boolean(ampm), utils);\n    onChange(timeWithMeridiem, 'partial');\n  }, [ampm, date, onChange, utils]);\n  return {\n    meridiemMode,\n    handleMeridiemChange\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}