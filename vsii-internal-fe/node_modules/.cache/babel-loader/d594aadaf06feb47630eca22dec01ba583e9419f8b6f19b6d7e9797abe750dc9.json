{"ast": null, "code": "\"use strict\";\n\"use client\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _createSvgIcon = _interopRequireDefault(require(\"./utils/createSvgIcon\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nvar _default = exports.default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)(\"path\", {\n  d: \"M12 3 2 12h3v8h6v-6h2v6h6v-8h3zm5 15h-2v-6H9v6H7v-7.81l5-4.5 5 4.5z\"\n}, \"0\"), /*#__PURE__*/(0, _jsxRuntime.jsx)(\"path\", {\n  d: \"M7 10.19V18h2v-6h6v6h2v-7.81l-5-4.5z\",\n  opacity: \".3\"\n}, \"1\")], 'HomeTwoTone');", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}