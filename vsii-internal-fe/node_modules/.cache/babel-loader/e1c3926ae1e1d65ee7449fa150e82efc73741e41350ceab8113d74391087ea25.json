{"ast": null, "code": "import{FormattedMessage}from'react-intl';// material-ui\nimport{TableCell,TableHead,TableRow}from'@mui/material';// project imports\nimport{checkAllowedPermission}from'utils/authorization';import{PERMISSIONS}from'constants/Permission';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SpecialHoursThead=()=>{const{specialHoursPermission}=PERMISSIONS.admin;const{manage_special_hours}=TEXT_CONFIG_SCREEN.administration;return/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_special_hours+'no'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_special_hours+'members'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_special_hours+'from-date'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_special_hours+'to-date'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_special_hours+'special-hours-type'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_special_hours+'hours-day'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_special_hours+'note'})}),checkAllowedPermission(specialHoursPermission.edit)&&/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_special_hours+'action'})})]})});};export default SpecialHoursThead;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}