{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"getRootProps\"];\nimport { useTabContext, getTabId, getPanelId } from '../TabsUnstyled';\nimport { useButton } from '../ButtonUnstyled';\nconst useTab = parameters => {\n  var _getPanelId, _getTabId;\n  const {\n    value: valueProp,\n    onChange,\n    onClick,\n    onFocus\n  } = parameters;\n  const _useButton = useButton(parameters),\n    {\n      getRootProps: getRootPropsButton\n    } = _useButton,\n    otherButtonProps = _objectWithoutPropertiesLoose(_useButton, _excluded);\n  const context = useTabContext();\n  if (context === null) {\n    throw new Error('No TabContext provided');\n  }\n  const value = valueProp != null ? valueProp : 0;\n  const selected = context.value === value;\n  const selectionFollowsFocus = context.selectionFollowsFocus;\n  const a11yAttributes = {\n    role: 'tab',\n    'aria-controls': (_getPanelId = getPanelId(context, value)) != null ? _getPanelId : undefined,\n    id: (_getTabId = getTabId(context, value)) != null ? _getTabId : undefined,\n    'aria-selected': selected,\n    disabled: otherButtonProps.disabled\n  };\n  const createHandleFocus = otherHandlers => event => {\n    var _otherHandlers$onFocu;\n    (_otherHandlers$onFocu = otherHandlers.onFocus) == null ? void 0 : _otherHandlers$onFocu.call(otherHandlers, event);\n    if (event.defaultPrevented) {\n      return;\n    }\n    if (selectionFollowsFocus && !selected) {\n      if (onChange) {\n        onChange(event, value);\n      }\n      context.onSelected(event, value);\n    }\n    if (onFocus) {\n      onFocus(event);\n    }\n  };\n  const createHandleClick = otherHandlers => event => {\n    var _otherHandlers$onClic;\n    (_otherHandlers$onClic = otherHandlers.onClick) == null ? void 0 : _otherHandlers$onClic.call(otherHandlers, event);\n    if (event.defaultPrevented) {\n      return;\n    }\n    if (!selected) {\n      if (onChange) {\n        onChange(event, value);\n      }\n      context.onSelected(event, value);\n    }\n    if (onClick) {\n      onClick(event);\n    }\n  };\n  const getRootProps = (otherHandlers = {}) => {\n    const buttonResolvedProps = getRootPropsButton(_extends({}, otherHandlers, {\n      onClick: createHandleClick(otherHandlers),\n      onFocus: createHandleFocus(otherHandlers)\n    }));\n    return _extends({}, buttonResolvedProps, a11yAttributes);\n  };\n  return _extends({\n    getRootProps\n  }, otherButtonProps, {\n    selected\n  });\n};\nexport default useTab;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}