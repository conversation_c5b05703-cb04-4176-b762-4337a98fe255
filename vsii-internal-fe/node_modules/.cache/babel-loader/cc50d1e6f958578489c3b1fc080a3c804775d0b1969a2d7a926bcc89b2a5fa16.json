{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/utils/common.tsx\";\nimport { Fragment } from 'react';\nimport { saveAs } from 'file-saver';\n\n// project imports\n\nimport { STATUS } from 'constants/Common';\nimport { getCookieByKey } from './cookies';\nimport { reportUrl } from 'index';\nimport { SEARCH_TIMESTAMP } from 'constants/Config';\n\n/**\n * format price\n * @param price\n * @return format price\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const formatPrice = price => {\n  return new Intl.NumberFormat('en-US').format(+price);\n};\n\n/**\n * format price\n * @param price\n * @return custom format price\n */\nexport const customFormatPrice = price => {\n  const parts = price.toString().split('.');\n  parts[0] = parts[0].replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n  return parts.join('.');\n};\n\n/**\n * transformRequestOptions\n * @param params\n * @returns\n */\nexport const transformRequestOptions = params => {\n  let options = '';\n  Object.entries(params).forEach(([key, value]) => {\n    if (typeof value !== 'object' && value) {\n      options += `${key}=${encodeURIComponent(value)}&`;\n    } else if (Array.isArray(value)) {\n      value.forEach(el => {\n        options += `${key}=${encodeURIComponent(el)}&`;\n      });\n    }\n  });\n  return options ? options.slice(0, -1) : options;\n};\n\n/**\n * export document\n * @param url\n * @param query\n */\nexport const exportDocument = (url, query) => {\n  const accessToken = getCookieByKey('accessToken');\n  // const urlDownload = `${baseUrl + '/' + url}?${qs.stringify({ ...query, token: accessToken })}`;\n  const urlDownload = `${reportUrl + '/' + url}?${transformRequestOptions({\n    ...query,\n    token: accessToken\n  })}`;\n  const win = window.open(urlDownload, '_blank');\n  win.focus();\n};\nexport const downloadDocument = (fileName, data) => {\n  const blob = new Blob([data]);\n  saveAs(blob, fileName);\n};\n\n/**\n * format tableCell project\n * @param projects\n * @param character\n * @return\n */\nexport const formatTableCellMemberInProject = (projects, character) => {\n  let splitCharacter = character ? character : ';';\n  if (!projects) return /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 27\n  }, this);\n  const formatProjects = projects.split(splitCharacter);\n  if (formatProjects.length > 1) {\n    return formatProjects.map((item, key) => /*#__PURE__*/_jsxDEV(Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: item.trim()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 17\n      }, this), item.trim() && /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 33\n      }, this)]\n    }, key, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 13\n    }, this));\n  }\n  return projects.includes(splitCharacter) ? projects.slice(0, -1) : projects;\n};\n/**\n * format tableCell project\n * @param {project: string[]}\n * @return {ReactNode} format tablecell\n */\nexport const formatTableCellProject = projects => {\n  return projects.map((project, key) => /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      children: project\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 13\n    }, this)]\n  }, key, true, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 9\n  }, this));\n};\n\n/**\n * convert status\n * @param status\n * @return status user\n */\nexport const convertStatus = status => {\n  return !!status ? STATUS.filter(el => el.value === status)[0].label : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false);\n};\n\n/**\n * isEmpty return true when value: undefined, null, \"\", [], {}\n * @param obj\n * @return {boolean} boolean\n */\nexport const isEmpty = value => {\n  return (\n    // null or undefined\n    value == null ||\n    // has length and it's zero\n    value.hasOwnProperty('length') && value.length === 0 ||\n    // is an Object and has no keys\n    value.constructor === Object && Object.keys(value).length === 0\n  );\n};\n\n/**\n * Validate file format\n * @param file\n * @return {boolean} boolean\n */\nexport const validateFileFormat = file => {\n  const validExtensions = ['xlsx', 'xls'];\n  const fileExtension = file.name.split('.')[1];\n  return validExtensions.includes(fileExtension);\n};\n\n/**\n * isNotNumber\n */\nexport const isNotNumber = string => {\n  if (string) {\n    if ((string === null || string === void 0 ? void 0 : string.length) === 0) {\n      // Nếu chuỗi rỗng, coi là chuỗi\n      return true;\n    } else if (string[0] === '0') {\n      // Nếu có số 0 ở đầu chuỗi, coi là chuỗi\n      if ((string === null || string === void 0 ? void 0 : string.length) === 1) {\n        return false;\n      } else {\n        return true;\n      }\n    } else {\n      if (!isNaN(string)) {\n        // Kiểm tra xem có thể chuyển đổi thành số hay không\n        return false;\n      } else {\n        return true;\n      }\n    }\n  }\n};\n\n/**\n * get PARAMS URL\n * @param {string[]} keyParams\n * @param {URLSearchParams} searchParams\n * @return {object} object urlParams\n */\nexport const getSearchParam = (keyParams, searchParams, keyParamsArray) => {\n  let urlParams = {};\n  for (const key of keyParams) {\n    !isNotNumber(searchParams.get(key)) ? urlParams[key] = searchParams.get(key) ? +searchParams.get(key) : null : urlParams[key] = searchParams.get(key);\n  }\n  if (keyParamsArray) {\n    for (const key of keyParamsArray) {\n      let isArrayNumber = !isNaN(searchParams.getAll(key)[0]);\n      urlParams[key] = isArrayNumber ? convertArrayStringToNumber(searchParams.getAll(key)) : searchParams.getAll(key);\n    }\n  }\n  return urlParams;\n};\n\n/**\n * Check invalid value in object\n * delete object key have value: null, undefined, '', {}, []\n * @param {object} value\n */\nexport const transformObject = (obj, ignoreKeys) => {\n  Object.keys(obj).forEach(key => {\n    if (isEmpty(obj[key]) || ignoreKeys !== null && ignoreKeys !== void 0 && ignoreKeys.includes(key)) {\n      delete obj[key];\n    }\n  });\n  return obj;\n};\n\n/**\n * removeExtraSpace\n * @param str\n * @returns\n */\nexport const removeExtraSpace = str => {\n  var _str$toString;\n  const stringFormat = !Array.isArray(str) ? str === null || str === void 0 ? void 0 : (_str$toString = str.toString()) === null || _str$toString === void 0 ? void 0 : _str$toString.trim().replace(/\\t/g, '').split(/ +/).join(' ') : str;\n  return stringFormat;\n};\n\n/**\n * check error tab\n * @param errors\n * @param fields\n * @returns\n */\nexport const getTabValueByFieldError = (errors, fields) => {\n  let tabValue = 0;\n  for (const error of fields) {\n    if (error.fields.some(field => errors[field])) {\n      tabValue = error.tabValue;\n      break;\n    }\n  }\n  return tabValue;\n};\n\n/**\n * convert array string to array number\n * @param arr\n * @returns\n */\nexport function convertArrayStringToNumber(arr) {\n  return arr === null || arr === void 0 ? void 0 : arr.map(str => {\n    return Number(str);\n  });\n}\n\n/**\n * calculation sum\n * @param numbers\n * @returns\n */\nexport function calculationSum(...numbers) {\n  let sum = 0;\n  for (let i = 0; i < numbers.length; i++) {\n    sum += numbers[i];\n  }\n  return sum;\n}\n\n// Calculate Monthly production performance\n\nexport const getDatesFromValue = (value, months) => {\n  const selectedMonth = months.find(month => month.value === value);\n  if (selectedMonth) {\n    const fromDate = selectedMonth.label.substring(selectedMonth.label.indexOf('(') + 1, selectedMonth.label.indexOf('~')).trim();\n    const toDate = selectedMonth.label.substring(selectedMonth.label.indexOf('~') + 1, selectedMonth.label.indexOf(')')).trim();\n    return {\n      fromDate,\n      toDate\n    };\n  }\n  return {\n    fromDate: '',\n    toDate: ''\n  };\n};\n\n// Calculate receivable (TM)\nexport const calculateReceivable = (month, paymentTerm) => {\n  const receivable = month - paymentTerm;\n  return receivable;\n};\n\n// Calculate amount (TM)\nexport const calculateAmount = (arr, standardWorkingDay) => {\n  for (let i = 0; i < arr.length; i++) {\n    const {\n      rateUSD,\n      quantity\n    } = arr[i];\n    if (quantity && rateUSD !== null) {\n      arr[i].amount = +rateUSD * +quantity * standardWorkingDay;\n    }\n  }\n};\n\n// Total amount (TM)\nexport const calculateTotalAmount = arr => {\n  let totalAmount = 0;\n  for (let i = 0; i < (arr === null || arr === void 0 ? void 0 : arr.length); i++) {\n    if (arr[i].amount !== null) {\n      totalAmount += parseInt(arr[i].amount);\n    }\n  }\n  return totalAmount;\n};\n\n// Calculate Contract size (TM)\nexport const calculateContractSize = (original, exchange) => {\n  const contractSize = original / exchange;\n  return contractSize;\n};\n\n// Calculate rate USD (TM)\nexport const calculateRateUsd = (exchange, rate) => {\n  // extra / usdmd * 23\n  const rateUsd = rate / exchange;\n  return rateUsd;\n};\n\n// Calculate delivered (Fix cost)\nexport const calculateDeliveredFixCost = (contractSize, contractAllocationByMonth) => {\n  const delivered = contractSize / contractAllocationByMonth;\n  return delivered;\n};\n\n//Total Delivered\nexport const calculateTotalDelivered = data => {\n  let totalDelivered = 0;\n  if (data) {\n    for (let i = 0; i < data.length; i++) {\n      totalDelivered += data[i].delivered.value;\n    }\n    return totalDelivered;\n  }\n};\n// Delivered month 4 last year\nexport const deliveredMonthLastYear = (data, month) => {\n  if (data) {\n    for (const item of data) {\n      if (item.month === month) {\n        return item.delivered.value;\n      }\n    }\n  } else {\n    return 0;\n  }\n};\nexport const getWorkingDaysByMonth = (months, monthNumber) => {\n  const month = months === null || months === void 0 ? void 0 : months.find(m => m.month === monthNumber);\n  if (month) {\n    return month.workingDays;\n  }\n  return 0;\n};\n\n// Backgroud color\nexport const getBackgroundColor = item => {\n  return item === 'sat' || item === 'sun' ? '#B1B1B1' : '';\n};\n\n/**\n * Get base64\n * @param text\n * @returns\n */\nexport const getBase64fromReaderResult = text => {\n  return text.replace('data:', '').replace(/^.+,/, '');\n};\n\n/**\n * Ponvert file to base64\n * @param file\n * @returns\n */\nexport const convertFileToBase64 = file => {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.readAsDataURL(file);\n    reader.onload = () => {\n      var _reader$result;\n      return resolve(((_reader$result = reader.result) === null || _reader$result === void 0 ? void 0 : _reader$result.toString()) || '');\n    };\n    reader.onerror = error => reject(error);\n  });\n};\n\n/**\n * show pdf in new tab\n * @param base64Data\n */\nexport function showPdfInNewTab(base64Data) {\n  const nav = window.navigator;\n  if (nav && nav.msSaveOrOpenBlob) {\n    var byteCharacters = atob(base64Data);\n    var byteNumbers = new Array(byteCharacters.length);\n    for (var i = 0; i < byteCharacters.length; i++) {\n      byteNumbers[i] = byteCharacters.charCodeAt(i);\n    }\n    var byteArray = new Uint8Array(byteNumbers);\n    var blob = new Blob([byteArray], {\n      type: 'application/pdf'\n    });\n    nav.msSaveOrOpenBlob(blob, 'myreport.pdf');\n  } else {\n    var pdfWindow = window.open('', '_blank');\n    const iframeTag = \"<iframe width='100%' style='margin: -8px;border: none;' height='100%' src='data:application/pdf;base64, \";\n    const iframeTagClose = \"'></iframe>\";\n    pdfWindow.document.write(iframeTag + encodeURI(base64Data) + iframeTagClose);\n  }\n}\n// get data productivity by month\nexport const getDataProductivityByMonth = (array, month) => {\n  return array && array.length > 0 && array.filter(el => {\n    return el.month === month;\n  });\n};\n\n// get receveiable by month\nexport const getReceivableByMonth = (array, month) => {\n  var _matchingData$deliver;\n  const matchingData = array.find(el => el.month === month);\n  return matchingData ? (_matchingData$deliver = matchingData.delivered) === null || _matchingData$deliver === void 0 ? void 0 : _matchingData$deliver.value : 0;\n};\n\n// CV config check IsEdit skill\n//edit\nexport const checkAndAddIsEdit = (skillList1, skillList2) => {\n  const newdata = [];\n  const skillList2Set = new Set(skillList2.map(item => item.idHexString));\n  for (const item of skillList1) {\n    const isDelete = !skillList2Set.has(item.idHexString);\n    const matchingItem = skillList2.find(item2 => item2.idHexString === item.idHexString);\n    const name = matchingItem ? matchingItem.name : item.name;\n    const new_item = {\n      idHexString: item.idHexString,\n      name,\n      isDelete\n    };\n    newdata.push(new_item);\n  }\n  for (const item of skillList2) {\n    if (!item.idHexString) {\n      newdata.push({\n        ...item,\n        isDelete: false\n      });\n    } else if (!skillList1.some(item1 => item1.idHexString === item.idHexString)) {\n      newdata.push({\n        ...item,\n        isDelete: true\n      });\n    }\n  }\n  return newdata;\n};\n\n//add\nexport const convertValueToNewValue = value => {\n  const newValue = [];\n  for (const item of value) {\n    if (item.idHexString !== undefined) {\n      newValue.push({\n        name: item.name,\n        isDelete: false\n      });\n    } else if (item.name && item.isDelete !== undefined) {\n      newValue.push({\n        name: item.name,\n        isDelete: item.isDelete\n      });\n    }\n  }\n  return newValue;\n};\n\n// compare effort member\nexport const compareStyleEffortMember = (effortVerified, effort) => {\n  return {\n    color: (effortVerified || 0) !== effort ? '#E70A17' : 'inherit'\n  };\n};\n// local storage\nexport const setLocalStorageSearchTime = value => {\n  localStorage.setItem(SEARCH_TIMESTAMP, `?${transformRequestOptions(value)}`);\n};\nexport const converStringToCalculationInputsObject = input => {\n  return input.split(/([+\\-*/])/).reduce((acc, current, index, array) => {\n    if (index === 0) {\n      acc.push({\n        sign: '',\n        code: current\n      });\n    } else if (index % 2 !== 0) {\n      acc.push({\n        sign: current,\n        code: array[index + 1]\n      });\n    }\n    return acc;\n  }, []);\n};\nexport const convertCalculationInputsToString = calculationInputs => {\n  return calculationInputs.map((item, index) => {\n    var _item$code2;\n    if (index === 0) {\n      var _item$code;\n      return (_item$code = item.code) === null || _item$code === void 0 ? void 0 : _item$code.value;\n    }\n    return `${item.sign}${(_item$code2 = item.code) === null || _item$code2 === void 0 ? void 0 : _item$code2.value}`;\n  }).join('');\n};\n\n// Check is holiday\nexport const checkIsHoliday = (date, holidays) => {\n  const dateStr = `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;\n  return holidays.includes(dateStr);\n};", "map": {"version": 3, "names": ["Fragment", "saveAs", "STATUS", "getCookie<PERSON>y<PERSON>ey", "reportUrl", "SEARCH_TIMESTAMP", "jsxDEV", "_jsxDEV", "_Fragment", "formatPrice", "price", "Intl", "NumberFormat", "format", "customFormatPrice", "parts", "toString", "split", "replace", "join", "transformRequestOptions", "params", "options", "Object", "entries", "for<PERSON>ach", "key", "value", "encodeURIComponent", "Array", "isArray", "el", "slice", "exportDocument", "url", "query", "accessToken", "urlDownload", "token", "win", "window", "open", "focus", "downloadDocument", "fileName", "data", "blob", "Blob", "formatTableCellMemberInProject", "projects", "character", "splitCharacter", "_jsxFileName", "lineNumber", "columnNumber", "formatProjects", "length", "map", "item", "children", "trim", "includes", "formatTableCellProject", "project", "convertStatus", "status", "filter", "label", "isEmpty", "hasOwnProperty", "constructor", "keys", "validateFileFormat", "file", "validExtensions", "fileExtension", "name", "isNotNumber", "string", "isNaN", "getSearchParam", "keyParams", "searchParams", "keyParamsArray", "urlParams", "get", "isArrayNumber", "getAll", "convertArrayStringToNumber", "transformObject", "obj", "<PERSON><PERSON><PERSON><PERSON>", "removeExtraSpace", "str", "_str$toString", "stringFormat", "getTabValueByFieldError", "errors", "fields", "tabValue", "error", "some", "field", "arr", "Number", "calculationSum", "numbers", "sum", "i", "getDatesFromValue", "months", "<PERSON><PERSON><PERSON><PERSON>", "find", "month", "fromDate", "substring", "indexOf", "toDate", "calculateReceivable", "paymentTerm", "receivable", "calculateAmount", "standardWorkingDay", "rateUSD", "quantity", "amount", "calculateTotalAmount", "totalAmount", "parseInt", "calculateContractSize", "original", "exchange", "contractSize", "calculateRateUsd", "rate", "rateUsd", "calculateDeliveredFixCost", "contractAllocationByMonth", "delivered", "calculateTotalDelivered", "totalDelivered", "deliveredMonthLastYear", "getWorkingDaysByMonth", "monthNumber", "m", "workingDays", "getBackgroundColor", "getBase64fromReaderResult", "text", "convertFileToBase64", "Promise", "resolve", "reject", "reader", "FileReader", "readAsDataURL", "onload", "_reader$result", "result", "onerror", "showPdfInNewTab", "base64Data", "nav", "navigator", "msSaveOrOpenBlob", "byteCharacters", "atob", "byteNumbers", "charCodeAt", "byteArray", "Uint8Array", "type", "pdfWindow", "iframeTag", "iframeTagClose", "document", "write", "encodeURI", "getDataProductivityByMonth", "array", "getReceivableByMonth", "_matchingData$deliver", "matchingData", "checkAndAddIsEdit", "skillList1", "skillList2", "newdata", "skillList2Set", "Set", "idHexString", "isDelete", "has", "matchingItem", "item2", "new_item", "push", "item1", "convertValueToNewValue", "newValue", "undefined", "compareStyleEffortMember", "effortVerified", "effort", "color", "setLocalStorageSearchTime", "localStorage", "setItem", "converStringToCalculationInputsObject", "input", "reduce", "acc", "current", "index", "sign", "code", "convertCalculationInputsToString", "calculationInputs", "_item$code2", "_item$code", "checkIsHoliday", "date", "holidays", "dateStr", "getDate", "padStart", "getMonth", "getFullYear"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/utils/common.tsx"], "sourcesContent": ["import { Fragment } from 'react';\nimport { FieldErrors } from 'react-hook-form';\nimport { saveAs } from 'file-saver';\n\n// project imports\nimport { IFieldByTabUser, IOption, IProductivity, IProductivityHcInfo, ISkill } from 'types';\nimport { STATUS } from 'constants/Common';\nimport { getCookieByKey } from './cookies';\nimport { reportUrl } from 'index';\nimport { SEARCH_TIMESTAMP } from 'constants/Config';\n\n/**\n * format price\n * @param price\n * @return format price\n */\nexport const formatPrice = (price: any) => {\n    return new Intl.NumberFormat('en-US').format(+price);\n};\n\n/**\n * format price\n * @param price\n * @return custom format price\n */\nexport const customFormatPrice = (price: any) => {\n    const parts = price.toString().split('.');\n    parts[0] = parts[0].replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n    return parts.join('.');\n};\n\n/**\n * transformRequestOptions\n * @param params\n * @returns\n */\nexport const transformRequestOptions = (params: any) => {\n    let options = '';\n    Object.entries<any>(params).forEach(([key, value]) => {\n        if (typeof value !== 'object' && value) {\n            options += `${key}=${encodeURIComponent(value)}&`;\n        } else if (Array.isArray(value)) {\n            value.forEach((el) => {\n                options += `${key}=${encodeURIComponent(el)}&`;\n            });\n        }\n    });\n    return options ? options.slice(0, -1) : options;\n};\n\n/**\n * export document\n * @param url\n * @param query\n */\nexport const exportDocument = (url: any, query?: any) => {\n    const accessToken = getCookieByKey('accessToken');\n    // const urlDownload = `${baseUrl + '/' + url}?${qs.stringify({ ...query, token: accessToken })}`;\n    const urlDownload = `${reportUrl + '/' + url}?${transformRequestOptions({ ...query, token: accessToken })}`;\n    const win = window.open(urlDownload, '_blank')!;\n    win.focus();\n};\n\nexport const downloadDocument = (fileName: string, data: Blob) => {\n    const blob = new Blob([data]);\n    saveAs(blob, fileName);\n};\n\n/**\n * format tableCell project\n * @param projects\n * @param character\n * @return\n */\nexport const formatTableCellMemberInProject = (projects: string | null, character?: string) => {\n    let splitCharacter = character ? character : ';';\n    if (!projects) return <span></span>;\n    const formatProjects = projects.split(splitCharacter);\n\n    if (formatProjects.length > 1) {\n        return formatProjects.map((item: string, key: number) => (\n            <Fragment key={key}>\n                <span>{item.trim()}</span>\n                {item.trim() && <br />}\n            </Fragment>\n        ));\n    }\n\n    return projects.includes(splitCharacter) ? projects.slice(0, -1) : projects;\n};\n/**\n * format tableCell project\n * @param {project: string[]}\n * @return {ReactNode} format tablecell\n */\nexport const formatTableCellProject = (projects: string[]) => {\n    return projects.map((project, key) => (\n        <Fragment key={key}>\n            <span>{project}</span>\n            <br />\n        </Fragment>\n    ));\n};\n\n/**\n * convert status\n * @param status\n * @return status user\n */\nexport const convertStatus = (status: string) => {\n    return !!status ? STATUS.filter((el) => el.value === status)[0].label : <></>;\n};\n\n/**\n * isEmpty return true when value: undefined, null, \"\", [], {}\n * @param obj\n * @return {boolean} boolean\n */\nexport const isEmpty = (value: any): boolean => {\n    return (\n        // null or undefined\n        value == null ||\n        // has length and it's zero\n        (value.hasOwnProperty('length') && value.length === 0) ||\n        // is an Object and has no keys\n        (value.constructor === Object && Object.keys(value).length === 0)\n    );\n};\n\n/**\n * Validate file format\n * @param file\n * @return {boolean} boolean\n */\nexport const validateFileFormat = (file: File): boolean => {\n    const validExtensions = ['xlsx', 'xls'];\n    const fileExtension = file.name.split('.')[1];\n    return validExtensions.includes(fileExtension);\n};\n\n/**\n * isNotNumber\n */\nexport const isNotNumber = (string: any) => {\n    if (string) {\n        if (string?.length === 0) {\n            // Nếu chuỗi rỗng, coi là chuỗi\n            return true;\n        } else if (string[0] === '0') {\n            // Nếu có số 0 ở đầu chuỗi, coi là chuỗi\n            if (string?.length === 1) {\n                return false;\n            } else {\n                return true;\n            }\n        } else {\n            if (!isNaN(string)) {\n                // Kiểm tra xem có thể chuyển đổi thành số hay không\n                return false;\n            } else {\n                return true;\n            }\n        }\n    }\n};\n\n/**\n * get PARAMS URL\n * @param {string[]} keyParams\n * @param {URLSearchParams} searchParams\n * @return {object} object urlParams\n */\nexport const getSearchParam = (keyParams: string[], searchParams: URLSearchParams, keyParamsArray?: string[]): object => {\n    let urlParams: { [key: string]: any } = {};\n\n    for (const key of keyParams) {\n        !isNotNumber(searchParams.get(key)! as any)\n            ? (urlParams[key] = searchParams.get(key) ? +searchParams.get(key)! : null)\n            : (urlParams[key] = searchParams.get(key));\n    }\n    if (keyParamsArray) {\n        for (const key of keyParamsArray) {\n            let isArrayNumber = !isNaN(searchParams.getAll(key)[0] as any);\n            urlParams[key] = isArrayNumber ? convertArrayStringToNumber(searchParams.getAll(key)) : searchParams.getAll(key);\n        }\n    }\n    return urlParams;\n};\n\n/**\n * Check invalid value in object\n * delete object key have value: null, undefined, '', {}, []\n * @param {object} value\n */\nexport const transformObject = (obj: any, ignoreKeys?: string[]) => {\n    Object.keys(obj).forEach((key) => {\n        if (isEmpty(obj[key]) || ignoreKeys?.includes(key)) {\n            delete obj[key];\n        }\n    });\n\n    return obj;\n};\n\n/**\n * removeExtraSpace\n * @param str\n * @returns\n */\nexport const removeExtraSpace = (str: any) => {\n    const stringFormat = !Array.isArray(str) ? str?.toString()?.trim().replace(/\\t/g, '').split(/ +/).join(' ') : str;\n\n    return stringFormat;\n};\n\n/**\n * check error tab\n * @param errors\n * @param fields\n * @returns\n */\nexport const getTabValueByFieldError = (errors: FieldErrors<any>, fields: IFieldByTabUser[]) => {\n    let tabValue = 0;\n    for (const error of fields) {\n        if (error.fields.some((field: string) => errors[field])) {\n            tabValue = error.tabValue;\n            break;\n        }\n    }\n    return tabValue;\n};\n\n/**\n * convert array string to array number\n * @param arr\n * @returns\n */\nexport function convertArrayStringToNumber(arr: string[]) {\n    return arr?.map((str) => {\n        return Number(str);\n    });\n}\n\n/**\n * calculation sum\n * @param numbers\n * @returns\n */\nexport function calculationSum(...numbers: number[]): number {\n    let sum = 0;\n    for (let i = 0; i < numbers.length; i++) {\n        sum += numbers[i];\n    }\n    return sum;\n}\n\n// Calculate Monthly production performance\n\nexport const getDatesFromValue = (value: number, months: IOption[]) => {\n    const selectedMonth = months.find((month: IOption) => month.value === value);\n    if (selectedMonth) {\n        const fromDate = selectedMonth.label.substring(selectedMonth.label.indexOf('(') + 1, selectedMonth.label.indexOf('~')).trim();\n        const toDate = selectedMonth.label.substring(selectedMonth.label.indexOf('~') + 1, selectedMonth.label.indexOf(')')).trim();\n        return { fromDate, toDate };\n    }\n    return { fromDate: '', toDate: '' };\n};\n\n// Calculate receivable (TM)\nexport const calculateReceivable = (month: number, paymentTerm: number) => {\n    const receivable = month - paymentTerm;\n    return receivable;\n};\n\n// Calculate amount (TM)\nexport const calculateAmount = (arr: IProductivityHcInfo[], standardWorkingDay: number) => {\n    for (let i = 0; i < arr.length; i++) {\n        const { rateUSD, quantity } = arr[i];\n        if (quantity && rateUSD !== null) {\n            arr[i].amount = +rateUSD * +quantity * standardWorkingDay;\n        }\n    }\n};\n\n// Total amount (TM)\nexport const calculateTotalAmount = (arr: IProductivityHcInfo[]) => {\n    let totalAmount = 0;\n    for (let i = 0; i < arr?.length; i++) {\n        if (arr[i].amount !== null) {\n            totalAmount += parseInt(arr[i].amount);\n        }\n    }\n    return totalAmount;\n};\n\n// Calculate Contract size (TM)\nexport const calculateContractSize = (original: number, exchange: number) => {\n    const contractSize = original / exchange;\n    return contractSize;\n};\n\n// Calculate rate USD (TM)\nexport const calculateRateUsd = (exchange: number, rate: number) => {\n    // extra / usdmd * 23\n    const rateUsd = rate / exchange;\n    return rateUsd;\n};\n\n// Calculate delivered (Fix cost)\nexport const calculateDeliveredFixCost = (contractSize: number, contractAllocationByMonth: number) => {\n    const delivered = contractSize / contractAllocationByMonth;\n    return delivered;\n};\n\n//Total Delivered\nexport const calculateTotalDelivered = (data: IProductivity[]) => {\n    let totalDelivered = 0;\n    if (data) {\n        for (let i = 0; i < data.length; i++) {\n            totalDelivered += data[i]!.delivered!.value!;\n        }\n        return totalDelivered;\n    }\n};\n// Delivered month 4 last year\nexport const deliveredMonthLastYear = (data: IProductivity[], month: number) => {\n    if (data) {\n        for (const item of data) {\n            if (item.month === month) {\n                return item!.delivered!.value;\n            }\n        }\n    } else {\n        return 0;\n    }\n};\n\nexport const getWorkingDaysByMonth = (months: any, monthNumber: any) => {\n    const month = months?.find((m: any) => m.month === monthNumber);\n    if (month) {\n        return month.workingDays;\n    }\n    return 0;\n};\n\n// Backgroud color\nexport const getBackgroundColor = (item: string) => {\n    return item === 'sat' || item === 'sun' ? '#B1B1B1' : '';\n};\n\n/**\n * Get base64\n * @param text\n * @returns\n */\nexport const getBase64fromReaderResult = (text: string) => {\n    return text.replace('data:', '').replace(/^.+,/, '');\n};\n\n/**\n * Ponvert file to base64\n * @param file\n * @returns\n */\nexport const convertFileToBase64 = (file: File): Promise<string> => {\n    return new Promise<string>((resolve, reject) => {\n        const reader = new FileReader();\n        reader.readAsDataURL(file);\n        reader.onload = () => resolve(reader.result?.toString() || '');\n        reader.onerror = (error) => reject(error);\n    });\n};\n\n/**\n * show pdf in new tab\n * @param base64Data\n */\nexport function showPdfInNewTab(base64Data: string) {\n    const nav = window.navigator as any;\n    if (nav && nav.msSaveOrOpenBlob) {\n        var byteCharacters = atob(base64Data);\n        var byteNumbers = new Array(byteCharacters.length);\n        for (var i = 0; i < byteCharacters.length; i++) {\n            byteNumbers[i] = byteCharacters.charCodeAt(i);\n        }\n        var byteArray = new Uint8Array(byteNumbers);\n        var blob = new Blob([byteArray], {\n            type: 'application/pdf'\n        });\n        nav.msSaveOrOpenBlob(blob, 'myreport.pdf');\n    } else {\n        var pdfWindow: any = window.open('', '_blank');\n        const iframeTag = \"<iframe width='100%' style='margin: -8px;border: none;' height='100%' src='data:application/pdf;base64, \";\n        const iframeTagClose = \"'></iframe>\";\n        pdfWindow.document.write(iframeTag + encodeURI(base64Data) + iframeTagClose);\n    }\n}\n// get data productivity by month\nexport const getDataProductivityByMonth = (array: IProductivity[], month: number) => {\n    return (\n        array &&\n        array.length > 0 &&\n        array.filter((el: IProductivity) => {\n            return el.month === month;\n        })\n    );\n};\n\n// get receveiable by month\nexport const getReceivableByMonth = (array: IProductivity[], month: number) => {\n    const matchingData = array.find((el: IProductivity) => el.month === month);\n    return matchingData ? matchingData.delivered?.value : 0;\n};\n\n// CV config check IsEdit skill\n//edit\nexport const checkAndAddIsEdit = (skillList1: ISkill[], skillList2: ISkill[]) => {\n    const newdata = [];\n\n    const skillList2Set = new Set(skillList2.map((item: ISkill) => item.idHexString));\n\n    for (const item of skillList1) {\n        const isDelete = !skillList2Set.has(item.idHexString);\n        const matchingItem = skillList2.find((item2: ISkill) => item2.idHexString === item.idHexString);\n        const name = matchingItem ? matchingItem.name : item.name;\n        const new_item = { idHexString: item.idHexString, name, isDelete };\n        newdata.push(new_item);\n    }\n\n    for (const item of skillList2) {\n        if (!item.idHexString) {\n            newdata.push({ ...item, isDelete: false });\n        } else if (!skillList1.some((item1: any) => item1.idHexString === item.idHexString)) {\n            newdata.push({ ...item, isDelete: true });\n        }\n    }\n\n    return newdata;\n};\n\n//add\nexport const convertValueToNewValue = (value: any) => {\n    const newValue = [];\n\n    for (const item of value) {\n        if (item.idHexString !== undefined) {\n            newValue.push({ name: item.name, isDelete: false });\n        } else if (item.name && item.isDelete !== undefined) {\n            newValue.push({ name: item.name, isDelete: item.isDelete });\n        }\n    }\n\n    return newValue;\n};\n\n// compare effort member\nexport const compareStyleEffortMember = (effortVerified?: number | null, effort?: number | null) => {\n    return { color: (effortVerified || 0) !== effort ? '#E70A17' : 'inherit' };\n};\n// local storage\nexport const setLocalStorageSearchTime = (value: any) => {\n    localStorage.setItem(SEARCH_TIMESTAMP, `?${transformRequestOptions(value)}`);\n};\n\nexport const converStringToCalculationInputsObject = (\n    input: string\n): {\n    sign?: string;\n    code?: string;\n}[] => {\n    return input.split(/([+\\-*/])/).reduce<\n        {\n            sign?: string;\n            code?: string;\n        }[]\n    >((acc, current, index, array) => {\n        if (index === 0) {\n            acc.push({ sign: '', code: current });\n        } else if (index % 2 !== 0) {\n            acc.push({ sign: current, code: array[index + 1] });\n        }\n        return acc;\n    }, []);\n};\n\nexport const convertCalculationInputsToString = (\n    calculationInputs: {\n        sign?: string;\n        code?: IOption;\n    }[]\n): string => {\n    return calculationInputs\n        .map((item, index) => {\n            if (index === 0) {\n                return item.code?.value;\n            }\n            return `${item.sign}${item.code?.value}`;\n        })\n        .join('');\n};\n\n// Check is holiday\nexport const checkIsHoliday = (date: Date, holidays: string[]) => {\n    const dateStr = `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1)\n        .toString()\n        .padStart(2, '0')}/${date.getFullYear()}`;\n    return holidays.includes(dateStr);\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,OAAO;AAEhC,SAASC,MAAM,QAAQ,YAAY;;AAEnC;;AAEA,SAASC,MAAM,QAAQ,kBAAkB;AACzC,SAASC,cAAc,QAAQ,WAAW;AAC1C,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,gBAAgB,QAAQ,kBAAkB;;AAEnD;AACA;AACA;AACA;AACA;AAJA,SAAAC,MAAA,IAAAC,OAAA,EAAAP,QAAA,IAAAQ,SAAA;AAKA,OAAO,MAAMC,WAAW,GAAIC,KAAU,IAAK;EACvC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CAAC,CAACH,KAAK,CAAC;AACxD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,iBAAiB,GAAIJ,KAAU,IAAK;EAC7C,MAAMK,KAAK,GAAGL,KAAK,CAACM,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;EACzCF,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACG,OAAO,CAAC,uBAAuB,EAAE,GAAG,CAAC;EACzD,OAAOH,KAAK,CAACI,IAAI,CAAC,GAAG,CAAC;AAC1B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,uBAAuB,GAAIC,MAAW,IAAK;EACpD,IAAIC,OAAO,GAAG,EAAE;EAChBC,MAAM,CAACC,OAAO,CAAMH,MAAM,CAAC,CAACI,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;IAClD,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,EAAE;MACpCL,OAAO,IAAI,GAAGI,GAAG,IAAIE,kBAAkB,CAACD,KAAK,CAAC,GAAG;IACrD,CAAC,MAAM,IAAIE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;MAC7BA,KAAK,CAACF,OAAO,CAAEM,EAAE,IAAK;QAClBT,OAAO,IAAI,GAAGI,GAAG,IAAIE,kBAAkB,CAACG,EAAE,CAAC,GAAG;MAClD,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACF,OAAOT,OAAO,GAAGA,OAAO,CAACU,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGV,OAAO;AACnD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,cAAc,GAAGA,CAACC,GAAQ,EAAEC,KAAW,KAAK;EACrD,MAAMC,WAAW,GAAGjC,cAAc,CAAC,aAAa,CAAC;EACjD;EACA,MAAMkC,WAAW,GAAG,GAAGjC,SAAS,GAAG,GAAG,GAAG8B,GAAG,IAAId,uBAAuB,CAAC;IAAE,GAAGe,KAAK;IAAEG,KAAK,EAAEF;EAAY,CAAC,CAAC,EAAE;EAC3G,MAAMG,GAAG,GAAGC,MAAM,CAACC,IAAI,CAACJ,WAAW,EAAE,QAAQ,CAAE;EAC/CE,GAAG,CAACG,KAAK,CAAC,CAAC;AACf,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAGA,CAACC,QAAgB,EAAEC,IAAU,KAAK;EAC9D,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAAC,CAAC;EAC7B5C,MAAM,CAAC6C,IAAI,EAAEF,QAAQ,CAAC;AAC1B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,8BAA8B,GAAGA,CAACC,QAAuB,EAAEC,SAAkB,KAAK;EAC3F,IAAIC,cAAc,GAAGD,SAAS,GAAGA,SAAS,GAAG,GAAG;EAChD,IAAI,CAACD,QAAQ,EAAE,oBAAO1C,OAAA;IAAAqC,QAAA,EAAAQ,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAY,CAAC;EACnC,MAAMC,cAAc,GAAGN,QAAQ,CAAChC,KAAK,CAACkC,cAAc,CAAC;EAErD,IAAII,cAAc,CAACC,MAAM,GAAG,CAAC,EAAE;IAC3B,OAAOD,cAAc,CAACE,GAAG,CAAC,CAACC,IAAY,EAAEhC,GAAW,kBAChDnB,OAAA,CAACP,QAAQ;MAAA2D,QAAA,gBACLpD,OAAA;QAAAoD,QAAA,EAAOD,IAAI,CAACE,IAAI,CAAC;MAAC;QAAAhB,QAAA,EAAAQ,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACzBI,IAAI,CAACE,IAAI,CAAC,CAAC,iBAAIrD,OAAA;QAAAqC,QAAA,EAAAQ,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA,GAFX5B,GAAG;MAAAkB,QAAA,EAAAQ,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGR,CACb,CAAC;EACN;EAEA,OAAOL,QAAQ,CAACY,QAAQ,CAACV,cAAc,CAAC,GAAGF,QAAQ,CAACjB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGiB,QAAQ;AAC/E,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMa,sBAAsB,GAAIb,QAAkB,IAAK;EAC1D,OAAOA,QAAQ,CAACQ,GAAG,CAAC,CAACM,OAAO,EAAErC,GAAG,kBAC7BnB,OAAA,CAACP,QAAQ;IAAA2D,QAAA,gBACLpD,OAAA;MAAAoD,QAAA,EAAOI;IAAO;MAAAnB,QAAA,EAAAQ,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACtB/C,OAAA;MAAAqC,QAAA,EAAAQ,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA,GAFK5B,GAAG;IAAAkB,QAAA,EAAAQ,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGR,CACb,CAAC;AACN,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMU,aAAa,GAAIC,MAAc,IAAK;EAC7C,OAAO,CAAC,CAACA,MAAM,GAAG/D,MAAM,CAACgE,MAAM,CAAEnC,EAAE,IAAKA,EAAE,CAACJ,KAAK,KAAKsC,MAAM,CAAC,CAAC,CAAC,CAAC,CAACE,KAAK,gBAAG5D,OAAA,CAAAC,SAAA,mBAAI,CAAC;AACjF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM4D,OAAO,GAAIzC,KAAU,IAAc;EAC5C;IACI;IACAA,KAAK,IAAI,IAAI;IACb;IACCA,KAAK,CAAC0C,cAAc,CAAC,QAAQ,CAAC,IAAI1C,KAAK,CAAC6B,MAAM,KAAK,CAAE;IACtD;IACC7B,KAAK,CAAC2C,WAAW,KAAK/C,MAAM,IAAIA,MAAM,CAACgD,IAAI,CAAC5C,KAAK,CAAC,CAAC6B,MAAM,KAAK;EAAE;AAEzE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMgB,kBAAkB,GAAIC,IAAU,IAAc;EACvD,MAAMC,eAAe,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC;EACvC,MAAMC,aAAa,GAAGF,IAAI,CAACG,IAAI,CAAC3D,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC7C,OAAOyD,eAAe,CAACb,QAAQ,CAACc,aAAa,CAAC;AAClD,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAME,WAAW,GAAIC,MAAW,IAAK;EACxC,IAAIA,MAAM,EAAE;IACR,IAAI,CAAAA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEtB,MAAM,MAAK,CAAC,EAAE;MACtB;MACA,OAAO,IAAI;IACf,CAAC,MAAM,IAAIsB,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC1B;MACA,IAAI,CAAAA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEtB,MAAM,MAAK,CAAC,EAAE;QACtB,OAAO,KAAK;MAChB,CAAC,MAAM;QACH,OAAO,IAAI;MACf;IACJ,CAAC,MAAM;MACH,IAAI,CAACuB,KAAK,CAACD,MAAM,CAAC,EAAE;QAChB;QACA,OAAO,KAAK;MAChB,CAAC,MAAM;QACH,OAAO,IAAI;MACf;IACJ;EACJ;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,cAAc,GAAGA,CAACC,SAAmB,EAAEC,YAA6B,EAAEC,cAAyB,KAAa;EACrH,IAAIC,SAAiC,GAAG,CAAC,CAAC;EAE1C,KAAK,MAAM1D,GAAG,IAAIuD,SAAS,EAAE;IACzB,CAACJ,WAAW,CAACK,YAAY,CAACG,GAAG,CAAC3D,GAAG,CAAS,CAAC,GACpC0D,SAAS,CAAC1D,GAAG,CAAC,GAAGwD,YAAY,CAACG,GAAG,CAAC3D,GAAG,CAAC,GAAG,CAACwD,YAAY,CAACG,GAAG,CAAC3D,GAAG,CAAE,GAAG,IAAI,GACvE0D,SAAS,CAAC1D,GAAG,CAAC,GAAGwD,YAAY,CAACG,GAAG,CAAC3D,GAAG,CAAE;EAClD;EACA,IAAIyD,cAAc,EAAE;IAChB,KAAK,MAAMzD,GAAG,IAAIyD,cAAc,EAAE;MAC9B,IAAIG,aAAa,GAAG,CAACP,KAAK,CAACG,YAAY,CAACK,MAAM,CAAC7D,GAAG,CAAC,CAAC,CAAC,CAAQ,CAAC;MAC9D0D,SAAS,CAAC1D,GAAG,CAAC,GAAG4D,aAAa,GAAGE,0BAA0B,CAACN,YAAY,CAACK,MAAM,CAAC7D,GAAG,CAAC,CAAC,GAAGwD,YAAY,CAACK,MAAM,CAAC7D,GAAG,CAAC;IACpH;EACJ;EACA,OAAO0D,SAAS;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,eAAe,GAAGA,CAACC,GAAQ,EAAEC,UAAqB,KAAK;EAChEpE,MAAM,CAACgD,IAAI,CAACmB,GAAG,CAAC,CAACjE,OAAO,CAAEC,GAAG,IAAK;IAC9B,IAAI0C,OAAO,CAACsB,GAAG,CAAChE,GAAG,CAAC,CAAC,IAAIiE,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAE9B,QAAQ,CAACnC,GAAG,CAAC,EAAE;MAChD,OAAOgE,GAAG,CAAChE,GAAG,CAAC;IACnB;EACJ,CAAC,CAAC;EAEF,OAAOgE,GAAG;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,gBAAgB,GAAIC,GAAQ,IAAK;EAAA,IAAAC,aAAA;EAC1C,MAAMC,YAAY,GAAG,CAAClE,KAAK,CAACC,OAAO,CAAC+D,GAAG,CAAC,GAAGA,GAAG,aAAHA,GAAG,wBAAAC,aAAA,GAAHD,GAAG,CAAE7E,QAAQ,CAAC,CAAC,cAAA8E,aAAA,uBAAfA,aAAA,CAAiBlC,IAAI,CAAC,CAAC,CAAC1C,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACD,KAAK,CAAC,IAAI,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,GAAG0E,GAAG;EAEjH,OAAOE,YAAY;AACvB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,uBAAuB,GAAGA,CAACC,MAAwB,EAAEC,MAAyB,KAAK;EAC5F,IAAIC,QAAQ,GAAG,CAAC;EAChB,KAAK,MAAMC,KAAK,IAAIF,MAAM,EAAE;IACxB,IAAIE,KAAK,CAACF,MAAM,CAACG,IAAI,CAAEC,KAAa,IAAKL,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE;MACrDH,QAAQ,GAAGC,KAAK,CAACD,QAAQ;MACzB;IACJ;EACJ;EACA,OAAOA,QAAQ;AACnB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,SAASX,0BAA0BA,CAACe,GAAa,EAAE;EACtD,OAAOA,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE9C,GAAG,CAAEoC,GAAG,IAAK;IACrB,OAAOW,MAAM,CAACX,GAAG,CAAC;EACtB,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASY,cAAcA,CAAC,GAAGC,OAAiB,EAAU;EACzD,IAAIC,GAAG,GAAG,CAAC;EACX,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAAClD,MAAM,EAAEoD,CAAC,EAAE,EAAE;IACrCD,GAAG,IAAID,OAAO,CAACE,CAAC,CAAC;EACrB;EACA,OAAOD,GAAG;AACd;;AAEA;;AAEA,OAAO,MAAME,iBAAiB,GAAGA,CAAClF,KAAa,EAAEmF,MAAiB,KAAK;EACnE,MAAMC,aAAa,GAAGD,MAAM,CAACE,IAAI,CAAEC,KAAc,IAAKA,KAAK,CAACtF,KAAK,KAAKA,KAAK,CAAC;EAC5E,IAAIoF,aAAa,EAAE;IACf,MAAMG,QAAQ,GAAGH,aAAa,CAAC5C,KAAK,CAACgD,SAAS,CAACJ,aAAa,CAAC5C,KAAK,CAACiD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEL,aAAa,CAAC5C,KAAK,CAACiD,OAAO,CAAC,GAAG,CAAC,CAAC,CAACxD,IAAI,CAAC,CAAC;IAC7H,MAAMyD,MAAM,GAAGN,aAAa,CAAC5C,KAAK,CAACgD,SAAS,CAACJ,aAAa,CAAC5C,KAAK,CAACiD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEL,aAAa,CAAC5C,KAAK,CAACiD,OAAO,CAAC,GAAG,CAAC,CAAC,CAACxD,IAAI,CAAC,CAAC;IAC3H,OAAO;MAAEsD,QAAQ;MAAEG;IAAO,CAAC;EAC/B;EACA,OAAO;IAAEH,QAAQ,EAAE,EAAE;IAAEG,MAAM,EAAE;EAAG,CAAC;AACvC,CAAC;;AAED;AACA,OAAO,MAAMC,mBAAmB,GAAGA,CAACL,KAAa,EAAEM,WAAmB,KAAK;EACvE,MAAMC,UAAU,GAAGP,KAAK,GAAGM,WAAW;EACtC,OAAOC,UAAU;AACrB,CAAC;;AAED;AACA,OAAO,MAAMC,eAAe,GAAGA,CAAClB,GAA0B,EAAEmB,kBAA0B,KAAK;EACvF,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,GAAG,CAAC/C,MAAM,EAAEoD,CAAC,EAAE,EAAE;IACjC,MAAM;MAAEe,OAAO;MAAEC;IAAS,CAAC,GAAGrB,GAAG,CAACK,CAAC,CAAC;IACpC,IAAIgB,QAAQ,IAAID,OAAO,KAAK,IAAI,EAAE;MAC9BpB,GAAG,CAACK,CAAC,CAAC,CAACiB,MAAM,GAAG,CAACF,OAAO,GAAG,CAACC,QAAQ,GAAGF,kBAAkB;IAC7D;EACJ;AACJ,CAAC;;AAED;AACA,OAAO,MAAMI,oBAAoB,GAAIvB,GAA0B,IAAK;EAChE,IAAIwB,WAAW,GAAG,CAAC;EACnB,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAGL,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE/C,MAAM,GAAEoD,CAAC,EAAE,EAAE;IAClC,IAAIL,GAAG,CAACK,CAAC,CAAC,CAACiB,MAAM,KAAK,IAAI,EAAE;MACxBE,WAAW,IAAIC,QAAQ,CAACzB,GAAG,CAACK,CAAC,CAAC,CAACiB,MAAM,CAAC;IAC1C;EACJ;EACA,OAAOE,WAAW;AACtB,CAAC;;AAED;AACA,OAAO,MAAME,qBAAqB,GAAGA,CAACC,QAAgB,EAAEC,QAAgB,KAAK;EACzE,MAAMC,YAAY,GAAGF,QAAQ,GAAGC,QAAQ;EACxC,OAAOC,YAAY;AACvB,CAAC;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAGA,CAACF,QAAgB,EAAEG,IAAY,KAAK;EAChE;EACA,MAAMC,OAAO,GAAGD,IAAI,GAAGH,QAAQ;EAC/B,OAAOI,OAAO;AAClB,CAAC;;AAED;AACA,OAAO,MAAMC,yBAAyB,GAAGA,CAACJ,YAAoB,EAAEK,yBAAiC,KAAK;EAClG,MAAMC,SAAS,GAAGN,YAAY,GAAGK,yBAAyB;EAC1D,OAAOC,SAAS;AACpB,CAAC;;AAED;AACA,OAAO,MAAMC,uBAAuB,GAAI9F,IAAqB,IAAK;EAC9D,IAAI+F,cAAc,GAAG,CAAC;EACtB,IAAI/F,IAAI,EAAE;IACN,KAAK,IAAI+D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/D,IAAI,CAACW,MAAM,EAAEoD,CAAC,EAAE,EAAE;MAClCgC,cAAc,IAAI/F,IAAI,CAAC+D,CAAC,CAAC,CAAE8B,SAAS,CAAE/G,KAAM;IAChD;IACA,OAAOiH,cAAc;EACzB;AACJ,CAAC;AACD;AACA,OAAO,MAAMC,sBAAsB,GAAGA,CAAChG,IAAqB,EAAEoE,KAAa,KAAK;EAC5E,IAAIpE,IAAI,EAAE;IACN,KAAK,MAAMa,IAAI,IAAIb,IAAI,EAAE;MACrB,IAAIa,IAAI,CAACuD,KAAK,KAAKA,KAAK,EAAE;QACtB,OAAOvD,IAAI,CAAEgF,SAAS,CAAE/G,KAAK;MACjC;IACJ;EACJ,CAAC,MAAM;IACH,OAAO,CAAC;EACZ;AACJ,CAAC;AAED,OAAO,MAAMmH,qBAAqB,GAAGA,CAAChC,MAAW,EAAEiC,WAAgB,KAAK;EACpE,MAAM9B,KAAK,GAAGH,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,IAAI,CAAEgC,CAAM,IAAKA,CAAC,CAAC/B,KAAK,KAAK8B,WAAW,CAAC;EAC/D,IAAI9B,KAAK,EAAE;IACP,OAAOA,KAAK,CAACgC,WAAW;EAC5B;EACA,OAAO,CAAC;AACZ,CAAC;;AAED;AACA,OAAO,MAAMC,kBAAkB,GAAIxF,IAAY,IAAK;EAChD,OAAOA,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,GAAG,SAAS,GAAG,EAAE;AAC5D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMyF,yBAAyB,GAAIC,IAAY,IAAK;EACvD,OAAOA,IAAI,CAAClI,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;AACxD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMmI,mBAAmB,GAAI5E,IAAU,IAAsB;EAChE,OAAO,IAAI6E,OAAO,CAAS,CAACC,OAAO,EAAEC,MAAM,KAAK;IAC5C,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,aAAa,CAAClF,IAAI,CAAC;IAC1BgF,MAAM,CAACG,MAAM,GAAG;MAAA,IAAAC,cAAA;MAAA,OAAMN,OAAO,CAAC,EAAAM,cAAA,GAAAJ,MAAM,CAACK,MAAM,cAAAD,cAAA,uBAAbA,cAAA,CAAe7I,QAAQ,CAAC,CAAC,KAAI,EAAE,CAAC;IAAA;IAC9DyI,MAAM,CAACM,OAAO,GAAI3D,KAAK,IAAKoD,MAAM,CAACpD,KAAK,CAAC;EAC7C,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,SAAS4D,eAAeA,CAACC,UAAkB,EAAE;EAChD,MAAMC,GAAG,GAAG1H,MAAM,CAAC2H,SAAgB;EACnC,IAAID,GAAG,IAAIA,GAAG,CAACE,gBAAgB,EAAE;IAC7B,IAAIC,cAAc,GAAGC,IAAI,CAACL,UAAU,CAAC;IACrC,IAAIM,WAAW,GAAG,IAAI1I,KAAK,CAACwI,cAAc,CAAC7G,MAAM,CAAC;IAClD,KAAK,IAAIoD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyD,cAAc,CAAC7G,MAAM,EAAEoD,CAAC,EAAE,EAAE;MAC5C2D,WAAW,CAAC3D,CAAC,CAAC,GAAGyD,cAAc,CAACG,UAAU,CAAC5D,CAAC,CAAC;IACjD;IACA,IAAI6D,SAAS,GAAG,IAAIC,UAAU,CAACH,WAAW,CAAC;IAC3C,IAAIzH,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC0H,SAAS,CAAC,EAAE;MAC7BE,IAAI,EAAE;IACV,CAAC,CAAC;IACFT,GAAG,CAACE,gBAAgB,CAACtH,IAAI,EAAE,cAAc,CAAC;EAC9C,CAAC,MAAM;IACH,IAAI8H,SAAc,GAAGpI,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;IAC9C,MAAMoI,SAAS,GAAG,0GAA0G;IAC5H,MAAMC,cAAc,GAAG,aAAa;IACpCF,SAAS,CAACG,QAAQ,CAACC,KAAK,CAACH,SAAS,GAAGI,SAAS,CAAChB,UAAU,CAAC,GAAGa,cAAc,CAAC;EAChF;AACJ;AACA;AACA,OAAO,MAAMI,0BAA0B,GAAGA,CAACC,KAAsB,EAAElE,KAAa,KAAK;EACjF,OACIkE,KAAK,IACLA,KAAK,CAAC3H,MAAM,GAAG,CAAC,IAChB2H,KAAK,CAACjH,MAAM,CAAEnC,EAAiB,IAAK;IAChC,OAAOA,EAAE,CAACkF,KAAK,KAAKA,KAAK;EAC7B,CAAC,CAAC;AAEV,CAAC;;AAED;AACA,OAAO,MAAMmE,oBAAoB,GAAGA,CAACD,KAAsB,EAAElE,KAAa,KAAK;EAAA,IAAAoE,qBAAA;EAC3E,MAAMC,YAAY,GAAGH,KAAK,CAACnE,IAAI,CAAEjF,EAAiB,IAAKA,EAAE,CAACkF,KAAK,KAAKA,KAAK,CAAC;EAC1E,OAAOqE,YAAY,IAAAD,qBAAA,GAAGC,YAAY,CAAC5C,SAAS,cAAA2C,qBAAA,uBAAtBA,qBAAA,CAAwB1J,KAAK,GAAG,CAAC;AAC3D,CAAC;;AAED;AACA;AACA,OAAO,MAAM4J,iBAAiB,GAAGA,CAACC,UAAoB,EAAEC,UAAoB,KAAK;EAC7E,MAAMC,OAAO,GAAG,EAAE;EAElB,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAACH,UAAU,CAAChI,GAAG,CAAEC,IAAY,IAAKA,IAAI,CAACmI,WAAW,CAAC,CAAC;EAEjF,KAAK,MAAMnI,IAAI,IAAI8H,UAAU,EAAE;IAC3B,MAAMM,QAAQ,GAAG,CAACH,aAAa,CAACI,GAAG,CAACrI,IAAI,CAACmI,WAAW,CAAC;IACrD,MAAMG,YAAY,GAAGP,UAAU,CAACzE,IAAI,CAAEiF,KAAa,IAAKA,KAAK,CAACJ,WAAW,KAAKnI,IAAI,CAACmI,WAAW,CAAC;IAC/F,MAAMjH,IAAI,GAAGoH,YAAY,GAAGA,YAAY,CAACpH,IAAI,GAAGlB,IAAI,CAACkB,IAAI;IACzD,MAAMsH,QAAQ,GAAG;MAAEL,WAAW,EAAEnI,IAAI,CAACmI,WAAW;MAAEjH,IAAI;MAAEkH;IAAS,CAAC;IAClEJ,OAAO,CAACS,IAAI,CAACD,QAAQ,CAAC;EAC1B;EAEA,KAAK,MAAMxI,IAAI,IAAI+H,UAAU,EAAE;IAC3B,IAAI,CAAC/H,IAAI,CAACmI,WAAW,EAAE;MACnBH,OAAO,CAACS,IAAI,CAAC;QAAE,GAAGzI,IAAI;QAAEoI,QAAQ,EAAE;MAAM,CAAC,CAAC;IAC9C,CAAC,MAAM,IAAI,CAACN,UAAU,CAACnF,IAAI,CAAE+F,KAAU,IAAKA,KAAK,CAACP,WAAW,KAAKnI,IAAI,CAACmI,WAAW,CAAC,EAAE;MACjFH,OAAO,CAACS,IAAI,CAAC;QAAE,GAAGzI,IAAI;QAAEoI,QAAQ,EAAE;MAAK,CAAC,CAAC;IAC7C;EACJ;EAEA,OAAOJ,OAAO;AAClB,CAAC;;AAED;AACA,OAAO,MAAMW,sBAAsB,GAAI1K,KAAU,IAAK;EAClD,MAAM2K,QAAQ,GAAG,EAAE;EAEnB,KAAK,MAAM5I,IAAI,IAAI/B,KAAK,EAAE;IACtB,IAAI+B,IAAI,CAACmI,WAAW,KAAKU,SAAS,EAAE;MAChCD,QAAQ,CAACH,IAAI,CAAC;QAAEvH,IAAI,EAAElB,IAAI,CAACkB,IAAI;QAAEkH,QAAQ,EAAE;MAAM,CAAC,CAAC;IACvD,CAAC,MAAM,IAAIpI,IAAI,CAACkB,IAAI,IAAIlB,IAAI,CAACoI,QAAQ,KAAKS,SAAS,EAAE;MACjDD,QAAQ,CAACH,IAAI,CAAC;QAAEvH,IAAI,EAAElB,IAAI,CAACkB,IAAI;QAAEkH,QAAQ,EAAEpI,IAAI,CAACoI;MAAS,CAAC,CAAC;IAC/D;EACJ;EAEA,OAAOQ,QAAQ;AACnB,CAAC;;AAED;AACA,OAAO,MAAME,wBAAwB,GAAGA,CAACC,cAA8B,EAAEC,MAAsB,KAAK;EAChG,OAAO;IAAEC,KAAK,EAAE,CAACF,cAAc,IAAI,CAAC,MAAMC,MAAM,GAAG,SAAS,GAAG;EAAU,CAAC;AAC9E,CAAC;AACD;AACA,OAAO,MAAME,yBAAyB,GAAIjL,KAAU,IAAK;EACrDkL,YAAY,CAACC,OAAO,CAACzM,gBAAgB,EAAE,IAAIe,uBAAuB,CAACO,KAAK,CAAC,EAAE,CAAC;AAChF,CAAC;AAED,OAAO,MAAMoL,qCAAqC,GAC9CC,KAAa,IAIV;EACH,OAAOA,KAAK,CAAC/L,KAAK,CAAC,WAAW,CAAC,CAACgM,MAAM,CAKpC,CAACC,GAAG,EAAEC,OAAO,EAAEC,KAAK,EAAEjC,KAAK,KAAK;IAC9B,IAAIiC,KAAK,KAAK,CAAC,EAAE;MACbF,GAAG,CAACf,IAAI,CAAC;QAAEkB,IAAI,EAAE,EAAE;QAAEC,IAAI,EAAEH;MAAQ,CAAC,CAAC;IACzC,CAAC,MAAM,IAAIC,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;MACxBF,GAAG,CAACf,IAAI,CAAC;QAAEkB,IAAI,EAAEF,OAAO;QAAEG,IAAI,EAAEnC,KAAK,CAACiC,KAAK,GAAG,CAAC;MAAE,CAAC,CAAC;IACvD;IACA,OAAOF,GAAG;EACd,CAAC,EAAE,EAAE,CAAC;AACV,CAAC;AAED,OAAO,MAAMK,gCAAgC,GACzCC,iBAGG,IACM;EACT,OAAOA,iBAAiB,CACnB/J,GAAG,CAAC,CAACC,IAAI,EAAE0J,KAAK,KAAK;IAAA,IAAAK,WAAA;IAClB,IAAIL,KAAK,KAAK,CAAC,EAAE;MAAA,IAAAM,UAAA;MACb,QAAAA,UAAA,GAAOhK,IAAI,CAAC4J,IAAI,cAAAI,UAAA,uBAATA,UAAA,CAAW/L,KAAK;IAC3B;IACA,OAAO,GAAG+B,IAAI,CAAC2J,IAAI,IAAAI,WAAA,GAAG/J,IAAI,CAAC4J,IAAI,cAAAG,WAAA,uBAATA,WAAA,CAAW9L,KAAK,EAAE;EAC5C,CAAC,CAAC,CACDR,IAAI,CAAC,EAAE,CAAC;AACjB,CAAC;;AAED;AACA,OAAO,MAAMwM,cAAc,GAAGA,CAACC,IAAU,EAAEC,QAAkB,KAAK;EAC9D,MAAMC,OAAO,GAAG,GAAGF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC/M,QAAQ,CAAC,CAAC,CAACgN,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAACJ,IAAI,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,EAChFjN,QAAQ,CAAC,CAAC,CACVgN,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIJ,IAAI,CAACM,WAAW,CAAC,CAAC,EAAE;EAC7C,OAAOL,QAAQ,CAAChK,QAAQ,CAACiK,OAAO,CAAC;AACrC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}