{"ast": null, "code": "/* eslint-disable react-hooks/exhaustive-deps */import React,{useCallback,useEffect,useState}from'react';// project imports\nimport{MonthlyEffortMemberProjectSearch,MonthlyEffortMemberProjectTBody,MonthlyEffortMemberProjectThead,MonthlyEffortMemberSearch,MonthlyEffortMemberTBody,MonthlyEffortMemberThead}from'containers/monthly-effort';import{SEARCH_PARAM_KEY,TEXT_CONFIG_SCREEN,monthlyEffortReportTabs,paginationParamDefault,paginationResponseDefault}from'constants/Common';import{exportDocument,getSearchParam,setLocalStorageSearchTime,transformObject}from'utils/common';import{checkAllowedPermission,checkAllowedTab}from'utils/authorization';import{monthlyEffortConfig}from'./Config';import{Table,TableFooter}from'components/extended/Table';import{convertMonthFromToDate}from'utils/date';import{TabPanel}from'components/extended/Tabs';import{PERMISSIONS}from'constants/Permission';import{FilterCollapse}from'containers/search';import MainCard from'components/cards/MainCard';import sendRequest from'services/ApiService';import{getMonthsOfYear}from'utils/date';import{TabCustom}from'containers';import Api from'constants/Api';// third party\nimport{useSearchParams}from'react-router-dom';// ==============================|| Monthly Effort - Department member ||============================== //\n/**\n *  URL Params\n *  tab\n *  page\n *  size\n *  year\n *  month\n *  ====== tab 0 ======\n *  timeStatus\n *  departmentId\n *  userId\n *  fullname\n *  ====== tab 1 ======\n *  projectId\n *  projectName\n */import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const MonthlyEffortDepartmentMember=()=>{// URL Params\nconst[searchParams,setSearchParams]=useSearchParams();const keyParams=[SEARCH_PARAM_KEY.tab,SEARCH_PARAM_KEY.page,SEARCH_PARAM_KEY.size,SEARCH_PARAM_KEY.year,SEARCH_PARAM_KEY.month,// ====== tab 0 - Member ======\nSEARCH_PARAM_KEY.timeStatus,SEARCH_PARAM_KEY.departmentId,SEARCH_PARAM_KEY.userId,SEARCH_PARAM_KEY.fullname,// ====== tab 1 - Project ======\nSEARCH_PARAM_KEY.projectId,SEARCH_PARAM_KEY.projectName];const params=getSearchParam(keyParams,searchParams);transformObject(params);// delete unnecessary key value\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst{fullname,projectName,...cloneParams}=params;// Hooks, State, Variable\nconst defaultConditions={...monthlyEffortConfig,...cloneParams,userId:params.userId?{value:params.userId,label:params.fullname}:null,projectId:params.projectId?{value:params.projectId,label:params.projectName}:null};const[loading,setLoading]=useState(false);const[paginationResponse,setPaginationResponse]=useState({...paginationResponseDefault,pageNumber:params.page?params.page:paginationResponseDefault.pageNumber,pageSize:params.size?params.size:paginationResponseDefault.pageSize});const[members,setMembers]=useState([]);const[projects,setProjects]=useState([]);const[tabValue,setTabValue]=useState(checkAllowedTab(monthlyEffortReportTabs,params.tab)[0]);const[conditions,setConditions]=useState(defaultConditions);const[formReset,setFormReset]=useState(defaultConditions);const[year,setYear]=useState(defaultConditions.year);const[monthSearch,setMonthSearch]=useState(params.month);const[yearSearch,setYearSearch]=useState(params.year);const[months,setMonths]=useState(getMonthsOfYear(monthlyEffortConfig.year));const[isChangeYear,setIsChangeYear]=useState(false);const[isChangeTab,setIsChangeTab]=useState(false);const{monthlyEffort}=PERMISSIONS.report;const{effortbymember}=TEXT_CONFIG_SCREEN.monthlyEffort;// Function\nconst getDataTable=async tabNumber=>{setLoading(true);if(tabNumber===0){delete conditions.projectId;}else{delete conditions.timeStatus;delete conditions.userId;}const payload=tabNumber===0?conditions.userId?{userId:conditions.userId.value}:null:conditions.projectId?{projectId:conditions.projectId.value}:null;const response=await sendRequest(tabNumber===0?Api.monthly_efford.getMember:Api.monthly_efford.getMemberProject,{...conditions,...payload,page:conditions.page+1});if(response){const{status,result}=response;if(status){const{content,pagination}=result;setPaginationResponse({...paginationResponse,totalElement:pagination.totalElement});tabValue===0?setMembers(content):setProjects(content);setLoading(false);}else{setDataEmpty();}return;}else{setDataEmpty();}};const setDataEmpty=()=>{setProjects([]);setMembers([]);setLoading(false);};const getMonthsInYear=useCallback(async p=>{const monthInYear=await getMonthsOfYear(p);return monthInYear;},[]);// ---------------------auto filter------------------\nconst monthOnload=months.find(item=>item.value===formReset.month);useEffect(()=>{if(monthOnload){setFormReset({...formReset,userId:null,...convertMonthFromToDate(monthOnload.label)});}},[monthOnload]);const handleChangeMonth=values=>{const getMonth=months.find(month=>month.value===values);if(getMonth){setFormReset({...formReset,month:values,userId:null,...convertMonthFromToDate(getMonth.label)});}};const handleChangeTimeStatus=values=>{setFormReset({...formReset,timeStatus:values,userId:null});};const handleChangeDepartmentId=values=>{setFormReset({...formReset,departmentId:values,userId:null});};const handleChangeMember=values=>{if(values){setFormReset({...formReset,year,timeStatus:values.timeStatus,departmentId:values.departmentId,userId:{value:values.userId,label:\"\".concat(values.firstName,\" \").concat(values.lastName)}});}else{setFormReset({...formReset,userId:null});}};// ---------------------end auto filter------------------\n// Event\nconst handleChangePage=(event,newPage)=>{setConditions({...conditions,page:newPage});setSearchParams({...params,page:newPage});};const handleChangeRowsPerPage=event=>{setConditions({...conditions,page:paginationParamDefault.page,size:parseInt(event.target.value,10)});setSearchParams({...params,page:paginationParamDefault.page,size:parseInt(event.target.value,10)});};const handleChangeTab=(event,newTabValue)=>{setTabValue(newTabValue);setYear(monthlyEffortConfig.year);setSearchParams({tab:newTabValue,month:monthSearch,year:yearSearch});setConditions({...monthlyEffortConfig,...paginationParamDefault,month:monthSearch,year:yearSearch});getDataTable(tabValue);setIsChangeTab(true);setIsChangeYear(false);setFormReset({...conditions});};const handleChangeYear=e=>{const{value}=e.target;setYear(value);setIsChangeYear(true);setIsChangeTab(false);};const handleExportDocument=()=>{exportDocument(Api.monthly_efford.getDownload.url,{year:conditions.year,month:conditions.month});};// Handle submit\nconst handleSearch=value=>{const{userId,projectId}=value;transformObject(value);const monthlyEffortMember=userId?{...value,tab:tabValue,userId:userId.value,fullname:userId.label}:{...value,tab:tabValue};const monthlyEffortProject=projectId?{...value,tab:tabValue,projectId:projectId.value,projectName:projectId.label}:{...value,tab:tabValue};setSearchParams(tabValue===0?monthlyEffortMember:monthlyEffortProject);setConditions({...value,page:paginationParamDefault.page});setMonthSearch(value.month);setYearSearch(value.year);// lưu thời gian vào localStorage\nsetLocalStorageSearchTime({month:value.month,year:value.year});};// Effect\nuseEffect(()=>{getDataTable(tabValue);},[tabValue,conditions]);useEffect(()=>{getMonthsInYear(year).then(items=>{setMonths(items);if(items.length>0&&isChangeYear){setFormReset({...formReset,year,month:months[0].value});}});},[year]);useEffect(()=>{getMonthsInYear(conditions.year).then(items=>{setMonths(items);if(items.length>0&&isChangeTab){setFormReset({...formReset,year:yearSearch?yearSearch:monthlyEffortConfig.year,month:monthSearch?monthSearch:monthlyEffortConfig.month});}});},[tabValue]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(TabCustom,{value:tabValue,handleChange:handleChangeTab,tabs:monthlyEffortReportTabs}),/*#__PURE__*/_jsxs(FilterCollapse,{handleExport:checkAllowedPermission(monthlyEffort.memberDownload)?handleExportDocument:undefined,downloadLabel:effortbymember+'download-report',children:[/*#__PURE__*/_jsx(TabPanel,{value:tabValue,index:0,children:/*#__PURE__*/_jsx(MonthlyEffortMemberSearch,{formReset:formReset,months:months,handleChangeYear:handleChangeYear,handleSearch:handleSearch,handleChangeMonth:handleChangeMonth,handleChangeTimeStatus:handleChangeTimeStatus,handleChangeDepartmentId:handleChangeDepartmentId,handleChangeMember:handleChangeMember})}),/*#__PURE__*/_jsx(TabPanel,{value:tabValue,index:1,children:/*#__PURE__*/_jsx(MonthlyEffortMemberProjectSearch,{formReset:formReset,months:months,handleChangeYear:handleChangeYear,handleSearch:handleSearch})})]}),/*#__PURE__*/_jsxs(MainCard,{children:[/*#__PURE__*/_jsx(TabPanel,{value:tabValue,index:0,children:/*#__PURE__*/_jsx(Table,{heads:/*#__PURE__*/_jsx(MonthlyEffortMemberThead,{}),isLoading:loading,data:members,children:/*#__PURE__*/_jsx(MonthlyEffortMemberTBody,{pageNumber:conditions.page,pageSize:conditions.size,members:members})})}),/*#__PURE__*/_jsx(TabPanel,{value:tabValue,index:1,children:/*#__PURE__*/_jsx(Table,{heads:/*#__PURE__*/_jsx(MonthlyEffortMemberProjectThead,{}),isLoading:loading,data:projects,children:/*#__PURE__*/_jsx(MonthlyEffortMemberProjectTBody,{pageNumber:conditions.page,pageSize:conditions.size,projects:projects})})})]}),/*#__PURE__*/_jsx(TableFooter,{pagination:{total:paginationResponse.totalElement,page:conditions.page,size:conditions.size},onPageChange:handleChangePage,onRowsPerPageChange:handleChangeRowsPerPage})]});};export default MonthlyEffortDepartmentMember;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}