{"ast": null, "code": "// material-ui\nimport{Grid}from'@mui/material';// project imports\nimport{Input}from'components/extended/Form';import{E_IS_LOGTIME,E_BIDDING_STATUS}from'constants/Common';import{Member}from'containers/search';// third party\nimport{FormattedMessage}from'react-intl';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AddOrEditBiddingOtherInfo=props=>{const{handleChangeUserContact,status}=props;const{salesReport}=TEXT_CONFIG_SCREEN;return/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Member,{isLogTime:E_IS_LOGTIME.YES,isDefaultAll:true,name:\"otherInfo.contact\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-contact'}),isIdHexString:true,handleChange:handleChangeUserContact,disabled:status===E_BIDDING_STATUS.CONTRACT})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-phone-number'}),name:\"otherInfo.phoneNumber\",disabled:status===E_BIDDING_STATUS.CONTRACT})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-presale-folder'}),name:\"otherInfo.presaleFolder\",disabled:status===E_BIDDING_STATUS.CONTRACT})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-email-address'}),name:\"otherInfo.emailAddress\",disabled:status===E_BIDDING_STATUS.CONTRACT})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Input,{label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.allSalesPineline+'-customer-contact'})// thieu\n,name:\"otherInfo.customerContact\",disabled:status===E_BIDDING_STATUS.CONTRACT})})]});};export default AddOrEditBiddingOtherInfo;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}