{"ast": null, "code": "import { useRef } from 'react';\nimport { isNodeOrChild } from './utils/is-node-or-child.mjs';\nimport { usePointerEvent, addPointerEvent } from '../events/use-pointer-event.mjs';\nimport { useUnmountEffect } from '../utils/use-unmount-effect.mjs';\nimport { AnimationType } from '../render/utils/types.mjs';\nimport { isDragActive } from './drag/utils/lock.mjs';\nimport { pipe } from '../utils/pipe.mjs';\n\n/**\n * @param handlers -\n * @internal\n */\nfunction useTapGesture(_ref) {\n  let {\n    onTap,\n    onTapStart,\n    onTapCancel,\n    whileTap,\n    visualElement\n  } = _ref;\n  const hasPressListeners = onTap || onTapStart || onTapCancel || whileTap;\n  const isPressing = useRef(false);\n  const cancelPointerEndListeners = useRef(null);\n  /**\n   * Only set listener to passive if there are no external listeners.\n   */\n  const eventOptions = {\n    passive: !(onTapStart || onTap || onTapCancel || onPointerDown)\n  };\n  function removePointerEndListener() {\n    cancelPointerEndListeners.current && cancelPointerEndListeners.current();\n    cancelPointerEndListeners.current = null;\n  }\n  function checkPointerEnd() {\n    removePointerEndListener();\n    isPressing.current = false;\n    visualElement.animationState && visualElement.animationState.setActive(AnimationType.Tap, false);\n    return !isDragActive();\n  }\n  function onPointerUp(event, info) {\n    if (!checkPointerEnd()) return;\n    /**\n     * We only count this as a tap gesture if the event.target is the same\n     * as, or a child of, this component's element\n     */\n    !isNodeOrChild(visualElement.current, event.target) ? onTapCancel && onTapCancel(event, info) : onTap && onTap(event, info);\n  }\n  function onPointerCancel(event, info) {\n    if (!checkPointerEnd()) return;\n    onTapCancel && onTapCancel(event, info);\n  }\n  function onPointerDown(event, info) {\n    removePointerEndListener();\n    if (isPressing.current) return;\n    isPressing.current = true;\n    cancelPointerEndListeners.current = pipe(addPointerEvent(window, \"pointerup\", onPointerUp, eventOptions), addPointerEvent(window, \"pointercancel\", onPointerCancel, eventOptions));\n    /**\n     * Ensure we trigger animations before firing event callback\n     */\n    visualElement.animationState && visualElement.animationState.setActive(AnimationType.Tap, true);\n    onTapStart && onTapStart(event, info);\n  }\n  usePointerEvent(visualElement, \"pointerdown\", hasPressListeners ? onPointerDown : undefined, eventOptions);\n  useUnmountEffect(removePointerEndListener);\n}\nexport { useTapGesture };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}