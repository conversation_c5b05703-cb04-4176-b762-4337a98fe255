{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/skills-manage/FieldsEducationHistory.tsx\";\n// material-ui\nimport { IconButton, Stack, TableCell, TableRow } from '@mui/material';\n\n// project imports\nimport { Checkbox } from 'components/extended/Form';\nimport InputTable from './InputTable';\n\n// assets\nimport Visibility from '@mui/icons-material/Visibility';\nimport VisibilityOff from '@mui/icons-material/VisibilityOff';\nimport { DeleteTwoToneIcon } from 'assets/images/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FieldsEducationHistory = props => {\n  const {\n    index,\n    handleRemove,\n    idHexString\n  } = props;\n  return /*#__PURE__*/_jsxDEV(TableRow, {\n    sx: {\n      position: 'relative'\n    },\n    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n      className: \"from-to-date-col vertical-align-top\",\n      children: [/*#__PURE__*/_jsxDEV(InputTable, {\n        name: `educationHistory.${index}.fromDate`,\n        placeholder: \"Fill from\",\n        label: \"From\",\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(InputTable, {\n        name: `educationHistory.${index}.toDate`,\n        placeholder: \"Fill to\",\n        label: \"To\",\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n      className: \"vertical-align-top\",\n      children: [/*#__PURE__*/_jsxDEV(InputTable, {\n        name: `educationHistory.${index}.school`,\n        label: \"University/School\",\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(InputTable, {\n        name: `educationHistory.${index}.qualification`,\n        placeholder: \"Enter\",\n        label: \"Degree/Qualifications\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        sx: {\n          position: 'absolute',\n          top: '50%',\n          right: '-100px',\n          transform: 'translateY(-50%)',\n          '& .Mui-checked': {\n            color: '#9e9e9e !important'\n          }\n        },\n        direction: \"row\",\n        justifyContent: \"space-between\",\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => handleRemove(index, idHexString),\n          children: /*#__PURE__*/_jsxDEV(DeleteTwoToneIcon, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Checkbox, {\n            name: `educationHistory.${index}.visible`,\n            checkboxProps: {\n              icon: /*#__PURE__*/_jsxDEV(VisibilityOff, {\n                fontSize: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 39\n              }, this),\n              checkedIcon: /*#__PURE__*/_jsxDEV(Visibility, {\n                fontSize: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 46\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 13\n    }, this)]\n  }, index, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 9\n  }, this);\n};\n_c = FieldsEducationHistory;\nexport default FieldsEducationHistory;\nvar _c;\n$RefreshReg$(_c, \"FieldsEducationHistory\");", "map": {"version": 3, "names": ["IconButton", "<PERSON><PERSON>", "TableCell", "TableRow", "Checkbox", "InputTable", "Visibility", "VisibilityOff", "DeleteTwoToneIcon", "jsxDEV", "_jsxDEV", "FieldsEducationHistory", "props", "index", "handleRemove", "idHexString", "sx", "position", "children", "className", "name", "placeholder", "label", "required", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "top", "right", "transform", "color", "direction", "justifyContent", "spacing", "onClick", "fontSize", "checkboxProps", "icon", "checkedIcon", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/skills-manage/FieldsEducationHistory.tsx"], "sourcesContent": ["// material-ui\nimport { <PERSON><PERSON>B<PERSON>on, Stack, TableCell, TableRow } from '@mui/material';\n\n// project imports\nimport { Checkbox } from 'components/extended/Form';\nimport InputTable from './InputTable';\n\n// assets\nimport Visibility from '@mui/icons-material/Visibility';\nimport VisibilityOff from '@mui/icons-material/VisibilityOff';\nimport { DeleteTwoToneIcon } from 'assets/images/icons';\n\ninterface IFieldsEducationHistoryProps {\n    index: number;\n    handleRemove: (index: number, idHexString?: string | null) => void;\n    idHexString?: string | null;\n}\n\nconst FieldsEducationHistory = (props: IFieldsEducationHistoryProps) => {\n    const { index, handleRemove, idHexString } = props;\n\n    return (\n        <TableRow key={index} sx={{ position: 'relative' }}>\n            <TableCell className=\"from-to-date-col vertical-align-top\">\n                <InputTable name={`educationHistory.${index}.fromDate`} placeholder=\"Fill from\" label=\"From\" required />\n                <InputTable name={`educationHistory.${index}.toDate`} placeholder=\"Fill to\" label=\"To\" required />\n            </TableCell>\n            <TableCell className=\"vertical-align-top\">\n                <InputTable name={`educationHistory.${index}.school`} label=\"University/School\" required />\n                <InputTable name={`educationHistory.${index}.qualification`} placeholder=\"Enter\" label=\"Degree/Qualifications\" />\n                <Stack\n                    sx={{\n                        position: 'absolute',\n                        top: '50%',\n                        right: '-100px',\n                        transform: 'translateY(-50%)',\n                        '& .Mui-checked': {\n                            color: '#9e9e9e !important'\n                        }\n                    }}\n                    direction=\"row\"\n                    justifyContent=\"space-between\"\n                    spacing={2}\n                >\n                    <IconButton onClick={() => handleRemove(index, idHexString)}>\n                        <DeleteTwoToneIcon fontSize=\"small\" />\n                    </IconButton>\n                    <div>\n                        <Checkbox\n                            name={`educationHistory.${index}.visible`}\n                            checkboxProps={{\n                                icon: <VisibilityOff fontSize=\"small\" />,\n                                checkedIcon: <Visibility fontSize=\"small\" />\n                            }}\n                        />\n                    </div>\n                </Stack>\n            </TableCell>\n        </TableRow>\n    );\n};\n\nexport default FieldsEducationHistory;\n"], "mappings": ";AAAA;AACA,SAASA,UAAU,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;;AAEtE;AACA,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAOC,UAAU,MAAM,cAAc;;AAErC;AACA,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,SAASC,iBAAiB,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQxD,MAAMC,sBAAsB,GAAIC,KAAmC,IAAK;EACpE,MAAM;IAAEC,KAAK;IAAEC,YAAY;IAAEC;EAAY,CAAC,GAAGH,KAAK;EAElD,oBACIF,OAAA,CAACP,QAAQ;IAAaa,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAW,CAAE;IAAAC,QAAA,gBAC/CR,OAAA,CAACR,SAAS;MAACiB,SAAS,EAAC,qCAAqC;MAAAD,QAAA,gBACtDR,OAAA,CAACL,UAAU;QAACe,IAAI,EAAE,oBAAoBP,KAAK,WAAY;QAACQ,WAAW,EAAC,WAAW;QAACC,KAAK,EAAC,MAAM;QAACC,QAAQ;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxGjB,OAAA,CAACL,UAAU;QAACe,IAAI,EAAE,oBAAoBP,KAAK,SAAU;QAACQ,WAAW,EAAC,SAAS;QAACC,KAAK,EAAC,IAAI;QAACC,QAAQ;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3F,CAAC,eACZjB,OAAA,CAACR,SAAS;MAACiB,SAAS,EAAC,oBAAoB;MAAAD,QAAA,gBACrCR,OAAA,CAACL,UAAU;QAACe,IAAI,EAAE,oBAAoBP,KAAK,SAAU;QAACS,KAAK,EAAC,mBAAmB;QAACC,QAAQ;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3FjB,OAAA,CAACL,UAAU;QAACe,IAAI,EAAE,oBAAoBP,KAAK,gBAAiB;QAACQ,WAAW,EAAC,OAAO;QAACC,KAAK,EAAC;MAAuB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjHjB,OAAA,CAACT,KAAK;QACFe,EAAE,EAAE;UACAC,QAAQ,EAAE,UAAU;UACpBW,GAAG,EAAE,KAAK;UACVC,KAAK,EAAE,QAAQ;UACfC,SAAS,EAAE,kBAAkB;UAC7B,gBAAgB,EAAE;YACdC,KAAK,EAAE;UACX;QACJ,CAAE;QACFC,SAAS,EAAC,KAAK;QACfC,cAAc,EAAC,eAAe;QAC9BC,OAAO,EAAE,CAAE;QAAAhB,QAAA,gBAEXR,OAAA,CAACV,UAAU;UAACmC,OAAO,EAAEA,CAAA,KAAMrB,YAAY,CAACD,KAAK,EAAEE,WAAW,CAAE;UAAAG,QAAA,eACxDR,OAAA,CAACF,iBAAiB;YAAC4B,QAAQ,EAAC;UAAO;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACbjB,OAAA;UAAAQ,QAAA,eACIR,OAAA,CAACN,QAAQ;YACLgB,IAAI,EAAE,oBAAoBP,KAAK,UAAW;YAC1CwB,aAAa,EAAE;cACXC,IAAI,eAAE5B,OAAA,CAACH,aAAa;gBAAC6B,QAAQ,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;cACxCY,WAAW,eAAE7B,OAAA,CAACJ,UAAU;gBAAC8B,QAAQ,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC/C;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,GAnCDd,KAAK;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAoCV,CAAC;AAEnB,CAAC;AAACa,EAAA,GA1CI7B,sBAAsB;AA4C5B,eAAeA,sBAAsB;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}