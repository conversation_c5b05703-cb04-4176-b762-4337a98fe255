{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/addorEditFlexibleReportConfig/Sum.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect } from 'react';\nimport HighlightOffOutlinedIcon from '@mui/icons-material/HighlightOffOutlined';\nimport DataSaverOnOutlinedIcon from '@mui/icons-material/DataSaverOnOutlined';\nimport { Box, ButtonBase, Divider, Popper, Typography } from '@mui/material';\nimport { useFieldArray, useFormContext } from 'react-hook-form';\nimport { FormattedMessage } from 'react-intl';\nimport { Autocomplete, Checkbox, Select } from 'components/extended/Form';\nimport { SIGN_CAlCULATE, TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sum = ({\n  columnsToSum\n}) => {\n  _s();\n  const {\n    Flexible_reporting_configuration\n  } = TEXT_CONFIG_SCREEN.administration.flexibleReport;\n  const methods = useFormContext();\n  const {\n    fields: calculationInputs,\n    append,\n    remove,\n    replace\n  } = useFieldArray({\n    control: methods.control,\n    name: 'calculationInputs'\n  });\n  const handleAddColumnValue = () => {\n    append({\n      sign: '+',\n      code: columnsToSum ? columnsToSum[0] : {}\n    });\n  };\n  useEffect(() => {\n    const calculationInputs = methods.getValues('calculationInputs');\n    if (!methods.getValues('id') && columnsToSum !== null && columnsToSum !== void 0 && columnsToSum.length && calculationInputs.length === 1 || methods.getValues('id') && !methods.getValues('isCalculation') || calculationInputs.filter(item => {\n      var _item$code;\n      return !((_item$code = item.code) !== null && _item$code !== void 0 && _item$code.label);\n    }).length) {\n      replace([{\n        sign: '+',\n        code: columnsToSum[0]\n      }]);\n    }\n  }, [replace, methods, calculationInputs.length, columnsToSum]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    mt: 2,\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: '#333',\n          display: 'flex',\n          gap: 1,\n          fontWeight: 600\n        },\n        variant: \"h3\",\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: Flexible_reporting_configuration + 'sum'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 1,\n        mt: 3,\n        sx: {\n          overflowX: 'auto'\n        },\n        pb: 2,\n        children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n          name: \"isCalculation\",\n          isControl: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 21\n        }, this), calculationInputs === null || calculationInputs === void 0 ? void 0 : calculationInputs.map((item, index) => /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 1.25,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            width: index !== 0 ? 60 : 0,\n            children: index !== 0 && /*#__PURE__*/_jsxDEV(Select, {\n              name: `calculationInputs.${index}.sign`,\n              selects: SIGN_CAlCULATE\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            width: 150,\n            children: /*#__PURE__*/_jsxDEV(Autocomplete, {\n              options: columnsToSum ? columnsToSum : [],\n              name: `calculationInputs.${index}.code`,\n              groupBy: option => option.typeCode,\n              isDefaultAll: true,\n              PopperComponent: props => /*#__PURE__*/_jsxDEV(Popper, {\n                ...props,\n                style: {\n                  width: 250\n                },\n                children: props.children\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 41\n              }, this),\n              isDisableClearable: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 29\n          }, this), index !== 0 && /*#__PURE__*/_jsxDEV(ButtonBase, {\n            onClick: () => remove(index),\n            children: /*#__PURE__*/_jsxDEV(HighlightOffOutlinedIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 33\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 25\n        }, this)), /*#__PURE__*/_jsxDEV(ButtonBase, {\n          onClick: handleAddColumnValue,\n          children: /*#__PURE__*/_jsxDEV(DataSaverOnOutlinedIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 9\n  }, this);\n};\n_s(Sum, \"IW+fYpZEmVXUuRYaKmhTr4f/h1s=\", false, function () {\n  return [useFormContext, useFieldArray];\n});\n_c = Sum;\nexport default Sum;\nvar _c;\n$RefreshReg$(_c, \"Sum\");", "map": {"version": 3, "names": ["useEffect", "HighlightOffOutlinedIcon", "DataSaverOnOutlinedIcon", "Box", "ButtonBase", "Divider", "<PERSON><PERSON>", "Typography", "useFieldArray", "useFormContext", "FormattedMessage", "Autocomplete", "Checkbox", "Select", "SIGN_CAlCULATE", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "Sum", "columnsToSum", "_s", "Flexible_reporting_configuration", "administration", "flexibleReport", "methods", "fields", "calculationInputs", "append", "remove", "replace", "control", "name", "handleAddColumnValue", "sign", "code", "getV<PERSON>ues", "length", "filter", "item", "_item$code", "label", "mt", "children", "sx", "color", "display", "gap", "fontWeight", "variant", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "overflowX", "pb", "isControl", "map", "index", "alignItems", "width", "selects", "options", "groupBy", "option", "typeCode", "isDefaultAll", "PopperComponent", "props", "style", "isDisableClearable", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/addorEditFlexibleReportConfig/Sum.tsx"], "sourcesContent": ["import { useEffect } from 'react';\nimport HighlightOffOutlinedIcon from '@mui/icons-material/HighlightOffOutlined';\nimport DataSaverOnOutlinedIcon from '@mui/icons-material/DataSaverOnOutlined';\nimport { Box, ButtonBase, Divider, Popper, Typography } from '@mui/material';\nimport { useFieldArray, useFormContext } from 'react-hook-form';\nimport { FormattedMessage } from 'react-intl';\n\nimport { Autocomplete, Checkbox, Select } from 'components/extended/Form';\nimport { SIGN_CAlCULATE, TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { IOption } from 'types';\ninterface ISumProps {\n    columnsToSum: IOption[];\n}\n\nconst Sum = ({ columnsToSum }: ISumProps) => {\n    const { Flexible_reporting_configuration } = TEXT_CONFIG_SCREEN.administration.flexibleReport;\n\n    const methods = useFormContext();\n\n    const {\n        fields: calculationInputs,\n        append,\n        remove,\n        replace\n    } = useFieldArray({\n        control: methods.control,\n        name: 'calculationInputs'\n    });\n\n    const handleAddColumnValue = () => {\n        append({\n            sign: '+',\n            code: columnsToSum ? columnsToSum[0] : {}\n        });\n    };\n\n    useEffect(() => {\n        const calculationInputs: { sign: string; code: IOption }[] = methods.getValues('calculationInputs');\n        if (\n            (!methods.getValues('id') && columnsToSum?.length && calculationInputs.length === 1) ||\n            (methods.getValues('id') && !methods.getValues('isCalculation')) ||\n            calculationInputs.filter((item: any) => !item.code?.label).length\n        ) {\n            replace([\n                {\n                    sign: '+',\n                    code: columnsToSum[0]\n                }\n            ]);\n        }\n    }, [replace, methods, calculationInputs.length, columnsToSum]);\n\n    return (\n        <Box mt={2}>\n            <Box>\n                <Typography sx={{ color: '#333', display: 'flex', gap: 1, fontWeight: 600 }} variant=\"h3\">\n                    <FormattedMessage id={Flexible_reporting_configuration + 'sum'} />\n                </Typography>\n                <Divider />\n\n                <Box display=\"flex\" gap={1} mt={3} sx={{ overflowX: 'auto' }} pb={2}>\n                    <Checkbox name=\"isCalculation\" isControl />\n                    {calculationInputs?.map((item, index) => (\n                        <Box key={item.id} display=\"flex\" gap={1.25} alignItems=\"center\">\n                            <Box width={index !== 0 ? 60 : 0}>\n                                {index !== 0 && <Select name={`calculationInputs.${index}.sign`} selects={SIGN_CAlCULATE} />}\n                            </Box>\n                            <Box width={150}>\n                                <Autocomplete\n                                    options={columnsToSum ? columnsToSum : []}\n                                    name={`calculationInputs.${index}.code`}\n                                    groupBy={(option: IOption) => option.typeCode}\n                                    isDefaultAll\n                                    PopperComponent={(props) => (\n                                        <Popper {...props} style={{ width: 250 }}>\n                                            {props.children}\n                                        </Popper>\n                                    )}\n                                    isDisableClearable\n                                />\n                            </Box>\n\n                            {index !== 0 && (\n                                <ButtonBase onClick={() => remove(index)}>\n                                    <HighlightOffOutlinedIcon />\n                                </ButtonBase>\n                            )}\n                        </Box>\n                    ))}\n                    <ButtonBase onClick={handleAddColumnValue}>\n                        <DataSaverOnOutlinedIcon />\n                    </ButtonBase>\n                </Box>\n            </Box>\n        </Box>\n    );\n};\n\nexport default Sum;\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,wBAAwB,MAAM,0CAA0C;AAC/E,OAAOC,uBAAuB,MAAM,yCAAyC;AAC7E,SAASC,GAAG,EAAEC,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,UAAU,QAAQ,eAAe;AAC5E,SAASC,aAAa,EAAEC,cAAc,QAAQ,iBAAiB;AAC/D,SAASC,gBAAgB,QAAQ,YAAY;AAE7C,SAASC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,0BAA0B;AACzE,SAASC,cAAc,EAAEC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMtE,MAAMC,GAAG,GAAGA,CAAC;EAAEC;AAAwB,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM;IAAEC;EAAiC,CAAC,GAAGN,kBAAkB,CAACO,cAAc,CAACC,cAAc;EAE7F,MAAMC,OAAO,GAAGf,cAAc,CAAC,CAAC;EAEhC,MAAM;IACFgB,MAAM,EAAEC,iBAAiB;IACzBC,MAAM;IACNC,MAAM;IACNC;EACJ,CAAC,GAAGrB,aAAa,CAAC;IACdsB,OAAO,EAAEN,OAAO,CAACM,OAAO;IACxBC,IAAI,EAAE;EACV,CAAC,CAAC;EAEF,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IAC/BL,MAAM,CAAC;MACHM,IAAI,EAAE,GAAG;MACTC,IAAI,EAAEf,YAAY,GAAGA,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC;IAC5C,CAAC,CAAC;EACN,CAAC;EAEDnB,SAAS,CAAC,MAAM;IACZ,MAAM0B,iBAAoD,GAAGF,OAAO,CAACW,SAAS,CAAC,mBAAmB,CAAC;IACnG,IACK,CAACX,OAAO,CAACW,SAAS,CAAC,IAAI,CAAC,IAAIhB,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEiB,MAAM,IAAIV,iBAAiB,CAACU,MAAM,KAAK,CAAC,IAClFZ,OAAO,CAACW,SAAS,CAAC,IAAI,CAAC,IAAI,CAACX,OAAO,CAACW,SAAS,CAAC,eAAe,CAAE,IAChET,iBAAiB,CAACW,MAAM,CAAEC,IAAS;MAAA,IAAAC,UAAA;MAAA,OAAK,GAAAA,UAAA,GAACD,IAAI,CAACJ,IAAI,cAAAK,UAAA,eAATA,UAAA,CAAWC,KAAK;IAAA,EAAC,CAACJ,MAAM,EACnE;MACEP,OAAO,CAAC,CACJ;QACII,IAAI,EAAE,GAAG;QACTC,IAAI,EAAEf,YAAY,CAAC,CAAC;MACxB,CAAC,CACJ,CAAC;IACN;EACJ,CAAC,EAAE,CAACU,OAAO,EAAEL,OAAO,EAAEE,iBAAiB,CAACU,MAAM,EAAEjB,YAAY,CAAC,CAAC;EAE9D,oBACIF,OAAA,CAACd,GAAG;IAACsC,EAAE,EAAE,CAAE;IAAAC,QAAA,eACPzB,OAAA,CAACd,GAAG;MAAAuC,QAAA,gBACAzB,OAAA,CAACV,UAAU;QAACoC,EAAE,EAAE;UAAEC,KAAK,EAAE,MAAM;UAAEC,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAI,CAAE;QAACC,OAAO,EAAC,IAAI;QAAAN,QAAA,eACrFzB,OAAA,CAACP,gBAAgB;UAACuC,EAAE,EAAE5B,gCAAgC,GAAG;QAAM;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACbpC,OAAA,CAACZ,OAAO;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEXpC,OAAA,CAACd,GAAG;QAAC0C,OAAO,EAAC,MAAM;QAACC,GAAG,EAAE,CAAE;QAACL,EAAE,EAAE,CAAE;QAACE,EAAE,EAAE;UAAEW,SAAS,EAAE;QAAO,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAb,QAAA,gBAChEzB,OAAA,CAACL,QAAQ;UAACmB,IAAI,EAAC,eAAe;UAACyB,SAAS;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC1C3B,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE+B,GAAG,CAAC,CAACnB,IAAI,EAAEoB,KAAK,kBAChCzC,OAAA,CAACd,GAAG;UAAe0C,OAAO,EAAC,MAAM;UAACC,GAAG,EAAE,IAAK;UAACa,UAAU,EAAC,QAAQ;UAAAjB,QAAA,gBAC5DzB,OAAA,CAACd,GAAG;YAACyD,KAAK,EAAEF,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,CAAE;YAAAhB,QAAA,EAC5BgB,KAAK,KAAK,CAAC,iBAAIzC,OAAA,CAACJ,MAAM;cAACkB,IAAI,EAAE,qBAAqB2B,KAAK,OAAQ;cAACG,OAAO,EAAE/C;YAAe;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,eACNpC,OAAA,CAACd,GAAG;YAACyD,KAAK,EAAE,GAAI;YAAAlB,QAAA,eACZzB,OAAA,CAACN,YAAY;cACTmD,OAAO,EAAE3C,YAAY,GAAGA,YAAY,GAAG,EAAG;cAC1CY,IAAI,EAAE,qBAAqB2B,KAAK,OAAQ;cACxCK,OAAO,EAAGC,MAAe,IAAKA,MAAM,CAACC,QAAS;cAC9CC,YAAY;cACZC,eAAe,EAAGC,KAAK,iBACnBnD,OAAA,CAACX,MAAM;gBAAA,GAAK8D,KAAK;gBAAEC,KAAK,EAAE;kBAAET,KAAK,EAAE;gBAAI,CAAE;gBAAAlB,QAAA,EACpC0B,KAAK,CAAC1B;cAAQ;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CACV;cACFiB,kBAAkB;YAAA;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EAELK,KAAK,KAAK,CAAC,iBACRzC,OAAA,CAACb,UAAU;YAACmE,OAAO,EAAEA,CAAA,KAAM3C,MAAM,CAAC8B,KAAK,CAAE;YAAAhB,QAAA,eACrCzB,OAAA,CAAChB,wBAAwB;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CACf;QAAA,GAvBKf,IAAI,CAACW,EAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwBZ,CACR,CAAC,eACFpC,OAAA,CAACb,UAAU;UAACmE,OAAO,EAAEvC,oBAAqB;UAAAU,QAAA,eACtCzB,OAAA,CAACf,uBAAuB;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACjC,EAAA,CAlFIF,GAAG;EAAA,QAGWT,cAAc,EAO1BD,aAAa;AAAA;AAAAgE,EAAA,GAVftD,GAAG;AAoFT,eAAeA,GAAG;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}