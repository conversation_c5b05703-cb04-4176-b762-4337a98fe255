{"ast": null, "code": "import { getPickersLocalization } from './utils/getPickersLocalization';\nconst views = {\n  // maps TimeView to its translation\n  hours: 'гадзіны',\n  minutes: 'хвіліны',\n  seconds: 'секунды',\n  // maps PickersToolbar[\"viewType\"] to its translation\n  calendar: 'календара',\n  clock: 'часу'\n};\nconst beBYPickers = {\n  // Calendar navigation\n  previousMonth: 'Папярэдні месяц',\n  nextMonth: 'Наступны месяц',\n  // View navigation\n  openPreviousView: 'адкрыць папярэдні выгляд',\n  openNextView: 'адкрыць наступны выгляд',\n  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'гадавы выгляд адкрыты, перайсці да каляндарнага выгляду' : 'каляндарны выгляд адкрыты, перайсці да гадавога выгляду',\n  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `тэкставае поле адкрыта, перайсці да выгляду ${views[viewType]}` : `Выгляд ${views[viewType]} зараз адкрыты, перайсці да тэкставага поля`,\n  // DateRange placeholders\n  start: 'Пачатак',\n  end: 'Канец',\n  // Action bar\n  cancelButtonLabel: 'Адмена',\n  clearButtonLabel: 'Ачысціць',\n  okButtonLabel: 'OK',\n  todayButtonLabel: 'Сёння',\n  // Toolbar titles\n  datePickerDefaultToolbarTitle: 'Абраць дату',\n  dateTimePickerDefaultToolbarTitle: 'Абраць дату і час',\n  timePickerDefaultToolbarTitle: 'Абраць час',\n  dateRangePickerDefaultToolbarTitle: 'Абраць каляндарны перыяд',\n  // Clock labels\n  clockLabelText: (view, time, adapter) => `Абярыце ${views[view]}. ${time === null ? 'Час не абраны' : `Абраны час ${adapter.format(time, 'fullTime')}`}`,\n  hoursClockNumberText: hours => `${hours} гадзін`,\n  minutesClockNumberText: minutes => `${minutes} хвілін`,\n  secondsClockNumberText: seconds => `${seconds} секунд`,\n  // Open picker labels\n  openDatePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Абраць дату, абрана дата  ${utils.format(value, 'fullDate')}` : 'Абраць дату',\n  openTimePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Абраць час, абрыны час  ${utils.format(value, 'fullTime')}` : 'Абраць час',\n  // Table labels\n  timeTableLabel: 'абраць час',\n  dateTableLabel: 'абраць дату'\n};\nexport const beBY = getPickersLocalization(beBYPickers);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}