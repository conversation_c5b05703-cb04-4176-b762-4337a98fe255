{"ast": null, "code": "var baseGet = require('./_baseGet'),\n  baseSlice = require('./_baseSlice');\n\n/**\n * Gets the parent value at `path` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} path The path to get the parent value of.\n * @returns {*} Returns the parent value.\n */\nfunction parent(object, path) {\n  return path.length < 2 ? object : baseGet(object, baseSlice(path, 0, -1));\n}\nmodule.exports = parent;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}