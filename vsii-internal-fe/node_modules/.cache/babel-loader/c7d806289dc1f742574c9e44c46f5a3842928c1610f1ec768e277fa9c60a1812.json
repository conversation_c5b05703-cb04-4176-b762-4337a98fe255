{"ast": null, "code": "import { isFirefox, isSafari } from './BrowserDetector.js';\nimport { MonotonicInterpolant } from './MonotonicInterpolant.js';\nconst ELEMENT_NODE = 1;\nexport function getNodeClientOffset(node) {\n  const el = node.nodeType === ELEMENT_NODE ? node : node.parentElement;\n  if (!el) {\n    return null;\n  }\n  const {\n    top,\n    left\n  } = el.getBoundingClientRect();\n  return {\n    x: left,\n    y: top\n  };\n}\nexport function getEventClientOffset(e) {\n  return {\n    x: e.clientX,\n    y: e.clientY\n  };\n}\nfunction isImageNode(node) {\n  var ref;\n  return node.nodeName === 'IMG' && (isFirefox() || !((ref = document.documentElement) === null || ref === void 0 ? void 0 : ref.contains(node)));\n}\nfunction getDragPreviewSize(isImage, dragPreview, sourceWidth, sourceHeight) {\n  let dragPreviewWidth = isImage ? dragPreview.width : sourceWidth;\n  let dragPreviewHeight = isImage ? dragPreview.height : sourceHeight;\n  // Work around @2x coordinate discrepancies in browsers\n  if (isSafari() && isImage) {\n    dragPreviewHeight /= window.devicePixelRatio;\n    dragPreviewWidth /= window.devicePixelRatio;\n  }\n  return {\n    dragPreviewWidth,\n    dragPreviewHeight\n  };\n}\nexport function getDragPreviewOffset(sourceNode, dragPreview, clientOffset, anchorPoint, offsetPoint) {\n  // The browsers will use the image intrinsic size under different conditions.\n  // Firefox only cares if it's an image, but WebKit also wants it to be detached.\n  const isImage = isImageNode(dragPreview);\n  const dragPreviewNode = isImage ? sourceNode : dragPreview;\n  const dragPreviewNodeOffsetFromClient = getNodeClientOffset(dragPreviewNode);\n  const offsetFromDragPreview = {\n    x: clientOffset.x - dragPreviewNodeOffsetFromClient.x,\n    y: clientOffset.y - dragPreviewNodeOffsetFromClient.y\n  };\n  const {\n    offsetWidth: sourceWidth,\n    offsetHeight: sourceHeight\n  } = sourceNode;\n  const {\n    anchorX,\n    anchorY\n  } = anchorPoint;\n  const {\n    dragPreviewWidth,\n    dragPreviewHeight\n  } = getDragPreviewSize(isImage, dragPreview, sourceWidth, sourceHeight);\n  const calculateYOffset = () => {\n    const interpolantY = new MonotonicInterpolant([0, 0.5, 1], [\n    // Dock to the top\n    offsetFromDragPreview.y,\n    // Align at the center\n    offsetFromDragPreview.y / sourceHeight * dragPreviewHeight,\n    // Dock to the bottom\n    offsetFromDragPreview.y + dragPreviewHeight - sourceHeight]);\n    let y = interpolantY.interpolate(anchorY);\n    // Work around Safari 8 positioning bug\n    if (isSafari() && isImage) {\n      // We'll have to wait for @3x to see if this is entirely correct\n      y += (window.devicePixelRatio - 1) * dragPreviewHeight;\n    }\n    return y;\n  };\n  const calculateXOffset = () => {\n    // Interpolate coordinates depending on anchor point\n    // If you know a simpler way to do this, let me know\n    const interpolantX = new MonotonicInterpolant([0, 0.5, 1], [\n    // Dock to the left\n    offsetFromDragPreview.x,\n    // Align at the center\n    offsetFromDragPreview.x / sourceWidth * dragPreviewWidth,\n    // Dock to the right\n    offsetFromDragPreview.x + dragPreviewWidth - sourceWidth]);\n    return interpolantX.interpolate(anchorX);\n  };\n  // Force offsets if specified in the options.\n  const {\n    offsetX,\n    offsetY\n  } = offsetPoint;\n  const isManualOffsetX = offsetX === 0 || offsetX;\n  const isManualOffsetY = offsetY === 0 || offsetY;\n  return {\n    x: isManualOffsetX ? offsetX : calculateXOffset(),\n    y: isManualOffsetY ? offsetY : calculateYOffset()\n  };\n}\n\n//# sourceMappingURL=OffsetUtils.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}