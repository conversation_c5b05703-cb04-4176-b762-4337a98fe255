{"ast": null, "code": "import { useContext, useRef } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { useVisualElementContext } from '../../context/MotionContext/index.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\nimport { LazyContext } from '../../context/LazyContext.mjs';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nfunction useVisualElement(Component, visualState, props, createVisualElement) {\n  const parent = useVisualElementContext();\n  const lazyContext = useContext(LazyContext);\n  const presenceContext = useContext(PresenceContext);\n  const reducedMotionConfig = useContext(MotionConfigContext).reducedMotion;\n  const visualElementRef = useRef();\n  /**\n   * If we haven't preloaded a renderer, check to see if we have one lazy-loaded\n   */\n  createVisualElement = createVisualElement || lazyContext.renderer;\n  if (!visualElementRef.current && createVisualElement) {\n    visualElementRef.current = createVisualElement(Component, {\n      visualState,\n      parent,\n      props,\n      presenceId: presenceContext ? presenceContext.id : undefined,\n      blockInitialAnimation: presenceContext ? presenceContext.initial === false : false,\n      reducedMotionConfig\n    });\n  }\n  const visualElement = visualElementRef.current;\n  useIsomorphicLayoutEffect(() => {\n    visualElement && visualElement.render();\n  });\n  /**\n   * If we have optimised appear animations to handoff from, trigger animateChanges\n   * from a synchronous useLayoutEffect to ensure there's no flash of incorrectly\n   * styled component in the event of a hydration error.\n   */\n  useIsomorphicLayoutEffect(() => {\n    if (visualElement && visualElement.animationState) {\n      visualElement.animationState.animateChanges();\n    }\n  });\n  useIsomorphicLayoutEffect(() => () => visualElement && visualElement.notify(\"Unmount\"), []);\n  return visualElement;\n}\nexport { useVisualElement };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}