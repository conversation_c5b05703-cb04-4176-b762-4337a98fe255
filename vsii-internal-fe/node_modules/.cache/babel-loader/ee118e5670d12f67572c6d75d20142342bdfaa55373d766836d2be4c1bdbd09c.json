{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport useEnhancedEffect from '../useEnhancedEffect';\n\n/**\n * Inspired by https://github.com/facebook/react/issues/14099#issuecomment-440013892\n * See RFC in https://github.com/reactjs/rfcs/pull/220\n */\n\nfunction useEventCallback(fn) {\n  const ref = React.useRef(fn);\n  useEnhancedEffect(() => {\n    ref.current = fn;\n  });\n  return React.useRef(function () {\n    return (\n      // @ts-expect-error hide `this`\n      (0, ref.current)(...arguments)\n    );\n  }).current;\n}\nexport default useEventCallback;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}