{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardMediaUtilityClass(slot) {\n  return generateUtilityClass('MuiCardMedia', slot);\n}\nconst cardMediaClasses = generateUtilityClasses('MuiCardMedia', ['root', 'media', 'img']);\nexport default cardMediaClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}