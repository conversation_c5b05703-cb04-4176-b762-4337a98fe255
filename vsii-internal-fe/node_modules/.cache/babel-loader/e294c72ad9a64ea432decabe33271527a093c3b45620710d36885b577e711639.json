{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\n\n// types\n\nconst initialState = {\n  action: false,\n  open: false,\n  isMultipleLanguage: true,\n  message: 'Note archived',\n  anchorOrigin: {\n    vertical: 'bottom',\n    horizontal: 'right'\n  },\n  variant: 'default',\n  alert: {\n    color: 'info',\n    variant: 'filled'\n  },\n  transition: 'Fade',\n  close: true,\n  actionButton: false,\n  duration: 6000\n};\n\n// ==============================|| SLICE - SNACKBAR ||============================== //\n\nconst snackbarSlice = createSlice({\n  name: 'snackbar',\n  initialState,\n  reducers: {\n    openSnackbar(state, action) {\n      const {\n        open,\n        message,\n        anchorOrigin,\n        variant,\n        alert,\n        transition,\n        close,\n        actionButton,\n        isMultipleLanguage,\n        duration\n      } = action.payload;\n      state.action = !state.action;\n      state.open = open || initialState.open;\n      state.message = message ? message : initialState.message;\n      state.isMultipleLanguage = isMultipleLanguage ? isMultipleLanguage : initialState.isMultipleLanguage;\n      state.anchorOrigin = anchorOrigin || initialState.anchorOrigin;\n      state.variant = variant || initialState.variant;\n      state.alert = {\n        color: (alert === null || alert === void 0 ? void 0 : alert.color) || initialState.alert.color,\n        variant: (alert === null || alert === void 0 ? void 0 : alert.variant) || initialState.alert.variant\n      };\n      state.transition = transition || initialState.transition;\n      state.close = close === false ? close : initialState.close;\n      state.actionButton = actionButton || initialState.actionButton;\n      state.duration = duration || initialState.duration;\n    },\n    closeSnackbar(state) {\n      state.open = false;\n    }\n  }\n});\nexport default snackbarSlice.reducer;\nexport const {\n  closeSnackbar,\n  openSnackbar\n} = snackbarSlice.actions;", "map": {"version": 3, "names": ["createSlice", "initialState", "action", "open", "isMultipleLanguage", "message", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "variant", "alert", "color", "transition", "close", "actionButton", "duration", "snackbarSlice", "name", "reducers", "openSnackbar", "state", "payload", "closeSnackbar", "reducer", "actions"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/store/slice/snackbarSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\n\n// types\nimport { SnackbarProps } from 'types';\n\nconst initialState: SnackbarProps = {\n    action: false,\n    open: false,\n    isMultipleLanguage: true,\n    message: 'Note archived',\n    anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n    },\n    variant: 'default',\n    alert: {\n        color: 'info',\n        variant: 'filled'\n    },\n    transition: 'Fade',\n    close: true,\n    actionButton: false,\n    duration: 6000\n};\n\n// ==============================|| SLICE - SNACKBAR ||============================== //\n\nconst snackbarSlice = createSlice({\n    name: 'snackbar',\n    initialState,\n    reducers: {\n        openSnackbar(state, action: PayloadAction<Partial<SnackbarProps>>) {\n            const { open, message, anchorOrigin, variant, alert, transition, close, actionButton, isMultipleLanguage, duration } =\n                action.payload;\n            state.action = !state.action;\n            state.open = open || initialState.open;\n            state.message = message ? message : initialState.message;\n            state.isMultipleLanguage = isMultipleLanguage ? isMultipleLanguage : initialState.isMultipleLanguage;\n            state.anchorOrigin = anchorOrigin || initialState.anchorOrigin;\n            state.variant = variant || initialState.variant;\n            state.alert = {\n                color: alert?.color || initialState.alert.color,\n                variant: alert?.variant || initialState.alert.variant\n            };\n            state.transition = transition || initialState.transition;\n            state.close = close === false ? close : initialState.close;\n            state.actionButton = actionButton || initialState.actionButton;\n            state.duration = duration || initialState.duration;\n        },\n\n        closeSnackbar(state) {\n            state.open = false;\n        }\n    }\n});\n\nexport default snackbarSlice.reducer;\n\nexport const { closeSnackbar, openSnackbar } = snackbarSlice.actions;\n"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;;AAE7D;;AAGA,MAAMC,YAA2B,GAAG;EAChCC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,KAAK;EACXC,kBAAkB,EAAE,IAAI;EACxBC,OAAO,EAAE,eAAe;EACxBC,YAAY,EAAE;IACVC,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE;EAChB,CAAC;EACDC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE;IACHC,KAAK,EAAE,MAAM;IACbF,OAAO,EAAE;EACb,CAAC;EACDG,UAAU,EAAE,MAAM;EAClBC,KAAK,EAAE,IAAI;EACXC,YAAY,EAAE,KAAK;EACnBC,QAAQ,EAAE;AACd,CAAC;;AAED;;AAEA,MAAMC,aAAa,GAAGhB,WAAW,CAAC;EAC9BiB,IAAI,EAAE,UAAU;EAChBhB,YAAY;EACZiB,QAAQ,EAAE;IACNC,YAAYA,CAACC,KAAK,EAAElB,MAA6C,EAAE;MAC/D,MAAM;QAAEC,IAAI;QAAEE,OAAO;QAAEC,YAAY;QAAEG,OAAO;QAAEC,KAAK;QAAEE,UAAU;QAAEC,KAAK;QAAEC,YAAY;QAAEV,kBAAkB;QAAEW;MAAS,CAAC,GAChHb,MAAM,CAACmB,OAAO;MAClBD,KAAK,CAAClB,MAAM,GAAG,CAACkB,KAAK,CAAClB,MAAM;MAC5BkB,KAAK,CAACjB,IAAI,GAAGA,IAAI,IAAIF,YAAY,CAACE,IAAI;MACtCiB,KAAK,CAACf,OAAO,GAAGA,OAAO,GAAGA,OAAO,GAAGJ,YAAY,CAACI,OAAO;MACxDe,KAAK,CAAChB,kBAAkB,GAAGA,kBAAkB,GAAGA,kBAAkB,GAAGH,YAAY,CAACG,kBAAkB;MACpGgB,KAAK,CAACd,YAAY,GAAGA,YAAY,IAAIL,YAAY,CAACK,YAAY;MAC9Dc,KAAK,CAACX,OAAO,GAAGA,OAAO,IAAIR,YAAY,CAACQ,OAAO;MAC/CW,KAAK,CAACV,KAAK,GAAG;QACVC,KAAK,EAAE,CAAAD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,KAAK,KAAIV,YAAY,CAACS,KAAK,CAACC,KAAK;QAC/CF,OAAO,EAAE,CAAAC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAED,OAAO,KAAIR,YAAY,CAACS,KAAK,CAACD;MAClD,CAAC;MACDW,KAAK,CAACR,UAAU,GAAGA,UAAU,IAAIX,YAAY,CAACW,UAAU;MACxDQ,KAAK,CAACP,KAAK,GAAGA,KAAK,KAAK,KAAK,GAAGA,KAAK,GAAGZ,YAAY,CAACY,KAAK;MAC1DO,KAAK,CAACN,YAAY,GAAGA,YAAY,IAAIb,YAAY,CAACa,YAAY;MAC9DM,KAAK,CAACL,QAAQ,GAAGA,QAAQ,IAAId,YAAY,CAACc,QAAQ;IACtD,CAAC;IAEDO,aAAaA,CAACF,KAAK,EAAE;MACjBA,KAAK,CAACjB,IAAI,GAAG,KAAK;IACtB;EACJ;AACJ,CAAC,CAAC;AAEF,eAAea,aAAa,CAACO,OAAO;AAEpC,OAAO,MAAM;EAAED,aAAa;EAAEH;AAAa,CAAC,GAAGH,aAAa,CAACQ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}