{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport function getStaticWrapperUtilityClass(slot) {\n  return generateUtilityClass('MuiPickerStaticWrapper', slot);\n}\nexport const pickerStaticWrapperClasses = generateUtilityClasses('MuiPickerStaticWrapper', ['root', 'content']);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}