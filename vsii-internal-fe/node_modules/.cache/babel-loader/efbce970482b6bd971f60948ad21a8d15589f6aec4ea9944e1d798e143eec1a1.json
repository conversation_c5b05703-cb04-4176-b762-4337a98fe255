{"ast": null, "code": "var ActionTypes; // split declaration and export due to https://github.com/codesandbox/codesandbox-client/issues/6435\n\n(function (ActionTypes) {\n  ActionTypes[\"blur\"] = \"blur\";\n  ActionTypes[\"focus\"] = \"focus\";\n  ActionTypes[\"keyDown\"] = \"keyDown\";\n  ActionTypes[\"optionClick\"] = \"optionClick\";\n  ActionTypes[\"optionHover\"] = \"optionHover\";\n  ActionTypes[\"optionsChange\"] = \"optionsChange\";\n  ActionTypes[\"setValue\"] = \"setValue\";\n  ActionTypes[\"setHighlight\"] = \"setHighlight\";\n  ActionTypes[\"textNavigation\"] = \"textNagivation\";\n})(ActionTypes || (ActionTypes = {}));\nexport { ActionTypes };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}