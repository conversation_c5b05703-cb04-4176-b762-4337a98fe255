{"ast": null, "code": "// material-ui\nimport{TableCell,TableRow,Typography}from'@mui/material';//project import\nimport ProductReportStatusChip from'components/extended/ProductReportStatusChip';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const BacklogTbody=props=>{var _data$effort;const{data,index,setBacklogDetail}=props;return/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{sx:{width:'2%',px:'3px'},children:index+1}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'35%',px:'3px'},children:/*#__PURE__*/_jsx(Typography,{sx:{':hover':{color:'#3163D4'},cursor:'pointer'},onClick:()=>setBacklogDetail(data),children:data.requireName})}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'35%',px:'3px'},children:data.sprintName}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'14%',px:0},children:/*#__PURE__*/_jsx(ProductReportStatusChip,{status:data.status})}),/*#__PURE__*/_jsx(TableCell,{sx:{width:'14%',px:'3px'},children:(_data$effort=data.effort)===null||_data$effort===void 0?void 0:_data$effort.toFixed(2)})]});};export default BacklogTbody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}