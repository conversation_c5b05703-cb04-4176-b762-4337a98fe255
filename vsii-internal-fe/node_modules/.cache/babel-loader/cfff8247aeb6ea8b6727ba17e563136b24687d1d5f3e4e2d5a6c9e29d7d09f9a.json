{"ast": null, "code": "/**\n * Use invariant() to assert state which your program assumes to be true.\n *\n * Provide sprintf-style format (only %s is supported) and arguments\n * to provide information about what broke and what you were\n * expecting.\n *\n * The invariant message will be stripped in production, but the invariant\n * will remain to ensure logic does not differ in production.\n */export function invariant(condition, format, ...args) {\n  if (isProduction()) {\n    if (format === undefined) {\n      throw new Error('invariant requires an error message argument');\n    }\n  }\n  if (!condition) {\n    let error;\n    if (format === undefined) {\n      error = new Error('Minified exception occurred; use the non-minified dev environment ' + 'for the full error message and additional helpful warnings.');\n    } else {\n      let argIndex = 0;\n      error = new Error(format.replace(/%s/g, function () {\n        return args[argIndex++];\n      }));\n      error.name = 'Invariant Violation';\n    }\n    error.framesToPop = 1 // we don't care about invariant's own frame\n    ;\n    throw error;\n  }\n}\nfunction isProduction() {\n  return typeof process !== 'undefined' && process.env['NODE_ENV'] === 'production';\n}\n\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}