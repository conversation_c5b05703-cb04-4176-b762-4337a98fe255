{"ast": null, "code": ";\n(function (root, factory) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var WordArray = C_lib.WordArray;\n    var Hasher = C_lib.Hasher;\n    var C_algo = C.algo;\n\n    // Reusable object\n    var W = [];\n\n    /**\n     * SHA-1 hash algorithm.\n     */\n    var SHA1 = C_algo.SHA1 = Hasher.extend({\n      _doReset: function () {\n        this._hash = new WordArray.init([0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0]);\n      },\n      _doProcessBlock: function (M, offset) {\n        // Shortcut\n        var H = this._hash.words;\n\n        // Working variables\n        var a = H[0];\n        var b = H[1];\n        var c = H[2];\n        var d = H[3];\n        var e = H[4];\n\n        // Computation\n        for (var i = 0; i < 80; i++) {\n          if (i < 16) {\n            W[i] = M[offset + i] | 0;\n          } else {\n            var n = W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16];\n            W[i] = n << 1 | n >>> 31;\n          }\n          var t = (a << 5 | a >>> 27) + e + W[i];\n          if (i < 20) {\n            t += (b & c | ~b & d) + 0x5a827999;\n          } else if (i < 40) {\n            t += (b ^ c ^ d) + 0x6ed9eba1;\n          } else if (i < 60) {\n            t += (b & c | b & d | c & d) - 0x70e44324;\n          } else /* if (i < 80) */{\n              t += (b ^ c ^ d) - 0x359d3e2a;\n            }\n          e = d;\n          d = c;\n          c = b << 30 | b >>> 2;\n          b = a;\n          a = t;\n        }\n\n        // Intermediate hash value\n        H[0] = H[0] + a | 0;\n        H[1] = H[1] + b | 0;\n        H[2] = H[2] + c | 0;\n        H[3] = H[3] + d | 0;\n        H[4] = H[4] + e | 0;\n      },\n      _doFinalize: function () {\n        // Shortcuts\n        var data = this._data;\n        var dataWords = data.words;\n        var nBitsTotal = this._nDataBytes * 8;\n        var nBitsLeft = data.sigBytes * 8;\n\n        // Add padding\n        dataWords[nBitsLeft >>> 5] |= 0x80 << 24 - nBitsLeft % 32;\n        dataWords[(nBitsLeft + 64 >>> 9 << 4) + 14] = Math.floor(nBitsTotal / 0x100000000);\n        dataWords[(nBitsLeft + 64 >>> 9 << 4) + 15] = nBitsTotal;\n        data.sigBytes = dataWords.length * 4;\n\n        // Hash final blocks\n        this._process();\n\n        // Return final computed hash\n        return this._hash;\n      },\n      clone: function () {\n        var clone = Hasher.clone.call(this);\n        clone._hash = this._hash.clone();\n        return clone;\n      }\n    });\n\n    /**\n     * Shortcut function to the hasher's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     *\n     * @return {WordArray} The hash.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hash = CryptoJS.SHA1('message');\n     *     var hash = CryptoJS.SHA1(wordArray);\n     */\n    C.SHA1 = Hasher._createHelper(SHA1);\n\n    /**\n     * Shortcut function to the HMAC's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     * @param {WordArray|string} key The secret key.\n     *\n     * @return {WordArray} The HMAC.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hmac = CryptoJS.HmacSHA1(message, key);\n     */\n    C.HmacSHA1 = Hasher._createHmacHelper(SHA1);\n  })();\n  return CryptoJS.SHA1;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}