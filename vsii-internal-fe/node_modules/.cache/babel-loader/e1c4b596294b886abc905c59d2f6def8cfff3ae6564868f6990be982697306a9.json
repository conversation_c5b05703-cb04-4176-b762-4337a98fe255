{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"aria-describedby\", \"aria-label\", \"aria-labelledby\", \"autoComplete\", \"autoFocus\", \"className\", \"component\", \"components\", \"componentsProps\", \"defaultValue\", \"disabled\", \"endAdornment\", \"error\", \"id\", \"multiline\", \"name\", \"onClick\", \"onChange\", \"onKeyDown\", \"onKeyUp\", \"onFocus\", \"onBlur\", \"placeholder\", \"readOnly\", \"required\", \"startAdornment\", \"value\", \"type\", \"rows\", \"minRows\", \"maxRows\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport isHostComponent from '../utils/isHostComponent';\nimport classes from './inputUnstyledClasses';\nimport useInput from './useInput';\nimport { useSlotProps } from '../utils';\n/**\n *\n * Demos:\n *\n * - [Unstyled input](https://mui.com/base/react-input/)\n *\n * API:\n *\n * - [InputUnstyled API](https://mui.com/base/api/input-unstyled/)\n */\n\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst InputUnstyled = /*#__PURE__*/React.forwardRef(function InputUnstyled(props, forwardedRef) {\n  var _ref, _components$Textarea, _components$Input;\n  const {\n      'aria-describedby': ariaDescribedby,\n      'aria-label': ariaLabel,\n      'aria-labelledby': ariaLabelledby,\n      autoComplete,\n      autoFocus,\n      className,\n      component,\n      components = {},\n      componentsProps = {},\n      defaultValue,\n      disabled,\n      endAdornment,\n      error,\n      id,\n      multiline = false,\n      name,\n      onClick,\n      onChange,\n      onKeyDown,\n      onKeyUp,\n      onFocus,\n      onBlur,\n      placeholder,\n      readOnly,\n      required,\n      startAdornment,\n      value,\n      type: typeProp,\n      rows,\n      minRows,\n      maxRows\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    getRootProps,\n    getInputProps,\n    focused,\n    formControlContext,\n    error: errorState,\n    disabled: disabledState\n  } = useInput({\n    disabled,\n    defaultValue,\n    error,\n    onBlur,\n    onClick,\n    onChange,\n    onFocus,\n    required,\n    value\n  });\n  const type = !multiline ? typeProp != null ? typeProp : 'text' : undefined;\n  const ownerState = _extends({}, props, {\n    disabled: disabledState,\n    error: errorState,\n    focused,\n    formControlContext,\n    multiline,\n    type\n  });\n  const rootStateClasses = {\n    [classes.disabled]: disabledState,\n    [classes.error]: errorState,\n    [classes.focused]: focused,\n    [classes.formControl]: Boolean(formControlContext),\n    [classes.multiline]: multiline,\n    [classes.adornedStart]: Boolean(startAdornment),\n    [classes.adornedEnd]: Boolean(endAdornment)\n  };\n  const inputStateClasses = {\n    [classes.disabled]: disabledState,\n    [classes.multiline]: multiline\n  };\n  const propsToForward = {\n    'aria-describedby': ariaDescribedby,\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledby,\n    autoComplete,\n    autoFocus,\n    id,\n    onKeyDown,\n    onKeyUp,\n    name,\n    placeholder,\n    readOnly,\n    type\n  };\n  const Root = (_ref = component != null ? component : components.Root) != null ? _ref : 'div';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    getSlotProps: getRootProps,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref: forwardedRef\n    },\n    ownerState,\n    className: [classes.root, rootStateClasses, className]\n  });\n  const Input = multiline ? (_components$Textarea = components.Textarea) != null ? _components$Textarea : 'textarea' : (_components$Input = components.Input) != null ? _components$Input : 'input';\n  const inputProps = useSlotProps({\n    elementType: Input,\n    getSlotProps: otherHandlers => getInputProps(_extends({}, otherHandlers, propsToForward)),\n    externalSlotProps: componentsProps.input,\n    additionalProps: _extends({\n      rows: multiline ? rows : undefined\n    }, multiline && !isHostComponent(Input) && {\n      minRows: rows || minRows,\n      maxRows: rows || maxRows\n    }),\n    ownerState,\n    className: [classes.input, inputStateClasses]\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    if (multiline) {\n      if (rows) {\n        if (minRows || maxRows) {\n          console.warn('MUI: You can not use the `minRows` or `maxRows` props when the input `rows` prop is set.');\n        }\n      }\n    }\n  }\n  return /*#__PURE__*/_jsxs(Root, _extends({}, rootProps, {\n    children: [startAdornment, /*#__PURE__*/_jsx(Input, _extends({}, inputProps)), endAdornment]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? InputUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * @ignore\n   */\n  'aria-describedby': PropTypes.string,\n  /**\n   * @ignore\n   */\n  'aria-label': PropTypes.string,\n  /**\n   * @ignore\n   */\n  'aria-labelledby': PropTypes.string,\n  /**\n   * This prop helps users to fill forms faster, especially on mobile devices.\n   * The name can be confusing, as it's more like an autofill.\n   * You can learn more about it [following the specification](https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#autofill).\n   */\n  autoComplete: PropTypes.string,\n  /**\n   * If `true`, the `input` element is focused during the first mount.\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Class name applied to the root element.\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the InputBase.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Input: PropTypes.elementType,\n    Root: PropTypes.elementType,\n    Textarea: PropTypes.elementType\n  }),\n  /**\n   * The props used for each slot inside the Input.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The default value. Use when the component is not controlled.\n   */\n  defaultValue: PropTypes.any,\n  /**\n   * If `true`, the component is disabled.\n   * The prop defaults to the value (`false`) inherited from the parent FormControl component.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Trailing adornment for this input.\n   */\n  endAdornment: PropTypes.node,\n  /**\n   * If `true`, the `input` will indicate an error.\n   * The prop defaults to the value (`false`) inherited from the parent FormControl component.\n   */\n  error: PropTypes.bool,\n  /**\n   * The id of the `input` element.\n   */\n  id: PropTypes.string,\n  /**\n   * Maximum number of rows to display when multiline option is set to true.\n   */\n  maxRows: PropTypes.number,\n  /**\n   * Minimum number of rows to display when multiline option is set to true.\n   */\n  minRows: PropTypes.number,\n  /**\n   * If `true`, a `textarea` element is rendered.\n   * @default false\n   */\n  multiline: PropTypes.bool,\n  /**\n   * Name attribute of the `input` element.\n   */\n  name: PropTypes.string,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyUp: PropTypes.func,\n  /**\n   * The short hint displayed in the `input` before the user enters a value.\n   */\n  placeholder: PropTypes.string,\n  /**\n   * It prevents the user from changing the value of the field\n   * (not from interacting with the field).\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, the `input` element is required.\n   * The prop defaults to the value (`false`) inherited from the parent FormControl component.\n   */\n  required: PropTypes.bool,\n  /**\n   * Number of rows to display when multiline option is set to true.\n   */\n  rows: PropTypes.number,\n  /**\n   * Leading adornment for this input.\n   */\n  startAdornment: PropTypes.node,\n  /**\n   * Type of the `input` element. It should be [a valid HTML5 input type](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#Form_%3Cinput%3E_types).\n   * @default 'text'\n   */\n  type: PropTypes\n  /* @typescript-to-proptypes-ignore */.oneOf(['button', 'checkbox', 'color', 'date', 'datetime-local', 'email', 'file', 'hidden', 'image', 'month', 'number', 'password', 'radio', 'range', 'reset', 'search', 'submit', 'tel', 'text', 'time', 'url', 'week']),\n  /**\n   * The value of the `input` element, required for a controlled component.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default InputUnstyled;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}