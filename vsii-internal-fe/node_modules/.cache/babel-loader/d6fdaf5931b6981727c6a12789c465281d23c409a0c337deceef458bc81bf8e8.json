{"ast": null, "code": "// mui\nimport{<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,TableCell,TableRow,Tooltip}from'@mui/material';import{useTheme}from'@mui/material/styles';//projec import\nimport{DatePicker,Input,NumericFormatCustom}from'components/extended/Form';import{MONEY_PLACEHOLDER}from'constants/Common';import{DeleteTwoToneIcon}from'assets/images/icons';import{FormattedMessage}from'react-intl';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const RankCostHistoryTBody=props=>{const{index,remove,count}=props;const theme=useTheme();return/*#__PURE__*/_jsxs(TableRow,{sx:{'& .MuiFormControl-root':{height:'50px'},'&.MuiTableRow-root':{'& td':{borderColor:'transparent'},'& .MuiInputBase-input':{[theme.breakpoints.down('sm')]:{width:'unset !important'}}}},children:[/*#__PURE__*/_jsx(TableCell,{sx:{'& .MuiTableCell-root':{marginBottom:'50px'}},children:index+1}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Input,{textFieldProps:{placeholder:MONEY_PLACEHOLDER,InputProps:{inputComponent:NumericFormatCustom}},name:\"rankCostHistoryList.\".concat(index,\".amount\")})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(DatePicker,{name:\"rankCostHistoryList.\".concat(index,\".fromDate\")})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(DatePicker,{name:\"rankCostHistoryList.\".concat(index,\".toDate\")})}),/*#__PURE__*/_jsx(TableCell,{sx:{'& .MuiStack-root':{marginBottom:'20px'}},children:count&&count>1&&/*#__PURE__*/_jsx(Stack,{direction:\"row\",justifyContent:\"center\",alignItems:\"center\",children:/*#__PURE__*/_jsx(Tooltip,{placement:\"top\",title:/*#__PURE__*/_jsx(FormattedMessage,{id:'delete'}),onClick:()=>index>0&&remove(index),children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"delete\",size:\"small\",children:/*#__PURE__*/_jsx(DeleteTwoToneIcon,{sx:{fontSize:'1.1rem'}})})})})})]});};export default RankCostHistoryTBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}