{"ast": null, "code": "import { isString } from '../utils.mjs';\nimport { hex } from './hex.mjs';\nimport { hsla } from './hsla.mjs';\nimport { rgba } from './rgba.mjs';\nconst color = {\n  test: v => rgba.test(v) || hex.test(v) || hsla.test(v),\n  parse: v => {\n    if (rgba.test(v)) {\n      return rgba.parse(v);\n    } else if (hsla.test(v)) {\n      return hsla.parse(v);\n    } else {\n      return hex.parse(v);\n    }\n  },\n  transform: v => {\n    return isString(v) ? v : v.hasOwnProperty(\"red\") ? rgba.transform(v) : hsla.transform(v);\n  }\n};\nexport { color };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}