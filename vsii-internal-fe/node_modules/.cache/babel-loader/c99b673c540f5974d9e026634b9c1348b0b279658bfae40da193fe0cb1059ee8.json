{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/OnGoingHCTBody.tsx\";\nimport React, { Fragment } from 'react';\n\n// material-ui\nimport { TableCell, TableBody, TableRow } from '@mui/material';\n\n// project imports\n\nimport { formatPrice } from 'utils/common';\nimport { Input, NumericFormatCustom } from 'components/extended/Form';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OnGoingHCTBody = props => {\n  const {\n    hcInfo\n  } = props;\n  return /*#__PURE__*/_jsxDEV(TableBody, {\n    children: /*#__PURE__*/_jsxDEV(TableRow, {\n      children: hcInfo === null || hcInfo === void 0 ? void 0 : hcInfo.map((item, key) => /*#__PURE__*/_jsxDEV(Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(TableCell, {\n          sx: {\n            '& .MuiFormControl-root': {\n              width: '100px'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            textFieldProps: {\n              InputProps: {\n                inputComponent: NumericFormatCustom\n              }\n            },\n            name: `monthlyHCList.${key}.hcMonthly`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          children: item === null || item === void 0 ? void 0 : item.billableDay\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          children: formatPrice(item === null || item === void 0 ? void 0 : item.billable)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 25\n        }, this)]\n      }, key, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 9\n  }, this);\n};\n_c = OnGoingHCTBody;\nexport default OnGoingHCTBody;\nvar _c;\n$RefreshReg$(_c, \"OnGoingHCTBody\");", "map": {"version": 3, "names": ["React", "Fragment", "TableCell", "TableBody", "TableRow", "formatPrice", "Input", "NumericFormatCustom", "jsxDEV", "_jsxDEV", "OnGoingHCTBody", "props", "hcInfo", "children", "map", "item", "key", "sx", "width", "textFieldProps", "InputProps", "inputComponent", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "billableDay", "billable", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/OnGoingHCTBody.tsx"], "sourcesContent": ["import React, { Fragment } from 'react';\n\n// material-ui\nimport { TableCell, TableBody, TableRow } from '@mui/material';\n\n// project imports\nimport { IMonthlyHCList } from 'types';\nimport { formatPrice } from 'utils/common';\nimport { Input, NumericFormatCustom } from 'components/extended/Form';\n\ninterface IOnGoingHCTBodyProps {\n    hcInfo?: IMonthlyHCList[];\n}\n\nconst OnGoingHCTBody = (props: IOnGoingHCTBodyProps) => {\n    const { hcInfo } = props;\n    return (\n        <TableBody>\n            <TableRow>\n                {hcInfo?.map((item: IMonthlyHCList, key: number) => (\n                    <Fragment key={key}>\n                        <TableCell\n                            sx={{\n                                '& .MuiFormControl-root': {\n                                    width: '100px'\n                                }\n                            }}\n                        >\n                            <Input\n                                textFieldProps={{\n                                    InputProps: {\n                                        inputComponent: NumericFormatCustom as any\n                                    }\n                                }}\n                                name={`monthlyHCList.${key}.hcMonthly`}\n                            />\n                        </TableCell>\n                        <TableCell>{item?.billableDay}</TableCell>\n                        <TableCell>{formatPrice(item?.billable)}</TableCell>\n                    </Fragment>\n                ))}\n            </TableRow>\n        </TableBody>\n    );\n};\n\nexport default OnGoingHCTBody;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;;AAEvC;AACA,SAASC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;;AAE9D;;AAEA,SAASC,WAAW,QAAQ,cAAc;AAC1C,SAASC,KAAK,EAAEC,mBAAmB,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMtE,MAAMC,cAAc,GAAIC,KAA2B,IAAK;EACpD,MAAM;IAAEC;EAAO,CAAC,GAAGD,KAAK;EACxB,oBACIF,OAAA,CAACN,SAAS;IAAAU,QAAA,eACNJ,OAAA,CAACL,QAAQ;MAAAS,QAAA,EACJD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,GAAG,CAAC,CAACC,IAAoB,EAAEC,GAAW,kBAC3CP,OAAA,CAACR,QAAQ;QAAAY,QAAA,gBACLJ,OAAA,CAACP,SAAS;UACNe,EAAE,EAAE;YACA,wBAAwB,EAAE;cACtBC,KAAK,EAAE;YACX;UACJ,CAAE;UAAAL,QAAA,eAEFJ,OAAA,CAACH,KAAK;YACFa,cAAc,EAAE;cACZC,UAAU,EAAE;gBACRC,cAAc,EAAEd;cACpB;YACJ,CAAE;YACFe,IAAI,EAAE,iBAAiBN,GAAG;UAAa;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eACZjB,OAAA,CAACP,SAAS;UAAAW,QAAA,EAAEE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY;QAAW;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC1CjB,OAAA,CAACP,SAAS;UAAAW,QAAA,EAAER,WAAW,CAACU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEa,QAAQ;QAAC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA,GAlBzCV,GAAG;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAmBR,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEpB,CAAC;AAACG,EAAA,GA9BInB,cAAc;AAgCpB,eAAeA,cAAc;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}