{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"action\", \"children\", \"component\", \"components\", \"componentsProps\", \"disabled\", \"focusableWhenDisabled\", \"onBlur\", \"onClick\", \"onFocus\", \"onFocusVisible\", \"onKeyDown\", \"onKeyUp\", \"onMouseLeave\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '../composeClasses';\nimport { getButtonUnstyledUtilityClass } from './buttonUnstyledClasses';\nimport useButton from './useButton';\nimport { useSlotProps } from '../utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    active,\n    disabled,\n    focusVisible\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', focusVisible && 'focusVisible', active && 'active']\n  };\n  return composeClasses(slots, getButtonUnstyledUtilityClass, {});\n};\n/**\n * The foundation for building custom-styled buttons.\n *\n * Demos:\n *\n * - [Unstyled button](https://mui.com/base/react-button/)\n *\n * API:\n *\n * - [ButtonUnstyled API](https://mui.com/base/api/button-unstyled/)\n */\n\nconst ButtonUnstyled = /*#__PURE__*/React.forwardRef(function ButtonUnstyled(props, forwardedRef) {\n  var _ref;\n  const {\n      action,\n      children,\n      component,\n      components = {},\n      componentsProps = {},\n      focusableWhenDisabled = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const buttonRef = React.useRef();\n  const {\n    active,\n    focusVisible,\n    setFocusVisible,\n    getRootProps\n  } = useButton(_extends({}, props, {\n    focusableWhenDisabled\n  }));\n  React.useImperativeHandle(action, () => ({\n    focusVisible: () => {\n      setFocusVisible(true);\n      buttonRef.current.focus();\n    }\n  }), [setFocusVisible]);\n  const ownerState = _extends({}, props, {\n    active,\n    focusableWhenDisabled,\n    focusVisible\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = (_ref = component != null ? component : components.Root) != null ? _ref : 'button';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    getSlotProps: getRootProps,\n    externalForwardedProps: other,\n    externalSlotProps: componentsProps.root,\n    additionalProps: {\n      ref: forwardedRef\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? ButtonUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * A ref for imperative actions. It currently only supports `focusVisible()` action.\n   */\n  action: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.shape({\n      focusVisible: PropTypes.func.isRequired\n    })\n  })]),\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the Button.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The props used for each slot inside the Button.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, allows a disabled button to receive focus.\n   * @default false\n   */\n  focusableWhenDisabled: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocusVisible: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyUp: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseLeave: PropTypes.func\n} : void 0;\nexport default ButtonUnstyled;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}