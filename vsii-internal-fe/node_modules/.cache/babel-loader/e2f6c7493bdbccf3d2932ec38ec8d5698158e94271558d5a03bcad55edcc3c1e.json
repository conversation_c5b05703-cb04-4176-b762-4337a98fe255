{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"component\", \"image\", \"src\", \"style\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport { getCardMediaUtilityClass } from './cardMediaClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    isMediaComponent,\n    isImageComponent\n  } = ownerState;\n  const slots = {\n    root: ['root', isMediaComponent && 'media', isImageComponent && 'img']\n  };\n  return composeClasses(slots, getCardMediaUtilityClass, classes);\n};\nconst CardMediaRoot = styled('div', {\n  name: 'MuiCardMedia',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    const {\n      isMediaComponent,\n      isImageComponent\n    } = ownerState;\n    return [styles.root, isMediaComponent && styles.media, isImageComponent && styles.img];\n  }\n})(_ref => {\n  let {\n    ownerState\n  } = _ref;\n  return _extends({\n    display: 'block',\n    backgroundSize: 'cover',\n    backgroundRepeat: 'no-repeat',\n    backgroundPosition: 'center'\n  }, ownerState.isMediaComponent && {\n    width: '100%'\n  }, ownerState.isImageComponent && {\n    // ⚠️ object-fit is not supported by IE11.\n    objectFit: 'cover'\n  });\n});\nconst MEDIA_COMPONENTS = ['video', 'audio', 'picture', 'iframe', 'img'];\nconst IMAGE_COMPONENTS = ['picture', 'img'];\nconst CardMedia = /*#__PURE__*/React.forwardRef(function CardMedia(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardMedia'\n  });\n  const {\n      children,\n      className,\n      component = 'div',\n      image,\n      src,\n      style\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const isMediaComponent = MEDIA_COMPONENTS.indexOf(component) !== -1;\n  const composedStyle = !isMediaComponent && image ? _extends({\n    backgroundImage: \"url(\\\"\".concat(image, \"\\\")\")\n  }, style) : style;\n  const ownerState = _extends({}, props, {\n    component,\n    isMediaComponent,\n    isImageComponent: IMAGE_COMPONENTS.indexOf(component) !== -1\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardMediaRoot, _extends({\n    className: clsx(classes.root, className),\n    as: component,\n    role: !isMediaComponent && image ? 'img' : undefined,\n    ref: ref,\n    style: composedStyle,\n    ownerState: ownerState,\n    src: isMediaComponent ? image || src : undefined\n  }, other, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? CardMedia.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: chainPropTypes(PropTypes.node, props => {\n    if (!props.children && !props.image && !props.src && !props.component) {\n      return new Error('MUI: Either `children`, `image`, `src` or `component` prop must be specified.');\n    }\n    return null;\n  }),\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Image to be displayed as a background image.\n   * Either `image` or `src` prop must be specified.\n   * Note that caller must specify height otherwise the image will not be visible.\n   */\n  image: PropTypes.string,\n  /**\n   * An alias for `image` property.\n   * Available only with media components.\n   * Media components: `video`, `audio`, `picture`, `iframe`, `img`.\n   */\n  src: PropTypes.string,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardMedia;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}