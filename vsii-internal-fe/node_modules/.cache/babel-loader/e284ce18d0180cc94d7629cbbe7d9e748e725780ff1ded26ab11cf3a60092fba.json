{"ast": null, "code": "import{FormattedMessage}from'react-intl';// project imports\nimport{Input}from'components/extended/Form';import{searchFormConfig}from'./Config';import{jsx as _jsx}from\"react/jsx-runtime\";const Type=_ref=>{let{label}=_ref;return/*#__PURE__*/_jsx(Input,{name:searchFormConfig.type.name,label:/*#__PURE__*/_jsx(FormattedMessage,{id:label||searchFormConfig.type.label})});};export default Type;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}