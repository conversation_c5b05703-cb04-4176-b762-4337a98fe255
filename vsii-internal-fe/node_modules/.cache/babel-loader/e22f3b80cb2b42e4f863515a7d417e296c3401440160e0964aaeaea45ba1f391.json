{"ast": null, "code": "import MixedSchema, { create as mixedCreate } from './mixed';\nimport BooleanSchema, { create as boolCreate } from './boolean';\nimport StringSchema, { create as stringCreate } from './string';\nimport NumberSchema, { create as numberCreate } from './number';\nimport DateSchema, { create as dateCreate } from './date';\nimport ObjectSchema, { create as objectCreate } from './object';\nimport ArraySchema, { create as arrayCreate } from './array';\nimport { create as refCreate } from './Reference';\nimport { create as lazyCreate } from './Lazy';\nimport ValidationError from './ValidationError';\nimport reach from './util/reach';\nimport isSchema from './util/isSchema';\nimport setLocale from './setLocale';\nimport BaseSchema from './schema';\nfunction addMethod(schemaType, name, fn) {\n  if (!schemaType || !isSchema(schemaType.prototype)) throw new TypeError('You must provide a yup schema constructor function');\n  if (typeof name !== 'string') throw new TypeError('A Method name must be provided');\n  if (typeof fn !== 'function') throw new TypeError('Method function must be provided');\n  schemaType.prototype[name] = fn;\n}\nexport { mixedCreate as mixed, boolCreate as bool, boolCreate as boolean, stringCreate as string, numberCreate as number, dateCreate as date, objectCreate as object, arrayCreate as array, refCreate as ref, lazyCreate as lazy, reach, isSchema, addMethod, setLocale, ValidationError };\nexport { BaseSchema, MixedSchema, BooleanSchema, StringSchema, NumberSchema, DateSchema, ObjectSchema, ArraySchema };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}