{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/monthly-project-cost/DetailReportByMonthThead.tsx\",\n  _s = $RefreshSig$();\n// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\nimport { FormattedMessage } from 'react-intl';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme } from '@mui/material/styles';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DetailReportByMonthThead = () => {\n  _s();\n  const theme = useTheme();\n  const matches = useMediaQuery(theme.breakpoints.up('md'));\n  const {\n    monthlyProjectCost\n  } = TEXT_CONFIG_SCREEN;\n  return /*#__PURE__*/_jsxDEV(TableHead, {\n    children: /*#__PURE__*/_jsxDEV(TableRow, {\n      sx: {\n        '& .MuiTableCell-root': {\n          textAlign: 'center',\n          fontWeight: '700'\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          textAlign: 'left !important',\n          left: !!matches ? 0 : 'unset',\n          zIndex: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: monthlyProjectCost.detailReportByMonth + 'project'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: monthlyProjectCost.detailReportByMonth + 'department'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          textAlign: 'left !important'\n        },\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: monthlyProjectCost.detailReportByMonth + 'project-type'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: monthlyProjectCost.detailReportByMonth + 'total-effort-md'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: monthlyProjectCost.detailReportByMonth + 'overhead-allocated-amt'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: monthlyProjectCost.detailReportByMonth + 'salary-cost'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: monthlyProjectCost.detailReportByMonth + 'total-cost'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 9\n  }, this);\n};\n_s(DetailReportByMonthThead, \"W0qUR68KjTkJDAHnizbTku3qehE=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c = DetailReportByMonthThead;\nexport default DetailReportByMonthThead;\nvar _c;\n$RefreshReg$(_c, \"DetailReportByMonthThead\");", "map": {"version": 3, "names": ["TableCell", "TableHead", "TableRow", "FormattedMessage", "useMediaQuery", "useTheme", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "DetailReportByMonthThead", "_s", "theme", "matches", "breakpoints", "up", "monthlyProjectCost", "children", "sx", "textAlign", "fontWeight", "left", "zIndex", "id", "detailReportByMonth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/monthly-project-cost/DetailReportByMonthThead.tsx"], "sourcesContent": ["// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\nimport { FormattedMessage } from 'react-intl';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme } from '@mui/material/styles';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\n\nconst DetailReportByMonthThead = () => {\n    const theme = useTheme();\n    const matches = useMediaQuery(theme.breakpoints.up('md'));\n\n    const { monthlyProjectCost } = TEXT_CONFIG_SCREEN;\n    return (\n        <TableHead>\n            <TableRow\n                sx={{\n                    '& .MuiTableCell-root': {\n                        textAlign: 'center',\n                        fontWeight: '700'\n                    }\n                }}\n            >\n                <TableCell\n                    sx={{\n                        textAlign: 'left !important',\n                        left: !!matches ? 0 : 'unset',\n                        zIndex: 3\n                    }}\n                >\n                    <FormattedMessage id={monthlyProjectCost.detailReportByMonth + 'project'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={monthlyProjectCost.detailReportByMonth + 'department'} />\n                </TableCell>\n                <TableCell sx={{ textAlign: 'left !important' }}>\n                    <FormattedMessage id={monthlyProjectCost.detailReportByMonth + 'project-type'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={monthlyProjectCost.detailReportByMonth + 'total-effort-md'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={monthlyProjectCost.detailReportByMonth + 'overhead-allocated-amt'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={monthlyProjectCost.detailReportByMonth + 'salary-cost'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={monthlyProjectCost.detailReportByMonth + 'total-cost'} />\n                </TableCell>\n            </TableRow>\n        </TableHead>\n    );\n};\n\nexport default DetailReportByMonthThead;\n"], "mappings": ";;AAAA;AACA,SAASA,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC9D,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,KAAK,GAAGN,QAAQ,CAAC,CAAC;EACxB,MAAMO,OAAO,GAAGR,aAAa,CAACO,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,CAAC;EAEzD,MAAM;IAAEC;EAAmB,CAAC,GAAGT,kBAAkB;EACjD,oBACIE,OAAA,CAACP,SAAS;IAAAe,QAAA,eACNR,OAAA,CAACN,QAAQ;MACLe,EAAE,EAAE;QACA,sBAAsB,EAAE;UACpBC,SAAS,EAAE,QAAQ;UACnBC,UAAU,EAAE;QAChB;MACJ,CAAE;MAAAH,QAAA,gBAEFR,OAAA,CAACR,SAAS;QACNiB,EAAE,EAAE;UACAC,SAAS,EAAE,iBAAiB;UAC5BE,IAAI,EAAE,CAAC,CAACR,OAAO,GAAG,CAAC,GAAG,OAAO;UAC7BS,MAAM,EAAE;QACZ,CAAE;QAAAL,QAAA,eAEFR,OAAA,CAACL,gBAAgB;UAACmB,EAAE,EAAEP,kBAAkB,CAACQ,mBAAmB,GAAG;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,eACZnB,OAAA,CAACR,SAAS;QAAAgB,QAAA,eACNR,OAAA,CAACL,gBAAgB;UAACmB,EAAE,EAAEP,kBAAkB,CAACQ,mBAAmB,GAAG;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eACZnB,OAAA,CAACR,SAAS;QAACiB,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAkB,CAAE;QAAAF,QAAA,eAC5CR,OAAA,CAACL,gBAAgB;UAACmB,EAAE,EAAEP,kBAAkB,CAACQ,mBAAmB,GAAG;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC,eACZnB,OAAA,CAACR,SAAS;QAAAgB,QAAA,eACNR,OAAA,CAACL,gBAAgB;UAACmB,EAAE,EAAEP,kBAAkB,CAACQ,mBAAmB,GAAG;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CAAC,eACZnB,OAAA,CAACR,SAAS;QAAAgB,QAAA,eACNR,OAAA,CAACL,gBAAgB;UAACmB,EAAE,EAAEP,kBAAkB,CAACQ,mBAAmB,GAAG;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC,eACZnB,OAAA,CAACR,SAAS;QAAAgB,QAAA,eACNR,OAAA,CAACL,gBAAgB;UAACmB,EAAE,EAAEP,kBAAkB,CAACQ,mBAAmB,GAAG;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eACZnB,OAAA,CAACR,SAAS;QAAAgB,QAAA,eACNR,OAAA,CAACL,gBAAgB;UAACmB,EAAE,EAAEP,kBAAkB,CAACQ,mBAAmB,GAAG;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEpB,CAAC;AAACjB,EAAA,CA7CID,wBAAwB;EAAA,QACZJ,QAAQ,EACND,aAAa;AAAA;AAAAwB,EAAA,GAF3BnB,wBAAwB;AA+C9B,eAAeA,wBAAwB;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}