{"ast": null, "code": "import{Grid,Typography}from'@mui/material';import ErrorIcon from'@mui/icons-material/Error';import{FormattedMessage}from'react-intl';import{getWeeklyEffortProjectOption,weeklyEffortSelector}from'store/slice/weeklyEffortSlice';import{weeklyEffortConfig,weeklyEffortProjectSchema}from'pages/Config';import{TEXT_CONFIG_SCREEN,TEXT_INPUT_COLOR_VERIFY_TIMESHEET}from'constants/Common';import{Autocomplete,Label}from'components/extended/Form';import{searchFormConfig}from'containers/search/Config';import ColorNoteTooltip from'components/ColorNoteTooltip';import{useAppDispatch,useAppSelector}from'app/hooks';import{SearchForm,Weeks,Years}from'../search';import{Button}from'components';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const WeeklyEffortProjectDetailSearch=_ref=>{let{formReset,weeks,handleSearch,handleChangeYear}=_ref;const{projectOptions}=useAppSelector(weeklyEffortSelector);const dispatch=useAppDispatch();const{Weeklyeffort}=TEXT_CONFIG_SCREEN;const handleChangeWeek=week=>{dispatch(getWeeklyEffortProjectOption({week,color:true}));};return/*#__PURE__*/_jsx(SearchForm,{defaultValues:weeklyEffortConfig,formSchema:weeklyEffortProjectSchema,handleSubmit:handleSearch,formReset:formReset,children:/*#__PURE__*/_jsxs(Grid,{container:true,alignItems:\"center\",spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:3,children:/*#__PURE__*/_jsx(Years,{handleChangeYear:handleChangeYear,ignoreDefault:true,label:Weeklyeffort+'year'})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:3,children:/*#__PURE__*/_jsx(Weeks,{weeks:weeks,onChange:handleChangeWeek,label:Weeklyeffort+'weeks'})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:3,children:/*#__PURE__*/_jsx(Autocomplete,{options:projectOptions,name:searchFormConfig.project.name,label:/*#__PURE__*/_jsxs(Typography,{display:\"flex\",gap:0.5,children:[/*#__PURE__*/_jsx(FormattedMessage,{id:Weeklyeffort+'projects'}),/*#__PURE__*/_jsx(ColorNoteTooltip,{notes:TEXT_INPUT_COLOR_VERIFY_TIMESHEET,width:\"150px\",children:/*#__PURE__*/_jsx(ErrorIcon,{sx:{fontSize:15}})})]}),groupBy:option=>option.typeCode,isDefaultAll:true})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,lg:3,children:[/*#__PURE__*/_jsx(Label,{label:\"\\xA0\"}),/*#__PURE__*/_jsx(Button,{type:\"submit\",size:\"medium\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:Weeklyeffort+'search'}),variant:\"contained\"})]})]})});};export default WeeklyEffortProjectDetailSearch;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}