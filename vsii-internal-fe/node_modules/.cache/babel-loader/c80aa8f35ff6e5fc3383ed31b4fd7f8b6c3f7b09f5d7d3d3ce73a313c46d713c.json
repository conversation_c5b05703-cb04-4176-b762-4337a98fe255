{"ast": null, "code": "import { getPickersLocalization } from './utils/getPickersLocalization'; // maps ClockPickerView to its translation\n\nconst clockViews = {\n  hours: '時間',\n  minutes: '分',\n  seconds: '秒'\n}; // maps PickersToolbar[\"viewType\"] to its translation\n\nconst pickerViews = {\n  calendar: 'カレンダー表示',\n  clock: '時計表示'\n};\nconst jaJPPickers = {\n  // Calendar navigation\n  previousMonth: '先月',\n  nextMonth: '来月',\n  // View navigation\n  openPreviousView: '前の表示を開く',\n  openNextView: '次の表示を開く',\n  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? '年選択表示からカレンダー表示に切り替える' : 'カレンダー表示から年選択表示に切り替える',\n  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `テキスト入力表示から${pickerViews[viewType]}に切り替える` : `${pickerViews[viewType]}からテキスト入力表示に切り替える`,\n  // DateRange placeholders\n  start: '開始',\n  end: '終了',\n  // Action bar\n  cancelButtonLabel: 'キャンセル',\n  clearButtonLabel: 'クリア',\n  okButtonLabel: '確定',\n  todayButtonLabel: '今日',\n  // Toolbar titles\n  datePickerDefaultToolbarTitle: '日付を選択',\n  dateTimePickerDefaultToolbarTitle: '日時を選択',\n  timePickerDefaultToolbarTitle: '時間を選択',\n  dateRangePickerDefaultToolbarTitle: '日付の範囲を選択',\n  // Clock labels\n  clockLabelText: (view, time, adapter) => {\n    var _clockViews$view;\n    return `${(_clockViews$view = clockViews[view]) != null ? _clockViews$view : view}を選択してください ${time === null ? '時間が選択されていません' : `選択した時間は ${adapter.format(time, 'fullTime')} です`}`;\n  },\n  hoursClockNumberText: hours => `${hours} ${clockViews.hours}`,\n  minutesClockNumberText: minutes => `${minutes} ${clockViews.minutes}`,\n  secondsClockNumberText: seconds => `${seconds} ${clockViews.seconds}`,\n  // Open picker labels\n  openDatePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `日付を選択してください。選択した日付は ${utils.format(value, 'fullDate')} です` : '日付を選択してください',\n  openTimePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `時間を選択してください。選択した時間は ${utils.format(value, 'fullTime')} です` : '時間を選択してください',\n  // Table labels\n  timeTableLabel: '時間を選択',\n  dateTableLabel: '日付を選択'\n};\nexport const jaJP = getPickersLocalization(jaJPPickers);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}