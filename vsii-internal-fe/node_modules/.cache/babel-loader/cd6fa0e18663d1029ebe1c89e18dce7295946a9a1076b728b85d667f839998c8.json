{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/ProjectReferenceTHead.tsx\";\n// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProjectReferenceTHead = () => {\n  const {\n    salesReport\n  } = TEXT_CONFIG_SCREEN;\n  return /*#__PURE__*/_jsxDEV(TableHead, {\n    children: /*#__PURE__*/_jsxDEV(TableRow, {\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.projectReference + 'no'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.projectReference + 'project-name'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.projectReference + 'client'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.projectReference + 'technology'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.projectReference + 'domain'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.projectReference + 'project-type'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.projectReference + 'effort'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.projectReference + 'project-manager'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.projectReference + 'project-description'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.projectReference + 'from-date'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.projectReference + 'to-date'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 9\n  }, this);\n};\n_c = ProjectReferenceTHead;\nexport default ProjectReferenceTHead;\nvar _c;\n$RefreshReg$(_c, \"ProjectReferenceTHead\");", "map": {"version": 3, "names": ["TableCell", "TableHead", "TableRow", "TEXT_CONFIG_SCREEN", "FormattedMessage", "jsxDEV", "_jsxDEV", "ProjectReferenceTHead", "salesReport", "children", "id", "projectReference", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/ProjectReferenceTHead.tsx"], "sourcesContent": ["// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\n\nconst ProjectReferenceTHead = () => {\n    const { salesReport } = TEXT_CONFIG_SCREEN;\n    return (\n        <TableHead>\n            <TableRow>\n                <TableCell>\n                    <FormattedMessage id={salesReport.projectReference + 'no'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.projectReference + 'project-name'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.projectReference + 'client'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.projectReference + 'technology'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.projectReference + 'domain'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.projectReference + 'project-type'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.projectReference + 'effort'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.projectReference + 'project-manager'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.projectReference + 'project-description'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.projectReference + 'from-date'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={salesReport.projectReference + 'to-date'} />\n                </TableCell>\n            </TableRow>\n        </TableHead>\n    );\n};\n\nexport default ProjectReferenceTHead;\n"], "mappings": ";AAAA;AACA,SAASA,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC9D,SAASC,kBAAkB,QAAQ,kBAAkB;;AAErD;AACA,SAASC,gBAAgB,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAChC,MAAM;IAAEC;EAAY,CAAC,GAAGL,kBAAkB;EAC1C,oBACIG,OAAA,CAACL,SAAS;IAAAQ,QAAA,eACNH,OAAA,CAACJ,QAAQ;MAAAO,QAAA,gBACLH,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,gBAAgB,GAAG;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,gBAAgB,GAAG;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,gBAAgB,GAAG;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,gBAAgB,GAAG;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,gBAAgB,GAAG;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,gBAAgB,GAAG;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,gBAAgB,GAAG;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,gBAAgB,GAAG;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,gBAAgB,GAAG;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,gBAAgB,GAAG;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eACZT,OAAA,CAACN,SAAS;QAAAS,QAAA,eACNH,OAAA,CAACF,gBAAgB;UAACM,EAAE,EAAEF,WAAW,CAACG,gBAAgB,GAAG;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEpB,CAAC;AAACC,EAAA,GAzCIT,qBAAqB;AA2C3B,eAAeA,qBAAqB;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}