{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/BiddingPackageName.tsx\";\nimport { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { Input } from 'components/extended/Form';\nimport { searchFormConfig } from './Config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BiddingPackageName = ({\n  label\n}) => {\n  return /*#__PURE__*/_jsxDEV(Input, {\n    name: searchFormConfig.biddingPackageName.name,\n    label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n      id: label || searchFormConfig.biddingPackageName.label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 20\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 9\n  }, this);\n};\n_c = BiddingPackageName;\nexport default BiddingPackageName;\nvar _c;\n$RefreshReg$(_c, \"BiddingPackageName\");", "map": {"version": 3, "names": ["FormattedMessage", "Input", "searchFormConfig", "jsxDEV", "_jsxDEV", "BiddingPackageName", "label", "name", "biddingPackageName", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/BiddingPackageName.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\r\n\r\n// project imports\r\nimport { Input } from 'components/extended/Form';\r\nimport { searchFormConfig } from './Config';\r\n\r\ninterface IBiddingPackageNameProps {\r\n    label?: string;\r\n}\r\n\r\nconst BiddingPackageName = ({ label }: IBiddingPackageNameProps) => {\r\n    return (\r\n        <Input\r\n            name={searchFormConfig.biddingPackageName.name}\r\n            label={<FormattedMessage id={label || searchFormConfig.biddingPackageName.label} />}\r\n        />\r\n    );\r\n};\r\n\r\nexport default BiddingPackageName;\r\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,gBAAgB,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM5C,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC;AAAgC,CAAC,KAAK;EAChE,oBACIF,OAAA,CAACH,KAAK;IACFM,IAAI,EAAEL,gBAAgB,CAACM,kBAAkB,CAACD,IAAK;IAC/CD,KAAK,eAAEF,OAAA,CAACJ,gBAAgB;MAACS,EAAE,EAAEH,KAAK,IAAIJ,gBAAgB,CAACM,kBAAkB,CAACF;IAAM;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvF,CAAC;AAEV,CAAC;AAACC,EAAA,GAPIT,kBAAkB;AASxB,eAAeA,kBAAkB;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}