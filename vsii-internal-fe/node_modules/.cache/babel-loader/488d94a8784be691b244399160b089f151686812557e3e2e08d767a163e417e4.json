{"ast": null, "code": "import _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport React, { Component, Children } from 'react';\nimport PropTypes from 'prop-types';\nimport { storeShape, subscriptionShape } from '../utils/PropTypes';\nimport warning from '../utils/warning';\nvar prefixUnsafeLifecycleMethods = typeof React.forwardRef !== \"undefined\";\nvar didWarnAboutReceivingStore = false;\nfunction warnAboutReceivingStore() {\n  if (didWarnAboutReceivingStore) {\n    return;\n  }\n  didWarnAboutReceivingStore = true;\n  warning('<Provider> does not support changing `store` on the fly. ' + 'It is most likely that you see this error because you updated to ' + 'Redux 2.x and React Redux 2.x which no longer hot reload reducers ' + 'automatically. See https://github.com/reduxjs/react-redux/releases/' + 'tag/v2.0.0 for the migration instructions.');\n}\nexport function createProvider(storeKey) {\n  var _Provider$childContex;\n  if (storeKey === void 0) {\n    storeKey = 'store';\n  }\n  var subscriptionKey = storeKey + \"Subscription\";\n  var Provider = /*#__PURE__*/\n  function (_Component) {\n    _inheritsLoose(Provider, _Component);\n    var _proto = Provider.prototype;\n    _proto.getChildContext = function getChildContext() {\n      var _ref;\n      return _ref = {}, _ref[storeKey] = this[storeKey], _ref[subscriptionKey] = null, _ref;\n    };\n    function Provider(props, context) {\n      var _this;\n      _this = _Component.call(this, props, context) || this;\n      _this[storeKey] = props.store;\n      return _this;\n    }\n    _proto.render = function render() {\n      return Children.only(this.props.children);\n    };\n    return Provider;\n  }(Component);\n  if (process.env.NODE_ENV !== 'production') {\n    // Use UNSAFE_ event name where supported\n    var eventName = prefixUnsafeLifecycleMethods ? 'UNSAFE_componentWillReceiveProps' : 'componentWillReceiveProps';\n    Provider.prototype[eventName] = function (nextProps) {\n      if (this[storeKey] !== nextProps.store) {\n        warnAboutReceivingStore();\n      }\n    };\n  }\n  Provider.propTypes = {\n    store: storeShape.isRequired,\n    children: PropTypes.element.isRequired\n  };\n  Provider.childContextTypes = (_Provider$childContex = {}, _Provider$childContex[storeKey] = storeShape.isRequired, _Provider$childContex[subscriptionKey] = subscriptionShape, _Provider$childContex);\n  return Provider;\n}\nexport default createProvider();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}