{"ast": null, "code": "import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';\nimport omit from 'lodash/omit';\nimport sendRequest from 'services/ApiService';\nimport Api from 'constants/Api';\nexport const getSearchTitle = createAsyncThunk(Api.title.search.url, async params => {\n  const response = await sendRequest(Api.title.search, params);\n  return response;\n});\nexport const createTitle = createAsyncThunk(Api.title.create.url, async params => {\n  const response = await sendRequest(Api.title.create, params);\n  return response;\n});\nexport const editTitle = createAsyncThunk('Api.title.edit.url', async params => {\n  const response = await sendRequest(Api.title.edit(params.id), omit(params, ['id']));\n  return response;\n});\nexport const deleteTitle = createAsyncThunk('Api.title.delete.url', async params => {\n  const response = await sendRequest(Api.title.delete(params));\n  return response;\n});\nconst initialState = {\n  loading: {}\n};\nconst titleSlice = createSlice({\n  name: 'title',\n  initialState: initialState,\n  reducers: {},\n  extraReducers: builder => {\n    builder.addCase(getSearchTitle.pending, state => {\n      state.loading[getSearchTitle.typePrefix] = true;\n    });\n    builder.addCase(getSearchTitle.fulfilled, (state, action) => {\n      if (action.payload.status && Array.isArray(action.payload.result.content)) {\n        state.titles = action.payload.result;\n      }\n      state.loading[getSearchTitle.typePrefix] = false;\n    });\n    builder.addCase(getSearchTitle.rejected, state => {\n      state.loading[getSearchTitle.typePrefix] = false;\n    });\n    builder.addCase(createTitle.pending, state => {\n      state.loading[createTitle.typePrefix] = true;\n    });\n    builder.addCase(createTitle.fulfilled, state => {\n      state.loading[createTitle.typePrefix] = false;\n    });\n    builder.addCase(createTitle.rejected, state => {\n      state.loading[createTitle.typePrefix] = false;\n    });\n    builder.addCase(editTitle.pending, state => {\n      state.loading[editTitle.typePrefix] = true;\n    });\n    builder.addCase(editTitle.fulfilled, state => {\n      state.loading[editTitle.typePrefix] = false;\n    });\n    builder.addCase(editTitle.rejected, state => {\n      state.loading[editTitle.typePrefix] = false;\n    });\n    builder.addCase(deleteTitle.pending, state => {\n      state.loading[deleteTitle.typePrefix] = true;\n    });\n    builder.addCase(deleteTitle.fulfilled, state => {\n      state.loading[deleteTitle.typePrefix] = false;\n    });\n    builder.addCase(deleteTitle.rejected, state => {\n      state.loading[deleteTitle.typePrefix] = false;\n    });\n  }\n});\nexport default titleSlice.reducer;\nexport const titleConfigSelector = state => state.title;", "map": {"version": 3, "names": ["createAsyncThunk", "createSlice", "omit", "sendRequest", "Api", "getSearchTitle", "title", "search", "url", "params", "response", "createTitle", "create", "editTitle", "edit", "id", "deleteTitle", "delete", "initialState", "loading", "titleSlice", "name", "reducers", "extraReducers", "builder", "addCase", "pending", "state", "typePrefix", "fulfilled", "action", "payload", "status", "Array", "isArray", "result", "content", "titles", "rejected", "reducer", "titleConfigSelector"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/store/slice/titleSlice.ts"], "sourcesContent": ["import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';\nimport omit from 'lodash/omit';\n\nimport { ICreateTitleRequest, IEditTitleRequest, IGetTitleResponse } from 'types/titleConfig';\nimport { ITitleFilterConfig } from 'pages/administration/Config';\nimport { IResponseList, Response } from 'types';\nimport sendRequest from 'services/ApiService';\nimport { RootState } from 'app/store';\nimport Api from 'constants/Api';\n\nexport const getSearchTitle = createAsyncThunk<IResponseList<IGetTitleResponse>, ITitleFilterConfig>(\n    Api.title.search.url,\n    async (params) => {\n        const response = await sendRequest(Api.title.search, params);\n\n        return response;\n    }\n);\n\nexport const createTitle = createAsyncThunk<Response<{ content: string }>, ICreateTitleRequest>(Api.title.create.url, async (params) => {\n    const response = await sendRequest(Api.title.create, params);\n\n    return response;\n});\n\nexport const editTitle = createAsyncThunk<Response<{ content: string }>, IEditTitleRequest>('Api.title.edit.url', async (params) => {\n    const response = await sendRequest(Api.title.edit(params.id), omit(params, ['id']));\n\n    return response;\n});\n\nexport const deleteTitle = createAsyncThunk<Response<{ content: string }>, string>('Api.title.delete.url', async (params) => {\n    const response = await sendRequest(Api.title.delete(params));\n\n    return response;\n});\n\ninterface ITitleConfigState {\n    titles?: IResponseList<IGetTitleResponse>['result'];\n    loading: { [key: string]: boolean };\n}\n\nconst initialState: ITitleConfigState = {\n    loading: {}\n};\n\nconst titleSlice = createSlice({\n    name: 'title',\n    initialState: initialState,\n    reducers: {},\n    extraReducers: (builder) => {\n        builder.addCase(getSearchTitle.pending, (state) => {\n            state.loading[getSearchTitle.typePrefix] = true;\n        });\n        builder.addCase(getSearchTitle.fulfilled, (state, action) => {\n            if (action.payload.status && Array.isArray(action.payload.result.content)) {\n                state.titles = action.payload.result;\n            }\n            state.loading[getSearchTitle.typePrefix] = false;\n        });\n        builder.addCase(getSearchTitle.rejected, (state) => {\n            state.loading[getSearchTitle.typePrefix] = false;\n        });\n        builder.addCase(createTitle.pending, (state) => {\n            state.loading[createTitle.typePrefix] = true;\n        });\n        builder.addCase(createTitle.fulfilled, (state) => {\n            state.loading[createTitle.typePrefix] = false;\n        });\n        builder.addCase(createTitle.rejected, (state) => {\n            state.loading[createTitle.typePrefix] = false;\n        });\n        builder.addCase(editTitle.pending, (state) => {\n            state.loading[editTitle.typePrefix] = true;\n        });\n        builder.addCase(editTitle.fulfilled, (state) => {\n            state.loading[editTitle.typePrefix] = false;\n        });\n        builder.addCase(editTitle.rejected, (state) => {\n            state.loading[editTitle.typePrefix] = false;\n        });\n        builder.addCase(deleteTitle.pending, (state) => {\n            state.loading[deleteTitle.typePrefix] = true;\n        });\n        builder.addCase(deleteTitle.fulfilled, (state) => {\n            state.loading[deleteTitle.typePrefix] = false;\n        });\n        builder.addCase(deleteTitle.rejected, (state) => {\n            state.loading[deleteTitle.typePrefix] = false;\n        });\n    }\n});\n\nexport default titleSlice.reducer;\n\nexport const titleConfigSelector = (state: RootState) => state.title;\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,WAAW,QAAQ,kBAAkB;AAChE,OAAOC,IAAI,MAAM,aAAa;AAK9B,OAAOC,WAAW,MAAM,qBAAqB;AAE7C,OAAOC,GAAG,MAAM,eAAe;AAE/B,OAAO,MAAMC,cAAc,GAAGL,gBAAgB,CAC1CI,GAAG,CAACE,KAAK,CAACC,MAAM,CAACC,GAAG,EACpB,MAAOC,MAAM,IAAK;EACd,MAAMC,QAAQ,GAAG,MAAMP,WAAW,CAACC,GAAG,CAACE,KAAK,CAACC,MAAM,EAAEE,MAAM,CAAC;EAE5D,OAAOC,QAAQ;AACnB,CACJ,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGX,gBAAgB,CAAqDI,GAAG,CAACE,KAAK,CAACM,MAAM,CAACJ,GAAG,EAAE,MAAOC,MAAM,IAAK;EACpI,MAAMC,QAAQ,GAAG,MAAMP,WAAW,CAACC,GAAG,CAACE,KAAK,CAACM,MAAM,EAAEH,MAAM,CAAC;EAE5D,OAAOC,QAAQ;AACnB,CAAC,CAAC;AAEF,OAAO,MAAMG,SAAS,GAAGb,gBAAgB,CAAmD,oBAAoB,EAAE,MAAOS,MAAM,IAAK;EAChI,MAAMC,QAAQ,GAAG,MAAMP,WAAW,CAACC,GAAG,CAACE,KAAK,CAACQ,IAAI,CAACL,MAAM,CAACM,EAAE,CAAC,EAAEb,IAAI,CAACO,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;EAEnF,OAAOC,QAAQ;AACnB,CAAC,CAAC;AAEF,OAAO,MAAMM,WAAW,GAAGhB,gBAAgB,CAAwC,sBAAsB,EAAE,MAAOS,MAAM,IAAK;EACzH,MAAMC,QAAQ,GAAG,MAAMP,WAAW,CAACC,GAAG,CAACE,KAAK,CAACW,MAAM,CAACR,MAAM,CAAC,CAAC;EAE5D,OAAOC,QAAQ;AACnB,CAAC,CAAC;AAOF,MAAMQ,YAA+B,GAAG;EACpCC,OAAO,EAAE,CAAC;AACd,CAAC;AAED,MAAMC,UAAU,GAAGnB,WAAW,CAAC;EAC3BoB,IAAI,EAAE,OAAO;EACbH,YAAY,EAAEA,YAAY;EAC1BI,QAAQ,EAAE,CAAC,CAAC;EACZC,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CAACC,OAAO,CAACpB,cAAc,CAACqB,OAAO,EAAGC,KAAK,IAAK;MAC/CA,KAAK,CAACR,OAAO,CAACd,cAAc,CAACuB,UAAU,CAAC,GAAG,IAAI;IACnD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACpB,cAAc,CAACwB,SAAS,EAAE,CAACF,KAAK,EAAEG,MAAM,KAAK;MACzD,IAAIA,MAAM,CAACC,OAAO,CAACC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAACC,OAAO,CAACI,MAAM,CAACC,OAAO,CAAC,EAAE;QACvET,KAAK,CAACU,MAAM,GAAGP,MAAM,CAACC,OAAO,CAACI,MAAM;MACxC;MACAR,KAAK,CAACR,OAAO,CAACd,cAAc,CAACuB,UAAU,CAAC,GAAG,KAAK;IACpD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACpB,cAAc,CAACiC,QAAQ,EAAGX,KAAK,IAAK;MAChDA,KAAK,CAACR,OAAO,CAACd,cAAc,CAACuB,UAAU,CAAC,GAAG,KAAK;IACpD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACd,WAAW,CAACe,OAAO,EAAGC,KAAK,IAAK;MAC5CA,KAAK,CAACR,OAAO,CAACR,WAAW,CAACiB,UAAU,CAAC,GAAG,IAAI;IAChD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACd,WAAW,CAACkB,SAAS,EAAGF,KAAK,IAAK;MAC9CA,KAAK,CAACR,OAAO,CAACR,WAAW,CAACiB,UAAU,CAAC,GAAG,KAAK;IACjD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACd,WAAW,CAAC2B,QAAQ,EAAGX,KAAK,IAAK;MAC7CA,KAAK,CAACR,OAAO,CAACR,WAAW,CAACiB,UAAU,CAAC,GAAG,KAAK;IACjD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACZ,SAAS,CAACa,OAAO,EAAGC,KAAK,IAAK;MAC1CA,KAAK,CAACR,OAAO,CAACN,SAAS,CAACe,UAAU,CAAC,GAAG,IAAI;IAC9C,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACZ,SAAS,CAACgB,SAAS,EAAGF,KAAK,IAAK;MAC5CA,KAAK,CAACR,OAAO,CAACN,SAAS,CAACe,UAAU,CAAC,GAAG,KAAK;IAC/C,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACZ,SAAS,CAACyB,QAAQ,EAAGX,KAAK,IAAK;MAC3CA,KAAK,CAACR,OAAO,CAACN,SAAS,CAACe,UAAU,CAAC,GAAG,KAAK;IAC/C,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACT,WAAW,CAACU,OAAO,EAAGC,KAAK,IAAK;MAC5CA,KAAK,CAACR,OAAO,CAACH,WAAW,CAACY,UAAU,CAAC,GAAG,IAAI;IAChD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACT,WAAW,CAACa,SAAS,EAAGF,KAAK,IAAK;MAC9CA,KAAK,CAACR,OAAO,CAACH,WAAW,CAACY,UAAU,CAAC,GAAG,KAAK;IACjD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACT,WAAW,CAACsB,QAAQ,EAAGX,KAAK,IAAK;MAC7CA,KAAK,CAACR,OAAO,CAACH,WAAW,CAACY,UAAU,CAAC,GAAG,KAAK;IACjD,CAAC,CAAC;EACN;AACJ,CAAC,CAAC;AAEF,eAAeR,UAAU,CAACmB,OAAO;AAEjC,OAAO,MAAMC,mBAAmB,GAAIb,KAAgB,IAAKA,KAAK,CAACrB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}