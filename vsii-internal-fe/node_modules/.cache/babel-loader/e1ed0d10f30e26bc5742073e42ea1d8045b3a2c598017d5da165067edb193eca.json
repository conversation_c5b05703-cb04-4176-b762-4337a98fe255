{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};\nvar _createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\nvar _react = require('react');\nvar _react2 = _interopRequireDefault(_react);\nvar _propTypes = require('prop-types');\nvar _perfectScrollbar = require('perfect-scrollbar');\nvar _perfectScrollbar2 = _interopRequireDefault(_perfectScrollbar);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _objectWithoutProperties(obj, keys) {\n  var target = {};\n  for (var i in obj) {\n    if (keys.indexOf(i) >= 0) continue;\n    if (!Object.prototype.hasOwnProperty.call(obj, i)) continue;\n    target[i] = obj[i];\n  }\n  return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n}\nvar handlerNameByEvent = {\n  'ps-scroll-y': 'onScrollY',\n  'ps-scroll-x': 'onScrollX',\n  'ps-scroll-up': 'onScrollUp',\n  'ps-scroll-down': 'onScrollDown',\n  'ps-scroll-left': 'onScrollLeft',\n  'ps-scroll-right': 'onScrollRight',\n  'ps-y-reach-start': 'onYReachStart',\n  'ps-y-reach-end': 'onYReachEnd',\n  'ps-x-reach-start': 'onXReachStart',\n  'ps-x-reach-end': 'onXReachEnd'\n};\nObject.freeze(handlerNameByEvent);\nvar ScrollBar = function (_Component) {\n  _inherits(ScrollBar, _Component);\n  function ScrollBar(props) {\n    _classCallCheck(this, ScrollBar);\n    var _this = _possibleConstructorReturn(this, (ScrollBar.__proto__ || Object.getPrototypeOf(ScrollBar)).call(this, props));\n    _this.handleRef = _this.handleRef.bind(_this);\n    _this._handlerByEvent = {};\n    return _this;\n  }\n  _createClass(ScrollBar, [{\n    key: 'componentDidMount',\n    value: function componentDidMount() {\n      if (this.props.option) {\n        console.warn('react-perfect-scrollbar: the \"option\" prop has been deprecated in favor of \"options\"');\n      }\n      this._ps = new _perfectScrollbar2.default(this._container, this.props.options || this.props.option);\n      // hook up events\n      this._updateEventHook();\n      this._updateClassName();\n    }\n  }, {\n    key: 'componentDidUpdate',\n    value: function componentDidUpdate(prevProps) {\n      this._updateEventHook(prevProps);\n      this.updateScroll();\n      if (prevProps.className !== this.props.className) {\n        this._updateClassName();\n      }\n    }\n  }, {\n    key: 'componentWillUnmount',\n    value: function componentWillUnmount() {\n      var _this2 = this;\n\n      // unhook up evens\n      Object.keys(this._handlerByEvent).forEach(function (key) {\n        var value = _this2._handlerByEvent[key];\n        if (value) {\n          _this2._container.removeEventListener(key, value, false);\n        }\n      });\n      this._handlerByEvent = {};\n      this._ps.destroy();\n      this._ps = null;\n    }\n  }, {\n    key: '_updateEventHook',\n    value: function _updateEventHook() {\n      var _this3 = this;\n      var prevProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n      // hook up events\n      Object.keys(handlerNameByEvent).forEach(function (key) {\n        var callback = _this3.props[handlerNameByEvent[key]];\n        var prevCallback = prevProps[handlerNameByEvent[key]];\n        if (callback !== prevCallback) {\n          if (prevCallback) {\n            var prevHandler = _this3._handlerByEvent[key];\n            _this3._container.removeEventListener(key, prevHandler, false);\n            _this3._handlerByEvent[key] = null;\n          }\n          if (callback) {\n            var handler = function handler() {\n              return callback(_this3._container);\n            };\n            _this3._container.addEventListener(key, handler, false);\n            _this3._handlerByEvent[key] = handler;\n          }\n        }\n      });\n    }\n  }, {\n    key: '_updateClassName',\n    value: function _updateClassName() {\n      var className = this.props.className;\n      var psClassNames = this._container.className.split(' ').filter(function (name) {\n        return name.match(/^ps([-_].+|)$/);\n      }).join(' ');\n      if (this._container) {\n        this._container.className = 'scrollbar-container' + (className ? ' ' + className : '') + (psClassNames ? ' ' + psClassNames : '');\n      }\n    }\n  }, {\n    key: 'updateScroll',\n    value: function updateScroll() {\n      this.props.onSync(this._ps);\n    }\n  }, {\n    key: 'handleRef',\n    value: function handleRef(ref) {\n      this._container = ref;\n      this.props.containerRef(ref);\n    }\n  }, {\n    key: 'render',\n    value: function render() {\n      var _props = this.props,\n        className = _props.className,\n        style = _props.style,\n        option = _props.option,\n        options = _props.options,\n        containerRef = _props.containerRef,\n        onScrollY = _props.onScrollY,\n        onScrollX = _props.onScrollX,\n        onScrollUp = _props.onScrollUp,\n        onScrollDown = _props.onScrollDown,\n        onScrollLeft = _props.onScrollLeft,\n        onScrollRight = _props.onScrollRight,\n        onYReachStart = _props.onYReachStart,\n        onYReachEnd = _props.onYReachEnd,\n        onXReachStart = _props.onXReachStart,\n        onXReachEnd = _props.onXReachEnd,\n        component = _props.component,\n        onSync = _props.onSync,\n        children = _props.children,\n        remainProps = _objectWithoutProperties(_props, ['className', 'style', 'option', 'options', 'containerRef', 'onScrollY', 'onScrollX', 'onScrollUp', 'onScrollDown', 'onScrollLeft', 'onScrollRight', 'onYReachStart', 'onYReachEnd', 'onXReachStart', 'onXReachEnd', 'component', 'onSync', 'children']);\n      var Comp = component;\n      return _react2.default.createElement(Comp, _extends({\n        style: style,\n        ref: this.handleRef\n      }, remainProps), children);\n    }\n  }]);\n  return ScrollBar;\n}(_react.Component);\nexports.default = ScrollBar;\nScrollBar.defaultProps = {\n  className: '',\n  style: undefined,\n  option: undefined,\n  options: undefined,\n  containerRef: function containerRef() {},\n  onScrollY: undefined,\n  onScrollX: undefined,\n  onScrollUp: undefined,\n  onScrollDown: undefined,\n  onScrollLeft: undefined,\n  onScrollRight: undefined,\n  onYReachStart: undefined,\n  onYReachEnd: undefined,\n  onXReachStart: undefined,\n  onXReachEnd: undefined,\n  onSync: function onSync(ps) {\n    return ps.update();\n  },\n  component: 'div'\n};\nScrollBar.propTypes = {\n  children: _propTypes.PropTypes.node.isRequired,\n  className: _propTypes.PropTypes.string,\n  style: _propTypes.PropTypes.object,\n  option: _propTypes.PropTypes.object,\n  options: _propTypes.PropTypes.object,\n  containerRef: _propTypes.PropTypes.func,\n  onScrollY: _propTypes.PropTypes.func,\n  onScrollX: _propTypes.PropTypes.func,\n  onScrollUp: _propTypes.PropTypes.func,\n  onScrollDown: _propTypes.PropTypes.func,\n  onScrollLeft: _propTypes.PropTypes.func,\n  onScrollRight: _propTypes.PropTypes.func,\n  onYReachStart: _propTypes.PropTypes.func,\n  onYReachEnd: _propTypes.PropTypes.func,\n  onXReachStart: _propTypes.PropTypes.func,\n  onXReachEnd: _propTypes.PropTypes.func,\n  onSync: _propTypes.PropTypes.func,\n  component: _propTypes.PropTypes.string\n};\nmodule.exports = exports['default'];", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}