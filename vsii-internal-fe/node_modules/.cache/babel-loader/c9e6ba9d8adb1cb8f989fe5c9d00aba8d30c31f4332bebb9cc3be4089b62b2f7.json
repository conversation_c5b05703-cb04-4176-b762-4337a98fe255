{"ast": null, "code": "import{createAsyncThunk,createSlice}from'@reduxjs/toolkit';import sendRequest from'services/ApiService';import Api from'constants/Api';// interface\n// initial state\nconst initialState={loading:{},warningNBMByMember:[]};export const getNBMByMember=createAsyncThunk(Api.non_billable_monitoring.getAll.url,async params=>{const response=await sendRequest(Api.non_billable_monitoring.getAll,params);return response;});export const getWarningNBMBytMember=createAsyncThunk(Api.non_billable_monitoring.getWarningNonbillable.url,async params=>{const response=await sendRequest(Api.non_billable_monitoring.getWarningNonbillable,params);return response;});const nonBillableMonitoringSlice=createSlice({name:'non-billable-monitoring',initialState:initialState,reducers:{},extraReducers:builder=>{// getNBMByMember\nbuilder.addCase(getNBMByMember.pending,state=>{state.NBMByMember=undefined;state.loading[getNBMByMember.typePrefix]=true;});builder.addCase(getNBMByMember.fulfilled,(state,action)=>{state.loading[getNBMByMember.typePrefix]=false;if(action.payload.status){state.NBMByMember=action.payload.result.content;}});builder.addCase(getNBMByMember.rejected,state=>{state.loading[getNBMByMember.typePrefix]=false;});// getWarningNBMBytMember\nbuilder.addCase(getWarningNBMBytMember.pending,state=>{state.warningNBMByMember=[];state.loading[getWarningNBMBytMember.typePrefix]=true;});builder.addCase(getWarningNBMBytMember.fulfilled,(state,action)=>{state.loading[getWarningNBMBytMember.typePrefix]=false;if(action.payload.status){state.warningNBMByMember=action.payload.result.content;}});builder.addCase(getWarningNBMBytMember.rejected,state=>{state.loading[getWarningNBMBytMember.typePrefix]=false;});}});// export const {} = nonBillableMonitoringSlice.actions;\n// selectors\nexport const nonBillableMonitoringSelector=state=>state.nonBillableMonitoring;// reducers\nexport default nonBillableMonitoringSlice.reducer;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}