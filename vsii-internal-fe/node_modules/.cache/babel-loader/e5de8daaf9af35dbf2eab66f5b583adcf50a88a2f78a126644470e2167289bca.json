{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/TextConfigTBody.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { TableBody } from '@mui/material';\nimport EditLanguageRow from './EditLanguageRow';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TextConfigTBody = ({\n  data,\n  conditions,\n  setDataEditLanguage\n}) => {\n  _s();\n  const [activeRowIndex, setActiveRowIndex] = useState(null);\n  const handleButtonClick = index => {\n    setActiveRowIndex(index);\n  };\n  return /*#__PURE__*/_jsxDEV(TableBody, {\n    children: data === null || data === void 0 ? void 0 : data.map((item, key) => /*#__PURE__*/_jsxDEV(EditLanguageRow, {\n      item: item,\n      conditions: conditions,\n      index: key,\n      setDataEditLanguage: setDataEditLanguage,\n      activeRowIndex: activeRowIndex,\n      handleclickEdit: () => handleButtonClick(key),\n      handleCancelEdit: () => {\n        setActiveRowIndex(null);\n      }\n    }, key, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 17\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 9\n  }, this);\n};\n_s(TextConfigTBody, \"XdNWcl8pY7Dzf3ql2hIT8KWLcXo=\");\n_c = TextConfigTBody;\nexport default TextConfigTBody;\nvar _c;\n$RefreshReg$(_c, \"TextConfigTBody\");", "map": {"version": 3, "names": ["React", "useState", "TableBody", "EditLanguageRow", "jsxDEV", "_jsxDEV", "TextConfigTBody", "data", "conditions", "setDataEditLanguage", "_s", "activeRowIndex", "setActiveRowIndex", "handleButtonClick", "index", "children", "map", "item", "key", "handleclickEdit", "handleCancelEdit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/TextConfigTBody.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { TableBody } from '@mui/material';\n\nimport { ISearchTextConfigParams, ITextConfig } from 'types/flexible-report';\nimport EditLanguageRow from './EditLanguageRow';\n\ninterface Props {\n    data: ITextConfig[] | undefined;\n    conditions: ISearchTextConfigParams;\n    setDataEditLanguage: React.Dispatch<React.SetStateAction<ITextConfig | undefined>>;\n}\n\nconst TextConfigTBody: React.FC<Props> = ({ data, conditions, setDataEditLanguage }) => {\n    const [activeRowIndex, setActiveRowIndex] = useState<number | null>(null);\n\n    const handleButtonClick = (index: number) => {\n        setActiveRowIndex(index);\n    };\n    return (\n        <TableBody>\n            {data?.map((item, key) => (\n                <EditLanguageRow\n                    key={key}\n                    item={item}\n                    conditions={conditions}\n                    index={key}\n                    setDataEditLanguage={setDataEditLanguage}\n                    activeRowIndex={activeRowIndex}\n                    handleclickEdit={() => handleButtonClick(key)}\n                    handleCancelEdit={() => {\n                        setActiveRowIndex(null);\n                    }}\n                />\n            ))}\n        </TableBody>\n    );\n};\n\nexport default TextConfigTBody;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,QAAQ,eAAe;AAGzC,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQhD,MAAMC,eAAgC,GAAGA,CAAC;EAAEC,IAAI;EAAEC,UAAU;EAAEC;AAAoB,CAAC,KAAK;EAAAC,EAAA;EACpF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGX,QAAQ,CAAgB,IAAI,CAAC;EAEzE,MAAMY,iBAAiB,GAAIC,KAAa,IAAK;IACzCF,iBAAiB,CAACE,KAAK,CAAC;EAC5B,CAAC;EACD,oBACIT,OAAA,CAACH,SAAS;IAAAa,QAAA,EACLR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,kBACjBb,OAAA,CAACF,eAAe;MAEZc,IAAI,EAAEA,IAAK;MACXT,UAAU,EAAEA,UAAW;MACvBM,KAAK,EAAEI,GAAI;MACXT,mBAAmB,EAAEA,mBAAoB;MACzCE,cAAc,EAAEA,cAAe;MAC/BQ,eAAe,EAAEA,CAAA,KAAMN,iBAAiB,CAACK,GAAG,CAAE;MAC9CE,gBAAgB,EAAEA,CAAA,KAAM;QACpBR,iBAAiB,CAAC,IAAI,CAAC;MAC3B;IAAE,GATGM,GAAG;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAUX,CACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEpB,CAAC;AAACd,EAAA,CAxBIJ,eAAgC;AAAAmB,EAAA,GAAhCnB,eAAgC;AA0BtC,eAAeA,eAAe;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}