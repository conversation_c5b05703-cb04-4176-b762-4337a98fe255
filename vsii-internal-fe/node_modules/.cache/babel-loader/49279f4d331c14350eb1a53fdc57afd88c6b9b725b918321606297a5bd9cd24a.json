{"ast": null, "code": "import { appearStoreId } from './store-id.mjs';\nimport { animateStyle } from '../waapi/index.mjs';\nimport { optimizedAppearDataId } from './data-id.mjs';\nfunction startOptimizedAppearAnimation(element, name, keyframes, options) {\n  window.MotionAppearAnimations || (window.MotionAppearAnimations = new Map());\n  const id = element.dataset[optimizedAppearDataId];\n  const animation = animateStyle(element, name, keyframes, options);\n  if (id && animation) {\n    window.MotionAppearAnimations.set(appearStoreId(id, name), animation);\n  }\n  return animation;\n}\nexport { startOptimizedAppearAnimation };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}