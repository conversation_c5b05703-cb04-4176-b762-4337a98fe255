{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./sha256\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./sha256\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var WordArray = C_lib.WordArray;\n    var C_algo = C.algo;\n    var SHA256 = C_algo.SHA256;\n\n    /**\n     * SHA-224 hash algorithm.\n     */\n    var SHA224 = C_algo.SHA224 = SHA256.extend({\n      _doReset: function () {\n        this._hash = new WordArray.init([0xc1059ed8, 0x367cd507, 0x3070dd17, 0xf70e5939, 0xffc00b31, 0x68581511, 0x64f98fa7, 0xbefa4fa4]);\n      },\n      _doFinalize: function () {\n        var hash = SHA256._doFinalize.call(this);\n        hash.sigBytes -= 4;\n        return hash;\n      }\n    });\n\n    /**\n     * Shortcut function to the hasher's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     *\n     * @return {WordArray} The hash.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hash = CryptoJS.SHA224('message');\n     *     var hash = CryptoJS.SHA224(wordArray);\n     */\n    C.SHA224 = SHA256._createHelper(SHA224);\n\n    /**\n     * Shortcut function to the HMAC's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     * @param {WordArray|string} key The secret key.\n     *\n     * @return {WordArray} The HMAC.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hmac = CryptoJS.HmacSHA224(message, key);\n     */\n    C.HmacSHA224 = SHA256._createHmacHelper(SHA224);\n  })();\n  return CryptoJS.SHA224;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}