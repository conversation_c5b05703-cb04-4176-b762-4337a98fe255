{"ast": null, "code": "import{FormattedMessage}from'react-intl';// material-ui\nimport{TableCell,TableHead,TableRow}from'@mui/material';// project imports\nimport{checkAllowedPermission}from'utils/authorization';import{PERMISSIONS}from'constants/Permission';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ManageHolidayThead=()=>{const{holidayPermission}=PERMISSIONS.admin;const{Manage_holidays}=TEXT_CONFIG_SCREEN.administration;return/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:Manage_holidays+'no'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:Manage_holidays+'from-date'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:Manage_holidays+'to-date'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:Manage_holidays+'holiday-type'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:Manage_holidays+'note'})}),checkAllowedPermission(holidayPermission.edit)&&/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:Manage_holidays+'action'})})]})});};export default ManageHolidayThead;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}