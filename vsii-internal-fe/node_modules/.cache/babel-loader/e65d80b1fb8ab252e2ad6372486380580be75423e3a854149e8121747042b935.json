{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/SaleTableTHead.tsx\";\nimport { FormattedMessage } from 'react-intl';\nimport { TableCell, TableHead, TableRow, styled } from '@mui/material';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RedAsterisk = styled('span')({\n  color: 'red',\n  paddingLeft: '4px'\n});\n_c = RedAsterisk;\nconst SaleTableTHead = props => {\n  const {\n    dataArray\n  } = props;\n  const {\n    salesReport\n  } = TEXT_CONFIG_SCREEN;\n  return /*#__PURE__*/_jsxDEV(TableHead, {\n    children: /*#__PURE__*/_jsxDEV(TableRow, {\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"center\",\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: \"no\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.monthlyProductionPerformance + '-role'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 21\n        }, this), (dataArray === null || dataArray === void 0 ? void 0 : dataArray.length) > 0 && /*#__PURE__*/_jsxDEV(RedAsterisk, {\n          children: \"*\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 47\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.monthlyProductionPerformance + '-rate'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 21\n        }, this), (dataArray === null || dataArray === void 0 ? void 0 : dataArray.length) > 0 && /*#__PURE__*/_jsxDEV(RedAsterisk, {\n          children: \"*\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 47\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.monthlyProductionPerformance + '-rate-usd'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 21\n        }, this), (dataArray === null || dataArray === void 0 ? void 0 : dataArray.length) > 0 && /*#__PURE__*/_jsxDEV(RedAsterisk, {\n          children: \"*\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 47\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.monthlyProductionPerformance + '-quantity'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 21\n        }, this), (dataArray === null || dataArray === void 0 ? void 0 : dataArray.length) > 0 && /*#__PURE__*/_jsxDEV(RedAsterisk, {\n          children: \"*\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 47\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"center\",\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.monthlyProductionPerformance + '-amount'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"center\",\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.monthlyProductionPerformance + '-action'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 9\n  }, this);\n};\n_c2 = SaleTableTHead;\nexport default SaleTableTHead;\nvar _c, _c2;\n$RefreshReg$(_c, \"RedAsterisk\");\n$RefreshReg$(_c2, \"SaleTableTHead\");", "map": {"version": 3, "names": ["FormattedMessage", "TableCell", "TableHead", "TableRow", "styled", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "RedAsterisk", "color", "paddingLeft", "_c", "SaleTableTHead", "props", "dataArray", "salesReport", "children", "align", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "monthlyProductionPerformance", "length", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/SaleTableTHead.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\nimport { TableCell, TableHead, TableRow, styled } from '@mui/material';\n\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\n\ninterface ISaleTableTHeadProps {\n    dataArray?: any;\n}\n\nconst RedAsterisk = styled('span')({\n    color: 'red',\n    paddingLeft: '4px'\n});\n\nconst SaleTableTHead = (props: ISaleTableTHeadProps) => {\n    const { dataArray } = props;\n\n    const { salesReport } = TEXT_CONFIG_SCREEN;\n\n    return (\n        <TableHead>\n            <TableRow>\n                <TableCell align=\"center\">\n                    <FormattedMessage id=\"no\" />\n                </TableCell>\n                <TableCell align=\"center\">\n                    <FormattedMessage id={salesReport.monthlyProductionPerformance + '-role'} />\n                    {dataArray?.length > 0 && <RedAsterisk>*</RedAsterisk>}\n                </TableCell>\n                <TableCell align=\"center\">\n                    <FormattedMessage id={salesReport.monthlyProductionPerformance + '-rate'} />\n                    {dataArray?.length > 0 && <RedAsterisk>*</RedAsterisk>}\n                </TableCell>\n                <TableCell align=\"center\">\n                    <FormattedMessage id={salesReport.monthlyProductionPerformance + '-rate-usd'} />\n                    {dataArray?.length > 0 && <RedAsterisk>*</RedAsterisk>}\n                </TableCell>\n                <TableCell align=\"center\">\n                    <FormattedMessage id={salesReport.monthlyProductionPerformance + '-quantity'} />\n                    {dataArray?.length > 0 && <RedAsterisk>*</RedAsterisk>}\n                </TableCell>\n                <TableCell align=\"center\">\n                    <FormattedMessage id={salesReport.monthlyProductionPerformance + '-amount'} />\n                </TableCell>\n                <TableCell align=\"center\">\n                    <FormattedMessage id={salesReport.monthlyProductionPerformance + '-action'} />\n                </TableCell>\n            </TableRow>\n        </TableHead>\n    );\n};\n\nexport default SaleTableTHead;\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,eAAe;AAEtE,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMtD,MAAMC,WAAW,GAAGJ,MAAM,CAAC,MAAM,CAAC,CAAC;EAC/BK,KAAK,EAAE,KAAK;EACZC,WAAW,EAAE;AACjB,CAAC,CAAC;AAACC,EAAA,GAHGH,WAAW;AAKjB,MAAMI,cAAc,GAAIC,KAA2B,IAAK;EACpD,MAAM;IAAEC;EAAU,CAAC,GAAGD,KAAK;EAE3B,MAAM;IAAEE;EAAY,CAAC,GAAGV,kBAAkB;EAE1C,oBACIE,OAAA,CAACL,SAAS;IAAAc,QAAA,eACNT,OAAA,CAACJ,QAAQ;MAAAa,QAAA,gBACLT,OAAA,CAACN,SAAS;QAACgB,KAAK,EAAC,QAAQ;QAAAD,QAAA,eACrBT,OAAA,CAACP,gBAAgB;UAACkB,EAAE,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACZf,OAAA,CAACN,SAAS;QAACgB,KAAK,EAAC,QAAQ;QAAAD,QAAA,gBACrBT,OAAA,CAACP,gBAAgB;UAACkB,EAAE,EAAEH,WAAW,CAACQ,4BAA4B,GAAG;QAAQ;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC3E,CAAAR,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEU,MAAM,IAAG,CAAC,iBAAIjB,OAAA,CAACC,WAAW;UAAAQ,QAAA,EAAC;QAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACZf,OAAA,CAACN,SAAS;QAACgB,KAAK,EAAC,QAAQ;QAAAD,QAAA,gBACrBT,OAAA,CAACP,gBAAgB;UAACkB,EAAE,EAAEH,WAAW,CAACQ,4BAA4B,GAAG;QAAQ;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC3E,CAAAR,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEU,MAAM,IAAG,CAAC,iBAAIjB,OAAA,CAACC,WAAW;UAAAQ,QAAA,EAAC;QAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACZf,OAAA,CAACN,SAAS;QAACgB,KAAK,EAAC,QAAQ;QAAAD,QAAA,gBACrBT,OAAA,CAACP,gBAAgB;UAACkB,EAAE,EAAEH,WAAW,CAACQ,4BAA4B,GAAG;QAAY;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC/E,CAAAR,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEU,MAAM,IAAG,CAAC,iBAAIjB,OAAA,CAACC,WAAW;UAAAQ,QAAA,EAAC;QAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACZf,OAAA,CAACN,SAAS;QAACgB,KAAK,EAAC,QAAQ;QAAAD,QAAA,gBACrBT,OAAA,CAACP,gBAAgB;UAACkB,EAAE,EAAEH,WAAW,CAACQ,4BAA4B,GAAG;QAAY;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC/E,CAAAR,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEU,MAAM,IAAG,CAAC,iBAAIjB,OAAA,CAACC,WAAW;UAAAQ,QAAA,EAAC;QAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACZf,OAAA,CAACN,SAAS;QAACgB,KAAK,EAAC,QAAQ;QAAAD,QAAA,eACrBT,OAAA,CAACP,gBAAgB;UAACkB,EAAE,EAAEH,WAAW,CAACQ,4BAA4B,GAAG;QAAU;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eACZf,OAAA,CAACN,SAAS;QAACgB,KAAK,EAAC,QAAQ;QAAAD,QAAA,eACrBT,OAAA,CAACP,gBAAgB;UAACkB,EAAE,EAAEH,WAAW,CAACQ,4BAA4B,GAAG;QAAU;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEpB,CAAC;AAACG,GAAA,GApCIb,cAAc;AAsCpB,eAAeA,cAAc;AAAC,IAAAD,EAAA,EAAAc,GAAA;AAAAC,YAAA,CAAAf,EAAA;AAAAe,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}