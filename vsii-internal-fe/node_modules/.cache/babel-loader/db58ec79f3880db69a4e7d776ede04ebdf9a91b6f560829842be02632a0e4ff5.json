{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nconst initialState = {\n  show: false,\n  isTabWrap: false\n};\nconst deniedPermissionSlice = createSlice({\n  name: 'deniedPermission',\n  initialState,\n  reducers: {\n    openDeniedPermission(state, action) {\n      state.show = true;\n      state.isTabWrap = !!action.payload;\n    },\n    closeDeniedPermission(state) {\n      state.show = false;\n      state.isTabWrap = false;\n    }\n  }\n});\nexport default deniedPermissionSlice.reducer;\nexport const {\n  closeDeniedPermission,\n  openDeniedPermission\n} = deniedPermissionSlice.actions;", "map": {"version": 3, "names": ["createSlice", "initialState", "show", "isTabWrap", "deniedPermissionSlice", "name", "reducers", "openDeniedPermission", "state", "action", "payload", "closeDeniedPermission", "reducer", "actions"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/store/slice/deniedPermissionSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\n\nconst initialState = {\n    show: false,\n    isTabWrap: false\n};\n\nconst deniedPermissionSlice = createSlice({\n    name: 'deniedPermission',\n    initialState,\n    reducers: {\n        openDeniedPermission(state, action: PayloadAction<boolean | undefined>) {\n            state.show = true;\n            state.isTabWrap = !!action.payload;\n        },\n\n        closeDeniedPermission(state) {\n            state.show = false;\n            state.isTabWrap = false;\n        }\n    }\n});\n\nexport default deniedPermissionSlice.reducer;\n\nexport const { closeDeniedPermission, openDeniedPermission } = deniedPermissionSlice.actions;\n"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AAE7D,MAAMC,YAAY,GAAG;EACjBC,IAAI,EAAE,KAAK;EACXC,SAAS,EAAE;AACf,CAAC;AAED,MAAMC,qBAAqB,GAAGJ,WAAW,CAAC;EACtCK,IAAI,EAAE,kBAAkB;EACxBJ,YAAY;EACZK,QAAQ,EAAE;IACNC,oBAAoBA,CAACC,KAAK,EAAEC,MAA0C,EAAE;MACpED,KAAK,CAACN,IAAI,GAAG,IAAI;MACjBM,KAAK,CAACL,SAAS,GAAG,CAAC,CAACM,MAAM,CAACC,OAAO;IACtC,CAAC;IAEDC,qBAAqBA,CAACH,KAAK,EAAE;MACzBA,KAAK,CAACN,IAAI,GAAG,KAAK;MAClBM,KAAK,CAACL,SAAS,GAAG,KAAK;IAC3B;EACJ;AACJ,CAAC,CAAC;AAEF,eAAeC,qBAAqB,CAACQ,OAAO;AAE5C,OAAO,MAAM;EAAED,qBAAqB;EAAEJ;AAAqB,CAAC,GAAGH,qBAAqB,CAACS,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}