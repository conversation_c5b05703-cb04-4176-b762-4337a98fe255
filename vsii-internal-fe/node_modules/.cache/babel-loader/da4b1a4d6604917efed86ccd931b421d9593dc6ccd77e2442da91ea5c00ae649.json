{"ast": null, "code": "import { useDrag } from '../../gestures/drag/use-drag.mjs';\nimport { usePanGesture } from '../../gestures/use-pan-gesture.mjs';\nimport { makeRenderlessComponent } from '../utils/make-renderless-component.mjs';\nconst drag = {\n  pan: makeRenderlessComponent(usePanGesture),\n  drag: makeRenderlessComponent(useDrag)\n};\nexport { drag };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}