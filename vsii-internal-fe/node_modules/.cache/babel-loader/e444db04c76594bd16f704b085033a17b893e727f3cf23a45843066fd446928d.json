{"ast": null, "code": "const testAnimation = (keyframes, options) => document.createElement(\"div\").animate(keyframes, options);\nconst featureTests = {\n  cssRegisterProperty: () => typeof CSS !== \"undefined\" && Object.hasOwnProperty.call(CSS, \"registerProperty\"),\n  waapi: () => Object.hasOwnProperty.call(Element.prototype, \"animate\"),\n  partialKeyframes: () => {\n    try {\n      testAnimation({\n        opacity: [1]\n      });\n    } catch (e) {\n      return false;\n    }\n    return true;\n  },\n  finished: () => Boolean(testAnimation({\n    opacity: [0, 1]\n  }, {\n    duration: 0.001\n  }).finished),\n  linearEasing: () => {\n    try {\n      testAnimation({\n        opacity: 0\n      }, {\n        easing: \"linear(0, 1)\"\n      });\n    } catch (e) {\n      return false;\n    }\n    return true;\n  }\n};\nconst results = {};\nconst supports = {};\nfor (const key in featureTests) {\n  supports[key] = () => {\n    if (results[key] === undefined) results[key] = featureTests[key]();\n    return results[key];\n  };\n}\nexport { supports };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}