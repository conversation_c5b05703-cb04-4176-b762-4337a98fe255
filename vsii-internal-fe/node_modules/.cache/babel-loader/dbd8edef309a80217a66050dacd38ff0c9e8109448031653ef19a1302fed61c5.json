{"ast": null, "code": "import React from'react';import{<PERSON><PERSON><PERSON><PERSON>on,TableBody,TableCell,TableRow,Tooltip,Stack}from'@mui/material';import EditTwoToneIcon from'@mui/icons-material/EditTwoTone';import{FormattedMessage}from'react-intl';import{checkAllowedPermission}from'utils/authorization';import{Checkbox}from'components/extended/Form';import{PERMISSIONS}from'constants/Permission';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ColumnConfigTBody=_ref=>{let{data,handleOpen,pageNumber,pageSize}=_ref;const{flexibleReportingConfigPermission}=PERMISSIONS.admin;return/*#__PURE__*/_jsx(TableBody,{children:data===null||data===void 0?void 0:data.map((item,key)=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:pageSize*(pageNumber-1)+key+1}),/*#__PURE__*/_jsx(TableCell,{children:item.reportName}),/*#__PURE__*/_jsx(TableCell,{children:item.columnName}),/*#__PURE__*/_jsx(TableCell,{sx:{textTransform:'capitalize'},children:item.inputType}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Checkbox,{name:\"\",isControl:false,disabled:true,valueChecked:item.isCalculate})}),/*#__PURE__*/_jsx(TableCell,{children:checkAllowedPermission(flexibleReportingConfigPermission.columnConfig.edit)&&/*#__PURE__*/_jsx(Stack,{direction:\"row\",justifyContent:\"center\",alignItems:\"center\",children:/*#__PURE__*/_jsx(Tooltip,{placement:\"top\",title:/*#__PURE__*/_jsx(FormattedMessage,{id:'edit'}),onClick:()=>handleOpen(item),children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"edit\",size:\"small\",children:/*#__PURE__*/_jsx(EditTwoToneIcon,{sx:{fontSize:'1.1rem'}})})})})})]},key))});};export default ColumnConfigTBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}