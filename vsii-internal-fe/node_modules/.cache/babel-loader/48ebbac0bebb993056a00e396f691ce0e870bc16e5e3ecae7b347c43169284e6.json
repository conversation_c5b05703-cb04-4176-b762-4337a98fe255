{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { filterProps } from './utils';\nimport { FormatError, ErrorCode } from 'intl-messageformat';\nimport { IntlFormatError } from './error';\nvar LIST_FORMAT_OPTIONS = ['type', 'style'];\nvar now = Date.now();\nfunction generateToken(i) {\n  return \"\".concat(now, \"_\").concat(i, \"_\").concat(now);\n}\nexport function formatList(opts, getListFormat, values, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var results = formatListToParts(opts, getListFormat, values, options).reduce(function (all, el) {\n    var val = el.value;\n    if (typeof val !== 'string') {\n      all.push(val);\n    } else if (typeof all[all.length - 1] === 'string') {\n      all[all.length - 1] += val;\n    } else {\n      all.push(val);\n    }\n    return all;\n  }, []);\n  return results.length === 1 ? results[0] : results.length === 0 ? '' : results;\n}\nexport function formatListToParts(_a, getListFormat, values, options) {\n  var locale = _a.locale,\n    onError = _a.onError;\n  if (options === void 0) {\n    options = {};\n  }\n  var ListFormat = Intl.ListFormat;\n  if (!ListFormat) {\n    onError(new FormatError(\"Intl.ListFormat is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-listformat\\\"\\n\", ErrorCode.MISSING_INTL_API));\n  }\n  var filteredOptions = filterProps(options, LIST_FORMAT_OPTIONS);\n  try {\n    var richValues_1 = {};\n    var serializedValues = values.map(function (v, i) {\n      if (typeof v === 'object') {\n        var id = generateToken(i);\n        richValues_1[id] = v;\n        return id;\n      }\n      return String(v);\n    });\n    return getListFormat(locale, filteredOptions).formatToParts(serializedValues).map(function (part) {\n      return part.type === 'literal' ? part : __assign(__assign({}, part), {\n        value: richValues_1[part.value] || part.value\n      });\n    });\n  } catch (e) {\n    onError(new IntlFormatError('Error formatting list.', locale, e));\n  }\n  // @ts-ignore\n  return values;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}