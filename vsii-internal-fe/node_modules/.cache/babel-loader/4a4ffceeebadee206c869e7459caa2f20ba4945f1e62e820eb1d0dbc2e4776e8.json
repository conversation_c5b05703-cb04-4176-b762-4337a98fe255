{"ast": null, "code": "import { isNumber } from '@motionone/utils';\nfunction calcNextTime(current, next, prev, labels) {\n  var _a;\n  if (isNumber(next)) {\n    return next;\n  } else if (next.startsWith(\"-\") || next.startsWith(\"+\")) {\n    return Math.max(0, current + parseFloat(next));\n  } else if (next === \"<\") {\n    return prev;\n  } else {\n    return (_a = labels.get(next)) !== null && _a !== void 0 ? _a : current;\n  }\n}\nexport { calcNextTime };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}