{"ast": null, "code": "import { __extends } from \"tslib\";\nexport var IntlErrorCode;\n(function (IntlErrorCode) {\n  IntlErrorCode[\"FORMAT_ERROR\"] = \"FORMAT_ERROR\";\n  IntlErrorCode[\"UNSUPPORTED_FORMATTER\"] = \"UNSUPPORTED_FORMATTER\";\n  IntlErrorCode[\"INVALID_CONFIG\"] = \"INVALID_CONFIG\";\n  IntlErrorCode[\"MISSING_DATA\"] = \"MISSING_DATA\";\n  IntlErrorCode[\"MISSING_TRANSLATION\"] = \"MISSING_TRANSLATION\";\n})(IntlErrorCode || (IntlErrorCode = {}));\nvar IntlError = /** @class */function (_super) {\n  __extends(IntlError, _super);\n  function IntlError(code, message, exception) {\n    var _this = this;\n    var err = exception ? exception instanceof Error ? exception : new Error(String(exception)) : undefined;\n    _this = _super.call(this, \"[@formatjs/intl Error \".concat(code, \"] \").concat(message, \"\\n\").concat(err ? \"\\n\".concat(err.message, \"\\n\").concat(err.stack) : '')) || this;\n    _this.code = code;\n    // @ts-ignore just so we don't need to declare dep on @types/node\n    if (typeof Error.captureStackTrace === 'function') {\n      // @ts-ignore just so we don't need to declare dep on @types/node\n      Error.captureStackTrace(_this, IntlError);\n    }\n    return _this;\n  }\n  return IntlError;\n}(Error);\nexport { IntlError };\nvar UnsupportedFormatterError = /** @class */function (_super) {\n  __extends(UnsupportedFormatterError, _super);\n  function UnsupportedFormatterError(message, exception) {\n    return _super.call(this, IntlErrorCode.UNSUPPORTED_FORMATTER, message, exception) || this;\n  }\n  return UnsupportedFormatterError;\n}(IntlError);\nexport { UnsupportedFormatterError };\nvar InvalidConfigError = /** @class */function (_super) {\n  __extends(InvalidConfigError, _super);\n  function InvalidConfigError(message, exception) {\n    return _super.call(this, IntlErrorCode.INVALID_CONFIG, message, exception) || this;\n  }\n  return InvalidConfigError;\n}(IntlError);\nexport { InvalidConfigError };\nvar MissingDataError = /** @class */function (_super) {\n  __extends(MissingDataError, _super);\n  function MissingDataError(message, exception) {\n    return _super.call(this, IntlErrorCode.MISSING_DATA, message, exception) || this;\n  }\n  return MissingDataError;\n}(IntlError);\nexport { MissingDataError };\nvar IntlFormatError = /** @class */function (_super) {\n  __extends(IntlFormatError, _super);\n  function IntlFormatError(message, locale, exception) {\n    var _this = _super.call(this, IntlErrorCode.FORMAT_ERROR, \"\".concat(message, \"\\nLocale: \").concat(locale, \"\\n\"), exception) || this;\n    _this.locale = locale;\n    return _this;\n  }\n  return IntlFormatError;\n}(IntlError);\nexport { IntlFormatError };\nvar MessageFormatError = /** @class */function (_super) {\n  __extends(MessageFormatError, _super);\n  function MessageFormatError(message, locale, descriptor, exception) {\n    var _this = _super.call(this, \"\".concat(message, \"\\nMessageID: \").concat(descriptor === null || descriptor === void 0 ? void 0 : descriptor.id, \"\\nDefault Message: \").concat(descriptor === null || descriptor === void 0 ? void 0 : descriptor.defaultMessage, \"\\nDescription: \").concat(descriptor === null || descriptor === void 0 ? void 0 : descriptor.description, \"\\n\"), locale, exception) || this;\n    _this.descriptor = descriptor;\n    _this.locale = locale;\n    return _this;\n  }\n  return MessageFormatError;\n}(IntlFormatError);\nexport { MessageFormatError };\nvar MissingTranslationError = /** @class */function (_super) {\n  __extends(MissingTranslationError, _super);\n  function MissingTranslationError(descriptor, locale) {\n    var _this = _super.call(this, IntlErrorCode.MISSING_TRANSLATION, \"Missing message: \\\"\".concat(descriptor.id, \"\\\" for locale \\\"\").concat(locale, \"\\\", using \").concat(descriptor.defaultMessage ? \"default message (\".concat(typeof descriptor.defaultMessage === 'string' ? descriptor.defaultMessage : descriptor.defaultMessage.map(function (e) {\n      var _a;\n      return (_a = e.value) !== null && _a !== void 0 ? _a : JSON.stringify(e);\n    }).join(), \")\") : 'id', \" as fallback.\")) || this;\n    _this.descriptor = descriptor;\n    return _this;\n  }\n  return MissingTranslationError;\n}(IntlError);\nexport { MissingTranslationError };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}