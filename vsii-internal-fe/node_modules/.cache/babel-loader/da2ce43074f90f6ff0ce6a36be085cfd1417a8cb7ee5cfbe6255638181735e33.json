{"ast": null, "code": "import { getPickersLocalization } from './utils/getPickersLocalization';\nconst ptBRPickers = {\n  // Calendar navigation\n  previousMonth: 'Mês anterior',\n  nextMonth: 'Próximo mês',\n  // View navigation\n  openPreviousView: 'Abrir próxima seleção',\n  openNextView: 'Abrir seleção anterior',\n  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'Seleção de ano está aberta, alternando para seleção de calendário' : 'Seleção de calendários está aberta, alternando para seleção de ano',\n  // inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `text input view is open, go to ${viewType} view` : `${viewType} view is open, go to text input view`,\n  // DateRange placeholders\n  start: 'Início',\n  end: 'Fim',\n  // Action bar\n  cancelButtonLabel: 'Cancelar',\n  clearButtonLabel: 'Lim<PERSON>',\n  okButtonLabel: 'OK',\n  todayButtonLabel: 'Hoje',\n  // Toolbar titles\n  datePickerDefaultToolbarTitle: 'Selecione a data',\n  dateTimePickerDefaultToolbarTitle: 'Selecione data e hora',\n  timePickerDefaultToolbarTitle: 'Selecione a hora',\n  dateRangePickerDefaultToolbarTitle: 'Selecione o intervalo entre datas',\n  // Clock labels\n  clockLabelText: (view, time, adapter) => \"Selecione \".concat(view, \". \").concat(time === null ? 'Hora não selecionada' : \"Selecionado a hora \".concat(adapter.format(time, 'fullTime'))),\n  hoursClockNumberText: hours => \"\".concat(hours, \" horas\"),\n  minutesClockNumberText: minutes => \"\".concat(minutes, \" minutos\"),\n  secondsClockNumberText: seconds => \"\".concat(seconds, \" segundos\"),\n  // Open picker labels\n  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? \"Escolha uma data, data selecionada \".concat(utils.format(utils.date(rawValue), 'fullDate')) : 'Escolha uma data',\n  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? \"Escolha uma hora, hora selecionada \".concat(utils.format(utils.date(rawValue), 'fullTime')) : 'Escolha uma hora',\n  // Table labels\n  timeTableLabel: 'escolha uma hora',\n  dateTableLabel: 'escolha uma data'\n};\nexport const ptBR = getPickersLocalization(ptBRPickers);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}