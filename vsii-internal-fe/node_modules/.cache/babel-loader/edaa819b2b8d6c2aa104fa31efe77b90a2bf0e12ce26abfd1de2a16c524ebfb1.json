{"ast": null, "code": "// material-ui\nimport{CircularProgress,TableBody,TableCell,TableRow}from'@mui/material';import{jsx as _jsx}from\"react/jsx-runtime\";const TableLoading=()=>{return/*#__PURE__*/_jsx(TableBody,{children:/*#__PURE__*/_jsx(TableRow,{children:/*#__PURE__*/_jsx(TableCell,{align:\"center\",colSpan:100,sx:{height:'200px'},children:/*#__PURE__*/_jsx(CircularProgress,{})})})});};export default TableLoading;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}