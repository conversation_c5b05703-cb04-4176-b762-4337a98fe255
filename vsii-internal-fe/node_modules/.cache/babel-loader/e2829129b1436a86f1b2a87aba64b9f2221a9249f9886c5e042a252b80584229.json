{"ast": null, "code": "import { getPickersLocalization } from './utils/getPickersLocalization';\nconst ukUAPickers = {\n  // Calendar navigation\n  previousMonth: 'Попередній місяць',\n  nextMonth: 'Наступний місяць',\n  // View navigation\n  openPreviousView: 'відкрити попередній вигляд',\n  openNextView: 'відкрити наступний вигляд',\n  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'річний вигляд відкрито, перейти до календарного вигляду' : 'календарний вигляд відкрито, перейти до річного вигляду',\n  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? \"\\u0442\\u0435\\u043A\\u0441\\u0442\\u043E\\u0432\\u0435 \\u043F\\u043E\\u043B\\u0435 \\u0432\\u0456\\u0434\\u043A\\u0440\\u0438\\u0442\\u0435, \\u043F\\u0435\\u0440\\u0435\\u0439\\u0442\\u0438 \\u0434\\u043E  \".concat(viewType, \" \\u0432\\u0438\\u0433\\u043B\\u044F\\u0434\\u0443\") : \"\".concat(viewType, \" \\u0432\\u0438\\u0433\\u043B\\u044F\\u0434 \\u043D\\u0430\\u0440\\u0430\\u0437\\u0456 \\u0432\\u0456\\u0434\\u043A\\u0440\\u0438\\u0442\\u043E, \\u043F\\u0435\\u0440\\u0435\\u0439\\u0442\\u0438 \\u0434\\u043E \\u0442\\u0435\\u043A\\u0441\\u0442\\u043E\\u0432\\u043E\\u0433\\u043E \\u043F\\u043E\\u043B\\u044F\"),\n  // DateRange placeholders\n  start: 'Початок',\n  end: 'Кінець',\n  // Action bar\n  cancelButtonLabel: 'Відміна',\n  clearButtonLabel: 'Очистити',\n  okButtonLabel: 'OK',\n  todayButtonLabel: 'Сьогодні',\n  // Toolbar titles\n  datePickerDefaultToolbarTitle: 'Вибрати дату',\n  dateTimePickerDefaultToolbarTitle: 'Вибрати дату і час',\n  timePickerDefaultToolbarTitle: 'Вибрати час',\n  dateRangePickerDefaultToolbarTitle: 'Вибрати календарний період',\n  // Clock labels\n  clockLabelText: (view, time, adapter) => \"Select \".concat(view, \". \").concat(time === null ? 'Час не вибраний' : \"\\u0412\\u0438\\u0431\\u0440\\u0430\\u043D\\u043E \\u0447\\u0430\\u0441 \".concat(adapter.format(time, 'fullTime'))),\n  hoursClockNumberText: hours => \"\".concat(hours, \" \\u0433\\u043E\\u0434\\u0438\\u043D\"),\n  minutesClockNumberText: minutes => \"\".concat(minutes, \" \\u0445\\u0432\\u0438\\u043B\\u0438\\u043D\"),\n  secondsClockNumberText: seconds => \"\".concat(seconds, \" \\u0441\\u0435\\u043A\\u0443\\u043D\\u0434\"),\n  // Open picker labels\n  openDatePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? \"\\u041E\\u0431\\u0435\\u0440\\u0456\\u0442\\u044C \\u0434\\u0430\\u0442\\u0443, \\u043E\\u0431\\u0440\\u0430\\u043D\\u0430 \\u0434\\u0430\\u0442\\u0430  \".concat(utils.format(value, 'fullDate')) : 'Оберіть дату',\n  openTimePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? \"\\u041E\\u0431\\u0435\\u0440\\u0456\\u0442\\u044C \\u0447\\u0430\\u0441, \\u043E\\u0431\\u0440\\u0430\\u043D\\u0438\\u0439 \\u0447\\u0430\\u0441  \".concat(utils.format(value, 'fullTime')) : 'Оберіть час',\n  // Table labels\n  timeTableLabel: 'оберіть час',\n  dateTableLabel: 'оберіть дату'\n};\nexport const ukUA = getPickersLocalization(ukUAPickers);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}