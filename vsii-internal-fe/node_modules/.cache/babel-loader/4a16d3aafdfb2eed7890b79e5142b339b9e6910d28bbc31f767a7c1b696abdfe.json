{"ast": null, "code": "import { getPickersLocalization } from './utils/getPickersLocalization';\nconst views = {\n  hours: '시간을',\n  minutes: '분을',\n  seconds: '초를'\n};\nconst koKRPickers = {\n  // Calendar navigation\n  previousMonth: '이전 달',\n  nextMonth: '다음 달',\n  // View navigation\n  openPreviousView: '이전 화면 보기',\n  openNextView: '다음 화면 보기',\n  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? '연도 선택 화면에서 달력 화면으로 전환하기' : '달력 화면에서 연도 선택 화면으로 전환하기',\n  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? \"\\uD14D\\uC2A4\\uD2B8 \\uC785\\uB825 \\uD654\\uBA74\\uC5D0\\uC11C \".concat(viewType, \" \\uD654\\uBA74\\uC73C\\uB85C \\uC804\\uD658\\uD558\\uAE30\") : \"\".concat(viewType, \" \\uD654\\uBA74\\uC5D0\\uC11C \\uD14D\\uC2A4\\uD2B8 \\uC785\\uB825 \\uD654\\uBA74\\uC73C\\uB85C \\uC804\\uD658\\uD558\\uAE30\"),\n  // DateRange placeholders\n  start: '시작',\n  end: '종료',\n  // Action bar\n  cancelButtonLabel: '취소',\n  clearButtonLabel: '초기화',\n  okButtonLabel: '확인',\n  todayButtonLabel: '오늘',\n  // Toolbar titles\n  datePickerDefaultToolbarTitle: '날짜 선택하기',\n  dateTimePickerDefaultToolbarTitle: '날짜 & 시간 선택하기',\n  timePickerDefaultToolbarTitle: '시간 선택하기',\n  dateRangePickerDefaultToolbarTitle: '날짜 범위 선택하기',\n  // Clock labels\n  clockLabelText: (view, time, adapter) => \"\".concat(views[view], \" \\uC120\\uD0DD\\uD558\\uC138\\uC694. \").concat(time === null ? '시간을 선택하지 않았습니다.' : \"\\uD604\\uC7AC \\uC120\\uD0DD\\uB41C \\uC2DC\\uAC04\\uC740 \".concat(adapter.format(time, 'fullTime'), \"\\uC785\\uB2C8\\uB2E4.\")),\n  hoursClockNumberText: hours => \"\".concat(hours, \"\\uC2DC\\uAC04\"),\n  minutesClockNumberText: minutes => \"\".concat(minutes, \"\\uBD84\"),\n  secondsClockNumberText: seconds => \"\".concat(seconds, \"\\uCD08\"),\n  // Open picker labels\n  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? \"\\uB0A0\\uC9DC\\uB97C \\uC120\\uD0DD\\uD558\\uC138\\uC694. \\uD604\\uC7AC \\uC120\\uD0DD\\uB41C \\uB0A0\\uC9DC\\uB294 \".concat(utils.format(utils.date(rawValue), 'fullDate'), \"\\uC785\\uB2C8\\uB2E4.\") : '날짜를 선택하세요',\n  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? \"\\uC2DC\\uAC04\\uC744 \\uC120\\uD0DD\\uD558\\uC138\\uC694. \\uD604\\uC7AC \\uC120\\uD0DD\\uB41C \\uC2DC\\uAC04\\uC740 \".concat(utils.format(utils.date(rawValue), 'fullTime'), \"\\uC785\\uB2C8\\uB2E4.\") : '시간을 선택하세요',\n  // Table labels\n  timeTableLabel: '선택한 시간',\n  dateTableLabel: '선택한 날짜'\n};\nexport const koKR = getPickersLocalization(koKRPickers);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}