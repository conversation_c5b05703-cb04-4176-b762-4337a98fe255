{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/GroupType.tsx\";\nimport React from 'react';\n\n// project imports\nimport { Select } from 'components/extended/Form';\nimport { searchFormConfig } from './Config';\nimport { DEFAULT_VALUE_OPTION, DEFAULT_VALUE_OPTION_SELECT, GROUP_TYPE } from 'constants/Common';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst GroupType = props => {\n  const {\n    isShowAll,\n    required,\n    label\n  } = props;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Select, {\n      required: required,\n      selects: !isShowAll ? [DEFAULT_VALUE_OPTION, ...GROUP_TYPE] : [DEFAULT_VALUE_OPTION_SELECT, ...GROUP_TYPE],\n      name: searchFormConfig.groupType.name,\n      label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n        id: label || searchFormConfig.groupType.label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 24\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_c = GroupType;\nGroupType.defaultProps = {\n  isShowAll: true,\n  required: false\n};\nexport default GroupType;\nvar _c;\n$RefreshReg$(_c, \"GroupType\");", "map": {"version": 3, "names": ["React", "Select", "searchFormConfig", "DEFAULT_VALUE_OPTION", "DEFAULT_VALUE_OPTION_SELECT", "GROUP_TYPE", "FormattedMessage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "GroupType", "props", "isShowAll", "required", "label", "children", "selects", "name", "groupType", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "defaultProps", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/GroupType.tsx"], "sourcesContent": ["import React from 'react';\n\n// project imports\nimport { Select } from 'components/extended/Form';\nimport { searchFormConfig } from './Config';\nimport { DEFAULT_VALUE_OPTION, DEFAULT_VALUE_OPTION_SELECT, GROUP_TYPE } from 'constants/Common';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\n\ninterface IGroupTypeProps {\n    isShowAll: boolean;\n    required: boolean;\n    label?: string;\n}\n\nconst GroupType = (props: IGroupTypeProps) => {\n    const { isShowAll, required, label } = props;\n    return (\n        <>\n            <Select\n                required={required}\n                selects={!isShowAll ? [DEFAULT_VALUE_OPTION, ...GROUP_TYPE] : [DEFAULT_VALUE_OPTION_SELECT, ...GROUP_TYPE]}\n                name={searchFormConfig.groupType.name}\n                label={<FormattedMessage id={label || searchFormConfig.groupType.label} />}\n            />\n        </>\n    );\n};\n\nGroupType.defaultProps = {\n    isShowAll: true,\n    required: false\n};\n\nexport default GroupType;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,gBAAgB,QAAQ,UAAU;AAC3C,SAASC,oBAAoB,EAAEC,2BAA2B,EAAEC,UAAU,QAAQ,kBAAkB;;AAEhG;AACA,SAASC,gBAAgB,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQ9C,MAAMC,SAAS,GAAIC,KAAsB,IAAK;EAC1C,MAAM;IAAEC,SAAS;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAGH,KAAK;EAC5C,oBACIJ,OAAA,CAAAE,SAAA;IAAAM,QAAA,eACIR,OAAA,CAACP,MAAM;MACHa,QAAQ,EAAEA,QAAS;MACnBG,OAAO,EAAE,CAACJ,SAAS,GAAG,CAACV,oBAAoB,EAAE,GAAGE,UAAU,CAAC,GAAG,CAACD,2BAA2B,EAAE,GAAGC,UAAU,CAAE;MAC3Ga,IAAI,EAAEhB,gBAAgB,CAACiB,SAAS,CAACD,IAAK;MACtCH,KAAK,eAAEP,OAAA,CAACF,gBAAgB;QAACc,EAAE,EAAEL,KAAK,IAAIb,gBAAgB,CAACiB,SAAS,CAACJ;MAAM;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9E;EAAC,gBACJ,CAAC;AAEX,CAAC;AAACC,EAAA,GAZId,SAAS;AAcfA,SAAS,CAACe,YAAY,GAAG;EACrBb,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACd,CAAC;AAED,eAAeH,SAAS;AAAC,IAAAc,EAAA;AAAAE,YAAA,CAAAF,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}