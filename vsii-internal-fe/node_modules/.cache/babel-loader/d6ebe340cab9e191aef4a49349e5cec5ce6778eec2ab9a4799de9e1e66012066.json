{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '@mui/base';\nexport function getTimelineOppositeContentUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineOppositeContent', slot);\n}\nconst timelineOppositeContentClasses = generateUtilityClasses('MuiTimelineOppositeContent', ['root', 'positionLeft', 'positionRight', 'positionAlternate']);\nexport default timelineOppositeContentClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}