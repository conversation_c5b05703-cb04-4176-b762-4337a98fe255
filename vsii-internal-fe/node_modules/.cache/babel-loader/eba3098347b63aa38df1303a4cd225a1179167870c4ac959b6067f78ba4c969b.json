{"ast": null, "code": "import{FormattedMessage}from'react-intl';// material-ui\nimport ErrorIcon from'@mui/icons-material/Error';import{Grid,Typography}from'@mui/material';// project imports\nimport{listProjectTeamConfig,listProjectTeamSchema}from'pages/Config';import{E_IS_LOGTIME,TEXT_CONFIG_SCREEN,TEXT_INPUT_COLOR_EFFORT_INCURRED}from'constants/Common';import{Department,Member,SearchForm,Weeks,Years}from'../search';import{searchFormConfig}from'../search/Config';import{Label}from'components/extended/Form';import{Button}from'components';import ColorNoteTooltip from'components/ColorNoteTooltip';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ListProjectTeamSearch=props=>{const{formReset,weeks,handleChangeYear,handleSearch,handleChangeWeek,handleChangeDepartmentId,handleChangeMember}=props;const{resourcesInProject}=TEXT_CONFIG_SCREEN;return/*#__PURE__*/_jsx(SearchForm,{defaultValues:listProjectTeamConfig,formSchema:listProjectTeamSchema,handleSubmit:handleSearch,formReset:formReset,children:/*#__PURE__*/_jsxs(Grid,{container:true,alignItems:\"center\",spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:2.4,children:/*#__PURE__*/_jsx(Years,{handleChangeYear:handleChangeYear,label:resourcesInProject+'year'})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:2.4,children:/*#__PURE__*/_jsx(Weeks,{weeks:weeks,onChange:handleChangeWeek,label:resourcesInProject+'weeks'})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:2.4,children:/*#__PURE__*/_jsx(Department,{onChange:handleChangeDepartmentId,label:resourcesInProject+'dept'})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:2.4,children:/*#__PURE__*/_jsx(Member,{autoFilter:formReset,handleChange:handleChangeMember,findAllType:\"SCREEN_EFFORT\",isUserName:true,isLogTime:E_IS_LOGTIME.YES,name:searchFormConfig.userName.name,label:/*#__PURE__*/_jsxs(Typography,{display:\"flex\",gap:0.5,children:[/*#__PURE__*/_jsx(FormattedMessage,{id:resourcesInProject+'members'}),/*#__PURE__*/_jsx(ColorNoteTooltip,{notes:TEXT_INPUT_COLOR_EFFORT_INCURRED,children:/*#__PURE__*/_jsx(ErrorIcon,{sx:{fontSize:15}})})]})})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,lg:2.4,children:[/*#__PURE__*/_jsx(Label,{label:\"\\xA0\"}),/*#__PURE__*/_jsx(Button,{type:\"submit\",size:\"medium\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:resourcesInProject+'search'}),variant:\"contained\"})]})]})});};export default ListProjectTeamSearch;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}