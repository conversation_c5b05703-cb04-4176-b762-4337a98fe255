{"ast": null, "code": "import { getPickersLocalization } from './utils/getPickersLocalization';\nconst nbNOPickers = {\n  // Calendar navigation\n  previousMonth: 'Forrige måned',\n  nextMonth: 'Neste måned',\n  // View navigation\n  openPreviousView: 'åpne forrige visning',\n  openNextView: 'åpne neste visning',\n  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'årsvisning er åpen, bytt til kalendervisning' : 'kalendervisning er åpen, bytt til årsvisning',\n  // inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `text input view is open, go to ${viewType} view` : `${viewType} view is open, go to text input view`,\n  // DateRange placeholders\n  start: 'Start',\n  end: 'Slutt',\n  // Action bar\n  cancelButtonLabel: 'Avbryt',\n  clearButtonLabel: 'Fjern',\n  okButtonLabel: 'OK',\n  todayButtonLabel: 'I dag',\n  // Toolbar titles\n  // datePickerDefaultToolbarTitle: 'Select date',\n  // dateTimePickerDefaultToolbarTitle: 'Select date & time',\n  // timePickerDefaultToolbarTitle: 'Select time',\n  // dateRangePickerDefaultToolbarTitle: 'Select date range',\n  // Clock labels\n  clockLabelText: (view, time, adapter) => `Velg ${view}. ${time === null ? 'Ingen tid valgt' : `Valgt tid er ${adapter.format(time, 'fullTime')}`}`,\n  hoursClockNumberText: hours => `${hours} timer`,\n  minutesClockNumberText: minutes => `${minutes} minutter`,\n  secondsClockNumberText: seconds => `${seconds} sekunder`,\n  // Open picker labels\n  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Velg dato, valgt dato er ${utils.format(utils.date(rawValue), 'fullDate')}` : 'Velg dato',\n  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? `Velg tid, valgt tid er ${utils.format(utils.date(rawValue), 'fullTime')}` : 'Velg tid',\n  // Table labels\n  timeTableLabel: 'velg tid',\n  dateTableLabel: 'velg dato'\n};\nexport const nbNO = getPickersLocalization(nbNOPickers);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}