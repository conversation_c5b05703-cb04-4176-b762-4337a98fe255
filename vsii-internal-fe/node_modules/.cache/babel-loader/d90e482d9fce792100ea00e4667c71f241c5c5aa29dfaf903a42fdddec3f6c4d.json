{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/non-billable-monitoring/NonBillByMemberThead.tsx\";\nimport { FormattedMessage } from 'react-intl';\n\n// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { PERMISSIONS } from 'constants/Permission';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction NonBillByMemberThead() {\n  const {\n    nonBillable\n  } = PERMISSIONS.report;\n  const {\n    nBMByMember\n  } = TEXT_CONFIG_SCREEN.nonBillablemonitoring;\n  return /*#__PURE__*/_jsxDEV(TableHead, {\n    children: /*#__PURE__*/_jsxDEV(TableRow, {\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: nBMByMember + 'no'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: nBMByMember + 'member-code'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: nBMByMember + 'members'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: nBMByMember + 'title'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: nBMByMember + 'dept'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: nBMByMember + 'total-efforts-manhours'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: nBMByMember + 'projects'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          color: '#D9001B !important'\n        },\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: nBMByMember + 'not-enough-timesheets-ratio'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: nBMByMember + 'billables-projects'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 17\n      }, this), checkAllowedPermission(nonBillable.commentDetail) && /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: nBMByMember + 'actions'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 9\n  }, this);\n}\n_c = NonBillByMemberThead;\nexport default NonBillByMemberThead;\nvar _c;\n$RefreshReg$(_c, \"NonBillByMemberThead\");", "map": {"version": 3, "names": ["FormattedMessage", "TableCell", "TableHead", "TableRow", "checkAllowedPermission", "PERMISSIONS", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "NonBillByMemberThead", "nonBillable", "report", "nBMByMember", "nonBillablemonitoring", "children", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "color", "commentDetail", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/non-billable-monitoring/NonBillByMemberThead.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\n\n// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { PERMISSIONS } from 'constants/Permission';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\n\nfunction NonBillByMemberThead() {\n    const { nonBillable } = PERMISSIONS.report;\n\n    const { nBMByMember } = TEXT_CONFIG_SCREEN.nonBillablemonitoring;\n\n    return (\n        <TableHead>\n            <TableRow>\n                <TableCell>\n                    <FormattedMessage id={nBMByMember + 'no'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={nBMByMember + 'member-code'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={nBMByMember + 'members'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={nBMByMember + 'title'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={nBMByMember + 'dept'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={nBMByMember + 'total-efforts-manhours'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={nBMByMember + 'projects'} />\n                </TableCell>\n                <TableCell sx={{ color: '#D9001B !important' }}>\n                    <FormattedMessage id={nBMByMember + 'not-enough-timesheets-ratio'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={nBMByMember + 'billables-projects'} />\n                </TableCell>\n                {checkAllowedPermission(nonBillable.commentDetail) && (\n                    <TableCell>\n                        <FormattedMessage id={nBMByMember + 'actions'} />\n                    </TableCell>\n                )}\n            </TableRow>\n        </TableHead>\n    );\n}\nexport default NonBillByMemberThead;\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC9D,SAASC,sBAAsB,QAAQ,qBAAqB;AAC5D,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,MAAM;IAAEC;EAAY,CAAC,GAAGL,WAAW,CAACM,MAAM;EAE1C,MAAM;IAAEC;EAAY,CAAC,GAAGN,kBAAkB,CAACO,qBAAqB;EAEhE,oBACIL,OAAA,CAACN,SAAS;IAAAY,QAAA,eACNN,OAAA,CAACL,QAAQ;MAAAW,QAAA,gBACLN,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,WAAW,GAAG;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACZX,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,WAAW,GAAG;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eACZX,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,WAAW,GAAG;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACZX,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,WAAW,GAAG;QAAQ;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACZX,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,WAAW,GAAG;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eACZX,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,WAAW,GAAG;QAAyB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACZX,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,WAAW,GAAG;QAAW;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACZX,OAAA,CAACP,SAAS;QAACmB,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAqB,CAAE;QAAAP,QAAA,eAC3CN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,WAAW,GAAG;QAA8B;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACZX,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,WAAW,GAAG;QAAqB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,EACXf,sBAAsB,CAACM,WAAW,CAACY,aAAa,CAAC,iBAC9Cd,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,WAAW,GAAG;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CACd;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEpB;AAACI,EAAA,GA3CQd,oBAAoB;AA4C7B,eAAeA,oBAAoB;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}