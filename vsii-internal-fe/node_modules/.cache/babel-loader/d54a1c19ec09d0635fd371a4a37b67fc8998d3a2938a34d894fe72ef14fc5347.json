{"ast": null, "code": "(function (a, b) {\n  if (\"function\" == typeof define && define.amd) define([], b);else if (\"undefined\" != typeof exports) b();else {\n    b(), a.FileSaver = {\n      exports: {}\n    }.exports;\n  }\n})(this, function () {\n  \"use strict\";\n\n  function b(a, b) {\n    return \"undefined\" == typeof b ? b = {\n      autoBom: !1\n    } : \"object\" != typeof b && (console.warn(\"Deprecated: Expected third argument to be a object\"), b = {\n      autoBom: !b\n    }), b.autoBom && /^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(a.type) ? new Blob([\"\\uFEFF\", a], {\n      type: a.type\n    }) : a;\n  }\n  function c(a, b, c) {\n    var d = new XMLHttpRequest();\n    d.open(\"GET\", a), d.responseType = \"blob\", d.onload = function () {\n      g(d.response, b, c);\n    }, d.onerror = function () {\n      console.error(\"could not download file\");\n    }, d.send();\n  }\n  function d(a) {\n    var b = new XMLHttpRequest();\n    b.open(\"HEAD\", a, !1);\n    try {\n      b.send();\n    } catch (a) {}\n    return 200 <= b.status && 299 >= b.status;\n  }\n  function e(a) {\n    try {\n      a.dispatchEvent(new MouseEvent(\"click\"));\n    } catch (c) {\n      var b = document.createEvent(\"MouseEvents\");\n      b.initMouseEvent(\"click\", !0, !0, window, 0, 0, 0, 80, 20, !1, !1, !1, !1, 0, null), a.dispatchEvent(b);\n    }\n  }\n  var f = \"object\" == typeof window && window.window === window ? window : \"object\" == typeof self && self.self === self ? self : \"object\" == typeof global && global.global === global ? global : void 0,\n    a = f.navigator && /Macintosh/.test(navigator.userAgent) && /AppleWebKit/.test(navigator.userAgent) && !/Safari/.test(navigator.userAgent),\n    g = f.saveAs || (\"object\" != typeof window || window !== f ? function () {} : \"download\" in HTMLAnchorElement.prototype && !a ? function (b, g, h) {\n      var i = f.URL || f.webkitURL,\n        j = document.createElement(\"a\");\n      g = g || b.name || \"download\", j.download = g, j.rel = \"noopener\", \"string\" == typeof b ? (j.href = b, j.origin === location.origin ? e(j) : d(j.href) ? c(b, g, h) : e(j, j.target = \"_blank\")) : (j.href = i.createObjectURL(b), setTimeout(function () {\n        i.revokeObjectURL(j.href);\n      }, 4E4), setTimeout(function () {\n        e(j);\n      }, 0));\n    } : \"msSaveOrOpenBlob\" in navigator ? function (f, g, h) {\n      if (g = g || f.name || \"download\", \"string\" != typeof f) navigator.msSaveOrOpenBlob(b(f, h), g);else if (d(f)) c(f, g, h);else {\n        var i = document.createElement(\"a\");\n        i.href = f, i.target = \"_blank\", setTimeout(function () {\n          e(i);\n        });\n      }\n    } : function (b, d, e, g) {\n      if (g = g || open(\"\", \"_blank\"), g && (g.document.title = g.document.body.innerText = \"downloading...\"), \"string\" == typeof b) return c(b, d, e);\n      var h = \"application/octet-stream\" === b.type,\n        i = /constructor/i.test(f.HTMLElement) || f.safari,\n        j = /CriOS\\/[\\d]+/.test(navigator.userAgent);\n      if ((j || h && i || a) && \"undefined\" != typeof FileReader) {\n        var k = new FileReader();\n        k.onloadend = function () {\n          var a = k.result;\n          a = j ? a : a.replace(/^data:[^;]*;/, \"data:attachment/file;\"), g ? g.location.href = a : location = a, g = null;\n        }, k.readAsDataURL(b);\n      } else {\n        var l = f.URL || f.webkitURL,\n          m = l.createObjectURL(b);\n        g ? g.location = m : location.href = m, g = null, setTimeout(function () {\n          l.revokeObjectURL(m);\n        }, 4E4);\n      }\n    });\n  f.saveAs = g.saveAs = g, \"undefined\" != typeof module && (module.exports = g);\n});\n\n//# sourceMappingURL=FileSaver.min.js.map", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}