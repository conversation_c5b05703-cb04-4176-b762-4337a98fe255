{"ast": null, "code": "import { GetOption } from '../GetOption';\nimport { IsWellFormedCurrencyCode } from '../IsWellFormedCurrencyCode';\nimport { IsWellFormedUnitIdentifier } from '../IsWellFormedUnitIdentifier';\n/**\n * https://tc39.es/ecma402/#sec-setnumberformatunitoptions\n */\nexport function SetNumberFormatUnitOptions(nf, options, _a) {\n  if (options === void 0) {\n    options = Object.create(null);\n  }\n  var getInternalSlots = _a.getInternalSlots;\n  var internalSlots = getInternalSlots(nf);\n  var style = GetOption(options, 'style', 'string', ['decimal', 'percent', 'currency', 'unit'], 'decimal');\n  internalSlots.style = style;\n  var currency = GetOption(options, 'currency', 'string', undefined, undefined);\n  if (currency !== undefined && !IsWellFormedCurrencyCode(currency)) {\n    throw RangeError('Malformed currency code');\n  }\n  if (style === 'currency' && currency === undefined) {\n    throw TypeError('currency cannot be undefined');\n  }\n  var currencyDisplay = GetOption(options, 'currencyDisplay', 'string', ['code', 'symbol', 'narrowSymbol', 'name'], 'symbol');\n  var currencySign = GetOption(options, 'currencySign', 'string', ['standard', 'accounting'], 'standard');\n  var unit = GetOption(options, 'unit', 'string', undefined, undefined);\n  if (unit !== undefined && !IsWellFormedUnitIdentifier(unit)) {\n    throw RangeError('Invalid unit argument for Intl.NumberFormat()');\n  }\n  if (style === 'unit' && unit === undefined) {\n    throw TypeError('unit cannot be undefined');\n  }\n  var unitDisplay = GetOption(options, 'unitDisplay', 'string', ['short', 'narrow', 'long'], 'short');\n  if (style === 'currency') {\n    internalSlots.currency = currency.toUpperCase();\n    internalSlots.currencyDisplay = currencyDisplay;\n    internalSlots.currencySign = currencySign;\n  }\n  if (style === 'unit') {\n    internalSlots.unit = unit;\n    internalSlots.unitDisplay = unitDisplay;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}