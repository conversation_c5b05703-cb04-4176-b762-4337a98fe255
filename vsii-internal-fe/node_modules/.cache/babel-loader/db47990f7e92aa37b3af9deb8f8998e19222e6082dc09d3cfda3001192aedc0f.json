{"ast": null, "code": "import{FormattedMessage}from'react-intl';// material-ui\nimport{TableCell,TableHead,TableRow}from'@mui/material';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const QuotaUpdateHistoryThead=()=>{const{manage_project}=TEXT_CONFIG_SCREEN.administration;return/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_project+'no'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_project+'quota'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_project+'update-date'})})]})});};export default QuotaUpdateHistoryThead;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}