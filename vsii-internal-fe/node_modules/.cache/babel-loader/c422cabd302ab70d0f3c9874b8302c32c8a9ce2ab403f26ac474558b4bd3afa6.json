{"ast": null, "code": "\"use strict\";\n\nvar __createBinding = this && this.__createBinding || (Object.create ? function (o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n    desc = {\n      enumerable: true,\n      get: function () {\n        return m[k];\n      }\n    };\n  }\n  Object.defineProperty(o, k2, desc);\n} : function (o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\nvar __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function (o, v) {\n  Object.defineProperty(o, \"default\", {\n    enumerable: true,\n    value: v\n  });\n} : function (o, v) {\n  o[\"default\"] = v;\n});\nvar __importStar = this && this.__importStar || function (mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n  __setModuleDefault(result, mod);\n  return result;\n};\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nvar __asyncValues = this && this.__asyncValues || function (o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator],\n    i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () {\n    return this;\n  }, i);\n  function verb(n) {\n    i[n] = o[n] && function (v) {\n      return new Promise(function (resolve, reject) {\n        v = o[n](v), settle(resolve, reject, v.done, v.value);\n      });\n    };\n  }\n  function settle(resolve, reject, d, v) {\n    Promise.resolve(v).then(function (v) {\n      resolve({\n        value: v,\n        done: d\n      });\n    }, reject);\n  }\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ReactMultiEmail = void 0;\nconst React = __importStar(require(\"react\"));\nconst isEmail_1 = require(\"./isEmail\");\nfunction ReactMultiEmail(props) {\n  const {\n    id,\n    style,\n    className = '',\n    noClass,\n    placeholder,\n    autoFocus,\n    allowDisplayName = false,\n    stripDisplayName = false,\n    allowDuplicate = false,\n    delimiter = \"[\".concat(allowDisplayName ? '' : ' ', \",;]\"),\n    initialInputValue = '',\n    inputClassName,\n    autoComplete,\n    getLabel,\n    enable,\n    onDisabled,\n    validateEmail,\n    onChange,\n    onChangeInput,\n    onFocus,\n    onBlur,\n    onKeyDown,\n    onKeyUp,\n    spinner,\n    disableOnBlurValidation = false\n  } = props;\n  const emailInputRef = React.useRef(null);\n  const [focused, setFocused] = React.useState(false);\n  const [emails, setEmails] = React.useState([]);\n  const [inputValue, setInputValue] = React.useState('');\n  const [spinning, setSpinning] = React.useState(false);\n  const findEmailAddress = React.useCallback((value, isEnter) => __awaiter(this, void 0, void 0, function* () {\n    var _a, _b;\n    const validEmails = [];\n    let inputValue = '';\n    const re = new RegExp(delimiter, 'g');\n    const isEmail = validateEmail || isEmail_1.isEmail;\n    const addEmails = email => {\n      if (!allowDuplicate) {\n        for (let i = 0, l = emails.length; i < l; i++) {\n          if (emails[i].toLowerCase() === email.toLowerCase()) {\n            return false;\n          }\n        }\n      }\n      validEmails.push(email);\n      return true;\n    };\n    if (value !== '') {\n      if (re.test(value)) {\n        const setArr = new Set(value.split(re).filter(n => n));\n        const arr = [...setArr];\n        while (arr.length) {\n          const validateResult = isEmail('' + arr[0].trim());\n          if (typeof validateResult === 'boolean') {\n            if (validateResult) {\n              addEmails('' + ((_a = arr.shift()) === null || _a === void 0 ? void 0 : _a.trim()));\n            } else {\n              if (allowDisplayName) {\n                const validateResultWithDisplayName = isEmail('' + arr[0].trim(), {\n                  allowDisplayName\n                });\n                if (validateResultWithDisplayName) {\n                  // Strip display name from email formatted as such \"First Last <<EMAIL>>\"\n                  const email = stripDisplayName ? (_b = arr.shift()) === null || _b === void 0 ? void 0 : _b.split('<')[1].split('>')[0] : arr.shift();\n                  addEmails('' + email);\n                } else {\n                  if (arr.length === 1) {\n                    inputValue = '' + arr.shift();\n                  } else {\n                    arr.shift();\n                  }\n                }\n              } else {\n                inputValue = '' + arr.shift();\n              }\n            }\n          } else {\n            // handle promise\n            setSpinning(true);\n            if ((yield validateEmail === null || validateEmail === void 0 ? void 0 : validateEmail(value)) === true) {\n              addEmails('' + arr.shift());\n              setSpinning(false);\n            } else {\n              if (arr.length === 1) {\n                inputValue = '' + arr.shift();\n              } else {\n                arr.shift();\n              }\n            }\n          }\n        }\n      } else {\n        if (enable && !enable({\n          emailCnt: emails.length\n        })) {\n          onDisabled === null || onDisabled === void 0 ? void 0 : onDisabled();\n          return;\n        }\n        if (isEnter) {\n          const validateResult = isEmail(value);\n          if (typeof validateResult === 'boolean') {\n            if (validateResult) {\n              addEmails(value);\n            } else if (allowDisplayName) {\n              const validateResultWithDisplayName = isEmail(value, {\n                allowDisplayName\n              });\n              if (validateResultWithDisplayName) {\n                // Strip display name from email formatted as such \"First Last <<EMAIL>>\"\n                const email = stripDisplayName ? value.split('<')[1].split('>')[0] : value;\n                addEmails(email);\n              } else {\n                inputValue = value;\n              }\n            } else {\n              inputValue = value;\n            }\n          } else {\n            // handle promise\n            setSpinning(true);\n            if ((yield validateEmail === null || validateEmail === void 0 ? void 0 : validateEmail(value)) === true) {\n              addEmails(value);\n            } else {\n              inputValue = value;\n            }\n            setSpinning(false);\n          }\n        } else {\n          inputValue = value;\n        }\n      }\n    }\n    setEmails([...emails, ...validEmails]);\n    setInputValue(inputValue);\n    if (validEmails.length) {\n      onChange === null || onChange === void 0 ? void 0 : onChange([...emails, ...validEmails]);\n    }\n    if (inputValue !== inputValue) {\n      onChangeInput === null || onChangeInput === void 0 ? void 0 : onChangeInput(inputValue);\n    }\n  }), [allowDisplayName, allowDuplicate, delimiter, emails, enable, onChange, onChangeInput, onDisabled, stripDisplayName, validateEmail]);\n  const onChangeInputValue = React.useCallback(value => __awaiter(this, void 0, void 0, function* () {\n    yield findEmailAddress(value);\n    onChangeInput === null || onChangeInput === void 0 ? void 0 : onChangeInput(value);\n  }), [findEmailAddress, onChangeInput]);\n  const removeEmail = React.useCallback((index, isDisabled) => {\n    if (isDisabled) {\n      return;\n    }\n    const _emails = [...emails.slice(0, index), ...emails.slice(index + 1)];\n    setEmails(_emails);\n    onChange === null || onChange === void 0 ? void 0 : onChange(_emails);\n  }, [emails, onChange]);\n  const handleOnKeydown = React.useCallback(e => {\n    onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(e);\n    switch (e.key) {\n      case 'Enter':\n        e.preventDefault();\n        break;\n      case 'Backspace':\n        if (!e.currentTarget.value) {\n          removeEmail(emails.length - 1, false);\n        }\n        break;\n      default:\n    }\n  }, [emails.length, onKeyDown, removeEmail]);\n  const handleOnKeyup = React.useCallback(e => __awaiter(this, void 0, void 0, function* () {\n    onKeyUp === null || onKeyUp === void 0 ? void 0 : onKeyUp(e);\n    switch (e.key) {\n      case 'Enter':\n        yield findEmailAddress(e.currentTarget.value, true);\n        break;\n      default:\n    }\n  }), [findEmailAddress, onKeyUp]);\n  const handleOnChange = React.useCallback(e => __awaiter(this, void 0, void 0, function* () {\n    return yield onChangeInputValue(e.currentTarget.value);\n  }), [onChangeInputValue]);\n  const handleOnBlur = React.useCallback(e => __awaiter(this, void 0, void 0, function* () {\n    setFocused(false);\n    if (!disableOnBlurValidation) {\n      yield findEmailAddress(e.currentTarget.value, true);\n    }\n    onBlur === null || onBlur === void 0 ? void 0 : onBlur();\n  }), [disableOnBlurValidation, findEmailAddress, onBlur]);\n  const handleOnFocus = React.useCallback(() => {\n    setFocused(true);\n    onFocus === null || onFocus === void 0 ? void 0 : onFocus();\n  }, [onFocus]);\n  React.useEffect(() => {\n    setInputValue(initialInputValue);\n  }, [initialInputValue]);\n  React.useEffect(() => {\n    var _a;\n    if (validateEmail) {\n      (() => __awaiter(this, void 0, void 0, function* () {\n        var _b, e_1, _c, _d;\n        var _e;\n        setSpinning(true);\n        const validEmails = [];\n        try {\n          for (var _f = true, _g = __asyncValues((_e = props.emails) !== null && _e !== void 0 ? _e : []), _h; _h = yield _g.next(), _b = _h.done, !_b;) {\n            _d = _h.value;\n            _f = false;\n            try {\n              const email = _d;\n              if (yield validateEmail(email)) {\n                validEmails.push(email);\n              }\n            } finally {\n              _f = true;\n            }\n          }\n        } catch (e_1_1) {\n          e_1 = {\n            error: e_1_1\n          };\n        } finally {\n          try {\n            if (!_f && !_b && (_c = _g.return)) yield _c.call(_g);\n          } finally {\n            if (e_1) throw e_1.error;\n          }\n        }\n        setEmails(validEmails);\n        setSpinning(false);\n      }))();\n    } else {\n      const validEmails = (_a = props.emails) === null || _a === void 0 ? void 0 : _a.filter(email => {\n        return (0, isEmail_1.isEmail)(email);\n      });\n      setEmails(validEmails !== null && validEmails !== void 0 ? validEmails : []);\n    }\n  }, [props.emails, validateEmail]);\n  return React.createElement(\"div\", {\n    className: \"\".concat(className, \" \").concat(noClass ? '' : 'react-multi-email', \" \").concat(focused ? 'focused' : '', \" \").concat(inputValue === '' && emails.length === 0 ? 'empty' : 'fill'),\n    style: style,\n    onClick: () => {\n      var _a;\n      return (_a = emailInputRef.current) === null || _a === void 0 ? void 0 : _a.focus();\n    }\n  }, spinning && (spinner === null || spinner === void 0 ? void 0 : spinner()), placeholder ? React.createElement(\"span\", {\n    \"data-placeholder\": true\n  }, placeholder) : null, React.createElement(\"div\", {\n    className: 'data-labels',\n    style: {\n      opacity: spinning ? 0.45 : 1.0,\n      display: 'contents',\n      flexWrap: 'inherit'\n    }\n  }, emails.map((email, index) => getLabel(email, index, removeEmail))), React.createElement(\"input\", {\n    id: id,\n    style: {\n      opacity: spinning ? 0.45 : 1.0\n    },\n    ref: emailInputRef,\n    type: 'text',\n    value: inputValue,\n    onFocus: handleOnFocus,\n    onBlur: handleOnBlur,\n    onChange: handleOnChange,\n    onKeyDown: handleOnKeydown,\n    onKeyUp: handleOnKeyup,\n    autoFocus: autoFocus,\n    className: inputClassName,\n    autoComplete: autoComplete\n  }));\n}\nexports.ReactMultiEmail = ReactMultiEmail;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}