{"ast": null, "code": "import ClassNameGenerator from '../ClassNameGenerator';\nexport const globalStateClasses = {\n  active: 'active',\n  checked: 'checked',\n  completed: 'completed',\n  disabled: 'disabled',\n  error: 'error',\n  expanded: 'expanded',\n  focused: 'focused',\n  focusVisible: 'focusVisible',\n  open: 'open',\n  readOnly: 'readOnly',\n  required: 'required',\n  selected: 'selected'\n};\nexport default function generateUtilityClass(componentName, slot) {\n  let globalStatePrefix = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'Mui';\n  const globalStateClass = globalStateClasses[slot];\n  return globalStateClass ? \"\".concat(globalStatePrefix, \"-\").concat(globalStateClass) : \"\".concat(ClassNameGenerator.generate(componentName), \"-\").concat(slot);\n}\nexport function isGlobalState(slot) {\n  return globalStateClasses[slot] !== undefined;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}