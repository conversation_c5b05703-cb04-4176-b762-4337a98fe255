{"ast": null, "code": "import{memo,useMemo}from'react';// material-ui\nimport{useTheme}from'@mui/material/styles';import{Box,Drawer,useMediaQuery}from'@mui/material';// third-party\nimport PerfectScrollbar from'react-perfect-scrollbar';// project imports\nimport MenuList from'./MenuList';import LogoSection from'./LogoSection';import MiniDrawerStyled from'./MiniDrawerStyled';import{LAYOUT_CONST}from'constants/Common';import useConfig from'hooks/useConfig';import{drawerWidth}from'store/constant';import{useAppSelector,useAppDispatch}from'app/hooks';import{openDrawer}from'store/slice/menuSlice';// ==============================|| SIDEBAR DRAWER ||============================== //\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Sidebar=()=>{const theme=useTheme();const matchUpMd=useMediaQuery(theme.breakpoints.up('md'));const matchDownMd=useMediaQuery(theme.breakpoints.down('md'));const dispatch=useAppDispatch();const{drawerOpen}=useAppSelector(state=>state.menu);const{drawerType}=useConfig();const logo=useMemo(()=>/*#__PURE__*/_jsx(Box,{sx:{display:'flex',p:2},children:/*#__PURE__*/_jsx(LogoSection,{})}),[]);const drawer=useMemo(()=>/*#__PURE__*/_jsx(PerfectScrollbar,{component:\"div\",style:{height:!matchUpMd?'calc(100vh - 56px)':'calc(100vh - 88px)',paddingLeft:drawerOpen?'16px':0,paddingRight:drawerOpen?'16px':0,marginTop:drawerOpen?0:'20px'},children:/*#__PURE__*/_jsx(MenuList,{})}),// eslint-disable-next-line react-hooks/exhaustive-deps\n[matchUpMd,drawerOpen,drawerType]);return/*#__PURE__*/_jsx(Box,{component:\"nav\",sx:{flexShrink:{md:0},width:matchUpMd?drawerWidth:'auto'},\"aria-label\":\"mailbox folders\",children:matchDownMd||drawerType===LAYOUT_CONST.MINI_DRAWER&&drawerOpen?/*#__PURE__*/_jsxs(Drawer,{variant:matchUpMd?'persistent':'temporary',anchor:\"left\",open:drawerOpen,onClose:()=>dispatch(openDrawer(!drawerOpen)),sx:{'& .MuiDrawer-paper':{mt:matchDownMd?0:11,zIndex:1099,width:drawerWidth,background:theme.palette.background.default,color:theme.palette.text.primary,borderRight:'none'}},ModalProps:{keepMounted:true},color:\"inherit\",children:[matchDownMd&&logo,drawer]}):/*#__PURE__*/_jsxs(MiniDrawerStyled,{variant:\"permanent\",open:drawerOpen,children:[logo,drawer]})});};export default/*#__PURE__*/memo(Sidebar);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}