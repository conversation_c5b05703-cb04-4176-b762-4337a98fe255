{"ast": null, "code": "import React,{useState}from'react';// material-ui\nimport{TableCell,TableRow}from'@mui/material';import Collapse from'@mui/material/Collapse';import IconButton from'@mui/material/IconButton';import KeyboardArrowDownIcon from'@mui/icons-material/KeyboardArrowDown';import KeyboardArrowUpIcon from'@mui/icons-material/KeyboardArrowUp';//third-party\nimport{FormattedMessage}from'react-intl';//project import\nimport sendRequest from'services/ApiService';import Api from'constants/Api';import{convertStatus,showPdfInNewTab}from'utils/common';import{PERMISSIONS}from'constants/Permission';import{checkAllowedPermission}from'utils/authorization';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const SkillsReportRow=props=>{const{data,page,size,index}=props;const[open,setOpen]=useState(false);const{skillsUpdate}=PERMISSIONS.report.skillManage;const handleViewPDF=async params=>{const response=await sendRequest(Api.skills_manage.viewPDF,params);if(response!==null&&response!==void 0&&response.status){const{content}=response.result;showPdfInNewTab(content.pdf);}else{return;}};const translateDegree=degree=>{switch(degree){case'diploma-degree':return/*#__PURE__*/_jsx(FormattedMessage,{id:\"diploma-degree\"});case'college-degree':return/*#__PURE__*/_jsx(FormattedMessage,{id:\"college-degree\"});case'post-graduated':return/*#__PURE__*/_jsx(FormattedMessage,{id:\"post-graduated\"});case'other':return/*#__PURE__*/_jsx(FormattedMessage,{id:\"other\"});default:return'';}};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(TableRow,{children:[data&&data.technologySkillResponse.length>1?/*#__PURE__*/_jsx(TableCell,{width:\"40px\",children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"expand row\",size:\"small\",onClick:()=>setOpen(!open),children:open?/*#__PURE__*/_jsx(KeyboardArrowUpIcon,{}):/*#__PURE__*/_jsx(KeyboardArrowDownIcon,{})})}):/*#__PURE__*/_jsx(TableCell,{width:\"40px\"}),/*#__PURE__*/_jsx(TableCell,{children:size*page+index+1}),/*#__PURE__*/_jsx(TableCell,{children:data.memberCode}),/*#__PURE__*/_jsx(TableCell,{sx:{cursor:checkAllowedPermission(skillsUpdate.viewCV)?'pointer':'unset','&:hover':{fontWeight:checkAllowedPermission(skillsUpdate.viewCV)?'bold':'inherit'}},onClick:checkAllowedPermission(skillsUpdate.viewCV)?()=>handleViewPDF({memberCode:data.memberCode}):undefined,children:data.fullNameEn}),/*#__PURE__*/_jsx(TableCell,{children:data.title}),/*#__PURE__*/_jsx(TableCell,{colSpan:3,sx:{padding:'6px 0'},children:/*#__PURE__*/_jsxs(TableRow,{sx:{display:'table',width:'100%','.MuiTableCell-root':{borderBottom:'none',wordBreak:'break-all'}},children:[/*#__PURE__*/_jsx(TableCell,{sx:{fontWeight:data.technologySkillResponse.length>0&&data.technologySkillResponse[0].mainSkill?'bold':'400'},width:'33.333%',align:\"center\",children:data.technologySkillResponse.length>0&&data.technologySkillResponse[0].techName}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",width:'33.333%',children:data.technologySkillResponse.length>0&&data.technologySkillResponse[0].level}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:data.technologySkillResponse.length>0&&data.technologySkillResponse[0].experiences})]},index)}),/*#__PURE__*/_jsx(TableCell,{children:translateDegree(data.degree)}),/*#__PURE__*/_jsx(TableCell,{children:convertStatus(data.status)})]}),data&&data.technologySkillResponse.length>1&&/*#__PURE__*/_jsxs(TableRow,{sx:{'& > .MuiTableCell-root':{borderBottom:'none',wordBreak:'break-all'}},children:[/*#__PURE__*/_jsx(TableCell,{colSpan:5}),/*#__PURE__*/_jsx(TableCell,{colSpan:3,sx:{padding:'6px 0'},children:/*#__PURE__*/_jsx(Collapse,{in:open,timeout:\"auto\",unmountOnExit:true,children:data.technologySkillResponse.slice(1).map((item,index)=>/*#__PURE__*/_jsxs(TableRow,{sx:{display:'table',width:'100%'},children:[/*#__PURE__*/_jsx(TableCell,{align:\"center\",width:'33.333%',children:item.techName}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",width:'33.333%',children:item.level}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:item.experiences})]},index))})})]})]});};export default SkillsReportRow;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}