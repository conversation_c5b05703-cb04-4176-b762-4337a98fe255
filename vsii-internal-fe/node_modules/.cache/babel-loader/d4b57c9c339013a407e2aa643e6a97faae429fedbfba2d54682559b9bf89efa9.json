{"ast": null, "code": "import { RawTask } from './RawTask.js';\nexport class TaskFactory {\n  create(task) {\n    const tasks = this.freeTasks;\n    const t1 = tasks.length ? tasks.pop() : new RawTask(this.onError, t => tasks[tasks.length] = t);\n    t1.task = task;\n    return t1;\n  }\n  constructor(onError) {\n    this.onError = onError;\n    this.freeTasks = [];\n  }\n}\n\n//# sourceMappingURL=TaskFactory.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}