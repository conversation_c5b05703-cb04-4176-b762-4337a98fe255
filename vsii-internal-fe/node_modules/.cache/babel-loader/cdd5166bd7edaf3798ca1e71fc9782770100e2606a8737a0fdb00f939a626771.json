{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/Contractor.tsx\";\nimport { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { searchFormConfig } from './Config';\nimport { Select } from 'components/extended/Form';\nimport { CONTRACTOR, DEFAULT_VALUE_OPTION } from 'constants/Common';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Contractor = props => {\n  const {\n    isShowAll,\n    required,\n    label\n  } = props;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Select, {\n      isMultipleLanguage: true,\n      required: required,\n      selects: isShowAll ? [DEFAULT_VALUE_OPTION, ...CONTRACTOR] : [],\n      name: searchFormConfig.contractor.name,\n      label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n        id: label || searchFormConfig.contractor.label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 24\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_c = Contractor;\nContractor.defaultProps = {\n  isShowAll: true\n};\nexport default Contractor;\nvar _c;\n$RefreshReg$(_c, \"Contractor\");", "map": {"version": 3, "names": ["FormattedMessage", "searchFormConfig", "Select", "CONTRACTOR", "DEFAULT_VALUE_OPTION", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Contractor", "props", "isShowAll", "required", "label", "children", "isMultipleLanguage", "selects", "name", "contractor", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "defaultProps", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/Contractor.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { searchFormConfig } from './Config';\nimport { Select } from 'components/extended/Form';\nimport { CONTRACTOR, DEFAULT_VALUE_OPTION } from 'constants/Common';\n\ninterface IContractorProps {\n    isShowAll?: boolean;\n    required?: boolean;\n    label?: string;\n}\n\nconst Contractor = (props: IContractorProps) => {\n    const { isShowAll, required, label } = props;\n\n    return (\n        <>\n            <Select\n                isMultipleLanguage\n                required={required}\n                selects={isShowAll ? [DEFAULT_VALUE_OPTION, ...CONTRACTOR] : []}\n                name={searchFormConfig.contractor.name}\n                label={<FormattedMessage id={label || searchFormConfig.contractor.label} />}\n            />\n        </>\n    );\n};\n\nContractor.defaultProps = {\n    isShowAll: true\n};\n\nexport default Contractor;\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,gBAAgB,QAAQ,UAAU;AAC3C,SAASC,MAAM,QAAQ,0BAA0B;AACjD,SAASC,UAAU,EAAEC,oBAAoB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQpE,MAAMC,UAAU,GAAIC,KAAuB,IAAK;EAC5C,MAAM;IAAEC,SAAS;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAGH,KAAK;EAE5C,oBACIJ,OAAA,CAAAE,SAAA;IAAAM,QAAA,eACIR,OAAA,CAACJ,MAAM;MACHa,kBAAkB;MAClBH,QAAQ,EAAEA,QAAS;MACnBI,OAAO,EAAEL,SAAS,GAAG,CAACP,oBAAoB,EAAE,GAAGD,UAAU,CAAC,GAAG,EAAG;MAChEc,IAAI,EAAEhB,gBAAgB,CAACiB,UAAU,CAACD,IAAK;MACvCJ,KAAK,eAAEP,OAAA,CAACN,gBAAgB;QAACmB,EAAE,EAAEN,KAAK,IAAIZ,gBAAgB,CAACiB,UAAU,CAACL;MAAM;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E;EAAC,gBACJ,CAAC;AAEX,CAAC;AAACC,EAAA,GAdIf,UAAU;AAgBhBA,UAAU,CAACgB,YAAY,GAAG;EACtBd,SAAS,EAAE;AACf,CAAC;AAED,eAAeF,UAAU;AAAC,IAAAe,EAAA;AAAAE,YAAA,CAAAF,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}