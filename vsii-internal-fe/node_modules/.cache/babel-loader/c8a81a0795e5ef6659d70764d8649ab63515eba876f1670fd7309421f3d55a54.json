{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"ToolbarComponent\", \"value\", \"onChange\", \"components\", \"componentsProps\", \"hideTabs\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useDateTimePickerDefaultizedProps, dateTimePickerValueManager } from '../DateTimePicker/shared';\nimport { DateTimePickerToolbar } from '../DateTimePicker/DateTimePickerToolbar';\nimport { MobileWrapper } from '../internals/components/wrappers/MobileWrapper';\nimport { CalendarOrClockPicker } from '../internals/components/CalendarOrClockPicker';\nimport { useDateTimeValidation } from '../internals/hooks/validation/useDateTimeValidation';\nimport { PureDateInput } from '../internals/components/PureDateInput';\nimport { usePickerState } from '../internals/hooks/usePickerState';\nimport { DateTimePickerTabs } from '../DateTimePicker/DateTimePickerTabs';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n\n/**\n *\n * Demos:\n *\n * - [Date Time Picker](https://mui.com/x/react-date-pickers/date-time-picker/)\n *\n * API:\n *\n * - [MobileDateTimePicker API](https://mui.com/x/api/date-pickers/mobile-date-time-picker/)\n */\nexport const MobileDateTimePicker = /*#__PURE__*/React.forwardRef(function MobileDateTimePicker(inProps, ref) {\n  const props = useDateTimePickerDefaultizedProps(inProps, 'MuiMobileDateTimePicker');\n  const validationError = useDateTimeValidation(props) !== null;\n  const {\n    pickerProps,\n    inputProps,\n    wrapperProps\n  } = usePickerState(props, dateTimePickerValueManager); // Note that we are passing down all the value without spread.\n  // It saves us >1kb gzip and make any prop available automatically on any level down.\n\n  const {\n      ToolbarComponent = DateTimePickerToolbar,\n      components: providedComponents,\n      componentsProps,\n      hideTabs = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const components = React.useMemo(() => _extends({\n    Tabs: DateTimePickerTabs\n  }, providedComponents), [providedComponents]);\n  const DateInputProps = _extends({}, inputProps, other, {\n    components,\n    componentsProps,\n    ref,\n    validationError\n  });\n  return /*#__PURE__*/_jsx(MobileWrapper, _extends({}, other, wrapperProps, {\n    DateInputProps: DateInputProps,\n    PureDateInputComponent: PureDateInput,\n    components: components,\n    componentsProps: componentsProps,\n    children: /*#__PURE__*/_jsx(CalendarOrClockPicker, _extends({}, pickerProps, {\n      autoFocus: true,\n      toolbarTitle: props.label || props.toolbarTitle,\n      ToolbarComponent: ToolbarComponent,\n      DateInputProps: DateInputProps,\n      components: components,\n      componentsProps: componentsProps,\n      hideTabs: hideTabs\n    }, other))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? MobileDateTimePicker.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * Regular expression to detect \"accepted\" symbols.\n   * @default /\\dap/gi\n   */\n  acceptRegex: PropTypes.instanceOf(RegExp),\n  /**\n   * 12h/24h view for hour selection clock.\n   * @default `utils.is12HourCycleInCurrentLocale()`\n   */\n  ampm: PropTypes.bool,\n  /**\n   * Display ampm controls under the clock (instead of in the toolbar).\n   * @default false\n   */\n  ampmInClock: PropTypes.bool,\n  autoFocus: PropTypes.bool,\n  children: PropTypes.node,\n  /**\n   * className applied to the root component.\n   */\n  className: PropTypes.string,\n  /**\n   * If `true` the popup or dialog will immediately close after submitting full date.\n   * @default `true` for Desktop, `false` for Mobile (based on the chosen wrapper and `desktopModeMediaQuery` prop).\n   */\n  closeOnSelect: PropTypes.bool,\n  /**\n   * Overrideable components.\n   * @default {}\n   */\n  components: PropTypes.object,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  componentsProps: PropTypes.object,\n  /**\n   * Date tab icon.\n   */\n  dateRangeIcon: PropTypes.node,\n  /**\n   * Formats the day of week displayed in the calendar header.\n   * @param {string} day The day of week provided by the adapter's method `getWeekdays`.\n   * @returns {string} The name to display.\n   * @default (day) => day.charAt(0).toUpperCase()\n   */\n  dayOfWeekFormatter: PropTypes.func,\n  /**\n   * Default calendar month displayed when `value={null}`.\n   */\n  defaultCalendarMonth: PropTypes.any,\n  /**\n   * Props applied to the [`Dialog`](https://mui.com/material-ui/api/dialog/) element.\n   */\n  DialogProps: PropTypes.object,\n  /**\n   * If `true`, the picker and text field are disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true` future days are disabled.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * If `true`, today's date is rendering without highlighting with circle.\n   * @default false\n   */\n  disableHighlightToday: PropTypes.bool,\n  /**\n   * Do not ignore date part when validating min/max time.\n   * @default false\n   */\n  disableIgnoringDatePartForTimeValidation: PropTypes.bool,\n  /**\n   * Disable mask on the keyboard, this should be used rarely. Consider passing proper mask for your format.\n   * @default false\n   */\n  disableMaskedInput: PropTypes.bool,\n  /**\n   * Do not render open picker button (renders only text field with validation).\n   * @default false\n   */\n  disableOpenPicker: PropTypes.bool,\n  /**\n   * If `true` past days are disabled.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  /**\n   * Accessible text that helps user to understand which time and view is selected.\n   * @template TDate\n   * @param {ClockPickerView} view The current view rendered.\n   * @param {TDate | null} time The current time.\n   * @param {MuiPickersAdapter<TDate>} adapter The current date adapter.\n   * @returns {string} The clock label.\n   * @deprecated Use the `localeText` prop of `LocalizationProvider` instead, see https://mui.com/x/react-date-pickers/localization/.\n   * @default <TDate extends any>(\n   *   view: ClockView,\n   *   time: TDate | null,\n   *   adapter: MuiPickersAdapter<TDate>,\n   * ) =>\n   *   `Select ${view}. ${\n   *     time === null ? 'No time selected' : `Selected time is ${adapter.format(time, 'fullTime')}`\n   *   }`\n   */\n  getClockLabelText: PropTypes.func,\n  /**\n   * Get aria-label text for control that opens picker dialog. Aria-label text must include selected date. @DateIOType\n   * @template TInputDate, TDate\n   * @param {TInputDate} date The date from which we want to add an aria-text.\n   * @param {MuiPickersAdapter<TDate>} utils The utils to manipulate the date.\n   * @returns {string} The aria-text to render inside the dialog.\n   * @default (date, utils) => `Choose date, selected date is ${utils.format(utils.date(date), 'fullDate')}`\n   */\n  getOpenDialogAriaText: PropTypes.func,\n  /**\n   * Get aria-label text for switching between views button.\n   * @param {CalendarPickerView} currentView The view from which we want to get the button text.\n   * @returns {string} The label of the view.\n   * @deprecated Use the `localeText` prop of `LocalizationProvider` instead, see https://mui.com/x/react-date-pickers/localization/.\n   */\n  getViewSwitchingButtonText: PropTypes.func,\n  /**\n   * Toggles visibility of date time switching tabs\n   * @default false for mobile, true for desktop\n   */\n  hideTabs: PropTypes.bool,\n  ignoreInvalidInputs: PropTypes.bool,\n  /**\n   * Props to pass to keyboard input adornment.\n   */\n  InputAdornmentProps: PropTypes.object,\n  /**\n   * Format string.\n   */\n  inputFormat: PropTypes.string,\n  InputProps: PropTypes.object,\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.object\n  })]),\n  label: PropTypes.node,\n  /**\n   * Left arrow icon aria-label text.\n   * @deprecated\n   */\n  leftArrowButtonText: PropTypes.string,\n  /**\n   * If `true` renders `LoadingComponent` in calendar instead of calendar view.\n   * Can be used to preload information and show it in calendar.\n   * @default false\n   */\n  loading: PropTypes.bool,\n  /**\n   * Custom mask. Can be used to override generate from format. (e.g. `__/__/____ __:__` or `__/__/____ __:__ _M`).\n   */\n  mask: PropTypes.string,\n  /**\n   * Maximal selectable date. @DateIOType\n   */\n  maxDate: PropTypes.any,\n  /**\n   * Maximal selectable moment of time with binding to date, to set max time in each day use `maxTime`.\n   */\n  maxDateTime: PropTypes.any,\n  /**\n   * Max time acceptable time.\n   * For input validation date part of passed object will be ignored if `disableIgnoringDatePartForTimeValidation` not specified.\n   */\n  maxTime: PropTypes.any,\n  /**\n   * Minimal selectable date. @DateIOType\n   */\n  minDate: PropTypes.any,\n  /**\n   * Minimal selectable moment of time with binding to date, to set min time in each day use `minTime`.\n   */\n  minDateTime: PropTypes.any,\n  /**\n   * Min time acceptable time.\n   * For input validation date part of passed object will be ignored if `disableIgnoringDatePartForTimeValidation` not specified.\n   */\n  minTime: PropTypes.any,\n  /**\n   * Step over minutes.\n   * @default 1\n   */\n  minutesStep: PropTypes.number,\n  /**\n   * Callback fired when date is accepted @DateIOType.\n   * @template TValue\n   * @param {TValue} value The value that was just accepted.\n   */\n  onAccept: PropTypes.func,\n  /**\n   * Callback fired when the value (the selected date) changes @DateIOType.\n   * @template TValue\n   * @param {TValue} value The new parsed value.\n   * @param {string} keyboardInputValue The current value of the keyboard input.\n   */\n  onChange: PropTypes.func.isRequired,\n  /**\n   * Callback fired when the popup requests to be closed.\n   * Use in controlled mode (see open).\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback that fired when input value or new `value` prop validation returns **new** validation error (or value is valid after error).\n   * In case of validation error detected `reason` prop return non-null value and `TextField` must be displayed in `error` state.\n   * This can be used to render appropriate form error.\n   *\n   * [Read the guide](https://next.material-ui-pickers.dev/guides/forms) about form integration and error displaying.\n   * @DateIOType\n   *\n   * @template TError, TInputValue\n   * @param {TError} reason The reason why the current value is not valid.\n   * @param {TInputValue} value The invalid value.\n   */\n  onError: PropTypes.func,\n  /**\n   * Callback firing on month change @DateIOType.\n   * @template TDate\n   * @param {TDate} month The new month.\n   * @returns {void|Promise} -\n   */\n  onMonthChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be opened.\n   * Use in controlled mode (see open).\n   */\n  onOpen: PropTypes.func,\n  /**\n   * Callback fired on view change.\n   * @param {CalendarOrClockPickerView} view The new view.\n   */\n  onViewChange: PropTypes.func,\n  /**\n   * Callback firing on year change @DateIOType.\n   * @template TDate\n   * @param {TDate} year The new year.\n   */\n  onYearChange: PropTypes.func,\n  /**\n   * Control the popup or dialog open state.\n   */\n  open: PropTypes.bool,\n  /**\n   * Props to pass to keyboard adornment button.\n   */\n  OpenPickerButtonProps: PropTypes.object,\n  /**\n   * First view to show.\n   * Must be a valid option from `views` list\n   * @default 'day'\n   */\n  openTo: PropTypes.oneOf(['day', 'hours', 'minutes', 'month', 'seconds', 'year']),\n  /**\n   * Force rendering in particular orientation.\n   */\n  orientation: PropTypes.oneOf(['landscape', 'portrait']),\n  /**\n   * Make picker read only.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * Disable heavy animations.\n   * @default typeof navigator !== 'undefined' && /(android)/i.test(navigator.userAgent)\n   */\n  reduceAnimations: PropTypes.bool,\n  /**\n   * Custom renderer for day. Check the [PickersDay](https://mui.com/x/api/date-pickers/pickers-day/) component.\n   * @template TDate\n   * @param {TDate} day The day to render.\n   * @param {Array<TDate | null>} selectedDays The days currently selected.\n   * @param {PickersDayProps<TDate>} pickersDayProps The props of the day to render.\n   * @returns {JSX.Element} The element representing the day.\n   */\n  renderDay: PropTypes.func,\n  /**\n   * The `renderInput` prop allows you to customize the rendered input.\n   * The `props` argument of this render prop contains props of [TextField](https://mui.com/material-ui/api/text-field/#props) that you need to forward.\n   * Pay specific attention to the `ref` and `inputProps` keys.\n   * @example ```jsx\n   * renderInput={props => <TextField {...props} />}\n   * ````\n   * @param {MuiTextFieldPropsType} props The props of the input.\n   * @returns {React.ReactNode} The node to render as the input.\n   */\n  renderInput: PropTypes.func.isRequired,\n  /**\n   * Component displaying when passed `loading` true.\n   * @returns {React.ReactNode} The node to render when loading.\n   * @default () => <span data-mui-test=\"loading-progress\">...</span>\n   */\n  renderLoading: PropTypes.func,\n  /**\n   * Custom formatter to be passed into Rifm component.\n   * @param {string} str The un-formatted string.\n   * @returns {string} The formatted string.\n   */\n  rifmFormatter: PropTypes.func,\n  /**\n   * Right arrow icon aria-label text.\n   * @deprecated\n   */\n  rightArrowButtonText: PropTypes.string,\n  /**\n   * Disable specific date. @DateIOType\n   * @template TDate\n   * @param {TDate} day The date to test.\n   * @returns {boolean} Returns `true` if the date should be disabled.\n   */\n  shouldDisableDate: PropTypes.func,\n  /**\n   * Disable specific months dynamically.\n   * Works like `shouldDisableDate` but for month selection view @DateIOType.\n   * @template TDate\n   * @param {TDate} month The month to check.\n   * @returns {boolean} If `true` the month will be disabled.\n   */\n  shouldDisableMonth: PropTypes.func,\n  /**\n   * Dynamically check if time is disabled or not.\n   * If returns `false` appropriate time point will ot be acceptable.\n   * @param {number} timeValue The value to check.\n   * @param {ClockPickerView} clockType The clock type of the timeValue.\n   * @returns {boolean} Returns `true` if the time should be disabled\n   */\n  shouldDisableTime: PropTypes.func,\n  /**\n   * Disable specific years dynamically.\n   * Works like `shouldDisableDate` but for year selection view @DateIOType.\n   * @template TDate\n   * @param {TDate} year The year to test.\n   * @returns {boolean} Returns `true` if the year should be disabled.\n   */\n  shouldDisableYear: PropTypes.func,\n  /**\n   * If `true`, days that have `outsideCurrentMonth={true}` are displayed.\n   * @default false\n   */\n  showDaysOutsideCurrentMonth: PropTypes.bool,\n  /**\n   * If `true`, show the toolbar even in desktop mode.\n   */\n  showToolbar: PropTypes.bool,\n  /**\n   * Time tab icon.\n   */\n  timeIcon: PropTypes.node,\n  /**\n   * Component that will replace default toolbar renderer.\n   * @default DateTimePickerToolbar\n   */\n  ToolbarComponent: PropTypes.elementType,\n  /**\n   * Date format, that is displaying in toolbar.\n   */\n  toolbarFormat: PropTypes.string,\n  /**\n   * Mobile picker date value placeholder, displaying if `value` === `null`.\n   * @default '–'\n   */\n  toolbarPlaceholder: PropTypes.node,\n  /**\n   * Mobile picker title, displaying in the toolbar.\n   * @default 'Select date & time'\n   */\n  toolbarTitle: PropTypes.node,\n  /**\n   * The value of the picker.\n   */\n  value: PropTypes.any,\n  /**\n   * Array of views to show.\n   * @default ['year', 'day', 'hours', 'minutes']\n   */\n  views: PropTypes.arrayOf(PropTypes.oneOf(['day', 'hours', 'minutes', 'month', 'seconds', 'year']).isRequired)\n} : void 0;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}