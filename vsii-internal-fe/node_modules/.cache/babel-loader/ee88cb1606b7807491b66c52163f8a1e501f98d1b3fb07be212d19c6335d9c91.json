{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { isFragment } from 'react-is';\nimport { useTabContext } from '../TabsUnstyled';\nimport extractEventHandlers from '../utils/extractEventHandlers';\nconst nextItem = (list, item) => {\n  if (!list) {\n    return null;\n  }\n  if (list === item) {\n    return list.firstChild;\n  }\n  if (item && item.nextElementSibling) {\n    return item.nextElementSibling;\n  }\n  return list.firstChild;\n};\nconst previousItem = (list, item) => {\n  if (!list) {\n    return null;\n  }\n  if (list === item) {\n    return list.lastChild;\n  }\n  if (item && item.previousElementSibling) {\n    return item.previousElementSibling;\n  }\n  return list.lastChild;\n};\nconst moveFocus = (list, currentFocus, traversalFunction) => {\n  let wrappedOnce = false;\n  let nextFocus = traversalFunction(list, currentFocus);\n  while (list && nextFocus) {\n    // Prevent infinite loop.\n    if (nextFocus === list.firstChild) {\n      if (wrappedOnce) {\n        return;\n      }\n      wrappedOnce = true;\n    } // Same logic as useAutocomplete.js\n\n    const nextFocusDisabled = nextFocus.disabled || nextFocus.getAttribute('aria-disabled') === 'true';\n    if (!nextFocus.hasAttribute('tabindex') || nextFocusDisabled) {\n      // Move to the next element.\n      nextFocus = traversalFunction(list, nextFocus);\n    } else {\n      nextFocus.focus();\n      return;\n    }\n  }\n};\nconst useTabsList = parameters => {\n  const {\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledBy,\n    children,\n    ref\n  } = parameters;\n  const tabsListRef = /*#__PURE__*/React.createRef();\n  const handleRef = useForkRef(tabsListRef, ref);\n  const context = useTabContext();\n  if (context === null) {\n    throw new Error('No TabContext provided');\n  }\n  const {\n    value,\n    orientation = 'horizontal',\n    direction = 'ltr'\n  } = context;\n  const isRtl = direction === 'rtl';\n  const handleKeyDown = event => {\n    const list = tabsListRef.current;\n    const currentFocus = ownerDocument(list).activeElement; // Keyboard navigation assumes that [role=\"tab\"] are siblings\n    // though we might warn in the future about nested, interactive elements\n    // as a a11y violation\n\n    const role = currentFocus == null ? void 0 : currentFocus.getAttribute('role');\n    if (role !== 'tab') {\n      return;\n    }\n    let previousItemKey = orientation === 'horizontal' ? 'ArrowLeft' : 'ArrowUp';\n    let nextItemKey = orientation === 'horizontal' ? 'ArrowRight' : 'ArrowDown';\n    if (orientation === 'horizontal' && isRtl) {\n      // swap previousItemKey with nextItemKey\n      previousItemKey = 'ArrowRight';\n      nextItemKey = 'ArrowLeft';\n    }\n    switch (event.key) {\n      case previousItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, previousItem);\n        break;\n      case nextItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, nextItem);\n        break;\n      case 'Home':\n        event.preventDefault();\n        moveFocus(list, null, nextItem);\n        break;\n      case 'End':\n        event.preventDefault();\n        moveFocus(list, null, previousItem);\n        break;\n      default:\n        break;\n    }\n  };\n  const createHandleKeyDown = otherHandlers => event => {\n    var _otherHandlers$onKeyD;\n    handleKeyDown(event);\n    (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null ? void 0 : _otherHandlers$onKeyD.call(otherHandlers, event);\n  };\n  const getRootProps = (otherHandlers = {}) => {\n    const propsEventHandlers = extractEventHandlers(parameters);\n    const externalEventHandlers = _extends({}, propsEventHandlers, otherHandlers);\n    const ownEventHandlers = {\n      onKeyDown: createHandleKeyDown(externalEventHandlers)\n    };\n    const mergedEventHandlers = _extends({}, externalEventHandlers, ownEventHandlers);\n    return _extends({\n      'aria-label': ariaLabel,\n      'aria-labelledby': ariaLabelledBy,\n      'aria-orientation': orientation === 'vertical' ? 'vertical' : undefined,\n      role: 'tablist',\n      ref: handleRef\n    }, mergedEventHandlers);\n  };\n  const processChildren = React.useCallback(() => {\n    const valueToIndex = new Map();\n    let childIndex = 0;\n    const processedChildren = React.Children.map(children, child => {\n      if (! /*#__PURE__*/React.isValidElement(child)) {\n        return null;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        if (isFragment(child)) {\n          console.error([\"MUI: The Tabs component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n        }\n      }\n      const childValue = child.props.value === undefined ? childIndex : child.props.value;\n      valueToIndex.set(childValue, childIndex);\n      childIndex += 1;\n      return /*#__PURE__*/React.cloneElement(child, _extends({\n        value: childValue\n      }, childIndex === 1 && value === false && !child.props.tabIndex || value === childValue ? {\n        tabIndex: 0\n      } : {\n        tabIndex: -1\n      }));\n    });\n    return processedChildren;\n  }, [children, value]);\n  return {\n    isRtl,\n    orientation,\n    value,\n    processChildren,\n    getRootProps\n  };\n};\nexport default useTabsList;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}