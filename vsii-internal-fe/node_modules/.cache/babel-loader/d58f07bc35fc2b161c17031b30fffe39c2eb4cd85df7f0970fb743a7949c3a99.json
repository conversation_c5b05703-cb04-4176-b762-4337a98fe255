{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport function getPickersDayUtilityClass(slot) {\n  return generateUtilityClass('MuiPickersDay', slot);\n}\nexport const pickersDayClasses = generateUtilityClasses('MuiPickersDay', ['root', 'dayWithMargin', 'dayOutsideMonth', 'hiddenDaySpacingFiller', 'today', 'selected', 'disabled']);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}