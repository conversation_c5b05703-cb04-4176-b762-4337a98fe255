{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/skills-manage/SkillsUpdateTBody.tsx\";\n/* eslint-disable prettier/prettier */\n// material-ui\nimport { IconButton, Stack, TableBody, TableCell, TableRow, Tooltip } from '@mui/material';\n\n// assets\nimport EditTwoToneIcon from '@mui/icons-material/EditTwoTone';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport DownloadIcon from '@mui/icons-material/Download';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\nimport { convertStatus } from 'utils/common';\n\n// project imports\nimport { PERMISSIONS } from 'constants/Permission';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SkillsUpdateTBody = props => {\n  const {\n    pageNumber,\n    pageSize,\n    members,\n    handleShowDetail,\n    handleOpenViewPDF,\n    handleOpenDownloadCV\n  } = props;\n  const {\n    skillsUpdate\n  } = PERMISSIONS.report.skillManage;\n  return /*#__PURE__*/_jsxDEV(TableBody, {\n    children: members === null || members === void 0 ? void 0 : members.map((mem, key) => /*#__PURE__*/_jsxDEV(TableRow, {\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"center\",\n        children: pageSize * pageNumber + key + 1\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"center\",\n        children: mem.memberCode\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"center\",\n        children: mem.fullNameEn\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"left\",\n        children: [mem === null || mem === void 0 ? void 0 : mem.title, \"-\", mem === null || mem === void 0 ? void 0 : mem.titleName]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"center\",\n        children: mem.department\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"center\",\n        children: convertStatus(mem.status)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 21\n      }, this), checkAllowedPermission(skillsUpdate.edit) || checkAllowedPermission(skillsUpdate.download) || checkAllowedPermission(skillsUpdate.viewCV) ? /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          justifyContent: \"center\",\n          spacing: 1,\n          children: [checkAllowedPermission(skillsUpdate.edit) && /*#__PURE__*/_jsxDEV(Tooltip, {\n            placement: \"top\",\n            title: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: \"edit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 48\n            }, this),\n            onClick: () => handleShowDetail({\n              memberCode: mem.memberCode,\n              userName: mem === null || mem === void 0 ? void 0 : mem.userName\n            }),\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              \"aria-label\": \"delete\",\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(EditTwoToneIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 37\n          }, this), checkAllowedPermission(skillsUpdate.download) && /*#__PURE__*/_jsxDEV(Tooltip, {\n            placement: \"top\",\n            title: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: \"download-word\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 69\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              \"aria-label\": \"delete\",\n              size: \"small\",\n              onClick: () => handleOpenDownloadCV(mem.memberCode),\n              children: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 37\n          }, this), checkAllowedPermission(skillsUpdate.viewCV) && /*#__PURE__*/_jsxDEV(Tooltip, {\n            placement: \"top\",\n            title: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: \"pdf\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 69\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              \"aria-label\": \"delete\",\n              size: \"small\",\n              onClick: () => handleOpenViewPDF(mem.memberCode),\n              children: /*#__PURE__*/_jsxDEV(PictureAsPdfIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 25\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false)]\n    }, key, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 17\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 9\n  }, this);\n};\n_c = SkillsUpdateTBody;\nexport default SkillsUpdateTBody;\nvar _c;\n$RefreshReg$(_c, \"SkillsUpdateTBody\");", "map": {"version": 3, "names": ["IconButton", "<PERSON><PERSON>", "TableBody", "TableCell", "TableRow", "<PERSON><PERSON><PERSON>", "EditTwoToneIcon", "PictureAsPdfIcon", "DownloadIcon", "FormattedMessage", "convertStatus", "PERMISSIONS", "checkAllowedPermission", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SkillsUpdateTBody", "props", "pageNumber", "pageSize", "members", "handleShowDetail", "handleOpenViewPDF", "handleOpenDownloadCV", "skillsUpdate", "report", "skillManage", "children", "map", "mem", "key", "align", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "memberCode", "fullNameEn", "title", "<PERSON><PERSON><PERSON>", "department", "status", "edit", "download", "viewCV", "direction", "alignItems", "justifyContent", "spacing", "placement", "id", "onClick", "userName", "size", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/skills-manage/SkillsUpdateTBody.tsx"], "sourcesContent": ["/* eslint-disable prettier/prettier */\n// material-ui\nimport { IconButton, Stack, TableBody, TableCell, TableRow, Tooltip } from '@mui/material';\n\n// assets\nimport EditTwoToneIcon from '@mui/icons-material/EditTwoTone';\nimport PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';\nimport DownloadIcon from '@mui/icons-material/Download';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\nimport { convertStatus } from 'utils/common';\n\n// project imports\nimport { PERMISSIONS } from 'constants/Permission';\nimport { checkAllowedPermission } from 'utils/authorization';\n\ninterface ISkillsUpdateTBodyProps {\n    pageNumber: number;\n    pageSize: number;\n    members: any[];\n    handleShowDetail: (params: { memberCode: string; userName: string }) => void;\n    handleOpenViewPDF: (memberCode: string) => void;\n    handleOpenDownloadCV: (memberCode: string) => void\n}\n\nconst SkillsUpdateTBody = (props: ISkillsUpdateTBodyProps) => {\n    const { pageNumber, pageSize, members, handleShowDetail, handleOpenViewPDF, handleOpenDownloadCV } = props;\n    const { skillsUpdate } = PERMISSIONS.report.skillManage;\n\n    return (\n        <TableBody>\n            {members?.map((mem, key) => (\n                <TableRow key={key}>\n                    <TableCell align=\"center\">{pageSize * pageNumber + key + 1}</TableCell>\n                    <TableCell align=\"center\">{mem.memberCode}</TableCell>\n                    <TableCell align=\"center\">{mem.fullNameEn}</TableCell>\n                    <TableCell align=\"left\">\n                        {mem?.title}-{mem?.titleName}\n                    </TableCell>\n                    <TableCell align=\"center\">{mem.department}</TableCell>\n                    <TableCell align=\"center\">{convertStatus(mem.status)}</TableCell>\n                    {checkAllowedPermission(skillsUpdate.edit) ||\n                        checkAllowedPermission(skillsUpdate.download) ||\n                        checkAllowedPermission(skillsUpdate.viewCV) ? (\n                        <TableCell>\n                            <Stack direction=\"row\" alignItems=\"center\" justifyContent=\"center\" spacing={1}>\n                                {checkAllowedPermission(skillsUpdate.edit) && (\n                                    <Tooltip\n                                        placement=\"top\"\n                                        title={<FormattedMessage id=\"edit\" />}\n                                        onClick={() => handleShowDetail({ memberCode: mem.memberCode, userName: mem?.userName })}\n                                    >\n                                        <IconButton aria-label=\"delete\" size=\"small\">\n                                            <EditTwoToneIcon />\n                                        </IconButton>\n                                    </Tooltip>\n                                )}\n                                {checkAllowedPermission(skillsUpdate.download) && (\n                                    <Tooltip placement=\"top\" title={<FormattedMessage id=\"download-word\" />}>\n                                        <IconButton\n                                            aria-label=\"delete\"\n                                            size=\"small\"\n                                            onClick={() => handleOpenDownloadCV(mem.memberCode)}\n                                        >\n                                            <DownloadIcon />\n                                        </IconButton>\n                                    </Tooltip>\n                                )}\n                                {checkAllowedPermission(skillsUpdate.viewCV) && (\n                                    <Tooltip placement=\"top\" title={<FormattedMessage id=\"pdf\" />}>\n                                        <IconButton\n                                            aria-label=\"delete\"\n                                            size=\"small\"\n                                            onClick={() => handleOpenViewPDF(mem.memberCode)}\n                                        >\n                                            <PictureAsPdfIcon />\n                                        </IconButton>\n                                    </Tooltip>\n                                )}\n                            </Stack>\n                        </TableCell>\n                    ) : (\n                        <></>\n                    )}\n                </TableRow>\n            ))}\n        </TableBody>\n    );\n};\n\nexport default SkillsUpdateTBody;\n"], "mappings": ";AAAA;AACA;AACA,SAASA,UAAU,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,eAAe;;AAE1F;AACA,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,YAAY,MAAM,8BAA8B;;AAEvD;AACA,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,aAAa,QAAQ,cAAc;;AAE5C;AACA,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,sBAAsB,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAW7D,MAAMC,iBAAiB,GAAIC,KAA8B,IAAK;EAC1D,MAAM;IAAEC,UAAU;IAAEC,QAAQ;IAAEC,OAAO;IAAEC,gBAAgB;IAAEC,iBAAiB;IAAEC;EAAqB,CAAC,GAAGN,KAAK;EAC1G,MAAM;IAAEO;EAAa,CAAC,GAAGd,WAAW,CAACe,MAAM,CAACC,WAAW;EAEvD,oBACIb,OAAA,CAACZ,SAAS;IAAA0B,QAAA,EACLP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,GAAG,CAAC,CAACC,GAAG,EAAEC,GAAG,kBACnBjB,OAAA,CAACV,QAAQ;MAAAwB,QAAA,gBACLd,OAAA,CAACX,SAAS;QAAC6B,KAAK,EAAC,QAAQ;QAAAJ,QAAA,EAAER,QAAQ,GAAGD,UAAU,GAAGY,GAAG,GAAG;MAAC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACvEtB,OAAA,CAACX,SAAS;QAAC6B,KAAK,EAAC,QAAQ;QAAAJ,QAAA,EAAEE,GAAG,CAACO;MAAU;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACtDtB,OAAA,CAACX,SAAS;QAAC6B,KAAK,EAAC,QAAQ;QAAAJ,QAAA,EAAEE,GAAG,CAACQ;MAAU;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACtDtB,OAAA,CAACX,SAAS;QAAC6B,KAAK,EAAC,MAAM;QAAAJ,QAAA,GAClBE,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAES,KAAK,EAAC,GAAC,EAACT,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEU,SAAS;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACZtB,OAAA,CAACX,SAAS;QAAC6B,KAAK,EAAC,QAAQ;QAAAJ,QAAA,EAAEE,GAAG,CAACW;MAAU;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACtDtB,OAAA,CAACX,SAAS;QAAC6B,KAAK,EAAC,QAAQ;QAAAJ,QAAA,EAAElB,aAAa,CAACoB,GAAG,CAACY,MAAM;MAAC;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAChExB,sBAAsB,CAACa,YAAY,CAACkB,IAAI,CAAC,IACtC/B,sBAAsB,CAACa,YAAY,CAACmB,QAAQ,CAAC,IAC7ChC,sBAAsB,CAACa,YAAY,CAACoB,MAAM,CAAC,gBAC3C/B,OAAA,CAACX,SAAS;QAAAyB,QAAA,eACNd,OAAA,CAACb,KAAK;UAAC6C,SAAS,EAAC,KAAK;UAACC,UAAU,EAAC,QAAQ;UAACC,cAAc,EAAC,QAAQ;UAACC,OAAO,EAAE,CAAE;UAAArB,QAAA,GACzEhB,sBAAsB,CAACa,YAAY,CAACkB,IAAI,CAAC,iBACtC7B,OAAA,CAACT,OAAO;YACJ6C,SAAS,EAAC,KAAK;YACfX,KAAK,eAAEzB,OAAA,CAACL,gBAAgB;cAAC0C,EAAE,EAAC;YAAM;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtCgB,OAAO,EAAEA,CAAA,KAAM9B,gBAAgB,CAAC;cAAEe,UAAU,EAAEP,GAAG,CAACO,UAAU;cAAEgB,QAAQ,EAAEvB,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEuB;YAAS,CAAC,CAAE;YAAAzB,QAAA,eAEzFd,OAAA,CAACd,UAAU;cAAC,cAAW,QAAQ;cAACsD,IAAI,EAAC,OAAO;cAAA1B,QAAA,eACxCd,OAAA,CAACR,eAAe;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACZ,EACAxB,sBAAsB,CAACa,YAAY,CAACmB,QAAQ,CAAC,iBAC1C9B,OAAA,CAACT,OAAO;YAAC6C,SAAS,EAAC,KAAK;YAACX,KAAK,eAAEzB,OAAA,CAACL,gBAAgB;cAAC0C,EAAE,EAAC;YAAe;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAR,QAAA,eACpEd,OAAA,CAACd,UAAU;cACP,cAAW,QAAQ;cACnBsD,IAAI,EAAC,OAAO;cACZF,OAAO,EAAEA,CAAA,KAAM5B,oBAAoB,CAACM,GAAG,CAACO,UAAU,CAAE;cAAAT,QAAA,eAEpDd,OAAA,CAACN,YAAY;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACZ,EACAxB,sBAAsB,CAACa,YAAY,CAACoB,MAAM,CAAC,iBACxC/B,OAAA,CAACT,OAAO;YAAC6C,SAAS,EAAC,KAAK;YAACX,KAAK,eAAEzB,OAAA,CAACL,gBAAgB;cAAC0C,EAAE,EAAC;YAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAR,QAAA,eAC1Dd,OAAA,CAACd,UAAU;cACP,cAAW,QAAQ;cACnBsD,IAAI,EAAC,OAAO;cACZF,OAAO,EAAEA,CAAA,KAAM7B,iBAAiB,CAACO,GAAG,CAACO,UAAU,CAAE;cAAAT,QAAA,eAEjDd,OAAA,CAACP,gBAAgB;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACZ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,gBAEZtB,OAAA,CAAAE,SAAA,mBAAI,CACP;IAAA,GAnDUe,GAAG;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAoDR,CACb;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEpB,CAAC;AAACmB,EAAA,GA/DItC,iBAAiB;AAiEvB,eAAeA,iBAAiB;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}