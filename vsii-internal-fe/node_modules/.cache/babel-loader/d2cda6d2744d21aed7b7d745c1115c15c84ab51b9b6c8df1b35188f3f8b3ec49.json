{"ast": null, "code": "// material-ui\nimport{TableBody,TableCell,TableRow,IconButton,Stack,Tooltip}from'@mui/material';import SpeakerNotesIcon from'@mui/icons-material/SpeakerNotes';// project imports\nimport{checkAllowedPermission}from'utils/authorization';import{PERMISSIONS}from'constants/Permission';// import { IconCheck } from '@tabler/icons';\nimport{formatTableCellMemberInProject}from'utils/common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";// const isNotLogTime = (notLogTime: boolean) => {\n//     return notLogTime ? <IconCheck /> : '';\n// };\nconst ListProjectTeamTBody=props=>{const{pageNumber,pageSize,projectTeam,handleOpenCommentDialog}=props;const{ResourcesInProjects}=PERMISSIONS.report;return/*#__PURE__*/_jsx(TableBody,{children:projectTeam.map((member,key)=>{var _member$comment;return/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:pageSize*pageNumber+key+1}),/*#__PURE__*/_jsx(TableCell,{children:member.memberCode}),/*#__PURE__*/_jsxs(TableCell,{children:[member.firstName,\" \",member.lastName]}),/*#__PURE__*/_jsx(TableCell,{children:member.userTitle}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:member.userDept}),/*#__PURE__*/_jsx(TableCell,{children:formatTableCellMemberInProject(member.mainHeadCount)}),/*#__PURE__*/_jsx(TableCell,{children:formatTableCellMemberInProject(member.subHeadCount)}),/*#__PURE__*/_jsx(TableCell,{children:formatTableCellMemberInProject(member.projectNonBillable)}),checkAllowedPermission(ResourcesInProjects.commentDetail)&&/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Stack,{direction:\"row\",justifyContent:\"center\",alignItems:\"center\",children:/*#__PURE__*/_jsx(Tooltip,{placement:\"top\",title:(_member$comment=member.comment)===null||_member$comment===void 0?void 0:_member$comment.note,onClick:()=>handleOpenCommentDialog(member.userId,\"\".concat(member.firstName,\" \").concat(member.lastName)),children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"comment\",size:\"small\",children:/*#__PURE__*/_jsx(SpeakerNotesIcon,{sx:{fontSize:'1.1rem'}})})})})})]},key);})});};export default ListProjectTeamTBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}