{"ast": null, "code": "// yup\nimport * as yup from 'yup';\n\n// third party\nimport moment from 'moment';\n\n// project imports\nimport { REGEX_CONSTANTS } from 'constants/Validation';\nimport { VALIDATE_MESSAGES } from 'constants/Message';\nimport { getCurrentYear } from 'utils/date';\nimport { CONTRACT_TYPE_SALE_REPORT, DEPARTMENTS, E_BIDDING_STATUS, ROLE_TYPE, UNIT_SALE_REPORT, paginationParamDefault } from 'constants/Common';\n\n// ============== Monthly Production Performance ============== //\n\nexport const monthlyProductionPerformanceFilterConfig = {\n  year: getCurrentYear(),\n  month: [],\n  language: 'en'\n};\nexport const monthlyProductionPerformanceFilterSchema = yup.object().shape({\n  year: yup.string(),\n  month: yup.array()\n});\nexport const monthlyProductionPerformanceInfoDefault = {\n  months: [],\n  departments: [],\n  companyTotals: {\n    totalHCs: [],\n    productivity: [],\n    saleTotals: []\n  }\n};\n\n// Edit HeadCount Form\nexport const productivityHeadCountEditFormSchema = yup.object().shape({\n  year: yup.string().nullable(),\n  month: yup.string().nullable(),\n  value: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED).matches(REGEX_CONSTANTS.REGEX_NUMBER, VALIDATE_MESSAGES.INVALID_NUMBER)\n});\nexport const productivityHeadCountEditFormDefault = {\n  year: getCurrentYear(),\n  month: '1',\n  value: ''\n};\n\n// ============== Sales Lead ============== //\n// Sales Lead Form Search\n\nexport const salesLeadFilterConfig = {\n  ...paginationParamDefault,\n  supplierName: '',\n  partnerName: '',\n  status: '',\n  receivedDate: null,\n  picUserName: null,\n  fromDate: null,\n  toDate: null\n};\nexport const salesLeadFilterShemcha = yup.object().shape({\n  partnerName: yup.string().nullable(),\n  supplierName: yup.string().nullable(),\n  status: yup.string().nullable(),\n  receivedDate: yup.date().nullable(),\n  picUserName: yup.object().shape({\n    value: yup.string(),\n    label: yup.string()\n  }).nullable(),\n  fromDate: yup.date().nullable(),\n  toDate: yup.date().nullable()\n});\n\n// Add or Edit Requests Checking Form\nexport const addOrEditRequestsCheckingFormDefault = {\n  partnerName: '',\n  request: '',\n  timeline: '',\n  status: '',\n  note: '',\n  picUserName: null,\n  domain: '',\n  possibility: '',\n  quantity: '',\n  receivedDate: null,\n  technology: ''\n};\nexport const addOrEditRequestsCheckingFormSchema = yup.object().shape({\n  partnerName: yup.string().matches(REGEX_CONSTANTS.REGEX_SPECIAL_CHARACTERS, VALIDATE_MESSAGES.SPECIAL_CHARACTERS_PARTER_NAME).matches(REGEX_CONSTANTS.REGEX_NO_NUMBER, VALIDATE_MESSAGES.NO_NUMBER).required(VALIDATE_MESSAGES.REQUIRED),\n  request: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n  timeline: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n  status: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n  note: yup.string(),\n  picUserName: yup.object().shape({\n    value: yup.string(),\n    label: yup.string()\n  }).nullable().required(VALIDATE_MESSAGES.REQUIRED),\n  domain: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n  possibility: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n  quantity: yup.string().matches(REGEX_CONSTANTS.REGEX_NUMBER, VALIDATE_MESSAGES.INVALID_NUMBER).nullable().required(VALIDATE_MESSAGES.REQUIRED),\n  receivedDate: yup.date().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n  technology: yup.string().required(VALIDATE_MESSAGES.REQUIRED)\n});\n\n// Add or Edit Supplier Checking Form\n\nexport const addOrEditSupplierCheckingFormDefault = {\n  picUserName: null,\n  note: '',\n  fromDate: null,\n  toDate: null,\n  supplierName: '',\n  technology: '',\n  unitPrice: null,\n  workType: '',\n  quantity: ''\n};\nexport const addOrEditSupplierCheckingFormSchema = yup.object().shape({\n  note: yup.string(),\n  fromDate: yup.date().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n  toDate: yup.date().nullable().required(VALIDATE_MESSAGES.REQUIRED).test('toDate-after-fromDate', VALIDATE_MESSAGES.AFTER_DAY, function (value) {\n    const fromDate = this.resolve(yup.ref('fromDate'));\n    return !value || !fromDate || moment(value).isSameOrAfter(fromDate, 'day');\n  }),\n  picUserName: yup.object().shape({\n    value: yup.string(),\n    label: yup.string()\n  }).nullable().required(VALIDATE_MESSAGES.REQUIRED),\n  supplierName: yup.string().matches(REGEX_CONSTANTS.REGEX_SPECIAL_CHARACTERS, VALIDATE_MESSAGES.SPECIAL_CHARACTERS_SUPPLIER_NAME).matches(REGEX_CONSTANTS.REGEX_NO_NUMBER, VALIDATE_MESSAGES.NO_NUMBER).required(VALIDATE_MESSAGES.REQUIRED),\n  quantity: yup.string().nullable().matches(REGEX_CONSTANTS.REGEX_NUMBER, VALIDATE_MESSAGES.INVALID_NUMBER),\n  technology: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n  unitPrice: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n  workType: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED)\n});\nexport const hcInfoFormDefault = {\n  role: '',\n  rate: '',\n  rateUSD: '',\n  quantity: '',\n  amount: ''\n};\nexport const hcInfoBuiddingFormDefault = {\n  role: '',\n  rate: '',\n  rateVND: '',\n  quantity: '',\n  amount: ''\n};\nexport const HcInfoDefault = {\n  role: '',\n  rate: 0,\n  rateUSD: 0,\n  quantity: 0,\n  amount: 0\n};\nexport const productionPerformanceAddOrEditFormDefault = {\n  idHexString: '',\n  year: getCurrentYear(),\n  month: 0,\n  projectId: null,\n  departmentId: '',\n  projectName: '',\n  contractSize: '',\n  serviceType: '',\n  contractType: CONTRACT_TYPE_SALE_REPORT.TM,\n  originalContractSize: '',\n  unit: UNIT_SALE_REPORT.MAN_MONTH,\n  contractAllocation: '',\n  duration: {\n    fromDate: null,\n    toDate: null\n  },\n  paymentTerm: '1',\n  currency: '',\n  standardWorkingDay: '',\n  exchangeRate: 0,\n  delivered: 0,\n  receivable: 0,\n  received: 0,\n  financial: 0,\n  deliveredCurrency: 0,\n  receivableCurrency: 0,\n  receivedCurrency: 0,\n  financialCurrency: 0,\n  hcInfo: []\n};\n\n//schema\nexport const productionPerformanceAddOrEditFormSchema = yup.object().shape({\n  year: yup.string().nullable(),\n  idHexString: yup.string().nullable(),\n  month: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n  departmentId: yup.string().nullable(),\n  serviceType: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n  projectId: yup.object().when('departmentId', {\n    is: departmentId => departmentId === DEPARTMENTS.SCS || departmentId === DEPARTMENTS.PRD,\n    then: yup.object().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n    otherwise: yup.object().nullable()\n  }),\n  projectName: yup.string().when('departmentId', {\n    is: DEPARTMENTS.SMD,\n    then: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n    otherwise: yup.string().nullable()\n  }),\n  contractSize: yup.string().when(['departmentId', 'contractType'], {\n    is: (departmentId, contractType) => departmentId === DEPARTMENTS.SCS && contractType === CONTRACT_TYPE_SALE_REPORT.FIXED_COST,\n    then: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n    otherwise: yup.string().nullable()\n  }),\n  contractType: yup.string().when('departmentId', {\n    is: DEPARTMENTS.SCS,\n    then: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n    otherwise: yup.string().nullable()\n  }),\n  unit: yup.string().when('departmentId', {\n    is: DEPARTMENTS.SCS,\n    then: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n    otherwise: yup.string().nullable()\n  }),\n  originalContractSize: yup.string().when(['departmentId', 'contractType'], {\n    is: (departmentId, contractType) => departmentId === DEPARTMENTS.SCS && contractType === CONTRACT_TYPE_SALE_REPORT.FIXED_COST,\n    then: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n    otherwise: yup.string().nullable()\n  }),\n  contractAllocation: yup.string().when('departmentId', {\n    is: DEPARTMENTS.SCS,\n    then: yup.string().nullable(),\n    otherwise: yup.string().nullable()\n  }),\n  duration: yup.object().when('departmentId', {\n    is: DEPARTMENTS.SCS,\n    then: yup.object().shape({\n      fromDate: yup.date().required(VALIDATE_MESSAGES.REQUIRED),\n      toDate: yup.date().required(VALIDATE_MESSAGES.REQUIRED).test('toDate-after-fromDate', VALIDATE_MESSAGES.AFTER_DAY, function (value) {\n        const fromDate = this.resolve(yup.ref('fromDate'));\n        return !value || !fromDate || moment(value).isSameOrAfter(fromDate, 'day');\n      })\n    }),\n    otherwise: yup.object().shape({\n      fromDate: yup.date().nullable(),\n      toDate: yup.date().nullable().test('toDate-after-fromDate', VALIDATE_MESSAGES.AFTER_DAY, function (value) {\n        const fromDate = this.resolve(yup.ref('fromDate'));\n        return !value || !fromDate || moment(value).isSameOrAfter(fromDate, 'day');\n      })\n    })\n  }),\n  paymentTerm: yup.string().nullable(),\n  currency: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n  delivered: yup.number().when('departmentId', {\n    is: DEPARTMENTS.SCS,\n    then: yup.number().nullable().typeError(VALIDATE_MESSAGES.INVALID_NUMBER).positive(VALIDATE_MESSAGES.POSITIVE_NUMBER).transform((_, val) => val !== '' ? Number(val) : null).min(0, VALIDATE_MESSAGES.POSITIVE_NUMBER),\n    otherwise: yup.number().nullable()\n  }),\n  receivable: yup.number().when('departmentId', {\n    is: DEPARTMENTS.SCS,\n    then: yup.number().nullable().typeError(VALIDATE_MESSAGES.INVALID_NUMBER).positive(VALIDATE_MESSAGES.POSITIVE_NUMBER).transform((_, val) => val !== '' ? Number(val) : null).min(0, VALIDATE_MESSAGES.POSITIVE_NUMBER),\n    otherwise: yup.number().nullable()\n  }),\n  received: yup.number().when('departmentId', {\n    is: DEPARTMENTS.SCS,\n    then: yup.number().nullable().typeError(VALIDATE_MESSAGES.INVALID_NUMBER).positive(VALIDATE_MESSAGES.POSITIVE_NUMBER).transform((_, val) => val !== '' ? Number(val) : null).min(0, VALIDATE_MESSAGES.POSITIVE_NUMBER),\n    otherwise: yup.number().nullable()\n  }),\n  financial: yup.number().when('departmentId', {\n    is: DEPARTMENTS.SCS,\n    then: yup.number().nullable().typeError(VALIDATE_MESSAGES.INVALID_NUMBER).positive(VALIDATE_MESSAGES.POSITIVE_NUMBER).transform((_, val) => val !== '' ? Number(val) : null).min(0, VALIDATE_MESSAGES.POSITIVE_NUMBER),\n    otherwise: yup.number().nullable()\n  }),\n  hcInfo: yup.array().when(['departmentId', 'contractType'], {\n    is: (departmentId, contractType) => departmentId === DEPARTMENTS.SCS && contractType === CONTRACT_TYPE_SALE_REPORT.TM,\n    then: yup.array().of(yup.object().shape({\n      role: yup.mixed().test('isRoleValid', VALIDATE_MESSAGES.REQUIRED, value => value === null || ROLE_TYPE.map(option => option.value).includes(value)).required(VALIDATE_MESSAGES.REQUIRED).nullable(),\n      rate: yup.number().nullable().required(VALIDATE_MESSAGES.REQUIRED).transform((value, originalValue) => {\n        if (originalValue === '') return null;\n        return value;\n      }),\n      rateUSD: yup.number().nullable().required(VALIDATE_MESSAGES.REQUIRED).transform((value, originalValue) => {\n        if (originalValue === '') return null;\n        return value;\n      }),\n      quantity: yup.number().nullable().required(VALIDATE_MESSAGES.REQUIRED).transform((value, originalValue) => {\n        if (originalValue === '') return null;\n        return value;\n      }),\n      amount: yup.number().nullable().transform((value, originalValue) => {\n        if (originalValue === '') return null;\n        return value;\n      })\n    }))\n  }),\n  exchangeRate: yup.number().when('departmentId', {\n    is: DEPARTMENTS.SCS,\n    then: yup.number().nullable(),\n    otherwise: yup.number().nullable()\n  }),\n  standardWorkingDay: yup.number().when('departmentId', {\n    is: DEPARTMENTS.SCS,\n    then: yup.number().nullable().transform((value, originalValue) => {\n      const parsedValue = parseFloat(originalValue);\n      return isNaN(parsedValue) ? undefined : parsedValue;\n    }),\n    otherwise: yup.number().nullable()\n  })\n});\n// ============== SALE PIPELINE ==============\n// ============== OnGoing ==============\n\nexport const onGoingConfig = {\n  ...paginationParamDefault,\n  size: 100,\n  type: '',\n  year: getCurrentYear(),\n  productionPerformanceIdHexString: null,\n  status: '',\n  language: 'en'\n};\nexport const onGoingSchema = yup.object().shape({\n  type: yup.string().nullable(),\n  year: yup.string().nullable(),\n  productionPerformanceIdHexString: yup.object().shape({\n    value: yup.string(),\n    label: yup.string()\n  }).nullable(),\n  status: yup.string().nullable()\n});\nexport const editOnGoingDefaultValue = {\n  idHexString: '',\n  projectInfo: {\n    year: null,\n    contractNo: '',\n    customer: '',\n    probability: '',\n    productionPerformanceIdHexString: null,\n    projectName: '',\n    status: '',\n    revenuePercent: null,\n    type: '',\n    serviceType: '',\n    contractDueDate: null,\n    contractDurationFrom: null,\n    contractDurationTo: null,\n    contractType: '',\n    note: '',\n    warrantyTime: ''\n  },\n  // financial info\n  financialInfo: {\n    sizeVND: '',\n    sizeUSD: '',\n    managementRevenueAllocated: '',\n    accountRevenueAllocatedVNDValue: '',\n    newSaleUSD: '',\n    currency: '',\n    exchangeRate: '',\n    acctReceivables: '',\n    netEarn: '',\n    paid: '',\n    remain: '',\n    quarterLicense1st: '',\n    quarterLicense2nd: '',\n    quarterLicense3rd: '',\n    quarterLicense4th: '',\n    licenseFee: ''\n  },\n  // hc info\n  hcInfo: {\n    billableHcs: '',\n    hcs: '',\n    teamLeadHcs: '',\n    seniorHcs: '',\n    middleHcs: '',\n    juniorHcs: '',\n    quarter1st: '',\n    quarter2nd: '',\n    quarter3rd: '',\n    quarter4th: '',\n    totalNewSale: '',\n    totalBillable: '',\n    monthlyHCList: [{\n      month: 0,\n      hcMonthly: 0,\n      billableDay: 0,\n      billable: 0\n    }]\n  },\n  // other info\n  otherInfo: {\n    contact: null,\n    presaleFolder: '',\n    customerContact: '',\n    phoneNumber: '',\n    emailAddress: ''\n  }\n};\nexport const editOnGoingSchema = yup.object().shape({\n  idHexString: yup.string().nullable(),\n  // project info\n  projectInfo: yup.object().shape({\n    year: yup.number().nullable(),\n    contractNo: yup.string().nullable(),\n    customer: yup.string().nullable(),\n    probability: yup.number().typeError(VALIDATE_MESSAGES.INVALID_NUMBER).transform((_, val) => val !== '' ? Number(val) : null).min(0, ({\n      min\n    }) => `${VALIDATE_MESSAGES.LARGER_OR_EQUAL}${min}`).max(100, ({\n      max\n    }) => `${VALIDATE_MESSAGES.LESS_OR_EQUAL} ${max}`).nullable(),\n    productionPerformanceIdHexString: yup.object().shape({\n      value: yup.string().nullable(),\n      label: yup.string()\n    }).nullable().required(VALIDATE_MESSAGES.REQUIRED),\n    projectName: yup.string().nullable(),\n    status: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n    type: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n    serviceType: yup.string().nullable(),\n    contractType: yup.string().nullable(),\n    contractDueDate: yup.date().nullable(),\n    contractDurationFrom: yup.date().nullable(),\n    contractDurationTo: yup.date().nullable(),\n    note: yup.string().nullable(),\n    warrantyTime: yup.string()\n  }),\n  // financial info\n  financialInfo: yup.object().shape({\n    sizeVND: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    sizeUSD: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    managementRevenueAllocated: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    accountRevenueAllocatedVNDValue: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    newSaleUSD: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    currency: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    exchangeRate: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    acctReceivables: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    netEarn: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    paid: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    remain: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    quarterLicense1st: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    quarterLicense2nd: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    quarterLicense3rd: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    quarterLicense4th: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    licenseFee: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable()\n  }),\n  // hc info\n  hcInfo: yup.object().shape({\n    billableHcs: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    hcs: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    seniorHcs: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    middleHcs: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    juniorHcs: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    teamLeadHcs: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    quarter1st: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    quarter2nd: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    quarter3rd: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    quarter4th: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    totalNewSale: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable(),\n    totalBillable: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable()\n  }),\n  // other info\n  otherInfo: yup.object().shape({\n    contact: yup.object().shape({\n      value: yup.string().nullable(),\n      label: yup.string()\n    }).nullable(),\n    presaleFolder: yup.string().nullable(),\n    customerContact: yup.string().nullable(),\n    phoneNumber: yup.string().transform(value => value === '' ? null : value).matches(REGEX_CONSTANTS.REGEX_PHONE_NUMBER, VALIDATE_MESSAGES.FORMAT_PHONE_NUMBER).max(10, VALIDATE_MESSAGES.MAX_PHONE_NUMBER).nullable(),\n    emailAddress: yup.string().nullable()\n  })\n});\n\n// ============== Bidding ==============\n// Filter\n\nexport const biddingFilterConfig = {\n  ...paginationParamDefault,\n  size: 100,\n  type: '',\n  year: getCurrentYear(),\n  status: '',\n  language: 'en',\n  projectName: '',\n  customer: '',\n  searchModeOfProject: 'contains',\n  searchModeOfCustomer: 'contains'\n};\nexport const biddingFilterShemcha = yup.object().shape({\n  type: yup.string().nullable(),\n  year: yup.string(),\n  projectName: yup.string().nullable(),\n  status: yup.string().nullable(),\n  customer: yup.string().nullable(),\n  searchModeOfProject: yup.string().nullable(),\n  searchModeOfCustomer: yup.string().nullable()\n});\n\n// Add or Edit\nexport const addOrEditBiddingConfig = {\n  // Project Info\n  project: {\n    customer: '',\n    projectName: '',\n    projectRedmineId: null,\n    type: '',\n    // sale pipeline type\n    serviceType: '',\n    contractType: 'TM',\n    note: '',\n    department: '',\n    contractNo: '',\n    probability: '',\n    status: E_BIDDING_STATUS.BIDDING,\n    contractDueDate: '',\n    contractDurationFrom: '',\n    contractDurationTo: '',\n    warrantyTime: '',\n    revenuePercent: ''\n  },\n  // Financial Info\n  financialInfo: {\n    originalContractSize: '',\n    currency: '',\n    exchangeRate: '',\n    sizeVND: '',\n    sizeUSD: '',\n    managementRevenueAllocated: '',\n    accountRevenueAllocatedVND: '',\n    newSaleUSD: '',\n    licenseFee: '',\n    acctReceivables: '',\n    netEarn: '',\n    paid: '',\n    remain: '',\n    quarterLicense1st: '',\n    quarterLicense2nd: '',\n    quarterLicense3rd: '',\n    quarterLicense4th: ''\n  },\n  // Hc Info\n  hcInfo: {\n    billableHcs: '',\n    hcs: '',\n    teamLeadHcs: '',\n    seniorHcs: '',\n    middleHcs: '',\n    juniorHcs: '',\n    quarter1st: '',\n    quarter2nd: '',\n    quarter3rd: '',\n    quarter4th: '',\n    totalBillable: '',\n    totalNewSale: '',\n    monthlyHCList: []\n  },\n  // Other Info\n  otherInfo: {\n    contact: null,\n    presaleFolder: '',\n    customerContact: '',\n    phoneNumber: '',\n    emailAddress: ''\n  }\n};\nexport const addOrEditBiddingSchema = yup.object().shape({\n  project: yup.object().shape({\n    projectName: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n    projectRedmineId: yup.object().test('', VALIDATE_MESSAGES.REQUIRED, function () {\n      const status = this.resolve(yup.ref('status'));\n      return status !== E_BIDDING_STATUS.CONTRACT || this.resolve(yup.ref('projectRedmineId'));\n    }).shape({\n      value: yup.string(),\n      label: yup.string()\n    }).nullable(),\n    department: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n    type: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n    serviceType: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n    status: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n    contractDurationFrom: yup.string().required(VALIDATE_MESSAGES.REQUIRED).nullable(),\n    contractDurationTo: yup.string().required(VALIDATE_MESSAGES.REQUIRED).nullable()\n  }),\n  financialInfo: yup.object().shape({\n    currency: yup.string().required(VALIDATE_MESSAGES.REQUIRED)\n  }),\n  otherInfo: yup.object().shape({\n    phoneNumber: yup.string().transform(value => value === '' ? null : value).matches(REGEX_CONSTANTS.REGEX_PHONE_NUMBER, VALIDATE_MESSAGES.FORMAT_PHONE_NUMBER).max(10, VALIDATE_MESSAGES.MAX_PHONE_NUMBER).nullable()\n  })\n});\nexport const TMBiddingFormDefaultValue = {\n  unit: 'Man-month',\n  rateByMonth: []\n};\nexport const TMBiddingFormSchema = yup.object().shape({\n  unit: yup.string().nullable(),\n  rateByMonth: yup.array().of(yup.object().shape({\n    role: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n    rate: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n    quantity: yup.string().required(VALIDATE_MESSAGES.REQUIRED)\n  }))\n});\nexport const FixCostBiddingFormDefaultValue = {\n  contractAllocationByMonth: '',\n  billable: 0\n};\nexport const FixCostBiddingSchema = yup.object().shape({\n  contractAllocationByMonth: yup.string().required(VALIDATE_MESSAGES.REQUIRED)\n});\n\n// ============== Budgeting plan ==============\n\nexport const budgetingPlanSearchConfig = {\n  year: getCurrentYear(),\n  type: '',\n  pipelineType: '',\n  language: 'en'\n};\nexport const budgetingPlanSearchSchema = yup.object().shape({\n  type: yup.string().nullable(),\n  year: yup.string().nullable()\n});\nexport const editBudgetingPlanDefaultValue = {\n  idHexString: '',\n  projectInfo: {\n    year: '',\n    salePipelineIdHexString: '',\n    salePipelineType: '',\n    projectName: '',\n    type: '',\n    serviceType: '',\n    note: '',\n    riskFactor: 0,\n    numberOfMonths: 0,\n    contractedValue: 0,\n    effortLimitManHours: null,\n    costLimitVND: 0,\n    licenseFee: 0\n  },\n  //projectKPIScore\n  projectKPIScore: {\n    estimateUsedEffort: 0,\n    estimatedUseCost: 0,\n    planDelivery: 0,\n    totalOnTimeDelivery: 0,\n    effortKPIScore: 0,\n    costKPI: 0,\n    deadlineKPI: 0,\n    taskMGT: 0,\n    kpiScore: 0\n  },\n  // projectKPIBonus\n  projectKPIBonus: {\n    addedEbitda: '',\n    projectSetRevenue: 0,\n    actualCostByACD: 0,\n    companyRevActualCost: 0,\n    projectMGTPerformanceLevel: 0,\n    projectSavingCost: 0,\n    estimatedKPIProjectSavingCost: 0,\n    estimatedShareCompanyProfit: 0,\n    estimatedTotalKPIBonus: 0,\n    totalKPIBonus: 0,\n    kpiBonus: 0\n  }\n};\nexport const editBudgetingPlanSchema = yup.object().shape({\n  idHexString: yup.string().nullable(),\n  // project info\n  projectInfo: yup.object().shape({\n    riskFactor: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable().min(0, VALIDATE_MESSAGES.RISK_FACTOR_MIN).max(100, VALIDATE_MESSAGES.RISK_FACTOR_MAX),\n    note: yup.string().nullable().max(5000, VALIDATE_MESSAGES.MAX_LENGTH)\n  }),\n  // projectKPIScore\n  projectKPIScore: yup.object().shape({\n    planDelivery: yup.number().transform(value => Number.isNaN(value) ? null : value).integer().nullable().min(0, VALIDATE_MESSAGES.PLAN_DELIVERY_MIN),\n    totalOnTimeDelivery: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable().when('planDelivery', planDelivery => {\n      if (planDelivery) {\n        return yup.number().transform(value => Number.isNaN(value) ? null : value).nullable().min(0, VALIDATE_MESSAGES.TOTAL_ON_TIME_DELIVERY_MIN).max(planDelivery, VALIDATE_MESSAGES.TOTAL_ON_TIME_DELIVERY_MORE);\n      }\n    }).min(0, VALIDATE_MESSAGES.TOTAL_ON_TIME_DELIVERY_MIN).max(10, VALIDATE_MESSAGES.TOTAL_ON_TIME_DELIVERY_MAX),\n    taskMGT: yup.number().transform(value => Number.isNaN(value) ? null : value).nullable().min(1, VALIDATE_MESSAGES.TASK_MGT_MIN).max(10, VALIDATE_MESSAGES.TASK_MGT_MAX)\n  }),\n  //projectKPIBonus\n  projectKPIBonus: yup.object().shape({\n    estimatedTotalKPIBonus: yup.string().nullable()\n  })\n});\n\n// Project reference\n\nexport const projectReferenceSearchDefaultValue = {\n  ...paginationParamDefault,\n  projectId: null,\n  projectType: '',\n  technology: '',\n  domain: ''\n};\nexport const projectReferenceSearchSchema = yup.object().shape({\n  projectType: yup.string().nullable(),\n  technology: yup.string().nullable(),\n  domain: yup.string().nullable()\n});\n\n// ============== Monitor Bidding Backages =============\n\n// Sales Lead Form Search\n\nexport const monitorBiddingPackagesFilterConfig = {\n  ...paginationParamDefault,\n  type: '',\n  packageName: '',\n  address: '',\n  biddingPackagesName: '',\n  status: ''\n};\nexport const biddingPackageShemcha = yup.object().shape({\n  type: yup.string().nullable(),\n  packageName: yup.string().nullable(),\n  address: yup.string().nullable(),\n  biddingPackagesName: yup.string().nullable(),\n  status: yup.string().nullable()\n});\n\n// Add or Edit Requests Checking Form\nexport const addOrEditBiddingReportFormDefault = {\n  type: '',\n  numberKHLCNT: '',\n  biddingPackagesName: '',\n  estimatedCost: null,\n  datePosting: '',\n  timeBiddingClosing: '',\n  formBiddingParticipation: '',\n  numberTBMT: '',\n  group: '',\n  link: '',\n  address: '',\n  // TODO:\n  status: 'Chưa đóng thầu',\n  company: '',\n  keyword: '',\n  bidPrice: null,\n  comment: ''\n};\nexport const addOrEditBiddingReportSchema = yup.object().shape({\n  type: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n  numberKHLCNT: yup.string().nullable(),\n  biddingPackagesName: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n  estimatedCost: yup.number().nullable().transform(value => Number.isNaN(value) ? null : value),\n  datePosting: yup.string().nullable(),\n  timeBiddingClosing: yup.string().nullable(),\n  formBiddingParticipation: yup.string().nullable(),\n  numberTBMT: yup.string().nullable(),\n  group: yup.string().nullable(),\n  address: yup.string().nullable(),\n  status: yup.string().nullable(),\n  company: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n  keyword: yup.string().nullable()\n});\nexport const synchronizeFormDefault = {\n  token: ''\n};\nexport const synchronizeSchema = yup.object().shape({\n  token: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED)\n});", "map": {"version": 3, "names": ["yup", "moment", "REGEX_CONSTANTS", "VALIDATE_MESSAGES", "getCurrentYear", "CONTRACT_TYPE_SALE_REPORT", "DEPARTMENTS", "E_BIDDING_STATUS", "ROLE_TYPE", "UNIT_SALE_REPORT", "paginationParamDefault", "monthlyProductionPerformanceFilterConfig", "year", "month", "language", "monthlyProductionPerformanceFilterSchema", "object", "shape", "string", "array", "monthlyProductionPerformanceInfoDefault", "months", "departments", "companyTotals", "totalHCs", "productivity", "saleTotals", "productivityHeadCountEditFormSchema", "nullable", "value", "required", "REQUIRED", "matches", "REGEX_NUMBER", "INVALID_NUMBER", "productivityHeadCountEditFormDefault", "salesLeadFilterConfig", "supplierName", "partner<PERSON>ame", "status", "receivedDate", "picUserName", "fromDate", "toDate", "salesLeadFilterShemcha", "date", "label", "addOrEditRequestsCheckingFormDefault", "request", "timeline", "note", "domain", "possibility", "quantity", "technology", "addOrEditRequestsCheckingFormSchema", "REGEX_SPECIAL_CHARACTERS", "SPECIAL_CHARACTERS_PARTER_NAME", "REGEX_NO_NUMBER", "NO_NUMBER", "addOrEditSupplierCheckingFormDefault", "unitPrice", "workType", "addOrEditSupplierCheckingFormSchema", "test", "AFTER_DAY", "resolve", "ref", "isSameOrAfter", "SPECIAL_CHARACTERS_SUPPLIER_NAME", "hcInfoFormDefault", "role", "rate", "rateUSD", "amount", "hcInfoBuiddingFormDefault", "rateVND", "HcInfoDefault", "productionPerformanceAddOrEditFormDefault", "idHexString", "projectId", "departmentId", "projectName", "contractSize", "serviceType", "contractType", "TM", "originalContractSize", "unit", "MAN_MONTH", "contractAllocation", "duration", "paymentTerm", "currency", "standardWorkingDay", "exchangeRate", "delivered", "receivable", "received", "financial", "deliveredCurrency", "receivableCurrency", "received<PERSON><PERSON>rency", "financialCurrency", "hcInfo", "productionPerformanceAddOrEditFormSchema", "when", "is", "SCS", "PRD", "then", "otherwise", "SMD", "FIXED_COST", "number", "typeError", "positive", "POSITIVE_NUMBER", "transform", "_", "val", "Number", "min", "of", "mixed", "map", "option", "includes", "originalValue", "parsedValue", "parseFloat", "isNaN", "undefined", "onGoingConfig", "size", "type", "productionPerformanceIdHexString", "onGoingSchema", "editOnGoingDefaultValue", "projectInfo", "contractNo", "customer", "probability", "revenuePercent", "contractDueDate", "contractDurationFrom", "contractDurationTo", "warrantyTime", "financialInfo", "sizeVND", "sizeUSD", "managementRevenueAllocated", "accountRevenueAllocatedVNDValue", "newSaleUSD", "acctReceivables", "netEarn", "paid", "remain", "quarterLicense1st", "quarterLicense2nd", "quarterLicense3rd", "quarterLicense4th", "licenseFee", "billableHcs", "hcs", "teamLeadHcs", "seniorHcs", "middleHcs", "juniorHcs", "quarter1st", "quarter2nd", "quarter3rd", "quarter4th", "totalNewSale", "totalBillable", "monthlyHCList", "hcMonthly", "billableDay", "billable", "otherInfo", "contact", "presaleFolder", "customerContact", "phoneNumber", "emailAddress", "editOnGoingSchema", "LARGER_OR_EQUAL", "max", "LESS_OR_EQUAL", "REGEX_PHONE_NUMBER", "FORMAT_PHONE_NUMBER", "MAX_PHONE_NUMBER", "biddingFilterConfig", "searchModeOfProject", "searchModeOfCustomer", "biddingFilter<PERSON><PERSON>", "addOrEditBiddingConfig", "project", "projectRedmineId", "department", "BIDDING", "accountRevenueAllocatedVND", "addOrEditBiddingSchema", "CONTRACT", "TMBiddingFormDefaultValue", "rateByMonth", "TMBiddingFormSchema", "FixCostBiddingFormDefaultValue", "contractAllocationByMonth", "FixCostBiddingSchema", "budgetingPlanSearchConfig", "pipelineType", "budgetingPlanSearchSchema", "editBudgetingPlanDefaultValue", "salePipelineIdHexString", "salePipelineType", "riskFactor", "numberOfMonths", "contractedValue", "effortLimitManHours", "costLimitVND", "projectKPIScore", "estimateUsedEffort", "estimatedUseCost", "planDelivery", "totalOnTimeDelivery", "effortKPIScore", "costKPI", "deadlineKPI", "taskMGT", "kpiScore", "projectKPIBonus", "addedEbitda", "projectSetRevenue", "actualCostByACD", "companyRevActualCost", "projectMGTPerformanceLevel", "projectSavingCost", "estimatedKPIProjectSavingCost", "estimatedShareCompanyProfit", "estimatedTotalKPIBonus", "totalKPIBonus", "kpiBonus", "editBudgetingPlanSchema", "RISK_FACTOR_MIN", "RISK_FACTOR_MAX", "MAX_LENGTH", "integer", "PLAN_DELIVERY_MIN", "TOTAL_ON_TIME_DELIVERY_MIN", "TOTAL_ON_TIME_DELIVERY_MORE", "TOTAL_ON_TIME_DELIVERY_MAX", "TASK_MGT_MIN", "TASK_MGT_MAX", "projectReferenceSearchDefaultValue", "projectType", "projectReferenceSearchSchema", "monitorBiddingPackagesFilterConfig", "packageName", "address", "biddingPackagesName", "biddingPackageShemcha", "addOrEditBiddingReportFormDefault", "numberKHLCNT", "estimatedCost", "datePosting", "timeBiddingClosing", "formBiddingParticipation", "numberTBMT", "group", "link", "company", "keyword", "bidPrice", "comment", "addOrEditBiddingReportSchema", "synchronizeFormDefault", "token", "synchronizeSchema"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/pages/sales/Config.ts"], "sourcesContent": ["// yup\nimport * as yup from 'yup';\n\n// third party\nimport moment from 'moment';\n\n// project imports\nimport { REGEX_CONSTANTS } from 'constants/Validation';\nimport { VALIDATE_MESSAGES } from 'constants/Message';\nimport { getCurrentYear } from 'utils/date';\nimport {\n    CONTRACT_TYPE_SALE_REPORT,\n    DEPARTMENTS,\n    E_BIDDING_STATUS,\n    ROLE_TYPE,\n    UNIT_SALE_REPORT,\n    paginationParamDefault\n} from 'constants/Common';\nimport {\n    IBiddingReport,\n    IBudgetingPlanItem,\n    IMonthlyProductionPerformanceAddOrEditForm,\n    IMonthlyProductionPerformanceInfo,\n    IOption,\n    IPaginationParam,\n    IProductivityHcInfo,\n    IProductivityHeadCountEditForm,\n    IRequestsChecking,\n    ISaleOnGoingItem,\n    ISupplierChecking\n} from 'types';\n\n// ============== Monthly Production Performance ============== //\nexport interface IMonthlyProductionPerformanceFilterConfig {\n    year: number;\n    month?: number[];\n    language?: string;\n}\n\nexport const monthlyProductionPerformanceFilterConfig: IMonthlyProductionPerformanceFilterConfig = {\n    year: getCurrentYear(),\n    month: [],\n    language: 'en'\n};\n\nexport const monthlyProductionPerformanceFilterSchema = yup.object().shape({\n    year: yup.string(),\n    month: yup.array()\n});\n\nexport const monthlyProductionPerformanceInfoDefault: IMonthlyProductionPerformanceInfo = {\n    months: [],\n    departments: [],\n    companyTotals: {\n        totalHCs: [],\n        productivity: [],\n        saleTotals: []\n    }\n};\n\n// Edit HeadCount Form\nexport const productivityHeadCountEditFormSchema = yup.object().shape({\n    year: yup.string().nullable(),\n    month: yup.string().nullable(),\n    value: yup\n        .string()\n        .nullable()\n        .required(VALIDATE_MESSAGES.REQUIRED)\n        .matches(REGEX_CONSTANTS.REGEX_NUMBER, VALIDATE_MESSAGES.INVALID_NUMBER)\n});\n\nexport const productivityHeadCountEditFormDefault: IProductivityHeadCountEditForm = {\n    year: getCurrentYear(),\n    month: '1',\n    value: ''\n};\n\n// ============== Sales Lead ============== //\n// Sales Lead Form Search\nexport interface ISalesLeadFilterConfig extends IPaginationParam {\n    partnerName: string;\n    picUserName: IOption | null;\n    supplierName: string;\n    status: string;\n    receivedDate: Date | null | string;\n    fromDate: Date | null | string;\n    toDate: Date | null | string;\n}\n\nexport const salesLeadFilterConfig: ISalesLeadFilterConfig = {\n    ...paginationParamDefault,\n    supplierName: '',\n    partnerName: '',\n    status: '',\n    receivedDate: null,\n    picUserName: null,\n    fromDate: null,\n    toDate: null\n};\n\nexport const salesLeadFilterShemcha = yup.object().shape({\n    partnerName: yup.string().nullable(),\n    supplierName: yup.string().nullable(),\n    status: yup.string().nullable(),\n    receivedDate: yup.date().nullable(),\n    picUserName: yup\n        .object()\n        .shape({\n            value: yup.string(),\n            label: yup.string()\n        })\n        .nullable(),\n    fromDate: yup.date().nullable(),\n    toDate: yup.date().nullable()\n});\n\n// Add or Edit Requests Checking Form\nexport const addOrEditRequestsCheckingFormDefault: IRequestsChecking = {\n    partnerName: '',\n    request: '',\n    timeline: '',\n    status: '',\n    note: '',\n    picUserName: null,\n    domain: '',\n    possibility: '',\n    quantity: '',\n    receivedDate: null,\n    technology: ''\n};\n\nexport const addOrEditRequestsCheckingFormSchema = yup.object().shape({\n    partnerName: yup\n        .string()\n        .matches(REGEX_CONSTANTS.REGEX_SPECIAL_CHARACTERS, VALIDATE_MESSAGES.SPECIAL_CHARACTERS_PARTER_NAME)\n        .matches(REGEX_CONSTANTS.REGEX_NO_NUMBER, VALIDATE_MESSAGES.NO_NUMBER)\n        .required(VALIDATE_MESSAGES.REQUIRED),\n    request: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n    timeline: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n    status: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n    note: yup.string(),\n    picUserName: yup\n        .object()\n        .shape({\n            value: yup.string(),\n            label: yup.string()\n        })\n        .nullable()\n        .required(VALIDATE_MESSAGES.REQUIRED),\n    domain: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n    possibility: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n    quantity: yup\n        .string()\n        .matches(REGEX_CONSTANTS.REGEX_NUMBER, VALIDATE_MESSAGES.INVALID_NUMBER)\n        .nullable()\n        .required(VALIDATE_MESSAGES.REQUIRED),\n    receivedDate: yup.date().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n    technology: yup.string().required(VALIDATE_MESSAGES.REQUIRED)\n});\n\n// Add or Edit Supplier Checking Form\n\nexport const addOrEditSupplierCheckingFormDefault: ISupplierChecking = {\n    picUserName: null,\n    note: '',\n    fromDate: null,\n    toDate: null,\n    supplierName: '',\n    technology: '',\n    unitPrice: null,\n    workType: '',\n    quantity: ''\n};\n\nexport const addOrEditSupplierCheckingFormSchema = yup.object().shape({\n    note: yup.string(),\n    fromDate: yup.date().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n    toDate: yup\n        .date()\n        .nullable()\n        .required(VALIDATE_MESSAGES.REQUIRED)\n        .test('toDate-after-fromDate', VALIDATE_MESSAGES.AFTER_DAY, function (value) {\n            const fromDate = this.resolve(yup.ref('fromDate'));\n            return !value || !fromDate || moment(value).isSameOrAfter(fromDate, 'day');\n        }),\n    picUserName: yup\n        .object()\n        .shape({\n            value: yup.string(),\n            label: yup.string()\n        })\n        .nullable()\n        .required(VALIDATE_MESSAGES.REQUIRED),\n    supplierName: yup\n        .string()\n        .matches(REGEX_CONSTANTS.REGEX_SPECIAL_CHARACTERS, VALIDATE_MESSAGES.SPECIAL_CHARACTERS_SUPPLIER_NAME)\n        .matches(REGEX_CONSTANTS.REGEX_NO_NUMBER, VALIDATE_MESSAGES.NO_NUMBER)\n        .required(VALIDATE_MESSAGES.REQUIRED),\n    quantity: yup.string().nullable().matches(REGEX_CONSTANTS.REGEX_NUMBER, VALIDATE_MESSAGES.INVALID_NUMBER),\n    technology: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n    unitPrice: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n    workType: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED)\n});\n\nexport const hcInfoFormDefault: IProductivityHcInfo = {\n    role: '',\n    rate: '',\n    rateUSD: '',\n    quantity: '',\n    amount: ''\n};\n\nexport const hcInfoBuiddingFormDefault = {\n    role: '',\n    rate: '',\n    rateVND: '',\n    quantity: '',\n    amount: ''\n};\n\nexport const HcInfoDefault = {\n    role: '',\n    rate: 0,\n    rateUSD: 0,\n    quantity: 0,\n    amount: 0\n};\n\nexport const productionPerformanceAddOrEditFormDefault: IMonthlyProductionPerformanceAddOrEditForm = {\n    idHexString: '',\n    year: getCurrentYear(),\n    month: 0,\n    projectId: null,\n    departmentId: '',\n    projectName: '',\n    contractSize: '',\n    serviceType: '',\n    contractType: CONTRACT_TYPE_SALE_REPORT.TM,\n    originalContractSize: '',\n    unit: UNIT_SALE_REPORT.MAN_MONTH,\n    contractAllocation: '',\n    duration: {\n        fromDate: null,\n        toDate: null\n    },\n    paymentTerm: '1',\n    currency: '',\n    standardWorkingDay: '',\n    exchangeRate: 0,\n    delivered: 0,\n    receivable: 0,\n    received: 0,\n    financial: 0,\n    deliveredCurrency: 0,\n    receivableCurrency: 0,\n    receivedCurrency: 0,\n    financialCurrency: 0,\n    hcInfo: []\n};\n\n//schema\nexport const productionPerformanceAddOrEditFormSchema = yup.object().shape({\n    year: yup.string().nullable(),\n    idHexString: yup.string().nullable(),\n    month: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n    departmentId: yup.string().nullable(),\n    serviceType: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n\n    projectId: yup.object().when('departmentId', {\n        is: (departmentId: string) => departmentId === DEPARTMENTS.SCS || departmentId === DEPARTMENTS.PRD,\n        then: yup.object().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n        otherwise: yup.object().nullable()\n    }),\n\n    projectName: yup.string().when('departmentId', {\n        is: DEPARTMENTS.SMD,\n        then: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n        otherwise: yup.string().nullable()\n    }),\n    contractSize: yup.string().when(['departmentId', 'contractType'], {\n        is: (departmentId: string, contractType: string) =>\n            departmentId === DEPARTMENTS.SCS && contractType === CONTRACT_TYPE_SALE_REPORT.FIXED_COST,\n        then: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n        otherwise: yup.string().nullable()\n    }),\n    contractType: yup.string().when('departmentId', {\n        is: DEPARTMENTS.SCS,\n        then: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n        otherwise: yup.string().nullable()\n    }),\n    unit: yup.string().when('departmentId', {\n        is: DEPARTMENTS.SCS,\n        then: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n        otherwise: yup.string().nullable()\n    }),\n    originalContractSize: yup.string().when(['departmentId', 'contractType'], {\n        is: (departmentId: string, contractType: string) =>\n            departmentId === DEPARTMENTS.SCS && contractType === CONTRACT_TYPE_SALE_REPORT.FIXED_COST,\n        then: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n        otherwise: yup.string().nullable()\n    }),\n    contractAllocation: yup.string().when('departmentId', {\n        is: DEPARTMENTS.SCS,\n        then: yup.string().nullable(),\n        otherwise: yup.string().nullable()\n    }),\n    duration: yup.object().when('departmentId', {\n        is: DEPARTMENTS.SCS,\n        then: yup.object().shape({\n            fromDate: yup.date().required(VALIDATE_MESSAGES.REQUIRED),\n            toDate: yup\n                .date()\n                .required(VALIDATE_MESSAGES.REQUIRED)\n                .test('toDate-after-fromDate', VALIDATE_MESSAGES.AFTER_DAY, function (value) {\n                    const fromDate = this.resolve(yup.ref('fromDate'));\n                    return !value || !fromDate || moment(value).isSameOrAfter(fromDate, 'day');\n                })\n        }),\n        otherwise: yup.object().shape({\n            fromDate: yup.date().nullable(),\n            toDate: yup\n                .date()\n                .nullable()\n                .test('toDate-after-fromDate', VALIDATE_MESSAGES.AFTER_DAY, function (value) {\n                    const fromDate = this.resolve(yup.ref('fromDate'));\n                    return !value || !fromDate || moment(value).isSameOrAfter(fromDate, 'day');\n                })\n        })\n    }),\n    paymentTerm: yup.string().nullable(),\n    currency: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n    delivered: yup.number().when('departmentId', {\n        is: DEPARTMENTS.SCS,\n        then: yup\n            .number()\n            .nullable()\n            .typeError(VALIDATE_MESSAGES.INVALID_NUMBER)\n            .positive(VALIDATE_MESSAGES.POSITIVE_NUMBER)\n            .transform((_, val) => (val !== '' ? Number(val) : null))\n            .min(0, VALIDATE_MESSAGES.POSITIVE_NUMBER),\n        otherwise: yup.number().nullable()\n    }),\n\n    receivable: yup.number().when('departmentId', {\n        is: DEPARTMENTS.SCS,\n        then: yup\n            .number()\n            .nullable()\n            .typeError(VALIDATE_MESSAGES.INVALID_NUMBER)\n            .positive(VALIDATE_MESSAGES.POSITIVE_NUMBER)\n            .transform((_, val) => (val !== '' ? Number(val) : null))\n            .min(0, VALIDATE_MESSAGES.POSITIVE_NUMBER),\n        otherwise: yup.number().nullable()\n    }),\n    received: yup.number().when('departmentId', {\n        is: DEPARTMENTS.SCS,\n        then: yup\n            .number()\n            .nullable()\n            .typeError(VALIDATE_MESSAGES.INVALID_NUMBER)\n            .positive(VALIDATE_MESSAGES.POSITIVE_NUMBER)\n            .transform((_, val) => (val !== '' ? Number(val) : null))\n            .min(0, VALIDATE_MESSAGES.POSITIVE_NUMBER),\n        otherwise: yup.number().nullable()\n    }),\n    financial: yup.number().when('departmentId', {\n        is: DEPARTMENTS.SCS,\n        then: yup\n            .number()\n            .nullable()\n            .typeError(VALIDATE_MESSAGES.INVALID_NUMBER)\n            .positive(VALIDATE_MESSAGES.POSITIVE_NUMBER)\n            .transform((_, val) => (val !== '' ? Number(val) : null))\n            .min(0, VALIDATE_MESSAGES.POSITIVE_NUMBER),\n        otherwise: yup.number().nullable()\n    }),\n\n    hcInfo: yup.array().when(['departmentId', 'contractType'], {\n        is: (departmentId: string, contractType: string) =>\n            departmentId === DEPARTMENTS.SCS && contractType === CONTRACT_TYPE_SALE_REPORT.TM,\n        then: yup.array().of(\n            yup.object().shape({\n                role: yup\n                    .mixed()\n                    .test(\n                        'isRoleValid',\n                        VALIDATE_MESSAGES.REQUIRED,\n                        (value) => value === null || ROLE_TYPE.map((option) => option.value).includes(value)\n                    )\n                    .required(VALIDATE_MESSAGES.REQUIRED)\n                    .nullable(),\n                rate: yup\n                    .number()\n                    .nullable()\n                    .required(VALIDATE_MESSAGES.REQUIRED)\n                    .transform((value, originalValue) => {\n                        if (originalValue === '') return null;\n                        return value;\n                    }),\n                rateUSD: yup\n                    .number()\n                    .nullable()\n                    .required(VALIDATE_MESSAGES.REQUIRED)\n                    .transform((value, originalValue) => {\n                        if (originalValue === '') return null;\n                        return value;\n                    }),\n                quantity: yup\n                    .number()\n                    .nullable()\n                    .required(VALIDATE_MESSAGES.REQUIRED)\n                    .transform((value, originalValue) => {\n                        if (originalValue === '') return null;\n                        return value;\n                    }),\n                amount: yup\n                    .number()\n                    .nullable()\n                    .transform((value, originalValue) => {\n                        if (originalValue === '') return null;\n                        return value;\n                    })\n            })\n        )\n    }),\n\n    exchangeRate: yup.number().when('departmentId', {\n        is: DEPARTMENTS.SCS,\n        then: yup.number().nullable(),\n        otherwise: yup.number().nullable()\n    }),\n\n    standardWorkingDay: yup.number().when('departmentId', {\n        is: DEPARTMENTS.SCS,\n        then: yup\n            .number()\n            .nullable()\n            .transform((value, originalValue) => {\n                const parsedValue = parseFloat(originalValue);\n                return isNaN(parsedValue) ? undefined : parsedValue;\n            }),\n        otherwise: yup.number().nullable()\n    })\n});\n// ============== SALE PIPELINE ==============\n// ============== OnGoing ==============\nexport interface IOnGoingConfig extends IPaginationParam {\n    type: string;\n    year: number;\n    productionPerformanceIdHexString: IOption | null;\n    status: string;\n}\n\nexport const onGoingConfig: IOnGoingConfig = {\n    ...paginationParamDefault,\n    size: 100,\n    type: '',\n    year: getCurrentYear(),\n    productionPerformanceIdHexString: null,\n    status: '',\n    language: 'en'\n};\n\nexport const onGoingSchema = yup.object().shape({\n    type: yup.string().nullable(),\n    year: yup.string().nullable(),\n    productionPerformanceIdHexString: yup\n        .object()\n        .shape({\n            value: yup.string(),\n            label: yup.string()\n        })\n        .nullable(),\n    status: yup.string().nullable()\n});\n\nexport const editOnGoingDefaultValue: ISaleOnGoingItem = {\n    idHexString: '',\n    projectInfo: {\n        year: null,\n        contractNo: '',\n        customer: '',\n        probability: '',\n        productionPerformanceIdHexString: null,\n        projectName: '',\n        status: '',\n        revenuePercent: null,\n        type: '',\n        serviceType: '',\n        contractDueDate: null,\n        contractDurationFrom: null,\n        contractDurationTo: null,\n        contractType: '',\n        note: '',\n        warrantyTime: ''\n    },\n    // financial info\n    financialInfo: {\n        sizeVND: '',\n        sizeUSD: '',\n        managementRevenueAllocated: '',\n        accountRevenueAllocatedVNDValue: '',\n        newSaleUSD: '',\n        currency: '',\n        exchangeRate: '',\n        acctReceivables: '',\n        netEarn: '',\n        paid: '',\n        remain: '',\n        quarterLicense1st: '',\n        quarterLicense2nd: '',\n        quarterLicense3rd: '',\n        quarterLicense4th: '',\n        licenseFee: ''\n    },\n    // hc info\n    hcInfo: {\n        billableHcs: '',\n        hcs: '',\n        teamLeadHcs: '',\n        seniorHcs: '',\n        middleHcs: '',\n        juniorHcs: '',\n        quarter1st: '',\n        quarter2nd: '',\n        quarter3rd: '',\n        quarter4th: '',\n        totalNewSale: '',\n        totalBillable: '',\n        monthlyHCList: [\n            {\n                month: 0,\n                hcMonthly: 0,\n                billableDay: 0,\n                billable: 0\n            }\n        ]\n    },\n    // other info\n    otherInfo: {\n        contact: null,\n        presaleFolder: '',\n        customerContact: '',\n        phoneNumber: '',\n        emailAddress: ''\n    }\n};\n\nexport const editOnGoingSchema = yup.object().shape({\n    idHexString: yup.string().nullable(),\n    // project info\n    projectInfo: yup.object().shape({\n        year: yup.number().nullable(),\n        contractNo: yup.string().nullable(),\n        customer: yup.string().nullable(),\n        probability: yup\n            .number()\n            .typeError(VALIDATE_MESSAGES.INVALID_NUMBER)\n            .transform((_, val) => (val !== '' ? Number(val) : null))\n            .min(0, ({ min }) => `${VALIDATE_MESSAGES.LARGER_OR_EQUAL}${min}`)\n            .max(100, ({ max }) => `${VALIDATE_MESSAGES.LESS_OR_EQUAL} ${max}`)\n            .nullable(),\n        productionPerformanceIdHexString: yup\n            .object()\n            .shape({\n                value: yup.string().nullable(),\n                label: yup.string()\n            })\n            .nullable()\n            .required(VALIDATE_MESSAGES.REQUIRED),\n        projectName: yup.string().nullable(),\n        status: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n        type: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n        serviceType: yup.string().nullable(),\n        contractType: yup.string().nullable(),\n        contractDueDate: yup.date().nullable(),\n        contractDurationFrom: yup.date().nullable(),\n        contractDurationTo: yup.date().nullable(),\n        note: yup.string().nullable(),\n        warrantyTime: yup.string()\n    }),\n    // financial info\n    financialInfo: yup.object().shape({\n        sizeVND: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        sizeUSD: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        managementRevenueAllocated: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        accountRevenueAllocatedVNDValue: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        newSaleUSD: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        currency: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        exchangeRate: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        acctReceivables: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        netEarn: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        paid: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        remain: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        quarterLicense1st: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        quarterLicense2nd: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        quarterLicense3rd: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        quarterLicense4th: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        licenseFee: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable()\n    }),\n    // hc info\n    hcInfo: yup.object().shape({\n        billableHcs: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        hcs: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        seniorHcs: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        middleHcs: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        juniorHcs: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        teamLeadHcs: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        quarter1st: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        quarter2nd: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        quarter3rd: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        quarter4th: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        totalNewSale: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable(),\n        totalBillable: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable()\n    }),\n    // other info\n    otherInfo: yup.object().shape({\n        contact: yup\n            .object()\n            .shape({\n                value: yup.string().nullable(),\n                label: yup.string()\n            })\n            .nullable(),\n        presaleFolder: yup.string().nullable(),\n        customerContact: yup.string().nullable(),\n        phoneNumber: yup\n            .string()\n            .transform((value) => (value === '' ? null : value))\n            .matches(REGEX_CONSTANTS.REGEX_PHONE_NUMBER, VALIDATE_MESSAGES.FORMAT_PHONE_NUMBER)\n            .max(10, VALIDATE_MESSAGES.MAX_PHONE_NUMBER)\n            .nullable(),\n        emailAddress: yup.string().nullable()\n    })\n});\n\n// ============== Bidding ==============\n// Filter\nexport interface IBiddingFilterConfig extends IPaginationParam {\n    type: string;\n    year: number;\n    status: string;\n    projectName: string;\n    customer: string;\n    searchModeOfProject: string;\n    searchModeOfCustomer: string;\n}\n\nexport const biddingFilterConfig: IBiddingFilterConfig = {\n    ...paginationParamDefault,\n    size: 100,\n    type: '',\n    year: getCurrentYear(),\n    status: '',\n    language: 'en',\n    projectName: '',\n    customer: '',\n    searchModeOfProject: 'contains',\n    searchModeOfCustomer: 'contains'\n};\n\nexport const biddingFilterShemcha = yup.object().shape({\n    type: yup.string().nullable(),\n    year: yup.string(),\n    projectName: yup.string().nullable(),\n    status: yup.string().nullable(),\n    customer: yup.string().nullable(),\n    searchModeOfProject: yup.string().nullable(),\n    searchModeOfCustomer: yup.string().nullable()\n});\n\n// Add or Edit\nexport const addOrEditBiddingConfig = {\n    // Project Info\n    project: {\n        customer: '',\n        projectName: '',\n        projectRedmineId: null,\n        type: '', // sale pipeline type\n        serviceType: '',\n        contractType: 'TM',\n        note: '',\n        department: '',\n        contractNo: '',\n        probability: '',\n        status: E_BIDDING_STATUS.BIDDING,\n        contractDueDate: '',\n        contractDurationFrom: '',\n        contractDurationTo: '',\n        warrantyTime: '',\n        revenuePercent: ''\n    },\n    // Financial Info\n    financialInfo: {\n        originalContractSize: '',\n        currency: '',\n        exchangeRate: '',\n        sizeVND: '',\n        sizeUSD: '',\n        managementRevenueAllocated: '',\n        accountRevenueAllocatedVND: '',\n        newSaleUSD: '',\n        licenseFee: '',\n        acctReceivables: '',\n        netEarn: '',\n        paid: '',\n        remain: '',\n        quarterLicense1st: '',\n        quarterLicense2nd: '',\n        quarterLicense3rd: '',\n        quarterLicense4th: ''\n    },\n    // Hc Info\n    hcInfo: {\n        billableHcs: '',\n        hcs: '',\n        teamLeadHcs: '',\n        seniorHcs: '',\n        middleHcs: '',\n        juniorHcs: '',\n        quarter1st: '',\n        quarter2nd: '',\n        quarter3rd: '',\n        quarter4th: '',\n        totalBillable: '',\n        totalNewSale: '',\n        monthlyHCList: []\n    },\n    // Other Info\n    otherInfo: {\n        contact: null,\n        presaleFolder: '',\n        customerContact: '',\n        phoneNumber: '',\n        emailAddress: ''\n    }\n};\n\nexport const addOrEditBiddingSchema = yup.object().shape({\n    project: yup.object().shape({\n        projectName: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n        projectRedmineId: yup\n            .object()\n            .test('', VALIDATE_MESSAGES.REQUIRED, function (this: any) {\n                const status = this.resolve(yup.ref('status'));\n                return status !== E_BIDDING_STATUS.CONTRACT || this.resolve(yup.ref('projectRedmineId'));\n            })\n            .shape({\n                value: yup.string(),\n                label: yup.string()\n            })\n            .nullable(),\n        department: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n        type: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n        serviceType: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n        status: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n        contractDurationFrom: yup.string().required(VALIDATE_MESSAGES.REQUIRED).nullable(),\n        contractDurationTo: yup.string().required(VALIDATE_MESSAGES.REQUIRED).nullable()\n    }),\n    financialInfo: yup.object().shape({\n        currency: yup.string().required(VALIDATE_MESSAGES.REQUIRED)\n    }),\n    otherInfo: yup.object().shape({\n        phoneNumber: yup\n            .string()\n            .transform((value) => (value === '' ? null : value))\n            .matches(REGEX_CONSTANTS.REGEX_PHONE_NUMBER, VALIDATE_MESSAGES.FORMAT_PHONE_NUMBER)\n            .max(10, VALIDATE_MESSAGES.MAX_PHONE_NUMBER)\n            .nullable()\n    })\n});\n\nexport const TMBiddingFormDefaultValue = {\n    unit: 'Man-month',\n    rateByMonth: []\n};\n\nexport const TMBiddingFormSchema = yup.object().shape({\n    unit: yup.string().nullable(),\n    rateByMonth: yup.array().of(\n        yup.object().shape({\n            role: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n            rate: yup.string().required(VALIDATE_MESSAGES.REQUIRED),\n            quantity: yup.string().required(VALIDATE_MESSAGES.REQUIRED)\n        })\n    )\n});\n\nexport const FixCostBiddingFormDefaultValue = {\n    contractAllocationByMonth: '',\n    billable: 0\n};\n\nexport const FixCostBiddingSchema = yup.object().shape({\n    contractAllocationByMonth: yup.string().required(VALIDATE_MESSAGES.REQUIRED)\n});\n\n// ============== Budgeting plan ==============\n\nexport interface IBudgetingPlanSearch {\n    year: number;\n    type: string;\n    pipelineType: string;\n    language: string;\n}\n\nexport const budgetingPlanSearchConfig: IBudgetingPlanSearch = {\n    year: getCurrentYear(),\n    type: '',\n    pipelineType: '',\n    language: 'en'\n};\n\nexport const budgetingPlanSearchSchema = yup.object().shape({\n    type: yup.string().nullable(),\n    year: yup.string().nullable()\n});\n\nexport const editBudgetingPlanDefaultValue: IBudgetingPlanItem = {\n    idHexString: '',\n    projectInfo: {\n        year: '',\n        salePipelineIdHexString: '',\n        salePipelineType: '',\n        projectName: '',\n        type: '',\n        serviceType: '',\n        note: '',\n        riskFactor: 0,\n        numberOfMonths: 0,\n        contractedValue: 0,\n        effortLimitManHours: null,\n        costLimitVND: 0,\n        licenseFee: 0\n    },\n    //projectKPIScore\n    projectKPIScore: {\n        estimateUsedEffort: 0,\n        estimatedUseCost: 0,\n        planDelivery: 0,\n        totalOnTimeDelivery: 0,\n        effortKPIScore: 0,\n        costKPI: 0,\n        deadlineKPI: 0,\n        taskMGT: 0,\n        kpiScore: 0\n    },\n    // projectKPIBonus\n    projectKPIBonus: {\n        addedEbitda: '',\n        projectSetRevenue: 0,\n        actualCostByACD: 0,\n        companyRevActualCost: 0,\n        projectMGTPerformanceLevel: 0,\n        projectSavingCost: 0,\n        estimatedKPIProjectSavingCost: 0,\n        estimatedShareCompanyProfit: 0,\n        estimatedTotalKPIBonus: 0,\n        totalKPIBonus: 0,\n        kpiBonus: 0\n    }\n};\n\nexport const editBudgetingPlanSchema = yup.object().shape({\n    idHexString: yup.string().nullable(),\n    // project info\n    projectInfo: yup.object().shape({\n        riskFactor: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable()\n            .min(0, VALIDATE_MESSAGES.RISK_FACTOR_MIN)\n            .max(100, VALIDATE_MESSAGES.RISK_FACTOR_MAX),\n        note: yup.string().nullable().max(5000, VALIDATE_MESSAGES.MAX_LENGTH)\n    }),\n    // projectKPIScore\n    projectKPIScore: yup.object().shape({\n        planDelivery: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .integer()\n            .nullable()\n            .min(0, VALIDATE_MESSAGES.PLAN_DELIVERY_MIN),\n        totalOnTimeDelivery: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable()\n            .when('planDelivery', (planDelivery): any => {\n                if (planDelivery) {\n                    return yup\n                        .number()\n                        .transform((value) => (Number.isNaN(value) ? null : value))\n                        .nullable()\n                        .min(0, VALIDATE_MESSAGES.TOTAL_ON_TIME_DELIVERY_MIN)\n                        .max(planDelivery, VALIDATE_MESSAGES.TOTAL_ON_TIME_DELIVERY_MORE);\n                }\n            })\n            .min(0, VALIDATE_MESSAGES.TOTAL_ON_TIME_DELIVERY_MIN)\n            .max(10, VALIDATE_MESSAGES.TOTAL_ON_TIME_DELIVERY_MAX),\n        taskMGT: yup\n            .number()\n            .transform((value) => (Number.isNaN(value) ? null : value))\n            .nullable()\n            .min(1, VALIDATE_MESSAGES.TASK_MGT_MIN)\n            .max(10, VALIDATE_MESSAGES.TASK_MGT_MAX)\n    }),\n    //projectKPIBonus\n    projectKPIBonus: yup.object().shape({\n        estimatedTotalKPIBonus: yup.string().nullable()\n    })\n});\n\n// Project reference\nexport interface IProjectReferenceSearchDefaultValue extends IPaginationParam {\n    projectType: string;\n    projectId: IOption | null;\n    technology: string;\n    domain: string;\n}\n\nexport const projectReferenceSearchDefaultValue: IProjectReferenceSearchDefaultValue = {\n    ...paginationParamDefault,\n    projectId: null,\n    projectType: '',\n    technology: '',\n    domain: ''\n};\n\nexport const projectReferenceSearchSchema = yup.object().shape({\n    projectType: yup.string().nullable(),\n    technology: yup.string().nullable(),\n    domain: yup.string().nullable()\n});\n\n// ============== Monitor Bidding Backages =============\n\n// Sales Lead Form Search\nexport interface IMonitorBiddingPackagesFilterConfig extends IPaginationParam {\n    type: string;\n    packageName: string;\n    address: string;\n    biddingPackagesName: string;\n    status: string;\n}\n\nexport const monitorBiddingPackagesFilterConfig: IMonitorBiddingPackagesFilterConfig = {\n    ...paginationParamDefault,\n    type: '',\n    packageName: '',\n    address: '',\n    biddingPackagesName: '',\n    status: ''\n};\n\nexport const biddingPackageShemcha = yup.object().shape({\n    type: yup.string().nullable(),\n    packageName: yup.string().nullable(),\n    address: yup.string().nullable(),\n    biddingPackagesName: yup.string().nullable(),\n    status: yup.string().nullable()\n});\n\n// Add or Edit Requests Checking Form\nexport const addOrEditBiddingReportFormDefault: IBiddingReport = {\n    type: '',\n    numberKHLCNT: '',\n    biddingPackagesName: '',\n    estimatedCost: null,\n    datePosting: '',\n    timeBiddingClosing: '',\n    formBiddingParticipation: '',\n    numberTBMT: '',\n    group: '',\n    link: '',\n    address: '',\n    // TODO:\n    status: 'Chưa đóng thầu',\n    company: '',\n    keyword: '',\n    bidPrice: null,\n    comment: ''\n};\n\nexport const addOrEditBiddingReportSchema = yup.object().shape({\n    type: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n    numberKHLCNT: yup.string().nullable(),\n    biddingPackagesName: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n    estimatedCost: yup\n        .number()\n        .nullable()\n        .transform((value) => (Number.isNaN(value) ? null : value)),\n    datePosting: yup.string().nullable(),\n    timeBiddingClosing: yup.string().nullable(),\n    formBiddingParticipation: yup.string().nullable(),\n    numberTBMT: yup.string().nullable(),\n    group: yup.string().nullable(),\n    address: yup.string().nullable(),\n    status: yup.string().nullable(),\n    company: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),\n    keyword: yup.string().nullable()\n});\n\nexport const synchronizeFormDefault = {\n    token: ''\n};\n\nexport const synchronizeSchema = yup.object().shape({\n    token: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED)\n});\n"], "mappings": "AAAA;AACA,OAAO,KAAKA,GAAG,MAAM,KAAK;;AAE1B;AACA,OAAOC,MAAM,MAAM,QAAQ;;AAE3B;AACA,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,iBAAiB,QAAQ,mBAAmB;AACrD,SAASC,cAAc,QAAQ,YAAY;AAC3C,SACIC,yBAAyB,EACzBC,WAAW,EACXC,gBAAgB,EAChBC,SAAS,EACTC,gBAAgB,EAChBC,sBAAsB,QACnB,kBAAkB;;AAezB;;AAOA,OAAO,MAAMC,wCAAmF,GAAG;EAC/FC,IAAI,EAAER,cAAc,CAAC,CAAC;EACtBS,KAAK,EAAE,EAAE;EACTC,QAAQ,EAAE;AACd,CAAC;AAED,OAAO,MAAMC,wCAAwC,GAAGf,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EACvEL,IAAI,EAAEZ,GAAG,CAACkB,MAAM,CAAC,CAAC;EAClBL,KAAK,EAAEb,GAAG,CAACmB,KAAK,CAAC;AACrB,CAAC,CAAC;AAEF,OAAO,MAAMC,uCAA0E,GAAG;EACtFC,MAAM,EAAE,EAAE;EACVC,WAAW,EAAE,EAAE;EACfC,aAAa,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,YAAY,EAAE,EAAE;IAChBC,UAAU,EAAE;EAChB;AACJ,CAAC;;AAED;AACA,OAAO,MAAMC,mCAAmC,GAAG3B,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EAClEL,IAAI,EAAEZ,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EAC7Bf,KAAK,EAAEb,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EAC9BC,KAAK,EAAE7B,GAAG,CACLkB,MAAM,CAAC,CAAC,CACRU,QAAQ,CAAC,CAAC,CACVE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC,CACpCC,OAAO,CAAC9B,eAAe,CAAC+B,YAAY,EAAE9B,iBAAiB,CAAC+B,cAAc;AAC/E,CAAC,CAAC;AAEF,OAAO,MAAMC,oCAAoE,GAAG;EAChFvB,IAAI,EAAER,cAAc,CAAC,CAAC;EACtBS,KAAK,EAAE,GAAG;EACVgB,KAAK,EAAE;AACX,CAAC;;AAED;AACA;;AAWA,OAAO,MAAMO,qBAA6C,GAAG;EACzD,GAAG1B,sBAAsB;EACzB2B,YAAY,EAAE,EAAE;EAChBC,WAAW,EAAE,EAAE;EACfC,MAAM,EAAE,EAAE;EACVC,YAAY,EAAE,IAAI;EAClBC,WAAW,EAAE,IAAI;EACjBC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE;AACZ,CAAC;AAED,OAAO,MAAMC,sBAAsB,GAAG5C,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EACrDqB,WAAW,EAAEtC,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EACpCS,YAAY,EAAErC,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EACrCW,MAAM,EAAEvC,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EAC/BY,YAAY,EAAExC,GAAG,CAAC6C,IAAI,CAAC,CAAC,CAACjB,QAAQ,CAAC,CAAC;EACnCa,WAAW,EAAEzC,GAAG,CACXgB,MAAM,CAAC,CAAC,CACRC,KAAK,CAAC;IACHY,KAAK,EAAE7B,GAAG,CAACkB,MAAM,CAAC,CAAC;IACnB4B,KAAK,EAAE9C,GAAG,CAACkB,MAAM,CAAC;EACtB,CAAC,CAAC,CACDU,QAAQ,CAAC,CAAC;EACfc,QAAQ,EAAE1C,GAAG,CAAC6C,IAAI,CAAC,CAAC,CAACjB,QAAQ,CAAC,CAAC;EAC/Be,MAAM,EAAE3C,GAAG,CAAC6C,IAAI,CAAC,CAAC,CAACjB,QAAQ,CAAC;AAChC,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMmB,oCAAuD,GAAG;EACnET,WAAW,EAAE,EAAE;EACfU,OAAO,EAAE,EAAE;EACXC,QAAQ,EAAE,EAAE;EACZV,MAAM,EAAE,EAAE;EACVW,IAAI,EAAE,EAAE;EACRT,WAAW,EAAE,IAAI;EACjBU,MAAM,EAAE,EAAE;EACVC,WAAW,EAAE,EAAE;EACfC,QAAQ,EAAE,EAAE;EACZb,YAAY,EAAE,IAAI;EAClBc,UAAU,EAAE;AAChB,CAAC;AAED,OAAO,MAAMC,mCAAmC,GAAGvD,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EAClEqB,WAAW,EAAEtC,GAAG,CACXkB,MAAM,CAAC,CAAC,CACRc,OAAO,CAAC9B,eAAe,CAACsD,wBAAwB,EAAErD,iBAAiB,CAACsD,8BAA8B,CAAC,CACnGzB,OAAO,CAAC9B,eAAe,CAACwD,eAAe,EAAEvD,iBAAiB,CAACwD,SAAS,CAAC,CACrE7B,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EACzCiB,OAAO,EAAEhD,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EAC1DkB,QAAQ,EAAEjD,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EAC3DQ,MAAM,EAAEvC,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EACzDmB,IAAI,EAAElD,GAAG,CAACkB,MAAM,CAAC,CAAC;EAClBuB,WAAW,EAAEzC,GAAG,CACXgB,MAAM,CAAC,CAAC,CACRC,KAAK,CAAC;IACHY,KAAK,EAAE7B,GAAG,CAACkB,MAAM,CAAC,CAAC;IACnB4B,KAAK,EAAE9C,GAAG,CAACkB,MAAM,CAAC;EACtB,CAAC,CAAC,CACDU,QAAQ,CAAC,CAAC,CACVE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EACzCoB,MAAM,EAAEnD,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EACzDqB,WAAW,EAAEpD,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EAC9DsB,QAAQ,EAAErD,GAAG,CACRkB,MAAM,CAAC,CAAC,CACRc,OAAO,CAAC9B,eAAe,CAAC+B,YAAY,EAAE9B,iBAAiB,CAAC+B,cAAc,CAAC,CACvEN,QAAQ,CAAC,CAAC,CACVE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EACzCS,YAAY,EAAExC,GAAG,CAAC6C,IAAI,CAAC,CAAC,CAACjB,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EACxEuB,UAAU,EAAEtD,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ;AAChE,CAAC,CAAC;;AAEF;;AAEA,OAAO,MAAM6B,oCAAuD,GAAG;EACnEnB,WAAW,EAAE,IAAI;EACjBS,IAAI,EAAE,EAAE;EACRR,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,IAAI;EACZN,YAAY,EAAE,EAAE;EAChBiB,UAAU,EAAE,EAAE;EACdO,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,EAAE;EACZT,QAAQ,EAAE;AACd,CAAC;AAED,OAAO,MAAMU,mCAAmC,GAAG/D,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EAClEiC,IAAI,EAAElD,GAAG,CAACkB,MAAM,CAAC,CAAC;EAClBwB,QAAQ,EAAE1C,GAAG,CAAC6C,IAAI,CAAC,CAAC,CAACjB,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EACpEY,MAAM,EAAE3C,GAAG,CACN6C,IAAI,CAAC,CAAC,CACNjB,QAAQ,CAAC,CAAC,CACVE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC,CACpCiC,IAAI,CAAC,uBAAuB,EAAE7D,iBAAiB,CAAC8D,SAAS,EAAE,UAAUpC,KAAK,EAAE;IACzE,MAAMa,QAAQ,GAAG,IAAI,CAACwB,OAAO,CAAClE,GAAG,CAACmE,GAAG,CAAC,UAAU,CAAC,CAAC;IAClD,OAAO,CAACtC,KAAK,IAAI,CAACa,QAAQ,IAAIzC,MAAM,CAAC4B,KAAK,CAAC,CAACuC,aAAa,CAAC1B,QAAQ,EAAE,KAAK,CAAC;EAC9E,CAAC,CAAC;EACND,WAAW,EAAEzC,GAAG,CACXgB,MAAM,CAAC,CAAC,CACRC,KAAK,CAAC;IACHY,KAAK,EAAE7B,GAAG,CAACkB,MAAM,CAAC,CAAC;IACnB4B,KAAK,EAAE9C,GAAG,CAACkB,MAAM,CAAC;EACtB,CAAC,CAAC,CACDU,QAAQ,CAAC,CAAC,CACVE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EACzCM,YAAY,EAAErC,GAAG,CACZkB,MAAM,CAAC,CAAC,CACRc,OAAO,CAAC9B,eAAe,CAACsD,wBAAwB,EAAErD,iBAAiB,CAACkE,gCAAgC,CAAC,CACrGrC,OAAO,CAAC9B,eAAe,CAACwD,eAAe,EAAEvD,iBAAiB,CAACwD,SAAS,CAAC,CACrE7B,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EACzCsB,QAAQ,EAAErD,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,CAACI,OAAO,CAAC9B,eAAe,CAAC+B,YAAY,EAAE9B,iBAAiB,CAAC+B,cAAc,CAAC;EACzGoB,UAAU,EAAEtD,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EACxE8B,SAAS,EAAE7D,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EACvE+B,QAAQ,EAAE9D,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ;AACzE,CAAC,CAAC;AAEF,OAAO,MAAMuC,iBAAsC,GAAG;EAClDC,IAAI,EAAE,EAAE;EACRC,IAAI,EAAE,EAAE;EACRC,OAAO,EAAE,EAAE;EACXpB,QAAQ,EAAE,EAAE;EACZqB,MAAM,EAAE;AACZ,CAAC;AAED,OAAO,MAAMC,yBAAyB,GAAG;EACrCJ,IAAI,EAAE,EAAE;EACRC,IAAI,EAAE,EAAE;EACRI,OAAO,EAAE,EAAE;EACXvB,QAAQ,EAAE,EAAE;EACZqB,MAAM,EAAE;AACZ,CAAC;AAED,OAAO,MAAMG,aAAa,GAAG;EACzBN,IAAI,EAAE,EAAE;EACRC,IAAI,EAAE,CAAC;EACPC,OAAO,EAAE,CAAC;EACVpB,QAAQ,EAAE,CAAC;EACXqB,MAAM,EAAE;AACZ,CAAC;AAED,OAAO,MAAMI,yCAAqF,GAAG;EACjGC,WAAW,EAAE,EAAE;EACfnE,IAAI,EAAER,cAAc,CAAC,CAAC;EACtBS,KAAK,EAAE,CAAC;EACRmE,SAAS,EAAE,IAAI;EACfC,YAAY,EAAE,EAAE;EAChBC,WAAW,EAAE,EAAE;EACfC,YAAY,EAAE,EAAE;EAChBC,WAAW,EAAE,EAAE;EACfC,YAAY,EAAEhF,yBAAyB,CAACiF,EAAE;EAC1CC,oBAAoB,EAAE,EAAE;EACxBC,IAAI,EAAE/E,gBAAgB,CAACgF,SAAS;EAChCC,kBAAkB,EAAE,EAAE;EACtBC,QAAQ,EAAE;IACNjD,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACZ,CAAC;EACDiD,WAAW,EAAE,GAAG;EAChBC,QAAQ,EAAE,EAAE;EACZC,kBAAkB,EAAE,EAAE;EACtBC,YAAY,EAAE,CAAC;EACfC,SAAS,EAAE,CAAC;EACZC,UAAU,EAAE,CAAC;EACbC,QAAQ,EAAE,CAAC;EACXC,SAAS,EAAE,CAAC;EACZC,iBAAiB,EAAE,CAAC;EACpBC,kBAAkB,EAAE,CAAC;EACrBC,gBAAgB,EAAE,CAAC;EACnBC,iBAAiB,EAAE,CAAC;EACpBC,MAAM,EAAE;AACZ,CAAC;;AAED;AACA,OAAO,MAAMC,wCAAwC,GAAGzG,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EACvEL,IAAI,EAAEZ,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EAC7BmD,WAAW,EAAE/E,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EACpCf,KAAK,EAAEb,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EACnEkD,YAAY,EAAEjF,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EACrCwD,WAAW,EAAEpF,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EAEzEiD,SAAS,EAAEhF,GAAG,CAACgB,MAAM,CAAC,CAAC,CAAC0F,IAAI,CAAC,cAAc,EAAE;IACzCC,EAAE,EAAG1B,YAAoB,IAAKA,YAAY,KAAK3E,WAAW,CAACsG,GAAG,IAAI3B,YAAY,KAAK3E,WAAW,CAACuG,GAAG;IAClGC,IAAI,EAAE9G,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;IAClEgF,SAAS,EAAE/G,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC;EACrC,CAAC,CAAC;EAEFsD,WAAW,EAAElF,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACwF,IAAI,CAAC,cAAc,EAAE;IAC3CC,EAAE,EAAErG,WAAW,CAAC0G,GAAG;IACnBF,IAAI,EAAE9G,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;IAClEgF,SAAS,EAAE/G,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC;EACrC,CAAC,CAAC;EACFuD,YAAY,EAAEnF,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACwF,IAAI,CAAC,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE;IAC9DC,EAAE,EAAEA,CAAC1B,YAAoB,EAAEI,YAAoB,KAC3CJ,YAAY,KAAK3E,WAAW,CAACsG,GAAG,IAAIvB,YAAY,KAAKhF,yBAAyB,CAAC4G,UAAU;IAC7FH,IAAI,EAAE9G,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;IAClEgF,SAAS,EAAE/G,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC;EACrC,CAAC,CAAC;EACFyD,YAAY,EAAErF,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACwF,IAAI,CAAC,cAAc,EAAE;IAC5CC,EAAE,EAAErG,WAAW,CAACsG,GAAG;IACnBE,IAAI,EAAE9G,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;IAClEgF,SAAS,EAAE/G,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC;EACrC,CAAC,CAAC;EACF4D,IAAI,EAAExF,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACwF,IAAI,CAAC,cAAc,EAAE;IACpCC,EAAE,EAAErG,WAAW,CAACsG,GAAG;IACnBE,IAAI,EAAE9G,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;IAClEgF,SAAS,EAAE/G,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC;EACrC,CAAC,CAAC;EACF2D,oBAAoB,EAAEvF,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACwF,IAAI,CAAC,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE;IACtEC,EAAE,EAAEA,CAAC1B,YAAoB,EAAEI,YAAoB,KAC3CJ,YAAY,KAAK3E,WAAW,CAACsG,GAAG,IAAIvB,YAAY,KAAKhF,yBAAyB,CAAC4G,UAAU;IAC7FH,IAAI,EAAE9G,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;IAClEgF,SAAS,EAAE/G,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC;EACrC,CAAC,CAAC;EACF8D,kBAAkB,EAAE1F,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACwF,IAAI,CAAC,cAAc,EAAE;IAClDC,EAAE,EAAErG,WAAW,CAACsG,GAAG;IACnBE,IAAI,EAAE9G,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;IAC7BmF,SAAS,EAAE/G,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC;EACrC,CAAC,CAAC;EACF+D,QAAQ,EAAE3F,GAAG,CAACgB,MAAM,CAAC,CAAC,CAAC0F,IAAI,CAAC,cAAc,EAAE;IACxCC,EAAE,EAAErG,WAAW,CAACsG,GAAG;IACnBE,IAAI,EAAE9G,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;MACrByB,QAAQ,EAAE1C,GAAG,CAAC6C,IAAI,CAAC,CAAC,CAACf,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;MACzDY,MAAM,EAAE3C,GAAG,CACN6C,IAAI,CAAC,CAAC,CACNf,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC,CACpCiC,IAAI,CAAC,uBAAuB,EAAE7D,iBAAiB,CAAC8D,SAAS,EAAE,UAAUpC,KAAK,EAAE;QACzE,MAAMa,QAAQ,GAAG,IAAI,CAACwB,OAAO,CAAClE,GAAG,CAACmE,GAAG,CAAC,UAAU,CAAC,CAAC;QAClD,OAAO,CAACtC,KAAK,IAAI,CAACa,QAAQ,IAAIzC,MAAM,CAAC4B,KAAK,CAAC,CAACuC,aAAa,CAAC1B,QAAQ,EAAE,KAAK,CAAC;MAC9E,CAAC;IACT,CAAC,CAAC;IACFqE,SAAS,EAAE/G,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;MAC1ByB,QAAQ,EAAE1C,GAAG,CAAC6C,IAAI,CAAC,CAAC,CAACjB,QAAQ,CAAC,CAAC;MAC/Be,MAAM,EAAE3C,GAAG,CACN6C,IAAI,CAAC,CAAC,CACNjB,QAAQ,CAAC,CAAC,CACVoC,IAAI,CAAC,uBAAuB,EAAE7D,iBAAiB,CAAC8D,SAAS,EAAE,UAAUpC,KAAK,EAAE;QACzE,MAAMa,QAAQ,GAAG,IAAI,CAACwB,OAAO,CAAClE,GAAG,CAACmE,GAAG,CAAC,UAAU,CAAC,CAAC;QAClD,OAAO,CAACtC,KAAK,IAAI,CAACa,QAAQ,IAAIzC,MAAM,CAAC4B,KAAK,CAAC,CAACuC,aAAa,CAAC1B,QAAQ,EAAE,KAAK,CAAC;MAC9E,CAAC;IACT,CAAC;EACL,CAAC,CAAC;EACFkD,WAAW,EAAE5F,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EACpCiE,QAAQ,EAAE7F,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EACtEiE,SAAS,EAAEhG,GAAG,CAACkH,MAAM,CAAC,CAAC,CAACR,IAAI,CAAC,cAAc,EAAE;IACzCC,EAAE,EAAErG,WAAW,CAACsG,GAAG;IACnBE,IAAI,EAAE9G,GAAG,CACJkH,MAAM,CAAC,CAAC,CACRtF,QAAQ,CAAC,CAAC,CACVuF,SAAS,CAAChH,iBAAiB,CAAC+B,cAAc,CAAC,CAC3CkF,QAAQ,CAACjH,iBAAiB,CAACkH,eAAe,CAAC,CAC3CC,SAAS,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAMA,GAAG,KAAK,EAAE,GAAGC,MAAM,CAACD,GAAG,CAAC,GAAG,IAAK,CAAC,CACxDE,GAAG,CAAC,CAAC,EAAEvH,iBAAiB,CAACkH,eAAe,CAAC;IAC9CN,SAAS,EAAE/G,GAAG,CAACkH,MAAM,CAAC,CAAC,CAACtF,QAAQ,CAAC;EACrC,CAAC,CAAC;EAEFqE,UAAU,EAAEjG,GAAG,CAACkH,MAAM,CAAC,CAAC,CAACR,IAAI,CAAC,cAAc,EAAE;IAC1CC,EAAE,EAAErG,WAAW,CAACsG,GAAG;IACnBE,IAAI,EAAE9G,GAAG,CACJkH,MAAM,CAAC,CAAC,CACRtF,QAAQ,CAAC,CAAC,CACVuF,SAAS,CAAChH,iBAAiB,CAAC+B,cAAc,CAAC,CAC3CkF,QAAQ,CAACjH,iBAAiB,CAACkH,eAAe,CAAC,CAC3CC,SAAS,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAMA,GAAG,KAAK,EAAE,GAAGC,MAAM,CAACD,GAAG,CAAC,GAAG,IAAK,CAAC,CACxDE,GAAG,CAAC,CAAC,EAAEvH,iBAAiB,CAACkH,eAAe,CAAC;IAC9CN,SAAS,EAAE/G,GAAG,CAACkH,MAAM,CAAC,CAAC,CAACtF,QAAQ,CAAC;EACrC,CAAC,CAAC;EACFsE,QAAQ,EAAElG,GAAG,CAACkH,MAAM,CAAC,CAAC,CAACR,IAAI,CAAC,cAAc,EAAE;IACxCC,EAAE,EAAErG,WAAW,CAACsG,GAAG;IACnBE,IAAI,EAAE9G,GAAG,CACJkH,MAAM,CAAC,CAAC,CACRtF,QAAQ,CAAC,CAAC,CACVuF,SAAS,CAAChH,iBAAiB,CAAC+B,cAAc,CAAC,CAC3CkF,QAAQ,CAACjH,iBAAiB,CAACkH,eAAe,CAAC,CAC3CC,SAAS,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAMA,GAAG,KAAK,EAAE,GAAGC,MAAM,CAACD,GAAG,CAAC,GAAG,IAAK,CAAC,CACxDE,GAAG,CAAC,CAAC,EAAEvH,iBAAiB,CAACkH,eAAe,CAAC;IAC9CN,SAAS,EAAE/G,GAAG,CAACkH,MAAM,CAAC,CAAC,CAACtF,QAAQ,CAAC;EACrC,CAAC,CAAC;EACFuE,SAAS,EAAEnG,GAAG,CAACkH,MAAM,CAAC,CAAC,CAACR,IAAI,CAAC,cAAc,EAAE;IACzCC,EAAE,EAAErG,WAAW,CAACsG,GAAG;IACnBE,IAAI,EAAE9G,GAAG,CACJkH,MAAM,CAAC,CAAC,CACRtF,QAAQ,CAAC,CAAC,CACVuF,SAAS,CAAChH,iBAAiB,CAAC+B,cAAc,CAAC,CAC3CkF,QAAQ,CAACjH,iBAAiB,CAACkH,eAAe,CAAC,CAC3CC,SAAS,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAMA,GAAG,KAAK,EAAE,GAAGC,MAAM,CAACD,GAAG,CAAC,GAAG,IAAK,CAAC,CACxDE,GAAG,CAAC,CAAC,EAAEvH,iBAAiB,CAACkH,eAAe,CAAC;IAC9CN,SAAS,EAAE/G,GAAG,CAACkH,MAAM,CAAC,CAAC,CAACtF,QAAQ,CAAC;EACrC,CAAC,CAAC;EAEF4E,MAAM,EAAExG,GAAG,CAACmB,KAAK,CAAC,CAAC,CAACuF,IAAI,CAAC,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE;IACvDC,EAAE,EAAEA,CAAC1B,YAAoB,EAAEI,YAAoB,KAC3CJ,YAAY,KAAK3E,WAAW,CAACsG,GAAG,IAAIvB,YAAY,KAAKhF,yBAAyB,CAACiF,EAAE;IACrFwB,IAAI,EAAE9G,GAAG,CAACmB,KAAK,CAAC,CAAC,CAACwG,EAAE,CAChB3H,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;MACfsD,IAAI,EAAEvE,GAAG,CACJ4H,KAAK,CAAC,CAAC,CACP5D,IAAI,CACD,aAAa,EACb7D,iBAAiB,CAAC4B,QAAQ,EACzBF,KAAK,IAAKA,KAAK,KAAK,IAAI,IAAIrB,SAAS,CAACqH,GAAG,CAAEC,MAAM,IAAKA,MAAM,CAACjG,KAAK,CAAC,CAACkG,QAAQ,CAAClG,KAAK,CACvF,CAAC,CACAC,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC,CACpCH,QAAQ,CAAC,CAAC;MACf4C,IAAI,EAAExE,GAAG,CACJkH,MAAM,CAAC,CAAC,CACRtF,QAAQ,CAAC,CAAC,CACVE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC,CACpCuF,SAAS,CAAC,CAACzF,KAAK,EAAEmG,aAAa,KAAK;QACjC,IAAIA,aAAa,KAAK,EAAE,EAAE,OAAO,IAAI;QACrC,OAAOnG,KAAK;MAChB,CAAC,CAAC;MACN4C,OAAO,EAAEzE,GAAG,CACPkH,MAAM,CAAC,CAAC,CACRtF,QAAQ,CAAC,CAAC,CACVE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC,CACpCuF,SAAS,CAAC,CAACzF,KAAK,EAAEmG,aAAa,KAAK;QACjC,IAAIA,aAAa,KAAK,EAAE,EAAE,OAAO,IAAI;QACrC,OAAOnG,KAAK;MAChB,CAAC,CAAC;MACNwB,QAAQ,EAAErD,GAAG,CACRkH,MAAM,CAAC,CAAC,CACRtF,QAAQ,CAAC,CAAC,CACVE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC,CACpCuF,SAAS,CAAC,CAACzF,KAAK,EAAEmG,aAAa,KAAK;QACjC,IAAIA,aAAa,KAAK,EAAE,EAAE,OAAO,IAAI;QACrC,OAAOnG,KAAK;MAChB,CAAC,CAAC;MACN6C,MAAM,EAAE1E,GAAG,CACNkH,MAAM,CAAC,CAAC,CACRtF,QAAQ,CAAC,CAAC,CACV0F,SAAS,CAAC,CAACzF,KAAK,EAAEmG,aAAa,KAAK;QACjC,IAAIA,aAAa,KAAK,EAAE,EAAE,OAAO,IAAI;QACrC,OAAOnG,KAAK;MAChB,CAAC;IACT,CAAC,CACL;EACJ,CAAC,CAAC;EAEFkE,YAAY,EAAE/F,GAAG,CAACkH,MAAM,CAAC,CAAC,CAACR,IAAI,CAAC,cAAc,EAAE;IAC5CC,EAAE,EAAErG,WAAW,CAACsG,GAAG;IACnBE,IAAI,EAAE9G,GAAG,CAACkH,MAAM,CAAC,CAAC,CAACtF,QAAQ,CAAC,CAAC;IAC7BmF,SAAS,EAAE/G,GAAG,CAACkH,MAAM,CAAC,CAAC,CAACtF,QAAQ,CAAC;EACrC,CAAC,CAAC;EAEFkE,kBAAkB,EAAE9F,GAAG,CAACkH,MAAM,CAAC,CAAC,CAACR,IAAI,CAAC,cAAc,EAAE;IAClDC,EAAE,EAAErG,WAAW,CAACsG,GAAG;IACnBE,IAAI,EAAE9G,GAAG,CACJkH,MAAM,CAAC,CAAC,CACRtF,QAAQ,CAAC,CAAC,CACV0F,SAAS,CAAC,CAACzF,KAAK,EAAEmG,aAAa,KAAK;MACjC,MAAMC,WAAW,GAAGC,UAAU,CAACF,aAAa,CAAC;MAC7C,OAAOG,KAAK,CAACF,WAAW,CAAC,GAAGG,SAAS,GAAGH,WAAW;IACvD,CAAC,CAAC;IACNlB,SAAS,EAAE/G,GAAG,CAACkH,MAAM,CAAC,CAAC,CAACtF,QAAQ,CAAC;EACrC,CAAC;AACL,CAAC,CAAC;AACF;AACA;;AAQA,OAAO,MAAMyG,aAA6B,GAAG;EACzC,GAAG3H,sBAAsB;EACzB4H,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,EAAE;EACR3H,IAAI,EAAER,cAAc,CAAC,CAAC;EACtBoI,gCAAgC,EAAE,IAAI;EACtCjG,MAAM,EAAE,EAAE;EACVzB,QAAQ,EAAE;AACd,CAAC;AAED,OAAO,MAAM2H,aAAa,GAAGzI,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EAC5CsH,IAAI,EAAEvI,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EAC7BhB,IAAI,EAAEZ,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EAC7B4G,gCAAgC,EAAExI,GAAG,CAChCgB,MAAM,CAAC,CAAC,CACRC,KAAK,CAAC;IACHY,KAAK,EAAE7B,GAAG,CAACkB,MAAM,CAAC,CAAC;IACnB4B,KAAK,EAAE9C,GAAG,CAACkB,MAAM,CAAC;EACtB,CAAC,CAAC,CACDU,QAAQ,CAAC,CAAC;EACfW,MAAM,EAAEvC,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC;AAClC,CAAC,CAAC;AAEF,OAAO,MAAM8G,uBAAyC,GAAG;EACrD3D,WAAW,EAAE,EAAE;EACf4D,WAAW,EAAE;IACT/H,IAAI,EAAE,IAAI;IACVgI,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfN,gCAAgC,EAAE,IAAI;IACtCtD,WAAW,EAAE,EAAE;IACf3C,MAAM,EAAE,EAAE;IACVwG,cAAc,EAAE,IAAI;IACpBR,IAAI,EAAE,EAAE;IACRnD,WAAW,EAAE,EAAE;IACf4D,eAAe,EAAE,IAAI;IACrBC,oBAAoB,EAAE,IAAI;IAC1BC,kBAAkB,EAAE,IAAI;IACxB7D,YAAY,EAAE,EAAE;IAChBnC,IAAI,EAAE,EAAE;IACRiG,YAAY,EAAE;EAClB,CAAC;EACD;EACAC,aAAa,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,0BAA0B,EAAE,EAAE;IAC9BC,+BAA+B,EAAE,EAAE;IACnCC,UAAU,EAAE,EAAE;IACd5D,QAAQ,EAAE,EAAE;IACZE,YAAY,EAAE,EAAE;IAChB2D,eAAe,EAAE,EAAE;IACnBC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,EAAE;IACVC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE,EAAE;IACrBC,UAAU,EAAE;EAChB,CAAC;EACD;EACA1D,MAAM,EAAE;IACJ2D,WAAW,EAAE,EAAE;IACfC,GAAG,EAAE,EAAE;IACPC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,CACX;MACIlK,KAAK,EAAE,CAAC;MACRmK,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE;IACd,CAAC;EAET,CAAC;EACD;EACAC,SAAS,EAAE;IACPC,OAAO,EAAE,IAAI;IACbC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE;EAClB;AACJ,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAGzL,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EAChD8D,WAAW,EAAE/E,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EACpC;EACA+G,WAAW,EAAE3I,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IAC5BL,IAAI,EAAEZ,GAAG,CAACkH,MAAM,CAAC,CAAC,CAACtF,QAAQ,CAAC,CAAC;IAC7BgH,UAAU,EAAE5I,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;IACnCiH,QAAQ,EAAE7I,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;IACjCkH,WAAW,EAAE9I,GAAG,CACXkH,MAAM,CAAC,CAAC,CACRC,SAAS,CAAChH,iBAAiB,CAAC+B,cAAc,CAAC,CAC3CoF,SAAS,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAMA,GAAG,KAAK,EAAE,GAAGC,MAAM,CAACD,GAAG,CAAC,GAAG,IAAK,CAAC,CACxDE,GAAG,CAAC,CAAC,EAAE,CAAC;MAAEA;IAAI,CAAC,KAAK,GAAGvH,iBAAiB,CAACuL,eAAe,GAAGhE,GAAG,EAAE,CAAC,CACjEiE,GAAG,CAAC,GAAG,EAAE,CAAC;MAAEA;IAAI,CAAC,KAAK,GAAGxL,iBAAiB,CAACyL,aAAa,IAAID,GAAG,EAAE,CAAC,CAClE/J,QAAQ,CAAC,CAAC;IACf4G,gCAAgC,EAAExI,GAAG,CAChCgB,MAAM,CAAC,CAAC,CACRC,KAAK,CAAC;MACHY,KAAK,EAAE7B,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;MAC9BkB,KAAK,EAAE9C,GAAG,CAACkB,MAAM,CAAC;IACtB,CAAC,CAAC,CACDU,QAAQ,CAAC,CAAC,CACVE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;IACzCmD,WAAW,EAAElF,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;IACpCW,MAAM,EAAEvC,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;IACzDwG,IAAI,EAAEvI,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;IAClEqD,WAAW,EAAEpF,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;IACpCyD,YAAY,EAAErF,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;IACrCoH,eAAe,EAAEhJ,GAAG,CAAC6C,IAAI,CAAC,CAAC,CAACjB,QAAQ,CAAC,CAAC;IACtCqH,oBAAoB,EAAEjJ,GAAG,CAAC6C,IAAI,CAAC,CAAC,CAACjB,QAAQ,CAAC,CAAC;IAC3CsH,kBAAkB,EAAElJ,GAAG,CAAC6C,IAAI,CAAC,CAAC,CAACjB,QAAQ,CAAC,CAAC;IACzCsB,IAAI,EAAElD,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;IAC7BuH,YAAY,EAAEnJ,GAAG,CAACkB,MAAM,CAAC;EAC7B,CAAC,CAAC;EACF;EACAkI,aAAa,EAAEpJ,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IAC9BoI,OAAO,EAAErJ,GAAG,CACPkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACf0H,OAAO,EAAEtJ,GAAG,CACPkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACf2H,0BAA0B,EAAEvJ,GAAG,CAC1BkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACf4H,+BAA+B,EAAExJ,GAAG,CAC/BkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACf6H,UAAU,EAAEzJ,GAAG,CACVkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACfiE,QAAQ,EAAE7F,GAAG,CACRkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACfmE,YAAY,EAAE/F,GAAG,CACZkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACf8H,eAAe,EAAE1J,GAAG,CACfkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACf+H,OAAO,EAAE3J,GAAG,CACPkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACfgI,IAAI,EAAE5J,GAAG,CACJkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACfiI,MAAM,EAAE7J,GAAG,CACNkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACfkI,iBAAiB,EAAE9J,GAAG,CACjBkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACfmI,iBAAiB,EAAE/J,GAAG,CACjBkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACfoI,iBAAiB,EAAEhK,GAAG,CACjBkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACfqI,iBAAiB,EAAEjK,GAAG,CACjBkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACfsI,UAAU,EAAElK,GAAG,CACVkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC;EAClB,CAAC,CAAC;EACF;EACA4E,MAAM,EAAExG,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IACvBkJ,WAAW,EAAEnK,GAAG,CACXkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACfwI,GAAG,EAAEpK,GAAG,CACHkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACf0I,SAAS,EAAEtK,GAAG,CACTkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACf2I,SAAS,EAAEvK,GAAG,CACTkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACf4I,SAAS,EAAExK,GAAG,CACTkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACfyI,WAAW,EAAErK,GAAG,CACXkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACf6I,UAAU,EAAEzK,GAAG,CACVkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACf8I,UAAU,EAAE1K,GAAG,CACVkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACf+I,UAAU,EAAE3K,GAAG,CACVkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACfgJ,UAAU,EAAE5K,GAAG,CACVkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACfiJ,YAAY,EAAE7K,GAAG,CACZkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC;IACfkJ,aAAa,EAAE9K,GAAG,CACbkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC;EAClB,CAAC,CAAC;EACF;EACAuJ,SAAS,EAAEnL,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IAC1BmK,OAAO,EAAEpL,GAAG,CACPgB,MAAM,CAAC,CAAC,CACRC,KAAK,CAAC;MACHY,KAAK,EAAE7B,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;MAC9BkB,KAAK,EAAE9C,GAAG,CAACkB,MAAM,CAAC;IACtB,CAAC,CAAC,CACDU,QAAQ,CAAC,CAAC;IACfyJ,aAAa,EAAErL,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;IACtC0J,eAAe,EAAEtL,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;IACxC2J,WAAW,EAAEvL,GAAG,CACXkB,MAAM,CAAC,CAAC,CACRoG,SAAS,CAAEzF,KAAK,IAAMA,KAAK,KAAK,EAAE,GAAG,IAAI,GAAGA,KAAM,CAAC,CACnDG,OAAO,CAAC9B,eAAe,CAAC2L,kBAAkB,EAAE1L,iBAAiB,CAAC2L,mBAAmB,CAAC,CAClFH,GAAG,CAAC,EAAE,EAAExL,iBAAiB,CAAC4L,gBAAgB,CAAC,CAC3CnK,QAAQ,CAAC,CAAC;IACf4J,YAAY,EAAExL,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC;EACxC,CAAC;AACL,CAAC,CAAC;;AAEF;AACA;;AAWA,OAAO,MAAMoK,mBAAyC,GAAG;EACrD,GAAGtL,sBAAsB;EACzB4H,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,EAAE;EACR3H,IAAI,EAAER,cAAc,CAAC,CAAC;EACtBmC,MAAM,EAAE,EAAE;EACVzB,QAAQ,EAAE,IAAI;EACdoE,WAAW,EAAE,EAAE;EACf2D,QAAQ,EAAE,EAAE;EACZoD,mBAAmB,EAAE,UAAU;EAC/BC,oBAAoB,EAAE;AAC1B,CAAC;AAED,OAAO,MAAMC,oBAAoB,GAAGnM,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EACnDsH,IAAI,EAAEvI,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EAC7BhB,IAAI,EAAEZ,GAAG,CAACkB,MAAM,CAAC,CAAC;EAClBgE,WAAW,EAAElF,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EACpCW,MAAM,EAAEvC,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EAC/BiH,QAAQ,EAAE7I,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EACjCqK,mBAAmB,EAAEjM,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EAC5CsK,oBAAoB,EAAElM,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC;AAChD,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMwK,sBAAsB,GAAG;EAClC;EACAC,OAAO,EAAE;IACLxD,QAAQ,EAAE,EAAE;IACZ3D,WAAW,EAAE,EAAE;IACfoH,gBAAgB,EAAE,IAAI;IACtB/D,IAAI,EAAE,EAAE;IAAE;IACVnD,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,IAAI;IAClBnC,IAAI,EAAE,EAAE;IACRqJ,UAAU,EAAE,EAAE;IACd3D,UAAU,EAAE,EAAE;IACdE,WAAW,EAAE,EAAE;IACfvG,MAAM,EAAEhC,gBAAgB,CAACiM,OAAO;IAChCxD,eAAe,EAAE,EAAE;IACnBC,oBAAoB,EAAE,EAAE;IACxBC,kBAAkB,EAAE,EAAE;IACtBC,YAAY,EAAE,EAAE;IAChBJ,cAAc,EAAE;EACpB,CAAC;EACD;EACAK,aAAa,EAAE;IACX7D,oBAAoB,EAAE,EAAE;IACxBM,QAAQ,EAAE,EAAE;IACZE,YAAY,EAAE,EAAE;IAChBsD,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,0BAA0B,EAAE,EAAE;IAC9BkD,0BAA0B,EAAE,EAAE;IAC9BhD,UAAU,EAAE,EAAE;IACdS,UAAU,EAAE,EAAE;IACdR,eAAe,EAAE,EAAE;IACnBC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,EAAE;IACVC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE;EACvB,CAAC;EACD;EACAzD,MAAM,EAAE;IACJ2D,WAAW,EAAE,EAAE;IACfC,GAAG,EAAE,EAAE;IACPC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdE,aAAa,EAAE,EAAE;IACjBD,YAAY,EAAE,EAAE;IAChBE,aAAa,EAAE;EACnB,CAAC;EACD;EACAI,SAAS,EAAE;IACPC,OAAO,EAAE,IAAI;IACbC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE;EAClB;AACJ,CAAC;AAED,OAAO,MAAMkB,sBAAsB,GAAG1M,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EACrDoL,OAAO,EAAErM,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IACxBiE,WAAW,EAAElF,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;IAC9DuK,gBAAgB,EAAEtM,GAAG,CAChBgB,MAAM,CAAC,CAAC,CACRgD,IAAI,CAAC,EAAE,EAAE7D,iBAAiB,CAAC4B,QAAQ,EAAE,YAAqB;MACvD,MAAMQ,MAAM,GAAG,IAAI,CAAC2B,OAAO,CAAClE,GAAG,CAACmE,GAAG,CAAC,QAAQ,CAAC,CAAC;MAC9C,OAAO5B,MAAM,KAAKhC,gBAAgB,CAACoM,QAAQ,IAAI,IAAI,CAACzI,OAAO,CAAClE,GAAG,CAACmE,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAC5F,CAAC,CAAC,CACDlD,KAAK,CAAC;MACHY,KAAK,EAAE7B,GAAG,CAACkB,MAAM,CAAC,CAAC;MACnB4B,KAAK,EAAE9C,GAAG,CAACkB,MAAM,CAAC;IACtB,CAAC,CAAC,CACDU,QAAQ,CAAC,CAAC;IACf2K,UAAU,EAAEvM,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;IAC7DwG,IAAI,EAAEvI,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;IACvDqD,WAAW,EAAEpF,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;IAC9DQ,MAAM,EAAEvC,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;IACzDkH,oBAAoB,EAAEjJ,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC,CAACH,QAAQ,CAAC,CAAC;IAClFsH,kBAAkB,EAAElJ,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC,CAACH,QAAQ,CAAC;EACnF,CAAC,CAAC;EACFwH,aAAa,EAAEpJ,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IAC9B4E,QAAQ,EAAE7F,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ;EAC9D,CAAC,CAAC;EACFoJ,SAAS,EAAEnL,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IAC1BsK,WAAW,EAAEvL,GAAG,CACXkB,MAAM,CAAC,CAAC,CACRoG,SAAS,CAAEzF,KAAK,IAAMA,KAAK,KAAK,EAAE,GAAG,IAAI,GAAGA,KAAM,CAAC,CACnDG,OAAO,CAAC9B,eAAe,CAAC2L,kBAAkB,EAAE1L,iBAAiB,CAAC2L,mBAAmB,CAAC,CAClFH,GAAG,CAAC,EAAE,EAAExL,iBAAiB,CAAC4L,gBAAgB,CAAC,CAC3CnK,QAAQ,CAAC;EAClB,CAAC;AACL,CAAC,CAAC;AAEF,OAAO,MAAMgL,yBAAyB,GAAG;EACrCpH,IAAI,EAAE,WAAW;EACjBqH,WAAW,EAAE;AACjB,CAAC;AAED,OAAO,MAAMC,mBAAmB,GAAG9M,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EAClDuE,IAAI,EAAExF,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EAC7BiL,WAAW,EAAE7M,GAAG,CAACmB,KAAK,CAAC,CAAC,CAACwG,EAAE,CACvB3H,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IACfsD,IAAI,EAAEvE,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;IACvDyC,IAAI,EAAExE,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;IACvDsB,QAAQ,EAAErD,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ;EAC9D,CAAC,CACL;AACJ,CAAC,CAAC;AAEF,OAAO,MAAMgL,8BAA8B,GAAG;EAC1CC,yBAAyB,EAAE,EAAE;EAC7B9B,QAAQ,EAAE;AACd,CAAC;AAED,OAAO,MAAM+B,oBAAoB,GAAGjN,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EACnD+L,yBAAyB,EAAEhN,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ;AAC/E,CAAC,CAAC;;AAEF;;AASA,OAAO,MAAMmL,yBAA+C,GAAG;EAC3DtM,IAAI,EAAER,cAAc,CAAC,CAAC;EACtBmI,IAAI,EAAE,EAAE;EACR4E,YAAY,EAAE,EAAE;EAChBrM,QAAQ,EAAE;AACd,CAAC;AAED,OAAO,MAAMsM,yBAAyB,GAAGpN,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EACxDsH,IAAI,EAAEvI,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EAC7BhB,IAAI,EAAEZ,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC;AAChC,CAAC,CAAC;AAEF,OAAO,MAAMyL,6BAAiD,GAAG;EAC7DtI,WAAW,EAAE,EAAE;EACf4D,WAAW,EAAE;IACT/H,IAAI,EAAE,EAAE;IACR0M,uBAAuB,EAAE,EAAE;IAC3BC,gBAAgB,EAAE,EAAE;IACpBrI,WAAW,EAAE,EAAE;IACfqD,IAAI,EAAE,EAAE;IACRnD,WAAW,EAAE,EAAE;IACflC,IAAI,EAAE,EAAE;IACRsK,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,eAAe,EAAE,CAAC;IAClBC,mBAAmB,EAAE,IAAI;IACzBC,YAAY,EAAE,CAAC;IACf1D,UAAU,EAAE;EAChB,CAAC;EACD;EACA2D,eAAe,EAAE;IACbC,kBAAkB,EAAE,CAAC;IACrBC,gBAAgB,EAAE,CAAC;IACnBC,YAAY,EAAE,CAAC;IACfC,mBAAmB,EAAE,CAAC;IACtBC,cAAc,EAAE,CAAC;IACjBC,OAAO,EAAE,CAAC;IACVC,WAAW,EAAE,CAAC;IACdC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE;EACd,CAAC;EACD;EACAC,eAAe,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE,CAAC;IACpBC,eAAe,EAAE,CAAC;IAClBC,oBAAoB,EAAE,CAAC;IACvBC,0BAA0B,EAAE,CAAC;IAC7BC,iBAAiB,EAAE,CAAC;IACpBC,6BAA6B,EAAE,CAAC;IAChCC,2BAA2B,EAAE,CAAC;IAC9BC,sBAAsB,EAAE,CAAC;IACzBC,aAAa,EAAE,CAAC;IAChBC,QAAQ,EAAE;EACd;AACJ,CAAC;AAED,OAAO,MAAMC,uBAAuB,GAAGnP,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EACtD8D,WAAW,EAAE/E,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EACpC;EACA+G,WAAW,EAAE3I,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IAC5BuM,UAAU,EAAExN,GAAG,CACVkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC,CACV8F,GAAG,CAAC,CAAC,EAAEvH,iBAAiB,CAACiP,eAAe,CAAC,CACzCzD,GAAG,CAAC,GAAG,EAAExL,iBAAiB,CAACkP,eAAe,CAAC;IAChDnM,IAAI,EAAElD,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,CAAC+J,GAAG,CAAC,IAAI,EAAExL,iBAAiB,CAACmP,UAAU;EACxE,CAAC,CAAC;EACF;EACAzB,eAAe,EAAE7N,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IAChC+M,YAAY,EAAEhO,GAAG,CACZkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1D0N,OAAO,CAAC,CAAC,CACT3N,QAAQ,CAAC,CAAC,CACV8F,GAAG,CAAC,CAAC,EAAEvH,iBAAiB,CAACqP,iBAAiB,CAAC;IAChDvB,mBAAmB,EAAEjO,GAAG,CACnBkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC,CACV8E,IAAI,CAAC,cAAc,EAAGsH,YAAY,IAAU;MACzC,IAAIA,YAAY,EAAE;QACd,OAAOhO,GAAG,CACLkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC,CACV8F,GAAG,CAAC,CAAC,EAAEvH,iBAAiB,CAACsP,0BAA0B,CAAC,CACpD9D,GAAG,CAACqC,YAAY,EAAE7N,iBAAiB,CAACuP,2BAA2B,CAAC;MACzE;IACJ,CAAC,CAAC,CACDhI,GAAG,CAAC,CAAC,EAAEvH,iBAAiB,CAACsP,0BAA0B,CAAC,CACpD9D,GAAG,CAAC,EAAE,EAAExL,iBAAiB,CAACwP,0BAA0B,CAAC;IAC1DtB,OAAO,EAAErO,GAAG,CACPkH,MAAM,CAAC,CAAC,CACRI,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC,CAC1DD,QAAQ,CAAC,CAAC,CACV8F,GAAG,CAAC,CAAC,EAAEvH,iBAAiB,CAACyP,YAAY,CAAC,CACtCjE,GAAG,CAAC,EAAE,EAAExL,iBAAiB,CAAC0P,YAAY;EAC/C,CAAC,CAAC;EACF;EACAtB,eAAe,EAAEvO,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;IAChC+N,sBAAsB,EAAEhP,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC;EAClD,CAAC;AACL,CAAC,CAAC;;AAEF;;AAQA,OAAO,MAAMkO,kCAAuE,GAAG;EACnF,GAAGpP,sBAAsB;EACzBsE,SAAS,EAAE,IAAI;EACf+K,WAAW,EAAE,EAAE;EACfzM,UAAU,EAAE,EAAE;EACdH,MAAM,EAAE;AACZ,CAAC;AAED,OAAO,MAAM6M,4BAA4B,GAAGhQ,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EAC3D8O,WAAW,EAAE/P,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EACpC0B,UAAU,EAAEtD,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EACnCuB,MAAM,EAAEnD,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC;AAClC,CAAC,CAAC;;AAEF;;AAEA;;AASA,OAAO,MAAMqO,kCAAuE,GAAG;EACnF,GAAGvP,sBAAsB;EACzB6H,IAAI,EAAE,EAAE;EACR2H,WAAW,EAAE,EAAE;EACfC,OAAO,EAAE,EAAE;EACXC,mBAAmB,EAAE,EAAE;EACvB7N,MAAM,EAAE;AACZ,CAAC;AAED,OAAO,MAAM8N,qBAAqB,GAAGrQ,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EACpDsH,IAAI,EAAEvI,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EAC7BsO,WAAW,EAAElQ,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EACpCuO,OAAO,EAAEnQ,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EAChCwO,mBAAmB,EAAEpQ,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EAC5CW,MAAM,EAAEvC,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC;AAClC,CAAC,CAAC;;AAEF;AACA,OAAO,MAAM0O,iCAAiD,GAAG;EAC7D/H,IAAI,EAAE,EAAE;EACRgI,YAAY,EAAE,EAAE;EAChBH,mBAAmB,EAAE,EAAE;EACvBI,aAAa,EAAE,IAAI;EACnBC,WAAW,EAAE,EAAE;EACfC,kBAAkB,EAAE,EAAE;EACtBC,wBAAwB,EAAE,EAAE;EAC5BC,UAAU,EAAE,EAAE;EACdC,KAAK,EAAE,EAAE;EACTC,IAAI,EAAE,EAAE;EACRX,OAAO,EAAE,EAAE;EACX;EACA5N,MAAM,EAAE,gBAAgB;EACxBwO,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,EAAE;EACXC,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE;AACb,CAAC;AAED,OAAO,MAAMC,4BAA4B,GAAGnR,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EAC3DsH,IAAI,EAAEvI,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EAClEwO,YAAY,EAAEvQ,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EACrCwO,mBAAmB,EAAEpQ,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EACjFyO,aAAa,EAAExQ,GAAG,CACbkH,MAAM,CAAC,CAAC,CACRtF,QAAQ,CAAC,CAAC,CACV0F,SAAS,CAAEzF,KAAK,IAAM4F,MAAM,CAACU,KAAK,CAACtG,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAM,CAAC;EAC/D4O,WAAW,EAAEzQ,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EACpC8O,kBAAkB,EAAE1Q,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EAC3C+O,wBAAwB,EAAE3Q,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EACjDgP,UAAU,EAAE5Q,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EACnCiP,KAAK,EAAE7Q,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EAC9BuO,OAAO,EAAEnQ,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EAChCW,MAAM,EAAEvC,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC;EAC/BmP,OAAO,EAAE/Q,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAAC;EACrEiP,OAAO,EAAEhR,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC;AACnC,CAAC,CAAC;AAEF,OAAO,MAAMwP,sBAAsB,GAAG;EAClCC,KAAK,EAAE;AACX,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAGtR,GAAG,CAACgB,MAAM,CAAC,CAAC,CAACC,KAAK,CAAC;EAChDoQ,KAAK,EAAErR,GAAG,CAACkB,MAAM,CAAC,CAAC,CAACU,QAAQ,CAAC,CAAC,CAACE,QAAQ,CAAC3B,iBAAiB,CAAC4B,QAAQ;AACtE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}