{"ast": null, "code": "// redux\nimport { createAsyncThunk, createSlice } from '@reduxjs/toolkit';\n\n// project imports\n\nimport sendRequest from 'services/ApiService';\nimport Api from 'constants/Api';\n\n// interface\n\n// initialState\nconst initialState = {\n  laguageConfigList: [],\n  loading: {}\n};\n\n// Call API\nexport const getLanguage = createAsyncThunk(Api.flexible_textConfig.getLanguage.url, async () => {\n  const response = await sendRequest(Api.flexible_textConfig.getLanguage);\n  return response;\n});\n\n// Slice & Actions\nconst languageConfigSlice = createSlice({\n  name: 'language-config',\n  initialState: initialState,\n  reducers: {},\n  extraReducers: builder => {\n    // getAll\n    builder.addCase(getLanguage.pending, state => {\n      state.laguageConfigList = [];\n      state.loading[getLanguage.typePrefix] = true;\n    });\n    builder.addCase(getLanguage.fulfilled, (state, action) => {\n      var _action$payload;\n      if ((_action$payload = action.payload) !== null && _action$payload !== void 0 && _action$payload.status) {\n        const {\n          content\n        } = action.payload.result;\n        state.laguageConfigList = content;\n      }\n      state.loading[getLanguage.typePrefix] = false;\n    });\n    builder.addCase(getLanguage.rejected, state => {\n      state.loading[getLanguage.typePrefix] = false;\n    });\n  }\n});\n\n// Reducer & export\nexport default languageConfigSlice.reducer;\n\n// export const {} = rankSlice.actions;\n\n// Selector & export\nexport const languageConfigSelector = state => state.languageConfig;", "map": {"version": 3, "names": ["createAsyncThunk", "createSlice", "sendRequest", "Api", "initialState", "laguageConfigList", "loading", "getLanguage", "flexible_textConfig", "url", "response", "languageConfigSlice", "name", "reducers", "extraReducers", "builder", "addCase", "pending", "state", "typePrefix", "fulfilled", "action", "_action$payload", "payload", "status", "content", "result", "rejected", "reducer", "languageConfigSelector", "languageConfig"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/store/slice/languageConfigSlice.ts"], "sourcesContent": ["// redux\nimport { createAsyncThunk, createSlice } from '@reduxjs/toolkit';\nimport { RootState } from 'app/store';\n\n// project imports\nimport { IResponseList } from 'types';\nimport sendRequest from 'services/ApiService';\nimport Api from 'constants/Api';\nimport { IGetAllLanguage, ILanguageConfig } from 'types/languageConfig';\n\n// interface\ninterface IRankInitialState {\n    laguageConfigList: ILanguageConfig[];\n    loading: {\n        [key: string]: boolean;\n    };\n}\n\n// initialState\nconst initialState: IRankInitialState = {\n    laguageConfigList: [],\n    loading: {}\n};\n\n// Call API\nexport const getLanguage = createAsyncThunk<IResponseList<IGetAllLanguage>>(Api.flexible_textConfig.getLanguage.url, async () => {\n    const response = await sendRequest(Api.flexible_textConfig.getLanguage);\n    return response;\n});\n\n// Slice & Actions\nconst languageConfigSlice = createSlice({\n    name: 'language-config',\n    initialState: initialState,\n    reducers: {},\n    extraReducers: (builder) => {\n        // getAll\n        builder.addCase(getLanguage.pending, (state) => {\n            state.laguageConfigList = [];\n            state.loading[getLanguage.typePrefix] = true;\n        });\n        builder.addCase(getLanguage.fulfilled, (state, action) => {\n            if (action.payload?.status) {\n                const { content } = action.payload.result;\n                state.laguageConfigList = content;\n            }\n            state.loading[getLanguage.typePrefix] = false;\n        });\n        builder.addCase(getLanguage.rejected, (state) => {\n            state.loading[getLanguage.typePrefix] = false;\n        });\n    }\n});\n\n// Reducer & export\nexport default languageConfigSlice.reducer;\n\n// export const {} = rankSlice.actions;\n\n// Selector & export\nexport const languageConfigSelector = (state: RootState) => state.languageConfig;\n"], "mappings": "AAAA;AACA,SAASA,gBAAgB,EAAEC,WAAW,QAAQ,kBAAkB;;AAGhE;;AAEA,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,GAAG,MAAM,eAAe;;AAG/B;;AAQA;AACA,MAAMC,YAA+B,GAAG;EACpCC,iBAAiB,EAAE,EAAE;EACrBC,OAAO,EAAE,CAAC;AACd,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAGP,gBAAgB,CAAiCG,GAAG,CAACK,mBAAmB,CAACD,WAAW,CAACE,GAAG,EAAE,YAAY;EAC7H,MAAMC,QAAQ,GAAG,MAAMR,WAAW,CAACC,GAAG,CAACK,mBAAmB,CAACD,WAAW,CAAC;EACvE,OAAOG,QAAQ;AACnB,CAAC,CAAC;;AAEF;AACA,MAAMC,mBAAmB,GAAGV,WAAW,CAAC;EACpCW,IAAI,EAAE,iBAAiB;EACvBR,YAAY,EAAEA,YAAY;EAC1BS,QAAQ,EAAE,CAAC,CAAC;EACZC,aAAa,EAAGC,OAAO,IAAK;IACxB;IACAA,OAAO,CAACC,OAAO,CAACT,WAAW,CAACU,OAAO,EAAGC,KAAK,IAAK;MAC5CA,KAAK,CAACb,iBAAiB,GAAG,EAAE;MAC5Ba,KAAK,CAACZ,OAAO,CAACC,WAAW,CAACY,UAAU,CAAC,GAAG,IAAI;IAChD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACT,WAAW,CAACa,SAAS,EAAE,CAACF,KAAK,EAAEG,MAAM,KAAK;MAAA,IAAAC,eAAA;MACtD,KAAAA,eAAA,GAAID,MAAM,CAACE,OAAO,cAAAD,eAAA,eAAdA,eAAA,CAAgBE,MAAM,EAAE;QACxB,MAAM;UAAEC;QAAQ,CAAC,GAAGJ,MAAM,CAACE,OAAO,CAACG,MAAM;QACzCR,KAAK,CAACb,iBAAiB,GAAGoB,OAAO;MACrC;MACAP,KAAK,CAACZ,OAAO,CAACC,WAAW,CAACY,UAAU,CAAC,GAAG,KAAK;IACjD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACT,WAAW,CAACoB,QAAQ,EAAGT,KAAK,IAAK;MAC7CA,KAAK,CAACZ,OAAO,CAACC,WAAW,CAACY,UAAU,CAAC,GAAG,KAAK;IACjD,CAAC,CAAC;EACN;AACJ,CAAC,CAAC;;AAEF;AACA,eAAeR,mBAAmB,CAACiB,OAAO;;AAE1C;;AAEA;AACA,OAAO,MAAMC,sBAAsB,GAAIX,KAAgB,IAAKA,KAAK,CAACY,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}