{"ast": null, "code": "import{Grid,Typography,List,ListItem,ListItemText}from'@mui/material';import FiberManualRecordIcon from'@mui/icons-material/FiberManualRecord';import{createSearchParams,useNavigate}from'react-router-dom';import{FormattedMessage}from'react-intl';import Chart from'react-apexcharts';import SkeletonSummaryCard from'components/cards/Skeleton/SummaryCard';import MainCard from'components/cards/MainCard';import{ROUTER}from'constants/Routers';import{TEXT_CONFIG_SCREEN}from'constants/Common';// =========================|| ACTUAL EFFORT ALLOCATION CARD ||========================= //\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ActualEffortAllocationCard=_ref=>{let{isLoading,data=[],conditions}=_ref;const navigate=useNavigate();const{Summary}=TEXT_CONFIG_SCREEN.monthlyEffort;return isLoading?/*#__PURE__*/_jsx(SkeletonSummaryCard,{}):/*#__PURE__*/_jsx(MainCard,{title:/*#__PURE__*/_jsx(FormattedMessage,{id:Summary+'effort-allocation'}),children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,alignItems:\"center\",children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:4,children:/*#__PURE__*/_jsxs(List,{children:[/*#__PURE__*/_jsx(ListItem,{sx:{'& .MuiTypography-root':{fontWeight:'700 !important'}},secondaryAction:/*#__PURE__*/_jsx(Typography,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:Summary+'effort-md'})}),children:/*#__PURE__*/_jsx(ListItemText,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:Summary+'type'})})}),data.map((item,index)=>/*#__PURE__*/_jsx(ListItem,{secondaryAction:/*#__PURE__*/_jsx(Typography,{children:item.effort}),children:/*#__PURE__*/_jsx(ListItemText,{children:item.type})},index))]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:8,children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,alignItems:\"center\",children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsx(Chart,{options:{colors:data.map(item=>item.color||'#ffffff'),responsive:[{breakpoint:1920,options:{chart:{width:350}}}],legend:{show:false},chart:{events:{dataPointSelection:(_event,_chartContext,config)=>{const index=config.dataPointIndex;navigate({pathname:\"/\".concat(ROUTER.reports.monthly_effort.index,\"/\").concat(ROUTER.reports.monthly_effort.project),search:\"?\".concat(createSearchParams({...conditions,projectType:data[index].type}))});},dataPointMouseEnter:function(event){event.target.style.cursor='pointer';}}},labels:data.map(item=>item.type)},series:data.map(item=>item.effort),type:\"pie\"})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:data.map((item,index)=>/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:12,children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,alignItems:\"center\",justifyContent:\"center\",children:[/*#__PURE__*/_jsx(Grid,{item:true,children:/*#__PURE__*/_jsx(FiberManualRecordIcon,{sx:{color:item.color||'#ffffff'}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:true,zeroMinWidth:true,children:/*#__PURE__*/_jsx(Grid,{container:true,spacing:1,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:true,zeroMinWidth:true,children:/*#__PURE__*/_jsx(Typography,{align:\"left\",variant:\"body2\",children:item.type})})})})]})},index))})]})})]})});};export default ActualEffortAllocationCard;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}