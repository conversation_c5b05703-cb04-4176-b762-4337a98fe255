{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/pages/non-billable-monitoring/non-bill-by-member/WarningNBMMember.tsx\",\n  _s = $RefreshSig$();\nimport { useSearchParams } from 'react-router-dom';\nimport { useCallback, useEffect, useState } from 'react';\nimport { useIntl } from 'react-intl';\nimport { WarningNonBillByMemberSearch, WarningNonbillMemberThead, WarningNonbillMemberTBody } from 'containers/non-billable-monitoring';\nimport { getWarningNBMBytMember, nonBillableMonitoringSelector } from 'store/slice/nonBillableMonitoringSlice';\nimport { openCommentDialog, isCommentedSelector, changeCommented } from 'store/slice/commentSlice';\nimport { convertWeekFromToDate, getNumberOfWeek } from 'utils/date';\nimport { REPORT_TYPE, TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { exportDocument, transformObject } from 'utils/common';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { useAppDispatch, useAppSelector } from 'app/hooks';\nimport { FilterCollapse } from 'containers/search';\nimport { PERMISSIONS } from 'constants/Permission';\nimport { Table } from 'components/extended/Table';\nimport MainCard from 'components/cards/MainCard';\nimport Api from 'constants/Api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst WarningNonBillByMember = ({\n  weeks,\n  defaultConditions,\n  formReset,\n  params,\n  handleChangeYear,\n  getWeekandYearWhenSearch\n}) => {\n  _s();\n  const [conditions, setConditions] = useState(defaultConditions);\n  const {\n    warningNBMByMember,\n    loading\n  } = useAppSelector(nonBillableMonitoringSelector);\n  const {\n    nBMByMember\n  } = TEXT_CONFIG_SCREEN.nonBillablemonitoring;\n  const dispatch = useAppDispatch();\n  const intl = useIntl();\n  const [, setSearchParams] = useSearchParams();\n  const {\n    nonBillable\n  } = PERMISSIONS.report;\n  const isCommented = useAppSelector(isCommentedSelector);\n  const handleOpenCommentDialog = (userId, subTitle) => {\n    const updatedConditions = {\n      ...conditions,\n      userId,\n      reportType: REPORT_TYPE.RP_NON_BILLABLE_MONITORING\n    };\n    const titleDetail = conditions !== null && conditions !== void 0 && conditions.week ? `${getNumberOfWeek(conditions.week)} - ${conditions.year}` : '';\n    dispatch(openCommentDialog({\n      conditions: updatedConditions,\n      titleDetail: userId ? subTitle : intl.formatMessage({\n        id: 'week'\n      }) + ' ' + titleDetail\n    }));\n  };\n  const handleExportDocument = () => {\n    exportDocument(Api.non_billable_monitoring.getDownload.url, {\n      ...convertWeekFromToDate(conditions.week),\n      weekNumber: getNumberOfWeek(conditions.week),\n      year: conditions.year\n    });\n  };\n\n  // Handle submit\n  const handleSearch = value => {\n    transformObject(value);\n    setSearchParams({\n      ...params,\n      ...value\n    });\n    setConditions(value);\n    getWeekandYearWhenSearch === null || getWeekandYearWhenSearch === void 0 ? void 0 : getWeekandYearWhenSearch(value.week, value.year);\n  };\n  const getTableData = useCallback(() => {\n    const weekSelected = convertWeekFromToDate(conditions.week);\n    dispatch(getWarningNBMBytMember({\n      ...weekSelected,\n      ...transformObject({\n        ...conditions\n      }, ['tab', 'week']),\n      reportType: REPORT_TYPE.RP_NON_BILLABLE_MONITORING\n    }));\n  }, [conditions, dispatch]);\n  useEffect(getTableData, [getTableData]);\n  useEffect(() => {\n    if (isCommented) {\n      getTableData();\n      dispatch(changeCommented(false));\n    }\n  }, [isCommented, dispatch, getTableData]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(FilterCollapse, {\n      downloadLabel: nBMByMember + 'download-report',\n      commentLabel: nBMByMember + 'commnents',\n      handleExport: checkAllowedPermission(nonBillable.download) ? handleExportDocument : undefined,\n      handleOpenCommentDialog: checkAllowedPermission(nonBillable.comment) ? handleOpenCommentDialog : undefined,\n      children: /*#__PURE__*/_jsxDEV(WarningNonBillByMemberSearch, {\n        conditions: formReset,\n        weeks: weeks,\n        handleChangeYear: handleChangeYear,\n        handleSearch: handleSearch\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(MainCard, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        heads: /*#__PURE__*/_jsxDEV(WarningNonbillMemberThead, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 28\n        }, this),\n        isLoading: loading[getWarningNBMBytMember.typePrefix],\n        data: warningNBMByMember,\n        children: /*#__PURE__*/_jsxDEV(WarningNonbillMemberTBody, {\n          data: warningNBMByMember\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(WarningNonBillByMember, \"U7Bu871EwHNEXb0aylCX3lS5dng=\", false, function () {\n  return [useAppSelector, useAppDispatch, useIntl, useSearchParams, useAppSelector];\n});\n_c = WarningNonBillByMember;\nexport default WarningNonBillByMember;\nvar _c;\n$RefreshReg$(_c, \"WarningNonBillByMember\");", "map": {"version": 3, "names": ["useSearchParams", "useCallback", "useEffect", "useState", "useIntl", "WarningNonBillByMemberSearch", "WarningNonbillMemberThead", "WarningNonbillMemberTBody", "getWarningNBMBytMember", "nonBillableMonitoringSelector", "openCommentDialog", "isCommentedSelector", "changeCommented", "convertWeekFromToDate", "getNumberOfWeek", "REPORT_TYPE", "TEXT_CONFIG_SCREEN", "exportDocument", "transformObject", "checkAllowedPermission", "useAppDispatch", "useAppSelector", "FilterCollapse", "PERMISSIONS", "Table", "MainCard", "Api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "WarningNonBillByMember", "weeks", "defaultConditions", "formReset", "params", "handleChangeYear", "getWeekandYearWhenSearch", "_s", "conditions", "setConditions", "warningNBMByMember", "loading", "nBMByMember", "nonBillablemonitoring", "dispatch", "intl", "setSearchParams", "nonBillable", "report", "isCommented", "handleOpenCommentDialog", "userId", "subTitle", "updatedConditions", "reportType", "RP_NON_BILLABLE_MONITORING", "titleDetail", "week", "year", "formatMessage", "id", "handleExportDocument", "non_billable_monitoring", "getDownload", "url", "weekNumber", "handleSearch", "value", "getTableData", "weekSelected", "children", "downloadLabel", "comment<PERSON>abel", "handleExport", "download", "undefined", "comment", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "heads", "isLoading", "typePrefix", "data", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/pages/non-billable-monitoring/non-bill-by-member/WarningNBMMember.tsx"], "sourcesContent": ["import { URLSearchParamsInit, useSearchParams } from 'react-router-dom';\nimport { useCallback, useEffect, useState } from 'react';\nimport { SelectChangeEvent } from '@mui/material';\nimport { useIntl } from 'react-intl';\n\nimport { WarningNonBillByMemberSearch, WarningNonbillMemberThead, WarningNonbillMemberTBody } from 'containers/non-billable-monitoring';\nimport { getWarningNBMBytMember, nonBillableMonitoringSelector } from 'store/slice/nonBillableMonitoringSlice';\nimport { openCommentDialog, isCommentedSelector, changeCommented } from 'store/slice/commentSlice';\nimport { convertWeekFromToDate, getNumberOfWeek } from 'utils/date';\nimport { REPORT_TYPE, TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { exportDocument, transformObject } from 'utils/common';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { useAppDispatch, useAppSelector } from 'app/hooks';\nimport { FilterCollapse } from 'containers/search';\nimport { PERMISSIONS } from 'constants/Permission';\nimport { Table } from 'components/extended/Table';\nimport MainCard from 'components/cards/MainCard';\nimport { INonBillConfig } from '../Config';\nimport { IOption } from 'types';\nimport Api from 'constants/Api';\n\ninterface IProps {\n    weeks: IOption[];\n    formReset: INonBillConfig;\n    defaultConditions: INonBillConfig;\n    params: {\n        [key: string]: any;\n    };\n    handleChangeYear: (e: SelectChangeEvent<unknown>) => void;\n    getWeekandYearWhenSearch?: (week: string, year: string | number) => void;\n}\n\nconst WarningNonBillByMember = ({ weeks, defaultConditions, formReset, params, handleChangeYear, getWeekandYearWhenSearch }: IProps) => {\n    const [conditions, setConditions] = useState<INonBillConfig>(defaultConditions);\n\n    const { warningNBMByMember, loading } = useAppSelector(nonBillableMonitoringSelector);\n\n    const { nBMByMember } = TEXT_CONFIG_SCREEN.nonBillablemonitoring;\n\n    const dispatch = useAppDispatch();\n\n    const intl = useIntl();\n\n    const [, setSearchParams] = useSearchParams();\n\n    const { nonBillable } = PERMISSIONS.report;\n    const isCommented = useAppSelector(isCommentedSelector);\n\n    const handleOpenCommentDialog = (userId?: string, subTitle?: string) => {\n        const updatedConditions = { ...conditions, userId, reportType: REPORT_TYPE.RP_NON_BILLABLE_MONITORING };\n        const titleDetail = conditions?.week ? `${getNumberOfWeek(conditions.week)} - ${conditions.year}` : '';\n\n        dispatch(\n            openCommentDialog({\n                conditions: updatedConditions,\n                titleDetail: userId ? subTitle : intl.formatMessage({ id: 'week' }) + ' ' + titleDetail\n            })\n        );\n    };\n\n    const handleExportDocument = () => {\n        exportDocument(Api.non_billable_monitoring.getDownload.url, {\n            ...convertWeekFromToDate(conditions.week),\n            weekNumber: getNumberOfWeek(conditions.week),\n            year: conditions.year\n        });\n    };\n\n    // Handle submit\n    const handleSearch = (value: INonBillConfig) => {\n        transformObject(value);\n\n        setSearchParams({ ...params, ...value } as unknown as URLSearchParamsInit);\n        setConditions(value);\n        getWeekandYearWhenSearch?.(value.week as string, value.year);\n    };\n\n    const getTableData = useCallback(() => {\n        const weekSelected = convertWeekFromToDate(conditions.week);\n\n        dispatch(\n            getWarningNBMBytMember({\n                ...weekSelected,\n                ...transformObject({ ...conditions }, ['tab', 'week']),\n                reportType: REPORT_TYPE.RP_NON_BILLABLE_MONITORING\n            })\n        );\n    }, [conditions, dispatch]);\n\n    useEffect(getTableData, [getTableData]);\n\n    useEffect(() => {\n        if (isCommented) {\n            getTableData();\n            dispatch(changeCommented(false));\n        }\n    }, [isCommented, dispatch, getTableData]);\n\n    return (\n        <>\n            <FilterCollapse\n                downloadLabel={nBMByMember + 'download-report'}\n                commentLabel={nBMByMember + 'commnents'}\n                handleExport={checkAllowedPermission(nonBillable.download) ? handleExportDocument : undefined}\n                handleOpenCommentDialog={checkAllowedPermission(nonBillable.comment) ? handleOpenCommentDialog : undefined}\n            >\n                <WarningNonBillByMemberSearch\n                    conditions={formReset}\n                    weeks={weeks}\n                    handleChangeYear={handleChangeYear}\n                    handleSearch={handleSearch}\n                />\n            </FilterCollapse>\n\n            <MainCard>\n                <Table\n                    heads={<WarningNonbillMemberThead />}\n                    isLoading={loading[getWarningNBMBytMember.typePrefix]}\n                    data={warningNBMByMember}\n                >\n                    <WarningNonbillMemberTBody data={warningNBMByMember} />\n                </Table>\n            </MainCard>\n        </>\n    );\n};\n\nexport default WarningNonBillByMember;\n"], "mappings": ";;AAAA,SAA8BA,eAAe,QAAQ,kBAAkB;AACvE,SAASC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAExD,SAASC,OAAO,QAAQ,YAAY;AAEpC,SAASC,4BAA4B,EAAEC,yBAAyB,EAAEC,yBAAyB,QAAQ,oCAAoC;AACvI,SAASC,sBAAsB,EAAEC,6BAA6B,QAAQ,wCAAwC;AAC9G,SAASC,iBAAiB,EAAEC,mBAAmB,EAAEC,eAAe,QAAQ,0BAA0B;AAClG,SAASC,qBAAqB,EAAEC,eAAe,QAAQ,YAAY;AACnE,SAASC,WAAW,EAAEC,kBAAkB,QAAQ,kBAAkB;AAClE,SAASC,cAAc,EAAEC,eAAe,QAAQ,cAAc;AAC9D,SAASC,sBAAsB,QAAQ,qBAAqB;AAC5D,SAASC,cAAc,EAAEC,cAAc,QAAQ,WAAW;AAC1D,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,KAAK,QAAQ,2BAA2B;AACjD,OAAOC,QAAQ,MAAM,2BAA2B;AAGhD,OAAOC,GAAG,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAahC,MAAMC,sBAAsB,GAAGA,CAAC;EAAEC,KAAK;EAAEC,iBAAiB;EAAEC,SAAS;EAAEC,MAAM;EAAEC,gBAAgB;EAAEC;AAAiC,CAAC,KAAK;EAAAC,EAAA;EACpI,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAiB8B,iBAAiB,CAAC;EAE/E,MAAM;IAAEQ,kBAAkB;IAAEC;EAAQ,CAAC,GAAGrB,cAAc,CAACZ,6BAA6B,CAAC;EAErF,MAAM;IAAEkC;EAAY,CAAC,GAAG3B,kBAAkB,CAAC4B,qBAAqB;EAEhE,MAAMC,QAAQ,GAAGzB,cAAc,CAAC,CAAC;EAEjC,MAAM0B,IAAI,GAAG1C,OAAO,CAAC,CAAC;EAEtB,MAAM,GAAG2C,eAAe,CAAC,GAAG/C,eAAe,CAAC,CAAC;EAE7C,MAAM;IAAEgD;EAAY,CAAC,GAAGzB,WAAW,CAAC0B,MAAM;EAC1C,MAAMC,WAAW,GAAG7B,cAAc,CAACV,mBAAmB,CAAC;EAEvD,MAAMwC,uBAAuB,GAAGA,CAACC,MAAe,EAAEC,QAAiB,KAAK;IACpE,MAAMC,iBAAiB,GAAG;MAAE,GAAGf,UAAU;MAAEa,MAAM;MAAEG,UAAU,EAAExC,WAAW,CAACyC;IAA2B,CAAC;IACvG,MAAMC,WAAW,GAAGlB,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEmB,IAAI,GAAG,GAAG5C,eAAe,CAACyB,UAAU,CAACmB,IAAI,CAAC,MAAMnB,UAAU,CAACoB,IAAI,EAAE,GAAG,EAAE;IAEtGd,QAAQ,CACJnC,iBAAiB,CAAC;MACd6B,UAAU,EAAEe,iBAAiB;MAC7BG,WAAW,EAAEL,MAAM,GAAGC,QAAQ,GAAGP,IAAI,CAACc,aAAa,CAAC;QAAEC,EAAE,EAAE;MAAO,CAAC,CAAC,GAAG,GAAG,GAAGJ;IAChF,CAAC,CACL,CAAC;EACL,CAAC;EAED,MAAMK,oBAAoB,GAAGA,CAAA,KAAM;IAC/B7C,cAAc,CAACS,GAAG,CAACqC,uBAAuB,CAACC,WAAW,CAACC,GAAG,EAAE;MACxD,GAAGpD,qBAAqB,CAAC0B,UAAU,CAACmB,IAAI,CAAC;MACzCQ,UAAU,EAAEpD,eAAe,CAACyB,UAAU,CAACmB,IAAI,CAAC;MAC5CC,IAAI,EAAEpB,UAAU,CAACoB;IACrB,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMQ,YAAY,GAAIC,KAAqB,IAAK;IAC5ClD,eAAe,CAACkD,KAAK,CAAC;IAEtBrB,eAAe,CAAC;MAAE,GAAGZ,MAAM;MAAE,GAAGiC;IAAM,CAAmC,CAAC;IAC1E5B,aAAa,CAAC4B,KAAK,CAAC;IACpB/B,wBAAwB,aAAxBA,wBAAwB,uBAAxBA,wBAAwB,CAAG+B,KAAK,CAACV,IAAI,EAAYU,KAAK,CAACT,IAAI,CAAC;EAChE,CAAC;EAED,MAAMU,YAAY,GAAGpE,WAAW,CAAC,MAAM;IACnC,MAAMqE,YAAY,GAAGzD,qBAAqB,CAAC0B,UAAU,CAACmB,IAAI,CAAC;IAE3Db,QAAQ,CACJrC,sBAAsB,CAAC;MACnB,GAAG8D,YAAY;MACf,GAAGpD,eAAe,CAAC;QAAE,GAAGqB;MAAW,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;MACtDgB,UAAU,EAAExC,WAAW,CAACyC;IAC5B,CAAC,CACL,CAAC;EACL,CAAC,EAAE,CAACjB,UAAU,EAAEM,QAAQ,CAAC,CAAC;EAE1B3C,SAAS,CAACmE,YAAY,EAAE,CAACA,YAAY,CAAC,CAAC;EAEvCnE,SAAS,CAAC,MAAM;IACZ,IAAIgD,WAAW,EAAE;MACbmB,YAAY,CAAC,CAAC;MACdxB,QAAQ,CAACjC,eAAe,CAAC,KAAK,CAAC,CAAC;IACpC;EACJ,CAAC,EAAE,CAACsC,WAAW,EAAEL,QAAQ,EAAEwB,YAAY,CAAC,CAAC;EAEzC,oBACIzC,OAAA,CAAAE,SAAA;IAAAyC,QAAA,gBACI3C,OAAA,CAACN,cAAc;MACXkD,aAAa,EAAE7B,WAAW,GAAG,iBAAkB;MAC/C8B,YAAY,EAAE9B,WAAW,GAAG,WAAY;MACxC+B,YAAY,EAAEvD,sBAAsB,CAAC6B,WAAW,CAAC2B,QAAQ,CAAC,GAAGb,oBAAoB,GAAGc,SAAU;MAC9FzB,uBAAuB,EAAEhC,sBAAsB,CAAC6B,WAAW,CAAC6B,OAAO,CAAC,GAAG1B,uBAAuB,GAAGyB,SAAU;MAAAL,QAAA,eAE3G3C,OAAA,CAACvB,4BAA4B;QACzBkC,UAAU,EAAEL,SAAU;QACtBF,KAAK,EAAEA,KAAM;QACbI,gBAAgB,EAAEA,gBAAiB;QACnC+B,YAAY,EAAEA;MAAa;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC,eAEjBrD,OAAA,CAACH,QAAQ;MAAA8C,QAAA,eACL3C,OAAA,CAACJ,KAAK;QACF0D,KAAK,eAAEtD,OAAA,CAACtB,yBAAyB;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACrCE,SAAS,EAAEzC,OAAO,CAAClC,sBAAsB,CAAC4E,UAAU,CAAE;QACtDC,IAAI,EAAE5C,kBAAmB;QAAA8B,QAAA,eAEzB3C,OAAA,CAACrB,yBAAyB;UAAC8E,IAAI,EAAE5C;QAAmB;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA,eACb,CAAC;AAEX,CAAC;AAAC3C,EAAA,CA7FIP,sBAAsB;EAAA,QAGgBV,cAAc,EAIrCD,cAAc,EAElBhB,OAAO,EAEQJ,eAAe,EAGvBqB,cAAc;AAAA;AAAAiE,EAAA,GAdhCvD,sBAAsB;AA+F5B,eAAeA,sBAAsB;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}