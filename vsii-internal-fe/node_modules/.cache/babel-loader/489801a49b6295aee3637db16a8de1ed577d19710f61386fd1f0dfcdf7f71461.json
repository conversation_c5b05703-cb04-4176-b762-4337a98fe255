{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/AddOrEditBiddingReport.tsx\";\n// Third party\nimport { FormattedMessage } from 'react-intl';\n\n// yup\nimport { yupResolver } from '@hookform/resolvers/yup';\n\n// material-ui\nimport { Button, DialogActions, Grid, Stack } from '@mui/material';\nimport { LoadingButton } from '@mui/lab';\n\n// project import\nimport { addOrEditBiddingReportFormDefault, addOrEditBiddingReportSchema } from 'pages/sales/Config';\nimport { FormProvider, Input, NumericFormatCustom } from 'components/extended/Form';\nimport { StatusBiddingReport } from 'containers/search';\nimport Modal from 'components/extended/Modal';\nimport { gridSpacing } from 'store/constant';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AddOrEditBiddingReport = props => {\n  const {\n    open,\n    handleClose,\n    data,\n    isEdit,\n    loading,\n    hanldeAdd,\n    hanldeEdit\n  } = props;\n  const {\n    salesReport\n  } = TEXT_CONFIG_SCREEN;\n  const handleSubmit = values => {\n    const idHexString = data.idHexString;\n    if (isEdit) {\n      hanldeEdit(values, idHexString);\n    } else {\n      hanldeAdd(values);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    isOpen: open,\n    title: isEdit ? salesReport.monitorBiddingPackages + 'report-edit-bidding-packages' : salesReport.monitorBiddingPackages + 'report-add-bidding-packages',\n    onClose: handleClose,\n    keepMounted: false,\n    children: /*#__PURE__*/_jsxDEV(FormProvider, {\n      form: {\n        defaultValues: addOrEditBiddingReportFormDefault,\n        resolver: yupResolver(addOrEditBiddingReportSchema)\n      },\n      onSubmit: handleSubmit,\n      formReset: data,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: gridSpacing,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            required: true,\n            name: \"type\",\n            label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: salesReport.monitorBiddingPackages + 'report-type'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 60\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            disabled: true,\n            name: \"numberKHLCNT\",\n            label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: salesReport.monitorBiddingPackages + 'report-KHLCNT-number'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 36\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            required: true,\n            name: \"biddingPackagesName\",\n            label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: salesReport.monitorBiddingPackages + 'report-bidding-package-name'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 36\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            textFieldProps: {\n              InputProps: {\n                inputComponent: NumericFormatCustom\n              }\n            },\n            disabled: true,\n            name: \"estimatedCost\",\n            label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: salesReport.monitorBiddingPackages + 'report-budget'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 36\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            disabled: true,\n            name: \"datePosting\",\n            label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: salesReport.monitorBiddingPackages + 'report-date-posting'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 36\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            disabled: true,\n            name: \"timeBiddingClosing\",\n            label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: salesReport.monitorBiddingPackages + 'report-time-bidding-closing'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 36\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            textFieldProps: {\n              InputProps: {\n                inputComponent: NumericFormatCustom\n              }\n            },\n            name: \"bidPrice\",\n            label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: salesReport.monitorBiddingPackages + 'report-bid-price'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 36\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            disabled: true,\n            name: \"formBiddingParticipation\",\n            label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: salesReport.monitorBiddingPackages + 'report-form-bidding-participation'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 36\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            name: \"numberTBMT\",\n            label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: salesReport.monitorBiddingPackages + 'report-TBMT-number'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 36\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            disabled: true,\n            name: \"group\",\n            label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: salesReport.monitorBiddingPackages + 'report-group'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 36\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            required: true,\n            name: \"company\",\n            label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: salesReport.monitorBiddingPackages + 'report-company'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 36\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            disabled: true,\n            name: \"address\",\n            label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: salesReport.monitorBiddingPackages + 'report-address'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 36\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            disabled: true,\n            name: \"keyword\",\n            label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: salesReport.monitorBiddingPackages + 'report-keyword'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 36\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          children: /*#__PURE__*/_jsxDEV(StatusBiddingReport, {\n            select: true,\n            label: salesReport.monitorBiddingPackages + 'report-status'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            textFieldProps: {\n              multiline: true,\n              rows: 4\n            },\n            name: \"comment\",\n            label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: salesReport.monitorBiddingPackages + 'report-comment'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 36\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(DialogActions, {\n            children: /*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              spacing: 1,\n              justifyContent: \"flex-end\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                color: \"error\",\n                onClick: handleClose,\n                children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n                  id: salesReport.monitorBiddingPackages + 'report-cancel'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(LoadingButton, {\n                loading: loading,\n                variant: \"contained\",\n                type: \"submit\",\n                children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n                  id: salesReport.monitorBiddingPackages + 'report-submit'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 9\n  }, this);\n};\n_c = AddOrEditBiddingReport;\nexport default AddOrEditBiddingReport;\nvar _c;\n$RefreshReg$(_c, \"AddOrEditBiddingReport\");", "map": {"version": 3, "names": ["FormattedMessage", "yupResolver", "<PERSON><PERSON>", "DialogActions", "Grid", "<PERSON><PERSON>", "LoadingButton", "addOrEditBiddingReportFormDefault", "addOrEditBiddingReportSchema", "FormProvider", "Input", "NumericFormatCustom", "StatusBiddingReport", "Modal", "gridSpacing", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "AddOrEditBiddingReport", "props", "open", "handleClose", "data", "isEdit", "loading", "han<PERSON><PERSON><PERSON>", "han<PERSON><PERSON><PERSON>", "salesReport", "handleSubmit", "values", "idHexString", "isOpen", "title", "monitorBiddingPackages", "onClose", "keepMounted", "children", "form", "defaultValues", "resolver", "onSubmit", "formReset", "container", "spacing", "item", "xs", "required", "name", "label", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "textFieldProps", "InputProps", "inputComponent", "select", "multiline", "rows", "direction", "justifyContent", "color", "onClick", "variant", "type", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/AddOrEditBiddingReport.tsx"], "sourcesContent": ["// Third party\r\nimport { FormattedMessage } from 'react-intl';\r\n\r\n// yup\r\nimport { yupResolver } from '@hookform/resolvers/yup';\r\n\r\n// material-ui\r\nimport { Button, DialogActions, Grid, Stack } from '@mui/material';\r\nimport { LoadingButton } from '@mui/lab';\r\n\r\n// project import\r\nimport { addOrEditBiddingReportFormDefault, addOrEditBiddingReportSchema } from 'pages/sales/Config';\r\nimport { FormProvider, Input, NumericFormatCustom } from 'components/extended/Form';\r\nimport { StatusBiddingReport } from 'containers/search';\r\nimport Modal from 'components/extended/Modal';\r\nimport { gridSpacing } from 'store/constant';\r\nimport { IBiddingReport } from 'types';\r\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\r\n\r\ninterface IAddOrEditBiddingReportProps {\r\n    open: boolean;\r\n    handleClose: () => void;\r\n    loading?: boolean;\r\n    isEdit: boolean;\r\n    data: IBiddingReport;\r\n    hanldeAdd: (value: IBiddingReport) => void;\r\n    hanldeEdit: (value: IBiddingReport, idHexString: string) => void;\r\n}\r\n\r\nconst AddOrEditBiddingReport = (props: IAddOrEditBiddingReportProps) => {\r\n    const { open, handleClose, data, isEdit, loading, hanldeAdd, hanldeEdit } = props;\r\n\r\n    const { salesReport } = TEXT_CONFIG_SCREEN;\r\n\r\n    const handleSubmit = (values: IBiddingReport) => {\r\n        const idHexString = data.idHexString;\r\n        if (isEdit) {\r\n            hanldeEdit(values, idHexString!);\r\n        } else {\r\n            hanldeAdd(values);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <Modal\r\n            isOpen={open}\r\n            title={\r\n                isEdit\r\n                    ? salesReport.monitorBiddingPackages + 'report-edit-bidding-packages'\r\n                    : salesReport.monitorBiddingPackages + 'report-add-bidding-packages'\r\n            }\r\n            onClose={handleClose}\r\n            keepMounted={false}\r\n        >\r\n            <FormProvider\r\n                form={{ defaultValues: addOrEditBiddingReportFormDefault, resolver: yupResolver(addOrEditBiddingReportSchema) }}\r\n                onSubmit={handleSubmit}\r\n                formReset={data}\r\n            >\r\n                <Grid container spacing={gridSpacing}>\r\n                    <Grid item xs={6}>\r\n                        <Input required name=\"type\" label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-type'} />} />\r\n                    </Grid>\r\n                    <Grid item xs={6}>\r\n                        <Input\r\n                            disabled\r\n                            name=\"numberKHLCNT\"\r\n                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-KHLCNT-number'} />}\r\n                        />\r\n                    </Grid>\r\n                    <Grid item xs={6}>\r\n                        <Input\r\n                            required\r\n                            name=\"biddingPackagesName\"\r\n                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-bidding-package-name'} />}\r\n                        />\r\n                    </Grid>\r\n                    <Grid item xs={6}>\r\n                        <Input\r\n                            textFieldProps={{\r\n                                InputProps: {\r\n                                    inputComponent: NumericFormatCustom as any\r\n                                }\r\n                            }}\r\n                            disabled\r\n                            name=\"estimatedCost\"\r\n                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-budget'} />}\r\n                        />\r\n                    </Grid>\r\n                    <Grid item xs={6}>\r\n                        <Input\r\n                            disabled\r\n                            name=\"datePosting\"\r\n                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-date-posting'} />}\r\n                        />\r\n                    </Grid>\r\n                    <Grid item xs={6}>\r\n                        <Input\r\n                            disabled\r\n                            name=\"timeBiddingClosing\"\r\n                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-time-bidding-closing'} />}\r\n                        />\r\n                    </Grid>\r\n                    <Grid item xs={6}>\r\n                        <Input\r\n                            textFieldProps={{\r\n                                InputProps: {\r\n                                    inputComponent: NumericFormatCustom as any\r\n                                }\r\n                            }}\r\n                            name=\"bidPrice\"\r\n                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-bid-price'} />}\r\n                        />\r\n                    </Grid>\r\n                    <Grid item xs={6}>\r\n                        <Input\r\n                            disabled\r\n                            name=\"formBiddingParticipation\"\r\n                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-form-bidding-participation'} />}\r\n                        />\r\n                    </Grid>\r\n                    <Grid item xs={6}>\r\n                        <Input\r\n                            name=\"numberTBMT\"\r\n                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-TBMT-number'} />}\r\n                        />\r\n                    </Grid>\r\n                    <Grid item xs={6}>\r\n                        <Input\r\n                            disabled\r\n                            name=\"group\"\r\n                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-group'} />}\r\n                        />\r\n                    </Grid>\r\n                    <Grid item xs={6}>\r\n                        <Input\r\n                            required\r\n                            name=\"company\"\r\n                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-company'} />}\r\n                        />\r\n                    </Grid>\r\n                    <Grid item xs={6}>\r\n                        <Input\r\n                            disabled\r\n                            name=\"address\"\r\n                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-address'} />}\r\n                        />\r\n                    </Grid>\r\n                    <Grid item xs={6}>\r\n                        <Input\r\n                            disabled\r\n                            name=\"keyword\"\r\n                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-keyword'} />}\r\n                        />\r\n                    </Grid>\r\n                    <Grid item xs={6}>\r\n                        <StatusBiddingReport select label={salesReport.monitorBiddingPackages + 'report-status'} />\r\n                    </Grid>\r\n                    <Grid item xs={12}>\r\n                        <Input\r\n                            textFieldProps={{ multiline: true, rows: 4 }}\r\n                            name=\"comment\"\r\n                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-comment'} />}\r\n                        />\r\n                    </Grid>\r\n                    {/* not done */}\r\n                    <Grid item xs={12}>\r\n                        <DialogActions>\r\n                            <Stack direction=\"row\" spacing={1} justifyContent=\"flex-end\">\r\n                                <Button color=\"error\" onClick={handleClose}>\r\n                                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-cancel'} />\r\n                                </Button>\r\n                                <LoadingButton loading={loading} variant=\"contained\" type=\"submit\">\r\n                                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-submit'} />\r\n                                </LoadingButton>\r\n                            </Stack>\r\n                        </DialogActions>\r\n                    </Grid>\r\n                </Grid>\r\n            </FormProvider>\r\n        </Modal>\r\n    );\r\n};\r\n\r\nexport default AddOrEditBiddingReport;\r\n"], "mappings": ";AAAA;AACA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,WAAW,QAAQ,yBAAyB;;AAErD;AACA,SAASC,MAAM,EAAEC,aAAa,EAAEC,IAAI,EAAEC,KAAK,QAAQ,eAAe;AAClE,SAASC,aAAa,QAAQ,UAAU;;AAExC;AACA,SAASC,iCAAiC,EAAEC,4BAA4B,QAAQ,oBAAoB;AACpG,SAASC,YAAY,EAAEC,KAAK,EAAEC,mBAAmB,QAAQ,0BAA0B;AACnF,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,OAAOC,KAAK,MAAM,2BAA2B;AAC7C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAYtD,MAAMC,sBAAsB,GAAIC,KAAmC,IAAK;EACpE,MAAM;IAAEC,IAAI;IAAEC,WAAW;IAAEC,IAAI;IAAEC,MAAM;IAAEC,OAAO;IAAEC,SAAS;IAAEC;EAAW,CAAC,GAAGP,KAAK;EAEjF,MAAM;IAAEQ;EAAY,CAAC,GAAGZ,kBAAkB;EAE1C,MAAMa,YAAY,GAAIC,MAAsB,IAAK;IAC7C,MAAMC,WAAW,GAAGR,IAAI,CAACQ,WAAW;IACpC,IAAIP,MAAM,EAAE;MACRG,UAAU,CAACG,MAAM,EAAEC,WAAY,CAAC;IACpC,CAAC,MAAM;MACHL,SAAS,CAACI,MAAM,CAAC;IACrB;EACJ,CAAC;EAED,oBACIZ,OAAA,CAACJ,KAAK;IACFkB,MAAM,EAAEX,IAAK;IACbY,KAAK,EACDT,MAAM,GACAI,WAAW,CAACM,sBAAsB,GAAG,8BAA8B,GACnEN,WAAW,CAACM,sBAAsB,GAAG,6BAC9C;IACDC,OAAO,EAAEb,WAAY;IACrBc,WAAW,EAAE,KAAM;IAAAC,QAAA,eAEnBnB,OAAA,CAACR,YAAY;MACT4B,IAAI,EAAE;QAAEC,aAAa,EAAE/B,iCAAiC;QAAEgC,QAAQ,EAAEtC,WAAW,CAACO,4BAA4B;MAAE,CAAE;MAChHgC,QAAQ,EAAEZ,YAAa;MACvBa,SAAS,EAAEnB,IAAK;MAAAc,QAAA,eAEhBnB,OAAA,CAACb,IAAI;QAACsC,SAAS;QAACC,OAAO,EAAE7B,WAAY;QAAAsB,QAAA,gBACjCnB,OAAA,CAACb,IAAI;UAACwC,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACbnB,OAAA,CAACP,KAAK;YAACoC,QAAQ;YAACC,IAAI,EAAC,MAAM;YAACC,KAAK,eAAE/B,OAAA,CAACjB,gBAAgB;cAACiD,EAAE,EAAEtB,WAAW,CAACM,sBAAsB,GAAG;YAAc;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjH,CAAC,eACPpC,OAAA,CAACb,IAAI;UAACwC,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACbnB,OAAA,CAACP,KAAK;YACF4C,QAAQ;YACRP,IAAI,EAAC,cAAc;YACnBC,KAAK,eAAE/B,OAAA,CAACjB,gBAAgB;cAACiD,EAAE,EAAEtB,WAAW,CAACM,sBAAsB,GAAG;YAAuB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACPpC,OAAA,CAACb,IAAI;UAACwC,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACbnB,OAAA,CAACP,KAAK;YACFoC,QAAQ;YACRC,IAAI,EAAC,qBAAqB;YAC1BC,KAAK,eAAE/B,OAAA,CAACjB,gBAAgB;cAACiD,EAAE,EAAEtB,WAAW,CAACM,sBAAsB,GAAG;YAA8B;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACPpC,OAAA,CAACb,IAAI;UAACwC,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACbnB,OAAA,CAACP,KAAK;YACF6C,cAAc,EAAE;cACZC,UAAU,EAAE;gBACRC,cAAc,EAAE9C;cACpB;YACJ,CAAE;YACF2C,QAAQ;YACRP,IAAI,EAAC,eAAe;YACpBC,KAAK,eAAE/B,OAAA,CAACjB,gBAAgB;cAACiD,EAAE,EAAEtB,WAAW,CAACM,sBAAsB,GAAG;YAAgB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACPpC,OAAA,CAACb,IAAI;UAACwC,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACbnB,OAAA,CAACP,KAAK;YACF4C,QAAQ;YACRP,IAAI,EAAC,aAAa;YAClBC,KAAK,eAAE/B,OAAA,CAACjB,gBAAgB;cAACiD,EAAE,EAAEtB,WAAW,CAACM,sBAAsB,GAAG;YAAsB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACPpC,OAAA,CAACb,IAAI;UAACwC,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACbnB,OAAA,CAACP,KAAK;YACF4C,QAAQ;YACRP,IAAI,EAAC,oBAAoB;YACzBC,KAAK,eAAE/B,OAAA,CAACjB,gBAAgB;cAACiD,EAAE,EAAEtB,WAAW,CAACM,sBAAsB,GAAG;YAA8B;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACPpC,OAAA,CAACb,IAAI;UAACwC,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACbnB,OAAA,CAACP,KAAK;YACF6C,cAAc,EAAE;cACZC,UAAU,EAAE;gBACRC,cAAc,EAAE9C;cACpB;YACJ,CAAE;YACFoC,IAAI,EAAC,UAAU;YACfC,KAAK,eAAE/B,OAAA,CAACjB,gBAAgB;cAACiD,EAAE,EAAEtB,WAAW,CAACM,sBAAsB,GAAG;YAAmB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACPpC,OAAA,CAACb,IAAI;UAACwC,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACbnB,OAAA,CAACP,KAAK;YACF4C,QAAQ;YACRP,IAAI,EAAC,0BAA0B;YAC/BC,KAAK,eAAE/B,OAAA,CAACjB,gBAAgB;cAACiD,EAAE,EAAEtB,WAAW,CAACM,sBAAsB,GAAG;YAAoC;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7G;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACPpC,OAAA,CAACb,IAAI;UAACwC,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACbnB,OAAA,CAACP,KAAK;YACFqC,IAAI,EAAC,YAAY;YACjBC,KAAK,eAAE/B,OAAA,CAACjB,gBAAgB;cAACiD,EAAE,EAAEtB,WAAW,CAACM,sBAAsB,GAAG;YAAqB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACPpC,OAAA,CAACb,IAAI;UAACwC,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACbnB,OAAA,CAACP,KAAK;YACF4C,QAAQ;YACRP,IAAI,EAAC,OAAO;YACZC,KAAK,eAAE/B,OAAA,CAACjB,gBAAgB;cAACiD,EAAE,EAAEtB,WAAW,CAACM,sBAAsB,GAAG;YAAe;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACPpC,OAAA,CAACb,IAAI;UAACwC,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACbnB,OAAA,CAACP,KAAK;YACFoC,QAAQ;YACRC,IAAI,EAAC,SAAS;YACdC,KAAK,eAAE/B,OAAA,CAACjB,gBAAgB;cAACiD,EAAE,EAAEtB,WAAW,CAACM,sBAAsB,GAAG;YAAiB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACPpC,OAAA,CAACb,IAAI;UAACwC,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACbnB,OAAA,CAACP,KAAK;YACF4C,QAAQ;YACRP,IAAI,EAAC,SAAS;YACdC,KAAK,eAAE/B,OAAA,CAACjB,gBAAgB;cAACiD,EAAE,EAAEtB,WAAW,CAACM,sBAAsB,GAAG;YAAiB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACPpC,OAAA,CAACb,IAAI;UAACwC,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACbnB,OAAA,CAACP,KAAK;YACF4C,QAAQ;YACRP,IAAI,EAAC,SAAS;YACdC,KAAK,eAAE/B,OAAA,CAACjB,gBAAgB;cAACiD,EAAE,EAAEtB,WAAW,CAACM,sBAAsB,GAAG;YAAiB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACPpC,OAAA,CAACb,IAAI;UAACwC,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAT,QAAA,eACbnB,OAAA,CAACL,mBAAmB;YAAC8C,MAAM;YAACV,KAAK,EAAErB,WAAW,CAACM,sBAAsB,GAAG;UAAgB;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF,CAAC,eACPpC,OAAA,CAACb,IAAI;UAACwC,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAT,QAAA,eACdnB,OAAA,CAACP,KAAK;YACF6C,cAAc,EAAE;cAAEI,SAAS,EAAE,IAAI;cAAEC,IAAI,EAAE;YAAE,CAAE;YAC7Cb,IAAI,EAAC,SAAS;YACdC,KAAK,eAAE/B,OAAA,CAACjB,gBAAgB;cAACiD,EAAE,EAAEtB,WAAW,CAACM,sBAAsB,GAAG;YAAiB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEPpC,OAAA,CAACb,IAAI;UAACwC,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAT,QAAA,eACdnB,OAAA,CAACd,aAAa;YAAAiC,QAAA,eACVnB,OAAA,CAACZ,KAAK;cAACwD,SAAS,EAAC,KAAK;cAAClB,OAAO,EAAE,CAAE;cAACmB,cAAc,EAAC,UAAU;cAAA1B,QAAA,gBACxDnB,OAAA,CAACf,MAAM;gBAAC6D,KAAK,EAAC,OAAO;gBAACC,OAAO,EAAE3C,WAAY;gBAAAe,QAAA,eACvCnB,OAAA,CAACjB,gBAAgB;kBAACiD,EAAE,EAAEtB,WAAW,CAACM,sBAAsB,GAAG;gBAAgB;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC,eACTpC,OAAA,CAACX,aAAa;gBAACkB,OAAO,EAAEA,OAAQ;gBAACyC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,QAAQ;gBAAA9B,QAAA,eAC9DnB,OAAA,CAACjB,gBAAgB;kBAACiD,EAAE,EAAEtB,WAAW,CAACM,sBAAsB,GAAG;gBAAgB;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEhB,CAAC;AAACc,EAAA,GAzJIjD,sBAAsB;AA2J5B,eAAeA,sBAAsB;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}