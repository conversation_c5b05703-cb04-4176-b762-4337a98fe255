{"ast": null, "code": "import{Grid}from'@mui/material';import{Input,Select}from'components/extended/Form';import{TEXT_CONDITIONS}from'constants/Common';import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TextCondition=_ref=>{let{conditionName,valueName}=_ref;return/*#__PURE__*/_jsxs(Grid,{container:true,spacing:1,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:3,children:/*#__PURE__*/_jsx(Select,{name:conditionName,selects:TEXT_CONDITIONS})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:4.5,children:/*#__PURE__*/_jsx(Input,{name:valueName})})]});};export default TextCondition;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}