{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/EmailConfigThead.tsx\";\nimport { FormattedMessage } from 'react-intl';\n\n// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\n\n//project imports\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { PERMISSIONS } from 'constants/Permission';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EmailConfigThead = () => {\n  const {\n    emailConfigPermission\n  } = PERMISSIONS.admin;\n  const {\n    email_config\n  } = TEXT_CONFIG_SCREEN.administration;\n  return /*#__PURE__*/_jsxDEV(TableHead, {\n    children: /*#__PURE__*/_jsxDEV(TableRow, {\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: email_config + 'no'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: email_config + 'email-code'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: email_config + 'send-to'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: email_config + 'send-cc'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: email_config + 'send-bcc'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this), checkAllowedPermission(emailConfigPermission.edit) && /*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"center\",\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: email_config + 'action'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 9\n  }, this);\n};\n_c = EmailConfigThead;\nexport default EmailConfigThead;\nvar _c;\n$RefreshReg$(_c, \"EmailConfigThead\");", "map": {"version": 3, "names": ["FormattedMessage", "TableCell", "TableHead", "TableRow", "checkAllowedPermission", "PERMISSIONS", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "EmailConfigThead", "emailConfigPermission", "admin", "email_config", "administration", "children", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "edit", "align", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/EmailConfigThead.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\n\n// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\n\n//project imports\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { PERMISSIONS } from 'constants/Permission';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\n\nconst EmailConfigThead = () => {\n    const { emailConfigPermission } = PERMISSIONS.admin;\n\n    const { email_config } = TEXT_CONFIG_SCREEN.administration;\n\n    return (\n        <TableHead>\n            <TableRow>\n                <TableCell>\n                    <FormattedMessage id={email_config + 'no'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={email_config + 'email-code'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={email_config + 'send-to'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={email_config + 'send-cc'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={email_config + 'send-bcc'} />\n                </TableCell>\n                {checkAllowedPermission(emailConfigPermission.edit) && (\n                    <TableCell align=\"center\">\n                        <FormattedMessage id={email_config + 'action'} />\n                    </TableCell>\n                )}\n            </TableRow>\n        </TableHead>\n    );\n};\n\nexport default EmailConfigThead;\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;;AAE9D;AACA,SAASC,sBAAsB,QAAQ,qBAAqB;AAC5D,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAC3B,MAAM;IAAEC;EAAsB,CAAC,GAAGL,WAAW,CAACM,KAAK;EAEnD,MAAM;IAAEC;EAAa,CAAC,GAAGN,kBAAkB,CAACO,cAAc;EAE1D,oBACIL,OAAA,CAACN,SAAS;IAAAY,QAAA,eACNN,OAAA,CAACL,QAAQ;MAAAW,QAAA,gBACLN,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,YAAY,GAAG;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACZX,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,YAAY,GAAG;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eACZX,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,YAAY,GAAG;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACZX,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,YAAY,GAAG;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACZX,OAAA,CAACP,SAAS;QAAAa,QAAA,eACNN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,YAAY,GAAG;QAAW;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,EACXf,sBAAsB,CAACM,qBAAqB,CAACU,IAAI,CAAC,iBAC/CZ,OAAA,CAACP,SAAS;QAACoB,KAAK,EAAC,QAAQ;QAAAP,QAAA,eACrBN,OAAA,CAACR,gBAAgB;UAACe,EAAE,EAAEH,YAAY,GAAG;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CACd;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEpB,CAAC;AAACG,EAAA,GA/BIb,gBAAgB;AAiCtB,eAAeA,gBAAgB;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}