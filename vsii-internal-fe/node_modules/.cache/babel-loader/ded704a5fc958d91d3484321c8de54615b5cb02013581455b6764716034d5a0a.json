{"ast": null, "code": "import { IsSanctionedSimpleUnitIdentifier } from './IsSanctionedSimpleUnitIdentifier';\n/**\n * This follows https://tc39.es/ecma402/#sec-case-sensitivity-and-case-mapping\n * @param str string to convert\n */\nfunction toLowerCase(str) {\n  return str.replace(/([A-Z])/g, function (_, c) {\n    return c.toLowerCase();\n  });\n}\n/**\n * https://tc39.es/ecma402/#sec-iswellformedunitidentifier\n * @param unit\n */\nexport function IsWellFormedUnitIdentifier(unit) {\n  unit = toLowerCase(unit);\n  if (IsSanctionedSimpleUnitIdentifier(unit)) {\n    return true;\n  }\n  var units = unit.split('-per-');\n  if (units.length !== 2) {\n    return false;\n  }\n  var numerator = units[0],\n    denominator = units[1];\n  if (!IsSanctionedSimpleUnitIdentifier(numerator) || !IsSanctionedSimpleUnitIdentifier(denominator)) {\n    return false;\n  }\n  return true;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}