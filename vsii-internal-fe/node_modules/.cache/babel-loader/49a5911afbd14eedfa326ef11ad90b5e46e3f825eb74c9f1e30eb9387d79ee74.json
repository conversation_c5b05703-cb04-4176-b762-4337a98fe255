{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"color\", \"component\", \"disabled\", \"disableFocusRipple\", \"focusVisibleClassName\", \"size\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport ButtonBase from '../ButtonBase';\nimport capitalize from '../utils/capitalize';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport fabClasses, { getFabUtilityClass } from './fabClasses';\nimport styled, { rootShouldForwardProp } from '../styles/styled';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    variant,\n    classes,\n    size\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, `size${capitalize(size)}`, color === 'inherit' ? 'colorInherit' : color]\n  };\n  const composedClasses = composeClasses(slots, getFabUtilityClass, classes);\n  return _extends({}, classes, composedClasses);\n};\nconst FabRoot = styled(ButtonBase, {\n  name: 'MuiFab',\n  slot: 'Root',\n  shouldForwardProp: prop => rootShouldForwardProp(prop) || prop === 'classes',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], styles[`size${capitalize(ownerState.size)}`], ownerState.color === 'inherit' && styles.colorInherit, styles[capitalize(ownerState.size)], styles[ownerState.color]];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  var _theme$palette$getCon, _theme$palette;\n  return _extends({}, theme.typography.button, {\n    minHeight: 36,\n    transition: theme.transitions.create(['background-color', 'box-shadow', 'border-color'], {\n      duration: theme.transitions.duration.short\n    }),\n    borderRadius: '50%',\n    padding: 0,\n    minWidth: 0,\n    width: 56,\n    height: 56,\n    zIndex: (theme.vars || theme).zIndex.fab,\n    boxShadow: (theme.vars || theme).shadows[6],\n    '&:active': {\n      boxShadow: (theme.vars || theme).shadows[12]\n    },\n    color: theme.vars ? theme.vars.palette.text.primary : (_theme$palette$getCon = (_theme$palette = theme.palette).getContrastText) == null ? void 0 : _theme$palette$getCon.call(_theme$palette, theme.palette.grey[300]),\n    backgroundColor: (theme.vars || theme).palette.grey[300],\n    '&:hover': {\n      backgroundColor: (theme.vars || theme).palette.grey.A100,\n      // Reset on touch devices, it doesn't add specificity\n      '@media (hover: none)': {\n        backgroundColor: (theme.vars || theme).palette.grey[300]\n      },\n      textDecoration: 'none'\n    },\n    [`&.${fabClasses.focusVisible}`]: {\n      boxShadow: (theme.vars || theme).shadows[6]\n    }\n  }, ownerState.size === 'small' && {\n    width: 40,\n    height: 40\n  }, ownerState.size === 'medium' && {\n    width: 48,\n    height: 48\n  }, ownerState.variant === 'extended' && {\n    borderRadius: 48 / 2,\n    padding: '0 16px',\n    width: 'auto',\n    minHeight: 'auto',\n    minWidth: 48,\n    height: 48\n  }, ownerState.variant === 'extended' && ownerState.size === 'small' && {\n    width: 'auto',\n    padding: '0 8px',\n    borderRadius: 34 / 2,\n    minWidth: 34,\n    height: 34\n  }, ownerState.variant === 'extended' && ownerState.size === 'medium' && {\n    width: 'auto',\n    padding: '0 16px',\n    borderRadius: 40 / 2,\n    minWidth: 40,\n    height: 40\n  }, ownerState.color === 'inherit' && {\n    color: 'inherit'\n  });\n}, ({\n  theme,\n  ownerState\n}) => _extends({}, ownerState.color !== 'inherit' && ownerState.color !== 'default' && (theme.vars || theme).palette[ownerState.color] != null && {\n  color: (theme.vars || theme).palette[ownerState.color].contrastText,\n  backgroundColor: (theme.vars || theme).palette[ownerState.color].main,\n  '&:hover': {\n    backgroundColor: (theme.vars || theme).palette[ownerState.color].dark,\n    // Reset on touch devices, it doesn't add specificity\n    '@media (hover: none)': {\n      backgroundColor: (theme.vars || theme).palette[ownerState.color].main\n    }\n  }\n}), ({\n  theme\n}) => ({\n  [`&.${fabClasses.disabled}`]: {\n    color: (theme.vars || theme).palette.action.disabled,\n    boxShadow: (theme.vars || theme).shadows[0],\n    backgroundColor: (theme.vars || theme).palette.action.disabledBackground\n  }\n}));\nconst Fab = /*#__PURE__*/React.forwardRef(function Fab(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiFab'\n  });\n  const {\n      children,\n      className,\n      color = 'default',\n      component = 'button',\n      disabled = false,\n      disableFocusRipple = false,\n      focusVisibleClassName,\n      size = 'large',\n      variant = 'circular'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    color,\n    component,\n    disabled,\n    disableFocusRipple,\n    size,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(FabRoot, _extends({\n    className: clsx(classes.root, className),\n    component: component,\n    disabled: disabled,\n    focusRipple: !disableFocusRipple,\n    focusVisibleClassName: clsx(classes.focusVisible, focusVisibleClassName),\n    ownerState: ownerState,\n    ref: ref\n  }, other, {\n    classes: classes,\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Fab.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'default'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'error', 'info', 'inherit', 'primary', 'secondary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the  keyboard focus ripple is disabled.\n   * @default false\n   */\n  disableFocusRipple: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  focusVisibleClassName: PropTypes.string,\n  /**\n   * The URL to link to when the button is clicked.\n   * If defined, an `a` element will be used as the root node.\n   */\n  href: PropTypes.string,\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense button styling.\n   * @default 'large'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium', 'large']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'circular'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['circular', 'extended']), PropTypes.string])\n} : void 0;\nexport default Fab;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}