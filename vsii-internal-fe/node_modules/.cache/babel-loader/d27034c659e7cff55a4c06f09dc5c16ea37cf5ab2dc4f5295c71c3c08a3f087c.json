{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/BiddingHCThead.tsx\",\n  _s = $RefreshSig$();\nimport { Fragment } from 'react';\n\n// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\n\n// react-hook-form\nimport { useFormContext } from 'react-hook-form';\n\n// project imports\nimport { HC_ASPL_MONTHS_OF_YEAR } from 'constants/Common';\n\n// third party\nimport { FormattedMessage, useIntl } from 'react-intl';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BiddingHCThead = props => {\n  _s();\n  const {\n    monthlyHCList\n  } = props;\n  const intl = useIntl();\n  const {\n    watch\n  } = useFormContext();\n  const contractDurationFrom = watch('project.contractDurationFrom');\n  const contractDurationTo = watch('project.contractDurationTo');\n  const {\n    salesReport\n  } = TEXT_CONFIG_SCREEN;\n  return /*#__PURE__*/_jsxDEV(TableHead, {\n    children: /*#__PURE__*/_jsxDEV(TableRow, {\n      sx: {\n        '& th': {\n          whiteSpace: 'nowrap'\n        }\n      },\n      children: [contractDurationFrom && contractDurationTo ? /*#__PURE__*/_jsxDEV(TableCell, {\n        sx: {\n          position: 'sticky',\n          left: 0,\n          backgroundColor: 'white'\n        },\n        align: \"center\",\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: \"action\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false), monthlyHCList === null || monthlyHCList === void 0 ? void 0 : monthlyHCList.map((item, key) => /*#__PURE__*/_jsxDEV(Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(TableCell, {\n          children: `${intl.formatMessage({\n            id: HC_ASPL_MONTHS_OF_YEAR[item === null || item === void 0 ? void 0 : item.month]\n          })} - ${item.year}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: salesReport.allSalesPineline + '-billable-day'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: salesReport.allSalesPineline + '-billable'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 25\n        }, this)]\n      }, key, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 21\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 9\n  }, this);\n};\n_s(BiddingHCThead, \"e1hPE2dC7itbp4V8RMkPiC50u1Y=\", false, function () {\n  return [useIntl, useFormContext];\n});\n_c = BiddingHCThead;\nexport default BiddingHCThead;\nvar _c;\n$RefreshReg$(_c, \"BiddingHCThead\");", "map": {"version": 3, "names": ["Fragment", "TableCell", "TableHead", "TableRow", "useFormContext", "HC_ASPL_MONTHS_OF_YEAR", "FormattedMessage", "useIntl", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "_Fragment", "BiddingHCThead", "props", "_s", "monthlyHCList", "intl", "watch", "contractDurationFrom", "contractDurationTo", "salesReport", "children", "sx", "whiteSpace", "position", "left", "backgroundColor", "align", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "key", "formatMessage", "month", "year", "allSalesPineline", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/BiddingHCThead.tsx"], "sourcesContent": ["import { Fragment } from 'react';\n\n// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\n\n// react-hook-form\nimport { useFormContext } from 'react-hook-form';\n\n// project imports\nimport { HC_ASPL_MONTHS_OF_YEAR } from 'constants/Common';\n\n// third party\nimport { FormattedMessage, useIntl } from 'react-intl';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\n\ninterface IBiddingHCTheadProps {\n    monthlyHCList?: any;\n}\n\nconst BiddingHCThead = (props: IBiddingHCTheadProps) => {\n    const { monthlyHCList } = props;\n    const intl = useIntl();\n    const { watch } = useFormContext();\n    const contractDurationFrom = watch('project.contractDurationFrom');\n    const contractDurationTo = watch('project.contractDurationTo');\n\n    const { salesReport } = TEXT_CONFIG_SCREEN;\n\n    return (\n        <TableHead>\n            <TableRow\n                sx={{\n                    '& th': {\n                        whiteSpace: 'nowrap'\n                    }\n                }}\n            >\n                {contractDurationFrom && contractDurationTo ? (\n                    <TableCell sx={{ position: 'sticky', left: 0, backgroundColor: 'white' }} align=\"center\">\n                        <FormattedMessage id=\"action\" />\n                    </TableCell>\n                ) : (\n                    <></>\n                )}\n                {monthlyHCList?.map((item: any, key: number) => (\n                    <Fragment key={key}>\n                        <TableCell>{`${intl.formatMessage({ id: HC_ASPL_MONTHS_OF_YEAR[item?.month] })} - ${item.year}`}</TableCell>\n                        <TableCell>\n                            <FormattedMessage id={salesReport.allSalesPineline + '-billable-day'} />\n                        </TableCell>\n                        <TableCell>\n                            <FormattedMessage id={salesReport.allSalesPineline + '-billable'} />\n                        </TableCell>\n                    </Fragment>\n                ))}\n            </TableRow>\n        </TableHead>\n    );\n};\n\nexport default BiddingHCThead;\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;;AAEhC;AACA,SAASC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;;AAE9D;AACA,SAASC,cAAc,QAAQ,iBAAiB;;AAEhD;AACA,SAASC,sBAAsB,QAAQ,kBAAkB;;AAEzD;AACA,SAASC,gBAAgB,EAAEC,OAAO,QAAQ,YAAY;AACtD,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAV,QAAA,IAAAW,SAAA;AAMtD,MAAMC,cAAc,GAAIC,KAA2B,IAAK;EAAAC,EAAA;EACpD,MAAM;IAAEC;EAAc,CAAC,GAAGF,KAAK;EAC/B,MAAMG,IAAI,GAAGT,OAAO,CAAC,CAAC;EACtB,MAAM;IAAEU;EAAM,CAAC,GAAGb,cAAc,CAAC,CAAC;EAClC,MAAMc,oBAAoB,GAAGD,KAAK,CAAC,8BAA8B,CAAC;EAClE,MAAME,kBAAkB,GAAGF,KAAK,CAAC,4BAA4B,CAAC;EAE9D,MAAM;IAAEG;EAAY,CAAC,GAAGZ,kBAAkB;EAE1C,oBACIE,OAAA,CAACR,SAAS;IAAAmB,QAAA,eACNX,OAAA,CAACP,QAAQ;MACLmB,EAAE,EAAE;QACA,MAAM,EAAE;UACJC,UAAU,EAAE;QAChB;MACJ,CAAE;MAAAF,QAAA,GAEDH,oBAAoB,IAAIC,kBAAkB,gBACvCT,OAAA,CAACT,SAAS;QAACqB,EAAE,EAAE;UAAEE,QAAQ,EAAE,QAAQ;UAAEC,IAAI,EAAE,CAAC;UAAEC,eAAe,EAAE;QAAQ,CAAE;QAACC,KAAK,EAAC,QAAQ;QAAAN,QAAA,eACpFX,OAAA,CAACJ,gBAAgB;UAACsB,EAAE,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,gBAEZtB,OAAA,CAAAC,SAAA,mBAAI,CACP,EACAI,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEkB,GAAG,CAAC,CAACC,IAAS,EAAEC,GAAW,kBACvCzB,OAAA,CAACV,QAAQ;QAAAqB,QAAA,gBACLX,OAAA,CAACT,SAAS;UAAAoB,QAAA,EAAE,GAAGL,IAAI,CAACoB,aAAa,CAAC;YAAER,EAAE,EAAEvB,sBAAsB,CAAC6B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,KAAK;UAAE,CAAC,CAAC,MAAMH,IAAI,CAACI,IAAI;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC5GtB,OAAA,CAACT,SAAS;UAAAoB,QAAA,eACNX,OAAA,CAACJ,gBAAgB;YAACsB,EAAE,EAAER,WAAW,CAACmB,gBAAgB,GAAG;UAAgB;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eACZtB,OAAA,CAACT,SAAS;UAAAoB,QAAA,eACNX,OAAA,CAACJ,gBAAgB;YAACsB,EAAE,EAAER,WAAW,CAACmB,gBAAgB,GAAG;UAAY;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA,GAPDG,GAAG;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQR,CACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEpB,CAAC;AAAClB,EAAA,CAvCIF,cAAc;EAAA,QAEHL,OAAO,EACFH,cAAc;AAAA;AAAAoC,EAAA,GAH9B5B,cAAc;AAyCpB,eAAeA,cAAc;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}