{"ast": null, "code": "import{useEffect}from'react';import{<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>ack,Typography}from'@mui/material';import{yupResolver}from'@hookform/resolvers/yup';import{useNavigate}from'react-router-dom';import{LoadingButton}from'@mui/lab';import{authSelector,forgotPW,resetAuthState}from'store/slice/authSlice';import{forgotConfig,forgotSchema}from'pages/Config';import{FormProvider,Input}from'components/extended/Form';import{useAppDispatch,useAppSelector}from'app/hooks';import{openSnackbar}from'store/slice/snackbarSlice';import{ROUTER}from'constants/Routers';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const AuthForgot=()=>{const{forgotPWSuccessfully,loading}=useAppSelector(authSelector);const dispatch=useAppDispatch();const navigate=useNavigate();const handleSubmit=async value=>{const resultAction=await dispatch(forgotPW(value));if(forgotPW.fulfilled.match(resultAction)){var _resultAction$payload;if((_resultAction$payload=resultAction.payload)!==null&&_resultAction$payload!==void 0&&_resultAction$payload.status){var _resultAction$payload2,_resultAction$payload3;dispatch(openSnackbar({open:true,message:(_resultAction$payload2=resultAction.payload)===null||_resultAction$payload2===void 0?void 0:(_resultAction$payload3=_resultAction$payload2.result)===null||_resultAction$payload3===void 0?void 0:_resultAction$payload3.content,variant:'alert',alert:{color:'success'}}));}else{var _resultAction$payload4,_resultAction$payload5,_resultAction$payload6;dispatch(openSnackbar({open:true,message:(_resultAction$payload4=resultAction.payload)===null||_resultAction$payload4===void 0?void 0:(_resultAction$payload5=_resultAction$payload4.result)===null||_resultAction$payload5===void 0?void 0:(_resultAction$payload6=_resultAction$payload5.content)===null||_resultAction$payload6===void 0?void 0:_resultAction$payload6.message,variant:'alert',alert:{color:'error'}}));}}};useEffect(()=>{return()=>{dispatch(resetAuthState());};},[dispatch]);return/*#__PURE__*/_jsxs(FormProvider,{form:{defaultValues:forgotConfig,resolver:yupResolver(forgotSchema)},onSubmit:handleSubmit,children:[!forgotPWSuccessfully?/*#__PURE__*/_jsxs(Grid,{container:true,gap:1,direction:\"column\",children:[/*#__PURE__*/_jsx(Typography,{textAlign:\"center\",children:\"Please enter your email address to search for your account.\"}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(Input,{name:\"email\",label:\"Email address\",required:true})]}):/*#__PURE__*/_jsxs(Grid,{container:true,gap:1,direction:\"column\",children:[/*#__PURE__*/_jsx(Typography,{children:\"Please check your email!\"}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsxs(Typography,{children:[\"Click the following \",/*#__PURE__*/_jsx(Link,{href:window.location.origin,children:\"link\"}),\" to login to InstantView:\"]})]}),/*#__PURE__*/_jsx(Stack,{direction:\"column\",alignItems:\"center\",sx:{mt:!forgotPWSuccessfully?8:3},children:!forgotPWSuccessfully?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(LoadingButton,{variant:\"contained\",type:\"submit\",sx:{width:'200px'},loading:loading[forgotPW.typePrefix],children:\"Confirm\"}),/*#__PURE__*/_jsx(Button,{variant:\"text\",onClick:()=>navigate(\"/\".concat(ROUTER.authentication.login)),sx:{mt:3,textDecoration:'none','&:hover':{textDecoration:'underline',background:'none'}},children:\"Back to Login\"})]}):/*#__PURE__*/_jsx(Button,{variant:\"contained\",sx:{width:'150px'},onClick:()=>navigate(\"/\".concat(ROUTER.authentication.login)),children:\"Back to Login\"})})]});};export default AuthForgot;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}