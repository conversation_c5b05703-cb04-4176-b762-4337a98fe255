{"ast": null, "code": "import { createElement } from 'react';\nimport { useHTMLProps } from '../html/use-props.mjs';\nimport { filterProps } from './utils/filter-props.mjs';\nimport { isSVGComponent } from './utils/is-svg-component.mjs';\nimport { useSVGProps } from '../svg/use-props.mjs';\nfunction createUseRender(forwardMotionProps = false) {\n  const useRender = (Component, props, projectionId, ref, {\n    latestValues\n  }, isStatic) => {\n    const useVisualProps = isSVGComponent(Component) ? useSVGProps : useHTMLProps;\n    const visualProps = useVisualProps(props, latestValues, isStatic, Component);\n    const filteredProps = filterProps(props, typeof Component === \"string\", forwardMotionProps);\n    const elementProps = {\n      ...filteredProps,\n      ...visualProps,\n      ref\n    };\n    if (projectionId) {\n      elementProps[\"data-projection-id\"] = projectionId;\n    }\n    return createElement(Component, elementProps);\n  };\n  return useRender;\n}\nexport { createUseRender };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}