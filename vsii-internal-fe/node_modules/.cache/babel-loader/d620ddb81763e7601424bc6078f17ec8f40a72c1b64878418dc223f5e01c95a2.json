{"ast": null, "code": "import { transformPropOrder } from './transform.mjs';\nconst translateAlias = {\n  x: \"translateX\",\n  y: \"translateY\",\n  z: \"translateZ\",\n  transformPerspective: \"perspective\"\n};\n/**\n * A function to use with Array.sort to sort transform keys by their default order.\n */\nconst sortTransformProps = (a, b) => transformPropOrder.indexOf(a) - transformPropOrder.indexOf(b);\n/**\n * Build a CSS transform style from individual x/y/scale etc properties.\n *\n * This outputs with a default order of transforms/scales/rotations, this can be customised by\n * providing a transformTemplate function.\n */\nfunction buildTransform({\n  transform,\n  transformKeys\n}, {\n  enableHardwareAcceleration = true,\n  allowTransformNone = true\n}, transformIsDefault, transformTemplate) {\n  // The transform string we're going to build into.\n  let transformString = \"\";\n  // Transform keys into their default order - this will determine the output order.\n  transformKeys.sort(sortTransformProps);\n  // Loop over each transform and build them into transformString\n  for (const key of transformKeys) {\n    transformString += `${translateAlias[key] || key}(${transform[key]}) `;\n  }\n  if (enableHardwareAcceleration && !transform.z) {\n    transformString += \"translateZ(0)\";\n  }\n  transformString = transformString.trim();\n  // If we have a custom `transform` template, pass our transform values and\n  // generated transformString to that before returning\n  if (transformTemplate) {\n    transformString = transformTemplate(transform, transformIsDefault ? \"\" : transformString);\n  } else if (allowTransformNone && transformIsDefault) {\n    transformString = \"none\";\n  }\n  return transformString;\n}\nexport { buildTransform };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}