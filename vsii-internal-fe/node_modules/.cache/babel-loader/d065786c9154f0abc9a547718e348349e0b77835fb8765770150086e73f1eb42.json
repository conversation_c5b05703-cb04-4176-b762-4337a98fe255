{"ast": null, "code": "import { createStore } from 'redux';\nimport { DragDropManagerImpl } from './classes/DragDropManagerImpl.js';\nimport { DragDropMonitorImpl } from './classes/DragDropMonitorImpl.js';\nimport { HandlerRegistryImpl } from './classes/HandlerRegistryImpl.js';\nimport { reduce } from './reducers/index.js';\nexport function createDragDropManager(backendFactory) {\n  let globalContext = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : undefined;\n  let backendOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  let debugMode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  const store = makeStoreInstance(debugMode);\n  const monitor = new DragDropMonitorImpl(store, new HandlerRegistryImpl(store));\n  const manager = new DragDropManagerImpl(store, monitor);\n  const backend = backendFactory(manager, globalContext, backendOptions);\n  manager.receiveBackend(backend);\n  return manager;\n}\nfunction makeStoreInstance(debugMode) {\n  // TODO: if we ever make a react-native version of this,\n  // we'll need to consider how to pull off dev-tooling\n  const reduxDevTools = typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION__;\n  return createStore(reduce, debugMode && reduxDevTools && reduxDevTools({\n    name: 'dnd-core',\n    instanceId: 'dnd-core'\n  }));\n}\n\n//# sourceMappingURL=createDragDropManager.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}