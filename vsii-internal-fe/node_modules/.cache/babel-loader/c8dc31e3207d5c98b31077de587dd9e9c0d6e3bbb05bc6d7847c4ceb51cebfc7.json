{"ast": null, "code": "import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';\nimport omit from 'lodash/omit';\nimport sendRequest from 'services/ApiService';\nimport Api from 'constants/Api';\nexport const getSearchDepartment = createAsyncThunk(Api.department.search.url, async params => {\n  const response = await sendRequest(Api.department.search, params);\n  return response;\n});\nexport const createDepartment = createAsyncThunk(Api.department.create.url, async params => {\n  const response = await sendRequest(Api.department.create, params);\n  return response;\n});\nexport const editDepartment = createAsyncThunk('Api.department.edit.url', async params => {\n  const response = await sendRequest(Api.department.edit(params.id), omit(params, ['id']));\n  return response;\n});\nexport const deleteDepartment = createAsyncThunk('Api.department.delete.url', async params => {\n  const response = await sendRequest(Api.department.delete(params));\n  return response;\n});\nconst initialState = {\n  loading: {}\n};\nconst departmnetSlice = createSlice({\n  name: 'department',\n  initialState: initialState,\n  reducers: {},\n  extraReducers: builder => {\n    builder.addCase(getSearchDepartment.pending, state => {\n      state.loading[getSearchDepartment.typePrefix] = true;\n    });\n    builder.addCase(getSearchDepartment.fulfilled, (state, action) => {\n      if (action.payload.status && Array.isArray(action.payload.result.content)) {\n        state.departments = action.payload.result;\n      }\n      state.loading[getSearchDepartment.typePrefix] = false;\n    });\n    builder.addCase(getSearchDepartment.rejected, state => {\n      state.loading[getSearchDepartment.typePrefix] = false;\n    });\n    builder.addCase(createDepartment.pending, state => {\n      state.loading[createDepartment.typePrefix] = true;\n    });\n    builder.addCase(createDepartment.fulfilled, state => {\n      state.loading[createDepartment.typePrefix] = false;\n    });\n    builder.addCase(createDepartment.rejected, state => {\n      state.loading[createDepartment.typePrefix] = false;\n    });\n    builder.addCase(editDepartment.pending, state => {\n      state.loading[editDepartment.typePrefix] = true;\n    });\n    builder.addCase(editDepartment.fulfilled, state => {\n      state.loading[editDepartment.typePrefix] = false;\n    });\n    builder.addCase(editDepartment.rejected, state => {\n      state.loading[editDepartment.typePrefix] = false;\n    });\n    builder.addCase(deleteDepartment.pending, state => {\n      state.loading[deleteDepartment.typePrefix] = true;\n    });\n    builder.addCase(deleteDepartment.fulfilled, state => {\n      state.loading[deleteDepartment.typePrefix] = false;\n    });\n    builder.addCase(deleteDepartment.rejected, state => {\n      state.loading[deleteDepartment.typePrefix] = false;\n    });\n  }\n});\nexport default departmnetSlice.reducer;\nexport const departmentSelector = state => state.department;", "map": {"version": 3, "names": ["createAsyncThunk", "createSlice", "omit", "sendRequest", "Api", "getSearchDepartment", "department", "search", "url", "params", "response", "createDepartment", "create", "editDepartment", "edit", "id", "deleteDepartment", "delete", "initialState", "loading", "departmnetSlice", "name", "reducers", "extraReducers", "builder", "addCase", "pending", "state", "typePrefix", "fulfilled", "action", "payload", "status", "Array", "isArray", "result", "content", "departments", "rejected", "reducer", "departmentSelector"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/store/slice/departmentSlice.ts"], "sourcesContent": ["import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';\nimport omit from 'lodash/omit';\n\nimport { ICreateDepartmentRequest, IEditDepartmentRequest, IGetDepartmentResponse } from 'types/department';\nimport { IDepartmentFilterConfig } from 'pages/administration/Config';\nimport { IResponseList, Response } from 'types';\nimport sendRequest from 'services/ApiService';\nimport { RootState } from 'app/store';\nimport Api from 'constants/Api';\n\nexport const getSearchDepartment = createAsyncThunk<IResponseList<IGetDepartmentResponse>, IDepartmentFilterConfig>(\n    Api.department.search.url,\n    async (params) => {\n        const response = await sendRequest(Api.department.search, params);\n\n        return response;\n    }\n);\n\nexport const createDepartment = createAsyncThunk<Response<{ content: string }>, ICreateDepartmentRequest>(\n    Api.department.create.url,\n    async (params) => {\n        const response = await sendRequest(Api.department.create, params);\n\n        return response;\n    }\n);\n\nexport const editDepartment = createAsyncThunk<Response<{ content: string }>, IEditDepartmentRequest>(\n    'Api.department.edit.url',\n    async (params) => {\n        const response = await sendRequest(Api.department.edit(params.id), omit(params, ['id']));\n\n        return response;\n    }\n);\n\nexport const deleteDepartment = createAsyncThunk<Response<{ content: string }>, string>('Api.department.delete.url', async (params) => {\n    const response = await sendRequest(Api.department.delete(params));\n\n    return response;\n});\n\ninterface IDepartmentState {\n    departments?: IResponseList<IGetDepartmentResponse>['result'];\n    loading: { [key: string]: boolean };\n}\n\nconst initialState: IDepartmentState = {\n    loading: {}\n};\n\nconst departmnetSlice = createSlice({\n    name: 'department',\n    initialState: initialState,\n    reducers: {},\n    extraReducers: (builder) => {\n        builder.addCase(getSearchDepartment.pending, (state) => {\n            state.loading[getSearchDepartment.typePrefix] = true;\n        });\n        builder.addCase(getSearchDepartment.fulfilled, (state, action) => {\n            if (action.payload.status && Array.isArray(action.payload.result.content)) {\n                state.departments = action.payload.result;\n            }\n            state.loading[getSearchDepartment.typePrefix] = false;\n        });\n        builder.addCase(getSearchDepartment.rejected, (state) => {\n            state.loading[getSearchDepartment.typePrefix] = false;\n        });\n        builder.addCase(createDepartment.pending, (state) => {\n            state.loading[createDepartment.typePrefix] = true;\n        });\n        builder.addCase(createDepartment.fulfilled, (state) => {\n            state.loading[createDepartment.typePrefix] = false;\n        });\n        builder.addCase(createDepartment.rejected, (state) => {\n            state.loading[createDepartment.typePrefix] = false;\n        });\n        builder.addCase(editDepartment.pending, (state) => {\n            state.loading[editDepartment.typePrefix] = true;\n        });\n        builder.addCase(editDepartment.fulfilled, (state) => {\n            state.loading[editDepartment.typePrefix] = false;\n        });\n        builder.addCase(editDepartment.rejected, (state) => {\n            state.loading[editDepartment.typePrefix] = false;\n        });\n        builder.addCase(deleteDepartment.pending, (state) => {\n            state.loading[deleteDepartment.typePrefix] = true;\n        });\n        builder.addCase(deleteDepartment.fulfilled, (state) => {\n            state.loading[deleteDepartment.typePrefix] = false;\n        });\n        builder.addCase(deleteDepartment.rejected, (state) => {\n            state.loading[deleteDepartment.typePrefix] = false;\n        });\n    }\n});\n\nexport default departmnetSlice.reducer;\n\nexport const departmentSelector = (state: RootState) => state.department;\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,WAAW,QAAQ,kBAAkB;AAChE,OAAOC,IAAI,MAAM,aAAa;AAK9B,OAAOC,WAAW,MAAM,qBAAqB;AAE7C,OAAOC,GAAG,MAAM,eAAe;AAE/B,OAAO,MAAMC,mBAAmB,GAAGL,gBAAgB,CAC/CI,GAAG,CAACE,UAAU,CAACC,MAAM,CAACC,GAAG,EACzB,MAAOC,MAAM,IAAK;EACd,MAAMC,QAAQ,GAAG,MAAMP,WAAW,CAACC,GAAG,CAACE,UAAU,CAACC,MAAM,EAAEE,MAAM,CAAC;EAEjE,OAAOC,QAAQ;AACnB,CACJ,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAGX,gBAAgB,CAC5CI,GAAG,CAACE,UAAU,CAACM,MAAM,CAACJ,GAAG,EACzB,MAAOC,MAAM,IAAK;EACd,MAAMC,QAAQ,GAAG,MAAMP,WAAW,CAACC,GAAG,CAACE,UAAU,CAACM,MAAM,EAAEH,MAAM,CAAC;EAEjE,OAAOC,QAAQ;AACnB,CACJ,CAAC;AAED,OAAO,MAAMG,cAAc,GAAGb,gBAAgB,CAC1C,yBAAyB,EACzB,MAAOS,MAAM,IAAK;EACd,MAAMC,QAAQ,GAAG,MAAMP,WAAW,CAACC,GAAG,CAACE,UAAU,CAACQ,IAAI,CAACL,MAAM,CAACM,EAAE,CAAC,EAAEb,IAAI,CAACO,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;EAExF,OAAOC,QAAQ;AACnB,CACJ,CAAC;AAED,OAAO,MAAMM,gBAAgB,GAAGhB,gBAAgB,CAAwC,2BAA2B,EAAE,MAAOS,MAAM,IAAK;EACnI,MAAMC,QAAQ,GAAG,MAAMP,WAAW,CAACC,GAAG,CAACE,UAAU,CAACW,MAAM,CAACR,MAAM,CAAC,CAAC;EAEjE,OAAOC,QAAQ;AACnB,CAAC,CAAC;AAOF,MAAMQ,YAA8B,GAAG;EACnCC,OAAO,EAAE,CAAC;AACd,CAAC;AAED,MAAMC,eAAe,GAAGnB,WAAW,CAAC;EAChCoB,IAAI,EAAE,YAAY;EAClBH,YAAY,EAAEA,YAAY;EAC1BI,QAAQ,EAAE,CAAC,CAAC;EACZC,aAAa,EAAGC,OAAO,IAAK;IACxBA,OAAO,CAACC,OAAO,CAACpB,mBAAmB,CAACqB,OAAO,EAAGC,KAAK,IAAK;MACpDA,KAAK,CAACR,OAAO,CAACd,mBAAmB,CAACuB,UAAU,CAAC,GAAG,IAAI;IACxD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACpB,mBAAmB,CAACwB,SAAS,EAAE,CAACF,KAAK,EAAEG,MAAM,KAAK;MAC9D,IAAIA,MAAM,CAACC,OAAO,CAACC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAACC,OAAO,CAACI,MAAM,CAACC,OAAO,CAAC,EAAE;QACvET,KAAK,CAACU,WAAW,GAAGP,MAAM,CAACC,OAAO,CAACI,MAAM;MAC7C;MACAR,KAAK,CAACR,OAAO,CAACd,mBAAmB,CAACuB,UAAU,CAAC,GAAG,KAAK;IACzD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACpB,mBAAmB,CAACiC,QAAQ,EAAGX,KAAK,IAAK;MACrDA,KAAK,CAACR,OAAO,CAACd,mBAAmB,CAACuB,UAAU,CAAC,GAAG,KAAK;IACzD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACd,gBAAgB,CAACe,OAAO,EAAGC,KAAK,IAAK;MACjDA,KAAK,CAACR,OAAO,CAACR,gBAAgB,CAACiB,UAAU,CAAC,GAAG,IAAI;IACrD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACd,gBAAgB,CAACkB,SAAS,EAAGF,KAAK,IAAK;MACnDA,KAAK,CAACR,OAAO,CAACR,gBAAgB,CAACiB,UAAU,CAAC,GAAG,KAAK;IACtD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACd,gBAAgB,CAAC2B,QAAQ,EAAGX,KAAK,IAAK;MAClDA,KAAK,CAACR,OAAO,CAACR,gBAAgB,CAACiB,UAAU,CAAC,GAAG,KAAK;IACtD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACZ,cAAc,CAACa,OAAO,EAAGC,KAAK,IAAK;MAC/CA,KAAK,CAACR,OAAO,CAACN,cAAc,CAACe,UAAU,CAAC,GAAG,IAAI;IACnD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACZ,cAAc,CAACgB,SAAS,EAAGF,KAAK,IAAK;MACjDA,KAAK,CAACR,OAAO,CAACN,cAAc,CAACe,UAAU,CAAC,GAAG,KAAK;IACpD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACZ,cAAc,CAACyB,QAAQ,EAAGX,KAAK,IAAK;MAChDA,KAAK,CAACR,OAAO,CAACN,cAAc,CAACe,UAAU,CAAC,GAAG,KAAK;IACpD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACT,gBAAgB,CAACU,OAAO,EAAGC,KAAK,IAAK;MACjDA,KAAK,CAACR,OAAO,CAACH,gBAAgB,CAACY,UAAU,CAAC,GAAG,IAAI;IACrD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACT,gBAAgB,CAACa,SAAS,EAAGF,KAAK,IAAK;MACnDA,KAAK,CAACR,OAAO,CAACH,gBAAgB,CAACY,UAAU,CAAC,GAAG,KAAK;IACtD,CAAC,CAAC;IACFJ,OAAO,CAACC,OAAO,CAACT,gBAAgB,CAACsB,QAAQ,EAAGX,KAAK,IAAK;MAClDA,KAAK,CAACR,OAAO,CAACH,gBAAgB,CAACY,UAAU,CAAC,GAAG,KAAK;IACtD,CAAC,CAAC;EACN;AACJ,CAAC,CAAC;AAEF,eAAeR,eAAe,CAACmB,OAAO;AAEtC,OAAO,MAAMC,kBAAkB,GAAIb,KAAgB,IAAKA,KAAK,CAACrB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}