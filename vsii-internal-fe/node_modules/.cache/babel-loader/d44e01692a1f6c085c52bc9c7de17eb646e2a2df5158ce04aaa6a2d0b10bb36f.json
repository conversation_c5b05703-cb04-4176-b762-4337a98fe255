{"ast": null, "code": "import { PartitionNumberPattern } from './PartitionNumberPattern';\nimport { CollapseNumberRange } from './CollapseNumberRange';\nimport { FormatApproximately } from './FormatApproximately';\n/**\n * https://tc39.es/ecma402/#sec-partitionnumberrangepattern\n */\nexport function PartitionNumberRangePattern(numberFormat, x, y, _a) {\n  var getInternalSlots = _a.getInternalSlots;\n  if (isNaN(x) || isNaN(y)) {\n    throw new RangeError('Input must be a number');\n  }\n  var result = [];\n  var xResult = PartitionNumberPattern(numberFormat, x, {\n    getInternalSlots: getInternalSlots\n  });\n  var yResult = PartitionNumberPattern(numberFormat, y, {\n    getInternalSlots: getInternalSlots\n  });\n  if (xResult === yResult) {\n    return FormatApproximately(numberFormat, xResult, {\n      getInternalSlots: getInternalSlots\n    });\n  }\n  for (var _i = 0, xResult_1 = xResult; _i < xResult_1.length; _i++) {\n    var r = xResult_1[_i];\n    r.source = 'startRange';\n  }\n  result = result.concat(xResult);\n  var internalSlots = getInternalSlots(numberFormat);\n  var symbols = internalSlots.dataLocaleData.numbers.symbols[internalSlots.numberingSystem];\n  result.push({\n    type: 'literal',\n    value: symbols.rangeSign,\n    source: 'shared'\n  });\n  for (var _b = 0, yResult_1 = yResult; _b < yResult_1.length; _b++) {\n    var r = yResult_1[_b];\n    r.source = 'endRange';\n  }\n  result = result.concat(yResult);\n  return CollapseNumberRange(result);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}