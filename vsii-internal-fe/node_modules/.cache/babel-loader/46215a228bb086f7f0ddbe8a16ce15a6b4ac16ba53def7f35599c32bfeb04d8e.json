{"ast": null, "code": "/* eslint-disable react-hooks/exhaustive-deps */import{useEffect,useState}from'react';// project imports\nimport{useAppDispatch}from'app/hooks';import MainCard from'components/cards/MainCard';import{Table,TableFooter}from'components/extended/Table';import Api from'constants/Api';import{SEARCH_PARAM_KEY,paginationParamDefault,paginationResponseDefault}from'constants/Common';import{PERMISSIONS}from'constants/Permission';import{TableToolbar}from'containers';import{AddOrEditBidding,BiddingSearch,BiddingTBody,BiddingThead,BiddingTotal,CommentPopover}from'containers/sales';import{FilterCollapse}from'containers/search';import sendRequest from'services/ApiService';import{closeConfirm,openConfirm}from'store/slice/confirmSlice';import{openSnackbar}from'store/slice/snackbarSlice';import{checkAllowedPermission}from'utils/authorization';import{getSearchParam,isEmpty,transformObject}from'utils/common';import{biddingFilterConfig}from'./Config';// third party\nimport{FormattedMessage}from'react-intl';import{useSearchParams}from'react-router-dom';// material-ui\nimport useConfig from'hooks/useConfig';// ==============================|| Bidding ||============================== //\n/**\n *  URL Params\n *  page\n *  size\n *  type\n *  year\n *  projectName\n *  status\n */import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const Bidding=()=>{// URL Params\nconst[searchParams,setSearchParams]=useSearchParams();const keyParams=[SEARCH_PARAM_KEY.page,SEARCH_PARAM_KEY.size,SEARCH_PARAM_KEY.type,SEARCH_PARAM_KEY.year,SEARCH_PARAM_KEY.status,SEARCH_PARAM_KEY.searchMode,SEARCH_PARAM_KEY.projectName,SEARCH_PARAM_KEY.customer,SEARCH_PARAM_KEY.searchModeOfCustomer,SEARCH_PARAM_KEY.searchModeOfProject];const params=getSearchParam(keyParams,searchParams);transformObject(params);const{locale}=useConfig();// Hooks, State, Variable\nconst dispatch=useAppDispatch();const defaultConditions={...biddingFilterConfig,...params,language:locale};const[loading,setLoading]=useState(false);const[loadingPost,setLoadingPost]=useState(false);const[paginationResponse,setPaginationResponse]=useState({...paginationResponseDefault,pageNumber:params.page?params.page:paginationResponseDefault.pageNumber,pageSize:params.size?params.size:paginationResponseDefault.pageSize});const[saleBiddings,setSaleBiddings]=useState([]);const[detailBidding,setDetailBidding]=useState(null);const[totalBidding,setTotalBidding]=useState([]);const[conditions,setConditions]=useState(defaultConditions);const[formReset]=useState(defaultConditions);const[isEditBidding,setIsEditBidding]=useState(false);const[openFormAddOrEditBidding,setOpenFormAddOrEditBidding]=useState(false);const{biddingPermission}=PERMISSIONS.sale.salePipeline;const[exchangeRateUSDpercentVND,setExchangeRateUSDpercentVND]=useState(0);const[currencyAndExchangeRateDefault,setCurrencyAndExchangeRateDefault]=useState(null);const[monthlyBillableDay,setMonthlyBillableDay]=useState();const[commentItem,setCommentItem]=useState(null);const[isEditComment,setIsEditComment]=useState(false);const[anchorElComment,setAnchorElComment]=useState(null);const[isRefresh,setIsRefresh]=useState(false);const[isEditedTotalList,setIsEditedTotalList]=useState(false);// ================= Functions =================\n// Get Bidding\nconst getDataTable=async()=>{setLoading(true);const response=await sendRequest(Api.sale_pipe_line_bidding.getBidding,{...conditions,page:conditions.page+1});if(response){const{status,result}=response;if(status){const{content,pagination}=result;if(!isEmpty(content)){setSaleBiddings(content.dataList);setPaginationResponse({...paginationResponse,totalElement:pagination===null||pagination===void 0?void 0:pagination.totalElement});setLoading(false);}else{setDataEmpty();}}}else{setDataEmpty();}};const getTotalBidding=async()=>{setLoading(true);const response=await sendRequest(Api.sale_pipe_line_bidding.getTotal,{...conditions});if(response){const{status,result}=response;if(status){const{content}=result;if(!isEmpty(content)){setTotalBidding(content.total.filter(item=>item.show));setLoading(false);}else{setTotalBidding([]);setLoading(false);}}}else{setTotalBidding([]);setLoading(false);}};// Get monthly billable day\nconst getMonthlyBillableDay=async()=>{const params={year:conditions.year};const response=await sendRequest(Api.sale_pipe_line_bidding.getMonthlyBillable,params);if(response!==null&&response!==void 0&&response.status){const{result}=response;if(result){setMonthlyBillableDay(result.content);}}else return;};// Get Exchange rate USD / VND\nconst getCurrencyAndExchangeRateDefault=async()=>{const response=await sendRequest(Api.sale_productivity.getExchangeRate,{year:conditions.year,convert:'Yes'});if(response){if(response.status){const{content}=response.result;if(!isEmpty(content)){setCurrencyAndExchangeRateDefault(content[0]);}else{setCurrencyAndExchangeRateDefault(null);}}}else return;};// Get exchange rate usd / vnd\nconst getExchangeRateUSDpercentVND=async()=>{const response=await sendRequest(Api.sale_productivity.getExchangeRate,{year:conditions.year,convert:'Yes',currency:'USD'});if(response){if(response.status){const{content}=response.result;if(!isEmpty(content)){setExchangeRateUSDpercentVND(content[0].exchangeRate);}else return;}}else return;};// Set data empty\nconst setDataEmpty=()=>{setSaleBiddings([]);setLoading(false);};// postAddOrEditBidding\nconst postAddOrEditBidding=async payload=>{setLoadingPost(true);const response=await sendRequest(Api.sale_pipe_line_bidding.postAddOrEditBidding,payload);if(response){if(response!==null&&response!==void 0&&response.status){setLoadingPost(false);setOpenFormAddOrEditBidding(false);setIsRefresh(!isRefresh);dispatch(closeConfirm());getDataTable();dispatch(openSnackbar({open:true,message:isEditBidding?'update-success':'add-success',variant:'alert',alert:{color:'success'}}));}else{setLoadingPost(false);dispatch(openSnackbar({open:true,message:response.result.content,variant:'alert',alert:{color:'warning'}}));}}setLoadingPost(false);};// Get detail bidding\nconst getDetailBidding=async idHexString=>{const response=await sendRequest(Api.sale_pipe_line_bidding.getDetailBidding,{year:conditions.year,idHexString});if(response!==null&&response!==void 0&&response.status){setOpenFormAddOrEditBidding(true);const{result}=response;setDetailBidding(result.content);}else return;};// delete bidding\nconst deleteProjectBidding=async idHexString=>{const response=await sendRequest(Api.sale_pipe_line_bidding.deleteBidding,{idHexString});if(response.status){dispatch(closeConfirm());dispatch(openSnackbar({open:true,message:'delete-success',variant:'alert',alert:{color:'success'}}));getDataTable();}};// post edit comment bidding\nconst postEditComment=async payload=>{const response=await sendRequest(Api.sale_pipe_line_bidding.comment,{...payload,year:conditions.year});if(response!==null&&response!==void 0&&response.status){dispatch(openSnackbar({open:true,message:'update-success',variant:'alert',alert:{color:'success'}}));setIsEditComment(false);const{content}=response.result;const biddingListUpdate=saleBiddings.map(prv=>{if(prv.idHexString===content.idHexString){return content;}return prv;});setSaleBiddings(biddingListUpdate);}else return;};// ================= Event =================\n// Handle delete bidding\nconst handleOpenDeleteProjectBidding=idHexString=>{dispatch(openConfirm({open:true,title:/*#__PURE__*/_jsx(FormattedMessage,{id:\"warning\"}),content:/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"messege-delete\"})}),handleConfirm:()=>deleteProjectBidding(idHexString)}));};// Handle open add or edit modal bidding\nconst handleOpenFormAddOrUpdateBidding=idHexString=>{if(idHexString){setIsEditBidding(true);getDetailBidding(idHexString);return;}setOpenFormAddOrEditBidding(true);setDetailBidding(null);};// Handle close form add or update bidding\nconst handleCloseFormAddOrUpdateBidding=()=>{setOpenFormAddOrEditBidding(false);setIsEditBidding(false);setDetailBidding(null);};// Handle change page\nconst handleChangePage=(event,newPage)=>{setConditions({...conditions,page:newPage});setSearchParams({...params,page:newPage});};// Handle change rows per page\nconst handleChangeRowsPerPage=event=>{setConditions({...conditions,page:paginationParamDefault.page,size:parseInt(event.target.value,10)});setSearchParams({...params,page:paginationParamDefault.page,size:parseInt(event.target.value,10)});};// Handle comment bidding\nconst handleOpenCommentBidding=(event,item)=>{setAnchorElComment(event.currentTarget);const{comment,...cloneItem}=item;setCommentItem({...cloneItem,content:comment});};// Handle close comment bidding\nconst handleCloseCommentBidding=()=>{setAnchorElComment(null);setIsEditComment(false);};// ================= Handle submit =================\nconst handleSearch=values=>{transformObject(values);setSearchParams(values);setConditions(values);};const hanldeConfirmEditList=async list=>{const res=await sendRequest(Api.flexible_report.editArrangement,list);dispatch(openSnackbar({open:true,message:res.status?'update-success':'update-fail',variant:'alert',alert:{color:res.status?'success':'error'}}));if(res.status){setTotalBidding(list);setIsEditedTotalList(false);}};// ================= Effect =================\nuseEffect(()=>{getDataTable();getMonthlyBillableDay();getExchangeRateUSDpercentVND();getCurrencyAndExchangeRateDefault();getTotalBidding();},[conditions]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FilterCollapse,{children:/*#__PURE__*/_jsx(BiddingSearch,{formReset:formReset,handleSearch:handleSearch,isRefresh:isRefresh})}),totalBidding.length>0&&/*#__PURE__*/_jsx(BiddingTotal,{totalBidding:totalBidding,loading:loading,isEdited:isEditedTotalList,setIsEdited:setIsEditedTotalList,handleConFirmEdit:hanldeConfirmEditList}),/*#__PURE__*/_jsxs(MainCard,{children:[/*#__PURE__*/_jsx(TableToolbar,{handleOpen:checkAllowedPermission(biddingPermission.add)?handleOpenFormAddOrUpdateBidding:undefined,handleRefreshData:getDataTable}),/*#__PURE__*/_jsx(Table,{heads:/*#__PURE__*/_jsx(BiddingThead,{}),isLoading:loading,data:saleBiddings,children:/*#__PURE__*/_jsx(BiddingTBody,{pageNumber:conditions.page,pageSize:conditions.size,handleOpen:handleOpenFormAddOrUpdateBidding,saleBiddings:saleBiddings,handleDelete:handleOpenDeleteProjectBidding,handleOpenComment:handleOpenCommentBidding})})]}),!loading&&/*#__PURE__*/_jsx(TableFooter,{pagination:{total:paginationResponse.totalElement,page:conditions.page,size:conditions.size},onPageChange:handleChangePage,onRowsPerPageChange:handleChangeRowsPerPage}),openFormAddOrEditBidding&&/*#__PURE__*/_jsx(AddOrEditBidding,{monthlyBillableDay:monthlyBillableDay,year:conditions.year,exchangeRateUSDpercentVND:exchangeRateUSDpercentVND,currencyAndExchangeRateDefault:currencyAndExchangeRateDefault,open:openFormAddOrEditBidding,handleClose:handleCloseFormAddOrUpdateBidding,isEdit:isEditBidding,postAddOrEditBidding:postAddOrEditBidding,detailBidding:detailBidding,loading:loadingPost}),/*#__PURE__*/_jsx(CommentPopover,{isSalesPipeLine:true,item:commentItem,anchorEl:anchorElComment,handleClose:handleCloseCommentBidding,isEdit:isEditComment,setIsEdit:setIsEditComment,editComment:postEditComment})]});};export default Bidding;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}