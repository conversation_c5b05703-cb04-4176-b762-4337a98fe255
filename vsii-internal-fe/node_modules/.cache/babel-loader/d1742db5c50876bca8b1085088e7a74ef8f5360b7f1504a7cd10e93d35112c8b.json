{"ast": null, "code": "/* eslint-disable react-hooks/exhaustive-deps */import{useCallback,useEffect,useState}from'react';import{useIntl}from'react-intl';import moment from'moment';// project imports\nimport{MonthlyEffortProjectSearch,MonthlyEffortProjectTBody,MonthlyEffortProjectThead}from'containers/monthly-effort';import{transformObject,exportDocument,getSearchParam,isEmpty,setLocalStorageSearchTime}from'utils/common';import{monthEffortProjectDefault,monthlyEffortProjectConfig}from'./Config';import{convertMonthFromToDate,getCurrentMonth,getCurrentYear,getMonthsOfYear}from'utils/date';import{SEARCH_PARAM_KEY,TEXT_CONFIG_SCREEN}from'constants/Common';import{PERMISSIONS}from'constants/Permission';import{FilterCollapse}from'containers/search';import{Table}from'components/extended/Table';import MainCard from'components/cards/MainCard';import sendRequest from'services/ApiService';import{useAppDispatch}from'app/hooks';import Api from'constants/Api';// third party\nimport{useSearchParams}from'react-router-dom';import{openSnackbar}from'store/slice/snackbarSlice';import{checkAllowedPermission}from'utils/authorization';// ==============================|| Monthy Effort - Project ||============================== //\n/**\n *  URL Params\n *  year\n *  month\n *  departmentId\n *  projectType\n *  projectId\n *  projectName\n */import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const MonthlyEffortProject=()=>{const dispatch=useAppDispatch();const intl=useIntl();// URL Params\nconst[searchParams,setSearchParams]=useSearchParams();const keyParams=[SEARCH_PARAM_KEY.year,SEARCH_PARAM_KEY.month,SEARCH_PARAM_KEY.departmentId,SEARCH_PARAM_KEY.projectType,SEARCH_PARAM_KEY.projectId,SEARCH_PARAM_KEY.projectName];const params=getSearchParam(keyParams,searchParams);transformObject(params);// delete unnecessary key value\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst{projectName,...cloneParams}=params;// Hooks, State, Variable\nconst defaultConditions={...monthlyEffortProjectConfig,...cloneParams,projectId:params.projectId?{value:params.projectId,label:params.projectName}:null};const[loading,setLoading]=useState(false);const[project,setProject]=useState(monthEffortProjectDefault);const[conditions,setConditions]=useState(defaultConditions);const[formReset,setFormReset]=useState(defaultConditions);const[year,setYear]=useState(defaultConditions.year);const[months,setMonths]=useState([]);const[isChangeYear,setIsChangeYear]=useState(false);const[month,setMonth]=useState({fromDate:'',toDate:''});const{monthlyEffort}=PERMISSIONS.report;const{monthlyEffortProject}=TEXT_CONFIG_SCREEN.monthlyEffort;// Function\nconst getDataTable=async()=>{setLoading(true);const response=await sendRequest(Api.monthly_efford.getProject,{...conditions,projectId:conditions.projectId?conditions.projectId.value:null});if(response.status){const{result}=response;setProject(isEmpty(result.content)?monthEffortProjectDefault:result.content);setLoading(false);return;}else{setDataEmpty();}};const setDataEmpty=()=>{setProject(monthEffortProjectDefault);setLoading(false);};const getMonthInYears=useCallback(async y=>{const monthInYears=await getMonthsOfYear(y);return monthInYears;},[]);// Event\nconst handleChangeYear=e=>{const{value}=e.target;setYear(value);setIsChangeYear(true);setFormReset(prev=>({...prev,projectId:null,year:value}));};const handleExportDocument=()=>{exportDocument(Api.monthly_efford.getDownload.url,{year:conditions.year,month:conditions.month});};const handleChangeProject=data=>{if(data)setFormReset(prev=>({...prev,departmentId:data===null||data===void 0?void 0:data.dept,projectType:data===null||data===void 0?void 0:data.typeCode,billable:data===null||data===void 0?void 0:data.billable,projectId:data?data:null}));};const monthOnload=months.find(item=>item.value===formReset.month);const handleChangeMonth=value=>{const getMonth=months.find(month=>month.value===value);if(getMonth){setMonth(convertMonthFromToDate(getMonth.label));}setFormReset(prev=>({...prev,projectId:null,month:value}));};const handleChangeProjectType=e=>{setFormReset(prev=>({...prev,projectId:null,projectType:e.target.value}));};const handleChangeBillable=e=>{setFormReset(prev=>({...prev,projectId:null,billable:e.target.value}));};const handleChangeDept=value=>{setFormReset(prev=>({...prev,projectId:null,departmentId:value}));};// Handle submit\nconst handleSearch=value=>{const{projectId}=value;transformObject(value);setSearchParams(projectId?{...value,projectId:projectId.value,projectName:projectId.label}:value);setConditions(value);// lưu thời gian vào localStorage\nsetLocalStorageSearchTime({month:value.month,year:value.year});};useEffect(()=>{if(monthOnload){setMonth(convertMonthFromToDate(monthOnload.label));}},[monthOnload]);// Effect\nuseEffect(()=>{getDataTable();},[conditions]);useEffect(()=>{getMonthInYears(year).then(items=>{if(getCurrentYear()===year){let currentMonth=items.slice(0,getCurrentMonth());setMonths(currentMonth);}else{setMonths(items);}if(items.length>0&&isChangeYear){setFormReset({...formReset,year,month:items[0].value});}});},[year]);useEffect(()=>{if(conditions.projectId){project.users.forEach(user=>{let monthsWithNoEffort=[];Object.keys(user.months).forEach(key=>{if(user.months[key]===0&&key!=='thirteenthSalary'){monthsWithNoEffort.push(Number(moment().month(key).format('M')));}});const filteredMonthsWithNoEffort=monthsWithNoEffort.filter(e=>e<=conditions.month).sort((a,b)=>a-b);if(filteredMonthsWithNoEffort.length>0){dispatch(openSnackbar({open:true,message:intl.formatMessage({id:'no_effort_data'},{projectName,months:filteredMonthsWithNoEffort.join(',')}),variant:'alert',alert:{color:'info'}}));}});}},[project]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FilterCollapse,{handleExport:checkAllowedPermission(monthlyEffort.projectDownload)?handleExportDocument:undefined,downloadLabel:monthlyEffortProject+'download-report',children:/*#__PURE__*/_jsx(MonthlyEffortProjectSearch,{formReset:formReset,months:months,handleChangeYear:handleChangeYear,handleSearch:handleSearch,handleChangeProject:handleChangeProject,handleChangeMonth:handleChangeMonth,handleChangeProjectType:handleChangeProjectType,handleChangeBillable:handleChangeBillable,handleChangeDept:handleChangeDept,month:month})}),/*#__PURE__*/_jsx(MainCard,{children:/*#__PURE__*/_jsx(Table,{heads:/*#__PURE__*/_jsx(MonthlyEffortProjectThead,{projectLength:project===null||project===void 0?void 0:project.users.length,currentYear:conditions.year}),isLoading:loading,data:project===null||project===void 0?void 0:project.users,children:/*#__PURE__*/_jsx(MonthlyEffortProjectTBody,{projects:project===null||project===void 0?void 0:project.users,total:project===null||project===void 0?void 0:project.total})})})]});};export default MonthlyEffortProject;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}