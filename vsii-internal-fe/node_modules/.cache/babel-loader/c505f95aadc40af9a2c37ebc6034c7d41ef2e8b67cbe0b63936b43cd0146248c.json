{"ast": null, "code": "import { warning } from 'hey-listen';\nimport { secondsToMilliseconds } from '../utils/time-conversion.mjs';\nimport { instantAnimationState } from '../utils/use-instant-transition-state.mjs';\nimport { createAcceleratedAnimation } from './waapi/create-accelerated-animation.mjs';\nimport { createInstantAnimation } from './create-instant-animation.mjs';\nimport { animate } from './legacy-popmotion/index.mjs';\nimport { inertia } from './legacy-popmotion/inertia.mjs';\nimport { getDefaultTransition } from './utils/default-transitions.mjs';\nimport { isAnimatable } from './utils/is-animatable.mjs';\nimport { getKeyframes } from './utils/keyframes.mjs';\nimport { getValueTransition, isTransitionDefined } from './utils/transitions.mjs';\nimport { supports } from './waapi/supports.mjs';\n\n/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\"opacity\"]);\nconst createMotionValueAnimation = function (valueName, value, target) {\n  let transition = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  return onComplete => {\n    const valueTransition = getValueTransition(transition, valueName) || {};\n    /**\n     * Most transition values are currently completely overwritten by value-specific\n     * transitions. In the future it'd be nicer to blend these transitions. But for now\n     * delay actually does inherit from the root transition if not value-specific.\n     */\n    const delay = valueTransition.delay || transition.delay || 0;\n    /**\n     * Elapsed isn't a public transition option but can be passed through from\n     * optimized appear effects in milliseconds.\n     */\n    let {\n      elapsed = 0\n    } = transition;\n    elapsed = elapsed - secondsToMilliseconds(delay);\n    const keyframes = getKeyframes(value, valueName, target, valueTransition);\n    /**\n     * Check if we're able to animate between the start and end keyframes,\n     * and throw a warning if we're attempting to animate between one that's\n     * animatable and another that isn't.\n     */\n    const originKeyframe = keyframes[0];\n    const targetKeyframe = keyframes[keyframes.length - 1];\n    const isOriginAnimatable = isAnimatable(valueName, originKeyframe);\n    const isTargetAnimatable = isAnimatable(valueName, targetKeyframe);\n    warning(isOriginAnimatable === isTargetAnimatable, \"You are trying to animate \".concat(valueName, \" from \\\"\").concat(originKeyframe, \"\\\" to \\\"\").concat(targetKeyframe, \"\\\". \").concat(originKeyframe, \" is not an animatable value - to enable this animation set \").concat(originKeyframe, \" to a value animatable to \").concat(targetKeyframe, \" via the `style` property.\"));\n    let options = {\n      keyframes,\n      velocity: value.getVelocity(),\n      ...valueTransition,\n      elapsed,\n      onUpdate: v => {\n        value.set(v);\n        valueTransition.onUpdate && valueTransition.onUpdate(v);\n      },\n      onComplete: () => {\n        onComplete();\n        valueTransition.onComplete && valueTransition.onComplete();\n      }\n    };\n    if (!isOriginAnimatable || !isTargetAnimatable || instantAnimationState.current || valueTransition.type === false) {\n      /**\n       * If we can't animate this value, or the global instant animation flag is set,\n       * or this is simply defined as an instant transition, return an instant transition.\n       */\n      return createInstantAnimation(options);\n    } else if (valueTransition.type === \"inertia\") {\n      /**\n       * If this is an inertia animation, we currently don't support pre-generating\n       * keyframes for this as such it must always run on the main thread.\n       */\n      const animation = inertia(options);\n      return () => animation.stop();\n    }\n    /**\n     * If there's no transition defined for this value, we can generate\n     * unqiue transition settings for this value.\n     */\n    if (!isTransitionDefined(valueTransition)) {\n      options = {\n        ...options,\n        ...getDefaultTransition(valueName, options)\n      };\n    }\n    /**\n     * Both WAAPI and our internal animation functions use durations\n     * as defined by milliseconds, while our external API defines them\n     * as seconds.\n     */\n    if (options.duration) {\n      options.duration = secondsToMilliseconds(options.duration);\n    }\n    if (options.repeatDelay) {\n      options.repeatDelay = secondsToMilliseconds(options.repeatDelay);\n    }\n    const visualElement = value.owner;\n    const element = visualElement && visualElement.current;\n    const canAccelerateAnimation = supports.waapi() && acceleratedValues.has(valueName) && !options.repeatDelay && options.repeatType !== \"mirror\" && options.damping !== 0 && visualElement && element instanceof HTMLElement && !visualElement.getProps().onUpdate;\n    if (canAccelerateAnimation) {\n      /**\n       * If this animation is capable of being run via WAAPI, then do so.\n       */\n      return createAcceleratedAnimation(value, valueName, options);\n    } else {\n      /**\n       * Otherwise, fall back to the main thread.\n       */\n      const animation = animate(options);\n      return () => animation.stop();\n    }\n  };\n};\nexport { createMotionValueAnimation };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}