{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/layout/Header/ManualSyncSection.tsx\",\n  _s = $RefreshSig$();\n// material-ui\nimport { useTheme } from '@mui/material/styles';\nimport { Avatar, Box } from '@mui/material';\nimport { useAppDispatch } from 'app/hooks';\nimport { openSync } from 'store/slice/syncSlice';\n\n// assets\nimport SyncIcon from '@mui/icons-material/Sync';\n\n// ==============================|| MANUAL SYNC ||============================== //\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ManualSyncSection = () => {\n  _s();\n  const theme = useTheme();\n  const dispatch = useAppDispatch();\n  const handleOpen = () => {\n    dispatch(openSync());\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Box\n    // sx={{\n    //     mr: 2,\n    //     [theme.breakpoints.down('md')]: {\n    //         mr: 2\n    //     }\n    // }}\n    , {\n      children: /*#__PURE__*/_jsxDEV(Avatar, {\n        variant: \"rounded\",\n        sx: {\n          ...theme.typography.commonAvatar,\n          ...theme.typography.mediumAvatar,\n          transition: 'all .2s ease-in-out',\n          background: theme.palette.mode === 'dark' ? theme.palette.dark.main : theme.palette.secondary.light,\n          color: theme.palette.mode === 'dark' ? theme.palette.warning.dark : theme.palette.secondary.dark,\n          '&[aria-controls=\"menu-list-grow\"],&:hover': {\n            background: theme.palette.mode === 'dark' ? theme.palette.warning.dark : theme.palette.secondary.dark,\n            color: theme.palette.mode === 'dark' ? theme.palette.grey[800] : theme.palette.secondary.light\n          }\n        },\n        onClick: handleOpen,\n        color: \"inherit\",\n        children: /*#__PURE__*/_jsxDEV(SyncIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_s(ManualSyncSection, \"FeERZUMYKxBkQHOiiti9oKgqzqE=\", false, function () {\n  return [useTheme, useAppDispatch];\n});\n_c = ManualSyncSection;\nexport default ManualSyncSection;\nvar _c;\n$RefreshReg$(_c, \"ManualSyncSection\");", "map": {"version": 3, "names": ["useTheme", "Avatar", "Box", "useAppDispatch", "openSync", "SyncIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ManualSyncSection", "_s", "theme", "dispatch", "handleOpen", "children", "variant", "sx", "typography", "commonAvatar", "mediumAvatar", "transition", "background", "palette", "mode", "dark", "main", "secondary", "light", "color", "warning", "grey", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/layout/Header/ManualSyncSection.tsx"], "sourcesContent": ["// material-ui\nimport { useTheme } from '@mui/material/styles';\nimport { Avatar, Box } from '@mui/material';\n\nimport { useAppDispatch } from 'app/hooks';\nimport { openSync } from 'store/slice/syncSlice';\n\n// assets\nimport SyncIcon from '@mui/icons-material/Sync';\n\n// ==============================|| MANUAL SYNC ||============================== //\n\nconst ManualSyncSection = () => {\n    const theme = useTheme();\n    const dispatch = useAppDispatch();\n\n    const handleOpen = () => {\n        dispatch(openSync());\n    };\n\n    return (\n        <>\n            <Box\n            // sx={{\n            //     mr: 2,\n            //     [theme.breakpoints.down('md')]: {\n            //         mr: 2\n            //     }\n            // }}\n            >\n                <Avatar\n                    variant=\"rounded\"\n                    sx={{\n                        ...theme.typography.commonAvatar,\n                        ...theme.typography.mediumAvatar,\n                        transition: 'all .2s ease-in-out',\n                        background: theme.palette.mode === 'dark' ? theme.palette.dark.main : theme.palette.secondary.light,\n                        color: theme.palette.mode === 'dark' ? theme.palette.warning.dark : theme.palette.secondary.dark,\n                        '&[aria-controls=\"menu-list-grow\"],&:hover': {\n                            background: theme.palette.mode === 'dark' ? theme.palette.warning.dark : theme.palette.secondary.dark,\n                            color: theme.palette.mode === 'dark' ? theme.palette.grey[800] : theme.palette.secondary.light\n                        }\n                    }}\n                    onClick={handleOpen}\n                    color=\"inherit\"\n                >\n                    <SyncIcon />\n                </Avatar>\n            </Box>\n        </>\n    );\n};\n\nexport default ManualSyncSection;\n"], "mappings": ";;AAAA;AACA,SAASA,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,MAAM,EAAEC,GAAG,QAAQ,eAAe;AAE3C,SAASC,cAAc,QAAQ,WAAW;AAC1C,SAASC,QAAQ,QAAQ,uBAAuB;;AAEhD;AACA,OAAOC,QAAQ,MAAM,0BAA0B;;AAE/C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,KAAK,GAAGZ,QAAQ,CAAC,CAAC;EACxB,MAAMa,QAAQ,GAAGV,cAAc,CAAC,CAAC;EAEjC,MAAMW,UAAU,GAAGA,CAAA,KAAM;IACrBD,QAAQ,CAACT,QAAQ,CAAC,CAAC,CAAC;EACxB,CAAC;EAED,oBACIG,OAAA,CAAAE,SAAA;IAAAM,QAAA,eACIR,OAAA,CAACL;IACD;IACA;IACA;IACA;IACA;IACA;IAAA;MAAAa,QAAA,eAEIR,OAAA,CAACN,MAAM;QACHe,OAAO,EAAC,SAAS;QACjBC,EAAE,EAAE;UACA,GAAGL,KAAK,CAACM,UAAU,CAACC,YAAY;UAChC,GAAGP,KAAK,CAACM,UAAU,CAACE,YAAY;UAChCC,UAAU,EAAE,qBAAqB;UACjCC,UAAU,EAAEV,KAAK,CAACW,OAAO,CAACC,IAAI,KAAK,MAAM,GAAGZ,KAAK,CAACW,OAAO,CAACE,IAAI,CAACC,IAAI,GAAGd,KAAK,CAACW,OAAO,CAACI,SAAS,CAACC,KAAK;UACnGC,KAAK,EAAEjB,KAAK,CAACW,OAAO,CAACC,IAAI,KAAK,MAAM,GAAGZ,KAAK,CAACW,OAAO,CAACO,OAAO,CAACL,IAAI,GAAGb,KAAK,CAACW,OAAO,CAACI,SAAS,CAACF,IAAI;UAChG,2CAA2C,EAAE;YACzCH,UAAU,EAAEV,KAAK,CAACW,OAAO,CAACC,IAAI,KAAK,MAAM,GAAGZ,KAAK,CAACW,OAAO,CAACO,OAAO,CAACL,IAAI,GAAGb,KAAK,CAACW,OAAO,CAACI,SAAS,CAACF,IAAI;YACrGI,KAAK,EAAEjB,KAAK,CAACW,OAAO,CAACC,IAAI,KAAK,MAAM,GAAGZ,KAAK,CAACW,OAAO,CAACQ,IAAI,CAAC,GAAG,CAAC,GAAGnB,KAAK,CAACW,OAAO,CAACI,SAAS,CAACC;UAC7F;QACJ,CAAE;QACFI,OAAO,EAAElB,UAAW;QACpBe,KAAK,EAAC,SAAS;QAAAd,QAAA,eAEfR,OAAA,CAACF,QAAQ;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC,gBACR,CAAC;AAEX,CAAC;AAACzB,EAAA,CAvCID,iBAAiB;EAAA,QACLV,QAAQ,EACLG,cAAc;AAAA;AAAAkC,EAAA,GAF7B3B,iBAAiB;AAyCvB,eAAeA,iBAAiB;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}