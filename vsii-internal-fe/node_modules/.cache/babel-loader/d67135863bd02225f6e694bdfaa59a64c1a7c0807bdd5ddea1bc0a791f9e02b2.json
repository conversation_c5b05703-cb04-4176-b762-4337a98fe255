{"ast": null, "code": "import{useEffect}from'react';import HighlightOffOutlinedIcon from'@mui/icons-material/HighlightOffOutlined';import DataSaverOnOutlinedIcon from'@mui/icons-material/DataSaverOnOutlined';import{Box,ButtonBase,Divider,Popper,Typography}from'@mui/material';import{useFieldArray,useFormContext}from'react-hook-form';import{FormattedMessage}from'react-intl';import{Autocomplete,Checkbox,Select}from'components/extended/Form';import{SIGN_CAlCULATE,TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Sum=_ref=>{let{columnsToSum}=_ref;const{Flexible_reporting_configuration}=TEXT_CONFIG_SCREEN.administration.flexibleReport;const methods=useFormContext();const{fields:calculationInputs,append,remove,replace}=useFieldArray({control:methods.control,name:'calculationInputs'});const handleAddColumnValue=()=>{append({sign:'+',code:columnsToSum?columnsToSum[0]:{}});};useEffect(()=>{const calculationInputs=methods.getValues('calculationInputs');if(!methods.getValues('id')&&columnsToSum!==null&&columnsToSum!==void 0&&columnsToSum.length&&calculationInputs.length===1||methods.getValues('id')&&!methods.getValues('isCalculation')||calculationInputs.filter(item=>{var _item$code;return!((_item$code=item.code)!==null&&_item$code!==void 0&&_item$code.label);}).length){replace([{sign:'+',code:columnsToSum[0]}]);}},[replace,methods,calculationInputs.length,columnsToSum]);return/*#__PURE__*/_jsx(Box,{mt:2,children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{sx:{color:'#333',display:'flex',gap:1,fontWeight:600},variant:\"h3\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:Flexible_reporting_configuration+'sum'})}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsxs(Box,{display:\"flex\",gap:1,mt:3,sx:{overflowX:'auto'},pb:2,children:[/*#__PURE__*/_jsx(Checkbox,{name:\"isCalculation\",isControl:true}),calculationInputs===null||calculationInputs===void 0?void 0:calculationInputs.map((item,index)=>/*#__PURE__*/_jsxs(Box,{display:\"flex\",gap:1.25,alignItems:\"center\",children:[/*#__PURE__*/_jsx(Box,{width:index!==0?60:0,children:index!==0&&/*#__PURE__*/_jsx(Select,{name:\"calculationInputs.\".concat(index,\".sign\"),selects:SIGN_CAlCULATE})}),/*#__PURE__*/_jsx(Box,{width:150,children:/*#__PURE__*/_jsx(Autocomplete,{options:columnsToSum?columnsToSum:[],name:\"calculationInputs.\".concat(index,\".code\"),groupBy:option=>option.typeCode,isDefaultAll:true,PopperComponent:props=>/*#__PURE__*/_jsx(Popper,{...props,style:{width:250},children:props.children}),isDisableClearable:true})}),index!==0&&/*#__PURE__*/_jsx(ButtonBase,{onClick:()=>remove(index),children:/*#__PURE__*/_jsx(HighlightOffOutlinedIcon,{})})]},item.id)),/*#__PURE__*/_jsx(ButtonBase,{onClick:handleAddColumnValue,children:/*#__PURE__*/_jsx(DataSaverOnOutlinedIcon,{})})]})]})});};export default Sum;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}