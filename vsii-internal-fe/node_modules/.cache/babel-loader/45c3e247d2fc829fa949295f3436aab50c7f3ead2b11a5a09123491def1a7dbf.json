{"ast": null, "code": "/**\n * Based on Kendo UI Core expression code <https://github.com/telerik/kendo-ui-core#license-information>\n */\n'use strict';\n\nfunction Cache(maxSize) {\n  this._maxSize = maxSize;\n  this.clear();\n}\nCache.prototype.clear = function () {\n  this._size = 0;\n  this._values = Object.create(null);\n};\nCache.prototype.get = function (key) {\n  return this._values[key];\n};\nCache.prototype.set = function (key, value) {\n  this._size >= this._maxSize && this.clear();\n  if (!(key in this._values)) this._size++;\n  return this._values[key] = value;\n};\nvar SPLIT_REGEX = /[^.^\\]^[]+|(?=\\[\\]|\\.\\.)/g,\n  DIGIT_REGEX = /^\\d+$/,\n  LEAD_DIGIT_REGEX = /^\\d/,\n  SPEC_CHAR_REGEX = /[~`!#$%\\^&*+=\\-\\[\\]\\\\';,/{}|\\\\\":<>\\?]/g,\n  CLEAN_QUOTES_REGEX = /^\\s*(['\"]?)(.*?)(\\1)\\s*$/,\n  MAX_CACHE_SIZE = 512;\nvar pathCache = new Cache(MAX_CACHE_SIZE),\n  setCache = new Cache(MAX_CACHE_SIZE),\n  getCache = new Cache(MAX_CACHE_SIZE);\nvar config;\nmodule.exports = {\n  Cache: Cache,\n  split: split,\n  normalizePath: normalizePath,\n  setter: function (path) {\n    var parts = normalizePath(path);\n    return setCache.get(path) || setCache.set(path, function setter(obj, value) {\n      var index = 0;\n      var len = parts.length;\n      var data = obj;\n      while (index < len - 1) {\n        var part = parts[index];\n        if (part === '__proto__' || part === 'constructor' || part === 'prototype') {\n          return obj;\n        }\n        data = data[parts[index++]];\n      }\n      data[parts[index]] = value;\n    });\n  },\n  getter: function (path, safe) {\n    var parts = normalizePath(path);\n    return getCache.get(path) || getCache.set(path, function getter(data) {\n      var index = 0,\n        len = parts.length;\n      while (index < len) {\n        if (data != null || !safe) data = data[parts[index++]];else return;\n      }\n      return data;\n    });\n  },\n  join: function (segments) {\n    return segments.reduce(function (path, part) {\n      return path + (isQuoted(part) || DIGIT_REGEX.test(part) ? '[' + part + ']' : (path ? '.' : '') + part);\n    }, '');\n  },\n  forEach: function (path, cb, thisArg) {\n    forEach(Array.isArray(path) ? path : split(path), cb, thisArg);\n  }\n};\nfunction normalizePath(path) {\n  return pathCache.get(path) || pathCache.set(path, split(path).map(function (part) {\n    return part.replace(CLEAN_QUOTES_REGEX, '$2');\n  }));\n}\nfunction split(path) {\n  return path.match(SPLIT_REGEX) || [''];\n}\nfunction forEach(parts, iter, thisArg) {\n  var len = parts.length,\n    part,\n    idx,\n    isArray,\n    isBracket;\n  for (idx = 0; idx < len; idx++) {\n    part = parts[idx];\n    if (part) {\n      if (shouldBeQuoted(part)) {\n        part = '\"' + part + '\"';\n      }\n      isBracket = isQuoted(part);\n      isArray = !isBracket && /^\\d+$/.test(part);\n      iter.call(thisArg, part, isBracket, isArray, idx, parts);\n    }\n  }\n}\nfunction isQuoted(str) {\n  return typeof str === 'string' && str && [\"'\", '\"'].indexOf(str.charAt(0)) !== -1;\n}\nfunction hasLeadingNumber(part) {\n  return part.match(LEAD_DIGIT_REGEX) && !part.match(DIGIT_REGEX);\n}\nfunction hasSpecialChars(part) {\n  return SPEC_CHAR_REGEX.test(part);\n}\nfunction shouldBeQuoted(part) {\n  return !isQuoted(part) && (hasLeadingNumber(part) || hasSpecialChars(part));\n}", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}