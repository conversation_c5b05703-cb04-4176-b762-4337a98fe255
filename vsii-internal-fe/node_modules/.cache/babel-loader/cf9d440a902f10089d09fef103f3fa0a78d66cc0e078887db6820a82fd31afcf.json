{"ast": null, "code": "import{useEffect,useState}from'react';// third party\nimport{useSearchParams}from'react-router-dom';// project imports\nimport{SEARCH_PARAM_KEY,TEXT_CONFIG_SCREEN,paginationParamDefault,paginationResponseDefault}from'constants/Common';import{exportDocument,getSearchParam,isEmpty,transformObject}from'utils/common';import{FilterCollapse}from'containers/search';import{ProjectReferenceSearch,ProjectReferenceTBody,ProjectReferenceTHead}from'containers/sales';import MainCard from'components/cards/MainCard';import{Table,TableFooter}from'components/extended/Table';import{projectReferenceSearchDefaultValue}from'./Config';import sendRequest from'services/ApiService';import Api from'constants/Api';import{PERMISSIONS}from'constants/Permission';import{checkAllowedPermission}from'utils/authorization';// ==============================|| Project Reference ||============================== //\n/**\n *  URL Params\n *  page\n *  size\n *  projectId\n *  projectName\n *  projectType\n */import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const ProjectReference=()=>{const{projectReference}=TEXT_CONFIG_SCREEN.salesReport;// URL Params\nconst[searchParams,setSearchParams]=useSearchParams();const keyParams=[SEARCH_PARAM_KEY.page,SEARCH_PARAM_KEY.size,SEARCH_PARAM_KEY.projectName,SEARCH_PARAM_KEY.projectId,SEARCH_PARAM_KEY.projectType,SEARCH_PARAM_KEY.domain,SEARCH_PARAM_KEY.technology];const params=getSearchParam(keyParams,searchParams);transformObject(params);// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst{projectName,...cloneParams}=params;// Hooks, State, Variable\nconst defaultConditions={...projectReferenceSearchDefaultValue,...cloneParams,projectId:params.projectId?{value:params.projectId,label:params.projectName}:null};const[isLoading,setIsLoading]=useState(false);const[projects,setProjects]=useState([]);const[conditions,setConditions]=useState(defaultConditions);const[formReset]=useState(defaultConditions);const[paginationResponse,setPaginationResponse]=useState({...paginationResponseDefault,pageNumber:params.page?params.page:paginationResponseDefault.pageNumber,pageSize:params.size?params.size:paginationResponseDefault.pageSize});const{projectReferencePermission}=PERMISSIONS.sale;// Function\nconst setDataEmpty=()=>{setProjects([]);setIsLoading(false);};const getAllProject=async()=>{setIsLoading(true);const response=await sendRequest(Api.project.getAll,{...conditions,page:conditions.page+1,projectId:conditions.projectId?conditions.projectId.value:null,isTypeSelect:true});if(response){const{status,result}=response;if(status){const{content,pagination}=result;if(!isEmpty(content)){setProjects(content);setPaginationResponse({...paginationResponse,totalElement:pagination===null||pagination===void 0?void 0:pagination.totalElement});setIsLoading(false);}else{setDataEmpty();setPaginationResponse({...paginationResponse,totalElement:pagination===null||pagination===void 0?void 0:pagination.totalElement});}}}else{setDataEmpty();}};const exportProjectReference=()=>{var _conditions$projectId;// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst{page,size,...cloneConditions}=conditions;transformObject(cloneConditions);exportDocument(Api.project.getDownload.url,{...cloneConditions,projectId:(_conditions$projectId=conditions.projectId)===null||_conditions$projectId===void 0?void 0:_conditions$projectId.value,isScreenPRCE:true});};// Event\n// Handle change page\nconst handleChangePage=(event,newPage)=>{setConditions({...conditions,page:newPage});setSearchParams({...params,page:newPage});};// Handle change rows per page\nconst handleChangeRowsPerPage=event=>{setConditions({...conditions,page:paginationParamDefault.page,size:parseInt(event.target.value,10)});setSearchParams({...params,page:paginationParamDefault.page,size:parseInt(event.target.value,10)});};// Submit\nconst handleSearch=values=>{transformObject(values);const params=values.projectId?{...values,projectId:values.projectId.value,projectName:values.projectId.label}:values;setSearchParams(params);setConditions({...values,page:paginationParamDefault.page,size:conditions.size});};// Effect\nuseEffect(()=>{getAllProject();// eslint-disable-next-line react-hooks/exhaustive-deps\n},[conditions]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FilterCollapse,{handleExport:checkAllowedPermission(projectReferencePermission.download)?exportProjectReference:undefined,downloadLabel:projectReference+'download',children:/*#__PURE__*/_jsx(ProjectReferenceSearch,{handleSearch:handleSearch,formReset:formReset})}),/*#__PURE__*/_jsx(MainCard,{children:/*#__PURE__*/_jsx(Table,{heads:/*#__PURE__*/_jsx(ProjectReferenceTHead,{}),data:projects,isLoading:isLoading,children:/*#__PURE__*/_jsx(ProjectReferenceTBody,{pageNumber:conditions.page,pageSize:conditions.size,projects:projects})})}),!isLoading&&/*#__PURE__*/_jsx(TableFooter,{pagination:{total:paginationResponse===null||paginationResponse===void 0?void 0:paginationResponse.totalElement,page:conditions.page,size:conditions.size},onPageChange:handleChangePage,onRowsPerPageChange:handleChangeRowsPerPage})]});};export default ProjectReference;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}