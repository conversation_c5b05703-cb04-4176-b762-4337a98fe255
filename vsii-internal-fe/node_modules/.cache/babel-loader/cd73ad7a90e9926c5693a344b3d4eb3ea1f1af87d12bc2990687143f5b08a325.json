{"ast": null, "code": "import React from'react';// material-ui\nimport{LoadingButton}from'@mui/lab';import{Button}from'@mui/material';// project imports\nimport{useAppDispatch}from'app/hooks';import{openSnackbar}from'store/slice/snackbarSlice';import{validateFileFormat}from'utils/common';// assets\nimport UploadFileRoundedIcon from'@mui/icons-material/UploadFileRounded';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const FileUploadSingle=_ref=>{let{selectedFile,loading,handleUpload,handleUploadConfirm,handleChange}=_ref;const dispatch=useAppDispatch();// On file select (from the pop up)\nconst handleFileChange=event=>{const{files}=event.target;const selectedFiles=files;handleChange(selectedFiles===null||selectedFiles===void 0?void 0:selectedFiles[0]);handleUploadConfirm(selectedFiles===null||selectedFiles===void 0?void 0:selectedFiles[0]);};// On file upload (click the upload button)\nconst handleFileUpload=()=>{if(validateFileFormat(selectedFile)){handleUpload(selectedFile);}else{dispatch(openSnackbar({open:true,message:'error-file',variant:'alert',alert:{color:'error'}}));}handleChange(null);};// File upload confirm\nreturn/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Button,{color:\"primary\",\"aria-label\":\"upload picture\",component:\"label\",sx:{textTransform:'none'},children:[/*#__PURE__*/_jsx(\"input\",{hidden:true,type:\"file\",accept:\".xlsx, .xls\",onChange:handleFileChange,onClick:e=>e.target.value=null}),selectedFile?\"\".concat(selectedFile.name):'Choose file...']}),/*#__PURE__*/_jsx(LoadingButton,{loading:loading,disabled:!selectedFile,variant:\"contained\",size:\"small\",startIcon:/*#__PURE__*/_jsx(UploadFileRoundedIcon,{}),loadingPosition:\"start\",onClick:()=>handleFileUpload(),children:\"Upload\"})]});};export default FileUploadSingle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}