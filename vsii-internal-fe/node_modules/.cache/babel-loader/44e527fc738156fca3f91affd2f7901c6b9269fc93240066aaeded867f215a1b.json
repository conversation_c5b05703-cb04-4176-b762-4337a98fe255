{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getDate\n * @category Day Helpers\n * @summary Get the day of the month of the given date.\n *\n * @description\n * Get the day of the month of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the day of month\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which day of the month is 29 February 2012?\n * const result = getDate(new Date(2012, 1, 29))\n * //=> 29\n */\nexport default function getDate(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var dayOfMonth = date.getDate();\n  return dayOfMonth;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}