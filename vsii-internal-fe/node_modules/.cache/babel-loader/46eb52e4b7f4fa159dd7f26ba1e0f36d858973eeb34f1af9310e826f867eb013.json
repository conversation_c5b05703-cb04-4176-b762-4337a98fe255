{"ast": null, "code": "import { getPickersLocalization } from './utils/getPickersLocalization';\nconst ukUAPickers = {\n  // Calendar navigation\n  previousMonth: 'Попередній місяць',\n  nextMonth: 'Наступний місяць',\n  // View navigation\n  openPreviousView: 'відкрити попередній вигляд',\n  openNextView: 'відкрити наступний вигляд',\n  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'річний вигляд відкрито, перейти до календарного вигляду' : 'календарний вигляд відкрито, перейти до річного вигляду',\n  inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `текстове поле відкрите, перейти до  ${viewType} вигляду` : `${viewType} вигляд наразі відкрито, перейти до текстового поля`,\n  // DateRange placeholders\n  start: 'Початок',\n  end: 'Кінець',\n  // Action bar\n  cancelButtonLabel: 'Відміна',\n  clearButtonLabel: 'Очистити',\n  okButtonLabel: 'OK',\n  todayButtonLabel: 'Сьогодні',\n  // Toolbar titles\n  datePickerDefaultToolbarTitle: 'Вибрати дату',\n  dateTimePickerDefaultToolbarTitle: 'Вибрати дату і час',\n  timePickerDefaultToolbarTitle: 'Вибрати час',\n  dateRangePickerDefaultToolbarTitle: 'Вибрати календарний період',\n  // Clock labels\n  clockLabelText: (view, time, adapter) => `Select ${view}. ${time === null ? 'Час не вибраний' : `Вибрано час ${adapter.format(time, 'fullTime')}`}`,\n  hoursClockNumberText: hours => `${hours} годин`,\n  minutesClockNumberText: minutes => `${minutes} хвилин`,\n  secondsClockNumberText: seconds => `${seconds} секунд`,\n  // Open picker labels\n  openDatePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Оберіть дату, обрана дата  ${utils.format(value, 'fullDate')}` : 'Оберіть дату',\n  openTimePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Оберіть час, обраний час  ${utils.format(value, 'fullTime')}` : 'Оберіть час',\n  // Table labels\n  timeTableLabel: 'оберіть час',\n  dateTableLabel: 'оберіть дату'\n};\nexport const ukUA = getPickersLocalization(ukUAPickers);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}