{"ast": null, "code": "import { S_UNICODE_REGEX } from '../regex.generated';\nimport { ToRawFixed } from './ToRawFixed';\nimport { digitMapping } from './digit-mapping.generated';\n// This is from: unicode-12.1.0/General_Category/Symbol/regex.js\n// IE11 does not support unicode flag, otherwise this is just /\\p{S}/u.\n// /^\\p{S}/u\nvar CARET_S_UNICODE_REGEX = new RegExp(\"^\".concat(S_UNICODE_REGEX.source));\n// /\\p{S}$/u\nvar S_DOLLAR_UNICODE_REGEX = new RegExp(\"\".concat(S_UNICODE_REGEX.source, \"$\"));\nvar CLDR_NUMBER_PATTERN = /[#0](?:[\\.,][#0]+)*/g;\nexport default function formatToParts(numberResult, data, pl, options) {\n  var sign = numberResult.sign,\n    exponent = numberResult.exponent,\n    magnitude = numberResult.magnitude;\n  var notation = options.notation,\n    style = options.style,\n    numberingSystem = options.numberingSystem;\n  var defaultNumberingSystem = data.numbers.nu[0];\n  // #region Part 1: partition and interpolate the CLDR number pattern.\n  // ----------------------------------------------------------\n  var compactNumberPattern = null;\n  if (notation === 'compact' && magnitude) {\n    compactNumberPattern = getCompactDisplayPattern(numberResult, pl, data, style, options.compactDisplay, options.currencyDisplay, numberingSystem);\n  }\n  // This is used multiple times\n  var nonNameCurrencyPart;\n  if (style === 'currency' && options.currencyDisplay !== 'name') {\n    var byCurrencyDisplay = data.currencies[options.currency];\n    if (byCurrencyDisplay) {\n      switch (options.currencyDisplay) {\n        case 'code':\n          nonNameCurrencyPart = options.currency;\n          break;\n        case 'symbol':\n          nonNameCurrencyPart = byCurrencyDisplay.symbol;\n          break;\n        default:\n          nonNameCurrencyPart = byCurrencyDisplay.narrow;\n          break;\n      }\n    } else {\n      // Fallback for unknown currency\n      nonNameCurrencyPart = options.currency;\n    }\n  }\n  var numberPattern;\n  if (!compactNumberPattern) {\n    // Note: if the style is unit, or is currency and the currency display is name,\n    // its unit parts will be interpolated in part 2. So here we can fallback to decimal.\n    if (style === 'decimal' || style === 'unit' || style === 'currency' && options.currencyDisplay === 'name') {\n      // Shortcut for decimal\n      var decimalData = data.numbers.decimal[numberingSystem] || data.numbers.decimal[defaultNumberingSystem];\n      numberPattern = getPatternForSign(decimalData.standard, sign);\n    } else if (style === 'currency') {\n      var currencyData = data.numbers.currency[numberingSystem] || data.numbers.currency[defaultNumberingSystem];\n      // We replace number pattern part with `0` for easier postprocessing.\n      numberPattern = getPatternForSign(currencyData[options.currencySign], sign);\n    } else {\n      // percent\n      var percentPattern = data.numbers.percent[numberingSystem] || data.numbers.percent[defaultNumberingSystem];\n      numberPattern = getPatternForSign(percentPattern, sign);\n    }\n  } else {\n    numberPattern = compactNumberPattern;\n  }\n  // Extract the decimal number pattern string. It looks like \"#,##0,00\", which will later be\n  // used to infer decimal group sizes.\n  var decimalNumberPattern = CLDR_NUMBER_PATTERN.exec(numberPattern)[0];\n  // Now we start to substitute patterns\n  // 1. replace strings like `0` and `#,##0.00` with `{0}`\n  // 2. unquote characters (invariant: the quoted characters does not contain the special tokens)\n  numberPattern = numberPattern.replace(CLDR_NUMBER_PATTERN, '{0}').replace(/'(.)'/g, '$1');\n  // Handle currency spacing (both compact and non-compact).\n  if (style === 'currency' && options.currencyDisplay !== 'name') {\n    var currencyData = data.numbers.currency[numberingSystem] || data.numbers.currency[defaultNumberingSystem];\n    // See `currencySpacing` substitution rule in TR-35.\n    // Here we always assume the currencyMatch is \"[:^S:]\" and surroundingMatch is \"[:digit:]\".\n    //\n    // Example 1: for pattern \"#,##0.00¤\" with symbol \"US$\", we replace \"¤\" with the symbol,\n    // but insert an extra non-break space before the symbol, because \"[:^S:]\" matches \"U\" in\n    // \"US$\" and \"[:digit:]\" matches the latn numbering system digits.\n    //\n    // Example 2: for pattern \"¤#,##0.00\" with symbol \"US$\", there is no spacing between symbol\n    // and number, because `$` does not match \"[:^S:]\".\n    //\n    // Implementation note: here we do the best effort to infer the insertion.\n    // We also assume that `beforeInsertBetween` and `afterInsertBetween` will never be `;`.\n    var afterCurrency = currencyData.currencySpacing.afterInsertBetween;\n    if (afterCurrency && !S_DOLLAR_UNICODE_REGEX.test(nonNameCurrencyPart)) {\n      numberPattern = numberPattern.replace('¤{0}', \"\\u00A4\".concat(afterCurrency, \"{0}\"));\n    }\n    var beforeCurrency = currencyData.currencySpacing.beforeInsertBetween;\n    if (beforeCurrency && !CARET_S_UNICODE_REGEX.test(nonNameCurrencyPart)) {\n      numberPattern = numberPattern.replace('{0}¤', \"{0}\".concat(beforeCurrency, \"\\u00A4\"));\n    }\n  }\n  // The following tokens are special: `{0}`, `¤`, `%`, `-`, `+`, `{c:...}.\n  var numberPatternParts = numberPattern.split(/({c:[^}]+}|\\{0\\}|[¤%\\-\\+])/g);\n  var numberParts = [];\n  var symbols = data.numbers.symbols[numberingSystem] || data.numbers.symbols[defaultNumberingSystem];\n  for (var _i = 0, numberPatternParts_1 = numberPatternParts; _i < numberPatternParts_1.length; _i++) {\n    var part = numberPatternParts_1[_i];\n    if (!part) {\n      continue;\n    }\n    switch (part) {\n      case '{0}':\n        {\n          // We only need to handle scientific and engineering notation here.\n          numberParts.push.apply(numberParts, paritionNumberIntoParts(symbols, numberResult, notation, exponent, numberingSystem,\n          // If compact number pattern exists, do not insert group separators.\n          !compactNumberPattern && Boolean(options.useGrouping), decimalNumberPattern, style));\n          break;\n        }\n      case '-':\n        numberParts.push({\n          type: 'minusSign',\n          value: symbols.minusSign\n        });\n        break;\n      case '+':\n        numberParts.push({\n          type: 'plusSign',\n          value: symbols.plusSign\n        });\n        break;\n      case '%':\n        numberParts.push({\n          type: 'percentSign',\n          value: symbols.percentSign\n        });\n        break;\n      case '¤':\n        // Computed above when handling currency spacing.\n        numberParts.push({\n          type: 'currency',\n          value: nonNameCurrencyPart\n        });\n        break;\n      default:\n        if (/^\\{c:/.test(part)) {\n          numberParts.push({\n            type: 'compact',\n            value: part.substring(3, part.length - 1)\n          });\n        } else {\n          // literal\n          numberParts.push({\n            type: 'literal',\n            value: part\n          });\n        }\n        break;\n    }\n  }\n  // #endregion\n  // #region Part 2: interpolate unit pattern if necessary.\n  // ----------------------------------------------\n  switch (style) {\n    case 'currency':\n      {\n        // `currencyDisplay: 'name'` has similar pattern handling as units.\n        if (options.currencyDisplay === 'name') {\n          var unitPattern = (data.numbers.currency[numberingSystem] || data.numbers.currency[defaultNumberingSystem]).unitPattern;\n          // Select plural\n          var unitName = void 0;\n          var currencyNameData = data.currencies[options.currency];\n          if (currencyNameData) {\n            unitName = selectPlural(pl, numberResult.roundedNumber * Math.pow(10, exponent), currencyNameData.displayName);\n          } else {\n            // Fallback for unknown currency\n            unitName = options.currency;\n          }\n          // Do {0} and {1} substitution\n          var unitPatternParts = unitPattern.split(/(\\{[01]\\})/g);\n          var result = [];\n          for (var _a = 0, unitPatternParts_1 = unitPatternParts; _a < unitPatternParts_1.length; _a++) {\n            var part = unitPatternParts_1[_a];\n            switch (part) {\n              case '{0}':\n                result.push.apply(result, numberParts);\n                break;\n              case '{1}':\n                result.push({\n                  type: 'currency',\n                  value: unitName\n                });\n                break;\n              default:\n                if (part) {\n                  result.push({\n                    type: 'literal',\n                    value: part\n                  });\n                }\n                break;\n            }\n          }\n          return result;\n        } else {\n          return numberParts;\n        }\n      }\n    case 'unit':\n      {\n        var unit = options.unit,\n          unitDisplay = options.unitDisplay;\n        var unitData = data.units.simple[unit];\n        var unitPattern = void 0;\n        if (unitData) {\n          // Simple unit pattern\n          unitPattern = selectPlural(pl, numberResult.roundedNumber * Math.pow(10, exponent), data.units.simple[unit][unitDisplay]);\n        } else {\n          // See: http://unicode.org/reports/tr35/tr35-general.html#perUnitPatterns\n          // If cannot find unit in the simple pattern, it must be \"per\" compound pattern.\n          // Implementation note: we are not following TR-35 here because we need to format to parts!\n          var _b = unit.split('-per-'),\n            numeratorUnit = _b[0],\n            denominatorUnit = _b[1];\n          unitData = data.units.simple[numeratorUnit];\n          var numeratorUnitPattern = selectPlural(pl, numberResult.roundedNumber * Math.pow(10, exponent), data.units.simple[numeratorUnit][unitDisplay]);\n          var perUnitPattern = data.units.simple[denominatorUnit].perUnit[unitDisplay];\n          if (perUnitPattern) {\n            // perUnitPattern exists, combine it with numeratorUnitPattern\n            unitPattern = perUnitPattern.replace('{0}', numeratorUnitPattern);\n          } else {\n            // get compoundUnit pattern (e.g. \"{0} per {1}\"), repalce {0} with numerator pattern and {1} with\n            // the denominator pattern in singular form.\n            var perPattern = data.units.compound.per[unitDisplay];\n            var denominatorPattern = selectPlural(pl, 1, data.units.simple[denominatorUnit][unitDisplay]);\n            unitPattern = unitPattern = perPattern.replace('{0}', numeratorUnitPattern).replace('{1}', denominatorPattern.replace('{0}', ''));\n          }\n        }\n        var result = [];\n        // We need spacing around \"{0}\" because they are not treated as \"unit\" parts, but \"literal\".\n        for (var _c = 0, _d = unitPattern.split(/(\\s*\\{0\\}\\s*)/); _c < _d.length; _c++) {\n          var part = _d[_c];\n          var interpolateMatch = /^(\\s*)\\{0\\}(\\s*)$/.exec(part);\n          if (interpolateMatch) {\n            // Space before \"{0}\"\n            if (interpolateMatch[1]) {\n              result.push({\n                type: 'literal',\n                value: interpolateMatch[1]\n              });\n            }\n            // \"{0}\" itself\n            result.push.apply(result, numberParts);\n            // Space after \"{0}\"\n            if (interpolateMatch[2]) {\n              result.push({\n                type: 'literal',\n                value: interpolateMatch[2]\n              });\n            }\n          } else if (part) {\n            result.push({\n              type: 'unit',\n              value: part\n            });\n          }\n        }\n        return result;\n      }\n    default:\n      return numberParts;\n  }\n  // #endregion\n}\n// A subset of https://tc39.es/ecma402/#sec-partitionnotationsubpattern\n// Plus the exponent parts handling.\nfunction paritionNumberIntoParts(symbols, numberResult, notation, exponent, numberingSystem, useGrouping,\n/**\n * This is the decimal number pattern without signs or symbols.\n * It is used to infer the group size when `useGrouping` is true.\n *\n * A typical value looks like \"#,##0.00\" (primary group size is 3).\n * Some locales like Hindi has secondary group size of 2 (e.g. \"#,##,##0.00\").\n */\ndecimalNumberPattern, style) {\n  var result = [];\n  // eslint-disable-next-line prefer-const\n  var n = numberResult.formattedString,\n    x = numberResult.roundedNumber;\n  if (isNaN(x)) {\n    return [{\n      type: 'nan',\n      value: n\n    }];\n  } else if (!isFinite(x)) {\n    return [{\n      type: 'infinity',\n      value: n\n    }];\n  }\n  var digitReplacementTable = digitMapping[numberingSystem];\n  if (digitReplacementTable) {\n    n = n.replace(/\\d/g, function (digit) {\n      return digitReplacementTable[+digit] || digit;\n    });\n  }\n  // TODO: Else use an implementation dependent algorithm to map n to the appropriate\n  // representation of n in the given numbering system.\n  var decimalSepIndex = n.indexOf('.');\n  var integer;\n  var fraction;\n  if (decimalSepIndex > 0) {\n    integer = n.slice(0, decimalSepIndex);\n    fraction = n.slice(decimalSepIndex + 1);\n  } else {\n    integer = n;\n  }\n  // #region Grouping integer digits\n  // The weird compact and x >= 10000 check is to ensure consistency with Node.js and Chrome.\n  // Note that `de` does not have compact form for thousands, but Node.js does not insert grouping separator\n  // unless the rounded number is greater than 10000:\n  //   NumberFormat('de', {notation: 'compact', compactDisplay: 'short'}).format(1234) //=> \"1234\"\n  //   NumberFormat('de').format(1234) //=> \"1.234\"\n  if (useGrouping && (notation !== 'compact' || x >= 10000)) {\n    // a. Let groupSepSymbol be the implementation-, locale-, and numbering system-dependent (ILND) String representing the grouping separator.\n    // For currency we should use `currencyGroup` instead of generic `group`\n    var groupSepSymbol = style === 'currency' && symbols.currencyGroup != null ? symbols.currencyGroup : symbols.group;\n    var groups = [];\n    // > There may be two different grouping sizes: The primary grouping size used for the least\n    // > significant integer group, and the secondary grouping size used for more significant groups.\n    // > If a pattern contains multiple grouping separators, the interval between the last one and the\n    // > end of the integer defines the primary grouping size, and the interval between the last two\n    // > defines the secondary grouping size. All others are ignored.\n    var integerNumberPattern = decimalNumberPattern.split('.')[0];\n    var patternGroups = integerNumberPattern.split(',');\n    var primaryGroupingSize = 3;\n    var secondaryGroupingSize = 3;\n    if (patternGroups.length > 1) {\n      primaryGroupingSize = patternGroups[patternGroups.length - 1].length;\n    }\n    if (patternGroups.length > 2) {\n      secondaryGroupingSize = patternGroups[patternGroups.length - 2].length;\n    }\n    var i = integer.length - primaryGroupingSize;\n    if (i > 0) {\n      // Slice the least significant integer group\n      groups.push(integer.slice(i, i + primaryGroupingSize));\n      // Then iteratively push the more signicant groups\n      // TODO: handle surrogate pairs in some numbering system digits\n      for (i -= secondaryGroupingSize; i > 0; i -= secondaryGroupingSize) {\n        groups.push(integer.slice(i, i + secondaryGroupingSize));\n      }\n      groups.push(integer.slice(0, i + secondaryGroupingSize));\n    } else {\n      groups.push(integer);\n    }\n    while (groups.length > 0) {\n      var integerGroup = groups.pop();\n      result.push({\n        type: 'integer',\n        value: integerGroup\n      });\n      if (groups.length > 0) {\n        result.push({\n          type: 'group',\n          value: groupSepSymbol\n        });\n      }\n    }\n  } else {\n    result.push({\n      type: 'integer',\n      value: integer\n    });\n  }\n  // #endregion\n  if (fraction !== undefined) {\n    var decimalSepSymbol = style === 'currency' && symbols.currencyDecimal != null ? symbols.currencyDecimal : symbols.decimal;\n    result.push({\n      type: 'decimal',\n      value: decimalSepSymbol\n    }, {\n      type: 'fraction',\n      value: fraction\n    });\n  }\n  if ((notation === 'scientific' || notation === 'engineering') && isFinite(x)) {\n    result.push({\n      type: 'exponentSeparator',\n      value: symbols.exponential\n    });\n    if (exponent < 0) {\n      result.push({\n        type: 'exponentMinusSign',\n        value: symbols.minusSign\n      });\n      exponent = -exponent;\n    }\n    var exponentResult = ToRawFixed(exponent, 0, 0);\n    result.push({\n      type: 'exponentInteger',\n      value: exponentResult.formattedString\n    });\n  }\n  return result;\n}\nfunction getPatternForSign(pattern, sign) {\n  if (pattern.indexOf(';') < 0) {\n    pattern = \"\".concat(pattern, \";-\").concat(pattern);\n  }\n  var _a = pattern.split(';'),\n    zeroPattern = _a[0],\n    negativePattern = _a[1];\n  switch (sign) {\n    case 0:\n      return zeroPattern;\n    case -1:\n      return negativePattern;\n    default:\n      return negativePattern.indexOf('-') >= 0 ? negativePattern.replace(/-/g, '+') : \"+\".concat(zeroPattern);\n  }\n}\n// Find the CLDR pattern for compact notation based on the magnitude of data and style.\n//\n// Example return value: \"¤ {c:laki}000;¤{c:laki} -0\" (`sw` locale):\n// - Notice the `{c:...}` token that wraps the compact literal.\n// - The consecutive zeros are normalized to single zero to match CLDR_NUMBER_PATTERN.\n//\n// Returning null means the compact display pattern cannot be found.\nfunction getCompactDisplayPattern(numberResult, pl, data, style, compactDisplay, currencyDisplay, numberingSystem) {\n  var _a;\n  var roundedNumber = numberResult.roundedNumber,\n    sign = numberResult.sign,\n    magnitude = numberResult.magnitude;\n  var magnitudeKey = String(Math.pow(10, magnitude));\n  var defaultNumberingSystem = data.numbers.nu[0];\n  var pattern;\n  if (style === 'currency' && currencyDisplay !== 'name') {\n    var byNumberingSystem = data.numbers.currency;\n    var currencyData = byNumberingSystem[numberingSystem] || byNumberingSystem[defaultNumberingSystem];\n    // NOTE: compact notation ignores currencySign!\n    var compactPluralRules = (_a = currencyData.short) === null || _a === void 0 ? void 0 : _a[magnitudeKey];\n    if (!compactPluralRules) {\n      return null;\n    }\n    pattern = selectPlural(pl, roundedNumber, compactPluralRules);\n  } else {\n    var byNumberingSystem = data.numbers.decimal;\n    var byCompactDisplay = byNumberingSystem[numberingSystem] || byNumberingSystem[defaultNumberingSystem];\n    var compactPlaralRule = byCompactDisplay[compactDisplay][magnitudeKey];\n    if (!compactPlaralRule) {\n      return null;\n    }\n    pattern = selectPlural(pl, roundedNumber, compactPlaralRule);\n  }\n  // See https://unicode.org/reports/tr35/tr35-numbers.html#Compact_Number_Formats\n  // > If the value is precisely “0”, either explicit or defaulted, then the normal number format\n  // > pattern for that sort of object is supplied.\n  if (pattern === '0') {\n    return null;\n  }\n  pattern = getPatternForSign(pattern, sign)\n  // Extract compact literal from the pattern\n  .replace(/([^\\s;\\-\\+\\d¤]+)/g, '{c:$1}')\n  // We replace one or more zeros with a single zero so it matches `CLDR_NUMBER_PATTERN`.\n  .replace(/0+/, '0');\n  return pattern;\n}\nfunction selectPlural(pl, x, rules) {\n  return rules[pl.select(x)] || rules.other;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}