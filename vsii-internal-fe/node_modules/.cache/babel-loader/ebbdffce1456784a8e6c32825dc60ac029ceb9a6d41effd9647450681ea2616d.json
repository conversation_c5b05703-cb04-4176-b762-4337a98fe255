{"ast": null, "code": "// project imports\nimport{EApproveStatus,paginationParamDefault}from'constants/Common';import{VALIDATE_MESSAGES}from'constants/Message';// third party\nimport*as yup from'yup';// ============== Manage leaves ============== //\n// Filter\nexport const manageLeavesDefaultValues={...paginationParamDefault,memberId:null,fromDate:null,toDate:null,type:'',dept:'',status:''};export const manageLeavesSearchSchema=yup.object().shape({fromDate:yup.date().nullable(),toDate:yup.date().nullable().min(yup.ref('fromDate'),VALIDATE_MESSAGES.ENDDATE),type:yup.string().nullable(),status:yup.string().nullable()});// Add or edit\nexport const addOrEditLeavesDefaultValues={id:'',member:null,directApproverIdHexString:'',nextApproverIdHexString:'',fromDate:null,toDate:null,leaveDetails:[{leaveType:'',numberOfDays:0,fromDate:null,toDate:null,note:'',hasError:false}]};export const addOrEditLeavesSchema=yup.object().shape({member:yup.object().shape({value:yup.string(),label:yup.string()}).nullable().required(VALIDATE_MESSAGES.REQUIRED),indirectManagement:yup.string().nullable(),directApproverIdHexString:yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),nextApproverIdHexString:yup.string().test('nextManagerApprove-validation',VALIDATE_MESSAGES.REQUIRED,function(value){const{status}=this.parent;const{currentUserId}=this.options.context||{};const isCreator=this.parent.member.value===currentUserId;const shouldValidate=status===EApproveStatus.AWAITING_QLTT&&!isCreator;return shouldValidate?!!value:true;}),requestDate:yup.date().nullable().required(VALIDATE_MESSAGES.REQUIRED),leaveDetails:yup.array().of(yup.object().shape({leaveType:yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),fromDate:yup.date().nullable().required(VALIDATE_MESSAGES.REQUIRED),toDate:yup.date().nullable().required(VALIDATE_MESSAGES.REQUIRED).min(yup.ref('fromDate'),VALIDATE_MESSAGES.AFTER_DAY),numberOfDays:yup.number(),note:yup.string().nullable(),hasError:yup.boolean().test('no-validation-error',VALIDATE_MESSAGES.REQUIRED,function(value){return value===false;})})).min(1,VALIDATE_MESSAGES.REQUIRED).test('date-sequence',VALIDATE_MESSAGES.LEAVE_SEQUENCE,function(leaveDetails){if(!Array.isArray(leaveDetails))return true;for(let i=1;i<leaveDetails.length;i++){const prev=leaveDetails[i-1];const current=leaveDetails[i];if(prev!==null&&prev!==void 0&&prev.toDate&&current!==null&&current!==void 0&&current.fromDate){const prevTo=new Date(prev.toDate);const currFrom=new Date(current.fromDate);if(currFrom<=prevTo){return this.createError({path:\"leaveDetails[\".concat(i,\"].fromDate\"),message:VALIDATE_MESSAGES.LEAVE_SEQUENCE});}}}return true;})});// Reject Leave Request\nexport const rejectLeaveRequestConfig={reason:'',fromDate:new Date(),toDate:new Date(),fullName:'',totalDays:''};export const rejectLeaveRequestSchema=yup.object().shape({reason:yup.string().required(VALIDATE_MESSAGES.REQUIRED),fullName:yup.string().required(VALIDATE_MESSAGES.REQUIRED)});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}