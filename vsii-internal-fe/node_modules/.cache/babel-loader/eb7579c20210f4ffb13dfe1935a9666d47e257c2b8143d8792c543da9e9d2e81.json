{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"collapseIcon\", \"ContentComponent\", \"ContentProps\", \"endIcon\", \"expandIcon\", \"disabled\", \"icon\", \"id\", \"label\", \"nodeId\", \"onClick\", \"onMouseDown\", \"TransitionComponent\", \"TransitionProps\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { elementTypeAcceptingRef } from '@mui/utils';\nimport Collapse from '@mui/material/Collapse';\nimport { alpha, styled, useThemeProps } from '@mui/material/styles';\nimport { ownerDocument, useForkRef, unsupportedProp } from '@mui/material/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport TreeViewContext from '../TreeView/TreeViewContext';\nimport { DescendantProvider, useDescendant } from '../TreeView/descendants';\nimport TreeItemContent from './TreeItemContent';\nimport treeItemClasses, { getTreeItemUtilityClass } from './treeItemClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    content: ['content'],\n    expanded: ['expanded'],\n    selected: ['selected'],\n    focused: ['focused'],\n    disabled: ['disabled'],\n    iconContainer: ['iconContainer'],\n    label: ['label'],\n    group: ['group']\n  };\n  return composeClasses(slots, getTreeItemUtilityClass, classes);\n};\nconst TreeItemRoot = styled('li', {\n  name: 'MuiTreeItem',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  listStyle: 'none',\n  margin: 0,\n  padding: 0,\n  outline: 0\n});\nconst StyledTreeItemContent = styled(TreeItemContent, {\n  name: 'MuiTreeItem',\n  slot: 'Content',\n  overridesResolver: (props, styles) => {\n    return [styles.content, styles.iconContainer && {\n      [\"& .\".concat(treeItemClasses.iconContainer)]: styles.iconContainer\n    }, styles.label && {\n      [\"& .\".concat(treeItemClasses.label)]: styles.label\n    }];\n  }\n})(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    padding: '0 8px',\n    width: '100%',\n    display: 'flex',\n    alignItems: 'center',\n    cursor: 'pointer',\n    WebkitTapHighlightColor: 'transparent',\n    '&:hover': {\n      backgroundColor: (theme.vars || theme).palette.action.hover,\n      // Reset on touch devices, it doesn't add specificity\n      '@media (hover: none)': {\n        backgroundColor: 'transparent'\n      }\n    },\n    [\"&.\".concat(treeItemClasses.disabled)]: {\n      opacity: (theme.vars || theme).palette.action.disabledOpacity,\n      backgroundColor: 'transparent'\n    },\n    [\"&.\".concat(treeItemClasses.focused)]: {\n      backgroundColor: (theme.vars || theme).palette.action.focus\n    },\n    [\"&.\".concat(treeItemClasses.selected)]: {\n      backgroundColor: theme.vars ? \"rgba(\".concat(theme.vars.palette.primary.mainChannel, \" / \").concat(theme.vars.palette.action.selectedOpacity, \")\") : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity),\n      '&:hover': {\n        backgroundColor: theme.vars ? \"rgba(\".concat(theme.vars.palette.primary.mainChannel, \" / calc(\").concat(theme.vars.palette.action.selectedOpacity, \" + \").concat(theme.vars.palette.action.hoverOpacity, \"))\") : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.hoverOpacity),\n        // Reset on touch devices, it doesn't add specificity\n        '@media (hover: none)': {\n          backgroundColor: theme.vars ? \"rgba(\".concat(theme.vars.palette.primary.mainChannel, \" / \").concat(theme.vars.palette.action.selectedOpacity, \")\") : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity)\n        }\n      },\n      [\"&.\".concat(treeItemClasses.focused)]: {\n        backgroundColor: theme.vars ? \"rgba(\".concat(theme.vars.palette.primary.mainChannel, \" / calc(\").concat(theme.vars.palette.action.selectedOpacity, \" + \").concat(theme.vars.palette.action.focusOpacity, \"))\") : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n      }\n    },\n    [\"& .\".concat(treeItemClasses.iconContainer)]: {\n      marginRight: 4,\n      width: 15,\n      display: 'flex',\n      flexShrink: 0,\n      justifyContent: 'center',\n      '& svg': {\n        fontSize: 18\n      }\n    },\n    [\"& .\".concat(treeItemClasses.label)]: _extends({\n      width: '100%',\n      // fixes overflow - see https://github.com/mui/material-ui/issues/27372\n      minWidth: 0,\n      paddingLeft: 4,\n      position: 'relative'\n    }, theme.typography.body1)\n  };\n});\nconst TreeItemGroup = styled(Collapse, {\n  name: 'MuiTreeItem',\n  slot: 'Group',\n  overridesResolver: (props, styles) => styles.group\n})({\n  margin: 0,\n  padding: 0,\n  marginLeft: 17\n});\nconst TreeItem = /*#__PURE__*/React.forwardRef(function TreeItem(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiTreeItem'\n  });\n  const {\n      children,\n      className,\n      collapseIcon,\n      ContentComponent = TreeItemContent,\n      ContentProps,\n      endIcon,\n      expandIcon,\n      disabled: disabledProp,\n      icon,\n      id: idProp,\n      label,\n      nodeId,\n      onClick,\n      onMouseDown,\n      TransitionComponent = Collapse,\n      TransitionProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    icons: contextIcons = {},\n    focus,\n    isExpanded,\n    isFocused,\n    isSelected,\n    isDisabled,\n    multiSelect,\n    disabledItemsFocusable,\n    mapFirstChar,\n    unMapFirstChar,\n    registerNode,\n    unregisterNode,\n    treeId\n  } = React.useContext(TreeViewContext);\n  let id = null;\n  if (idProp != null) {\n    id = idProp;\n  } else if (treeId && nodeId) {\n    id = \"\".concat(treeId, \"-\").concat(nodeId);\n  }\n  const [treeitemElement, setTreeitemElement] = React.useState(null);\n  const contentRef = React.useRef(null);\n  const handleRef = useForkRef(setTreeitemElement, ref);\n  const descendant = React.useMemo(() => ({\n    element: treeitemElement,\n    id: nodeId\n  }), [nodeId, treeitemElement]);\n  const {\n    index,\n    parentId\n  } = useDescendant(descendant);\n  const expandable = Boolean(Array.isArray(children) ? children.length : children);\n  const expanded = isExpanded ? isExpanded(nodeId) : false;\n  const focused = isFocused ? isFocused(nodeId) : false;\n  const selected = isSelected ? isSelected(nodeId) : false;\n  const disabled = isDisabled ? isDisabled(nodeId) : false;\n  const ownerState = _extends({}, props, {\n    expanded,\n    focused,\n    selected,\n    disabled\n  });\n  const classes = useUtilityClasses(ownerState);\n  let displayIcon;\n  let expansionIcon;\n  if (expandable) {\n    if (!expanded) {\n      expansionIcon = expandIcon || contextIcons.defaultExpandIcon;\n    } else {\n      expansionIcon = collapseIcon || contextIcons.defaultCollapseIcon;\n    }\n  }\n  if (expandable) {\n    displayIcon = contextIcons.defaultParentIcon;\n  } else {\n    displayIcon = endIcon || contextIcons.defaultEndIcon;\n  }\n  React.useEffect(() => {\n    // On the first render a node's index will be -1. We want to wait for the real index.\n    if (registerNode && unregisterNode && index !== -1) {\n      registerNode({\n        id: nodeId,\n        idAttribute: id,\n        index,\n        parentId,\n        expandable,\n        disabled: disabledProp\n      });\n      return () => {\n        unregisterNode(nodeId);\n      };\n    }\n    return undefined;\n  }, [registerNode, unregisterNode, parentId, index, nodeId, expandable, disabledProp, id]);\n  React.useEffect(() => {\n    if (mapFirstChar && unMapFirstChar && label) {\n      mapFirstChar(nodeId, contentRef.current.textContent.substring(0, 1).toLowerCase());\n      return () => {\n        unMapFirstChar(nodeId);\n      };\n    }\n    return undefined;\n  }, [mapFirstChar, unMapFirstChar, nodeId, label]);\n  let ariaSelected;\n  if (multiSelect) {\n    ariaSelected = selected;\n  } else if (selected) {\n    /* single-selection trees unset aria-selected on un-selected items.\n     *\n     * If the tree does not support multiple selection, aria-selected\n     * is set to true for the selected node and it is not present on any other node in the tree.\n     * Source: https://www.w3.org/WAI/ARIA/apg/patterns/treeview/\n     */\n    ariaSelected = true;\n  }\n  function handleFocus(event) {\n    // DOM focus stays on the tree which manages focus with aria-activedescendant\n    if (event.target === event.currentTarget) {\n      ownerDocument(event.target).getElementById(treeId).focus({\n        preventScroll: true\n      });\n    }\n    const unfocusable = !disabledItemsFocusable && disabled;\n    if (!focused && event.currentTarget === event.target && !unfocusable) {\n      focus(event, nodeId);\n    }\n  }\n  return /*#__PURE__*/_jsxs(TreeItemRoot, _extends({\n    className: clsx(classes.root, className),\n    role: \"treeitem\",\n    \"aria-expanded\": expandable ? expanded : null,\n    \"aria-selected\": ariaSelected,\n    \"aria-disabled\": disabled || null,\n    ref: handleRef,\n    id: id,\n    tabIndex: -1\n  }, other, {\n    ownerState: ownerState,\n    onFocus: handleFocus,\n    children: [/*#__PURE__*/_jsx(StyledTreeItemContent, _extends({\n      as: ContentComponent,\n      ref: contentRef,\n      classes: {\n        root: classes.content,\n        expanded: classes.expanded,\n        selected: classes.selected,\n        focused: classes.focused,\n        disabled: classes.disabled,\n        iconContainer: classes.iconContainer,\n        label: classes.label\n      },\n      label: label,\n      nodeId: nodeId,\n      onClick: onClick,\n      onMouseDown: onMouseDown,\n      icon: icon,\n      expansionIcon: expansionIcon,\n      displayIcon: displayIcon,\n      ownerState: ownerState\n    }, ContentProps)), children && /*#__PURE__*/_jsx(DescendantProvider, {\n      id: nodeId,\n      children: /*#__PURE__*/_jsx(TreeItemGroup, _extends({\n        as: TransitionComponent,\n        unmountOnExit: true,\n        className: classes.group,\n        in: expanded,\n        component: \"ul\",\n        role: \"group\"\n      }, TransitionProps, {\n        children: children\n      }))\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TreeItem.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The icon used to collapse the node.\n   */\n  collapseIcon: PropTypes.node,\n  /**\n   * The component used for the content node.\n   * @default TreeItemContent\n   */\n  ContentComponent: elementTypeAcceptingRef,\n  /**\n   * Props applied to ContentComponent\n   */\n  ContentProps: PropTypes.object,\n  /**\n   * If `true`, the node is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * The icon displayed next to a end node.\n   */\n  endIcon: PropTypes.node,\n  /**\n   * The icon used to expand the node.\n   */\n  expandIcon: PropTypes.node,\n  /**\n   * The icon to display next to the tree node's label.\n   */\n  icon: PropTypes.node,\n  /**\n   * @ignore\n   */\n  id: PropTypes.string,\n  /**\n   * The tree node label.\n   */\n  label: PropTypes.node,\n  /**\n   * The id of the node.\n   */\n  nodeId: PropTypes.string.isRequired,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * This prop isn't supported.\n   * Use the `onNodeFocus` callback on the tree if you need to monitor a node's focus.\n   */\n  onFocus: unsupportedProp,\n  /**\n   * @ignore\n   */\n  onMouseDown: PropTypes.func,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Collapse\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](http://reactcommunity.org/react-transition-group/transition/) component.\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default TreeItem;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}