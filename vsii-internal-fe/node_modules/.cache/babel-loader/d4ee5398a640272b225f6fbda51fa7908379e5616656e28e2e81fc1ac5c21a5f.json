{"ast": null, "code": "import{useSearchParams}from'react-router-dom';import{FormattedMessage}from'react-intl';import{Grid}from'@mui/material';import{FormProvider,Input,Label}from'components/extended/Form';import{searchFormConfig}from'containers/search/Config';import{transformObject}from'utils/common';import{Button}from'components';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DepartmentSearch=_ref=>{let{conditions,setConditions}=_ref;const[,setSearchParams]=useSearchParams();const{manage_department}=TEXT_CONFIG_SCREEN.administration;const handleSearch=value=>{const newValue=transformObject({...value,page:1});setSearchParams(newValue);setConditions(newValue);};return/*#__PURE__*/_jsx(FormProvider,{form:{defaultValues:conditions},onSubmit:handleSearch,children:/*#__PURE__*/_jsxs(Grid,{container:true,justifyContent:\"space-between\",children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:3,children:/*#__PURE__*/_jsx(Input,{name:searchFormConfig.department.manage.name,label:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_department+'department'})})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,lg:2,children:[/*#__PURE__*/_jsx(Label,{label:\"\\xA0\"}),/*#__PURE__*/_jsx(Button,{type:\"submit\",size:\"medium\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_department+'search'}),variant:\"contained\"})]})]})});};export default DepartmentSearch;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}