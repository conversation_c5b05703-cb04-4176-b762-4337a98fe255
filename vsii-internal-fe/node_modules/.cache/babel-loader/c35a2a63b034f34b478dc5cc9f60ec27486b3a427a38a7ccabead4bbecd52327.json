{"ast": null, "code": "import{Box,Grid,Typography}from'@mui/material';import{FormattedMessage}from'react-intl';import{Months,Project,Years}from'containers/search';import{TEXT_CONFIG_SCREEN}from'constants/Common';import MilestoneListTable from'./MilestoneListTable';import{Input}from'components/extended/Form';import{gridSpacing}from'store/constant';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ProjectReportInfo=_ref=>{let{methods,disabledProject,handleChangeYear,handleMonthChange,handleChangeProject,months,month,disableEdit,setProjects}=_ref;const{project_report}=TEXT_CONFIG_SCREEN.generalReport;return/*#__PURE__*/_jsxs(Grid,{container:true,spacing:gridSpacing,children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:7,sx:{display:'flex',flexDirection:'column',gap:'16px'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Typography,{sx:{flexBasis:'30%',marginRight:'16px',fontSize:'14px',color:'#333'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:project_report+'year'})}),/*#__PURE__*/_jsx(Box,{sx:{flexBasis:'70%'},children:/*#__PURE__*/_jsx(Years,{disableLabel:true,name:\"projectReportInfo.year\",handleChangeYear:handleChangeYear,disabled:disableEdit})})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Typography,{sx:{flexBasis:'30%',marginRight:'16px',fontSize:'14px',color:'#333'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:project_report+'month'})}),/*#__PURE__*/_jsx(Box,{sx:{flexBasis:'70%'},children:/*#__PURE__*/_jsx(Months,{name:\"projectReportInfo.month\",disabledLabel:true,months:months,onChange:handleMonthChange,disabled:disableEdit})})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Typography,{sx:{flexBasis:'30%',marginRight:'16px',fontSize:'14px',color:'#333'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:project_report+'projects'})}),/*#__PURE__*/_jsx(Box,{sx:{flexBasis:'70%'},children:/*#__PURE__*/_jsx(Project,{disabled:disabledProject,isDefaultAll:true,month:month,name:\"projectReportInfo.projectId\",isNotStatus:true,handleChange:handleChangeProject,label:\"\",setProjectsAll:setProjects})})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Typography,{sx:{flexBasis:'30%',marginRight:'16px',fontSize:'14px',color:'#333'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:project_report+'project-type'})}),/*#__PURE__*/_jsx(Box,{sx:{flexBasis:'70%'},children:/*#__PURE__*/_jsx(Input,{disabled:true,name:\"projectReportInfo.projectType\"})})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Typography,{sx:{flexBasis:'30%',marginRight:'16px',fontSize:'14px',color:'#333'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:project_report+'project-manager'})}),/*#__PURE__*/_jsx(Box,{sx:{flexBasis:'70%'},children:/*#__PURE__*/_jsx(Input,{disabled:true,name:\"projectReportInfo.userNamePM\"})})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Typography,{sx:{flexBasis:'30%',marginRight:'16px',fontSize:'14px',color:'#333'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:project_report+'start-date'})}),/*#__PURE__*/_jsx(Box,{sx:{flexBasis:'70%'},children:/*#__PURE__*/_jsx(Input,{disabled:true,name:\"projectReportInfo.startDate\"})})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Typography,{sx:{flexBasis:'30%',marginRight:'16px',fontSize:'14px',color:'#333'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:project_report+'end-date'})}),/*#__PURE__*/_jsx(Box,{sx:{flexBasis:'70%'},children:/*#__PURE__*/_jsx(Input,{disabled:true,name:\"projectReportInfo.endDate\"})})]})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,children:[/*#__PURE__*/_jsx(Typography,{sx:theme=>({color:theme.palette.primary.main,fontWeight:600}),mb:2,mt:2,children:/*#__PURE__*/_jsx(FormattedMessage,{id:project_report+'milestone-list-approved-by-bod-customer'})}),/*#__PURE__*/_jsx(MilestoneListTable,{methods:methods,disabled:disableEdit})]})]});};export default ProjectReportInfo;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}