{"ast": null, "code": "// material-ui\nimport{TableCell,TableHead,TableRow}from'@mui/material';import{Checkbox}from'components/extended/Form';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{FormattedMessage}from'react-intl';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function WeeklyEffortProjectDetailThead(props){const{handleCheckAll,isCheckAll,isSomeSelected}=props;const{Weeklyeffort}=TEXT_CONFIG_SCREEN;return/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Checkbox,{name:\"checkAll\",isControl:false,handleChange:handleCheckAll,valueChecked:isCheckAll,indeterminate:isSomeSelected})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:Weeklyeffort+'members'})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(FormattedMessage,{id:Weeklyeffort+'member-code'})}),/*#__PURE__*/_jsxs(TableCell,{sx:{whiteSpace:'nowrap'},children:[/*#__PURE__*/_jsx(FormattedMessage,{id:Weeklyeffort+'effort-pm-verified'}),\" /\",/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(FormattedMessage,{id:Weeklyeffort+'effort-in-week-hours'})]}),/*#__PURE__*/_jsxs(TableCell,{sx:{whiteSpace:'nowrap'},children:[/*#__PURE__*/_jsx(FormattedMessage,{id:Weeklyeffort+'payable-ot-verified'}),\" /\",/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(FormattedMessage,{id:\"payable-ot\"})]}),/*#__PURE__*/_jsxs(TableCell,{sx:{whiteSpace:'nowrap'},children:[/*#__PURE__*/_jsx(FormattedMessage,{id:Weeklyeffort+'non-payable-ot-verified'}),\" /\",/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(FormattedMessage,{id:Weeklyeffort+'non-payable-ot'})]}),/*#__PURE__*/_jsx(TableCell,{sx:{whiteSpace:'nowrap'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:Weeklyeffort+'pm-verifed'})}),/*#__PURE__*/_jsx(TableCell,{sx:{whiteSpace:'nowrap'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:Weeklyeffort+'pm-verified-date'})}),/*#__PURE__*/_jsx(TableCell,{sx:{whiteSpace:'nowrap'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:Weeklyeffort+'qa-verifed'})}),/*#__PURE__*/_jsx(TableCell,{sx:{whiteSpace:'nowrap'},children:/*#__PURE__*/_jsx(FormattedMessage,{id:Weeklyeffort+'qa-verified-date'})})]})});}export default WeeklyEffortProjectDetailThead;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}