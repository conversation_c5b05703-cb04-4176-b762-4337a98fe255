{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/list-project-team/ListProjectTeamTBody.tsx\";\n// material-ui\nimport { TableBody, TableCell, TableRow, IconButton, Stack, Tooltip } from '@mui/material';\nimport SpeakerNotesIcon from '@mui/icons-material/SpeakerNotes';\n\n// project imports\n\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { PERMISSIONS } from 'constants/Permission';\n\n// import { IconCheck } from '@tabler/icons';\nimport { formatTableCellMemberInProject } from 'utils/common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// const isNotLogTime = (notLogTime: boolean) => {\n//     return notLogTime ? <IconCheck /> : '';\n// };\n\nconst ListProjectTeamTBody = props => {\n  const {\n    pageNumber,\n    pageSize,\n    projectTeam,\n    handleOpenCommentDialog\n  } = props;\n  const {\n    ResourcesInProjects\n  } = PERMISSIONS.report;\n  return /*#__PURE__*/_jsxDEV(TableBody, {\n    children: projectTeam.map((member, key) => {\n      var _member$comment;\n      return /*#__PURE__*/_jsxDEV(TableRow, {\n        children: [/*#__PURE__*/_jsxDEV(TableCell, {\n          children: pageSize * pageNumber + key + 1\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          children: member.memberCode\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          children: [member.firstName, \" \", member.lastName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          children: member.userTitle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          align: \"center\",\n          children: member.userDept\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          children: formatTableCellMemberInProject(member.mainHeadCount)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          children: formatTableCellMemberInProject(member.subHeadCount)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n          children: formatTableCellMemberInProject(member.projectNonBillable)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 21\n        }, this), checkAllowedPermission(ResourcesInProjects.commentDetail) && /*#__PURE__*/_jsxDEV(TableCell, {\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            children: /*#__PURE__*/_jsxDEV(Tooltip, {\n              placement: \"top\",\n              title: (_member$comment = member.comment) === null || _member$comment === void 0 ? void 0 : _member$comment.note,\n              onClick: () => handleOpenCommentDialog(member.userId, `${member.firstName} ${member.lastName}`),\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                \"aria-label\": \"comment\",\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(SpeakerNotesIcon, {\n                  sx: {\n                    fontSize: '1.1rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 51,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 25\n        }, this)]\n      }, key, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this);\n    })\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 9\n  }, this);\n};\n_c = ListProjectTeamTBody;\nexport default ListProjectTeamTBody;\nvar _c;\n$RefreshReg$(_c, \"ListProjectTeamTBody\");", "map": {"version": 3, "names": ["TableBody", "TableCell", "TableRow", "IconButton", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "SpeakerNotesIcon", "checkAllowedPermission", "PERMISSIONS", "formatTableCellMemberInProject", "jsxDEV", "_jsxDEV", "ListProjectTeamTBody", "props", "pageNumber", "pageSize", "projectTeam", "handleOpenCommentDialog", "ResourcesInProjects", "report", "children", "map", "member", "key", "_member$comment", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "memberCode", "firstName", "lastName", "userTitle", "align", "userDept", "mainHeadCount", "subHeadCount", "projectNonBillable", "commentDetail", "direction", "justifyContent", "alignItems", "placement", "title", "comment", "note", "onClick", "userId", "size", "sx", "fontSize", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/list-project-team/ListProjectTeamTBody.tsx"], "sourcesContent": ["// material-ui\nimport { TableBody, TableCell, TableRow, IconButton, Stack, Tooltip } from '@mui/material';\nimport SpeakerNotesIcon from '@mui/icons-material/SpeakerNotes';\n\n// project imports\nimport { IProjectTeam } from 'types';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { PERMISSIONS } from 'constants/Permission';\n\n// import { IconCheck } from '@tabler/icons';\nimport { formatTableCellMemberInProject } from 'utils/common';\n\ninterface IListProjectTeamTBodyProps {\n    pageNumber: number;\n    pageSize: number;\n    projectTeam: IProjectTeam[];\n    handleOpenCommentDialog: (userId?: string, subTitle?: string) => void;\n}\n\n// const isNotLogTime = (notLogTime: boolean) => {\n//     return notLogTime ? <IconCheck /> : '';\n// };\n\nconst ListProjectTeamTBody = (props: IListProjectTeamTBodyProps) => {\n    const { pageNumber, pageSize, projectTeam, handleOpenCommentDialog } = props;\n    const { ResourcesInProjects } = PERMISSIONS.report;\n\n    return (\n        <TableBody>\n            {projectTeam.map((member, key) => (\n                <TableRow key={key}>\n                    <TableCell>{pageSize * pageNumber + key + 1}</TableCell>\n                    <TableCell>{member.memberCode}</TableCell>\n                    <TableCell>\n                        {member.firstName} {member.lastName}\n                    </TableCell>\n                    <TableCell>{member.userTitle}</TableCell>\n                    <TableCell align=\"center\">{member.userDept}</TableCell>\n                    <TableCell>{formatTableCellMemberInProject(member.mainHeadCount)}</TableCell>\n                    <TableCell>{formatTableCellMemberInProject(member.subHeadCount)}</TableCell>\n                    <TableCell>{formatTableCellMemberInProject(member.projectNonBillable)}</TableCell>\n                    {checkAllowedPermission(ResourcesInProjects.commentDetail) && (\n                        <TableCell>\n                            <Stack direction=\"row\" justifyContent=\"center\" alignItems=\"center\">\n                                <Tooltip\n                                    placement=\"top\"\n                                    title={member.comment?.note}\n                                    onClick={() => handleOpenCommentDialog(member.userId, `${member.firstName} ${member.lastName}`)}\n                                >\n                                    <IconButton aria-label=\"comment\" size=\"small\">\n                                        <SpeakerNotesIcon sx={{ fontSize: '1.1rem' }} />\n                                    </IconButton>\n                                </Tooltip>\n                            </Stack>\n                        </TableCell>\n                    )}\n\n                    {/* <TableCell align=\"center\">{isNotLogTime(member.notLogTime)}</TableCell> */}\n                </TableRow>\n            ))}\n        </TableBody>\n    );\n};\n\nexport default ListProjectTeamTBody;\n"], "mappings": ";AAAA;AACA,SAASA,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,QAAQ,eAAe;AAC1F,OAAOC,gBAAgB,MAAM,kCAAkC;;AAE/D;;AAEA,SAASC,sBAAsB,QAAQ,qBAAqB;AAC5D,SAASC,WAAW,QAAQ,sBAAsB;;AAElD;AACA,SAASC,8BAA8B,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAS9D;AACA;AACA;;AAEA,MAAMC,oBAAoB,GAAIC,KAAiC,IAAK;EAChE,MAAM;IAAEC,UAAU;IAAEC,QAAQ;IAAEC,WAAW;IAAEC;EAAwB,CAAC,GAAGJ,KAAK;EAC5E,MAAM;IAAEK;EAAoB,CAAC,GAAGV,WAAW,CAACW,MAAM;EAElD,oBACIR,OAAA,CAACX,SAAS;IAAAoB,QAAA,EACLJ,WAAW,CAACK,GAAG,CAAC,CAACC,MAAM,EAAEC,GAAG;MAAA,IAAAC,eAAA;MAAA,oBACzBb,OAAA,CAACT,QAAQ;QAAAkB,QAAA,gBACLT,OAAA,CAACV,SAAS;UAAAmB,QAAA,EAAEL,QAAQ,GAAGD,UAAU,GAAGS,GAAG,GAAG;QAAC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACxDjB,OAAA,CAACV,SAAS;UAAAmB,QAAA,EAAEE,MAAM,CAACO;QAAU;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC1CjB,OAAA,CAACV,SAAS;UAAAmB,QAAA,GACLE,MAAM,CAACQ,SAAS,EAAC,GAAC,EAACR,MAAM,CAACS,QAAQ;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACZjB,OAAA,CAACV,SAAS;UAAAmB,QAAA,EAAEE,MAAM,CAACU;QAAS;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACzCjB,OAAA,CAACV,SAAS;UAACgC,KAAK,EAAC,QAAQ;UAAAb,QAAA,EAAEE,MAAM,CAACY;QAAQ;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACvDjB,OAAA,CAACV,SAAS;UAAAmB,QAAA,EAAEX,8BAA8B,CAACa,MAAM,CAACa,aAAa;QAAC;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC7EjB,OAAA,CAACV,SAAS;UAAAmB,QAAA,EAAEX,8BAA8B,CAACa,MAAM,CAACc,YAAY;QAAC;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC5EjB,OAAA,CAACV,SAAS;UAAAmB,QAAA,EAAEX,8BAA8B,CAACa,MAAM,CAACe,kBAAkB;QAAC;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACjFrB,sBAAsB,CAACW,mBAAmB,CAACoB,aAAa,CAAC,iBACtD3B,OAAA,CAACV,SAAS;UAAAmB,QAAA,eACNT,OAAA,CAACP,KAAK;YAACmC,SAAS,EAAC,KAAK;YAACC,cAAc,EAAC,QAAQ;YAACC,UAAU,EAAC,QAAQ;YAAArB,QAAA,eAC9DT,OAAA,CAACN,OAAO;cACJqC,SAAS,EAAC,KAAK;cACfC,KAAK,GAAAnB,eAAA,GAAEF,MAAM,CAACsB,OAAO,cAAApB,eAAA,uBAAdA,eAAA,CAAgBqB,IAAK;cAC5BC,OAAO,EAAEA,CAAA,KAAM7B,uBAAuB,CAACK,MAAM,CAACyB,MAAM,EAAE,GAAGzB,MAAM,CAACQ,SAAS,IAAIR,MAAM,CAACS,QAAQ,EAAE,CAAE;cAAAX,QAAA,eAEhGT,OAAA,CAACR,UAAU;gBAAC,cAAW,SAAS;gBAAC6C,IAAI,EAAC,OAAO;gBAAA5B,QAAA,eACzCT,OAAA,CAACL,gBAAgB;kBAAC2C,EAAE,EAAE;oBAAEC,QAAQ,EAAE;kBAAS;gBAAE;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACd;MAAA,GAzBUL,GAAG;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4BR,CAAC;IAAA,CACd;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEpB,CAAC;AAACuB,EAAA,GAvCIvC,oBAAoB;AAyC1B,eAAeA,oBAAoB;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}