{"ast": null, "code": "import { isKeyframesTarget } from '../../../animation/utils/is-keyframes-target.mjs';\nimport { invariant } from 'hey-listen';\nimport { transformPropOrder } from '../../html/utils/transform.mjs';\nimport { findDimensionValueType } from '../value-types/dimensions.mjs';\nimport { isBrowser } from '../../../utils/is-browser.mjs';\nimport { number } from '../../../value/types/numbers/index.mjs';\nimport { px } from '../../../value/types/numbers/units.mjs';\nconst positionalKeys = new Set([\"width\", \"height\", \"top\", \"left\", \"right\", \"bottom\", \"x\", \"y\"]);\nconst isPositionalKey = key => positionalKeys.has(key);\nconst hasPositionalKey = target => {\n  return Object.keys(target).some(isPositionalKey);\n};\nconst setAndResetVelocity = (value, to) => {\n  // Looks odd but setting it twice doesn't render, it'll just\n  // set both prev and current to the latest value\n  value.set(to, false);\n  value.set(to);\n};\nconst isNumOrPxType = v => v === number || v === px;\nvar BoundingBoxDimension;\n(function (BoundingBoxDimension) {\n  BoundingBoxDimension[\"width\"] = \"width\";\n  BoundingBoxDimension[\"height\"] = \"height\";\n  BoundingBoxDimension[\"left\"] = \"left\";\n  BoundingBoxDimension[\"right\"] = \"right\";\n  BoundingBoxDimension[\"top\"] = \"top\";\n  BoundingBoxDimension[\"bottom\"] = \"bottom\";\n})(BoundingBoxDimension || (BoundingBoxDimension = {}));\nconst getPosFromMatrix = (matrix, pos) => parseFloat(matrix.split(\", \")[pos]);\nconst getTranslateFromMatrix = (pos2, pos3) => (_bbox, {\n  transform\n}) => {\n  if (transform === \"none\" || !transform) return 0;\n  const matrix3d = transform.match(/^matrix3d\\((.+)\\)$/);\n  if (matrix3d) {\n    return getPosFromMatrix(matrix3d[1], pos3);\n  } else {\n    const matrix = transform.match(/^matrix\\((.+)\\)$/);\n    if (matrix) {\n      return getPosFromMatrix(matrix[1], pos2);\n    } else {\n      return 0;\n    }\n  }\n};\nconst transformKeys = new Set([\"x\", \"y\", \"z\"]);\nconst nonTranslationalTransformKeys = transformPropOrder.filter(key => !transformKeys.has(key));\nfunction removeNonTranslationalTransform(visualElement) {\n  const removedTransforms = [];\n  nonTranslationalTransformKeys.forEach(key => {\n    const value = visualElement.getValue(key);\n    if (value !== undefined) {\n      removedTransforms.push([key, value.get()]);\n      value.set(key.startsWith(\"scale\") ? 1 : 0);\n    }\n  });\n  // Apply changes to element before measurement\n  if (removedTransforms.length) visualElement.render();\n  return removedTransforms;\n}\nconst positionalValues = {\n  // Dimensions\n  width: ({\n    x\n  }, {\n    paddingLeft = \"0\",\n    paddingRight = \"0\"\n  }) => x.max - x.min - parseFloat(paddingLeft) - parseFloat(paddingRight),\n  height: ({\n    y\n  }, {\n    paddingTop = \"0\",\n    paddingBottom = \"0\"\n  }) => y.max - y.min - parseFloat(paddingTop) - parseFloat(paddingBottom),\n  top: (_bbox, {\n    top\n  }) => parseFloat(top),\n  left: (_bbox, {\n    left\n  }) => parseFloat(left),\n  bottom: ({\n    y\n  }, {\n    top\n  }) => parseFloat(top) + (y.max - y.min),\n  right: ({\n    x\n  }, {\n    left\n  }) => parseFloat(left) + (x.max - x.min),\n  // Transform\n  x: getTranslateFromMatrix(4, 13),\n  y: getTranslateFromMatrix(5, 14)\n};\nconst convertChangedValueTypes = (target, visualElement, changedKeys) => {\n  const originBbox = visualElement.measureViewportBox();\n  const element = visualElement.current;\n  const elementComputedStyle = getComputedStyle(element);\n  const {\n    display\n  } = elementComputedStyle;\n  const origin = {};\n  // If the element is currently set to display: \"none\", make it visible before\n  // measuring the target bounding box\n  if (display === \"none\") {\n    visualElement.setStaticValue(\"display\", target.display || \"block\");\n  }\n  /**\n   * Record origins before we render and update styles\n   */\n  changedKeys.forEach(key => {\n    origin[key] = positionalValues[key](originBbox, elementComputedStyle);\n  });\n  // Apply the latest values (as set in checkAndConvertChangedValueTypes)\n  visualElement.render();\n  const targetBbox = visualElement.measureViewportBox();\n  changedKeys.forEach(key => {\n    // Restore styles to their **calculated computed style**, not their actual\n    // originally set style. This allows us to animate between equivalent pixel units.\n    const value = visualElement.getValue(key);\n    setAndResetVelocity(value, origin[key]);\n    target[key] = positionalValues[key](targetBbox, elementComputedStyle);\n  });\n  return target;\n};\nconst checkAndConvertChangedValueTypes = (visualElement, target, origin = {}, transitionEnd = {}) => {\n  target = {\n    ...target\n  };\n  transitionEnd = {\n    ...transitionEnd\n  };\n  const targetPositionalKeys = Object.keys(target).filter(isPositionalKey);\n  // We want to remove any transform values that could affect the element's bounding box before\n  // it's measured. We'll reapply these later.\n  let removedTransformValues = [];\n  let hasAttemptedToRemoveTransformValues = false;\n  const changedValueTypeKeys = [];\n  targetPositionalKeys.forEach(key => {\n    const value = visualElement.getValue(key);\n    if (!visualElement.hasValue(key)) return;\n    let from = origin[key];\n    let fromType = findDimensionValueType(from);\n    const to = target[key];\n    let toType;\n    // TODO: The current implementation of this basically throws an error\n    // if you try and do value conversion via keyframes. There's probably\n    // a way of doing this but the performance implications would need greater scrutiny,\n    // as it'd be doing multiple resize-remeasure operations.\n    if (isKeyframesTarget(to)) {\n      const numKeyframes = to.length;\n      const fromIndex = to[0] === null ? 1 : 0;\n      from = to[fromIndex];\n      fromType = findDimensionValueType(from);\n      for (let i = fromIndex; i < numKeyframes; i++) {\n        if (!toType) {\n          toType = findDimensionValueType(to[i]);\n          invariant(toType === fromType || isNumOrPxType(fromType) && isNumOrPxType(toType), \"Keyframes must be of the same dimension as the current value\");\n        } else {\n          invariant(findDimensionValueType(to[i]) === toType, \"All keyframes must be of the same type\");\n        }\n      }\n    } else {\n      toType = findDimensionValueType(to);\n    }\n    if (fromType !== toType) {\n      // If they're both just number or px, convert them both to numbers rather than\n      // relying on resize/remeasure to convert (which is wasteful in this situation)\n      if (isNumOrPxType(fromType) && isNumOrPxType(toType)) {\n        const current = value.get();\n        if (typeof current === \"string\") {\n          value.set(parseFloat(current));\n        }\n        if (typeof to === \"string\") {\n          target[key] = parseFloat(to);\n        } else if (Array.isArray(to) && toType === px) {\n          target[key] = to.map(parseFloat);\n        }\n      } else if ((fromType === null || fromType === void 0 ? void 0 : fromType.transform) && (toType === null || toType === void 0 ? void 0 : toType.transform) && (from === 0 || to === 0)) {\n        // If one or the other value is 0, it's safe to coerce it to the\n        // type of the other without measurement\n        if (from === 0) {\n          value.set(toType.transform(from));\n        } else {\n          target[key] = fromType.transform(to);\n        }\n      } else {\n        // If we're going to do value conversion via DOM measurements, we first\n        // need to remove non-positional transform values that could affect the bbox measurements.\n        if (!hasAttemptedToRemoveTransformValues) {\n          removedTransformValues = removeNonTranslationalTransform(visualElement);\n          hasAttemptedToRemoveTransformValues = true;\n        }\n        changedValueTypeKeys.push(key);\n        transitionEnd[key] = transitionEnd[key] !== undefined ? transitionEnd[key] : target[key];\n        setAndResetVelocity(value, to);\n      }\n    }\n  });\n  if (changedValueTypeKeys.length) {\n    const scrollY = changedValueTypeKeys.indexOf(\"height\") >= 0 ? window.pageYOffset : null;\n    const convertedTarget = convertChangedValueTypes(target, visualElement, changedValueTypeKeys);\n    // If we removed transform values, reapply them before the next render\n    if (removedTransformValues.length) {\n      removedTransformValues.forEach(([key, value]) => {\n        visualElement.getValue(key).set(value);\n      });\n    }\n    // Reapply original values\n    visualElement.render();\n    // Restore scroll position\n    if (isBrowser && scrollY !== null) {\n      window.scrollTo({\n        top: scrollY\n      });\n    }\n    return {\n      target: convertedTarget,\n      transitionEnd\n    };\n  } else {\n    return {\n      target,\n      transitionEnd\n    };\n  }\n};\n/**\n * Convert value types for x/y/width/height/top/left/bottom/right\n *\n * Allows animation between `'auto'` -> `'100%'` or `0` -> `'calc(50% - 10vw)'`\n *\n * @internal\n */\nfunction unitConversion(visualElement, target, origin, transitionEnd) {\n  return hasPositionalKey(target) ? checkAndConvertChangedValueTypes(visualElement, target, origin, transitionEnd) : {\n    target,\n    transitionEnd\n  };\n}\nexport { BoundingBoxDimension, positionalValues, unitConversion };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}