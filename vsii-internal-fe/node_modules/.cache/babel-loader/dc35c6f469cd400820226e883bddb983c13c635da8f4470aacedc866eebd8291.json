{"ast": null, "code": "//\n// Main\n//\nexport function memoize(fn, options) {\n  var cache = options && options.cache ? options.cache : cacheDefault;\n  var serializer = options && options.serializer ? options.serializer : serializerDefault;\n  var strategy = options && options.strategy ? options.strategy : strategyDefault;\n  return strategy(fn, {\n    cache: cache,\n    serializer: serializer\n  });\n}\n//\n// Strategy\n//\nfunction isPrimitive(value) {\n  return value == null || typeof value === 'number' || typeof value === 'boolean'; // || typeof value === \"string\" 'unsafe' primitive for our needs\n}\nfunction monadic(fn, cache, serializer, arg) {\n  var cacheKey = isPrimitive(arg) ? arg : serializer(arg);\n  var computedValue = cache.get(cacheKey);\n  if (typeof computedValue === 'undefined') {\n    computedValue = fn.call(this, arg);\n    cache.set(cacheKey, computedValue);\n  }\n  return computedValue;\n}\nfunction variadic(fn, cache, serializer) {\n  var args = Array.prototype.slice.call(arguments, 3);\n  var cacheKey = serializer(args);\n  var computedValue = cache.get(cacheKey);\n  if (typeof computedValue === 'undefined') {\n    computedValue = fn.apply(this, args);\n    cache.set(cacheKey, computedValue);\n  }\n  return computedValue;\n}\nfunction assemble(fn, context, strategy, cache, serialize) {\n  return strategy.bind(context, fn, cache, serialize);\n}\nfunction strategyDefault(fn, options) {\n  var strategy = fn.length === 1 ? monadic : variadic;\n  return assemble(fn, this, strategy, options.cache.create(), options.serializer);\n}\nfunction strategyVariadic(fn, options) {\n  return assemble(fn, this, variadic, options.cache.create(), options.serializer);\n}\nfunction strategyMonadic(fn, options) {\n  return assemble(fn, this, monadic, options.cache.create(), options.serializer);\n}\n//\n// Serializer\n//\nvar serializerDefault = function () {\n  return JSON.stringify(arguments);\n};\n//\n// Cache\n//\nfunction ObjectWithoutPrototypeCache() {\n  this.cache = Object.create(null);\n}\nObjectWithoutPrototypeCache.prototype.get = function (key) {\n  return this.cache[key];\n};\nObjectWithoutPrototypeCache.prototype.set = function (key, value) {\n  this.cache[key] = value;\n};\nvar cacheDefault = {\n  create: function create() {\n    // @ts-ignore\n    return new ObjectWithoutPrototypeCache();\n  }\n};\nexport var strategies = {\n  variadic: strategyVariadic,\n  monadic: strategyMonadic\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}