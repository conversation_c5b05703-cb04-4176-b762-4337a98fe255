{"ast": null, "code": "import { cancelSync, flushSync, sync } from '../../frameloop/index.mjs';\nimport { animate } from '../../animation/animate.mjs';\nimport { SubscriptionManager } from '../../utils/subscription-manager.mjs';\nimport { mixValues } from '../animation/mix-values.mjs';\nimport { copyBoxInto } from '../geometry/copy.mjs';\nimport { translateAxis, transformBox, applyBoxDelta, applyTreeDeltas } from '../geometry/delta-apply.mjs';\nimport { calcRelativePosition, calcRelativeBox, calcBoxDelta, calcLength, isNear } from '../geometry/delta-calc.mjs';\nimport { removeBoxTransforms } from '../geometry/delta-remove.mjs';\nimport { createBox, createDelta } from '../geometry/models.mjs';\nimport { getValueTransition } from '../../animation/utils/transitions.mjs';\nimport { boxEquals, isDelta<PERSON>ero, aspectRatio } from '../geometry/utils.mjs';\nimport { NodeStack } from '../shared/stack.mjs';\nimport { scaleCorrectors } from '../styles/scale-correction.mjs';\nimport { buildProjectionTransform } from '../styles/transform.mjs';\nimport { eachAxis } from '../utils/each-axis.mjs';\nimport { hasTransform, hasScale, has2DTranslate } from '../utils/has-transform.mjs';\nimport { FlatTree } from '../../render/utils/flat-tree.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { globalProjectionState } from './state.mjs';\nimport { delay } from '../../utils/delay.mjs';\nimport { mix } from '../../utils/mix.mjs';\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\n/**\n * We use 1000 as the animation target as 0-1000 maps better to pixels than 0-1\n * which has a noticeable difference in spring animations\n */\nconst animationTarget = 1000;\nlet id = 0;\nfunction createProjectionNode({\n  attachResizeListener,\n  defaultParent,\n  measureScroll,\n  checkIsScrollRoot,\n  resetTransform\n}) {\n  return class ProjectionNode {\n    constructor(elementId, latestValues = {}, parent = defaultParent === null || defaultParent === void 0 ? void 0 : defaultParent()) {\n      /**\n       * A unique ID generated for every projection node.\n       */\n      this.id = id++;\n      /**\n       * An id that represents a unique session instigated by startUpdate.\n       */\n      this.animationId = 0;\n      /**\n       * A Set containing all this component's children. This is used to iterate\n       * through the children.\n       *\n       * TODO: This could be faster to iterate as a flat array stored on the root node.\n       */\n      this.children = new Set();\n      /**\n       * Options for the node. We use this to configure what kind of layout animations\n       * we should perform (if any).\n       */\n      this.options = {};\n      /**\n       * We use this to detect when its safe to shut down part of a projection tree.\n       * We have to keep projecting children for scale correction and relative projection\n       * until all their parents stop performing layout animations.\n       */\n      this.isTreeAnimating = false;\n      this.isAnimationBlocked = false;\n      /**\n       * Flag to true if we think this layout has been changed. We can't always know this,\n       * currently we set it to true every time a component renders, or if it has a layoutDependency\n       * if that has changed between renders. Additionally, components can be grouped by LayoutGroup\n       * and if one node is dirtied, they all are.\n       */\n      this.isLayoutDirty = false;\n      this.isTransformDirty = false;\n      /**\n       * Flag to true if we think the projection calculations for this or any\n       * child might need recalculating as a result of an updated transform or layout animation.\n       */\n      this.isProjectionDirty = false;\n      /**\n       * Block layout updates for instant layout transitions throughout the tree.\n       */\n      this.updateManuallyBlocked = false;\n      this.updateBlockedByResize = false;\n      /**\n       * Set to true between the start of the first `willUpdate` call and the end of the `didUpdate`\n       * call.\n       */\n      this.isUpdating = false;\n      /**\n       * If this is an SVG element we currently disable projection transforms\n       */\n      this.isSVG = false;\n      /**\n       * Flag to true (during promotion) if a node doing an instant layout transition needs to reset\n       * its projection styles.\n       */\n      this.needsReset = false;\n      /**\n       * Flags whether this node should have its transform reset prior to measuring.\n       */\n      this.shouldResetTransform = false;\n      /**\n       * An object representing the calculated contextual/accumulated/tree scale.\n       * This will be used to scale calculcated projection transforms, as these are\n       * calculated in screen-space but need to be scaled for elements to layoutly\n       * make it to their calculated destinations.\n       *\n       * TODO: Lazy-init\n       */\n      this.treeScale = {\n        x: 1,\n        y: 1\n      };\n      /**\n       *\n       */\n      this.eventHandlers = new Map();\n      // Note: Currently only running on root node\n      this.potentialNodes = new Map();\n      this.checkUpdateFailed = () => {\n        if (this.isUpdating) {\n          this.isUpdating = false;\n          this.clearAllSnapshots();\n        }\n      };\n      /**\n       * This is a multi-step process as shared nodes might be of different depths. Nodes\n       * are sorted by depth order, so we need to resolve the entire tree before moving to\n       * the next step.\n       */\n      this.updateProjection = () => {\n        this.nodes.forEach(propagateDirtyNodes);\n        this.nodes.forEach(resolveTargetDelta);\n        this.nodes.forEach(calcProjection);\n      };\n      this.hasProjected = false;\n      this.isVisible = true;\n      this.animationProgress = 0;\n      /**\n       * Shared layout\n       */\n      // TODO Only running on root node\n      this.sharedNodes = new Map();\n      this.elementId = elementId;\n      this.latestValues = latestValues;\n      this.root = parent ? parent.root || parent : this;\n      this.path = parent ? [...parent.path, parent] : [];\n      this.parent = parent;\n      this.depth = parent ? parent.depth + 1 : 0;\n      elementId && this.root.registerPotentialNode(elementId, this);\n      for (let i = 0; i < this.path.length; i++) {\n        this.path[i].shouldResetTransform = true;\n      }\n      if (this.root === this) this.nodes = new FlatTree();\n    }\n    addEventListener(name, handler) {\n      if (!this.eventHandlers.has(name)) {\n        this.eventHandlers.set(name, new SubscriptionManager());\n      }\n      return this.eventHandlers.get(name).add(handler);\n    }\n    notifyListeners(name, ...args) {\n      const subscriptionManager = this.eventHandlers.get(name);\n      subscriptionManager === null || subscriptionManager === void 0 ? void 0 : subscriptionManager.notify(...args);\n    }\n    hasListeners(name) {\n      return this.eventHandlers.has(name);\n    }\n    registerPotentialNode(elementId, node) {\n      this.potentialNodes.set(elementId, node);\n    }\n    /**\n     * Lifecycles\n     */\n    mount(instance, isLayoutDirty = false) {\n      var _a;\n      if (this.instance) return;\n      this.isSVG = instance instanceof SVGElement && instance.tagName !== \"svg\";\n      this.instance = instance;\n      const {\n        layoutId,\n        layout,\n        visualElement\n      } = this.options;\n      if (visualElement && !visualElement.current) {\n        visualElement.mount(instance);\n      }\n      this.root.nodes.add(this);\n      (_a = this.parent) === null || _a === void 0 ? void 0 : _a.children.add(this);\n      this.elementId && this.root.potentialNodes.delete(this.elementId);\n      if (isLayoutDirty && (layout || layoutId)) {\n        this.isLayoutDirty = true;\n      }\n      if (attachResizeListener) {\n        let cancelDelay;\n        const resizeUnblockUpdate = () => this.root.updateBlockedByResize = false;\n        attachResizeListener(instance, () => {\n          this.root.updateBlockedByResize = true;\n          cancelDelay && cancelDelay();\n          cancelDelay = delay(resizeUnblockUpdate, 250);\n          if (globalProjectionState.hasAnimatedSinceResize) {\n            globalProjectionState.hasAnimatedSinceResize = false;\n            this.nodes.forEach(finishAnimation);\n          }\n        });\n      }\n      if (layoutId) {\n        this.root.registerSharedNode(layoutId, this);\n      }\n      // Only register the handler if it requires layout animation\n      if (this.options.animate !== false && visualElement && (layoutId || layout)) {\n        this.addEventListener(\"didUpdate\", ({\n          delta,\n          hasLayoutChanged,\n          hasRelativeTargetChanged,\n          layout: newLayout\n        }) => {\n          var _a, _b, _c, _d, _e;\n          if (this.isTreeAnimationBlocked()) {\n            this.target = undefined;\n            this.relativeTarget = undefined;\n            return;\n          }\n          // TODO: Check here if an animation exists\n          const layoutTransition = (_b = (_a = this.options.transition) !== null && _a !== void 0 ? _a : visualElement.getDefaultTransition()) !== null && _b !== void 0 ? _b : defaultLayoutTransition;\n          const {\n            onLayoutAnimationStart,\n            onLayoutAnimationComplete\n          } = visualElement.getProps();\n          /**\n           * The target layout of the element might stay the same,\n           * but its position relative to its parent has changed.\n           */\n          const targetChanged = !this.targetLayout || !boxEquals(this.targetLayout, newLayout) || hasRelativeTargetChanged;\n          /**\n           * If the layout hasn't seemed to have changed, it might be that the\n           * element is visually in the same place in the document but its position\n           * relative to its parent has indeed changed. So here we check for that.\n           */\n          const hasOnlyRelativeTargetChanged = !hasLayoutChanged && hasRelativeTargetChanged;\n          if (((_c = this.resumeFrom) === null || _c === void 0 ? void 0 : _c.instance) || hasOnlyRelativeTargetChanged || hasLayoutChanged && (targetChanged || !this.currentAnimation)) {\n            if (this.resumeFrom) {\n              this.resumingFrom = this.resumeFrom;\n              this.resumingFrom.resumingFrom = undefined;\n            }\n            this.setAnimationOrigin(delta, hasOnlyRelativeTargetChanged);\n            const animationOptions = {\n              ...getValueTransition(layoutTransition, \"layout\"),\n              onPlay: onLayoutAnimationStart,\n              onComplete: onLayoutAnimationComplete\n            };\n            if (visualElement.shouldReduceMotion) {\n              animationOptions.delay = 0;\n              animationOptions.type = false;\n            }\n            this.startAnimation(animationOptions);\n          } else {\n            /**\n             * If the layout hasn't changed and we have an animation that hasn't started yet,\n             * finish it immediately. Otherwise it will be animating from a location\n             * that was probably never commited to screen and look like a jumpy box.\n             */\n            if (!hasLayoutChanged && this.animationProgress === 0) {\n              finishAnimation(this);\n            }\n            this.isLead() && ((_e = (_d = this.options).onExitComplete) === null || _e === void 0 ? void 0 : _e.call(_d));\n          }\n          this.targetLayout = newLayout;\n        });\n      }\n    }\n    unmount() {\n      var _a, _b;\n      this.options.layoutId && this.willUpdate();\n      this.root.nodes.remove(this);\n      (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.remove(this);\n      (_b = this.parent) === null || _b === void 0 ? void 0 : _b.children.delete(this);\n      this.instance = undefined;\n      cancelSync.preRender(this.updateProjection);\n    }\n    // only on the root\n    blockUpdate() {\n      this.updateManuallyBlocked = true;\n    }\n    unblockUpdate() {\n      this.updateManuallyBlocked = false;\n    }\n    isUpdateBlocked() {\n      return this.updateManuallyBlocked || this.updateBlockedByResize;\n    }\n    isTreeAnimationBlocked() {\n      var _a;\n      return this.isAnimationBlocked || ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isTreeAnimationBlocked()) || false;\n    }\n    // Note: currently only running on root node\n    startUpdate() {\n      var _a;\n      if (this.isUpdateBlocked()) return;\n      this.isUpdating = true;\n      (_a = this.nodes) === null || _a === void 0 ? void 0 : _a.forEach(resetRotation);\n      this.animationId++;\n    }\n    willUpdate(shouldNotifyListeners = true) {\n      var _a, _b, _c;\n      if (this.root.isUpdateBlocked()) {\n        (_b = (_a = this.options).onExitComplete) === null || _b === void 0 ? void 0 : _b.call(_a);\n        return;\n      }\n      !this.root.isUpdating && this.root.startUpdate();\n      if (this.isLayoutDirty) return;\n      this.isLayoutDirty = true;\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        node.shouldResetTransform = true;\n        node.updateScroll(\"snapshot\");\n      }\n      const {\n        layoutId,\n        layout\n      } = this.options;\n      if (layoutId === undefined && !layout) return;\n      const transformTemplate = (_c = this.options.visualElement) === null || _c === void 0 ? void 0 : _c.getProps().transformTemplate;\n      this.prevTransformTemplateValue = transformTemplate === null || transformTemplate === void 0 ? void 0 : transformTemplate(this.latestValues, \"\");\n      this.updateSnapshot();\n      shouldNotifyListeners && this.notifyListeners(\"willUpdate\");\n    }\n    // Note: Currently only running on root node\n    didUpdate() {\n      const updateWasBlocked = this.isUpdateBlocked();\n      // When doing an instant transition, we skip the layout update,\n      // but should still clean up the measurements so that the next\n      // snapshot could be taken correctly.\n      if (updateWasBlocked) {\n        this.unblockUpdate();\n        this.clearAllSnapshots();\n        this.nodes.forEach(clearMeasurements);\n        return;\n      }\n      if (!this.isUpdating) return;\n      this.isUpdating = false;\n      /**\n       * Search for and mount newly-added projection elements.\n       *\n       * TODO: Every time a new component is rendered we could search up the tree for\n       * the closest mounted node and query from there rather than document.\n       */\n      if (this.potentialNodes.size) {\n        this.potentialNodes.forEach(mountNodeEarly);\n        this.potentialNodes.clear();\n      }\n      /**\n       * Write\n       */\n      this.nodes.forEach(resetTransformStyle);\n      /**\n       * Read ==================\n       */\n      // Update layout measurements of updated children\n      this.nodes.forEach(updateLayout);\n      /**\n       * Write\n       */\n      // Notify listeners that the layout is updated\n      this.nodes.forEach(notifyLayoutUpdate);\n      this.clearAllSnapshots();\n      // Flush any scheduled updates\n      flushSync.update();\n      flushSync.preRender();\n      flushSync.render();\n    }\n    clearAllSnapshots() {\n      this.nodes.forEach(clearSnapshot);\n      this.sharedNodes.forEach(removeLeadSnapshots);\n    }\n    scheduleUpdateProjection() {\n      sync.preRender(this.updateProjection, false, true);\n    }\n    scheduleCheckAfterUnmount() {\n      /**\n       * If the unmounting node is in a layoutGroup and did trigger a willUpdate,\n       * we manually call didUpdate to give a chance to the siblings to animate.\n       * Otherwise, cleanup all snapshots to prevents future nodes from reusing them.\n       */\n      sync.postRender(() => {\n        if (this.isLayoutDirty) {\n          this.root.didUpdate();\n        } else {\n          this.root.checkUpdateFailed();\n        }\n      });\n    }\n    /**\n     * Update measurements\n     */\n    updateSnapshot() {\n      if (this.snapshot || !this.instance) return;\n      this.snapshot = this.measure();\n    }\n    updateLayout() {\n      var _a;\n      if (!this.instance) return;\n      // TODO: Incorporate into a forwarded scroll offset\n      this.updateScroll();\n      if (!(this.options.alwaysMeasureLayout && this.isLead()) && !this.isLayoutDirty) {\n        return;\n      }\n      /**\n       * When a node is mounted, it simply resumes from the prevLead's\n       * snapshot instead of taking a new one, but the ancestors scroll\n       * might have updated while the prevLead is unmounted. We need to\n       * update the scroll again to make sure the layout we measure is\n       * up to date.\n       */\n      if (this.resumeFrom && !this.resumeFrom.instance) {\n        for (let i = 0; i < this.path.length; i++) {\n          const node = this.path[i];\n          node.updateScroll();\n        }\n      }\n      const prevLayout = this.layout;\n      this.layout = this.measure(false);\n      this.layoutCorrected = createBox();\n      this.isLayoutDirty = false;\n      this.projectionDelta = undefined;\n      this.notifyListeners(\"measure\", this.layout.layoutBox);\n      (_a = this.options.visualElement) === null || _a === void 0 ? void 0 : _a.notify(\"LayoutMeasure\", this.layout.layoutBox, prevLayout === null || prevLayout === void 0 ? void 0 : prevLayout.layoutBox);\n    }\n    updateScroll(phase = \"measure\") {\n      let needsMeasurement = Boolean(this.options.layoutScroll && this.instance);\n      if (this.scroll && this.scroll.animationId === this.root.animationId && this.scroll.phase === phase) {\n        needsMeasurement = false;\n      }\n      if (needsMeasurement) {\n        this.scroll = {\n          animationId: this.root.animationId,\n          phase,\n          isRoot: checkIsScrollRoot(this.instance),\n          offset: measureScroll(this.instance)\n        };\n      }\n    }\n    resetTransform() {\n      var _a;\n      if (!resetTransform) return;\n      const isResetRequested = this.isLayoutDirty || this.shouldResetTransform;\n      const hasProjection = this.projectionDelta && !isDeltaZero(this.projectionDelta);\n      const transformTemplate = (_a = this.options.visualElement) === null || _a === void 0 ? void 0 : _a.getProps().transformTemplate;\n      const transformTemplateValue = transformTemplate === null || transformTemplate === void 0 ? void 0 : transformTemplate(this.latestValues, \"\");\n      const transformTemplateHasChanged = transformTemplateValue !== this.prevTransformTemplateValue;\n      if (isResetRequested && (hasProjection || hasTransform(this.latestValues) || transformTemplateHasChanged)) {\n        resetTransform(this.instance, transformTemplateValue);\n        this.shouldResetTransform = false;\n        this.scheduleRender();\n      }\n    }\n    measure(removeTransform = true) {\n      const pageBox = this.measurePageBox();\n      let layoutBox = this.removeElementScroll(pageBox);\n      /**\n       * Measurements taken during the pre-render stage\n       * still have transforms applied so we remove them\n       * via calculation.\n       */\n      if (removeTransform) {\n        layoutBox = this.removeTransform(layoutBox);\n      }\n      roundBox(layoutBox);\n      return {\n        animationId: this.root.animationId,\n        measuredBox: pageBox,\n        layoutBox,\n        latestValues: {},\n        source: this.id\n      };\n    }\n    measurePageBox() {\n      const {\n        visualElement\n      } = this.options;\n      if (!visualElement) return createBox();\n      const box = visualElement.measureViewportBox();\n      // Remove viewport scroll to give page-relative coordinates\n      const {\n        scroll\n      } = this.root;\n      if (scroll) {\n        translateAxis(box.x, scroll.offset.x);\n        translateAxis(box.y, scroll.offset.y);\n      }\n      return box;\n    }\n    removeElementScroll(box) {\n      const boxWithoutScroll = createBox();\n      copyBoxInto(boxWithoutScroll, box);\n      /**\n       * Performance TODO: Keep a cumulative scroll offset down the tree\n       * rather than loop back up the path.\n       */\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        const {\n          scroll,\n          options\n        } = node;\n        if (node !== this.root && scroll && options.layoutScroll) {\n          /**\n           * If this is a new scroll root, we want to remove all previous scrolls\n           * from the viewport box.\n           */\n          if (scroll.isRoot) {\n            copyBoxInto(boxWithoutScroll, box);\n            const {\n              scroll: rootScroll\n            } = this.root;\n            /**\n             * Undo the application of page scroll that was originally added\n             * to the measured bounding box.\n             */\n            if (rootScroll) {\n              translateAxis(boxWithoutScroll.x, -rootScroll.offset.x);\n              translateAxis(boxWithoutScroll.y, -rootScroll.offset.y);\n            }\n          }\n          translateAxis(boxWithoutScroll.x, scroll.offset.x);\n          translateAxis(boxWithoutScroll.y, scroll.offset.y);\n        }\n      }\n      return boxWithoutScroll;\n    }\n    applyTransform(box, transformOnly = false) {\n      const withTransforms = createBox();\n      copyBoxInto(withTransforms, box);\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        if (!transformOnly && node.options.layoutScroll && node.scroll && node !== node.root) {\n          transformBox(withTransforms, {\n            x: -node.scroll.offset.x,\n            y: -node.scroll.offset.y\n          });\n        }\n        if (!hasTransform(node.latestValues)) continue;\n        transformBox(withTransforms, node.latestValues);\n      }\n      if (hasTransform(this.latestValues)) {\n        transformBox(withTransforms, this.latestValues);\n      }\n      return withTransforms;\n    }\n    removeTransform(box) {\n      var _a;\n      const boxWithoutTransform = createBox();\n      copyBoxInto(boxWithoutTransform, box);\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        if (!node.instance) continue;\n        if (!hasTransform(node.latestValues)) continue;\n        hasScale(node.latestValues) && node.updateSnapshot();\n        const sourceBox = createBox();\n        const nodeBox = node.measurePageBox();\n        copyBoxInto(sourceBox, nodeBox);\n        removeBoxTransforms(boxWithoutTransform, node.latestValues, (_a = node.snapshot) === null || _a === void 0 ? void 0 : _a.layoutBox, sourceBox);\n      }\n      if (hasTransform(this.latestValues)) {\n        removeBoxTransforms(boxWithoutTransform, this.latestValues);\n      }\n      return boxWithoutTransform;\n    }\n    /**\n     *\n     */\n    setTargetDelta(delta) {\n      this.targetDelta = delta;\n      this.isProjectionDirty = true;\n      this.root.scheduleUpdateProjection();\n    }\n    setOptions(options) {\n      this.options = {\n        ...this.options,\n        ...options,\n        crossfade: options.crossfade !== undefined ? options.crossfade : true\n      };\n    }\n    clearMeasurements() {\n      this.scroll = undefined;\n      this.layout = undefined;\n      this.snapshot = undefined;\n      this.prevTransformTemplateValue = undefined;\n      this.targetDelta = undefined;\n      this.target = undefined;\n      this.isLayoutDirty = false;\n    }\n    /**\n     * Frame calculations\n     */\n    resolveTargetDelta() {\n      var _a;\n      /**\n       * Once the dirty status of nodes has been spread through the tree, we also\n       * need to check if we have a shared node of a different depth that has itself\n       * been dirtied.\n       */\n      const lead = this.getLead();\n      this.isProjectionDirty || (this.isProjectionDirty = lead.isProjectionDirty);\n      this.isTransformDirty || (this.isTransformDirty = lead.isTransformDirty);\n      /**\n       * We don't use transform for this step of processing so we don't\n       * need to check whether any nodes have changed transform.\n       */\n      if (!this.isProjectionDirty && !this.attemptToResolveRelativeTarget) return;\n      const {\n        layout,\n        layoutId\n      } = this.options;\n      /**\n       * If we have no layout, we can't perform projection, so early return\n       */\n      if (!this.layout || !(layout || layoutId)) return;\n      /**\n       * If we don't have a targetDelta but do have a layout, we can attempt to resolve\n       * a relativeParent. This will allow a component to perform scale correction\n       * even if no animation has started.\n       */\n      // TODO If this is unsuccessful this currently happens every frame\n      if (!this.targetDelta && !this.relativeTarget) {\n        // TODO: This is a semi-repetition of further down this function, make DRY\n        const relativeParent = this.getClosestProjectingParent();\n        if (relativeParent && relativeParent.layout) {\n          this.relativeParent = relativeParent;\n          this.relativeTarget = createBox();\n          this.relativeTargetOrigin = createBox();\n          calcRelativePosition(this.relativeTargetOrigin, this.layout.layoutBox, relativeParent.layout.layoutBox);\n          copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n        } else {\n          this.relativeParent = this.relativeTarget = undefined;\n        }\n      }\n      /**\n       * If we have no relative target or no target delta our target isn't valid\n       * for this frame.\n       */\n      if (!this.relativeTarget && !this.targetDelta) return;\n      /**\n       * Lazy-init target data structure\n       */\n      if (!this.target) {\n        this.target = createBox();\n        this.targetWithTransforms = createBox();\n      }\n      /**\n       * If we've got a relative box for this component, resolve it into a target relative to the parent.\n       */\n      if (this.relativeTarget && this.relativeTargetOrigin && ((_a = this.relativeParent) === null || _a === void 0 ? void 0 : _a.target)) {\n        calcRelativeBox(this.target, this.relativeTarget, this.relativeParent.target);\n        /**\n         * If we've only got a targetDelta, resolve it into a target\n         */\n      } else if (this.targetDelta) {\n        if (Boolean(this.resumingFrom)) {\n          // TODO: This is creating a new object every frame\n          this.target = this.applyTransform(this.layout.layoutBox);\n        } else {\n          copyBoxInto(this.target, this.layout.layoutBox);\n        }\n        applyBoxDelta(this.target, this.targetDelta);\n      } else {\n        /**\n         * If no target, use own layout as target\n         */\n        copyBoxInto(this.target, this.layout.layoutBox);\n      }\n      /**\n       * If we've been told to attempt to resolve a relative target, do so.\n       */\n      if (this.attemptToResolveRelativeTarget) {\n        this.attemptToResolveRelativeTarget = false;\n        const relativeParent = this.getClosestProjectingParent();\n        if (relativeParent && Boolean(relativeParent.resumingFrom) === Boolean(this.resumingFrom) && !relativeParent.options.layoutScroll && relativeParent.target) {\n          this.relativeParent = relativeParent;\n          this.relativeTarget = createBox();\n          this.relativeTargetOrigin = createBox();\n          calcRelativePosition(this.relativeTargetOrigin, this.target, relativeParent.target);\n          copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n        } else {\n          this.relativeParent = this.relativeTarget = undefined;\n        }\n      }\n    }\n    getClosestProjectingParent() {\n      if (!this.parent || hasScale(this.parent.latestValues) || has2DTranslate(this.parent.latestValues)) return undefined;\n      if ((this.parent.relativeTarget || this.parent.targetDelta) && this.parent.layout) {\n        return this.parent;\n      } else {\n        return this.parent.getClosestProjectingParent();\n      }\n    }\n    calcProjection() {\n      var _a;\n      const {\n        isProjectionDirty,\n        isTransformDirty\n      } = this;\n      this.isProjectionDirty = this.isTransformDirty = false;\n      const lead = this.getLead();\n      const isShared = Boolean(this.resumingFrom) || this !== lead;\n      let canSkip = true;\n      if (isProjectionDirty) canSkip = false;\n      if (isShared && isTransformDirty) canSkip = false;\n      if (canSkip) return;\n      const {\n        layout,\n        layoutId\n      } = this.options;\n      /**\n       * If this section of the tree isn't animating we can\n       * delete our target sources for the following frame.\n       */\n      this.isTreeAnimating = Boolean(((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isTreeAnimating) || this.currentAnimation || this.pendingAnimation);\n      if (!this.isTreeAnimating) {\n        this.targetDelta = this.relativeTarget = undefined;\n      }\n      if (!this.layout || !(layout || layoutId)) return;\n      /**\n       * Reset the corrected box with the latest values from box, as we're then going\n       * to perform mutative operations on it.\n       */\n      copyBoxInto(this.layoutCorrected, this.layout.layoutBox);\n      /**\n       * Apply all the parent deltas to this box to produce the corrected box. This\n       * is the layout box, as it will appear on screen as a result of the transforms of its parents.\n       */\n      applyTreeDeltas(this.layoutCorrected, this.treeScale, this.path, isShared);\n      const {\n        target\n      } = lead;\n      if (!target) return;\n      if (!this.projectionDelta) {\n        this.projectionDelta = createDelta();\n        this.projectionDeltaWithTransform = createDelta();\n      }\n      const prevTreeScaleX = this.treeScale.x;\n      const prevTreeScaleY = this.treeScale.y;\n      const prevProjectionTransform = this.projectionTransform;\n      /**\n       * Update the delta between the corrected box and the target box before user-set transforms were applied.\n       * This will allow us to calculate the corrected borderRadius and boxShadow to compensate\n       * for our layout reprojection, but still allow them to be scaled correctly by the user.\n       * It might be that to simplify this we may want to accept that user-set scale is also corrected\n       * and we wouldn't have to keep and calc both deltas, OR we could support a user setting\n       * to allow people to choose whether these styles are corrected based on just the\n       * layout reprojection or the final bounding box.\n       */\n      calcBoxDelta(this.projectionDelta, this.layoutCorrected, target, this.latestValues);\n      this.projectionTransform = buildProjectionTransform(this.projectionDelta, this.treeScale);\n      if (this.projectionTransform !== prevProjectionTransform || this.treeScale.x !== prevTreeScaleX || this.treeScale.y !== prevTreeScaleY) {\n        this.hasProjected = true;\n        this.scheduleRender();\n        this.notifyListeners(\"projectionUpdate\", target);\n      }\n    }\n    hide() {\n      this.isVisible = false;\n      // TODO: Schedule render\n    }\n    show() {\n      this.isVisible = true;\n      // TODO: Schedule render\n    }\n    scheduleRender(notifyAll = true) {\n      var _a, _b, _c;\n      (_b = (_a = this.options).scheduleRender) === null || _b === void 0 ? void 0 : _b.call(_a);\n      notifyAll && ((_c = this.getStack()) === null || _c === void 0 ? void 0 : _c.scheduleRender());\n      if (this.resumingFrom && !this.resumingFrom.instance) {\n        this.resumingFrom = undefined;\n      }\n    }\n    setAnimationOrigin(delta, hasOnlyRelativeTargetChanged = false) {\n      var _a, _b;\n      const snapshot = this.snapshot;\n      const snapshotLatestValues = (snapshot === null || snapshot === void 0 ? void 0 : snapshot.latestValues) || {};\n      const mixedValues = {\n        ...this.latestValues\n      };\n      const targetDelta = createDelta();\n      this.relativeTarget = this.relativeTargetOrigin = undefined;\n      this.attemptToResolveRelativeTarget = !hasOnlyRelativeTargetChanged;\n      const relativeLayout = createBox();\n      const isSharedLayoutAnimation = (snapshot === null || snapshot === void 0 ? void 0 : snapshot.source) !== ((_a = this.layout) === null || _a === void 0 ? void 0 : _a.source);\n      const isOnlyMember = (((_b = this.getStack()) === null || _b === void 0 ? void 0 : _b.members.length) || 0) <= 1;\n      const shouldCrossfadeOpacity = Boolean(isSharedLayoutAnimation && !isOnlyMember && this.options.crossfade === true && !this.path.some(hasOpacityCrossfade));\n      this.animationProgress = 0;\n      this.mixTargetDelta = latest => {\n        var _a;\n        const progress = latest / 1000;\n        mixAxisDelta(targetDelta.x, delta.x, progress);\n        mixAxisDelta(targetDelta.y, delta.y, progress);\n        this.setTargetDelta(targetDelta);\n        if (this.relativeTarget && this.relativeTargetOrigin && this.layout && ((_a = this.relativeParent) === null || _a === void 0 ? void 0 : _a.layout)) {\n          calcRelativePosition(relativeLayout, this.layout.layoutBox, this.relativeParent.layout.layoutBox);\n          mixBox(this.relativeTarget, this.relativeTargetOrigin, relativeLayout, progress);\n        }\n        if (isSharedLayoutAnimation) {\n          this.animationValues = mixedValues;\n          mixValues(mixedValues, snapshotLatestValues, this.latestValues, progress, shouldCrossfadeOpacity, isOnlyMember);\n        }\n        this.root.scheduleUpdateProjection();\n        this.scheduleRender();\n        this.animationProgress = progress;\n      };\n      this.mixTargetDelta(0);\n    }\n    startAnimation(options) {\n      var _a, _b;\n      this.notifyListeners(\"animationStart\");\n      (_a = this.currentAnimation) === null || _a === void 0 ? void 0 : _a.stop();\n      if (this.resumingFrom) {\n        (_b = this.resumingFrom.currentAnimation) === null || _b === void 0 ? void 0 : _b.stop();\n      }\n      if (this.pendingAnimation) {\n        cancelSync.update(this.pendingAnimation);\n        this.pendingAnimation = undefined;\n      }\n      /**\n       * Start the animation in the next frame to have a frame with progress 0,\n       * where the target is the same as when the animation started, so we can\n       * calculate the relative positions correctly for instant transitions.\n       */\n      this.pendingAnimation = sync.update(() => {\n        globalProjectionState.hasAnimatedSinceResize = true;\n        this.currentAnimation = animate(0, animationTarget, {\n          ...options,\n          onUpdate: latest => {\n            var _a;\n            this.mixTargetDelta(latest);\n            (_a = options.onUpdate) === null || _a === void 0 ? void 0 : _a.call(options, latest);\n          },\n          onComplete: () => {\n            var _a;\n            (_a = options.onComplete) === null || _a === void 0 ? void 0 : _a.call(options);\n            this.completeAnimation();\n          }\n        });\n        if (this.resumingFrom) {\n          this.resumingFrom.currentAnimation = this.currentAnimation;\n        }\n        this.pendingAnimation = undefined;\n      });\n    }\n    completeAnimation() {\n      var _a;\n      if (this.resumingFrom) {\n        this.resumingFrom.currentAnimation = undefined;\n        this.resumingFrom.preserveOpacity = undefined;\n      }\n      (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.exitAnimationComplete();\n      this.resumingFrom = this.currentAnimation = this.animationValues = undefined;\n      this.notifyListeners(\"animationComplete\");\n    }\n    finishAnimation() {\n      var _a;\n      if (this.currentAnimation) {\n        (_a = this.mixTargetDelta) === null || _a === void 0 ? void 0 : _a.call(this, animationTarget);\n        this.currentAnimation.stop();\n      }\n      this.completeAnimation();\n    }\n    applyTransformsToTarget() {\n      const lead = this.getLead();\n      let {\n        targetWithTransforms,\n        target,\n        layout,\n        latestValues\n      } = lead;\n      if (!targetWithTransforms || !target || !layout) return;\n      /**\n       * If we're only animating position, and this element isn't the lead element,\n       * then instead of projecting into the lead box we instead want to calculate\n       * a new target that aligns the two boxes but maintains the layout shape.\n       */\n      if (this !== lead && this.layout && layout && shouldAnimatePositionOnly(this.options.animationType, this.layout.layoutBox, layout.layoutBox)) {\n        target = this.target || createBox();\n        const xLength = calcLength(this.layout.layoutBox.x);\n        target.x.min = lead.target.x.min;\n        target.x.max = target.x.min + xLength;\n        const yLength = calcLength(this.layout.layoutBox.y);\n        target.y.min = lead.target.y.min;\n        target.y.max = target.y.min + yLength;\n      }\n      copyBoxInto(targetWithTransforms, target);\n      /**\n       * Apply the latest user-set transforms to the targetBox to produce the targetBoxFinal.\n       * This is the final box that we will then project into by calculating a transform delta and\n       * applying it to the corrected box.\n       */\n      transformBox(targetWithTransforms, latestValues);\n      /**\n       * Update the delta between the corrected box and the final target box, after\n       * user-set transforms are applied to it. This will be used by the renderer to\n       * create a transform style that will reproject the element from its layout layout\n       * into the desired bounding box.\n       */\n      calcBoxDelta(this.projectionDeltaWithTransform, this.layoutCorrected, targetWithTransforms, latestValues);\n    }\n    registerSharedNode(layoutId, node) {\n      var _a, _b, _c;\n      if (!this.sharedNodes.has(layoutId)) {\n        this.sharedNodes.set(layoutId, new NodeStack());\n      }\n      const stack = this.sharedNodes.get(layoutId);\n      stack.add(node);\n      node.promote({\n        transition: (_a = node.options.initialPromotionConfig) === null || _a === void 0 ? void 0 : _a.transition,\n        preserveFollowOpacity: (_c = (_b = node.options.initialPromotionConfig) === null || _b === void 0 ? void 0 : _b.shouldPreserveFollowOpacity) === null || _c === void 0 ? void 0 : _c.call(_b, node)\n      });\n    }\n    isLead() {\n      const stack = this.getStack();\n      return stack ? stack.lead === this : true;\n    }\n    getLead() {\n      var _a;\n      const {\n        layoutId\n      } = this.options;\n      return layoutId ? ((_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.lead) || this : this;\n    }\n    getPrevLead() {\n      var _a;\n      const {\n        layoutId\n      } = this.options;\n      return layoutId ? (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.prevLead : undefined;\n    }\n    getStack() {\n      const {\n        layoutId\n      } = this.options;\n      if (layoutId) return this.root.sharedNodes.get(layoutId);\n    }\n    promote({\n      needsReset,\n      transition,\n      preserveFollowOpacity\n    } = {}) {\n      const stack = this.getStack();\n      if (stack) stack.promote(this, preserveFollowOpacity);\n      if (needsReset) {\n        this.projectionDelta = undefined;\n        this.needsReset = true;\n      }\n      if (transition) this.setOptions({\n        transition\n      });\n    }\n    relegate() {\n      const stack = this.getStack();\n      if (stack) {\n        return stack.relegate(this);\n      } else {\n        return false;\n      }\n    }\n    resetRotation() {\n      const {\n        visualElement\n      } = this.options;\n      if (!visualElement) return;\n      // If there's no detected rotation values, we can early return without a forced render.\n      let hasRotate = false;\n      /**\n       * An unrolled check for rotation values. Most elements don't have any rotation and\n       * skipping the nested loop and new object creation is 50% faster.\n       */\n      const {\n        latestValues\n      } = visualElement;\n      if (latestValues.rotate || latestValues.rotateX || latestValues.rotateY || latestValues.rotateZ) {\n        hasRotate = true;\n      }\n      // If there's no rotation values, we don't need to do any more.\n      if (!hasRotate) return;\n      const resetValues = {};\n      // Check the rotate value of all axes and reset to 0\n      for (let i = 0; i < transformAxes.length; i++) {\n        const key = \"rotate\" + transformAxes[i];\n        // Record the rotation and then temporarily set it to 0\n        if (latestValues[key]) {\n          resetValues[key] = latestValues[key];\n          visualElement.setStaticValue(key, 0);\n        }\n      }\n      // Force a render of this element to apply the transform with all rotations\n      // set to 0.\n      visualElement === null || visualElement === void 0 ? void 0 : visualElement.render();\n      // Put back all the values we reset\n      for (const key in resetValues) {\n        visualElement.setStaticValue(key, resetValues[key]);\n      }\n      // Schedule a render for the next frame. This ensures we won't visually\n      // see the element with the reset rotate value applied.\n      visualElement.scheduleRender();\n    }\n    getProjectionStyles(styleProp = {}) {\n      var _a, _b, _c;\n      // TODO: Return lifecycle-persistent object\n      const styles = {};\n      if (!this.instance || this.isSVG) return styles;\n      if (!this.isVisible) {\n        return {\n          visibility: \"hidden\"\n        };\n      } else {\n        styles.visibility = \"\";\n      }\n      const transformTemplate = (_a = this.options.visualElement) === null || _a === void 0 ? void 0 : _a.getProps().transformTemplate;\n      if (this.needsReset) {\n        this.needsReset = false;\n        styles.opacity = \"\";\n        styles.pointerEvents = resolveMotionValue(styleProp.pointerEvents) || \"\";\n        styles.transform = transformTemplate ? transformTemplate(this.latestValues, \"\") : \"none\";\n        return styles;\n      }\n      const lead = this.getLead();\n      if (!this.projectionDelta || !this.layout || !lead.target) {\n        const emptyStyles = {};\n        if (this.options.layoutId) {\n          emptyStyles.opacity = this.latestValues.opacity !== undefined ? this.latestValues.opacity : 1;\n          emptyStyles.pointerEvents = resolveMotionValue(styleProp.pointerEvents) || \"\";\n        }\n        if (this.hasProjected && !hasTransform(this.latestValues)) {\n          emptyStyles.transform = transformTemplate ? transformTemplate({}, \"\") : \"none\";\n          this.hasProjected = false;\n        }\n        return emptyStyles;\n      }\n      const valuesToRender = lead.animationValues || lead.latestValues;\n      this.applyTransformsToTarget();\n      styles.transform = buildProjectionTransform(this.projectionDeltaWithTransform, this.treeScale, valuesToRender);\n      if (transformTemplate) {\n        styles.transform = transformTemplate(valuesToRender, styles.transform);\n      }\n      const {\n        x,\n        y\n      } = this.projectionDelta;\n      styles.transformOrigin = `${x.origin * 100}% ${y.origin * 100}% 0`;\n      if (lead.animationValues) {\n        /**\n         * If the lead component is animating, assign this either the entering/leaving\n         * opacity\n         */\n        styles.opacity = lead === this ? (_c = (_b = valuesToRender.opacity) !== null && _b !== void 0 ? _b : this.latestValues.opacity) !== null && _c !== void 0 ? _c : 1 : this.preserveOpacity ? this.latestValues.opacity : valuesToRender.opacityExit;\n      } else {\n        /**\n         * Or we're not animating at all, set the lead component to its layout\n         * opacity and other components to hidden.\n         */\n        styles.opacity = lead === this ? valuesToRender.opacity !== undefined ? valuesToRender.opacity : \"\" : valuesToRender.opacityExit !== undefined ? valuesToRender.opacityExit : 0;\n      }\n      /**\n       * Apply scale correction\n       */\n      for (const key in scaleCorrectors) {\n        if (valuesToRender[key] === undefined) continue;\n        const {\n          correct,\n          applyTo\n        } = scaleCorrectors[key];\n        const corrected = correct(valuesToRender[key], lead);\n        if (applyTo) {\n          const num = applyTo.length;\n          for (let i = 0; i < num; i++) {\n            styles[applyTo[i]] = corrected;\n          }\n        } else {\n          styles[key] = corrected;\n        }\n      }\n      /**\n       * Disable pointer events on follow components. This is to ensure\n       * that if a follow component covers a lead component it doesn't block\n       * pointer events on the lead.\n       */\n      if (this.options.layoutId) {\n        styles.pointerEvents = lead === this ? resolveMotionValue(styleProp.pointerEvents) || \"\" : \"none\";\n      }\n      return styles;\n    }\n    clearSnapshot() {\n      this.resumeFrom = this.snapshot = undefined;\n    }\n    // Only run on root\n    resetTree() {\n      this.root.nodes.forEach(node => {\n        var _a;\n        return (_a = node.currentAnimation) === null || _a === void 0 ? void 0 : _a.stop();\n      });\n      this.root.nodes.forEach(clearMeasurements);\n      this.root.sharedNodes.clear();\n    }\n  };\n}\nfunction updateLayout(node) {\n  node.updateLayout();\n}\nfunction notifyLayoutUpdate(node) {\n  var _a, _b, _c;\n  const snapshot = ((_a = node.resumeFrom) === null || _a === void 0 ? void 0 : _a.snapshot) || node.snapshot;\n  if (node.isLead() && node.layout && snapshot && node.hasListeners(\"didUpdate\")) {\n    const {\n      layoutBox: layout,\n      measuredBox: measuredLayout\n    } = node.layout;\n    const {\n      animationType\n    } = node.options;\n    const isShared = snapshot.source !== node.layout.source;\n    // TODO Maybe we want to also resize the layout snapshot so we don't trigger\n    // animations for instance if layout=\"size\" and an element has only changed position\n    if (animationType === \"size\") {\n      eachAxis(axis => {\n        const axisSnapshot = isShared ? snapshot.measuredBox[axis] : snapshot.layoutBox[axis];\n        const length = calcLength(axisSnapshot);\n        axisSnapshot.min = layout[axis].min;\n        axisSnapshot.max = axisSnapshot.min + length;\n      });\n    } else if (shouldAnimatePositionOnly(animationType, snapshot.layoutBox, layout)) {\n      eachAxis(axis => {\n        const axisSnapshot = isShared ? snapshot.measuredBox[axis] : snapshot.layoutBox[axis];\n        const length = calcLength(layout[axis]);\n        axisSnapshot.max = axisSnapshot.min + length;\n      });\n    }\n    const layoutDelta = createDelta();\n    calcBoxDelta(layoutDelta, layout, snapshot.layoutBox);\n    const visualDelta = createDelta();\n    if (isShared) {\n      calcBoxDelta(visualDelta, node.applyTransform(measuredLayout, true), snapshot.measuredBox);\n    } else {\n      calcBoxDelta(visualDelta, layout, snapshot.layoutBox);\n    }\n    const hasLayoutChanged = !isDeltaZero(layoutDelta);\n    let hasRelativeTargetChanged = false;\n    if (!node.resumeFrom) {\n      const relativeParent = node.getClosestProjectingParent();\n      /**\n       * If the relativeParent is itself resuming from a different element then\n       * the relative snapshot is not relavent\n       */\n      if (relativeParent && !relativeParent.resumeFrom) {\n        const {\n          snapshot: parentSnapshot,\n          layout: parentLayout\n        } = relativeParent;\n        if (parentSnapshot && parentLayout) {\n          const relativeSnapshot = createBox();\n          calcRelativePosition(relativeSnapshot, snapshot.layoutBox, parentSnapshot.layoutBox);\n          const relativeLayout = createBox();\n          calcRelativePosition(relativeLayout, layout, parentLayout.layoutBox);\n          if (!boxEquals(relativeSnapshot, relativeLayout)) {\n            hasRelativeTargetChanged = true;\n          }\n        }\n      }\n    }\n    node.notifyListeners(\"didUpdate\", {\n      layout,\n      snapshot,\n      delta: visualDelta,\n      layoutDelta,\n      hasLayoutChanged,\n      hasRelativeTargetChanged\n    });\n  } else if (node.isLead()) {\n    (_c = (_b = node.options).onExitComplete) === null || _c === void 0 ? void 0 : _c.call(_b);\n  }\n  /**\n   * Clearing transition\n   * TODO: Investigate why this transition is being passed in as {type: false } from Framer\n   * and why we need it at all\n   */\n  node.options.transition = undefined;\n}\nfunction propagateDirtyNodes(node) {\n  /**\n   * Propagate isProjectionDirty. Nodes are ordered by depth, so if the parent here\n   * is dirty we can simply pass this forward.\n   */\n  node.isProjectionDirty || (node.isProjectionDirty = Boolean(node.parent && node.parent.isProjectionDirty));\n  /**\n   * Propagate isTransformDirty.\n   */\n  node.isTransformDirty || (node.isTransformDirty = Boolean(node.parent && node.parent.isTransformDirty));\n}\nfunction clearSnapshot(node) {\n  node.clearSnapshot();\n}\nfunction clearMeasurements(node) {\n  node.clearMeasurements();\n}\nfunction resetTransformStyle(node) {\n  const {\n    visualElement\n  } = node.options;\n  if (visualElement === null || visualElement === void 0 ? void 0 : visualElement.getProps().onBeforeLayoutMeasure) {\n    visualElement.notify(\"BeforeLayoutMeasure\");\n  }\n  node.resetTransform();\n}\nfunction finishAnimation(node) {\n  node.finishAnimation();\n  node.targetDelta = node.relativeTarget = node.target = undefined;\n}\nfunction resolveTargetDelta(node) {\n  node.resolveTargetDelta();\n}\nfunction calcProjection(node) {\n  node.calcProjection();\n}\nfunction resetRotation(node) {\n  node.resetRotation();\n}\nfunction removeLeadSnapshots(stack) {\n  stack.removeLeadSnapshot();\n}\nfunction mixAxisDelta(output, delta, p) {\n  output.translate = mix(delta.translate, 0, p);\n  output.scale = mix(delta.scale, 1, p);\n  output.origin = delta.origin;\n  output.originPoint = delta.originPoint;\n}\nfunction mixAxis(output, from, to, p) {\n  output.min = mix(from.min, to.min, p);\n  output.max = mix(from.max, to.max, p);\n}\nfunction mixBox(output, from, to, p) {\n  mixAxis(output.x, from.x, to.x, p);\n  mixAxis(output.y, from.y, to.y, p);\n}\nfunction hasOpacityCrossfade(node) {\n  return node.animationValues && node.animationValues.opacityExit !== undefined;\n}\nconst defaultLayoutTransition = {\n  duration: 0.45,\n  ease: [0.4, 0, 0.1, 1]\n};\nfunction mountNodeEarly(node, elementId) {\n  /**\n   * Rather than searching the DOM from document we can search the\n   * path for the deepest mounted ancestor and search from there\n   */\n  let searchNode = node.root;\n  for (let i = node.path.length - 1; i >= 0; i--) {\n    if (Boolean(node.path[i].instance)) {\n      searchNode = node.path[i];\n      break;\n    }\n  }\n  const searchElement = searchNode && searchNode !== node.root ? searchNode.instance : document;\n  const element = searchElement.querySelector(`[data-projection-id=\"${elementId}\"]`);\n  if (element) node.mount(element, true);\n}\nfunction roundAxis(axis) {\n  axis.min = Math.round(axis.min);\n  axis.max = Math.round(axis.max);\n}\nfunction roundBox(box) {\n  roundAxis(box.x);\n  roundAxis(box.y);\n}\nfunction shouldAnimatePositionOnly(animationType, snapshot, layout) {\n  return animationType === \"position\" || animationType === \"preserve-aspect\" && !isNear(aspectRatio(snapshot), aspectRatio(layout), 0.2);\n}\nexport { createProjectionNode, mixAxis, mixAxisDelta, mixBox, propagateDirtyNodes };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}