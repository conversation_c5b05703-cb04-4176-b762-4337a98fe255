{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"checked\", \"className\", \"componentsProps\", \"control\", \"disabled\", \"disableTypography\", \"inputRef\", \"label\", \"labelPlacement\", \"name\", \"onChange\", \"required\", \"slotProps\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useFormControl } from '../FormControl';\nimport Stack from '../Stack';\nimport Typography from '../Typography';\nimport capitalize from '../utils/capitalize';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport formControlLabelClasses, { getFormControlLabelUtilityClasses } from './formControlLabelClasses';\nimport formControlState from '../FormControl/formControlState';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disabled,\n    labelPlacement,\n    error,\n    required\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', \"labelPlacement\".concat(capitalize(labelPlacement)), error && 'error', required && 'required'],\n    label: ['label', disabled && 'disabled'],\n    asterisk: ['asterisk', error && 'error']\n  };\n  return composeClasses(slots, getFormControlLabelUtilityClasses, classes);\n};\nexport const FormControlLabelRoot = styled('label', {\n  name: 'MuiFormControlLabel',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [\"& .\".concat(formControlLabelClasses.label)]: styles.label\n    }, styles.root, styles[\"labelPlacement\".concat(capitalize(ownerState.labelPlacement))]];\n  }\n})(_ref2 => {\n  let {\n    theme,\n    ownerState\n  } = _ref2;\n  return _extends({\n    display: 'inline-flex',\n    alignItems: 'center',\n    cursor: 'pointer',\n    // For correct alignment with the text.\n    verticalAlign: 'middle',\n    WebkitTapHighlightColor: 'transparent',\n    marginLeft: -11,\n    marginRight: 16,\n    // used for row presentation of radio/checkbox\n    [\"&.\".concat(formControlLabelClasses.disabled)]: {\n      cursor: 'default'\n    }\n  }, ownerState.labelPlacement === 'start' && {\n    flexDirection: 'row-reverse',\n    marginLeft: 16,\n    // used for row presentation of radio/checkbox\n    marginRight: -11\n  }, ownerState.labelPlacement === 'top' && {\n    flexDirection: 'column-reverse',\n    marginLeft: 16\n  }, ownerState.labelPlacement === 'bottom' && {\n    flexDirection: 'column',\n    marginLeft: 16\n  }, {\n    [\"& .\".concat(formControlLabelClasses.label)]: {\n      [\"&.\".concat(formControlLabelClasses.disabled)]: {\n        color: (theme.vars || theme).palette.text.disabled\n      }\n    }\n  });\n});\nconst AsteriskComponent = styled('span', {\n  name: 'MuiFormControlLabel',\n  slot: 'Asterisk',\n  overridesResolver: (props, styles) => styles.asterisk\n})(_ref3 => {\n  let {\n    theme\n  } = _ref3;\n  return {\n    [\"&.\".concat(formControlLabelClasses.error)]: {\n      color: (theme.vars || theme).palette.error.main\n    }\n  };\n});\n\n/**\n * Drop-in replacement of the `Radio`, `Switch` and `Checkbox` component.\n * Use this component if you want to display an extra label.\n */\nconst FormControlLabel = /*#__PURE__*/React.forwardRef(function FormControlLabel(inProps, ref) {\n  var _ref, _slotProps$typography;\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiFormControlLabel'\n  });\n  const {\n      className,\n      componentsProps = {},\n      control,\n      disabled: disabledProp,\n      disableTypography,\n      label: labelProp,\n      labelPlacement = 'end',\n      required: requiredProp,\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const muiFormControl = useFormControl();\n  const disabled = (_ref = disabledProp != null ? disabledProp : control.props.disabled) != null ? _ref : muiFormControl == null ? void 0 : muiFormControl.disabled;\n  const required = requiredProp != null ? requiredProp : control.props.required;\n  const controlProps = {\n    disabled,\n    required\n  };\n  ['checked', 'name', 'onChange', 'value', 'inputRef'].forEach(key => {\n    if (typeof control.props[key] === 'undefined' && typeof props[key] !== 'undefined') {\n      controlProps[key] = props[key];\n    }\n  });\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['error']\n  });\n  const ownerState = _extends({}, props, {\n    disabled,\n    labelPlacement,\n    required,\n    error: fcs.error\n  });\n  const classes = useUtilityClasses(ownerState);\n  const typographySlotProps = (_slotProps$typography = slotProps.typography) != null ? _slotProps$typography : componentsProps.typography;\n  let label = labelProp;\n  if (label != null && label.type !== Typography && !disableTypography) {\n    label = /*#__PURE__*/_jsx(Typography, _extends({\n      component: \"span\"\n    }, typographySlotProps, {\n      className: clsx(classes.label, typographySlotProps == null ? void 0 : typographySlotProps.className),\n      children: label\n    }));\n  }\n  return /*#__PURE__*/_jsxs(FormControlLabelRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other, {\n    children: [/*#__PURE__*/React.cloneElement(control, controlProps), required ? /*#__PURE__*/_jsxs(Stack, {\n      display: \"block\",\n      children: [label, /*#__PURE__*/_jsxs(AsteriskComponent, {\n        ownerState: ownerState,\n        \"aria-hidden\": true,\n        className: classes.asterisk,\n        children: [\"\\u2009\", '*']\n      })]\n    }) : label]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? FormControlLabel.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the component appears selected.\n   */\n  checked: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    typography: PropTypes.object\n  }),\n  /**\n   * A control element. For instance, it can be a `Radio`, a `Switch` or a `Checkbox`.\n   */\n  control: PropTypes.element.isRequired,\n  /**\n   * If `true`, the control is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the label is rendered as it is passed without an additional typography node.\n   */\n  disableTypography: PropTypes.bool,\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * A text or an element to be used in an enclosing label element.\n   */\n  label: PropTypes.node,\n  /**\n   * The position of the label.\n   * @default 'end'\n   */\n  labelPlacement: PropTypes.oneOf(['bottom', 'end', 'start', 'top']),\n  /**\n   * @ignore\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the label will indicate that the `input` is required.\n   */\n  required: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    typography: PropTypes.object\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the component.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default FormControlLabel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}