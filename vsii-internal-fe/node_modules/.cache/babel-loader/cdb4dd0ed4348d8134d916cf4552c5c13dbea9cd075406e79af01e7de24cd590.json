{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/LevelSkill.tsx\";\nimport { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { MultipleSelect } from 'components/extended/Form';\nimport { searchFormConfig } from './Config';\nimport { LEVEL } from 'constants/Common';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst LevelSkill = ({\n  handleChange,\n  label\n}) => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(MultipleSelect, {\n      isMultipleLanguage: true,\n      selects: LEVEL,\n      handleChange: handleChange,\n      name: searchFormConfig.levelSKill.name,\n      label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n        id: label || searchFormConfig.levelSKill.label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 24\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 13\n    }, this)\n  }, void 0, false);\n};\n_c = LevelSkill;\nexport default LevelSkill;\nvar _c;\n$RefreshReg$(_c, \"LevelSkill\");", "map": {"version": 3, "names": ["FormattedMessage", "MultipleSelect", "searchFormConfig", "LEVEL", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LevelSkill", "handleChange", "label", "children", "isMultipleLanguage", "selects", "name", "levelSKill", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/LevelSkill.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\n\n// project imports\nimport { MultipleSelect } from 'components/extended/Form';\nimport { searchFormConfig } from './Config';\nimport { LEVEL } from 'constants/Common';\n\ninterface ILevelSkillProps {\n    label?: string;\n    handleChange?: (values: string[]) => void;\n}\n\nconst LevelSkill = ({ handleChange, label }: ILevelSkillProps) => {\n    return (\n        <>\n            <MultipleSelect\n                isMultipleLanguage\n                selects={LEVEL}\n                handleChange={handleChange}\n                name={searchFormConfig.levelSKill.name}\n                label={<FormattedMessage id={label || searchFormConfig.levelSKill.label} />}\n            />\n        </>\n    );\n};\n\nexport default LevelSkill;\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,gBAAgB,QAAQ,UAAU;AAC3C,SAASC,KAAK,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOzC,MAAMC,UAAU,GAAGA,CAAC;EAAEC,YAAY;EAAEC;AAAwB,CAAC,KAAK;EAC9D,oBACIL,OAAA,CAAAE,SAAA;IAAAI,QAAA,eACIN,OAAA,CAACJ,cAAc;MACXW,kBAAkB;MAClBC,OAAO,EAAEV,KAAM;MACfM,YAAY,EAAEA,YAAa;MAC3BK,IAAI,EAAEZ,gBAAgB,CAACa,UAAU,CAACD,IAAK;MACvCJ,KAAK,eAAEL,OAAA,CAACL,gBAAgB;QAACgB,EAAE,EAAEN,KAAK,IAAIR,gBAAgB,CAACa,UAAU,CAACL;MAAM;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E;EAAC,gBACJ,CAAC;AAEX,CAAC;AAACC,EAAA,GAZIb,UAAU;AAchB,eAAeA,UAAU;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}