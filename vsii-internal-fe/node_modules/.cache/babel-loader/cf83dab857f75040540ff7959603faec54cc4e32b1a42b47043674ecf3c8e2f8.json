{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nlet isCallingCanDrag = false;\nlet isCallingIsDragging = false;\nexport class DragSourceMonitorImpl {\n  receiveHandlerId(sourceId) {\n    this.sourceId = sourceId;\n  }\n  getHandlerId() {\n    return this.sourceId;\n  }\n  canDrag() {\n    invariant(!isCallingCanDrag, 'You may not call monitor.canDrag() inside your canDrag() implementation. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor');\n    try {\n      isCallingCanDrag = true;\n      return this.internalMonitor.canDragSource(this.sourceId);\n    } finally {\n      isCallingCanDrag = false;\n    }\n  }\n  isDragging() {\n    if (!this.sourceId) {\n      return false;\n    }\n    invariant(!isCallingIsDragging, 'You may not call monitor.isDragging() inside your isDragging() implementation. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor');\n    try {\n      isCallingIsDragging = true;\n      return this.internalMonitor.isDraggingSource(this.sourceId);\n    } finally {\n      isCallingIsDragging = false;\n    }\n  }\n  subscribeToStateChange(listener, options) {\n    return this.internalMonitor.subscribeToStateChange(listener, options);\n  }\n  isDraggingSource(sourceId) {\n    return this.internalMonitor.isDraggingSource(sourceId);\n  }\n  isOverTarget(targetId, options) {\n    return this.internalMonitor.isOverTarget(targetId, options);\n  }\n  getTargetIds() {\n    return this.internalMonitor.getTargetIds();\n  }\n  isSourcePublic() {\n    return this.internalMonitor.isSourcePublic();\n  }\n  getSourceId() {\n    return this.internalMonitor.getSourceId();\n  }\n  subscribeToOffsetChange(listener) {\n    return this.internalMonitor.subscribeToOffsetChange(listener);\n  }\n  canDragSource(sourceId) {\n    return this.internalMonitor.canDragSource(sourceId);\n  }\n  canDropOnTarget(targetId) {\n    return this.internalMonitor.canDropOnTarget(targetId);\n  }\n  getItemType() {\n    return this.internalMonitor.getItemType();\n  }\n  getItem() {\n    return this.internalMonitor.getItem();\n  }\n  getDropResult() {\n    return this.internalMonitor.getDropResult();\n  }\n  didDrop() {\n    return this.internalMonitor.didDrop();\n  }\n  getInitialClientOffset() {\n    return this.internalMonitor.getInitialClientOffset();\n  }\n  getInitialSourceClientOffset() {\n    return this.internalMonitor.getInitialSourceClientOffset();\n  }\n  getSourceClientOffset() {\n    return this.internalMonitor.getSourceClientOffset();\n  }\n  getClientOffset() {\n    return this.internalMonitor.getClientOffset();\n  }\n  getDifferenceFromInitialOffset() {\n    return this.internalMonitor.getDifferenceFromInitialOffset();\n  }\n  constructor(manager) {\n    this.sourceId = null;\n    this.internalMonitor = manager.getMonitor();\n  }\n}\n\n//# sourceMappingURL=DragSourceMonitorImpl.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}