{"ast": null, "code": "// material-ui\nimport{TableCell,TableHead,TableRow}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ErrorImportThead=()=>{return/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:\"Sheet Name\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Message\"})]})});};export default ErrorImportThead;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}