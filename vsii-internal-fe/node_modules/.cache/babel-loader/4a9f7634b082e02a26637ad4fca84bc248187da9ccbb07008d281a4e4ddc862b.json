{"ast": null, "code": "import React from'react';// project imports\nimport{FormProvider}from'components/extended/Form';import{yupResolver}from'@hookform/resolvers/yup';// yup\nimport{jsx as _jsx}from\"react/jsx-runtime\";function SearchForm(props){const{children,defaultValues,formSchema,handleSubmit,formReset}=props;return/*#__PURE__*/_jsx(FormProvider,{form:{defaultValues,resolver:yupResolver(formSchema)},formReset:formReset,onSubmit:handleSubmit,children:children});}export default SearchForm;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}