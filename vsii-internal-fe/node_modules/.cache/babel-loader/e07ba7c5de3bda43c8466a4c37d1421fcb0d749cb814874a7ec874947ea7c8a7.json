{"ast": null, "code": "import React,{useEffect,useState}from'react';// project imports\nimport{useAppDispatch}from'app/hooks';import MainCard from'components/cards/MainCard';import{Table,TableFooter}from'components/extended/Table';import Api from'constants/Api';import{PERMISSION_EXPANDED_DEFAULT_VALUE,SEARCH_PARAM_KEY,TEXT_CONFIG_SCREEN,TREEITEM_DEFAULT_VALUE,paginationParamDefault,paginationResponseDefault}from'constants/Common';import{TableToolbar}from'containers';import{AddOrEditGroup,ManageGroupSearch,ManageGroupTBody,ManageGroupThead}from'containers/administration';import{FilterCollapse}from'containers/search';import sendRequest from'services/ApiService';import{openSnackbar}from'store/slice/snackbarSlice';import{groupSearchConfig}from'./Config';import{TreeItem}from'components/extended/Tree';import{checkAllowedPermission}from'utils/authorization';import{PERMISSIONS}from'constants/Permission';import{getSearchParam,transformObject}from'utils/common';import{getUserInfo}from'store/slice/authSlice';// third party\nimport{useSearchParams}from'react-router-dom';// ==============================|| Manage Group ||============================== //\n/**\n *  URL Params\n *  page\n *  size\n *  groupCode\n *  groupName\n */import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Group=()=>{const{manage_group}=TEXT_CONFIG_SCREEN.administration;// URL Params\nconst[searchParams,setSearchParams]=useSearchParams();const keyParams=[SEARCH_PARAM_KEY.page,SEARCH_PARAM_KEY.size,SEARCH_PARAM_KEY.groupCode,SEARCH_PARAM_KEY.groupName];const params=getSearchParam(keyParams,searchParams);transformObject(params);// Hooks, State, Variable\nconst dispatch=useAppDispatch();const[loading,setLoading]=useState(false);const[loadingDataAssignedUser,setloadingDataAssignedUser]=useState(false);const[paginationResponse,setPaginationResponse]=useState({...paginationResponseDefault,pageNumber:params.page?params.page:paginationResponseDefault.pageNumber,pageSize:params.size?params.size:paginationResponseDefault.pageSize});const[group,setGroup]=useState();const[groups,setGroups]=useState([]);const[assignedUser,setAssignedUser]=useState([]);const[conditions,setConditions]=useState({...groupSearchConfig,...params});const[formReset]=useState({...groupSearchConfig,...params});const[tabValue,setTabValue]=useState(0);const[open,setOpen]=useState(false);const[isEdit,setIsEdit]=useState(false);const[selectedPermission,setSelectedPermission]=useState([]);const[selectedExpanded,setSelectedExpanded]=useState([]);const[permission,setPermission]=useState(TREEITEM_DEFAULT_VALUE);const{groupPermission}=PERMISSIONS.admin;// Function\nconst getDataTable=async()=>{setLoading(true);const response=await sendRequest(Api.group.getAll,{...conditions,page:conditions.page+1});if(response){const{status,result}=response;if(status){const{content,pagination}=result;setGroups(content);setPaginationResponse(pagination?{...paginationResponse,totalElement:pagination.totalElement}:paginationResponseDefault);setLoading(false);}else{setDataEmpty();}}else{setDataEmpty();}};const getDataTableUser=async()=>{if(isEdit){setloadingDataAssignedUser(true);const response=await sendRequest(Api.member.getAll,{groupId:group===null||group===void 0?void 0:group.groupId});if(response){const{status,result}=response;if(status&&result){const{content}=result;setAssignedUser(content);setloadingDataAssignedUser(false);}else{setDataEmpty();}}}else{setDataEmpty();}};const getAllFunction=async()=>{const response=await sendRequest(Api.master.getFunctionAll);if(response){const{status,result}=response;if(status){setPermission({...permission,children:result.content});}}};const setDataEmpty=()=>{setGroups([]);setAssignedUser([]);setLoading(false);setPaginationResponse(paginationResponseDefault);};// Event\nconst handleChangePage=(event,newPage)=>{setConditions({...conditions,page:newPage});setSearchParams({...params,page:newPage});};const handleChangeRowsPerPage=event=>{setConditions({...conditions,page:paginationParamDefault.page,size:parseInt(event.target.value,10)});setSearchParams({...params,page:paginationParamDefault.page,size:parseInt(event.target.value,10)});};const handleOpenDialog=item=>{if(item){setIsEdit(true);if(item.children.length>0){item.children.forEach(module=>{module.children&&module.children.forEach(mainFnc=>{mainFnc.children&&mainFnc.children.forEach(fnc=>{setSelectedPermission(prv=>[...prv,fnc.value]);setSelectedExpanded(prv=>[...prv,fnc.value]);});setSelectedExpanded(prv=>[...prv,mainFnc.value]);});setSelectedExpanded(prv=>[...prv,module.value]);});}else{setSelectedExpanded(PERMISSION_EXPANDED_DEFAULT_VALUE);}setGroup(item);}else{setIsEdit(false);setGroup(undefined);setSelectedPermission([]);}setTabValue(0);setOpen(true);};const handleCloseDialog=()=>{setSelectedExpanded([]);setSelectedPermission([]);setOpen(false);};const handleChangeTab=(event,newTabValue)=>{setTabValue(newTabValue);};// Handle submit\nconst handleSearch=value=>{transformObject(value);setSearchParams({...value,page:paginationParamDefault.page,size:conditions.size});setConditions({...value,page:paginationParamDefault.page,size:conditions.size});};const handlePostSaveOrUpdateGroup=async values=>{const payload={...values,functions:selectedPermission};const response=await sendRequest(Api.group.postSaveOrUpdate,payload);if(response){const{status,result}=response;if(status){if(result.content){await dispatch(getUserInfo({showLoadingScreen:false}));handleCloseDialog();getDataTable();dispatch(openSnackbar({open:true,message:isEdit?'update-success':'add-success',variant:'alert',alert:{color:'success'}}));}}}};// Effect\nuseEffect(()=>{getDataTable();// eslint-disable-next-line react-hooks/exhaustive-deps\n},[conditions]);useEffect(()=>{getDataTableUser();// eslint-disable-next-line react-hooks/exhaustive-deps\n},[group===null||group===void 0?void 0:group.groupId]);useEffect(()=>{getAllFunction();// eslint-disable-next-line react-hooks/exhaustive-deps\n},[]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FilterCollapse,{children:/*#__PURE__*/_jsx(ManageGroupSearch,{formReset:formReset,handleSearch:handleSearch})}),/*#__PURE__*/_jsxs(MainCard,{children:[checkAllowedPermission(groupPermission.add)&&/*#__PURE__*/_jsx(TableToolbar,{handleOpen:handleOpenDialog,handleRefreshData:getDataTable,addLabel:manage_group+'add-new'}),/*#__PURE__*/_jsx(Table,{heads:/*#__PURE__*/_jsx(ManageGroupThead,{}),isLoading:loading,data:groups,children:/*#__PURE__*/_jsx(ManageGroupTBody,{pageNumber:conditions.page,pageSize:conditions.size,groups:groups,handleOpen:handleOpenDialog})})]}),!loading&&/*#__PURE__*/_jsx(TableFooter,{pagination:{total:paginationResponse.totalElement,page:conditions.page,size:conditions.size},onPageChange:handleChangePage,onRowsPerPageChange:handleChangeRowsPerPage}),/*#__PURE__*/_jsx(AddOrEditGroup,{modal:{open,handleClose:handleCloseDialog},tab:{tabValue,handleChangeTab},group:group,assignedUser:assignedUser,loading:loadingDataAssignedUser,isEdit:isEdit,handleSubmit:handlePostSaveOrUpdateGroup,selectedExpand:selectedExpanded,renderTree:/*#__PURE__*/_jsx(TreeItem,{list:permission,setSelected:setSelectedPermission,selected:selectedPermission})})]});};export default Group;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}