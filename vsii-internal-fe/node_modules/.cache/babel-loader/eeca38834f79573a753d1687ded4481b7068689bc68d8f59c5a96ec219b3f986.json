{"ast": null, "code": "var basePickBy = require('./_basePickBy'),\n  hasIn = require('./hasIn');\n\n/**\n * The base implementation of `_.pick` without support for individual\n * property identifiers.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @returns {Object} Returns the new object.\n */\nfunction basePick(object, paths) {\n  return basePickBy(object, paths, function (value, path) {\n    return hasIn(object, path);\n  });\n}\nmodule.exports = basePick;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}