{"ast": null, "code": "export const searchFormConfig = {\n  year: {\n    label: 'year',\n    name: 'year'\n  },\n  month: {\n    label: 'month',\n    name: 'month'\n  },\n  week: {\n    label: 'weeks',\n    name: 'week'\n  },\n  timeStatus: {\n    label: 'time-status',\n    name: 'timeStatus'\n  },\n  department: {\n    label: 'department',\n    name: 'departmentId',\n    manage: {\n      label: 'department-full',\n      name: 'deptId'\n    }\n  },\n  project: {\n    label: 'project',\n    name: 'projectId'\n  },\n  userId: {\n    label: 'member',\n    name: 'userId'\n  },\n  userName: {\n    label: 'user-name',\n    name: 'userName'\n  },\n  status: {\n    label: 'status',\n    name: 'status'\n  },\n  memberCode: {\n    label: 'member-code',\n    name: 'memberCode'\n  },\n  titleCode: {\n    label: 'location',\n    name: 'titleCode'\n  },\n  rankId: {\n    label: 'level',\n    name: 'rankId'\n  },\n  projectType: {\n    label: 'project-type',\n    name: 'projectType',\n    manage: {\n      label: 'project-type',\n      name: 'typeCode'\n    }\n  },\n  holidayType: {\n    label: 'holiday-type',\n    name: 'type'\n  },\n  billable: {\n    label: 'Billable',\n    name: 'billable'\n  },\n  headcount: {\n    label: 'Headcount',\n    name: 'roleId'\n  },\n  period: {\n    label: 'period',\n    name: 'period'\n  },\n  specialHours: {\n    label: 'special-holiday-type',\n    name: 'type'\n  },\n  productivityType: {\n    label: 'service-type',\n    name: 'serviceType'\n  },\n  contractType: {\n    label: 'contract-type',\n    name: 'contractType'\n  },\n  currency: {\n    label: 'currency',\n    name: 'currency'\n  },\n  roleType: {\n    label: 'role',\n    name: 'role'\n  },\n  paymentTermType: {\n    label: 'payment-term',\n    name: 'paymentTerm'\n  },\n  partnerName: {\n    label: 'partner-name',\n    name: 'partnerName'\n  },\n  statusRequestsChecking: {\n    label: 'status',\n    name: 'status'\n  },\n  receivedDate: {\n    label: 'received-date',\n    name: 'receivedDate'\n  },\n  picUserName: {\n    label: 'pic-user-name',\n    name: 'picUserName'\n  },\n  possibility: {\n    label: 'possibility',\n    name: 'possibility'\n  },\n  date: {\n    label: 'date',\n    name: 'date'\n  },\n  supplierName: {\n    label: 'supplier-name',\n    name: 'supplierName'\n  },\n  workType: {\n    label: 'work-type',\n    name: 'workType'\n  },\n  weekDays: {\n    label: 'weekdays',\n    name: 'timeSendMail.weekdays'\n  },\n  emailType: {\n    label: 'email-type',\n    name: 'emailType'\n  },\n  dayInMonth: {\n    label: 'day-in-month',\n    name: 'timeSendMail.day'\n  },\n  groupType: {\n    label: 'group-type',\n    name: 'groupType'\n  },\n  contractor: {\n    label: 'contractor',\n    name: 'contractor'\n  },\n  projectManager: {\n    label: 'project-manager',\n    name: 'projectManager'\n  },\n  idHexString: {\n    label: 'idHexString',\n    name: 'idHexString'\n  },\n  salePipelineType: {\n    label: 'sale-pipeline-type',\n    name: 'type'\n  },\n  salePipelineStatus: {\n    label: 'sale-pipeline-status',\n    name: 'status'\n  },\n  productionPerformance: {\n    label: 'project',\n    name: 'productionPerformanceIdHexString'\n  },\n  salePipelineBudgetingPlanServiceType: {\n    label: 'service-type',\n    name: 'type'\n  },\n  biddingStatus: {\n    label: 'status',\n    name: 'status'\n  },\n  type: {\n    label: 'type',\n    name: 'type'\n  },\n  titleCodeSkillReport: {\n    label: 'title-code',\n    name: 'titleCode'\n  },\n  skill: {\n    label: 'skill',\n    name: 'skill'\n  },\n  dataSource: {\n    label: 'data-source',\n    name: 'dataSource'\n  },\n  levelSKill: {\n    label: 'level',\n    name: 'level'\n  },\n  degree: {\n    label: 'degree',\n    name: 'degree'\n  },\n  language: {\n    label: 'language',\n    name: 'language'\n  },\n  technologies: {\n    label: 'technologies',\n    name: 'technologies'\n  },\n  unit: {\n    label: 'unit',\n    name: 'unit'\n  },\n  budgetingPlanType: {\n    label: 'type',\n    name: 'pipelineType'\n  },\n  biddingPackageName: {\n    label: 'bidding-package-name',\n    name: 'biddingPackagesName'\n  },\n  address: {\n    label: 'address',\n    name: 'address'\n  },\n  reportName: {\n    label: 'report-name',\n    name: 'reportName'\n  },\n  columnName: {\n    label: 'column-name',\n    name: 'columnName'\n  },\n  inputType: {\n    label: 'input-type',\n    name: 'inputType'\n  },\n  layoutOption: {\n    label: 'layout',\n    name: 'layout'\n  },\n  conditionType: {\n    label: 'condition',\n    name: 'conditionType'\n  }\n};", "map": {"version": 3, "names": ["searchFormConfig", "year", "label", "name", "month", "week", "timeStatus", "department", "manage", "project", "userId", "userName", "status", "memberCode", "titleCode", "rankId", "projectType", "holidayType", "billable", "headcount", "period", "specialHours", "productivityType", "contractType", "currency", "roleType", "paymentTermType", "partner<PERSON>ame", "statusRequestsChecking", "receivedDate", "picUserName", "possibility", "date", "supplierName", "workType", "weekDays", "emailType", "dayInMonth", "groupType", "contractor", "projectManager", "idHexString", "salePipelineType", "salePipelineStatus", "productionPerformance", "salePipelineBudgetingPlanServiceType", "biddingStatus", "type", "titleCodeSkillReport", "skill", "dataSource", "levelSKill", "degree", "language", "technologies", "unit", "budgetingPlanType", "biddingPackageName", "address", "reportName", "columnName", "inputType", "layoutOption", "conditionType"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/Config.ts"], "sourcesContent": ["export const searchFormConfig = {\n    year: {\n        label: 'year',\n        name: 'year'\n    },\n    month: {\n        label: 'month',\n        name: 'month'\n    },\n    week: {\n        label: 'weeks',\n        name: 'week'\n    },\n    timeStatus: {\n        label: 'time-status',\n        name: 'timeStatus'\n    },\n    department: {\n        label: 'department',\n        name: 'departmentId',\n        manage: {\n            label: 'department-full',\n            name: 'deptId'\n        }\n    },\n    project: {\n        label: 'project',\n        name: 'projectId'\n    },\n    userId: {\n        label: 'member',\n        name: 'userId'\n    },\n    userName: {\n        label: 'user-name',\n        name: 'userName'\n    },\n    status: {\n        label: 'status',\n        name: 'status'\n    },\n    memberCode: {\n        label: 'member-code',\n        name: 'memberCode'\n    },\n    titleCode: {\n        label: 'location',\n        name: 'titleCode'\n    },\n    rankId: {\n        label: 'level',\n        name: 'rankId'\n    },\n    projectType: {\n        label: 'project-type',\n        name: 'projectType',\n        manage: {\n            label: 'project-type',\n            name: 'typeCode'\n        }\n    },\n    holidayType: {\n        label: 'holiday-type',\n        name: 'type'\n    },\n    billable: {\n        label: 'Billable',\n        name: 'billable'\n    },\n    headcount: {\n        label: 'Headcount',\n        name: 'roleId'\n    },\n    period: {\n        label: 'period',\n        name: 'period'\n    },\n    specialHours: {\n        label: 'special-holiday-type',\n        name: 'type'\n    },\n    productivityType: {\n        label: 'service-type',\n        name: 'serviceType'\n    },\n    contractType: {\n        label: 'contract-type',\n        name: 'contractType'\n    },\n    currency: {\n        label: 'currency',\n        name: 'currency'\n    },\n    roleType: {\n        label: 'role',\n        name: 'role'\n    },\n    paymentTermType: {\n        label: 'payment-term',\n        name: 'paymentTerm'\n    },\n    partnerName: {\n        label: 'partner-name',\n        name: 'partnerName'\n    },\n    statusRequestsChecking: {\n        label: 'status',\n        name: 'status'\n    },\n    receivedDate: {\n        label: 'received-date',\n        name: 'receivedDate'\n    },\n    picUserName: {\n        label: 'pic-user-name',\n        name: 'picUserName'\n    },\n    possibility: {\n        label: 'possibility',\n        name: 'possibility'\n    },\n    date: {\n        label: 'date',\n        name: 'date'\n    },\n    supplierName: {\n        label: 'supplier-name',\n        name: 'supplierName'\n    },\n    workType: {\n        label: 'work-type',\n        name: 'workType'\n    },\n    weekDays: {\n        label: 'weekdays',\n        name: 'timeSendMail.weekdays'\n    },\n    emailType: {\n        label: 'email-type',\n        name: 'emailType'\n    },\n    dayInMonth: {\n        label: 'day-in-month',\n        name: 'timeSendMail.day'\n    },\n    groupType: {\n        label: 'group-type',\n        name: 'groupType'\n    },\n    contractor: {\n        label: 'contractor',\n        name: 'contractor'\n    },\n    projectManager: {\n        label: 'project-manager',\n        name: 'projectManager'\n    },\n    idHexString: {\n        label: 'idHexString',\n        name: 'idHexString'\n    },\n    salePipelineType: {\n        label: 'sale-pipeline-type',\n        name: 'type'\n    },\n    salePipelineStatus: {\n        label: 'sale-pipeline-status',\n        name: 'status'\n    },\n    productionPerformance: {\n        label: 'project',\n        name: 'productionPerformanceIdHexString'\n    },\n    salePipelineBudgetingPlanServiceType: {\n        label: 'service-type',\n        name: 'type'\n    },\n    biddingStatus: {\n        label: 'status',\n        name: 'status'\n    },\n    type: {\n        label: 'type',\n        name: 'type'\n    },\n    titleCodeSkillReport: {\n        label: 'title-code',\n        name: 'titleCode'\n    },\n    skill: {\n        label: 'skill',\n        name: 'skill'\n    },\n    dataSource: {\n        label: 'data-source',\n        name: 'dataSource'\n    },\n    levelSKill: {\n        label: 'level',\n        name: 'level'\n    },\n    degree: {\n        label: 'degree',\n        name: 'degree'\n    },\n    language: {\n        label: 'language',\n        name: 'language'\n    },\n    technologies: {\n        label: 'technologies',\n        name: 'technologies'\n    },\n    unit: {\n        label: 'unit',\n        name: 'unit'\n    },\n    budgetingPlanType: {\n        label: 'type',\n        name: 'pipelineType'\n    },\n    biddingPackageName: {\n        label: 'bidding-package-name',\n        name: 'biddingPackagesName'\n    },\n    address: {\n        label: 'address',\n        name: 'address'\n    },\n    reportName: {\n        label: 'report-name',\n        name: 'reportName'\n    },\n    columnName: {\n        label: 'column-name',\n        name: 'columnName'\n    },\n    inputType: {\n        label: 'input-type',\n        name: 'inputType'\n    },\n    layoutOption: {\n        label: 'layout',\n        name: 'layout'\n    },\n    conditionType: {\n        label: 'condition',\n        name: 'conditionType'\n    }\n};\n"], "mappings": "AAAA,OAAO,MAAMA,gBAAgB,GAAG;EAC5BC,IAAI,EAAE;IACFC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACV,CAAC;EACDC,KAAK,EAAE;IACHF,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE;EACV,CAAC;EACDE,IAAI,EAAE;IACFH,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE;EACV,CAAC;EACDG,UAAU,EAAE;IACRJ,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE;EACV,CAAC;EACDI,UAAU,EAAE;IACRL,KAAK,EAAE,YAAY;IACnBC,IAAI,EAAE,cAAc;IACpBK,MAAM,EAAE;MACJN,KAAK,EAAE,iBAAiB;MACxBC,IAAI,EAAE;IACV;EACJ,CAAC;EACDM,OAAO,EAAE;IACLP,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE;EACV,CAAC;EACDO,MAAM,EAAE;IACJR,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE;EACV,CAAC;EACDQ,QAAQ,EAAE;IACNT,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE;EACV,CAAC;EACDS,MAAM,EAAE;IACJV,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE;EACV,CAAC;EACDU,UAAU,EAAE;IACRX,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE;EACV,CAAC;EACDW,SAAS,EAAE;IACPZ,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE;EACV,CAAC;EACDY,MAAM,EAAE;IACJb,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE;EACV,CAAC;EACDa,WAAW,EAAE;IACTd,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE,aAAa;IACnBK,MAAM,EAAE;MACJN,KAAK,EAAE,cAAc;MACrBC,IAAI,EAAE;IACV;EACJ,CAAC;EACDc,WAAW,EAAE;IACTf,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE;EACV,CAAC;EACDe,QAAQ,EAAE;IACNhB,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE;EACV,CAAC;EACDgB,SAAS,EAAE;IACPjB,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE;EACV,CAAC;EACDiB,MAAM,EAAE;IACJlB,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE;EACV,CAAC;EACDkB,YAAY,EAAE;IACVnB,KAAK,EAAE,sBAAsB;IAC7BC,IAAI,EAAE;EACV,CAAC;EACDmB,gBAAgB,EAAE;IACdpB,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE;EACV,CAAC;EACDoB,YAAY,EAAE;IACVrB,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE;EACV,CAAC;EACDqB,QAAQ,EAAE;IACNtB,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE;EACV,CAAC;EACDsB,QAAQ,EAAE;IACNvB,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACV,CAAC;EACDuB,eAAe,EAAE;IACbxB,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE;EACV,CAAC;EACDwB,WAAW,EAAE;IACTzB,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE;EACV,CAAC;EACDyB,sBAAsB,EAAE;IACpB1B,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE;EACV,CAAC;EACD0B,YAAY,EAAE;IACV3B,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE;EACV,CAAC;EACD2B,WAAW,EAAE;IACT5B,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE;EACV,CAAC;EACD4B,WAAW,EAAE;IACT7B,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE;EACV,CAAC;EACD6B,IAAI,EAAE;IACF9B,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACV,CAAC;EACD8B,YAAY,EAAE;IACV/B,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE;EACV,CAAC;EACD+B,QAAQ,EAAE;IACNhC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE;EACV,CAAC;EACDgC,QAAQ,EAAE;IACNjC,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE;EACV,CAAC;EACDiC,SAAS,EAAE;IACPlC,KAAK,EAAE,YAAY;IACnBC,IAAI,EAAE;EACV,CAAC;EACDkC,UAAU,EAAE;IACRnC,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE;EACV,CAAC;EACDmC,SAAS,EAAE;IACPpC,KAAK,EAAE,YAAY;IACnBC,IAAI,EAAE;EACV,CAAC;EACDoC,UAAU,EAAE;IACRrC,KAAK,EAAE,YAAY;IACnBC,IAAI,EAAE;EACV,CAAC;EACDqC,cAAc,EAAE;IACZtC,KAAK,EAAE,iBAAiB;IACxBC,IAAI,EAAE;EACV,CAAC;EACDsC,WAAW,EAAE;IACTvC,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE;EACV,CAAC;EACDuC,gBAAgB,EAAE;IACdxC,KAAK,EAAE,oBAAoB;IAC3BC,IAAI,EAAE;EACV,CAAC;EACDwC,kBAAkB,EAAE;IAChBzC,KAAK,EAAE,sBAAsB;IAC7BC,IAAI,EAAE;EACV,CAAC;EACDyC,qBAAqB,EAAE;IACnB1C,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE;EACV,CAAC;EACD0C,oCAAoC,EAAE;IAClC3C,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE;EACV,CAAC;EACD2C,aAAa,EAAE;IACX5C,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE;EACV,CAAC;EACD4C,IAAI,EAAE;IACF7C,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACV,CAAC;EACD6C,oBAAoB,EAAE;IAClB9C,KAAK,EAAE,YAAY;IACnBC,IAAI,EAAE;EACV,CAAC;EACD8C,KAAK,EAAE;IACH/C,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE;EACV,CAAC;EACD+C,UAAU,EAAE;IACRhD,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE;EACV,CAAC;EACDgD,UAAU,EAAE;IACRjD,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE;EACV,CAAC;EACDiD,MAAM,EAAE;IACJlD,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE;EACV,CAAC;EACDkD,QAAQ,EAAE;IACNnD,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE;EACV,CAAC;EACDmD,YAAY,EAAE;IACVpD,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE;EACV,CAAC;EACDoD,IAAI,EAAE;IACFrD,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACV,CAAC;EACDqD,iBAAiB,EAAE;IACftD,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACV,CAAC;EACDsD,kBAAkB,EAAE;IAChBvD,KAAK,EAAE,sBAAsB;IAC7BC,IAAI,EAAE;EACV,CAAC;EACDuD,OAAO,EAAE;IACLxD,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE;EACV,CAAC;EACDwD,UAAU,EAAE;IACRzD,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE;EACV,CAAC;EACDyD,UAAU,EAAE;IACR1D,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE;EACV,CAAC;EACD0D,SAAS,EAAE;IACP3D,KAAK,EAAE,YAAY;IACnBC,IAAI,EAAE;EACV,CAAC;EACD2D,YAAY,EAAE;IACV5D,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE;EACV,CAAC;EACD4D,aAAa,EAAE;IACX7D,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE;EACV;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}