{"ast": null, "code": "import { useMemo } from 'react';\nimport { SourceConnector } from '../../internals/index.js';\nimport { useDragDropManager } from '../useDragDropManager.js';\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect.js';\nexport function useDragSourceConnector(dragSourceOptions, dragPreviewOptions) {\n  const manager = useDragDropManager();\n  const connector = useMemo(() => new SourceConnector(manager.getBackend()), [manager]);\n  useIsomorphicLayoutEffect(() => {\n    connector.dragSourceOptions = dragSourceOptions || null;\n    connector.reconnect();\n    return () => connector.disconnectDragSource();\n  }, [connector, dragSourceOptions]);\n  useIsomorphicLayoutEffect(() => {\n    connector.dragPreviewOptions = dragPreviewOptions || null;\n    connector.reconnect();\n    return () => connector.disconnectDragPreview();\n  }, [connector, dragPreviewOptions]);\n  return connector;\n}\n\n//# sourceMappingURL=useDragSourceConnector.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}