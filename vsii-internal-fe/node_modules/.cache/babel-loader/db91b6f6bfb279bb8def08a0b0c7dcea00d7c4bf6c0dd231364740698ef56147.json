{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/ErrorImportThead.tsx\";\n// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ErrorImportThead = () => {\n  return /*#__PURE__*/_jsxDEV(TableHead, {\n    children: /*#__PURE__*/_jsxDEV(TableRow, {\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        children: \"Sheet Name\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: \"Message\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 9\n  }, this);\n};\n_c = ErrorImportThead;\nexport default ErrorImportThead;\nvar _c;\n$RefreshReg$(_c, \"ErrorImportThead\");", "map": {"version": 3, "names": ["TableCell", "TableHead", "TableRow", "jsxDEV", "_jsxDEV", "ErrorImportThead", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/ErrorImportThead.tsx"], "sourcesContent": ["// material-ui\nimport { TableCell, TableHead, TableRow } from '@mui/material';\n\nconst ErrorImportThead = () => {\n    return (\n        <TableHead>\n            <TableRow>\n                <TableCell>Sheet Name</TableCell>\n                <TableCell>Message</TableCell>\n            </TableRow>\n        </TableHead>\n    );\n};\n\nexport default ErrorImportThead;\n"], "mappings": ";AAAA;AACA,SAASA,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAC3B,oBACID,OAAA,CAACH,SAAS;IAAAK,QAAA,eACNF,OAAA,CAACF,QAAQ;MAAAI,QAAA,gBACLF,OAAA,CAACJ,SAAS;QAAAM,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACjCN,OAAA,CAACJ,SAAS;QAAAM,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEpB,CAAC;AAACC,EAAA,GATIN,gBAAgB;AAWtB,eAAeA,gBAAgB;AAAC,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}