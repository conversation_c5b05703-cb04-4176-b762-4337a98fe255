{"ast": null, "code": "import { sync } from '../frameloop/index.mjs';\nimport { useEffect } from 'react';\nimport { useInstantLayoutTransition } from '../projection/use-instant-layout-transition.mjs';\nimport { useForceUpdate } from './use-force-update.mjs';\nimport { instantAnimationState } from './use-instant-transition-state.mjs';\nfunction useInstantTransition() {\n  const [forceUpdate, forcedRenderCount] = useForceUpdate();\n  const startInstantLayoutTransition = useInstantLayoutTransition();\n  useEffect(() => {\n    /**\n     * Unblock after two animation frames, otherwise this will unblock too soon.\n     */\n    sync.postRender(() => sync.postRender(() => instantAnimationState.current = false));\n  }, [forcedRenderCount]);\n  return callback => {\n    startInstantLayoutTransition(() => {\n      instantAnimationState.current = true;\n      forceUpdate();\n      callback();\n    });\n  };\n}\nexport { useInstantTransition };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}