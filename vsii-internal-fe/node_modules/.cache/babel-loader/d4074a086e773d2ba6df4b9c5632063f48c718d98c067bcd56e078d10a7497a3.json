{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/SummarySearch.tsx\";\n// material-ui\nimport { Grid } from '@mui/material';\n\n// project imports\nimport { monthlyProductionPerformanceFilterConfig, monthlyProductionPerformanceFilterSchema } from 'pages/sales/Config';\nimport { SearchForm, SalesYear } from '../search';\nimport { E_SCREEN_SALES_YEAR } from 'constants/Common';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SummarySearch = props => {\n  const {\n    conditions,\n    handleChangeYear,\n    handleSearch\n  } = props;\n  const handleYearChange = e => {\n    const value = {\n      year: Number(e.target.value)\n    };\n    handleChangeYear(e);\n    handleSearch(value);\n  };\n  return /*#__PURE__*/_jsxDEV(SearchForm, {\n    defaultValues: monthlyProductionPerformanceFilterConfig,\n    formSchema: monthlyProductionPerformanceFilterSchema,\n    handleSubmit: handleSearch,\n    formReset: conditions,\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      alignItems: \"center\",\n      spacing: 2,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2,\n        children: /*#__PURE__*/_jsxDEV(SalesYear, {\n          handleChangeYear: handleYearChange,\n          screen: E_SCREEN_SALES_YEAR.SALES_PIPELINE_SUMMARY,\n          label: TEXT_CONFIG_SCREEN.salesReport.summary + '-year'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 9\n  }, this);\n};\n_c = SummarySearch;\nexport default SummarySearch;\nvar _c;\n$RefreshReg$(_c, \"SummarySearch\");", "map": {"version": 3, "names": ["Grid", "monthlyProductionPerformanceFilterConfig", "monthlyProductionPerformanceFilterSchema", "SearchForm", "SalesYear", "E_SCREEN_SALES_YEAR", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "SummarySearch", "props", "conditions", "handleChangeYear", "handleSearch", "handleYearChange", "e", "value", "year", "Number", "target", "defaultValues", "formSchema", "handleSubmit", "formReset", "children", "container", "alignItems", "spacing", "item", "xs", "lg", "screen", "SALES_PIPELINE_SUMMARY", "label", "salesReport", "summary", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/SummarySearch.tsx"], "sourcesContent": ["// material-ui\nimport { Grid, SelectChangeEvent } from '@mui/material';\n\n// project imports\nimport {\n    IMonthlyProductionPerformanceFilterConfig,\n    monthlyProductionPerformanceFilterConfig,\n    monthlyProductionPerformanceFilterSchema\n} from 'pages/sales/Config';\nimport { SearchForm, SalesYear } from '../search';\nimport { E_SCREEN_SALES_YEAR } from 'constants/Common';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\n\ninterface ISummarySearchProps {\n    conditions: IMonthlyProductionPerformanceFilterConfig;\n    handleChangeYear: (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => void;\n    handleSearch: (value: IMonthlyProductionPerformanceFilterConfig) => void;\n}\n\nconst SummarySearch = (props: ISummarySearchProps) => {\n    const { conditions, handleChangeYear, handleSearch } = props;\n\n    const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => {\n        const value = { year: Number(e.target.value) };\n        handleChangeYear(e);\n        handleSearch(value);\n    };\n\n    return (\n        <SearchForm\n            defaultValues={monthlyProductionPerformanceFilterConfig}\n            formSchema={monthlyProductionPerformanceFilterSchema}\n            handleSubmit={handleSearch}\n            formReset={conditions}\n        >\n            <Grid container alignItems=\"center\" spacing={2}>\n                <Grid item xs={12} lg={2}>\n                    <SalesYear\n                        handleChangeYear={handleYearChange}\n                        screen={E_SCREEN_SALES_YEAR.SALES_PIPELINE_SUMMARY}\n                        label={TEXT_CONFIG_SCREEN.salesReport.summary + '-year'}\n                    />\n                </Grid>\n            </Grid>\n        </SearchForm>\n    );\n};\n\nexport default SummarySearch;\n"], "mappings": ";AAAA;AACA,SAASA,IAAI,QAA2B,eAAe;;AAEvD;AACA,SAEIC,wCAAwC,EACxCC,wCAAwC,QACrC,oBAAoB;AAC3B,SAASC,UAAU,EAAEC,SAAS,QAAQ,WAAW;AACjD,SAASC,mBAAmB,QAAQ,kBAAkB;AACtD,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQtD,MAAMC,aAAa,GAAIC,KAA0B,IAAK;EAClD,MAAM;IAAEC,UAAU;IAAEC,gBAAgB;IAAEC;EAAa,CAAC,GAAGH,KAAK;EAE5D,MAAMI,gBAAgB,GAAIC,CAAoE,IAAK;IAC/F,MAAMC,KAAK,GAAG;MAAEC,IAAI,EAAEC,MAAM,CAACH,CAAC,CAACI,MAAM,CAACH,KAAK;IAAE,CAAC;IAC9CJ,gBAAgB,CAACG,CAAC,CAAC;IACnBF,YAAY,CAACG,KAAK,CAAC;EACvB,CAAC;EAED,oBACIR,OAAA,CAACL,UAAU;IACPiB,aAAa,EAAEnB,wCAAyC;IACxDoB,UAAU,EAAEnB,wCAAyC;IACrDoB,YAAY,EAAET,YAAa;IAC3BU,SAAS,EAAEZ,UAAW;IAAAa,QAAA,eAEtBhB,OAAA,CAACR,IAAI;MAACyB,SAAS;MAACC,UAAU,EAAC,QAAQ;MAACC,OAAO,EAAE,CAAE;MAAAH,QAAA,eAC3ChB,OAAA,CAACR,IAAI;QAAC4B,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,eACrBhB,OAAA,CAACJ,SAAS;UACNQ,gBAAgB,EAAEE,gBAAiB;UACnCiB,MAAM,EAAE1B,mBAAmB,CAAC2B,sBAAuB;UACnDC,KAAK,EAAE3B,kBAAkB,CAAC4B,WAAW,CAACC,OAAO,GAAG;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAErB,CAAC;AAACC,EAAA,GA3BI/B,aAAa;AA6BnB,eAAeA,aAAa;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}