{"ast": null, "code": "// Third party\nimport{Grid,Typography}from'@mui/material';import{FormattedMessage}from'react-intl';// project import\nimport{filterSprint,productReportSelector}from'store/slice/productReportSlice';import{useAppDispatch,useAppSelector}from'app/hooks';import{Select}from'components/extended/Form';import{formatPrice}from'utils/common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ProductInfo=()=>{var _projectDetail$sprint;const{sprints,projectDetail,sprintSelectDefaut}=useAppSelector(productReportSelector);const dispatch=useAppDispatch();return/*#__PURE__*/_jsxs(Grid,{container:true,gap:{xs:2,sm:0,md:0},justifyContent:\"space-between\",children:[/*#__PURE__*/_jsxs(Grid,{container:true,item:true,xs:12,sm:5.9,md:5.9,children:[/*#__PURE__*/_jsxs(Grid,{container:true,item:true,xs:12,sx:{mb:2},children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:3,display:\"flex\",alignItems:\"center\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"product-report.modal.leftSide.prjInfo.project\"})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:9,sx:{bgcolor:'#eeeeee',p:'10px 5px',borderRadius:'5px',border:'1px solid rgba(0, 0, 0, 0.26)'},children:/*#__PURE__*/_jsx(Typography,{children:projectDetail===null||projectDetail===void 0?void 0:projectDetail.projectName})})]}),/*#__PURE__*/_jsxs(Grid,{container:true,item:true,xs:12,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:3,display:\"flex\",alignItems:\"center\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"product-report.modal.leftSide.prjInfo.sprint\"})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:9,children:/*#__PURE__*/_jsx(Select,{name:\"sprint\",selects:((projectDetail===null||projectDetail===void 0?void 0:projectDetail.sprints.map(sprint=>({label:sprint.name,value:sprint.sprintId.toString()})))||[]).concat([{value:'all',label:'All'}]),handleChangeFullOption:_ref=>{let{value}=_ref;return dispatch(filterSprint(value));},defaultValue:sprintSelectDefaut||(projectDetail===null||projectDetail===void 0?void 0:(_projectDetail$sprint=projectDetail.sprints[0])===null||_projectDetail$sprint===void 0?void 0:_projectDetail$sprint.name)||'all',isControl:false})})]})]}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:5.9,md:5.9,gridRow:2,children:/*#__PURE__*/_jsxs(Grid,{container:true,item:true,xs:12,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:3,display:\"flex\",alignItems:\"center\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"product-report.modal.leftSide.prjInfo.sprintCost\"})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:9,sx:{bgcolor:'#eeeeee',p:'10px 5px',borderRadius:'5px',border:'1px solid rgba(0, 0, 0, 0.26)'},children:/*#__PURE__*/_jsx(Typography,{children:formatPrice(sprints.reduce((prev,next)=>prev+(next.currentSprintCost||0),0))})})]})})]});};export default ProductInfo;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}