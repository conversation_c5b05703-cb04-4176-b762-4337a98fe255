{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"disabled\", \"component\", \"components\", \"componentsProps\", \"label\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { getMenuItemUnstyledUtilityClass } from './menuItemUnstyledClasses';\nimport useMenuItem from './useMenuItem';\nimport composeClasses from '../composeClasses';\nimport useSlotProps from '../utils/useSlotProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction getUtilityClasses(ownerState) {\n  const {\n    disabled,\n    focusVisible\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', focusVisible && 'focusVisible']\n  };\n  return composeClasses(slots, getMenuItemUnstyledUtilityClass, {});\n}\n/**\n *\n * Demos:\n *\n * - [Unstyled menu](https://mui.com/base/react-menu/)\n *\n * API:\n *\n * - [MenuItemUnstyled API](https://mui.com/base/api/menu-item-unstyled/)\n */\n\nconst MenuItemUnstyled = /*#__PURE__*/React.forwardRef(function MenuItemUnstyled(props, ref) {\n  var _ref;\n  const {\n      children,\n      disabled: disabledProp = false,\n      component,\n      components = {},\n      componentsProps = {},\n      label\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    getRootProps,\n    disabled,\n    focusVisible\n  } = useMenuItem({\n    disabled: disabledProp,\n    ref,\n    label\n  });\n  const ownerState = _extends({}, props, {\n    disabled,\n    focusVisible\n  });\n  const classes = getUtilityClasses(ownerState);\n  const Root = (_ref = component != null ? component : components.Root) != null ? _ref : 'li';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    getSlotProps: getRootProps,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    className: classes.root,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? MenuItemUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the MenuItem.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The props used for each slot inside the MenuItem.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * If `true`, the menu item will be disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * A text representation of the menu item's content.\n   * Used for keyboard text navigation matching.\n   */\n  label: PropTypes.string\n} : void 0;\nexport default MenuItemUnstyled;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}