{"ast": null, "code": "import { getPickersLocalization } from './utils/getPickersLocalization';\nconst plPLPickers = {\n  // Calendar navigation\n  previousMonth: 'Poprzedni miesiąc',\n  nextMonth: 'Następny miesiąc',\n  // View navigation\n  openPreviousView: 'otwórz poprzedni widok',\n  openNextView: 'otwórz następny widok',\n  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'otwarty jest widok roku, przełącz na widok kalendarza' : 'otwarty jest widok kalendarza, przełącz na widok roku',\n  // inputModeToggleButtonAriaLabel: (isKeyboardInputOpen, viewType) => isKeyboardInputOpen ? `text input view is open, go to ${viewType} view` : `${viewType} view is open, go to text input view`,\n  // DateRange placeholders\n  start: '<PERSON>czątek',\n  end: '<PERSON>nie<PERSON>',\n  // Action bar\n  cancelButtonLabel: 'Anuluj',\n  clearButtonLabel: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n  okButtonLabel: 'Zatwi<PERSON><PERSON>',\n  todayButtonLabel: 'Dzisiaj',\n  // Toolbar titles\n  // datePickerDefaultToolbarTitle: 'Select date',\n  // dateTimePickerDefaultToolbarTitle: 'Select date & time',\n  // timePickerDefaultToolbarTitle: 'Select time',\n  // dateRangePickerDefaultToolbarTitle: 'Select date range',\n  // Clock labels\n  clockLabelText: (view, time, adapter) => \"Select \".concat(view, \". \").concat(time === null ? 'Nie wybrano czasu' : \"Wybrany czas to \".concat(adapter.format(time, 'fullTime'))),\n  hoursClockNumberText: hours => \"\".concat(hours, \" godzin\"),\n  minutesClockNumberText: minutes => \"\".concat(minutes, \" minut\"),\n  secondsClockNumberText: seconds => \"\".concat(seconds, \" sekund\"),\n  // Open picker labels\n  openDatePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? \"Wybierz dat\\u0119, obecnie wybrana data to \".concat(utils.format(utils.date(rawValue), 'fullDate')) : 'Wybierz datę',\n  openTimePickerDialogue: (rawValue, utils) => rawValue && utils.isValid(utils.date(rawValue)) ? \"Wybierz czas, obecnie wybrany czas to \".concat(utils.format(utils.date(rawValue), 'fullTime')) : 'Wybierz czas',\n  // Table labels\n  timeTableLabel: 'wybierz czas',\n  dateTableLabel: 'wybierz datę'\n};\nexport const plPL = getPickersLocalization(plPLPickers);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}