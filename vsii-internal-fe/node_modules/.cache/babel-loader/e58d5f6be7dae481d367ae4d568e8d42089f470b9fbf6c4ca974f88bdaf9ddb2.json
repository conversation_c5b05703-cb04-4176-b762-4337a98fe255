{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport function getClockUtilityClass(slot) {\n  return generateUtilityClass('MuiClock', slot);\n}\nexport const clockClasses = generateUtilityClasses('MuiClock', ['root', 'clock', 'wrapper', 'squareMask', 'pin', 'amButton', 'pmButton']);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}