{"ast": null, "code": "import generateUtilityClasses from '../generateUtilityClasses';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getBadgeUnstyledUtilityClass(slot) {\n  return generateUtilityClass('BaseBadge', slot);\n}\nconst badgeUnstyledClasses = generateUtilityClasses('BaseBadge', ['root', 'badge', 'invisible']);\nexport default badgeUnstyledClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}