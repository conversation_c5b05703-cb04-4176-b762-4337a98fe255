{"ast": null, "code": "import { useEffect, useMemo } from 'react';\nimport { DragSourceImpl } from './DragSourceImpl.js';\nexport function useDragSource(spec, monitor, connector) {\n  const handler = useMemo(() => new DragSourceImpl(spec, monitor, connector), [monitor, connector]);\n  useEffect(() => {\n    handler.spec = spec;\n  }, [spec]);\n  return handler;\n}\n\n//# sourceMappingURL=useDragSource.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}