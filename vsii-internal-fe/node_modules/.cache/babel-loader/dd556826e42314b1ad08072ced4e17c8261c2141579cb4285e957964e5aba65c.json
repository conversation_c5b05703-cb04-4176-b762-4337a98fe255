{"ast": null, "code": "// material-ui\nimport{Grid}from'@mui/material';// project imports\nimport{SearchForm,Skill,TitleCode,LevelSkill,Degree,Member}from'../search';import{Label}from'components/extended/Form';import{skillsReportSearchDefaultValue,skillsReportSearchDefaultValueSchema}from'pages/skills-manage/Config';import{Button}from'components';//third-party\nimport{FormattedMessage}from'react-intl';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SkillsReportSearch=props=>{const{formReset,handleSearch,handleChangeTitleCode,handleChangeMember,handleChangeSkill,handleChangeLevel,handleChangeDegree}=props;const{salesReport}=TEXT_CONFIG_SCREEN;return/*#__PURE__*/_jsx(SearchForm,{defaultValues:skillsReportSearchDefaultValue,formSchema:skillsReportSearchDefaultValueSchema,handleSubmit:handleSearch,formReset:formReset,children:/*#__PURE__*/_jsxs(Grid,{container:true,alignItems:\"center\",spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:2,children:/*#__PURE__*/_jsx(TitleCode,{handleChange:handleChangeTitleCode,label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.skillsReport+'title'})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:2,children:/*#__PURE__*/_jsx(Skill,{handleChange:handleChangeSkill,label:salesReport.skillsReport+'skill-name'})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:2,children:/*#__PURE__*/_jsx(LevelSkill,{handleChange:handleChangeLevel,label:salesReport.skillsReport+'skill-level'})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:2,children:/*#__PURE__*/_jsx(Degree,{handleChange:handleChangeDegree,label:salesReport.skillsReport+'degree'})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:2,children:/*#__PURE__*/_jsx(Member,{isUserName:true,isFindSkill:true,autoFilter:formReset,handleChange:handleChangeMember,name:\"userName\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.skillsReport+'members'})})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,lg:2,children:[/*#__PURE__*/_jsx(Label,{label:\"\\xA0\"}),/*#__PURE__*/_jsx(Button,{type:\"submit\",size:\"medium\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:salesReport.skillsReport+'search'}),variant:\"contained\"})]})]})});};export default SkillsReportSearch;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}