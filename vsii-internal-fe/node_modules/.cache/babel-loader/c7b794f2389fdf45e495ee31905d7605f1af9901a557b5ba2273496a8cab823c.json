{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/AddOrEditBiddingProjectInfo.tsx\",\n  _s = $RefreshSig$();\nimport { FormattedMessage } from 'react-intl';\n\n// react-hook-form\nimport { useFormContext } from 'react-hook-form';\n\n// material-ui\nimport { Grid } from '@mui/material';\n\n// project imports\nimport { DatePicker, Input, PercentageFormat } from 'components/extended/Form';\nimport Api from 'constants/Api';\nimport { CONTRACT_TYPE_SALE_REPORT, E_BIDDING_STATUS, PERCENT_PLACEHOLDER, SERVICE_TYPE_STATUS } from 'constants/Common';\nimport { BiddingStatus, ContractType, DepartmentBidding, Project, SalePipelineType, ServiceType } from 'containers/search';\nimport sendRequest from 'services/ApiService';\nimport { dateFormat } from 'utils/date';\nimport { isEmpty } from 'utils/common';\n\n// third party\nimport { useEffect } from 'react';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AddOrEditBiddingProjectInfo = props => {\n  _s();\n  // ================= Hooks, State, Variable, Props =================\n  const {\n    isEdit,\n    status,\n    handleSetHCInfo\n  } = props;\n  const {\n    watch,\n    setValue\n  } = useFormContext();\n  const statusBidding = watch('project.status');\n  const contractType = watch('project.contractType');\n  const serviceType = watch('project.serviceType');\n  const probability = watch('project.probability');\n  const {\n    salesReport\n  } = TEXT_CONFIG_SCREEN;\n\n  // ================= Function =================\n  const estimateHcInfoByFromToDate = async (fromDate, toDate) => {\n    const payload = {\n      contractType,\n      from: dateFormat(fromDate),\n      to: dateFormat(toDate)\n    };\n    const response = await sendRequest(Api.sale_pipe_line_bidding.estimateHCInfo, payload);\n    if (response !== null && response !== void 0 && response.status) {\n      const {\n        result\n      } = response;\n      handleSetHCInfo(result.content);\n    }\n  };\n\n  // ================= Effect =================\n  useEffect(() => {\n    const {\n      unsubscribe\n    } = watch((value, info) => {\n      var _info$name;\n      if ((_info$name = info.name) !== null && _info$name !== void 0 && _info$name.startsWith('project')) {\n        var _info$name2, _info$name3, _info$name4, _info$name5;\n        if ((_info$name2 = info.name) !== null && _info$name2 !== void 0 && _info$name2.endsWith('contractDurationFrom') || (_info$name3 = info.name) !== null && _info$name3 !== void 0 && _info$name3.endsWith('contractDurationTo')) {\n          const contractDurationFrom = value.project.contractDurationFrom;\n          const contractDurationTo = value.project.contractDurationTo;\n          if (!isEmpty(contractDurationFrom) && !isEmpty(contractDurationTo)) {\n            estimateHcInfoByFromToDate(contractDurationFrom, contractDurationTo);\n          }\n        }\n        if ((_info$name4 = info.name) !== null && _info$name4 !== void 0 && _info$name4.endsWith('serviceType')) {\n          value.project.serviceType === SERVICE_TYPE_STATUS.PRODUCT && setValue('project.contractType', CONTRACT_TYPE_SALE_REPORT.TM);\n        }\n        if ((_info$name5 = info.name) !== null && _info$name5 !== void 0 && _info$name5.endsWith('probability')) {\n          const probability = value.project.probability;\n          if (probability < 80) {\n            setValue('project.revenuePercent', '');\n          }\n        }\n      }\n    });\n    return () => unsubscribe();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [watch]);\n  return /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 2,\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        name: \"project.customer\",\n        label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.allSalesPineline + '-customer'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 28\n        }, this),\n        disabled: status === E_BIDDING_STATUS.CONTRACT\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(DepartmentBidding, {\n        name: \"project.department\",\n        required: true,\n        disabled: status === E_BIDDING_STATUS.CONTRACT,\n        label: salesReport.allSalesPineline + '-department'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        name: \"project.projectName\",\n        required: true,\n        label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.allSalesPineline + '-project-name'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 28\n        }, this),\n        disabled: status === E_BIDDING_STATUS.CONTRACT\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        name: \"project.contractNo\",\n        label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.allSalesPineline + '-contract-no'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 28\n        }, this),\n        disabled: status === E_BIDDING_STATUS.CONTRACT\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(Project, {\n        name: \"project.projectRedmineId\",\n        required: statusBidding === E_BIDDING_STATUS.CONTRACT,\n        label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.allSalesPineline + '-project-redmine-name'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 28\n        }, this),\n        isDefaultAll: true,\n        projectAuthorization: \"false\",\n        disabled: isEdit && status === E_BIDDING_STATUS.CONTRACT\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        name: \"project.probability\",\n        label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.allSalesPineline + '-probability'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 28\n        }, this),\n        disabled: status === E_BIDDING_STATUS.CONTRACT,\n        textFieldProps: {\n          placeholder: PERCENT_PLACEHOLDER,\n          InputProps: {\n            inputComponent: PercentageFormat\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(SalePipelineType, {\n        name: \"project.type\",\n        required: true,\n        isShowAll: false,\n        disabled: status === E_BIDDING_STATUS.CONTRACT,\n        label: salesReport.allSalesPineline + '-type'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        name: \"project.revenuePercent\",\n        label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: \"revenue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 28\n        }, this),\n        disabled: status === E_BIDDING_STATUS.CONTRACT || probability < 80,\n        textFieldProps: {\n          placeholder: PERCENT_PLACEHOLDER,\n          InputProps: {\n            inputComponent: PercentageFormat\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(BiddingStatus, {\n        name: \"project.status\",\n        required: true,\n        disabled: !isEdit || status === E_BIDDING_STATUS.CONTRACT || status === E_BIDDING_STATUS.FAILED || status === E_BIDDING_STATUS.LOSS,\n        isShowAll: false,\n        label: salesReport.allSalesPineline + '-status'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(ServiceType, {\n        required: true,\n        name: \"project.serviceType\",\n        disabled: status === E_BIDDING_STATUS.CONTRACT,\n        label: salesReport.allSalesPineline + '-service-type'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(DatePicker, {\n        name: \"project.contractDueDate\",\n        label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.allSalesPineline + '-contract-date'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 28\n        }, this) // thieu\n        ,\n        disabled: status === E_BIDDING_STATUS.CONTRACT\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(ContractType, {\n        name: \"project.contractType\",\n        required: true,\n        disabled: isEdit || status === E_BIDDING_STATUS.CONTRACT || serviceType === SERVICE_TYPE_STATUS.PRODUCT,\n        label: salesReport.allSalesPineline + '-contract-type'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(DatePicker, {\n        required: true,\n        name: \"project.contractDurationFrom\",\n        label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.allSalesPineline + '-contract-duration-from'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 28\n        }, this),\n        disabled: status === E_BIDDING_STATUS.CONTRACT\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(DatePicker, {\n        required: true,\n        name: \"project.contractDurationTo\",\n        label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: \"contract-duration-to\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 28\n        }, this),\n        disabled: status === E_BIDDING_STATUS.CONTRACT\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        name: \"project.note\",\n        label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: salesReport.allSalesPineline + '-note'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 28\n        }, this),\n        textFieldProps: {\n          multiline: true,\n          rows: 5\n        },\n        disabled: status === E_BIDDING_STATUS.CONTRACT\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      lg: 6,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          name: \"project.warrantyTime\",\n          label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: salesReport.allSalesPineline + '-warranty-time'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 32\n          }, this),\n          disabled: status === E_BIDDING_STATUS.CONTRACT\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 9\n  }, this);\n};\n_s(AddOrEditBiddingProjectInfo, \"1FHiEzWHzCDs1qoxuY2OXf1mIUA=\", false, function () {\n  return [useFormContext];\n});\n_c = AddOrEditBiddingProjectInfo;\nexport default AddOrEditBiddingProjectInfo;\nvar _c;\n$RefreshReg$(_c, \"AddOrEditBiddingProjectInfo\");", "map": {"version": 3, "names": ["FormattedMessage", "useFormContext", "Grid", "DatePicker", "Input", "PercentageFormat", "Api", "CONTRACT_TYPE_SALE_REPORT", "E_BIDDING_STATUS", "PERCENT_PLACEHOLDER", "SERVICE_TYPE_STATUS", "BiddingStatus", "ContractType", "DepartmentBidding", "Project", "SalePipelineType", "ServiceType", "sendRequest", "dateFormat", "isEmpty", "useEffect", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "AddOrEditBiddingProjectInfo", "props", "_s", "isEdit", "status", "handleSetHCInfo", "watch", "setValue", "statusBidding", "contractType", "serviceType", "probability", "salesReport", "estimateHcInfoByFromToDate", "fromDate", "toDate", "payload", "from", "to", "response", "sale_pipe_line_bidding", "estimateHCInfo", "result", "content", "unsubscribe", "value", "info", "_info$name", "name", "startsWith", "_info$name2", "_info$name3", "_info$name4", "_info$name5", "endsWith", "contractDurationFrom", "project", "contractDurationTo", "PRODUCT", "TM", "container", "spacing", "children", "item", "xs", "lg", "label", "id", "allSalesPineline", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "CONTRACT", "required", "isDefaultAll", "projectAuthorization", "textFieldProps", "placeholder", "InputProps", "inputComponent", "isShowAll", "FAILED", "LOSS", "multiline", "rows", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/sales/AddOrEditBiddingProjectInfo.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\n\n// react-hook-form\nimport { useFormContext } from 'react-hook-form';\n\n// material-ui\nimport { Grid } from '@mui/material';\n\n// project imports\nimport { DatePicker, Input, PercentageFormat } from 'components/extended/Form';\nimport Api from 'constants/Api';\nimport { CONTRACT_TYPE_SALE_REPORT, E_BIDDING_STATUS, PERCENT_PLACEHOLDER, SERVICE_TYPE_STATUS } from 'constants/Common';\nimport { BiddingStatus, ContractType, DepartmentBidding, Project, SalePipelineType, ServiceType } from 'containers/search';\nimport sendRequest from 'services/ApiService';\nimport { dateFormat } from 'utils/date';\nimport { isEmpty } from 'utils/common';\n\n// third party\nimport { useEffect } from 'react';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\n\ninterface IAddOrEditBiddingProjectInfoProps {\n    isEdit?: boolean;\n    status?: string;\n    handleSetHCInfo: (data: any) => void;\n}\n\nconst AddOrEditBiddingProjectInfo = (props: IAddOrEditBiddingProjectInfoProps) => {\n    // ================= Hooks, State, Variable, Props =================\n    const { isEdit, status, handleSetHCInfo } = props;\n    const { watch, setValue } = useFormContext();\n    const statusBidding = watch('project.status');\n    const contractType = watch('project.contractType');\n    const serviceType = watch('project.serviceType');\n    const probability = watch('project.probability');\n\n    const { salesReport } = TEXT_CONFIG_SCREEN;\n\n    // ================= Function =================\n    const estimateHcInfoByFromToDate = async (fromDate: string, toDate: string) => {\n        const payload = { contractType, from: dateFormat(fromDate), to: dateFormat(toDate) };\n        const response = await sendRequest(Api.sale_pipe_line_bidding.estimateHCInfo, payload);\n        if (response?.status) {\n            const { result } = response;\n            handleSetHCInfo(result.content);\n        }\n    };\n\n    // ================= Effect =================\n    useEffect(() => {\n        const { unsubscribe } = watch((value, info) => {\n            if (info.name?.startsWith('project')) {\n                if (info.name?.endsWith('contractDurationFrom') || info.name?.endsWith('contractDurationTo')) {\n                    const contractDurationFrom = value.project.contractDurationFrom;\n                    const contractDurationTo = value.project.contractDurationTo;\n                    if (!isEmpty(contractDurationFrom) && !isEmpty(contractDurationTo)) {\n                        estimateHcInfoByFromToDate(contractDurationFrom, contractDurationTo);\n                    }\n                }\n                if (info.name?.endsWith('serviceType')) {\n                    value.project.serviceType === SERVICE_TYPE_STATUS.PRODUCT &&\n                        setValue('project.contractType', CONTRACT_TYPE_SALE_REPORT.TM);\n                }\n                if (info.name?.endsWith('probability')) {\n                    const probability = value.project.probability;\n                    if (probability < 80) {\n                        setValue('project.revenuePercent', '');\n                    }\n                }\n            }\n        });\n        return () => unsubscribe();\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [watch]);\n\n    return (\n        <Grid container spacing={2}>\n            <Grid item xs={12} lg={6}>\n                <Input\n                    name=\"project.customer\"\n                    label={<FormattedMessage id={salesReport.allSalesPineline + '-customer'} />}\n                    disabled={status === E_BIDDING_STATUS.CONTRACT}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <DepartmentBidding\n                    name=\"project.department\"\n                    required\n                    disabled={status === E_BIDDING_STATUS.CONTRACT}\n                    label={salesReport.allSalesPineline + '-department'}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <Input\n                    name=\"project.projectName\"\n                    required\n                    label={<FormattedMessage id={salesReport.allSalesPineline + '-project-name'} />}\n                    disabled={status === E_BIDDING_STATUS.CONTRACT}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <Input\n                    name=\"project.contractNo\"\n                    label={<FormattedMessage id={salesReport.allSalesPineline + '-contract-no'} />}\n                    disabled={status === E_BIDDING_STATUS.CONTRACT}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <Project\n                    name=\"project.projectRedmineId\"\n                    required={statusBidding === E_BIDDING_STATUS.CONTRACT}\n                    label={<FormattedMessage id={salesReport.allSalesPineline + '-project-redmine-name'} />}\n                    isDefaultAll\n                    projectAuthorization=\"false\"\n                    disabled={isEdit && status === E_BIDDING_STATUS.CONTRACT}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <Input\n                    name=\"project.probability\"\n                    label={<FormattedMessage id={salesReport.allSalesPineline + '-probability'} />}\n                    disabled={status === E_BIDDING_STATUS.CONTRACT}\n                    textFieldProps={{\n                        placeholder: PERCENT_PLACEHOLDER,\n                        InputProps: {\n                            inputComponent: PercentageFormat as any\n                        }\n                    }}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <SalePipelineType\n                    name=\"project.type\"\n                    required\n                    isShowAll={false}\n                    disabled={status === E_BIDDING_STATUS.CONTRACT}\n                    label={salesReport.allSalesPineline + '-type'}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <Input\n                    name=\"project.revenuePercent\"\n                    label={<FormattedMessage id=\"revenue\" />}\n                    disabled={status === E_BIDDING_STATUS.CONTRACT || probability < 80}\n                    textFieldProps={{\n                        placeholder: PERCENT_PLACEHOLDER,\n                        InputProps: {\n                            inputComponent: PercentageFormat as any\n                        }\n                    }}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <BiddingStatus\n                    name=\"project.status\"\n                    required\n                    disabled={\n                        !isEdit ||\n                        status === E_BIDDING_STATUS.CONTRACT ||\n                        status === E_BIDDING_STATUS.FAILED ||\n                        status === E_BIDDING_STATUS.LOSS\n                    }\n                    isShowAll={false}\n                    label={salesReport.allSalesPineline + '-status'}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <ServiceType\n                    required\n                    name=\"project.serviceType\"\n                    disabled={status === E_BIDDING_STATUS.CONTRACT}\n                    label={salesReport.allSalesPineline + '-service-type'}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <DatePicker\n                    name=\"project.contractDueDate\"\n                    label={<FormattedMessage id={salesReport.allSalesPineline + '-contract-date'} />} // thieu\n                    disabled={status === E_BIDDING_STATUS.CONTRACT}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <ContractType\n                    name=\"project.contractType\"\n                    required\n                    disabled={isEdit || status === E_BIDDING_STATUS.CONTRACT || serviceType === SERVICE_TYPE_STATUS.PRODUCT}\n                    label={salesReport.allSalesPineline + '-contract-type'}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <DatePicker\n                    required\n                    name=\"project.contractDurationFrom\"\n                    label={<FormattedMessage id={salesReport.allSalesPineline + '-contract-duration-from'} />}\n                    disabled={status === E_BIDDING_STATUS.CONTRACT}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <DatePicker\n                    required\n                    name=\"project.contractDurationTo\"\n                    label={<FormattedMessage id=\"contract-duration-to\" />}\n                    disabled={status === E_BIDDING_STATUS.CONTRACT}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <Input\n                    name=\"project.note\"\n                    label={<FormattedMessage id={salesReport.allSalesPineline + '-note'} />}\n                    textFieldProps={{ multiline: true, rows: 5 }}\n                    disabled={status === E_BIDDING_STATUS.CONTRACT}\n                />\n            </Grid>\n            <Grid item xs={12} lg={6}>\n                <Grid item xs={12} lg={12}>\n                    <Input\n                        name=\"project.warrantyTime\"\n                        label={<FormattedMessage id={salesReport.allSalesPineline + '-warranty-time'} />}\n                        disabled={status === E_BIDDING_STATUS.CONTRACT}\n                    />\n                </Grid>\n            </Grid>\n        </Grid>\n    );\n};\n\nexport default AddOrEditBiddingProjectInfo;\n"], "mappings": ";;AAAA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,cAAc,QAAQ,iBAAiB;;AAEhD;AACA,SAASC,IAAI,QAAQ,eAAe;;AAEpC;AACA,SAASC,UAAU,EAAEC,KAAK,EAAEC,gBAAgB,QAAQ,0BAA0B;AAC9E,OAAOC,GAAG,MAAM,eAAe;AAC/B,SAASC,yBAAyB,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,mBAAmB,QAAQ,kBAAkB;AACxH,SAASC,aAAa,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,WAAW,QAAQ,mBAAmB;AAC1H,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,UAAU,QAAQ,YAAY;AACvC,SAASC,OAAO,QAAQ,cAAc;;AAEtC;AACA,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQtD,MAAMC,2BAA2B,GAAIC,KAAwC,IAAK;EAAAC,EAAA;EAC9E;EACA,MAAM;IAAEC,MAAM;IAAEC,MAAM;IAAEC;EAAgB,CAAC,GAAGJ,KAAK;EACjD,MAAM;IAAEK,KAAK;IAAEC;EAAS,CAAC,GAAG9B,cAAc,CAAC,CAAC;EAC5C,MAAM+B,aAAa,GAAGF,KAAK,CAAC,gBAAgB,CAAC;EAC7C,MAAMG,YAAY,GAAGH,KAAK,CAAC,sBAAsB,CAAC;EAClD,MAAMI,WAAW,GAAGJ,KAAK,CAAC,qBAAqB,CAAC;EAChD,MAAMK,WAAW,GAAGL,KAAK,CAAC,qBAAqB,CAAC;EAEhD,MAAM;IAAEM;EAAY,CAAC,GAAGf,kBAAkB;;EAE1C;EACA,MAAMgB,0BAA0B,GAAG,MAAAA,CAAOC,QAAgB,EAAEC,MAAc,KAAK;IAC3E,MAAMC,OAAO,GAAG;MAAEP,YAAY;MAAEQ,IAAI,EAAEvB,UAAU,CAACoB,QAAQ,CAAC;MAAEI,EAAE,EAAExB,UAAU,CAACqB,MAAM;IAAE,CAAC;IACpF,MAAMI,QAAQ,GAAG,MAAM1B,WAAW,CAACX,GAAG,CAACsC,sBAAsB,CAACC,cAAc,EAAEL,OAAO,CAAC;IACtF,IAAIG,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEf,MAAM,EAAE;MAClB,MAAM;QAAEkB;MAAO,CAAC,GAAGH,QAAQ;MAC3Bd,eAAe,CAACiB,MAAM,CAACC,OAAO,CAAC;IACnC;EACJ,CAAC;;EAED;EACA3B,SAAS,CAAC,MAAM;IACZ,MAAM;MAAE4B;IAAY,CAAC,GAAGlB,KAAK,CAAC,CAACmB,KAAK,EAAEC,IAAI,KAAK;MAAA,IAAAC,UAAA;MAC3C,KAAAA,UAAA,GAAID,IAAI,CAACE,IAAI,cAAAD,UAAA,eAATA,UAAA,CAAWE,UAAU,CAAC,SAAS,CAAC,EAAE;QAAA,IAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA;QAClC,IAAI,CAAAH,WAAA,GAAAJ,IAAI,CAACE,IAAI,cAAAE,WAAA,eAATA,WAAA,CAAWI,QAAQ,CAAC,sBAAsB,CAAC,KAAAH,WAAA,GAAIL,IAAI,CAACE,IAAI,cAAAG,WAAA,eAATA,WAAA,CAAWG,QAAQ,CAAC,oBAAoB,CAAC,EAAE;UAC1F,MAAMC,oBAAoB,GAAGV,KAAK,CAACW,OAAO,CAACD,oBAAoB;UAC/D,MAAME,kBAAkB,GAAGZ,KAAK,CAACW,OAAO,CAACC,kBAAkB;UAC3D,IAAI,CAAC1C,OAAO,CAACwC,oBAAoB,CAAC,IAAI,CAACxC,OAAO,CAAC0C,kBAAkB,CAAC,EAAE;YAChExB,0BAA0B,CAACsB,oBAAoB,EAAEE,kBAAkB,CAAC;UACxE;QACJ;QACA,KAAAL,WAAA,GAAIN,IAAI,CAACE,IAAI,cAAAI,WAAA,eAATA,WAAA,CAAWE,QAAQ,CAAC,aAAa,CAAC,EAAE;UACpCT,KAAK,CAACW,OAAO,CAAC1B,WAAW,KAAKxB,mBAAmB,CAACoD,OAAO,IACrD/B,QAAQ,CAAC,sBAAsB,EAAExB,yBAAyB,CAACwD,EAAE,CAAC;QACtE;QACA,KAAAN,WAAA,GAAIP,IAAI,CAACE,IAAI,cAAAK,WAAA,eAATA,WAAA,CAAWC,QAAQ,CAAC,aAAa,CAAC,EAAE;UACpC,MAAMvB,WAAW,GAAGc,KAAK,CAACW,OAAO,CAACzB,WAAW;UAC7C,IAAIA,WAAW,GAAG,EAAE,EAAE;YAClBJ,QAAQ,CAAC,wBAAwB,EAAE,EAAE,CAAC;UAC1C;QACJ;MACJ;IACJ,CAAC,CAAC;IACF,OAAO,MAAMiB,WAAW,CAAC,CAAC;IAC1B;EACJ,CAAC,EAAE,CAAClB,KAAK,CAAC,CAAC;EAEX,oBACIP,OAAA,CAACrB,IAAI;IAAC8D,SAAS;IAACC,OAAO,EAAE,CAAE;IAAAC,QAAA,gBACvB3C,OAAA,CAACrB,IAAI;MAACiE,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrB3C,OAAA,CAACnB,KAAK;QACFgD,IAAI,EAAC,kBAAkB;QACvBkB,KAAK,eAAE/C,OAAA,CAACvB,gBAAgB;UAACuE,EAAE,EAAEnC,WAAW,CAACoC,gBAAgB,GAAG;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC5EC,QAAQ,EAAEjD,MAAM,KAAKpB,gBAAgB,CAACsE;MAAS;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPrD,OAAA,CAACrB,IAAI;MAACiE,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrB3C,OAAA,CAACV,iBAAiB;QACduC,IAAI,EAAC,oBAAoB;QACzB2B,QAAQ;QACRF,QAAQ,EAAEjD,MAAM,KAAKpB,gBAAgB,CAACsE,QAAS;QAC/CR,KAAK,EAAElC,WAAW,CAACoC,gBAAgB,GAAG;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPrD,OAAA,CAACrB,IAAI;MAACiE,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrB3C,OAAA,CAACnB,KAAK;QACFgD,IAAI,EAAC,qBAAqB;QAC1B2B,QAAQ;QACRT,KAAK,eAAE/C,OAAA,CAACvB,gBAAgB;UAACuE,EAAE,EAAEnC,WAAW,CAACoC,gBAAgB,GAAG;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAChFC,QAAQ,EAAEjD,MAAM,KAAKpB,gBAAgB,CAACsE;MAAS;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPrD,OAAA,CAACrB,IAAI;MAACiE,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrB3C,OAAA,CAACnB,KAAK;QACFgD,IAAI,EAAC,oBAAoB;QACzBkB,KAAK,eAAE/C,OAAA,CAACvB,gBAAgB;UAACuE,EAAE,EAAEnC,WAAW,CAACoC,gBAAgB,GAAG;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC/EC,QAAQ,EAAEjD,MAAM,KAAKpB,gBAAgB,CAACsE;MAAS;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPrD,OAAA,CAACrB,IAAI;MAACiE,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrB3C,OAAA,CAACT,OAAO;QACJsC,IAAI,EAAC,0BAA0B;QAC/B2B,QAAQ,EAAE/C,aAAa,KAAKxB,gBAAgB,CAACsE,QAAS;QACtDR,KAAK,eAAE/C,OAAA,CAACvB,gBAAgB;UAACuE,EAAE,EAAEnC,WAAW,CAACoC,gBAAgB,GAAG;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxFI,YAAY;QACZC,oBAAoB,EAAC,OAAO;QAC5BJ,QAAQ,EAAElD,MAAM,IAAIC,MAAM,KAAKpB,gBAAgB,CAACsE;MAAS;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPrD,OAAA,CAACrB,IAAI;MAACiE,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrB3C,OAAA,CAACnB,KAAK;QACFgD,IAAI,EAAC,qBAAqB;QAC1BkB,KAAK,eAAE/C,OAAA,CAACvB,gBAAgB;UAACuE,EAAE,EAAEnC,WAAW,CAACoC,gBAAgB,GAAG;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC/EC,QAAQ,EAAEjD,MAAM,KAAKpB,gBAAgB,CAACsE,QAAS;QAC/CI,cAAc,EAAE;UACZC,WAAW,EAAE1E,mBAAmB;UAChC2E,UAAU,EAAE;YACRC,cAAc,EAAEhF;UACpB;QACJ;MAAE;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPrD,OAAA,CAACrB,IAAI;MAACiE,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrB3C,OAAA,CAACR,gBAAgB;QACbqC,IAAI,EAAC,cAAc;QACnB2B,QAAQ;QACRO,SAAS,EAAE,KAAM;QACjBT,QAAQ,EAAEjD,MAAM,KAAKpB,gBAAgB,CAACsE,QAAS;QAC/CR,KAAK,EAAElC,WAAW,CAACoC,gBAAgB,GAAG;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPrD,OAAA,CAACrB,IAAI;MAACiE,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrB3C,OAAA,CAACnB,KAAK;QACFgD,IAAI,EAAC,wBAAwB;QAC7BkB,KAAK,eAAE/C,OAAA,CAACvB,gBAAgB;UAACuE,EAAE,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzCC,QAAQ,EAAEjD,MAAM,KAAKpB,gBAAgB,CAACsE,QAAQ,IAAI3C,WAAW,GAAG,EAAG;QACnE+C,cAAc,EAAE;UACZC,WAAW,EAAE1E,mBAAmB;UAChC2E,UAAU,EAAE;YACRC,cAAc,EAAEhF;UACpB;QACJ;MAAE;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPrD,OAAA,CAACrB,IAAI;MAACiE,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrB3C,OAAA,CAACZ,aAAa;QACVyC,IAAI,EAAC,gBAAgB;QACrB2B,QAAQ;QACRF,QAAQ,EACJ,CAAClD,MAAM,IACPC,MAAM,KAAKpB,gBAAgB,CAACsE,QAAQ,IACpClD,MAAM,KAAKpB,gBAAgB,CAAC+E,MAAM,IAClC3D,MAAM,KAAKpB,gBAAgB,CAACgF,IAC/B;QACDF,SAAS,EAAE,KAAM;QACjBhB,KAAK,EAAElC,WAAW,CAACoC,gBAAgB,GAAG;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPrD,OAAA,CAACrB,IAAI;MAACiE,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrB3C,OAAA,CAACP,WAAW;QACR+D,QAAQ;QACR3B,IAAI,EAAC,qBAAqB;QAC1ByB,QAAQ,EAAEjD,MAAM,KAAKpB,gBAAgB,CAACsE,QAAS;QAC/CR,KAAK,EAAElC,WAAW,CAACoC,gBAAgB,GAAG;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPrD,OAAA,CAACrB,IAAI;MAACiE,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrB3C,OAAA,CAACpB,UAAU;QACPiD,IAAI,EAAC,yBAAyB;QAC9BkB,KAAK,eAAE/C,OAAA,CAACvB,gBAAgB;UAACuE,EAAE,EAAEnC,WAAW,CAACoC,gBAAgB,GAAG;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE,CAAC;QAAA;QAClFC,QAAQ,EAAEjD,MAAM,KAAKpB,gBAAgB,CAACsE;MAAS;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPrD,OAAA,CAACrB,IAAI;MAACiE,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrB3C,OAAA,CAACX,YAAY;QACTwC,IAAI,EAAC,sBAAsB;QAC3B2B,QAAQ;QACRF,QAAQ,EAAElD,MAAM,IAAIC,MAAM,KAAKpB,gBAAgB,CAACsE,QAAQ,IAAI5C,WAAW,KAAKxB,mBAAmB,CAACoD,OAAQ;QACxGQ,KAAK,EAAElC,WAAW,CAACoC,gBAAgB,GAAG;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPrD,OAAA,CAACrB,IAAI;MAACiE,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrB3C,OAAA,CAACpB,UAAU;QACP4E,QAAQ;QACR3B,IAAI,EAAC,8BAA8B;QACnCkB,KAAK,eAAE/C,OAAA,CAACvB,gBAAgB;UAACuE,EAAE,EAAEnC,WAAW,CAACoC,gBAAgB,GAAG;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC1FC,QAAQ,EAAEjD,MAAM,KAAKpB,gBAAgB,CAACsE;MAAS;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPrD,OAAA,CAACrB,IAAI;MAACiE,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrB3C,OAAA,CAACpB,UAAU;QACP4E,QAAQ;QACR3B,IAAI,EAAC,4BAA4B;QACjCkB,KAAK,eAAE/C,OAAA,CAACvB,gBAAgB;UAACuE,EAAE,EAAC;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtDC,QAAQ,EAAEjD,MAAM,KAAKpB,gBAAgB,CAACsE;MAAS;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPrD,OAAA,CAACrB,IAAI;MAACiE,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrB3C,OAAA,CAACnB,KAAK;QACFgD,IAAI,EAAC,cAAc;QACnBkB,KAAK,eAAE/C,OAAA,CAACvB,gBAAgB;UAACuE,EAAE,EAAEnC,WAAW,CAACoC,gBAAgB,GAAG;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxEM,cAAc,EAAE;UAAEO,SAAS,EAAE,IAAI;UAAEC,IAAI,EAAE;QAAE,CAAE;QAC7Cb,QAAQ,EAAEjD,MAAM,KAAKpB,gBAAgB,CAACsE;MAAS;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACPrD,OAAA,CAACrB,IAAI;MAACiE,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACrB3C,OAAA,CAACrB,IAAI;QAACiE,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAH,QAAA,eACtB3C,OAAA,CAACnB,KAAK;UACFgD,IAAI,EAAC,sBAAsB;UAC3BkB,KAAK,eAAE/C,OAAA,CAACvB,gBAAgB;YAACuE,EAAE,EAAEnC,WAAW,CAACoC,gBAAgB,GAAG;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACjFC,QAAQ,EAAEjD,MAAM,KAAKpB,gBAAgB,CAACsE;QAAS;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEf,CAAC;AAAClD,EAAA,CArMIF,2BAA2B;EAAA,QAGDvB,cAAc;AAAA;AAAA0F,EAAA,GAHxCnE,2BAA2B;AAuMjC,eAAeA,2BAA2B;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}