{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/pages/sales/Bidding.tsx\",\n  _s = $RefreshSig$();\n/* eslint-disable react-hooks/exhaustive-deps */\nimport { useEffect, useState } from 'react';\n\n// project imports\nimport { useAppDispatch } from 'app/hooks';\nimport MainCard from 'components/cards/MainCard';\nimport { Table, TableFooter } from 'components/extended/Table';\nimport Api from 'constants/Api';\nimport { SEARCH_PARAM_KEY, paginationParamDefault, paginationResponseDefault } from 'constants/Common';\nimport { PERMISSIONS } from 'constants/Permission';\nimport { TableToolbar } from 'containers';\nimport { AddOrEditBidding, BiddingSearch, BiddingTBody, BiddingThead, BiddingTotal, CommentPopover } from 'containers/sales';\nimport { FilterCollapse } from 'containers/search';\nimport sendRequest from 'services/ApiService';\nimport { closeConfirm, openConfirm } from 'store/slice/confirmSlice';\nimport { openSnackbar } from 'store/slice/snackbarSlice';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { getSearchParam, isEmpty, transformObject } from 'utils/common';\nimport { biddingFilterConfig } from './Config';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\nimport { useSearchParams } from 'react-router-dom';\n\n// material-ui\n\nimport useConfig from 'hooks/useConfig';\n\n// ==============================|| Bidding ||============================== //\n/**\n *  URL Params\n *  page\n *  size\n *  type\n *  year\n *  projectName\n *  status\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Bidding = () => {\n  _s();\n  // URL Params\n  const [searchParams, setSearchParams] = useSearchParams();\n  const keyParams = [SEARCH_PARAM_KEY.page, SEARCH_PARAM_KEY.size, SEARCH_PARAM_KEY.type, SEARCH_PARAM_KEY.year, SEARCH_PARAM_KEY.status, SEARCH_PARAM_KEY.searchMode, SEARCH_PARAM_KEY.projectName, SEARCH_PARAM_KEY.customer, SEARCH_PARAM_KEY.searchModeOfCustomer, SEARCH_PARAM_KEY.searchModeOfProject];\n  const params = getSearchParam(keyParams, searchParams);\n  transformObject(params);\n  const {\n    locale\n  } = useConfig();\n\n  // Hooks, State, Variable\n  const dispatch = useAppDispatch();\n  const defaultConditions = {\n    ...biddingFilterConfig,\n    ...params,\n    language: locale\n  };\n  const [loading, setLoading] = useState(false);\n  const [loadingPost, setLoadingPost] = useState(false);\n  const [paginationResponse, setPaginationResponse] = useState({\n    ...paginationResponseDefault,\n    pageNumber: params.page ? params.page : paginationResponseDefault.pageNumber,\n    pageSize: params.size ? params.size : paginationResponseDefault.pageSize\n  });\n  const [saleBiddings, setSaleBiddings] = useState([]);\n  const [detailBidding, setDetailBidding] = useState(null);\n  const [totalBidding, setTotalBidding] = useState([]);\n  const [conditions, setConditions] = useState(defaultConditions);\n  const [formReset] = useState(defaultConditions);\n  const [isEditBidding, setIsEditBidding] = useState(false);\n  const [openFormAddOrEditBidding, setOpenFormAddOrEditBidding] = useState(false);\n  const {\n    biddingPermission\n  } = PERMISSIONS.sale.salePipeline;\n  const [exchangeRateUSDpercentVND, setExchangeRateUSDpercentVND] = useState(0);\n  const [currencyAndExchangeRateDefault, setCurrencyAndExchangeRateDefault] = useState(null);\n  const [monthlyBillableDay, setMonthlyBillableDay] = useState();\n  const [commentItem, setCommentItem] = useState(null);\n  const [isEditComment, setIsEditComment] = useState(false);\n  const [anchorElComment, setAnchorElComment] = useState(null);\n  const [isRefresh, setIsRefresh] = useState(false);\n  const [isEditedTotalList, setIsEditedTotalList] = useState(false);\n\n  // ================= Functions =================\n  // Get Bidding\n  const getDataTable = async () => {\n    setLoading(true);\n    const response = await sendRequest(Api.sale_pipe_line_bidding.getBidding, {\n      ...conditions,\n      page: conditions.page + 1\n    });\n    if (response) {\n      const {\n        status,\n        result\n      } = response;\n      if (status) {\n        const {\n          content,\n          pagination\n        } = result;\n        if (!isEmpty(content)) {\n          setSaleBiddings(content.dataList);\n          setPaginationResponse({\n            ...paginationResponse,\n            totalElement: pagination === null || pagination === void 0 ? void 0 : pagination.totalElement\n          });\n          setLoading(false);\n        } else {\n          setDataEmpty();\n        }\n      }\n    } else {\n      setDataEmpty();\n    }\n  };\n  const getTotalBidding = async () => {\n    setLoading(true);\n    const response = await sendRequest(Api.sale_pipe_line_bidding.getTotal, {\n      ...conditions\n    });\n    if (response) {\n      const {\n        status,\n        result\n      } = response;\n      if (status) {\n        const {\n          content\n        } = result;\n        if (!isEmpty(content)) {\n          setTotalBidding(content.total.filter(item => item.show));\n          setLoading(false);\n        } else {\n          setTotalBidding([]);\n          setLoading(false);\n        }\n      }\n    } else {\n      setTotalBidding([]);\n      setLoading(false);\n    }\n  };\n\n  // Get monthly billable day\n  const getMonthlyBillableDay = async () => {\n    const params = {\n      year: conditions.year\n    };\n    const response = await sendRequest(Api.sale_pipe_line_bidding.getMonthlyBillable, params);\n    if (response !== null && response !== void 0 && response.status) {\n      const {\n        result\n      } = response;\n      if (result) {\n        setMonthlyBillableDay(result.content);\n      }\n    } else return;\n  };\n\n  // Get Exchange rate USD / VND\n  const getCurrencyAndExchangeRateDefault = async () => {\n    const response = await sendRequest(Api.sale_productivity.getExchangeRate, {\n      year: conditions.year,\n      convert: 'Yes'\n    });\n    if (response) {\n      if (response.status) {\n        const {\n          content\n        } = response.result;\n        if (!isEmpty(content)) {\n          setCurrencyAndExchangeRateDefault(content[0]);\n        } else {\n          setCurrencyAndExchangeRateDefault(null);\n        }\n      }\n    } else return;\n  };\n\n  // Get exchange rate usd / vnd\n  const getExchangeRateUSDpercentVND = async () => {\n    const response = await sendRequest(Api.sale_productivity.getExchangeRate, {\n      year: conditions.year,\n      convert: 'Yes',\n      currency: 'USD'\n    });\n    if (response) {\n      if (response.status) {\n        const {\n          content\n        } = response.result;\n        if (!isEmpty(content)) {\n          setExchangeRateUSDpercentVND(content[0].exchangeRate);\n        } else return;\n      }\n    } else return;\n  };\n\n  // Set data empty\n  const setDataEmpty = () => {\n    setSaleBiddings([]);\n    setLoading(false);\n  };\n\n  // postAddOrEditBidding\n  const postAddOrEditBidding = async payload => {\n    setLoadingPost(true);\n    const response = await sendRequest(Api.sale_pipe_line_bidding.postAddOrEditBidding, payload);\n    if (response) {\n      if (response !== null && response !== void 0 && response.status) {\n        setLoadingPost(false);\n        setOpenFormAddOrEditBidding(false);\n        setIsRefresh(!isRefresh);\n        dispatch(closeConfirm());\n        getDataTable();\n        dispatch(openSnackbar({\n          open: true,\n          message: isEditBidding ? 'update-success' : 'add-success',\n          variant: 'alert',\n          alert: {\n            color: 'success'\n          }\n        }));\n      } else {\n        setLoadingPost(false);\n        dispatch(openSnackbar({\n          open: true,\n          message: response.result.content,\n          variant: 'alert',\n          alert: {\n            color: 'warning'\n          }\n        }));\n      }\n    }\n    setLoadingPost(false);\n  };\n\n  // Get detail bidding\n  const getDetailBidding = async idHexString => {\n    const response = await sendRequest(Api.sale_pipe_line_bidding.getDetailBidding, {\n      year: conditions.year,\n      idHexString\n    });\n    if (response !== null && response !== void 0 && response.status) {\n      setOpenFormAddOrEditBidding(true);\n      const {\n        result\n      } = response;\n      setDetailBidding(result.content);\n    } else return;\n  };\n\n  // delete bidding\n  const deleteProjectBidding = async idHexString => {\n    const response = await sendRequest(Api.sale_pipe_line_bidding.deleteBidding, {\n      idHexString\n    });\n    if (response.status) {\n      dispatch(closeConfirm());\n      dispatch(openSnackbar({\n        open: true,\n        message: 'delete-success',\n        variant: 'alert',\n        alert: {\n          color: 'success'\n        }\n      }));\n      getDataTable();\n    }\n  };\n\n  // post edit comment bidding\n  const postEditComment = async payload => {\n    const response = await sendRequest(Api.sale_pipe_line_bidding.comment, {\n      ...payload,\n      year: conditions.year\n    });\n    if (response !== null && response !== void 0 && response.status) {\n      dispatch(openSnackbar({\n        open: true,\n        message: 'update-success',\n        variant: 'alert',\n        alert: {\n          color: 'success'\n        }\n      }));\n      setIsEditComment(false);\n      const {\n        content\n      } = response.result;\n      const biddingListUpdate = saleBiddings.map(prv => {\n        if (prv.idHexString === content.idHexString) {\n          return content;\n        }\n        return prv;\n      });\n      setSaleBiddings(biddingListUpdate);\n    } else return;\n  };\n\n  // ================= Event =================\n  // Handle delete bidding\n  const handleOpenDeleteProjectBidding = idHexString => {\n    dispatch(openConfirm({\n      open: true,\n      title: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n        id: \"warning\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 24\n      }, this),\n      content: /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: \"messege-delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 25\n        }, this)\n      }, void 0, false),\n      handleConfirm: () => deleteProjectBidding(idHexString)\n    }));\n  };\n\n  // Handle open add or edit modal bidding\n  const handleOpenFormAddOrUpdateBidding = idHexString => {\n    if (idHexString) {\n      setIsEditBidding(true);\n      getDetailBidding(idHexString);\n      return;\n    }\n    setOpenFormAddOrEditBidding(true);\n    setDetailBidding(null);\n  };\n\n  // Handle close form add or update bidding\n  const handleCloseFormAddOrUpdateBidding = () => {\n    setOpenFormAddOrEditBidding(false);\n    setIsEditBidding(false);\n    setDetailBidding(null);\n  };\n\n  // Handle change page\n  const handleChangePage = (event, newPage) => {\n    setConditions({\n      ...conditions,\n      page: newPage\n    });\n    setSearchParams({\n      ...params,\n      page: newPage\n    });\n  };\n\n  // Handle change rows per page\n  const handleChangeRowsPerPage = event => {\n    setConditions({\n      ...conditions,\n      page: paginationParamDefault.page,\n      size: parseInt(event.target.value, 10)\n    });\n    setSearchParams({\n      ...params,\n      page: paginationParamDefault.page,\n      size: parseInt(event.target.value, 10)\n    });\n  };\n\n  // Handle comment bidding\n  const handleOpenCommentBidding = (event, item) => {\n    setAnchorElComment(event.currentTarget);\n    const {\n      comment,\n      ...cloneItem\n    } = item;\n    setCommentItem({\n      ...cloneItem,\n      content: comment\n    });\n  };\n\n  // Handle close comment bidding\n  const handleCloseCommentBidding = () => {\n    setAnchorElComment(null);\n    setIsEditComment(false);\n  };\n\n  // ================= Handle submit =================\n  const handleSearch = values => {\n    transformObject(values);\n    setSearchParams(values);\n    setConditions(values);\n  };\n  const hanldeConfirmEditList = async list => {\n    const res = await sendRequest(Api.flexible_report.editArrangement, list);\n    dispatch(openSnackbar({\n      open: true,\n      message: res.status ? 'update-success' : 'update-fail',\n      variant: 'alert',\n      alert: {\n        color: res.status ? 'success' : 'error'\n      }\n    }));\n    if (res.status) {\n      setTotalBidding(list);\n      setIsEditedTotalList(false);\n    }\n  };\n  // ================= Effect =================\n  useEffect(() => {\n    getDataTable();\n    getMonthlyBillableDay();\n    getExchangeRateUSDpercentVND();\n    getCurrencyAndExchangeRateDefault();\n    getTotalBidding();\n  }, [conditions]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(FilterCollapse, {\n      children: /*#__PURE__*/_jsxDEV(BiddingSearch, {\n        formReset: formReset,\n        handleSearch: handleSearch,\n        isRefresh: isRefresh\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 13\n    }, this), totalBidding.length > 0 && /*#__PURE__*/_jsxDEV(BiddingTotal, {\n      totalBidding: totalBidding,\n      loading: loading,\n      isEdited: isEditedTotalList,\n      setIsEdited: setIsEditedTotalList,\n      handleConFirmEdit: hanldeConfirmEditList\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(MainCard, {\n      children: [/*#__PURE__*/_jsxDEV(TableToolbar, {\n        handleOpen: checkAllowedPermission(biddingPermission.add) ? handleOpenFormAddOrUpdateBidding : undefined,\n        handleRefreshData: getDataTable\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        heads: /*#__PURE__*/_jsxDEV(BiddingThead, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 31\n        }, this),\n        isLoading: loading,\n        data: saleBiddings,\n        children: /*#__PURE__*/_jsxDEV(BiddingTBody, {\n          pageNumber: conditions.page,\n          pageSize: conditions.size,\n          handleOpen: handleOpenFormAddOrUpdateBidding,\n          saleBiddings: saleBiddings,\n          handleDelete: handleOpenDeleteProjectBidding,\n          handleOpenComment: handleOpenCommentBidding\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 13\n    }, this), !loading && /*#__PURE__*/_jsxDEV(TableFooter, {\n      pagination: {\n        total: paginationResponse.totalElement,\n        page: conditions.page,\n        size: conditions.size\n      },\n      onPageChange: handleChangePage,\n      onRowsPerPageChange: handleChangeRowsPerPage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 417,\n      columnNumber: 17\n    }, this), openFormAddOrEditBidding && /*#__PURE__*/_jsxDEV(AddOrEditBidding, {\n      monthlyBillableDay: monthlyBillableDay,\n      year: conditions.year,\n      exchangeRateUSDpercentVND: exchangeRateUSDpercentVND,\n      currencyAndExchangeRateDefault: currencyAndExchangeRateDefault,\n      open: openFormAddOrEditBidding,\n      handleClose: handleCloseFormAddOrUpdateBidding,\n      isEdit: isEditBidding,\n      postAddOrEditBidding: postAddOrEditBidding,\n      detailBidding: detailBidding,\n      loading: loadingPost\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(CommentPopover, {\n      isSalesPipeLine: true,\n      item: commentItem,\n      anchorEl: anchorElComment,\n      handleClose: handleCloseCommentBidding,\n      isEdit: isEditComment,\n      setIsEdit: setIsEditComment,\n      editComment: postEditComment\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 439,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(Bidding, \"bPvJFPw8V8KQgXS6kFOs/JuNk7E=\", false, function () {\n  return [useSearchParams, useConfig, useAppDispatch];\n});\n_c = Bidding;\nexport default Bidding;\nvar _c;\n$RefreshReg$(_c, \"Bidding\");", "map": {"version": 3, "names": ["useEffect", "useState", "useAppDispatch", "MainCard", "Table", "TableFooter", "Api", "SEARCH_PARAM_KEY", "paginationParamDefault", "paginationResponseDefault", "PERMISSIONS", "TableToolbar", "AddOrEditBidding", "BiddingSearch", "BiddingTBody", "BiddingThead", "BiddingTotal", "CommentPopover", "FilterCollapse", "sendRequest", "closeConfirm", "openConfirm", "openSnackbar", "checkAllowedPermission", "getSearchParam", "isEmpty", "transformObject", "biddingFilterConfig", "FormattedMessage", "useSearchParams", "useConfig", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Bidding", "_s", "searchParams", "setSearchParams", "keyParams", "page", "size", "type", "year", "status", "searchMode", "projectName", "customer", "searchModeOfCustomer", "searchModeOfProject", "params", "locale", "dispatch", "defaultConditions", "language", "loading", "setLoading", "loadingPost", "setLoadingPost", "paginationResponse", "setPaginationResponse", "pageNumber", "pageSize", "saleBiddings", "setSaleBiddings", "detailBidding", "setDetailBidding", "totalBidding", "setTotalBidding", "conditions", "setConditions", "formReset", "isEditBidding", "setIsEditBidding", "openFormAddOrEditBidding", "setOpenFormAddOrEditBidding", "biddingPermission", "sale", "salePipeline", "exchangeRateUSDpercentVND", "setExchangeRateUSDpercentVND", "currencyAndExchangeRateDefault", "setCurrencyAndExchangeRateDefault", "monthlyBillableDay", "setMonthlyBillableDay", "commentItem", "setCommentItem", "isEditComment", "setIsEditComment", "anchorElComment", "setAnchorElComment", "isRefresh", "setIsRefresh", "isEditedTotalList", "setIsEditedTotalList", "getDataTable", "response", "sale_pipe_line_bidding", "getBidding", "result", "content", "pagination", "dataList", "totalElement", "setDataEmpty", "getTotalBidding", "getTotal", "total", "filter", "item", "show", "getMonthlyBillableDay", "getMonthlyBillable", "getCurrencyAndExchangeRateDefault", "sale_productivity", "getExchangeRate", "convert", "getExchangeRateUSDpercentVND", "currency", "exchangeRate", "postAddOrEditBidding", "payload", "open", "message", "variant", "alert", "color", "getDetailBidding", "idHexString", "deleteProjectBidding", "deleteBidding", "postEditComment", "comment", "biddingListUpdate", "map", "prv", "handleOpenDeleteProjectBidding", "title", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "handleConfirm", "handleOpenFormAddOrUpdateBidding", "handleCloseFormAddOrUpdateBidding", "handleChangePage", "event", "newPage", "handleChangeRowsPerPage", "parseInt", "target", "value", "handleOpenCommentBidding", "currentTarget", "cloneItem", "handleCloseCommentBidding", "handleSearch", "values", "hanldeConfirmEditList", "list", "res", "flexible_report", "editArrangement", "length", "isEdited", "setIsEdited", "handleConFirmEdit", "handleOpen", "add", "undefined", "handleRefreshData", "heads", "isLoading", "data", "handleDelete", "handleOpenComment", "onPageChange", "onRowsPerPageChange", "handleClose", "isEdit", "isSalesPipeLine", "anchorEl", "setIsEdit", "editComment", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/pages/sales/Bidding.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\nimport { useEffect, useState } from 'react';\n\n// project imports\nimport { useAppDispatch } from 'app/hooks';\nimport MainCard from 'components/cards/MainCard';\nimport { Table, TableFooter } from 'components/extended/Table';\nimport Api from 'constants/Api';\nimport { SEARCH_PARAM_KEY, paginationParamDefault, paginationResponseDefault } from 'constants/Common';\nimport { PERMISSIONS } from 'constants/Permission';\nimport { TableToolbar } from 'containers';\nimport { AddOrEditBidding, BiddingSearch, BiddingTBody, BiddingThead, BiddingTotal, CommentPopover } from 'containers/sales';\nimport { FilterCollapse } from 'containers/search';\nimport sendRequest from 'services/ApiService';\nimport { closeConfirm, openConfirm } from 'store/slice/confirmSlice';\nimport { openSnackbar } from 'store/slice/snackbarSlice';\nimport {\n    IBiddingResponse,\n    ICurrencyBidding,\n    ICurrencyBiddingList,\n    IDetailBidding,\n    IDetailBiddingResponse,\n    IPaginationResponse,\n    IResponseList,\n    ISaleBiddingItem,\n    ITotalBidding,\n    ITotalBiddingResponse\n} from 'types';\nimport { checkAllowedPermission } from 'utils/authorization';\nimport { getSearchParam, isEmpty, transformObject } from 'utils/common';\nimport { IBiddingFilterConfig, biddingFilterConfig } from './Config';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\nimport { useSearchParams } from 'react-router-dom';\n\n// material-ui\nimport { PopoverVirtualElement } from '@mui/material';\nimport useConfig from 'hooks/useConfig';\n\n// ==============================|| Bidding ||============================== //\n/**\n *  URL Params\n *  page\n *  size\n *  type\n *  year\n *  projectName\n *  status\n */\nconst Bidding = () => {\n    // URL Params\n    const [searchParams, setSearchParams] = useSearchParams();\n    const keyParams = [\n        SEARCH_PARAM_KEY.page,\n        SEARCH_PARAM_KEY.size,\n        SEARCH_PARAM_KEY.type,\n        SEARCH_PARAM_KEY.year,\n        SEARCH_PARAM_KEY.status,\n        SEARCH_PARAM_KEY.searchMode,\n        SEARCH_PARAM_KEY.projectName,\n        SEARCH_PARAM_KEY.customer,\n        SEARCH_PARAM_KEY.searchModeOfCustomer,\n        SEARCH_PARAM_KEY.searchModeOfProject\n    ];\n    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);\n    transformObject(params);\n\n    const { locale } = useConfig();\n\n    // Hooks, State, Variable\n    const dispatch = useAppDispatch();\n    const defaultConditions = {\n        ...biddingFilterConfig,\n        ...params,\n        language: locale\n    };\n    const [loading, setLoading] = useState<boolean>(false);\n    const [loadingPost, setLoadingPost] = useState<boolean>(false);\n    const [paginationResponse, setPaginationResponse] = useState<IPaginationResponse>({\n        ...paginationResponseDefault,\n        pageNumber: params.page ? params.page : paginationResponseDefault.pageNumber,\n        pageSize: params.size ? params.size : paginationResponseDefault.pageSize\n    });\n    const [saleBiddings, setSaleBiddings] = useState<ISaleBiddingItem[]>([]);\n    const [detailBidding, setDetailBidding] = useState<IDetailBidding | null>(null);\n    const [totalBidding, setTotalBidding] = useState<ITotalBidding[]>([]);\n    const [conditions, setConditions] = useState<IBiddingFilterConfig>(defaultConditions);\n    const [formReset] = useState<IBiddingFilterConfig>(defaultConditions);\n    const [isEditBidding, setIsEditBidding] = useState<boolean>(false);\n    const [openFormAddOrEditBidding, setOpenFormAddOrEditBidding] = useState<boolean>(false);\n    const { biddingPermission } = PERMISSIONS.sale.salePipeline;\n    const [exchangeRateUSDpercentVND, setExchangeRateUSDpercentVND] = useState<number>(0);\n    const [currencyAndExchangeRateDefault, setCurrencyAndExchangeRateDefault] = useState<ICurrencyBidding | null>(null);\n    const [monthlyBillableDay, setMonthlyBillableDay] = useState<any>();\n    const [commentItem, setCommentItem] = useState<any>(null);\n    const [isEditComment, setIsEditComment] = useState<boolean>(false);\n    const [anchorElComment, setAnchorElComment] = useState<\n        Element | (() => Element) | PopoverVirtualElement | (() => PopoverVirtualElement) | null | undefined\n    >(null);\n    const [isRefresh, setIsRefresh] = useState<boolean>(false);\n\n    const [isEditedTotalList, setIsEditedTotalList] = useState(false);\n\n    // ================= Functions =================\n    // Get Bidding\n    const getDataTable = async () => {\n        setLoading(true);\n        const response: IResponseList<IBiddingResponse> = await sendRequest(Api.sale_pipe_line_bidding.getBidding, {\n            ...conditions,\n            page: conditions.page + 1\n        });\n\n        if (response) {\n            const { status, result } = response;\n\n            if (status) {\n                const { content, pagination } = result;\n                if (!isEmpty(content)) {\n                    setSaleBiddings(content.dataList);\n                    setPaginationResponse({ ...paginationResponse, totalElement: pagination?.totalElement });\n                    setLoading(false);\n                } else {\n                    setDataEmpty();\n                }\n            }\n        } else {\n            setDataEmpty();\n        }\n    };\n\n    const getTotalBidding = async () => {\n        setLoading(true);\n        const response: IResponseList<ITotalBiddingResponse> = await sendRequest(Api.sale_pipe_line_bidding.getTotal, {\n            ...conditions\n        });\n\n        if (response) {\n            const { status, result } = response;\n\n            if (status) {\n                const { content } = result;\n                if (!isEmpty(content)) {\n                    setTotalBidding(content.total.filter((item) => item.show));\n                    setLoading(false);\n                } else {\n                    setTotalBidding([]);\n                    setLoading(false);\n                }\n            }\n        } else {\n            setTotalBidding([]);\n            setLoading(false);\n        }\n    };\n\n    // Get monthly billable day\n    const getMonthlyBillableDay = async () => {\n        const params = { year: conditions.year };\n        const response = await sendRequest(Api.sale_pipe_line_bidding.getMonthlyBillable, params);\n        if (response?.status) {\n            const { result } = response;\n            if (result) {\n                setMonthlyBillableDay(result.content);\n            }\n        } else return;\n    };\n\n    // Get Exchange rate USD / VND\n    const getCurrencyAndExchangeRateDefault = async () => {\n        const response: IResponseList<ICurrencyBiddingList> = await sendRequest(Api.sale_productivity.getExchangeRate, {\n            year: conditions.year,\n            convert: 'Yes'\n        });\n        if (response) {\n            if (response.status) {\n                const { content } = response.result;\n                if (!isEmpty(content)) {\n                    setCurrencyAndExchangeRateDefault(content[0]);\n                } else {\n                    setCurrencyAndExchangeRateDefault(null);\n                }\n            }\n        } else return;\n    };\n\n    // Get exchange rate usd / vnd\n    const getExchangeRateUSDpercentVND = async () => {\n        const response: IResponseList<ICurrencyBiddingList> = await sendRequest(Api.sale_productivity.getExchangeRate, {\n            year: conditions.year,\n            convert: 'Yes',\n            currency: 'USD'\n        });\n        if (response) {\n            if (response.status) {\n                const { content } = response.result;\n                if (!isEmpty(content)) {\n                    setExchangeRateUSDpercentVND(content[0].exchangeRate);\n                } else return;\n            }\n        } else return;\n    };\n\n    // Set data empty\n    const setDataEmpty = () => {\n        setSaleBiddings([]);\n        setLoading(false);\n    };\n\n    // postAddOrEditBidding\n    const postAddOrEditBidding = async (payload: any) => {\n        setLoadingPost(true);\n        const response = await sendRequest(Api.sale_pipe_line_bidding.postAddOrEditBidding, payload);\n        if (response) {\n            if (response?.status) {\n                setLoadingPost(false);\n                setOpenFormAddOrEditBidding(false);\n                setIsRefresh(!isRefresh);\n                dispatch(closeConfirm());\n                getDataTable();\n                dispatch(\n                    openSnackbar({\n                        open: true,\n                        message: isEditBidding ? 'update-success' : 'add-success',\n                        variant: 'alert',\n                        alert: { color: 'success' }\n                    })\n                );\n            } else {\n                setLoadingPost(false);\n                dispatch(\n                    openSnackbar({\n                        open: true,\n                        message: response.result.content,\n                        variant: 'alert',\n                        alert: { color: 'warning' }\n                    })\n                );\n            }\n        }\n        setLoadingPost(false);\n    };\n\n    // Get detail bidding\n    const getDetailBidding = async (idHexString: string) => {\n        const response: IResponseList<IDetailBiddingResponse> = await sendRequest(Api.sale_pipe_line_bidding.getDetailBidding, {\n            year: conditions.year,\n            idHexString\n        });\n        if (response?.status) {\n            setOpenFormAddOrEditBidding(true);\n            const { result } = response;\n            setDetailBidding(result.content);\n        } else return;\n    };\n\n    // delete bidding\n    const deleteProjectBidding = async (idHexString: string) => {\n        const response = await sendRequest(Api.sale_pipe_line_bidding.deleteBidding, { idHexString });\n        if (response.status) {\n            dispatch(closeConfirm());\n            dispatch(\n                openSnackbar({\n                    open: true,\n                    message: 'delete-success',\n                    variant: 'alert',\n                    alert: { color: 'success' }\n                })\n            );\n            getDataTable();\n        }\n    };\n\n    // post edit comment bidding\n    const postEditComment = async (payload?: any) => {\n        const response = await sendRequest(Api.sale_pipe_line_bidding.comment, { ...payload, year: conditions.year });\n        if (response?.status) {\n            dispatch(openSnackbar({ open: true, message: 'update-success', variant: 'alert', alert: { color: 'success' } }));\n            setIsEditComment(false);\n            const { content } = response.result;\n            const biddingListUpdate = saleBiddings.map((prv) => {\n                if (prv.idHexString === content.idHexString) {\n                    return content;\n                }\n                return prv;\n            });\n            setSaleBiddings(biddingListUpdate);\n        } else return;\n    };\n\n    // ================= Event =================\n    // Handle delete bidding\n    const handleOpenDeleteProjectBidding = (idHexString: string) => {\n        dispatch(\n            openConfirm({\n                open: true,\n                title: <FormattedMessage id=\"warning\" />,\n                content: (\n                    <>\n                        <FormattedMessage id=\"messege-delete\" />\n                    </>\n                ),\n                handleConfirm: () => deleteProjectBidding(idHexString)\n            })\n        );\n    };\n\n    // Handle open add or edit modal bidding\n    const handleOpenFormAddOrUpdateBidding = (idHexString?: string) => {\n        if (idHexString) {\n            setIsEditBidding(true);\n            getDetailBidding(idHexString);\n            return;\n        }\n        setOpenFormAddOrEditBidding(true);\n        setDetailBidding(null);\n    };\n\n    // Handle close form add or update bidding\n    const handleCloseFormAddOrUpdateBidding = () => {\n        setOpenFormAddOrEditBidding(false);\n        setIsEditBidding(false);\n        setDetailBidding(null);\n    };\n\n    // Handle change page\n    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {\n        setConditions({ ...conditions, page: newPage });\n        setSearchParams({ ...params, page: newPage } as any);\n    };\n\n    // Handle change rows per page\n    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n        setConditions({ ...conditions, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) });\n        setSearchParams({ ...params, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) } as any);\n    };\n\n    // Handle comment bidding\n    const handleOpenCommentBidding = (event: React.MouseEvent<Element>, item: any) => {\n        setAnchorElComment(event.currentTarget);\n        const { comment, ...cloneItem } = item;\n        setCommentItem({ ...cloneItem, content: comment });\n    };\n\n    // Handle close comment bidding\n    const handleCloseCommentBidding = () => {\n        setAnchorElComment(null);\n        setIsEditComment(false);\n    };\n\n    // ================= Handle submit =================\n    const handleSearch = (values: any) => {\n        transformObject(values);\n        setSearchParams(values);\n        setConditions(values);\n    };\n\n    const hanldeConfirmEditList = async (list: ITotalBidding[]) => {\n        const res = await sendRequest(Api.flexible_report.editArrangement, list);\n        dispatch(\n            openSnackbar({\n                open: true,\n                message: res.status ? 'update-success' : 'update-fail',\n                variant: 'alert',\n                alert: { color: res.status ? 'success' : 'error' }\n            })\n        );\n        if (res.status) {\n            setTotalBidding(list);\n            setIsEditedTotalList(false);\n        }\n    };\n    // ================= Effect =================\n    useEffect(() => {\n        getDataTable();\n        getMonthlyBillableDay();\n        getExchangeRateUSDpercentVND();\n        getCurrencyAndExchangeRateDefault();\n        getTotalBidding();\n    }, [conditions]);\n\n    return (\n        <>\n            {/* Search form  */}\n            <FilterCollapse>\n                <BiddingSearch formReset={formReset} handleSearch={handleSearch} isRefresh={isRefresh} />\n            </FilterCollapse>\n            {/* Bidding Total  */}\n            {totalBidding.length > 0 && (\n                <BiddingTotal\n                    totalBidding={totalBidding}\n                    loading={loading}\n                    isEdited={isEditedTotalList}\n                    setIsEdited={setIsEditedTotalList}\n                    handleConFirmEdit={hanldeConfirmEditList}\n                />\n            )}\n            {/* Bidding List */}\n            <MainCard>\n                <TableToolbar\n                    handleOpen={checkAllowedPermission(biddingPermission.add) ? handleOpenFormAddOrUpdateBidding : undefined}\n                    handleRefreshData={getDataTable}\n                />\n                <Table heads={<BiddingThead />} isLoading={loading} data={saleBiddings}>\n                    <BiddingTBody\n                        pageNumber={conditions.page}\n                        pageSize={conditions.size}\n                        handleOpen={handleOpenFormAddOrUpdateBidding}\n                        saleBiddings={saleBiddings}\n                        handleDelete={handleOpenDeleteProjectBidding}\n                        handleOpenComment={handleOpenCommentBidding}\n                    />\n                </Table>\n            </MainCard>\n            {/* Pagination  */}\n            {!loading && (\n                <TableFooter\n                    pagination={{ total: paginationResponse.totalElement, page: conditions.page, size: conditions.size }}\n                    onPageChange={handleChangePage}\n                    onRowsPerPageChange={handleChangeRowsPerPage}\n                />\n            )}\n            {/* Form Add or Edit Bidding */}\n            {openFormAddOrEditBidding && (\n                <AddOrEditBidding\n                    monthlyBillableDay={monthlyBillableDay}\n                    year={conditions.year}\n                    exchangeRateUSDpercentVND={exchangeRateUSDpercentVND}\n                    currencyAndExchangeRateDefault={currencyAndExchangeRateDefault!}\n                    open={openFormAddOrEditBidding}\n                    handleClose={handleCloseFormAddOrUpdateBidding}\n                    isEdit={isEditBidding}\n                    postAddOrEditBidding={postAddOrEditBidding}\n                    detailBidding={detailBidding}\n                    loading={loadingPost}\n                />\n            )}\n            {/* Comment */}\n            <CommentPopover\n                isSalesPipeLine\n                item={commentItem!}\n                anchorEl={anchorElComment}\n                handleClose={handleCloseCommentBidding}\n                isEdit={isEditComment}\n                setIsEdit={setIsEditComment}\n                editComment={postEditComment}\n            />\n        </>\n    );\n};\n\nexport default Bidding;\n"], "mappings": ";;AAAA;AACA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;;AAE3C;AACA,SAASC,cAAc,QAAQ,WAAW;AAC1C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,KAAK,EAAEC,WAAW,QAAQ,2BAA2B;AAC9D,OAAOC,GAAG,MAAM,eAAe;AAC/B,SAASC,gBAAgB,EAAEC,sBAAsB,EAAEC,yBAAyB,QAAQ,kBAAkB;AACtG,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,YAAY,QAAQ,YAAY;AACzC,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAEC,cAAc,QAAQ,kBAAkB;AAC5H,SAASC,cAAc,QAAQ,mBAAmB;AAClD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,YAAY,EAAEC,WAAW,QAAQ,0BAA0B;AACpE,SAASC,YAAY,QAAQ,2BAA2B;AAaxD,SAASC,sBAAsB,QAAQ,qBAAqB;AAC5D,SAASC,cAAc,EAAEC,OAAO,EAAEC,eAAe,QAAQ,cAAc;AACvE,SAA+BC,mBAAmB,QAAQ,UAAU;;AAEpE;AACA,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,eAAe,QAAQ,kBAAkB;;AAElD;;AAEA,OAAOC,SAAS,MAAM,iBAAiB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGT,eAAe,CAAC,CAAC;EACzD,MAAMU,SAAS,GAAG,CACdhC,gBAAgB,CAACiC,IAAI,EACrBjC,gBAAgB,CAACkC,IAAI,EACrBlC,gBAAgB,CAACmC,IAAI,EACrBnC,gBAAgB,CAACoC,IAAI,EACrBpC,gBAAgB,CAACqC,MAAM,EACvBrC,gBAAgB,CAACsC,UAAU,EAC3BtC,gBAAgB,CAACuC,WAAW,EAC5BvC,gBAAgB,CAACwC,QAAQ,EACzBxC,gBAAgB,CAACyC,oBAAoB,EACrCzC,gBAAgB,CAAC0C,mBAAmB,CACvC;EACD,MAAMC,MAA8B,GAAG1B,cAAc,CAACe,SAAS,EAAEF,YAAY,CAAC;EAC9EX,eAAe,CAACwB,MAAM,CAAC;EAEvB,MAAM;IAAEC;EAAO,CAAC,GAAGrB,SAAS,CAAC,CAAC;;EAE9B;EACA,MAAMsB,QAAQ,GAAGlD,cAAc,CAAC,CAAC;EACjC,MAAMmD,iBAAiB,GAAG;IACtB,GAAG1B,mBAAmB;IACtB,GAAGuB,MAAM;IACTI,QAAQ,EAAEH;EACd,CAAC;EACD,MAAM,CAACI,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAU,KAAK,CAAC;EACtD,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAU,KAAK,CAAC;EAC9D,MAAM,CAAC0D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3D,QAAQ,CAAsB;IAC9E,GAAGQ,yBAAyB;IAC5BoD,UAAU,EAAEX,MAAM,CAACV,IAAI,GAAGU,MAAM,CAACV,IAAI,GAAG/B,yBAAyB,CAACoD,UAAU;IAC5EC,QAAQ,EAAEZ,MAAM,CAACT,IAAI,GAAGS,MAAM,CAACT,IAAI,GAAGhC,yBAAyB,CAACqD;EACpE,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAqB,EAAE,CAAC;EACxE,MAAM,CAACgE,aAAa,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAAwB,IAAI,CAAC;EAC/E,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAkB,EAAE,CAAC;EACrE,MAAM,CAACoE,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAuBoD,iBAAiB,CAAC;EACrF,MAAM,CAACkB,SAAS,CAAC,GAAGtE,QAAQ,CAAuBoD,iBAAiB,CAAC;EACrE,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxE,QAAQ,CAAU,KAAK,CAAC;EAClE,MAAM,CAACyE,wBAAwB,EAAEC,2BAA2B,CAAC,GAAG1E,QAAQ,CAAU,KAAK,CAAC;EACxF,MAAM;IAAE2E;EAAkB,CAAC,GAAGlE,WAAW,CAACmE,IAAI,CAACC,YAAY;EAC3D,MAAM,CAACC,yBAAyB,EAAEC,4BAA4B,CAAC,GAAG/E,QAAQ,CAAS,CAAC,CAAC;EACrF,MAAM,CAACgF,8BAA8B,EAAEC,iCAAiC,CAAC,GAAGjF,QAAQ,CAA0B,IAAI,CAAC;EACnH,MAAM,CAACkF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnF,QAAQ,CAAM,CAAC;EACnE,MAAM,CAACoF,WAAW,EAAEC,cAAc,CAAC,GAAGrF,QAAQ,CAAM,IAAI,CAAC;EACzD,MAAM,CAACsF,aAAa,EAAEC,gBAAgB,CAAC,GAAGvF,QAAQ,CAAU,KAAK,CAAC;EAClE,MAAM,CAACwF,eAAe,EAAEC,kBAAkB,CAAC,GAAGzF,QAAQ,CAEpD,IAAI,CAAC;EACP,MAAM,CAAC0F,SAAS,EAAEC,YAAY,CAAC,GAAG3F,QAAQ,CAAU,KAAK,CAAC;EAE1D,MAAM,CAAC4F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA;EACA,MAAM8F,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7BvC,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMwC,QAAyC,GAAG,MAAM7E,WAAW,CAACb,GAAG,CAAC2F,sBAAsB,CAACC,UAAU,EAAE;MACvG,GAAG7B,UAAU;MACb7B,IAAI,EAAE6B,UAAU,CAAC7B,IAAI,GAAG;IAC5B,CAAC,CAAC;IAEF,IAAIwD,QAAQ,EAAE;MACV,MAAM;QAAEpD,MAAM;QAAEuD;MAAO,CAAC,GAAGH,QAAQ;MAEnC,IAAIpD,MAAM,EAAE;QACR,MAAM;UAAEwD,OAAO;UAAEC;QAAW,CAAC,GAAGF,MAAM;QACtC,IAAI,CAAC1E,OAAO,CAAC2E,OAAO,CAAC,EAAE;UACnBpC,eAAe,CAACoC,OAAO,CAACE,QAAQ,CAAC;UACjC1C,qBAAqB,CAAC;YAAE,GAAGD,kBAAkB;YAAE4C,YAAY,EAAEF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEE;UAAa,CAAC,CAAC;UACxF/C,UAAU,CAAC,KAAK,CAAC;QACrB,CAAC,MAAM;UACHgD,YAAY,CAAC,CAAC;QAClB;MACJ;IACJ,CAAC,MAAM;MACHA,YAAY,CAAC,CAAC;IAClB;EACJ,CAAC;EAED,MAAMC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChCjD,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMwC,QAA8C,GAAG,MAAM7E,WAAW,CAACb,GAAG,CAAC2F,sBAAsB,CAACS,QAAQ,EAAE;MAC1G,GAAGrC;IACP,CAAC,CAAC;IAEF,IAAI2B,QAAQ,EAAE;MACV,MAAM;QAAEpD,MAAM;QAAEuD;MAAO,CAAC,GAAGH,QAAQ;MAEnC,IAAIpD,MAAM,EAAE;QACR,MAAM;UAAEwD;QAAQ,CAAC,GAAGD,MAAM;QAC1B,IAAI,CAAC1E,OAAO,CAAC2E,OAAO,CAAC,EAAE;UACnBhC,eAAe,CAACgC,OAAO,CAACO,KAAK,CAACC,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACC,IAAI,CAAC,CAAC;UAC1DtD,UAAU,CAAC,KAAK,CAAC;QACrB,CAAC,MAAM;UACHY,eAAe,CAAC,EAAE,CAAC;UACnBZ,UAAU,CAAC,KAAK,CAAC;QACrB;MACJ;IACJ,CAAC,MAAM;MACHY,eAAe,CAAC,EAAE,CAAC;MACnBZ,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA,MAAMuD,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACtC,MAAM7D,MAAM,GAAG;MAAEP,IAAI,EAAE0B,UAAU,CAAC1B;IAAK,CAAC;IACxC,MAAMqD,QAAQ,GAAG,MAAM7E,WAAW,CAACb,GAAG,CAAC2F,sBAAsB,CAACe,kBAAkB,EAAE9D,MAAM,CAAC;IACzF,IAAI8C,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEpD,MAAM,EAAE;MAClB,MAAM;QAAEuD;MAAO,CAAC,GAAGH,QAAQ;MAC3B,IAAIG,MAAM,EAAE;QACRf,qBAAqB,CAACe,MAAM,CAACC,OAAO,CAAC;MACzC;IACJ,CAAC,MAAM;EACX,CAAC;;EAED;EACA,MAAMa,iCAAiC,GAAG,MAAAA,CAAA,KAAY;IAClD,MAAMjB,QAA6C,GAAG,MAAM7E,WAAW,CAACb,GAAG,CAAC4G,iBAAiB,CAACC,eAAe,EAAE;MAC3GxE,IAAI,EAAE0B,UAAU,CAAC1B,IAAI;MACrByE,OAAO,EAAE;IACb,CAAC,CAAC;IACF,IAAIpB,QAAQ,EAAE;MACV,IAAIA,QAAQ,CAACpD,MAAM,EAAE;QACjB,MAAM;UAAEwD;QAAQ,CAAC,GAAGJ,QAAQ,CAACG,MAAM;QACnC,IAAI,CAAC1E,OAAO,CAAC2E,OAAO,CAAC,EAAE;UACnBlB,iCAAiC,CAACkB,OAAO,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,MAAM;UACHlB,iCAAiC,CAAC,IAAI,CAAC;QAC3C;MACJ;IACJ,CAAC,MAAM;EACX,CAAC;;EAED;EACA,MAAMmC,4BAA4B,GAAG,MAAAA,CAAA,KAAY;IAC7C,MAAMrB,QAA6C,GAAG,MAAM7E,WAAW,CAACb,GAAG,CAAC4G,iBAAiB,CAACC,eAAe,EAAE;MAC3GxE,IAAI,EAAE0B,UAAU,CAAC1B,IAAI;MACrByE,OAAO,EAAE,KAAK;MACdE,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAItB,QAAQ,EAAE;MACV,IAAIA,QAAQ,CAACpD,MAAM,EAAE;QACjB,MAAM;UAAEwD;QAAQ,CAAC,GAAGJ,QAAQ,CAACG,MAAM;QACnC,IAAI,CAAC1E,OAAO,CAAC2E,OAAO,CAAC,EAAE;UACnBpB,4BAA4B,CAACoB,OAAO,CAAC,CAAC,CAAC,CAACmB,YAAY,CAAC;QACzD,CAAC,MAAM;MACX;IACJ,CAAC,MAAM;EACX,CAAC;;EAED;EACA,MAAMf,YAAY,GAAGA,CAAA,KAAM;IACvBxC,eAAe,CAAC,EAAE,CAAC;IACnBR,UAAU,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMgE,oBAAoB,GAAG,MAAOC,OAAY,IAAK;IACjD/D,cAAc,CAAC,IAAI,CAAC;IACpB,MAAMsC,QAAQ,GAAG,MAAM7E,WAAW,CAACb,GAAG,CAAC2F,sBAAsB,CAACuB,oBAAoB,EAAEC,OAAO,CAAC;IAC5F,IAAIzB,QAAQ,EAAE;MACV,IAAIA,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEpD,MAAM,EAAE;QAClBc,cAAc,CAAC,KAAK,CAAC;QACrBiB,2BAA2B,CAAC,KAAK,CAAC;QAClCiB,YAAY,CAAC,CAACD,SAAS,CAAC;QACxBvC,QAAQ,CAAChC,YAAY,CAAC,CAAC,CAAC;QACxB2E,YAAY,CAAC,CAAC;QACd3C,QAAQ,CACJ9B,YAAY,CAAC;UACToG,IAAI,EAAE,IAAI;UACVC,OAAO,EAAEnD,aAAa,GAAG,gBAAgB,GAAG,aAAa;UACzDoD,OAAO,EAAE,OAAO;UAChBC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAC9B,CAAC,CACL,CAAC;MACL,CAAC,MAAM;QACHpE,cAAc,CAAC,KAAK,CAAC;QACrBN,QAAQ,CACJ9B,YAAY,CAAC;UACToG,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE3B,QAAQ,CAACG,MAAM,CAACC,OAAO;UAChCwB,OAAO,EAAE,OAAO;UAChBC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAC9B,CAAC,CACL,CAAC;MACL;IACJ;IACApE,cAAc,CAAC,KAAK,CAAC;EACzB,CAAC;;EAED;EACA,MAAMqE,gBAAgB,GAAG,MAAOC,WAAmB,IAAK;IACpD,MAAMhC,QAA+C,GAAG,MAAM7E,WAAW,CAACb,GAAG,CAAC2F,sBAAsB,CAAC8B,gBAAgB,EAAE;MACnHpF,IAAI,EAAE0B,UAAU,CAAC1B,IAAI;MACrBqF;IACJ,CAAC,CAAC;IACF,IAAIhC,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEpD,MAAM,EAAE;MAClB+B,2BAA2B,CAAC,IAAI,CAAC;MACjC,MAAM;QAAEwB;MAAO,CAAC,GAAGH,QAAQ;MAC3B9B,gBAAgB,CAACiC,MAAM,CAACC,OAAO,CAAC;IACpC,CAAC,MAAM;EACX,CAAC;;EAED;EACA,MAAM6B,oBAAoB,GAAG,MAAOD,WAAmB,IAAK;IACxD,MAAMhC,QAAQ,GAAG,MAAM7E,WAAW,CAACb,GAAG,CAAC2F,sBAAsB,CAACiC,aAAa,EAAE;MAAEF;IAAY,CAAC,CAAC;IAC7F,IAAIhC,QAAQ,CAACpD,MAAM,EAAE;MACjBQ,QAAQ,CAAChC,YAAY,CAAC,CAAC,CAAC;MACxBgC,QAAQ,CACJ9B,YAAY,CAAC;QACToG,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,gBAAgB;QACzBC,OAAO,EAAE,OAAO;QAChBC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAC9B,CAAC,CACL,CAAC;MACD/B,YAAY,CAAC,CAAC;IAClB;EACJ,CAAC;;EAED;EACA,MAAMoC,eAAe,GAAG,MAAOV,OAAa,IAAK;IAC7C,MAAMzB,QAAQ,GAAG,MAAM7E,WAAW,CAACb,GAAG,CAAC2F,sBAAsB,CAACmC,OAAO,EAAE;MAAE,GAAGX,OAAO;MAAE9E,IAAI,EAAE0B,UAAU,CAAC1B;IAAK,CAAC,CAAC;IAC7G,IAAIqD,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEpD,MAAM,EAAE;MAClBQ,QAAQ,CAAC9B,YAAY,CAAC;QAAEoG,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,gBAAgB;QAAEC,OAAO,EAAE,OAAO;QAAEC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAAE,CAAC,CAAC,CAAC;MAChHtC,gBAAgB,CAAC,KAAK,CAAC;MACvB,MAAM;QAAEY;MAAQ,CAAC,GAAGJ,QAAQ,CAACG,MAAM;MACnC,MAAMkC,iBAAiB,GAAGtE,YAAY,CAACuE,GAAG,CAAEC,GAAG,IAAK;QAChD,IAAIA,GAAG,CAACP,WAAW,KAAK5B,OAAO,CAAC4B,WAAW,EAAE;UACzC,OAAO5B,OAAO;QAClB;QACA,OAAOmC,GAAG;MACd,CAAC,CAAC;MACFvE,eAAe,CAACqE,iBAAiB,CAAC;IACtC,CAAC,MAAM;EACX,CAAC;;EAED;EACA;EACA,MAAMG,8BAA8B,GAAIR,WAAmB,IAAK;IAC5D5E,QAAQ,CACJ/B,WAAW,CAAC;MACRqG,IAAI,EAAE,IAAI;MACVe,KAAK,eAAEzG,OAAA,CAACJ,gBAAgB;QAAC8G,EAAE,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACxC1C,OAAO,eACHpE,OAAA,CAAAE,SAAA;QAAA6G,QAAA,eACI/G,OAAA,CAACJ,gBAAgB;UAAC8G,EAAE,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC,gBAC1C,CACL;MACDE,aAAa,EAAEA,CAAA,KAAMf,oBAAoB,CAACD,WAAW;IACzD,CAAC,CACL,CAAC;EACL,CAAC;;EAED;EACA,MAAMiB,gCAAgC,GAAIjB,WAAoB,IAAK;IAC/D,IAAIA,WAAW,EAAE;MACbvD,gBAAgB,CAAC,IAAI,CAAC;MACtBsD,gBAAgB,CAACC,WAAW,CAAC;MAC7B;IACJ;IACArD,2BAA2B,CAAC,IAAI,CAAC;IACjCT,gBAAgB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMgF,iCAAiC,GAAGA,CAAA,KAAM;IAC5CvE,2BAA2B,CAAC,KAAK,CAAC;IAClCF,gBAAgB,CAAC,KAAK,CAAC;IACvBP,gBAAgB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMiF,gBAAgB,GAAGA,CAACC,KAAiD,EAAEC,OAAe,KAAK;IAC7F/E,aAAa,CAAC;MAAE,GAAGD,UAAU;MAAE7B,IAAI,EAAE6G;IAAQ,CAAC,CAAC;IAC/C/G,eAAe,CAAC;MAAE,GAAGY,MAAM;MAAEV,IAAI,EAAE6G;IAAQ,CAAQ,CAAC;EACxD,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAIF,KAAgE,IAAK;IAClG9E,aAAa,CAAC;MAAE,GAAGD,UAAU;MAAE7B,IAAI,EAAEhC,sBAAsB,CAACgC,IAAI;MAAEC,IAAI,EAAE8G,QAAQ,CAACH,KAAK,CAACI,MAAM,CAACC,KAAK,EAAE,EAAE;IAAE,CAAC,CAAC;IAC3GnH,eAAe,CAAC;MAAE,GAAGY,MAAM;MAAEV,IAAI,EAAEhC,sBAAsB,CAACgC,IAAI;MAAEC,IAAI,EAAE8G,QAAQ,CAACH,KAAK,CAACI,MAAM,CAACC,KAAK,EAAE,EAAE;IAAE,CAAQ,CAAC;EACpH,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAGA,CAACN,KAAgC,EAAEvC,IAAS,KAAK;IAC9EnB,kBAAkB,CAAC0D,KAAK,CAACO,aAAa,CAAC;IACvC,MAAM;MAAEvB,OAAO;MAAE,GAAGwB;IAAU,CAAC,GAAG/C,IAAI;IACtCvB,cAAc,CAAC;MAAE,GAAGsE,SAAS;MAAExD,OAAO,EAAEgC;IAAQ,CAAC,CAAC;EACtD,CAAC;;EAED;EACA,MAAMyB,yBAAyB,GAAGA,CAAA,KAAM;IACpCnE,kBAAkB,CAAC,IAAI,CAAC;IACxBF,gBAAgB,CAAC,KAAK,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMsE,YAAY,GAAIC,MAAW,IAAK;IAClCrI,eAAe,CAACqI,MAAM,CAAC;IACvBzH,eAAe,CAACyH,MAAM,CAAC;IACvBzF,aAAa,CAACyF,MAAM,CAAC;EACzB,CAAC;EAED,MAAMC,qBAAqB,GAAG,MAAOC,IAAqB,IAAK;IAC3D,MAAMC,GAAG,GAAG,MAAM/I,WAAW,CAACb,GAAG,CAAC6J,eAAe,CAACC,eAAe,EAAEH,IAAI,CAAC;IACxE7G,QAAQ,CACJ9B,YAAY,CAAC;MACToG,IAAI,EAAE,IAAI;MACVC,OAAO,EAAEuC,GAAG,CAACtH,MAAM,GAAG,gBAAgB,GAAG,aAAa;MACtDgF,OAAO,EAAE,OAAO;MAChBC,KAAK,EAAE;QAAEC,KAAK,EAAEoC,GAAG,CAACtH,MAAM,GAAG,SAAS,GAAG;MAAQ;IACrD,CAAC,CACL,CAAC;IACD,IAAIsH,GAAG,CAACtH,MAAM,EAAE;MACZwB,eAAe,CAAC6F,IAAI,CAAC;MACrBnE,oBAAoB,CAAC,KAAK,CAAC;IAC/B;EACJ,CAAC;EACD;EACA9F,SAAS,CAAC,MAAM;IACZ+F,YAAY,CAAC,CAAC;IACdgB,qBAAqB,CAAC,CAAC;IACvBM,4BAA4B,CAAC,CAAC;IAC9BJ,iCAAiC,CAAC,CAAC;IACnCR,eAAe,CAAC,CAAC;EACrB,CAAC,EAAE,CAACpC,UAAU,CAAC,CAAC;EAEhB,oBACIrC,OAAA,CAAAE,SAAA;IAAA6G,QAAA,gBAEI/G,OAAA,CAACd,cAAc;MAAA6H,QAAA,eACX/G,OAAA,CAACnB,aAAa;QAAC0D,SAAS,EAAEA,SAAU;QAACuF,YAAY,EAAEA,YAAa;QAACnE,SAAS,EAAEA;MAAU;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7E,CAAC,EAEhB3E,YAAY,CAACkG,MAAM,GAAG,CAAC,iBACpBrI,OAAA,CAAChB,YAAY;MACTmD,YAAY,EAAEA,YAAa;MAC3BZ,OAAO,EAAEA,OAAQ;MACjB+G,QAAQ,EAAEzE,iBAAkB;MAC5B0E,WAAW,EAAEzE,oBAAqB;MAClC0E,iBAAiB,EAAER;IAAsB;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CACJ,eAED9G,OAAA,CAAC7B,QAAQ;MAAA4I,QAAA,gBACL/G,OAAA,CAACrB,YAAY;QACT8J,UAAU,EAAElJ,sBAAsB,CAACqD,iBAAiB,CAAC8F,GAAG,CAAC,GAAGzB,gCAAgC,GAAG0B,SAAU;QACzGC,iBAAiB,EAAE7E;MAAa;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACF9G,OAAA,CAAC5B,KAAK;QAACyK,KAAK,eAAE7I,OAAA,CAACjB,YAAY;UAAA4H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACgC,SAAS,EAAEvH,OAAQ;QAACwH,IAAI,EAAEhH,YAAa;QAAAgF,QAAA,eACnE/G,OAAA,CAAClB,YAAY;UACT+C,UAAU,EAAEQ,UAAU,CAAC7B,IAAK;UAC5BsB,QAAQ,EAAEO,UAAU,CAAC5B,IAAK;UAC1BgI,UAAU,EAAExB,gCAAiC;UAC7ClF,YAAY,EAAEA,YAAa;UAC3BiH,YAAY,EAAExC,8BAA+B;UAC7CyC,iBAAiB,EAAEvB;QAAyB;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAEV,CAACvF,OAAO,iBACLvB,OAAA,CAAC3B,WAAW;MACRgG,UAAU,EAAE;QAAEM,KAAK,EAAEhD,kBAAkB,CAAC4C,YAAY;QAAE/D,IAAI,EAAE6B,UAAU,CAAC7B,IAAI;QAAEC,IAAI,EAAE4B,UAAU,CAAC5B;MAAK,CAAE;MACrGyI,YAAY,EAAE/B,gBAAiB;MAC/BgC,mBAAmB,EAAE7B;IAAwB;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CACJ,EAEApE,wBAAwB,iBACrB1C,OAAA,CAACpB,gBAAgB;MACbuE,kBAAkB,EAAEA,kBAAmB;MACvCxC,IAAI,EAAE0B,UAAU,CAAC1B,IAAK;MACtBoC,yBAAyB,EAAEA,yBAA0B;MACrDE,8BAA8B,EAAEA,8BAAgC;MAChEyC,IAAI,EAAEhD,wBAAyB;MAC/B0G,WAAW,EAAElC,iCAAkC;MAC/CmC,MAAM,EAAE7G,aAAc;MACtBgD,oBAAoB,EAAEA,oBAAqB;MAC3CvD,aAAa,EAAEA,aAAc;MAC7BV,OAAO,EAAEE;IAAY;MAAAkF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CACJ,eAED9G,OAAA,CAACf,cAAc;MACXqK,eAAe;MACfzE,IAAI,EAAExB,WAAa;MACnBkG,QAAQ,EAAE9F,eAAgB;MAC1B2F,WAAW,EAAEvB,yBAA0B;MACvCwB,MAAM,EAAE9F,aAAc;MACtBiG,SAAS,EAAEhG,gBAAiB;MAC5BiG,WAAW,EAAEtD;IAAgB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC;EAAA,eACJ,CAAC;AAEX,CAAC;AAAC1G,EAAA,CA/YID,OAAO;EAAA,QAE+BN,eAAe,EAgBpCC,SAAS,EAGX5B,cAAc;AAAA;AAAAwL,EAAA,GArB7BvJ,OAAO;AAiZb,eAAeA,OAAO;AAAC,IAAAuJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}