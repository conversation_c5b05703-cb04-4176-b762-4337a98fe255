{"ast": null, "code": "// materia-ui\nimport SpeakerNotesIcon from'@mui/icons-material/SpeakerNotes';import{IconButton,Stack,TableBody,TableCell,TableRow,Tooltip}from'@mui/material';// project imports\nimport{PERMISSIONS}from'constants/Permission';import{checkAllowedPermission}from'utils/authorization';import{formatTableCellProject}from'utils/common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const NonBillByMemberTBody=props=>{const{data,handleOpenCommentDialog}=props;const{nonBillable}=PERMISSIONS.report;return/*#__PURE__*/_jsx(TableBody,{children:data.map((item,key)=>{var _item$comment;return/*#__PURE__*/_jsxs(TableRow,{sx:{backgroundColor:item.color},children:[/*#__PURE__*/_jsx(TableCell,{children:key+1}),/*#__PURE__*/_jsx(TableCell,{children:item.id}),/*#__PURE__*/_jsx(TableCell,{children:item.firstName+' '+item.lastName}),/*#__PURE__*/_jsx(TableCell,{children:item.title}),/*#__PURE__*/_jsx(TableCell,{children:item.department}),/*#__PURE__*/_jsx(TableCell,{children:item.nonBillDivideTotal}),/*#__PURE__*/_jsx(TableCell,{children:item.projectList&&formatTableCellProject(item.projectList)}),/*#__PURE__*/_jsx(TableCell,{sx:{fontWeight:'700 !important'},children:item.notLogtimeYet}),/*#__PURE__*/_jsx(TableCell,{sx:{fontWeight:'700 !important'},children:item.billAbleProject}),checkAllowedPermission(nonBillable.commentDetail)&&/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Stack,{direction:\"row\",justifyContent:\"center\",alignItems:\"center\",children:/*#__PURE__*/_jsx(Tooltip,{placement:\"top\",title:(_item$comment=item.comment)===null||_item$comment===void 0?void 0:_item$comment.note,onClick:()=>handleOpenCommentDialog(item.userId,\"\".concat(item.firstName,\" \").concat(item.lastName)),children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"comment\",size:\"small\",children:/*#__PURE__*/_jsx(SpeakerNotesIcon,{sx:{fontSize:'1.1rem'}})})})})})]},key);})});};export default NonBillByMemberTBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}