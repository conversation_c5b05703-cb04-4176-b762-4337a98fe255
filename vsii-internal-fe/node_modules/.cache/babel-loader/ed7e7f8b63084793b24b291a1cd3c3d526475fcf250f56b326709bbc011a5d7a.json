{"ast": null, "code": "import { UNICODE_EXTENSION_SEQUENCE_REGEX, findBestMatch } from './utils';\n/**\n * https://tc39.es/ecma402/#sec-bestfitmatcher\n * @param availableLocales\n * @param requestedLocales\n * @param getDefaultLocale\n */\nexport function BestFitMatcher(availableLocales, requestedLocales, getDefaultLocale) {\n  var foundLocale;\n  var extension;\n  var noExtensionLocales = [];\n  var noExtensionLocaleMap = requestedLocales.reduce(function (all, l) {\n    var noExtensionLocale = l.replace(UNICODE_EXTENSION_SEQUENCE_REGEX, '');\n    noExtensionLocales.push(noExtensionLocale);\n    all[noExtensionLocale] = l;\n    return all;\n  }, {});\n  var result = findBestMatch(noExtensionLocales, availableLocales);\n  if (result.matchedSupportedLocale && result.matchedDesiredLocale) {\n    foundLocale = result.matchedSupportedLocale;\n    extension = noExtensionLocaleMap[result.matchedDesiredLocale].slice(result.matchedDesiredLocale.length) || undefined;\n  }\n  if (!foundLocale) {\n    return {\n      locale: getDefaultLocale()\n    };\n  }\n  return {\n    locale: foundLocale,\n    extension: extension\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}