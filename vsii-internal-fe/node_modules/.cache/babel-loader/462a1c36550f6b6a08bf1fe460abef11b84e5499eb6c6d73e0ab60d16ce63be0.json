{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/non-billable-monitoring/NonBillCostByWeekCard.tsx\",\n  _s = $RefreshSig$();\n// third-party\nimport Chart from 'react-apexcharts';\nimport { FormattedMessage, useIntl } from 'react-intl';\n\n// material-ui\nimport { useTheme } from '@mui/material/styles';\nimport { Grid, TableContainer, Table, TableHead, TableRow, TableCell, TableBody } from '@mui/material';\n\n// project imports\nimport MainCard from 'components/cards/MainCard';\nimport SkeletonSummaryCard from 'components/cards/Skeleton/SummaryCard';\nimport { gridSpacing } from 'store/constant';\nimport { formatPrice } from 'utils/common';\nimport { nonBillCostByWeekChartOption } from 'pages/non-billable-monitoring/Config';\n\n// =========================|| NonBill Cost By Week Card ||========================= //\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst NonBillCostByWeekCard = ({\n  data,\n  isLoading,\n  year\n}) => {\n  _s();\n  const theme = useTheme();\n  const intl = useIntl();\n  const newData = [...data.nonbillByWeekList].reverse();\n  const labels = newData && newData.map(item => item.week.substring(0, 7));\n  const colors = ['#4272df'];\n  const series = [{\n    name: `${intl.formatMessage({\n      id: 'budget-by-week'\n    })}`,\n    data: newData.map(value => value.budgetByWeek)\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: isLoading ? /*#__PURE__*/_jsxDEV(SkeletonSummaryCard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(MainCard, {\n      sx: {\n        marginBottom: theme.spacing(gridSpacing)\n      },\n      title: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n        id: \"NBM-ratio\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 84\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          lg: 5,\n          children: /*#__PURE__*/_jsxDEV(TableContainer, {\n            sx: {\n              maxHeight: 300\n            },\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              \"aria-label\": \"simple table\",\n              size: \"small\",\n              stickyHeader: true,\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n                      id: \"weeks\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 51,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 50,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n                      id: \"budget-by-week\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 54,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 53,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 49,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: data.nonbillByWeekList.map((item, key) => {\n                  return /*#__PURE__*/_jsxDEV(TableRow, {\n                    sx: {\n                      '&:last-child td, &:last-child th': {\n                        border: 0\n                      }\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: `${item.week}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 62,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"right\",\n                      children: formatPrice(item.budgetByWeek)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 63,\n                      columnNumber: 53\n                    }, this)]\n                  }, key, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 61,\n                    columnNumber: 49\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          lg: 7,\n          children: /*#__PURE__*/_jsxDEV(Chart, {\n            options: nonBillCostByWeekChartOption(labels, colors),\n            series: series,\n            type: \"bar\",\n            height: 450\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 17\n    }, this)\n  }, void 0, false);\n};\n_s(NonBillCostByWeekCard, \"gcd1iw4D0SRuv56FRuZKb4N6ifM=\", false, function () {\n  return [useTheme, useIntl];\n});\n_c = NonBillCostByWeekCard;\nexport default NonBillCostByWeekCard;\nvar _c;\n$RefreshReg$(_c, \"NonBillCostByWeekCard\");", "map": {"version": 3, "names": ["Chart", "FormattedMessage", "useIntl", "useTheme", "Grid", "TableContainer", "Table", "TableHead", "TableRow", "TableCell", "TableBody", "MainCard", "SkeletonSummaryCard", "gridSpacing", "formatPrice", "nonBillCostByWeekChartOption", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "NonBillCostByWeekCard", "data", "isLoading", "year", "_s", "theme", "intl", "newData", "nonbillByWeekList", "reverse", "labels", "map", "item", "week", "substring", "colors", "series", "name", "formatMessage", "id", "value", "budgetByWeek", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "marginBottom", "spacing", "title", "container", "xs", "lg", "maxHeight", "size", "<PERSON><PERSON><PERSON><PERSON>", "align", "key", "border", "options", "type", "height", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/non-billable-monitoring/NonBillCostByWeekCard.tsx"], "sourcesContent": ["// third-party\nimport Chart from 'react-apexcharts';\nimport { FormattedMessage, useIntl } from 'react-intl';\n\n// material-ui\nimport { useTheme } from '@mui/material/styles';\nimport { Grid, TableContainer, Table, TableHead, TableRow, TableCell, TableBody } from '@mui/material';\n\n// project imports\nimport MainCard from 'components/cards/MainCard';\nimport SkeletonSummaryCard from 'components/cards/Skeleton/SummaryCard';\nimport { gridSpacing } from 'store/constant';\nimport { INonBillByWeek, INonBillByWeekList } from 'types';\nimport { formatPrice } from 'utils/common';\nimport { nonBillCostByWeekChartOption } from 'pages/non-billable-monitoring/Config';\n\n// =========================|| NonBill Cost By Week Card ||========================= //\n\ninterface INonBillCostByWeekCardProps {\n    data: INonBillByWeekList;\n    isLoading: boolean;\n    year: string | number;\n}\n\nconst NonBillCostByWeekCard = ({ data, isLoading, year }: INonBillCostByWeekCardProps) => {\n    const theme = useTheme();\n    const intl = useIntl();\n    const newData = [...data.nonbillByWeekList].reverse();\n    const labels = newData && newData.map((item) => item.week.substring(0, 7));\n    const colors = ['#4272df'];\n    const series = [\n        {\n            name: `${intl.formatMessage({ id: 'budget-by-week' })}`,\n            data: newData.map((value) => value.budgetByWeek)\n        }\n    ];\n\n    return (\n        <>\n            {isLoading ? (\n                <SkeletonSummaryCard />\n            ) : (\n                <MainCard sx={{ marginBottom: theme.spacing(gridSpacing) }} title={<FormattedMessage id=\"NBM-ratio\" />}>\n                    <Grid container spacing={2}>\n                        <Grid item xs={12} lg={5}>\n                            <TableContainer sx={{ maxHeight: 300 }}>\n                                <Table aria-label=\"simple table\" size=\"small\" stickyHeader>\n                                    <TableHead>\n                                        <TableRow>\n                                            <TableCell>\n                                                <FormattedMessage id=\"weeks\" />\n                                            </TableCell>\n                                            <TableCell align=\"right\">\n                                                <FormattedMessage id=\"budget-by-week\" />\n                                            </TableCell>\n                                        </TableRow>\n                                    </TableHead>\n                                    <TableBody>\n                                        {data.nonbillByWeekList.map((item: INonBillByWeek, key) => {\n                                            return (\n                                                <TableRow key={key} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>\n                                                    <TableCell>{`${item.week}`}</TableCell>\n                                                    <TableCell align=\"right\">{formatPrice(item.budgetByWeek)}</TableCell>\n                                                </TableRow>\n                                            );\n                                        })}\n                                    </TableBody>\n                                </Table>\n                            </TableContainer>\n                        </Grid>\n                        <Grid item xs={12} lg={7}>\n                            <Chart options={nonBillCostByWeekChartOption(labels, colors)} series={series} type=\"bar\" height={450} />\n                        </Grid>\n                    </Grid>\n                </MainCard>\n            )}\n        </>\n    );\n};\n\nexport default NonBillCostByWeekCard;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,MAAM,kBAAkB;AACpC,SAASC,gBAAgB,EAAEC,OAAO,QAAQ,YAAY;;AAEtD;AACA,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,IAAI,EAAEC,cAAc,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,QAAQ,eAAe;;AAEtG;AACA,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,mBAAmB,MAAM,uCAAuC;AACvE,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,WAAW,QAAQ,cAAc;AAC1C,SAASC,4BAA4B,QAAQ,sCAAsC;;AAEnF;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQA,MAAMC,qBAAqB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,SAAS;EAAEC;AAAkC,CAAC,KAAK;EAAAC,EAAA;EACtF,MAAMC,KAAK,GAAGtB,QAAQ,CAAC,CAAC;EACxB,MAAMuB,IAAI,GAAGxB,OAAO,CAAC,CAAC;EACtB,MAAMyB,OAAO,GAAG,CAAC,GAAGN,IAAI,CAACO,iBAAiB,CAAC,CAACC,OAAO,CAAC,CAAC;EACrD,MAAMC,MAAM,GAAGH,OAAO,IAAIA,OAAO,CAACI,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC1E,MAAMC,MAAM,GAAG,CAAC,SAAS,CAAC;EAC1B,MAAMC,MAAM,GAAG,CACX;IACIC,IAAI,EAAE,GAAGX,IAAI,CAACY,aAAa,CAAC;MAAEC,EAAE,EAAE;IAAiB,CAAC,CAAC,EAAE;IACvDlB,IAAI,EAAEM,OAAO,CAACI,GAAG,CAAES,KAAK,IAAKA,KAAK,CAACC,YAAY;EACnD,CAAC,CACJ;EAED,oBACIxB,OAAA,CAAAE,SAAA;IAAAuB,QAAA,EACKpB,SAAS,gBACNL,OAAA,CAACL,mBAAmB;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAEvB7B,OAAA,CAACN,QAAQ;MAACoC,EAAE,EAAE;QAAEC,YAAY,EAAEvB,KAAK,CAACwB,OAAO,CAACpC,WAAW;MAAE,CAAE;MAACqC,KAAK,eAAEjC,OAAA,CAAChB,gBAAgB;QAACsC,EAAE,EAAC;MAAW;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAAAJ,QAAA,eACnGzB,OAAA,CAACb,IAAI;QAAC+C,SAAS;QAACF,OAAO,EAAE,CAAE;QAAAP,QAAA,gBACvBzB,OAAA,CAACb,IAAI;UAAC4B,IAAI;UAACoB,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eACrBzB,OAAA,CAACZ,cAAc;YAAC0C,EAAE,EAAE;cAAEO,SAAS,EAAE;YAAI,CAAE;YAAAZ,QAAA,eACnCzB,OAAA,CAACX,KAAK;cAAC,cAAW,cAAc;cAACiD,IAAI,EAAC,OAAO;cAACC,YAAY;cAAAd,QAAA,gBACtDzB,OAAA,CAACV,SAAS;gBAAAmC,QAAA,eACNzB,OAAA,CAACT,QAAQ;kBAAAkC,QAAA,gBACLzB,OAAA,CAACR,SAAS;oBAAAiC,QAAA,eACNzB,OAAA,CAAChB,gBAAgB;sBAACsC,EAAE,EAAC;oBAAO;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACZ7B,OAAA,CAACR,SAAS;oBAACgD,KAAK,EAAC,OAAO;oBAAAf,QAAA,eACpBzB,OAAA,CAAChB,gBAAgB;sBAACsC,EAAE,EAAC;oBAAgB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZ7B,OAAA,CAACP,SAAS;gBAAAgC,QAAA,EACLrB,IAAI,CAACO,iBAAiB,CAACG,GAAG,CAAC,CAACC,IAAoB,EAAE0B,GAAG,KAAK;kBACvD,oBACIzC,OAAA,CAACT,QAAQ;oBAAWuC,EAAE,EAAE;sBAAE,kCAAkC,EAAE;wBAAEY,MAAM,EAAE;sBAAE;oBAAE,CAAE;oBAAAjB,QAAA,gBAC1EzB,OAAA,CAACR,SAAS;sBAAAiC,QAAA,EAAE,GAAGV,IAAI,CAACC,IAAI;oBAAE;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACvC7B,OAAA,CAACR,SAAS;sBAACgD,KAAK,EAAC,OAAO;sBAAAf,QAAA,EAAE5B,WAAW,CAACkB,IAAI,CAACS,YAAY;oBAAC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA,GAF1DY,GAAG;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGR,CAAC;gBAEnB,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACP7B,OAAA,CAACb,IAAI;UAAC4B,IAAI;UAACoB,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eACrBzB,OAAA,CAACjB,KAAK;YAAC4D,OAAO,EAAE7C,4BAA4B,CAACe,MAAM,EAAEK,MAAM,CAAE;YAACC,MAAM,EAAEA,MAAO;YAACyB,IAAI,EAAC,KAAK;YAACC,MAAM,EAAE;UAAI;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EACb,gBACH,CAAC;AAEX,CAAC;AAACtB,EAAA,CAtDIJ,qBAAqB;EAAA,QACTjB,QAAQ,EACTD,OAAO;AAAA;AAAA6D,EAAA,GAFlB3C,qBAAqB;AAwD3B,eAAeA,qBAAqB;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}