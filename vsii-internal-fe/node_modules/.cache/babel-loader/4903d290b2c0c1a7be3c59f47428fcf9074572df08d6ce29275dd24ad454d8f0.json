{"ast": null, "code": "/**\n * https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * Credit: https://github.com/caridy/intl-datetimeformat-pattern/blob/master/index.js\n * with some tweaks\n */\nvar DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;\n/**\n * Parse Date time skeleton into Intl.DateTimeFormatOptions\n * Ref: https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * @public\n * @param skeleton skeleton string\n */\nexport function parseDateTimeSkeleton(skeleton) {\n  var result = {};\n  skeleton.replace(DATE_TIME_REGEX, function (match) {\n    var len = match.length;\n    switch (match[0]) {\n      // Era\n      case 'G':\n        result.era = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n        break;\n      // Year\n      case 'y':\n        result.year = len === 2 ? '2-digit' : 'numeric';\n        break;\n      case 'Y':\n      case 'u':\n      case 'U':\n      case 'r':\n        throw new RangeError('`Y/u/U/r` (year) patterns are not supported, use `y` instead');\n      // Quarter\n      case 'q':\n      case 'Q':\n        throw new RangeError('`q/Q` (quarter) patterns are not supported');\n      // Month\n      case 'M':\n      case 'L':\n        result.month = ['numeric', '2-digit', 'short', 'long', 'narrow'][len - 1];\n        break;\n      // Week\n      case 'w':\n      case 'W':\n        throw new RangeError('`w/W` (week) patterns are not supported');\n      case 'd':\n        result.day = ['numeric', '2-digit'][len - 1];\n        break;\n      case 'D':\n      case 'F':\n      case 'g':\n        throw new RangeError('`D/F/g` (day) patterns are not supported, use `d` instead');\n      // Weekday\n      case 'E':\n        result.weekday = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n        break;\n      case 'e':\n        if (len < 4) {\n          throw new RangeError('`e..eee` (weekday) patterns are not supported');\n        }\n        result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n        break;\n      case 'c':\n        if (len < 4) {\n          throw new RangeError('`c..ccc` (weekday) patterns are not supported');\n        }\n        result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n        break;\n      // Period\n      case 'a':\n        // AM, PM\n        result.hour12 = true;\n        break;\n      case 'b': // am, pm, noon, midnight\n      case 'B':\n        // flexible day periods\n        throw new RangeError('`b/B` (period) patterns are not supported, use `a` instead');\n      // Hour\n      case 'h':\n        result.hourCycle = 'h12';\n        result.hour = ['numeric', '2-digit'][len - 1];\n        break;\n      case 'H':\n        result.hourCycle = 'h23';\n        result.hour = ['numeric', '2-digit'][len - 1];\n        break;\n      case 'K':\n        result.hourCycle = 'h11';\n        result.hour = ['numeric', '2-digit'][len - 1];\n        break;\n      case 'k':\n        result.hourCycle = 'h24';\n        result.hour = ['numeric', '2-digit'][len - 1];\n        break;\n      case 'j':\n      case 'J':\n      case 'C':\n        throw new RangeError('`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead');\n      // Minute\n      case 'm':\n        result.minute = ['numeric', '2-digit'][len - 1];\n        break;\n      // Second\n      case 's':\n        result.second = ['numeric', '2-digit'][len - 1];\n        break;\n      case 'S':\n      case 'A':\n        throw new RangeError('`S/A` (second) patterns are not supported, use `s` instead');\n      // Zone\n      case 'z':\n        // 1..3, 4: specific non-location format\n        result.timeZoneName = len < 4 ? 'short' : 'long';\n        break;\n      case 'Z': // 1..3, 4, 5: The ISO8601 varios formats\n      case 'O': // 1, 4: milliseconds in day short, long\n      case 'v': // 1, 4: generic non-location format\n      case 'V': // 1, 2, 3, 4: time zone ID or city\n      case 'X': // 1, 2, 3, 4: The ISO8601 varios formats\n      case 'x':\n        // 1, 2, 3, 4: The ISO8601 varios formats\n        throw new RangeError('`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead');\n    }\n    return '';\n  });\n  return result;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}