{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/FieldsOnboardTableTHead.tsx\";\nimport { FormattedMessage } from 'react-intl';\n\n//mui import\nimport { TableCell, TableHead, TableRow } from '@mui/material';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FieldsOnboardHistoryTableTHead = props => {\n  const {\n    length\n  } = props;\n  const {\n    manage_user\n  } = TEXT_CONFIG_SCREEN.administration;\n  return /*#__PURE__*/_jsxDEV(TableHead, {\n    children: /*#__PURE__*/_jsxDEV(TableRow, {\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: manage_user + 'no'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: manage_user + 'contractor'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: [/*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: manage_user + 'onboard-date'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"MuiInputLabel-asterisk\",\n          children: \" *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: manage_user + 'outboard-date'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: manage_user + 'official-business-day'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this), length > 0 && /*#__PURE__*/_jsxDEV(TableCell, {\n        align: \"center\",\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: manage_user + 'action'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 9\n  }, this);\n};\n_c = FieldsOnboardHistoryTableTHead;\nexport default FieldsOnboardHistoryTableTHead;\nvar _c;\n$RefreshReg$(_c, \"FieldsOnboardHistoryTableTHead\");", "map": {"version": 3, "names": ["FormattedMessage", "TableCell", "TableHead", "TableRow", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "FieldsOnboardHistoryTableTHead", "props", "length", "manage_user", "administration", "children", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "align", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/administration/FieldsOnboardTableTHead.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\n\n//mui import\nimport { TableCell, TableHead, TableRow } from '@mui/material';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\n\ninterface IFieldsOnboardHistoryTableTHeadProps {\n    length: number;\n}\n\nconst FieldsOnboardHistoryTableTHead = (props: IFieldsOnboardHistoryTableTHeadProps) => {\n    const { length } = props;\n    const { manage_user } = TEXT_CONFIG_SCREEN.administration;\n\n    return (\n        <TableHead>\n            <TableRow>\n                <TableCell>\n                    <FormattedMessage id={manage_user + 'no'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={manage_user + 'contractor'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={manage_user + 'onboard-date'} />\n                    <span className=\"MuiInputLabel-asterisk\"> *</span>\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={manage_user + 'outboard-date'} />\n                </TableCell>\n                <TableCell>\n                    <FormattedMessage id={manage_user + 'official-business-day'} />\n                </TableCell>\n                {length > 0 && (\n                    <TableCell align=\"center\">\n                        <FormattedMessage id={manage_user + 'action'} />\n                    </TableCell>\n                )}\n            </TableRow>\n        </TableHead>\n    );\n};\n\nexport default FieldsOnboardHistoryTableTHead;\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;;AAE7C;AACA,SAASC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC9D,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMtD,MAAMC,8BAA8B,GAAIC,KAA2C,IAAK;EACpF,MAAM;IAAEC;EAAO,CAAC,GAAGD,KAAK;EACxB,MAAM;IAAEE;EAAY,CAAC,GAAGN,kBAAkB,CAACO,cAAc;EAEzD,oBACIL,OAAA,CAACJ,SAAS;IAAAU,QAAA,eACNN,OAAA,CAACH,QAAQ;MAAAS,QAAA,gBACLN,OAAA,CAACL,SAAS;QAAAW,QAAA,eACNN,OAAA,CAACN,gBAAgB;UAACa,EAAE,EAAEH,WAAW,GAAG;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACZX,OAAA,CAACL,SAAS;QAAAW,QAAA,eACNN,OAAA,CAACN,gBAAgB;UAACa,EAAE,EAAEH,WAAW,GAAG;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eACZX,OAAA,CAACL,SAAS;QAAAW,QAAA,gBACNN,OAAA,CAACN,gBAAgB;UAACa,EAAE,EAAEH,WAAW,GAAG;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDX,OAAA;UAAMY,SAAS,EAAC,wBAAwB;UAAAN,QAAA,EAAC;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACZX,OAAA,CAACL,SAAS;QAAAW,QAAA,eACNN,OAAA,CAACN,gBAAgB;UAACa,EAAE,EAAEH,WAAW,GAAG;QAAgB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACZX,OAAA,CAACL,SAAS;QAAAW,QAAA,eACNN,OAAA,CAACN,gBAAgB;UAACa,EAAE,EAAEH,WAAW,GAAG;QAAwB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,EACXR,MAAM,GAAG,CAAC,iBACPH,OAAA,CAACL,SAAS;QAACkB,KAAK,EAAC,QAAQ;QAAAP,QAAA,eACrBN,OAAA,CAACN,gBAAgB;UAACa,EAAE,EAAEH,WAAW,GAAG;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CACd;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEpB,CAAC;AAACG,EAAA,GA/BIb,8BAA8B;AAiCpC,eAAeA,8BAA8B;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}