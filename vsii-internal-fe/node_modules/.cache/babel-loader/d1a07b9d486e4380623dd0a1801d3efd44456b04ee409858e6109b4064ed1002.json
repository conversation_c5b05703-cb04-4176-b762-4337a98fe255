{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/icons/MonthlyEffort.tsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MonthlyEffort = () => {\n  return /*#__PURE__*/_jsxDEV(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 20,\n    height: 20,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M8 2V5\",\n      stroke: \"#292D32\",\n      strokeWidth: \"1.5\",\n      strokeMiterlimit: 10,\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 4,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M16 2V5\",\n      stroke: \"#292D32\",\n      strokeWidth: \"1.5\",\n      strokeMiterlimit: 10,\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M3.5 9.09H20.5\",\n      stroke: \"#292D32\",\n      strokeWidth: \"1.5\",\n      strokeMiterlimit: 10,\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M21 8.5V17C21 20 19.5 22 16 22H8C4.5 22 3 20 3 17V8.5C3 5.5 4.5 3.5 8 3.5H16C19.5 3.5 21 5.5 21 8.5Z\",\n      stroke: \"#292D32\",\n      strokeWidth: \"1.5\",\n      strokeMiterlimit: 10,\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M15.6947 13.7H15.7037\",\n      stroke: \"#292D32\",\n      strokeWidth: 2,\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M15.6947 16.7H15.7037\",\n      stroke: \"#292D32\",\n      strokeWidth: 2,\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M11.9955 13.7H12.0045\",\n      stroke: \"#292D32\",\n      strokeWidth: 2,\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M11.9955 16.7H12.0045\",\n      stroke: \"#292D32\",\n      strokeWidth: 2,\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M8.29431 13.7H8.30329\",\n      stroke: \"#292D32\",\n      strokeWidth: 2,\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n      d: \"M8.29431 16.7H8.30329\",\n      stroke: \"#292D32\",\n      strokeWidth: 2,\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 3,\n    columnNumber: 9\n  }, this);\n};\n_c = MonthlyEffort;\nexport default MonthlyEffort;\nvar _c;\n$RefreshReg$(_c, \"MonthlyEffort\");", "map": {"version": 3, "names": ["MonthlyEffort", "_jsxDEV", "xmlns", "width", "height", "viewBox", "fill", "children", "d", "stroke", "strokeWidth", "strokeMiterlimit", "strokeLinecap", "strokeLinejoin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/icons/MonthlyEffort.tsx"], "sourcesContent": ["const MonthlyEffort = () => {\n    return (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width={20} height={20} viewBox=\"0 0 24 24\" fill=\"none\">\n            <path d=\"M8 2V5\" stroke=\"#292D32\" strokeWidth=\"1.5\" strokeMiterlimit={10} strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n            <path d=\"M16 2V5\" stroke=\"#292D32\" strokeWidth=\"1.5\" strokeMiterlimit={10} strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n            <path\n                d=\"M3.5 9.09H20.5\"\n                stroke=\"#292D32\"\n                strokeWidth=\"1.5\"\n                strokeMiterlimit={10}\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n            <path\n                d=\"M21 8.5V17C21 20 19.5 22 16 22H8C4.5 22 3 20 3 17V8.5C3 5.5 4.5 3.5 8 3.5H16C19.5 3.5 21 5.5 21 8.5Z\"\n                stroke=\"#292D32\"\n                strokeWidth=\"1.5\"\n                strokeMiterlimit={10}\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n            />\n            <path d=\"M15.6947 13.7H15.7037\" stroke=\"#292D32\" strokeWidth={2} strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n            <path d=\"M15.6947 16.7H15.7037\" stroke=\"#292D32\" strokeWidth={2} strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n            <path d=\"M11.9955 13.7H12.0045\" stroke=\"#292D32\" strokeWidth={2} strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n            <path d=\"M11.9955 16.7H12.0045\" stroke=\"#292D32\" strokeWidth={2} strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n            <path d=\"M8.29431 13.7H8.30329\" stroke=\"#292D32\" strokeWidth={2} strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n            <path d=\"M8.29431 16.7H8.30329\" stroke=\"#292D32\" strokeWidth={2} strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n        </svg>\n    );\n};\n\nexport default MonthlyEffort;\n"], "mappings": ";;AAAA,MAAMA,aAAa,GAAGA,CAAA,KAAM;EACxB,oBACIC,OAAA;IAAKC,KAAK,EAAC,4BAA4B;IAACC,KAAK,EAAE,EAAG;IAACC,MAAM,EAAE,EAAG;IAACC,OAAO,EAAC,WAAW;IAACC,IAAI,EAAC,MAAM;IAAAC,QAAA,gBAC1FN,OAAA;MAAMO,CAAC,EAAC,QAAQ;MAACC,MAAM,EAAC,SAAS;MAACC,WAAW,EAAC,KAAK;MAACC,gBAAgB,EAAE,EAAG;MAACC,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzHhB,OAAA;MAAMO,CAAC,EAAC,SAAS;MAACC,MAAM,EAAC,SAAS;MAACC,WAAW,EAAC,KAAK;MAACC,gBAAgB,EAAE,EAAG;MAACC,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1HhB,OAAA;MACIO,CAAC,EAAC,gBAAgB;MAClBC,MAAM,EAAC,SAAS;MAChBC,WAAW,EAAC,KAAK;MACjBC,gBAAgB,EAAE,EAAG;MACrBC,aAAa,EAAC,OAAO;MACrBC,cAAc,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eACFhB,OAAA;MACIO,CAAC,EAAC,sGAAsG;MACxGC,MAAM,EAAC,SAAS;MAChBC,WAAW,EAAC,KAAK;MACjBC,gBAAgB,EAAE,EAAG;MACrBC,aAAa,EAAC,OAAO;MACrBC,cAAc,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eACFhB,OAAA;MAAMO,CAAC,EAAC,uBAAuB;MAACC,MAAM,EAAC,SAAS;MAACC,WAAW,EAAE,CAAE;MAACE,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChHhB,OAAA;MAAMO,CAAC,EAAC,uBAAuB;MAACC,MAAM,EAAC,SAAS;MAACC,WAAW,EAAE,CAAE;MAACE,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChHhB,OAAA;MAAMO,CAAC,EAAC,uBAAuB;MAACC,MAAM,EAAC,SAAS;MAACC,WAAW,EAAE,CAAE;MAACE,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChHhB,OAAA;MAAMO,CAAC,EAAC,uBAAuB;MAACC,MAAM,EAAC,SAAS;MAACC,WAAW,EAAE,CAAE;MAACE,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChHhB,OAAA;MAAMO,CAAC,EAAC,uBAAuB;MAACC,MAAM,EAAC,SAAS;MAACC,WAAW,EAAE,CAAE;MAACE,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChHhB,OAAA;MAAMO,CAAC,EAAC,uBAAuB;MAACC,MAAM,EAAC,SAAS;MAACC,WAAW,EAAE,CAAE;MAACE,aAAa,EAAC,OAAO;MAACC,cAAc,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/G,CAAC;AAEd,CAAC;AAACC,EAAA,GA7BIlB,aAAa;AA+BnB,eAAeA,aAAa;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}