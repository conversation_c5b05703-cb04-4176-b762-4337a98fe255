{"ast": null, "code": "import PropTypes from 'prop-types';\nimport chainPropTypes from '../chainPropTypes';\nfunction isClassComponent(elementType) {\n  // elementType.prototype?.isReactComponent\n  const {\n    prototype = {}\n  } = elementType;\n  return Boolean(prototype.isReactComponent);\n}\nfunction elementTypeAcceptingRef(props, propName, componentName, location, propFullName) {\n  const propValue = props[propName];\n  const safePropName = propFullName || propName;\n  if (propValue == null ||\n  // When server-side rendering React doesn't warn either.\n  // This is not an accurate check for SSR.\n  // This is only in place for emotion compat.\n  // TODO: Revisit once https://github.com/facebook/react/issues/20047 is resolved.\n  typeof window === 'undefined') {\n    return null;\n  }\n  let warningHint;\n\n  /**\n   * Blacklisting instead of whitelisting\n   *\n   * Blacklisting will miss some components, such as React.Fragment. Those will at least\n   * trigger a warning in React.\n   * We can't whitelist because there is no safe way to detect React.forwardRef\n   * or class components. \"Safe\" means there's no public API.\n   *\n   */\n  if (typeof propValue === 'function' && !isClassComponent(propValue)) {\n    warningHint = 'Did you accidentally provide a plain function component instead?';\n  }\n  if (warningHint !== undefined) {\n    return new Error(\"Invalid \".concat(location, \" `\").concat(safePropName, \"` supplied to `\").concat(componentName, \"`. \") + \"Expected an element type that can hold a ref. \".concat(warningHint, \" \") + 'For more information see https://mui.com/r/caveat-with-refs-guide');\n  }\n  return null;\n}\nexport default chainPropTypes(PropTypes.elementType, elementTypeAcceptingRef);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}