{"ast": null, "code": "// material-ui\nimport{Grid,TableBody,Typography}from'@mui/material';import{FormattedMessage}from'react-intl';//project import\nimport BacklogDetailThead from'./BacklogDetailThead';import BacklogDetailTBody from'./BacklogDetailTbody';import{Table}from'components/extended/Table';import{gridSpacing}from'store/constant';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const BacklogDetail=props=>{const{data}=props;return/*#__PURE__*/_jsxs(Grid,{container:true,spacing:gridSpacing,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Typography,{variant:\"h3\",sx:{textDecoration:'underline',my:'10px'},children:data.requireName})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sx:{my:'15px'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"product-report.modal.rightSide.backlog.description\"})}),/*#__PURE__*/_jsx(Typography,{component:\"pre\",sx:{mt:'10px'},children:data.desc})]}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Table,{heads:/*#__PURE__*/_jsx(BacklogDetailThead,{}),data:data.tasks,sx:{maxHeight:'50vh'},heightTableEmpty:\"200px\",borderedEmpty:false,children:/*#__PURE__*/_jsx(TableBody,{children:data.tasks.map((data,index)=>/*#__PURE__*/_jsx(BacklogDetailTBody,{data:data},index))})})})]});};export default BacklogDetail;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}