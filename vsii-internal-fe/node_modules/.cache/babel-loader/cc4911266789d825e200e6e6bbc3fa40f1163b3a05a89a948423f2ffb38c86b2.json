{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '@mui/base';\nexport function getTimelineContentUtilityClass(slot) {\n  return generateUtilityClass('MuiTimelineContent', slot);\n}\nconst timelineContentClasses = generateUtilityClasses('MuiTimelineContent', ['root', 'positionLeft', 'positionRight', 'positionAlternate']);\nexport default timelineContentClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}