{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nfunction splitItems(maxItems, items, createId) {\n  var slicedItems = sliceIntoItems(maxItems, items);\n  return slicedItems.map(mapToChunk(createId));\n}\nexports.splitItems = splitItems;\nfunction sliceIntoItems(maxItems, items) {\n  var numberOfSlices = Math.ceil(items.length / maxItems);\n  var sliceIndexes = Array.apply(null, Array(numberOfSlices)).map(function (_, index) {\n    return index;\n  });\n  return sliceIndexes.map(function (index) {\n    return items.slice(index * maxItems, index * maxItems + maxItems);\n  });\n}\nfunction mapToChunk(createId) {\n  return function (items) {\n    return {\n      id: createId(),\n      items: items\n    };\n  };\n}", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}