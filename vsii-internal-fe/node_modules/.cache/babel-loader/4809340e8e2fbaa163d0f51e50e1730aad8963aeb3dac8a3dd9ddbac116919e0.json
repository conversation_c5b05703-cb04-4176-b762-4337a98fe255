{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useForkRef } from '@mui/material/utils';\nimport { WrapperVariantContext } from './WrapperVariantContext';\nimport { PickersPopper } from '../PickersPopper';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport function DesktopWrapper(props) {\n  const {\n    children,\n    DateInputProps,\n    KeyboardDateInputComponent,\n    onClear,\n    onDismiss,\n    onCancel,\n    onAccept,\n    onSetToday,\n    open,\n    PopperProps,\n    PaperProps,\n    TransitionComponent,\n    components,\n    componentsProps\n  } = props;\n  const ownInputRef = React.useRef(null);\n  const inputRef = useForkRef(DateInputProps.inputRef, ownInputRef);\n  return /*#__PURE__*/_jsxs(WrapperVariantContext.Provider, {\n    value: \"desktop\",\n    children: [/*#__PURE__*/_jsx(KeyboardDateInputComponent, _extends({}, DateInputProps, {\n      inputRef: inputRef\n    })), /*#__PURE__*/_jsx(PickersPopper, {\n      role: \"dialog\",\n      open: open,\n      anchorEl: ownInputRef.current,\n      TransitionComponent: TransitionComponent,\n      PopperProps: PopperProps,\n      PaperProps: PaperProps,\n      onClose: onDismiss,\n      onCancel: onCancel,\n      onClear: onClear,\n      onAccept: onAccept,\n      onSetToday: onSetToday,\n      components: components,\n      componentsProps: componentsProps,\n      children: children\n    })]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}