{"ast": null, "code": "import React,{useEffect,useState}from'react';// projec import\nimport{EditSystemConfig,SystemConfigTBody,SystemConfigThead}from'containers/administration';import MainCard from'components/cards/MainCard';import{Table,TableFooter}from'components/extended/Table';import{SEARCH_PARAM_KEY,paginationParamDefault,paginationResponseDefault}from'constants/Common';import sendRequest from'services/ApiService';import Api from'constants/Api';import{configFormDefault}from'./Config';import{openSnackbar}from'store/slice/snackbarSlice';import{useAppDispatch}from'app/hooks';import{checkAllowedPermission}from'utils/authorization';import{PERMISSIONS}from'constants/Permission';import{getSearchParam,transformObject}from'utils/common';// third party\nimport{useSearchParams}from'react-router-dom';// ==============================|| System Config ||============================== //\n/**\n *  page\n *  size\n */import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SystemConfig=()=>{// URL Params\nconst[searchParams,setSearchParams]=useSearchParams();const keyParams=[SEARCH_PARAM_KEY.page,SEARCH_PARAM_KEY.size];const params=getSearchParam(keyParams,searchParams);transformObject(params);// Hooks, State, Variable\nconst[loading,setLoading]=useState(false);const dispatch=useAppDispatch();const[open,setOpen]=useState(false);const[editLoading,setEditLoading]=useState(false);const[conditions,setConditions]=useState({...paginationParamDefault,...params});const[paginationResponse,setPaginationResponse]=useState({...paginationResponseDefault,pageNumber:params.page?params.page:paginationResponseDefault.pageNumber,pageSize:params.size?params.size:paginationResponseDefault.pageSize});const[systemConfigs,setSystemConfigs]=useState([]);const[systemConfig,setSystemConfig]=useState(configFormDefault);const{systemConfigPermission}=PERMISSIONS.admin;const getDataTable=async()=>{setLoading(true);const response=await sendRequest(Api.system_config.getAll,{...conditions,page:conditions.page+1});if(response){const{status,result}=response;if(status){const{content,pagination}=result;setPaginationResponse({...paginationResponse,totalElement:pagination===null||pagination===void 0?void 0:pagination.totalElement});setSystemConfigs(Array.isArray(content)?content:[]);setLoading(false);}else{setDataEmpty();}return;}else{setDataEmpty();}};const postEditConfig=async valueConfig=>{setEditLoading(true);const response=await sendRequest(Api.system_config.postUpdateConfig,valueConfig);if(response){dispatch(openSnackbar({open:true,message:'update-success',variant:'alert',alert:{color:'success'}}));setEditLoading(false);getDataTable();setOpen(false);}};const handleOpenDialog=item=>{setSystemConfig(item?{...item,key:item.key?item.key:'',value:item.value?item.value:'',note:item.note?item.note:''}:configFormDefault);setOpen(true);};const setDataEmpty=()=>{setSystemConfigs([]);setLoading(false);};// Event\nconst handleChangePage=(event,newPage)=>{setConditions({...conditions,page:newPage});setSearchParams({...params,page:newPage});};const handleChangeRowsPerPage=event=>{setConditions({...conditions,page:paginationParamDefault.page,size:parseInt(event.target.value,10)});setSearchParams({...params,page:paginationParamDefault.page,size:parseInt(event.target.value,10)});};const handleCloseDialog=()=>{setOpen(false);};const handleEditConfig=configEdit=>{postEditConfig(configEdit);};useEffect(()=>{getDataTable();// eslint-disable-next-line react-hooks/exhaustive-deps\n},[]);return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(MainCard,{children:/*#__PURE__*/_jsx(Table,{heads:/*#__PURE__*/_jsx(SystemConfigThead,{}),isLoading:loading,data:systemConfigs,children:/*#__PURE__*/_jsx(SystemConfigTBody,{page:conditions.page,size:conditions.size,systemConfigs:systemConfigs,handleOpen:handleOpenDialog})})}),!loading&&/*#__PURE__*/_jsx(TableFooter,{pagination:{total:paginationResponse.totalElement,page:conditions.page,size:conditions.size},onPageChange:handleChangePage,onRowsPerPageChange:handleChangeRowsPerPage}),checkAllowedPermission(systemConfigPermission.edit)&&/*#__PURE__*/_jsx(EditSystemConfig,{open:open,loading:editLoading,systemConfig:systemConfig,handleClose:handleCloseDialog,editSystemConfig:handleEditConfig})]});};export default SystemConfig;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}