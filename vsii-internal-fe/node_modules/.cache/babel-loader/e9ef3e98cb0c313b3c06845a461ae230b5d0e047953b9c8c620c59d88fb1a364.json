{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"absolute\", \"children\", \"className\", \"component\", \"flexItem\", \"light\", \"orientation\", \"role\", \"textAlign\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getDividerUtilityClass } from './dividerClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    absolute,\n    children,\n    classes,\n    flexItem,\n    light,\n    orientation,\n    textAlign,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', absolute && 'absolute', variant, light && 'light', orientation === 'vertical' && 'vertical', flexItem && 'flexItem', children && 'withChildren', children && orientation === 'vertical' && 'withChildrenVertical', textAlign === 'right' && orientation !== 'vertical' && 'textAlignRight', textAlign === 'left' && orientation !== 'vertical' && 'textAlignLeft'],\n    wrapper: ['wrapper', orientation === 'vertical' && 'wrapperVertical']\n  };\n  return composeClasses(slots, getDividerUtilityClass, classes);\n};\nconst DividerRoot = styled('div', {\n  name: 'MuiDivider',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.absolute && styles.absolute, styles[ownerState.variant], ownerState.light && styles.light, ownerState.orientation === 'vertical' && styles.vertical, ownerState.flexItem && styles.flexItem, ownerState.children && styles.withChildren, ownerState.children && ownerState.orientation === 'vertical' && styles.withChildrenVertical, ownerState.textAlign === 'right' && ownerState.orientation !== 'vertical' && styles.textAlignRight, ownerState.textAlign === 'left' && ownerState.orientation !== 'vertical' && styles.textAlignLeft];\n  }\n})(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  return _extends({\n    margin: 0,\n    // Reset browser default style.\n    flexShrink: 0,\n    borderWidth: 0,\n    borderStyle: 'solid',\n    borderColor: (theme.vars || theme).palette.divider,\n    borderBottomWidth: 'thin'\n  }, ownerState.absolute && {\n    position: 'absolute',\n    bottom: 0,\n    left: 0,\n    width: '100%'\n  }, ownerState.light && {\n    borderColor: theme.vars ? \"rgba(\".concat(theme.vars.palette.dividerChannel, \" / 0.08)\") : alpha(theme.palette.divider, 0.08)\n  }, ownerState.variant === 'inset' && {\n    marginLeft: 72\n  }, ownerState.variant === 'middle' && ownerState.orientation === 'horizontal' && {\n    marginLeft: theme.spacing(2),\n    marginRight: theme.spacing(2)\n  }, ownerState.variant === 'middle' && ownerState.orientation === 'vertical' && {\n    marginTop: theme.spacing(1),\n    marginBottom: theme.spacing(1)\n  }, ownerState.orientation === 'vertical' && {\n    height: '100%',\n    borderBottomWidth: 0,\n    borderRightWidth: 'thin'\n  }, ownerState.flexItem && {\n    alignSelf: 'stretch',\n    height: 'auto'\n  });\n}, _ref2 => {\n  let {\n    ownerState\n  } = _ref2;\n  return _extends({}, ownerState.children && {\n    display: 'flex',\n    whiteSpace: 'nowrap',\n    textAlign: 'center',\n    border: 0,\n    borderTopStyle: 'solid',\n    borderLeftStyle: 'solid',\n    '&::before, &::after': {\n      content: '\"\"',\n      alignSelf: 'center'\n    }\n  });\n}, _ref3 => {\n  let {\n    theme,\n    ownerState\n  } = _ref3;\n  return _extends({}, ownerState.children && ownerState.orientation !== 'vertical' && {\n    '&::before, &::after': {\n      width: '100%',\n      borderTop: \"thin solid \".concat((theme.vars || theme).palette.divider),\n      borderTopStyle: 'inherit'\n    }\n  });\n}, _ref4 => {\n  let {\n    theme,\n    ownerState\n  } = _ref4;\n  return _extends({}, ownerState.children && ownerState.orientation === 'vertical' && {\n    flexDirection: 'column',\n    '&::before, &::after': {\n      height: '100%',\n      borderLeft: \"thin solid \".concat((theme.vars || theme).palette.divider),\n      borderLeftStyle: 'inherit'\n    }\n  });\n}, _ref5 => {\n  let {\n    ownerState\n  } = _ref5;\n  return _extends({}, ownerState.textAlign === 'right' && ownerState.orientation !== 'vertical' && {\n    '&::before': {\n      width: '90%'\n    },\n    '&::after': {\n      width: '10%'\n    }\n  }, ownerState.textAlign === 'left' && ownerState.orientation !== 'vertical' && {\n    '&::before': {\n      width: '10%'\n    },\n    '&::after': {\n      width: '90%'\n    }\n  });\n});\nconst DividerWrapper = styled('span', {\n  name: 'MuiDivider',\n  slot: 'Wrapper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.wrapper, ownerState.orientation === 'vertical' && styles.wrapperVertical];\n  }\n})(_ref6 => {\n  let {\n    theme,\n    ownerState\n  } = _ref6;\n  return _extends({\n    display: 'inline-block',\n    paddingLeft: \"calc(\".concat(theme.spacing(1), \" * 1.2)\"),\n    paddingRight: \"calc(\".concat(theme.spacing(1), \" * 1.2)\")\n  }, ownerState.orientation === 'vertical' && {\n    paddingTop: \"calc(\".concat(theme.spacing(1), \" * 1.2)\"),\n    paddingBottom: \"calc(\".concat(theme.spacing(1), \" * 1.2)\")\n  });\n});\nconst Divider = /*#__PURE__*/React.forwardRef(function Divider(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDivider'\n  });\n  const {\n      absolute = false,\n      children,\n      className,\n      component = children ? 'div' : 'hr',\n      flexItem = false,\n      light = false,\n      orientation = 'horizontal',\n      role = component !== 'hr' ? 'separator' : undefined,\n      textAlign = 'center',\n      variant = 'fullWidth'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    absolute,\n    component,\n    flexItem,\n    light,\n    orientation,\n    role,\n    textAlign,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(DividerRoot, _extends({\n    as: component,\n    className: clsx(classes.root, className),\n    role: role,\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: children ? /*#__PURE__*/_jsx(DividerWrapper, {\n      className: classes.wrapper,\n      ownerState: ownerState,\n      children: children\n    }) : null\n  }));\n});\n\n/**\n * The following flag is used to ensure that this component isn't tabbable i.e.\n * does not get highlight/focus inside of MUI List.\n */\nDivider.muiSkipListHighlight = true;\nprocess.env.NODE_ENV !== \"production\" ? Divider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Absolutely position the element.\n   * @default false\n   */\n  absolute: PropTypes.bool,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, a vertical divider will have the correct height when used in flex container.\n   * (By default, a vertical divider will have a calculated height of `0px` if it is the child of a flex container.)\n   * @default false\n   */\n  flexItem: PropTypes.bool,\n  /**\n   * If `true`, the divider will have a lighter color.\n   * @default false\n   * @deprecated Use <Divider sx={{ opacity: 0.6 }} /> (or any opacity or color) instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)\n   */\n  light: PropTypes.bool,\n  /**\n   * The component orientation.\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * @ignore\n   */\n  role: PropTypes /* @typescript-to-proptypes-ignore */.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The text alignment.\n   * @default 'center'\n   */\n  textAlign: PropTypes.oneOf(['center', 'left', 'right']),\n  /**\n   * The variant to use.\n   * @default 'fullWidth'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['fullWidth', 'inset', 'middle']), PropTypes.string])\n} : void 0;\nexport default Divider;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}