{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport areArraysEqual from '../utils/areArraysEqual';\n/**\n * Gets the current state. If the selectedValue is controlled,\n * the `value` prop is the source of truth instead of the internal state.\n */\n\nfunction getControlledState(internalState, props) {\n  if (props.value !== undefined) {\n    return _extends({}, internalState, {\n      selectedValue: props.value\n    });\n  }\n  return internalState;\n}\nfunction areOptionsEqual(option1, option2, optionComparer) {\n  if (option1 === option2) {\n    return true;\n  }\n  if (option1 === null || option2 === null) {\n    return false;\n  }\n  return optionComparer(option1, option2);\n}\n/**\n * Triggers change event handlers when reducer returns changed state.\n */\n\nfunction useStateChangeDetection(nextState, internalPreviousState, propsRef, hasDispatchedActionRef) {\n  React.useEffect(() => {\n    if (!propsRef.current || !hasDispatchedActionRef.current) {\n      // Detect changes only if an action has been dispatched.\n      return;\n    }\n    hasDispatchedActionRef.current = false;\n    const previousState = getControlledState(internalPreviousState, propsRef.current);\n    const {\n      multiple,\n      optionComparer\n    } = propsRef.current;\n    if (multiple) {\n      var _previousState$select;\n      const previousSelectedValues = (_previousState$select = previousState == null ? void 0 : previousState.selectedValue) != null ? _previousState$select : [];\n      const nextSelectedValues = nextState.selectedValue;\n      const onChange = propsRef.current.onChange;\n      if (!areArraysEqual(nextSelectedValues, previousSelectedValues, optionComparer)) {\n        onChange == null ? void 0 : onChange(nextSelectedValues);\n      }\n    } else {\n      const previousSelectedValue = previousState == null ? void 0 : previousState.selectedValue;\n      const nextSelectedValue = nextState.selectedValue;\n      const onChange = propsRef.current.onChange;\n      if (!areOptionsEqual(nextSelectedValue, previousSelectedValue, optionComparer)) {\n        onChange == null ? void 0 : onChange(nextSelectedValue);\n      }\n    }\n  }, [nextState.selectedValue, internalPreviousState, propsRef, hasDispatchedActionRef]);\n  React.useEffect(() => {\n    if (!propsRef.current) {\n      return;\n    } // Fires the highlightChange event when reducer returns changed `highlightedValue`.\n\n    if (!areOptionsEqual(internalPreviousState.highlightedValue, nextState.highlightedValue, propsRef.current.optionComparer)) {\n      var _propsRef$current, _propsRef$current$onH;\n      (_propsRef$current = propsRef.current) == null ? void 0 : (_propsRef$current$onH = _propsRef$current.onHighlightChange) == null ? void 0 : _propsRef$current$onH.call(_propsRef$current, nextState.highlightedValue);\n    }\n  }, [nextState.highlightedValue, internalPreviousState.highlightedValue, propsRef]);\n}\nexport default function useControllableReducer(internalReducer, externalReducer, props) {\n  var _ref;\n  const {\n    value,\n    defaultValue\n  } = props;\n  const propsRef = React.useRef(props);\n  propsRef.current = props;\n  const hasDispatchedActionRef = React.useRef(false);\n  const initialSelectedValue = (_ref = value === undefined ? defaultValue : value) != null ? _ref : props.multiple ? [] : null;\n  const initalState = {\n    highlightedValue: null,\n    selectedValue: initialSelectedValue\n  };\n  const combinedReducer = React.useCallback((state, action) => {\n    hasDispatchedActionRef.current = true;\n    if (externalReducer) {\n      return externalReducer(getControlledState(state, propsRef.current), action);\n    }\n    return internalReducer(getControlledState(state, propsRef.current), action);\n  }, [externalReducer, internalReducer, propsRef]);\n  const [nextState, dispatch] = React.useReducer(combinedReducer, initalState);\n  const previousState = React.useRef(initalState);\n  React.useEffect(() => {\n    previousState.current = nextState;\n  }, [previousState, nextState]);\n  useStateChangeDetection(nextState, previousState.current, propsRef, hasDispatchedActionRef);\n  return [getControlledState(nextState, propsRef.current), dispatch];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}