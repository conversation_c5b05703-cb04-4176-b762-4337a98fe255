{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"value\", \"defaultValue\", \"orientation\", \"direction\", \"component\", \"components\", \"componentsProps\", \"onChange\", \"selectionFollowsFocus\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useSlotProps } from '../utils';\nimport composeClasses from '../composeClasses';\nimport { getTabsUnstyledUtilityClass } from './tabsUnstyledClasses';\nimport useTabs from './useTabs';\nimport Context from './TabsContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    orientation\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation]\n  };\n  return composeClasses(slots, getTabsUnstyledUtilityClass, {});\n};\n/**\n *\n * Demos:\n *\n * - [Unstyled tabs](https://mui.com/base/react-tabs/)\n *\n * API:\n *\n * - [TabsUnstyled API](https://mui.com/base/api/tabs-unstyled/)\n */\n\nconst TabsUnstyled = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _ref;\n  const {\n      children,\n      orientation = 'horizontal',\n      direction = 'ltr',\n      component,\n      components = {},\n      componentsProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    tabsContextValue\n  } = useTabs(props);\n  const ownerState = _extends({}, props, {\n    orientation,\n    direction\n  });\n  const classes = useUtilityClasses(ownerState);\n  const TabsRoot = (_ref = component != null ? component : components.Root) != null ? _ref : 'div';\n  const tabsRootProps = useSlotProps({\n    elementType: TabsRoot,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(TabsRoot, _extends({}, tabsRootProps, {\n    children: /*#__PURE__*/_jsx(Context.Provider, {\n      value: tabsContextValue,\n      children: children\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TabsUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the Tabs.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The props used for each slot inside the Tabs.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The default value. Use when the component is not controlled.\n   */\n  defaultValue: PropTypes.oneOfType([PropTypes.oneOf([false]), PropTypes.number, PropTypes.string]),\n  /**\n   * The direction of the text.\n   * @default 'ltr'\n   */\n  direction: PropTypes.oneOf(['ltr', 'rtl']),\n  /**\n   * Callback invoked when new value is being set.\n   */\n  onChange: PropTypes.func,\n  /**\n   * The component orientation (layout flow direction).\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * If `true` the selected tab changes on focus. Otherwise it only\n   * changes on activation.\n   */\n  selectionFollowsFocus: PropTypes.bool,\n  /**\n   * The value of the currently selected `Tab`.\n   * If you don't want any selected `Tab`, you can set this prop to `false`.\n   */\n  value: PropTypes.oneOfType([PropTypes.oneOf([false]), PropTypes.number, PropTypes.string])\n} : void 0;\nexport default TabsUnstyled;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}