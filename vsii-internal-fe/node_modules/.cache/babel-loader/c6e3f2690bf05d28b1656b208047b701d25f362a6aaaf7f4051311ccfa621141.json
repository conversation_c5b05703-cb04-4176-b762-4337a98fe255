{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/layout/MainLayout.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useMemo } from 'react';\nimport { Outlet } from 'react-router-dom';\n\n// material-ui\nimport { styled, useTheme } from '@mui/material/styles';\nimport { AppBar, Box, Container, CssBaseline, Toolbar, useMediaQuery } from '@mui/material';\n\n// project imports\nimport Header from './Header';\nimport Sidebar from './Sidebar';\nimport Breadcrumbs from 'components/extended/Breadcrumbs';\nimport DeniedPermissionScreen from 'components/DeniedPermissionScreen';\nimport navigation from 'menu-items';\nimport { LAYOUT_CONST } from 'constants/Common';\nimport useConfig from 'hooks/useConfig';\nimport { drawerWidth } from 'store/constant';\nimport { openDrawer } from 'store/slice/menuSlice';\nimport { useAppSelector, useAppDispatch } from 'app/hooks';\n\n// assets\nimport { IconChevronRight } from '@tabler/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// styles\nconst Main = styled('main', {\n  shouldForwardProp: prop => prop !== 'open'\n})(({\n  theme,\n  open,\n  layout\n}) => ({\n  ...theme.typography.mainContent,\n  borderBottomLeftRadius: 0,\n  borderBottomRightRadius: 0,\n  ...(!open && {\n    transition: theme.transitions.create('margin', {\n      easing: theme.transitions.easing.sharp,\n      duration: theme.transitions.duration.shorter\n    }),\n    [theme.breakpoints.up('md')]: {\n      marginLeft: layout === LAYOUT_CONST.VERTICAL_LAYOUT ? -(drawerWidth - 72) : '20px',\n      width: `calc(100% - ${drawerWidth}px)`,\n      marginTop: 88\n    },\n    [theme.breakpoints.down('md')]: {\n      marginLeft: '20px',\n      width: `calc(100% - ${drawerWidth}px)`,\n      padding: '16px',\n      marginTop: 88\n    },\n    [theme.breakpoints.down('sm')]: {\n      marginLeft: '10px',\n      width: `calc(100% - ${drawerWidth}px)`,\n      padding: '16px',\n      marginRight: '10px',\n      marginTop: 88\n    }\n  }),\n  ...(open && {\n    transition: theme.transitions.create('margin', {\n      easing: theme.transitions.easing.easeOut,\n      duration: theme.transitions.duration.shorter\n    }),\n    marginLeft: 0,\n    marginTop: 88,\n    width: `calc(100% - ${drawerWidth}px)`,\n    [theme.breakpoints.up('md')]: {\n      marginTop: 88\n    },\n    [theme.breakpoints.down('md')]: {\n      marginLeft: '20px',\n      marginTop: 88\n    },\n    [theme.breakpoints.down('sm')]: {\n      marginLeft: '10px',\n      marginTop: 88\n    }\n  })\n}));\n\n// ==============================|| MAIN LAYOUT ||============================== //\n_c = Main;\nconst MainLayout = () => {\n  _s();\n  const theme = useTheme();\n  const matchDownMd = useMediaQuery(theme.breakpoints.down('md'));\n  const dispatch = useAppDispatch();\n  const {\n    drawerOpen\n  } = useAppSelector(state => state.menu);\n  const {\n    drawerType,\n    container,\n    layout\n  } = useConfig();\n  const {\n    show,\n    isTabWrap\n  } = useAppSelector(state => state.deniedPermission);\n  useEffect(() => {\n    if (drawerType === LAYOUT_CONST.DEFAULT_DRAWER) {\n      dispatch(openDrawer(true));\n    } else {\n      dispatch(openDrawer(false));\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [drawerType]);\n  useEffect(() => {\n    if (drawerType === LAYOUT_CONST.DEFAULT_DRAWER) {\n      dispatch(openDrawer(true));\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  useEffect(() => {\n    if (matchDownMd) {\n      dispatch(openDrawer(true));\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [matchDownMd]);\n  const header = useMemo(() => /*#__PURE__*/_jsxDEV(Toolbar, {\n    sx: {\n      p: '16px'\n    },\n    children: /*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 117,\n    columnNumber: 13\n  }, this),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [layout, matchDownMd]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex'\n    },\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(AppBar, {\n      enableColorOnDark: true,\n      position: \"fixed\",\n      color: \"inherit\",\n      elevation: 0,\n      sx: {\n        bgcolor: theme.palette.background.default\n      },\n      children: header\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Main, {\n      theme: theme,\n      open: drawerOpen,\n      layout: layout,\n      children: show && isTabWrap ? /*#__PURE__*/_jsxDEV(DeniedPermissionScreen, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(Container, {\n        maxWidth: container ? 'lg' : false,\n        ...(!container && {\n          sx: {\n            px: {\n              xs: 0\n            }\n          }\n        }),\n        children: [/*#__PURE__*/_jsxDEV(Breadcrumbs, {\n          separator: IconChevronRight,\n          navigation: navigation,\n          icon: true,\n          title: true,\n          rightAlign: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 9\n  }, this);\n};\n_s(MainLayout, \"YmoyOfnloOZvxOcm/r1s8M2S97E=\", false, function () {\n  return [useTheme, useMediaQuery, useAppDispatch, useAppSelector, useConfig, useAppSelector];\n});\n_c2 = MainLayout;\nexport default MainLayout;\nvar _c, _c2;\n$RefreshReg$(_c, \"Main\");\n$RefreshReg$(_c2, \"MainLayout\");", "map": {"version": 3, "names": ["useEffect", "useMemo", "Outlet", "styled", "useTheme", "AppBar", "Box", "Container", "CssBaseline", "<PERSON><PERSON><PERSON>", "useMediaQuery", "Header", "Sidebar", "Breadcrumbs", "DeniedPermissionScreen", "navigation", "LAYOUT_CONST", "useConfig", "drawerWidth", "openDrawer", "useAppSelector", "useAppDispatch", "IconChevronRight", "jsxDEV", "_jsxDEV", "Main", "shouldForwardProp", "prop", "theme", "open", "layout", "typography", "mainContent", "borderBottomLeftRadius", "borderBottomRightRadius", "transition", "transitions", "create", "easing", "sharp", "duration", "shorter", "breakpoints", "up", "marginLeft", "VERTICAL_LAYOUT", "width", "marginTop", "down", "padding", "marginRight", "easeOut", "_c", "MainLayout", "_s", "matchDownMd", "dispatch", "drawerOpen", "state", "menu", "drawerType", "container", "show", "isTabWrap", "deniedPermission", "DEFAULT_DRAWER", "header", "sx", "p", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "enableColorOnDark", "position", "color", "elevation", "bgcolor", "palette", "background", "default", "max<PERSON><PERSON><PERSON>", "px", "xs", "separator", "icon", "title", "rightAlign", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/layout/MainLayout.tsx"], "sourcesContent": ["import { useEffect, useMemo } from 'react';\nimport { Outlet } from 'react-router-dom';\n\n// material-ui\nimport { styled, useTheme, Theme } from '@mui/material/styles';\nimport { AppBar, Box, Container, CssBaseline, Toolbar, useMediaQuery } from '@mui/material';\n\n// project imports\nimport Header from './Header';\nimport Sidebar from './Sidebar';\nimport Breadcrumbs from 'components/extended/Breadcrumbs';\nimport DeniedPermissionScreen from 'components/DeniedPermissionScreen';\nimport navigation from 'menu-items';\nimport { LAYOUT_CONST } from 'constants/Common';\nimport useConfig from 'hooks/useConfig';\nimport { drawerWidth } from 'store/constant';\nimport { openDrawer } from 'store/slice/menuSlice';\nimport { useAppSelector, useAppDispatch } from 'app/hooks';\n\n// assets\nimport { IconChevronRight } from '@tabler/icons';\n\ninterface MainStyleProps {\n    theme: Theme;\n    open: boolean;\n    layout: string;\n}\n\n// styles\nconst Main = styled('main', { shouldForwardProp: (prop) => prop !== 'open' })(({ theme, open, layout }: MainStyleProps) => ({\n    ...theme.typography.mainContent,\n    borderBottomLeftRadius: 0,\n    borderBottomRightRadius: 0,\n    ...(!open && {\n        transition: theme.transitions.create('margin', {\n            easing: theme.transitions.easing.sharp,\n            duration: theme.transitions.duration.shorter\n        }),\n        [theme.breakpoints.up('md')]: {\n            marginLeft: layout === LAYOUT_CONST.VERTICAL_LAYOUT ? -(drawerWidth - 72) : '20px',\n            width: `calc(100% - ${drawerWidth}px)`,\n            marginTop: 88\n        },\n        [theme.breakpoints.down('md')]: {\n            marginLeft: '20px',\n            width: `calc(100% - ${drawerWidth}px)`,\n            padding: '16px',\n            marginTop: 88\n        },\n        [theme.breakpoints.down('sm')]: {\n            marginLeft: '10px',\n            width: `calc(100% - ${drawerWidth}px)`,\n            padding: '16px',\n            marginRight: '10px',\n            marginTop: 88\n        }\n    }),\n    ...(open && {\n        transition: theme.transitions.create('margin', {\n            easing: theme.transitions.easing.easeOut,\n            duration: theme.transitions.duration.shorter\n        }),\n        marginLeft: 0,\n        marginTop: 88,\n        width: `calc(100% - ${drawerWidth}px)`,\n        [theme.breakpoints.up('md')]: {\n            marginTop: 88\n        },\n        [theme.breakpoints.down('md')]: {\n            marginLeft: '20px',\n            marginTop: 88\n        },\n        [theme.breakpoints.down('sm')]: {\n            marginLeft: '10px',\n            marginTop: 88\n        }\n    })\n}));\n\n// ==============================|| MAIN LAYOUT ||============================== //\n\nconst MainLayout = () => {\n    const theme = useTheme();\n\n    const matchDownMd = useMediaQuery(theme.breakpoints.down('md'));\n    const dispatch = useAppDispatch();\n    const { drawerOpen } = useAppSelector((state) => state.menu);\n    const { drawerType, container, layout } = useConfig();\n\n    const { show, isTabWrap } = useAppSelector((state) => state.deniedPermission);\n\n    useEffect(() => {\n        if (drawerType === LAYOUT_CONST.DEFAULT_DRAWER) {\n            dispatch(openDrawer(true));\n        } else {\n            dispatch(openDrawer(false));\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [drawerType]);\n\n    useEffect(() => {\n        if (drawerType === LAYOUT_CONST.DEFAULT_DRAWER) {\n            dispatch(openDrawer(true));\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n\n    useEffect(() => {\n        if (matchDownMd) {\n            dispatch(openDrawer(true));\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [matchDownMd]);\n\n    const header = useMemo(\n        () => (\n            <Toolbar sx={{ p: '16px' }}>\n                <Header />\n            </Toolbar>\n        ),\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        [layout, matchDownMd]\n    );\n\n    return (\n        <Box sx={{ display: 'flex' }}>\n            <CssBaseline />\n            {/* header */}\n            <AppBar enableColorOnDark position=\"fixed\" color=\"inherit\" elevation={0} sx={{ bgcolor: theme.palette.background.default }}>\n                {header}\n            </AppBar>\n\n            {/* drawer */}\n            <Sidebar />\n\n            {/* main content */}\n            <Main theme={theme} open={drawerOpen} layout={layout}>\n                {show && isTabWrap ? (\n                    <DeniedPermissionScreen />\n                ) : (\n                    <Container maxWidth={container ? 'lg' : false} {...(!container && { sx: { px: { xs: 0 } } })}>\n                        <Breadcrumbs separator={IconChevronRight} navigation={navigation} icon title rightAlign />\n                        <Outlet />\n                    </Container>\n                )}\n            </Main>\n        </Box>\n    );\n};\n\nexport default MainLayout;\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,OAAO,QAAQ,OAAO;AAC1C,SAASC,MAAM,QAAQ,kBAAkB;;AAEzC;AACA,SAASC,MAAM,EAAEC,QAAQ,QAAe,sBAAsB;AAC9D,SAASC,MAAM,EAAEC,GAAG,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,aAAa,QAAQ,eAAe;;AAE3F;AACA,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,WAAW,MAAM,iCAAiC;AACzD,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,cAAc,EAAEC,cAAc,QAAQ,WAAW;;AAE1D;AACA,SAASC,gBAAgB,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQjD;AACA,MAAMC,IAAI,GAAGtB,MAAM,CAAC,MAAM,EAAE;EAAEuB,iBAAiB,EAAGC,IAAI,IAAKA,IAAI,KAAK;AAAO,CAAC,CAAC,CAAC,CAAC;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAuB,CAAC,MAAM;EACxH,GAAGF,KAAK,CAACG,UAAU,CAACC,WAAW;EAC/BC,sBAAsB,EAAE,CAAC;EACzBC,uBAAuB,EAAE,CAAC;EAC1B,IAAI,CAACL,IAAI,IAAI;IACTM,UAAU,EAAEP,KAAK,CAACQ,WAAW,CAACC,MAAM,CAAC,QAAQ,EAAE;MAC3CC,MAAM,EAAEV,KAAK,CAACQ,WAAW,CAACE,MAAM,CAACC,KAAK;MACtCC,QAAQ,EAAEZ,KAAK,CAACQ,WAAW,CAACI,QAAQ,CAACC;IACzC,CAAC,CAAC;IACF,CAACb,KAAK,CAACc,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;MAC1BC,UAAU,EAAEd,MAAM,KAAKd,YAAY,CAAC6B,eAAe,GAAG,EAAE3B,WAAW,GAAG,EAAE,CAAC,GAAG,MAAM;MAClF4B,KAAK,EAAE,eAAe5B,WAAW,KAAK;MACtC6B,SAAS,EAAE;IACf,CAAC;IACD,CAACnB,KAAK,CAACc,WAAW,CAACM,IAAI,CAAC,IAAI,CAAC,GAAG;MAC5BJ,UAAU,EAAE,MAAM;MAClBE,KAAK,EAAE,eAAe5B,WAAW,KAAK;MACtC+B,OAAO,EAAE,MAAM;MACfF,SAAS,EAAE;IACf,CAAC;IACD,CAACnB,KAAK,CAACc,WAAW,CAACM,IAAI,CAAC,IAAI,CAAC,GAAG;MAC5BJ,UAAU,EAAE,MAAM;MAClBE,KAAK,EAAE,eAAe5B,WAAW,KAAK;MACtC+B,OAAO,EAAE,MAAM;MACfC,WAAW,EAAE,MAAM;MACnBH,SAAS,EAAE;IACf;EACJ,CAAC,CAAC;EACF,IAAIlB,IAAI,IAAI;IACRM,UAAU,EAAEP,KAAK,CAACQ,WAAW,CAACC,MAAM,CAAC,QAAQ,EAAE;MAC3CC,MAAM,EAAEV,KAAK,CAACQ,WAAW,CAACE,MAAM,CAACa,OAAO;MACxCX,QAAQ,EAAEZ,KAAK,CAACQ,WAAW,CAACI,QAAQ,CAACC;IACzC,CAAC,CAAC;IACFG,UAAU,EAAE,CAAC;IACbG,SAAS,EAAE,EAAE;IACbD,KAAK,EAAE,eAAe5B,WAAW,KAAK;IACtC,CAACU,KAAK,CAACc,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;MAC1BI,SAAS,EAAE;IACf,CAAC;IACD,CAACnB,KAAK,CAACc,WAAW,CAACM,IAAI,CAAC,IAAI,CAAC,GAAG;MAC5BJ,UAAU,EAAE,MAAM;MAClBG,SAAS,EAAE;IACf,CAAC;IACD,CAACnB,KAAK,CAACc,WAAW,CAACM,IAAI,CAAC,IAAI,CAAC,GAAG;MAC5BJ,UAAU,EAAE,MAAM;MAClBG,SAAS,EAAE;IACf;EACJ,CAAC;AACL,CAAC,CAAC,CAAC;;AAEH;AAAAK,EAAA,GAlDM3B,IAAI;AAoDV,MAAM4B,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM1B,KAAK,GAAGxB,QAAQ,CAAC,CAAC;EAExB,MAAMmD,WAAW,GAAG7C,aAAa,CAACkB,KAAK,CAACc,WAAW,CAACM,IAAI,CAAC,IAAI,CAAC,CAAC;EAC/D,MAAMQ,QAAQ,GAAGnC,cAAc,CAAC,CAAC;EACjC,MAAM;IAAEoC;EAAW,CAAC,GAAGrC,cAAc,CAAEsC,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC;EAC5D,MAAM;IAAEC,UAAU;IAAEC,SAAS;IAAE/B;EAAO,CAAC,GAAGb,SAAS,CAAC,CAAC;EAErD,MAAM;IAAE6C,IAAI;IAAEC;EAAU,CAAC,GAAG3C,cAAc,CAAEsC,KAAK,IAAKA,KAAK,CAACM,gBAAgB,CAAC;EAE7EhE,SAAS,CAAC,MAAM;IACZ,IAAI4D,UAAU,KAAK5C,YAAY,CAACiD,cAAc,EAAE;MAC5CT,QAAQ,CAACrC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC,MAAM;MACHqC,QAAQ,CAACrC,UAAU,CAAC,KAAK,CAAC,CAAC;IAC/B;IACA;EACJ,CAAC,EAAE,CAACyC,UAAU,CAAC,CAAC;EAEhB5D,SAAS,CAAC,MAAM;IACZ,IAAI4D,UAAU,KAAK5C,YAAY,CAACiD,cAAc,EAAE;MAC5CT,QAAQ,CAACrC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC9B;IACA;EACJ,CAAC,EAAE,EAAE,CAAC;EAENnB,SAAS,CAAC,MAAM;IACZ,IAAIuD,WAAW,EAAE;MACbC,QAAQ,CAACrC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC9B;IACA;EACJ,CAAC,EAAE,CAACoC,WAAW,CAAC,CAAC;EAEjB,MAAMW,MAAM,GAAGjE,OAAO,CAClB,mBACIuB,OAAA,CAACf,OAAO;IAAC0D,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAO,CAAE;IAAAC,QAAA,eACvB7C,OAAA,CAACb,MAAM;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACZ;EACD;EACA,CAAC3C,MAAM,EAAEyB,WAAW,CACxB,CAAC;EAED,oBACI/B,OAAA,CAAClB,GAAG;IAAC6D,EAAE,EAAE;MAAEO,OAAO,EAAE;IAAO,CAAE;IAAAL,QAAA,gBACzB7C,OAAA,CAAChB,WAAW;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEfjD,OAAA,CAACnB,MAAM;MAACsE,iBAAiB;MAACC,QAAQ,EAAC,OAAO;MAACC,KAAK,EAAC,SAAS;MAACC,SAAS,EAAE,CAAE;MAACX,EAAE,EAAE;QAAEY,OAAO,EAAEnD,KAAK,CAACoD,OAAO,CAACC,UAAU,CAACC;MAAQ,CAAE;MAAAb,QAAA,EACtHH;IAAM;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGTjD,OAAA,CAACZ,OAAO;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXjD,OAAA,CAACC,IAAI;MAACG,KAAK,EAAEA,KAAM;MAACC,IAAI,EAAE4B,UAAW;MAAC3B,MAAM,EAAEA,MAAO;MAAAuC,QAAA,EAChDP,IAAI,IAAIC,SAAS,gBACdvC,OAAA,CAACV,sBAAsB;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAE1BjD,OAAA,CAACjB,SAAS;QAAC4E,QAAQ,EAAEtB,SAAS,GAAG,IAAI,GAAG,KAAM;QAAA,IAAM,CAACA,SAAS,IAAI;UAAEM,EAAE,EAAE;YAAEiB,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE;UAAE;QAAE,CAAC;QAAAhB,QAAA,gBACvF7C,OAAA,CAACX,WAAW;UAACyE,SAAS,EAAEhE,gBAAiB;UAACP,UAAU,EAAEA,UAAW;UAACwE,IAAI;UAACC,KAAK;UAACC,UAAU;QAAA;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1FjD,OAAA,CAACtB,MAAM;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACd;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEd,CAAC;AAACnB,EAAA,CAnEID,UAAU;EAAA,QACEjD,QAAQ,EAEFM,aAAa,EAChBW,cAAc,EACRD,cAAc,EACKH,SAAS,EAEvBG,cAAc;AAAA;AAAAsE,GAAA,GARxCrC,UAAU;AAqEhB,eAAeA,UAAU;AAAC,IAAAD,EAAA,EAAAsC,GAAA;AAAAC,YAAA,CAAAvC,EAAA;AAAAuC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}