{"ast": null, "code": "import { isMotionValue } from './utils/is-motion-value.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\nfunction useOnChange(value, callback) {\n  useIsomorphicLayoutEffect(() => {\n    if (isMotionValue(value)) {\n      callback(value.get());\n      return value.on(\"change\", callback);\n    }\n  }, [value, callback]);\n}\nfunction useMultiOnChange(values, handler, cleanup) {\n  useIsomorphicLayoutEffect(() => {\n    const subscriptions = values.map(value => value.on(\"change\", handler));\n    return () => {\n      subscriptions.forEach(unsubscribe => unsubscribe());\n      cleanup();\n    };\n  });\n}\nexport { useMultiOnChange, useOnChange };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}