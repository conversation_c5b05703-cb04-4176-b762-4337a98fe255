{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/weekly-effort/WeeklyEffortMemberSearch.tsx\";\nimport { FormattedMessage } from 'react-intl';\nimport { Grid, Typography } from '@mui/material';\nimport ErrorIcon from '@mui/icons-material/Error';\nimport { weeklyEffortConfig, weeklyEffortMemberSchema } from 'pages/Config';\nimport { E_IS_LOGTIME, TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';\nimport { Member, SearchForm, TimeStatus, Weeks, Years } from '../search';\nimport ColorNoteTooltip from 'components/ColorNoteTooltip';\nimport { Label } from 'components/extended/Form';\nimport { Button } from 'components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WeeklyEffortMemberSearch = ({\n  weeks,\n  formReset,\n  handleChangeYear,\n  handleSearch,\n  handleChangeWeek,\n  handleChangeTimeStatus,\n  handleChangeMember\n}) => {\n  const {\n    Weeklyeffort\n  } = TEXT_CONFIG_SCREEN;\n  return /*#__PURE__*/_jsxDEV(SearchForm, {\n    defaultValues: weeklyEffortConfig,\n    formSchema: weeklyEffortMemberSchema,\n    handleSubmit: handleSearch,\n    formReset: formReset,\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      alignItems: \"center\",\n      spacing: 2,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2.4,\n        children: /*#__PURE__*/_jsxDEV(Years, {\n          handleChangeYear: handleChangeYear,\n          ignoreDefault: true,\n          label: Weeklyeffort + 'year'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2.4,\n        children: /*#__PURE__*/_jsxDEV(Weeks, {\n          weeks: weeks,\n          onChange: handleChangeWeek,\n          label: Weeklyeffort + 'weeks'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2.4,\n        children: /*#__PURE__*/_jsxDEV(TimeStatus, {\n          isMultiple: true,\n          onChange: handleChangeTimeStatus,\n          label: Weeklyeffort + 'timesheet-status'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2.4,\n        children: /*#__PURE__*/_jsxDEV(Member, {\n          autoFilter: formReset,\n          handleChange: handleChangeMember,\n          isLogTime: E_IS_LOGTIME.YES,\n          findAllType: \"SCREEN_EFFORT\",\n          label: /*#__PURE__*/_jsxDEV(Typography, {\n            display: \"flex\",\n            gap: 0.5,\n            children: [/*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: Weeklyeffort + 'members'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(ColorNoteTooltip, {\n              notes: TEXT_INPUT_COLOR_EFFORT_INCURRED,\n              children: /*#__PURE__*/_jsxDEV(ErrorIcon, {\n                sx: {\n                  fontSize: 15\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 2.4,\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          label: \"\\xA0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          size: \"medium\",\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: Weeklyeffort + 'search'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 67\n          }, this),\n          variant: \"contained\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 9\n  }, this);\n};\n_c = WeeklyEffortMemberSearch;\nexport default WeeklyEffortMemberSearch;\nvar _c;\n$RefreshReg$(_c, \"WeeklyEffortMemberSearch\");", "map": {"version": 3, "names": ["FormattedMessage", "Grid", "Typography", "ErrorIcon", "weeklyEffortConfig", "weeklyEffortMemberSchema", "E_IS_LOGTIME", "TEXT_CONFIG_SCREEN", "TEXT_INPUT_COLOR_EFFORT_INCURRED", "Member", "SearchForm", "TimeStatus", "Weeks", "Years", "ColorNoteTooltip", "Label", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "WeeklyEffortMemberSearch", "weeks", "formReset", "handleChangeYear", "handleSearch", "handleChangeWeek", "handleChangeTimeStatus", "handleChangeMember", "Weeklyeffort", "defaultValues", "formSchema", "handleSubmit", "children", "container", "alignItems", "spacing", "item", "xs", "lg", "<PERSON><PERSON><PERSON><PERSON>", "label", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "isMultiple", "autoFilter", "handleChange", "isLogTime", "YES", "findAllType", "display", "gap", "id", "notes", "sx", "fontSize", "type", "size", "variant", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/weekly-effort/WeeklyEffortMemberSearch.tsx"], "sourcesContent": ["import { FormattedMessage } from 'react-intl';\nimport { Grid, SelectChangeEvent, Typography } from '@mui/material';\nimport ErrorIcon from '@mui/icons-material/Error';\n\nimport { IWeeklyEffortConfig, weeklyEffortConfig, weeklyEffortMemberSchema } from 'pages/Config';\nimport { E_IS_LOGTIME, TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';\nimport { Member, SearchForm, TimeStatus, Weeks, Years } from '../search';\nimport ColorNoteTooltip from 'components/ColorNoteTooltip';\nimport { Label } from 'components/extended/Form';\nimport { IMember } from 'types/member';\nimport { Button } from 'components';\nimport { IOption } from 'types';\n\ninterface IWeeklyEffortMemberSearchProps {\n    weeks: IOption[];\n    formReset: IWeeklyEffortConfig;\n    handleChangeYear: (e: SelectChangeEvent<unknown>) => void;\n    handleSearch: (value: IWeeklyEffortConfig) => void;\n    handleChangeWeek?: (value: string) => void;\n    handleChangeTimeStatus?: (value: string[]) => void;\n    handleChangeMember?: (value: IMember) => void;\n}\n\nconst WeeklyEffortMemberSearch = ({\n    weeks,\n    formReset,\n    handleChangeYear,\n    handleSearch,\n    handleChangeWeek,\n    handleChangeTimeStatus,\n    handleChangeMember\n}: IWeeklyEffortMemberSearchProps) => {\n    const { Weeklyeffort } = TEXT_CONFIG_SCREEN;\n\n    return (\n        <SearchForm\n            defaultValues={weeklyEffortConfig}\n            formSchema={weeklyEffortMemberSchema}\n            handleSubmit={handleSearch}\n            formReset={formReset}\n        >\n            <Grid container alignItems=\"center\" spacing={2}>\n                <Grid item xs={12} lg={2.4}>\n                    <Years handleChangeYear={handleChangeYear} ignoreDefault label={Weeklyeffort + 'year'} />\n                </Grid>\n                <Grid item xs={12} lg={2.4}>\n                    <Weeks weeks={weeks} onChange={handleChangeWeek} label={Weeklyeffort + 'weeks'} />\n                </Grid>\n                <Grid item xs={12} lg={2.4}>\n                    <TimeStatus isMultiple onChange={handleChangeTimeStatus} label={Weeklyeffort + 'timesheet-status'} />\n                </Grid>\n\n                <Grid item xs={12} lg={2.4}>\n                    <Member\n                        autoFilter={formReset}\n                        handleChange={handleChangeMember}\n                        isLogTime={E_IS_LOGTIME.YES}\n                        findAllType=\"SCREEN_EFFORT\"\n                        label={\n                            <Typography display=\"flex\" gap={0.5}>\n                                <FormattedMessage id={Weeklyeffort + 'members'} />\n                                <ColorNoteTooltip notes={TEXT_INPUT_COLOR_EFFORT_INCURRED}>\n                                    <ErrorIcon sx={{ fontSize: 15 }} />\n                                </ColorNoteTooltip>\n                            </Typography>\n                        }\n                    />\n                </Grid>\n                <Grid item xs={12} lg={2.4}>\n                    <Label label=\"&nbsp;\" />\n                    <Button type=\"submit\" size=\"medium\" children={<FormattedMessage id={Weeklyeffort + 'search'} />} variant=\"contained\" />\n                </Grid>\n            </Grid>\n        </SearchForm>\n    );\n};\n\nexport default WeeklyEffortMemberSearch;\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,IAAI,EAAqBC,UAAU,QAAQ,eAAe;AACnE,OAAOC,SAAS,MAAM,2BAA2B;AAEjD,SAA8BC,kBAAkB,EAAEC,wBAAwB,QAAQ,cAAc;AAChG,SAASC,YAAY,EAAEC,kBAAkB,EAAEC,gCAAgC,QAAQ,kBAAkB;AACrG,SAASC,MAAM,EAAEC,UAAU,EAAEC,UAAU,EAAEC,KAAK,EAAEC,KAAK,QAAQ,WAAW;AACxE,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,KAAK,QAAQ,0BAA0B;AAEhD,SAASC,MAAM,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAapC,MAAMC,wBAAwB,GAAGA,CAAC;EAC9BC,KAAK;EACLC,SAAS;EACTC,gBAAgB;EAChBC,YAAY;EACZC,gBAAgB;EAChBC,sBAAsB;EACtBC;AAC4B,CAAC,KAAK;EAClC,MAAM;IAAEC;EAAa,CAAC,GAAGpB,kBAAkB;EAE3C,oBACIW,OAAA,CAACR,UAAU;IACPkB,aAAa,EAAExB,kBAAmB;IAClCyB,UAAU,EAAExB,wBAAyB;IACrCyB,YAAY,EAAEP,YAAa;IAC3BF,SAAS,EAAEA,SAAU;IAAAU,QAAA,eAErBb,OAAA,CAACjB,IAAI;MAAC+B,SAAS;MAACC,UAAU,EAAC,QAAQ;MAACC,OAAO,EAAE,CAAE;MAAAH,QAAA,gBAC3Cb,OAAA,CAACjB,IAAI;QAACkC,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,GAAI;QAAAN,QAAA,eACvBb,OAAA,CAACL,KAAK;UAACS,gBAAgB,EAAEA,gBAAiB;UAACgB,aAAa;UAACC,KAAK,EAAEZ,YAAY,GAAG;QAAO;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvF,CAAC,eACPzB,OAAA,CAACjB,IAAI;QAACkC,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,GAAI;QAAAN,QAAA,eACvBb,OAAA,CAACN,KAAK;UAACQ,KAAK,EAAEA,KAAM;UAACwB,QAAQ,EAAEpB,gBAAiB;UAACe,KAAK,EAAEZ,YAAY,GAAG;QAAQ;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChF,CAAC,eACPzB,OAAA,CAACjB,IAAI;QAACkC,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,GAAI;QAAAN,QAAA,eACvBb,OAAA,CAACP,UAAU;UAACkC,UAAU;UAACD,QAAQ,EAAEnB,sBAAuB;UAACc,KAAK,EAAEZ,YAAY,GAAG;QAAmB;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnG,CAAC,eAEPzB,OAAA,CAACjB,IAAI;QAACkC,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,GAAI;QAAAN,QAAA,eACvBb,OAAA,CAACT,MAAM;UACHqC,UAAU,EAAEzB,SAAU;UACtB0B,YAAY,EAAErB,kBAAmB;UACjCsB,SAAS,EAAE1C,YAAY,CAAC2C,GAAI;UAC5BC,WAAW,EAAC,eAAe;UAC3BX,KAAK,eACDrB,OAAA,CAAChB,UAAU;YAACiD,OAAO,EAAC,MAAM;YAACC,GAAG,EAAE,GAAI;YAAArB,QAAA,gBAChCb,OAAA,CAAClB,gBAAgB;cAACqD,EAAE,EAAE1B,YAAY,GAAG;YAAU;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClDzB,OAAA,CAACJ,gBAAgB;cAACwC,KAAK,EAAE9C,gCAAiC;cAAAuB,QAAA,eACtDb,OAAA,CAACf,SAAS;gBAACoD,EAAE,EAAE;kBAAEC,QAAQ,EAAE;gBAAG;cAAE;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QACf;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACPzB,OAAA,CAACjB,IAAI;QAACkC,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,GAAI;QAAAN,QAAA,gBACvBb,OAAA,CAACH,KAAK;UAACwB,KAAK,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxBzB,OAAA,CAACF,MAAM;UAACyC,IAAI,EAAC,QAAQ;UAACC,IAAI,EAAC,QAAQ;UAAC3B,QAAQ,eAAEb,OAAA,CAAClB,gBAAgB;YAACqD,EAAE,EAAE1B,YAAY,GAAG;UAAS;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACgB,OAAO,EAAC;QAAW;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAErB,CAAC;AAACiB,EAAA,GApDIzC,wBAAwB;AAsD9B,eAAeA,wBAAwB;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}