{"ast": null, "code": "import React from'react';import{Popover as <PERSON><PERSON>Popover}from'@mui/material';import{jsx as _jsx}from\"react/jsx-runtime\";const Popover=props=>{const{anchorEl,handleClose,children,sx}=props;const open=<PERSON><PERSON>an(anchorEl);const id=open?'simple-popover':undefined;return/*#__PURE__*/_jsx(MuiPopover,{id:id,open:open,anchorEl:anchorEl,onClose:handleClose,anchorOrigin:{vertical:'bottom',horizontal:'center'},transformOrigin:{vertical:'top',horizontal:'center'},sx:sx,children:children});};export default Popover;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}