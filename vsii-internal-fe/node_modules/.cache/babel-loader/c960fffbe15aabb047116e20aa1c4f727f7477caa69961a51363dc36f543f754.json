{"ast": null, "code": "import * as React from 'react';\nimport { forwardRef, useContext } from 'react';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { MotionContext } from '../context/MotionContext/index.mjs';\nimport { useVisualElement } from './utils/use-visual-element.mjs';\nimport { useMotionRef } from './utils/use-motion-ref.mjs';\nimport { useCreateMotionContext } from '../context/MotionContext/create.mjs';\nimport { featureDefinitions } from './features/definitions.mjs';\nimport { loadFeatures } from './features/load-features.mjs';\nimport { isBrowser } from '../utils/is-browser.mjs';\nimport { useProjectionId } from '../projection/node/id.mjs';\nimport { LayoutGroupContext } from '../context/LayoutGroupContext.mjs';\nimport { VisualElementHandler } from './utils/VisualElementHandler.mjs';\nimport { LazyContext } from '../context/LazyContext.mjs';\nimport { SwitchLayoutGroupContext } from '../context/SwitchLayoutGroupContext.mjs';\nimport { motionComponentSymbol } from './utils/symbol.mjs';\n\n/**\n * Create a `motion` component.\n *\n * This function accepts a Component argument, which can be either a string (ie \"div\"\n * for `motion.div`), or an actual React component.\n *\n * Alongside this is a config option which provides a way of rendering the provided\n * component \"offline\", or outside the React render cycle.\n */\nfunction createMotionComponent(_ref) {\n  let {\n    preloadedFeatures,\n    createVisualElement,\n    projectionNodeConstructor,\n    useRender,\n    useVisualState,\n    Component\n  } = _ref;\n  preloadedFeatures && loadFeatures(preloadedFeatures);\n  function MotionComponent(props, externalRef) {\n    const configAndProps = {\n      ...useContext(MotionConfigContext),\n      ...props,\n      layoutId: useLayoutId(props)\n    };\n    const {\n      isStatic\n    } = configAndProps;\n    let features = null;\n    const context = useCreateMotionContext(props);\n    /**\n     * Create a unique projection ID for this component. If a new component is added\n     * during a layout animation we'll use this to query the DOM and hydrate its ref early, allowing\n     * us to measure it as soon as any layout effect flushes pending layout animations.\n     *\n     * Performance note: It'd be better not to have to search the DOM for these elements.\n     * For newly-entering components it could be enough to only correct treeScale, in which\n     * case we could mount in a scale-correction mode. This wouldn't be enough for\n     * shared element transitions however. Perhaps for those we could revert to a root node\n     * that gets forceRendered and layout animations are triggered on its layout effect.\n     */\n    const projectionId = isStatic ? undefined : useProjectionId();\n    /**\n     *\n     */\n    const visualState = useVisualState(props, isStatic);\n    if (!isStatic && isBrowser) {\n      /**\n       * Create a VisualElement for this component. A VisualElement provides a common\n       * interface to renderer-specific APIs (ie DOM/Three.js etc) as well as\n       * providing a way of rendering to these APIs outside of the React render loop\n       * for more performant animations and interactions\n       */\n      context.visualElement = useVisualElement(Component, visualState, configAndProps, createVisualElement);\n      /**\n       * Load Motion gesture and animation features. These are rendered as renderless\n       * components so each feature can optionally make use of React lifecycle methods.\n       */\n      const lazyStrictMode = useContext(LazyContext).strict;\n      const initialLayoutGroupConfig = useContext(SwitchLayoutGroupContext);\n      if (context.visualElement) {\n        features = context.visualElement.loadFeatures(\n        // Note: Pass the full new combined props to correctly re-render dynamic feature components.\n        configAndProps, lazyStrictMode, preloadedFeatures, projectionId, projectionNodeConstructor || featureDefinitions.projectionNodeConstructor, initialLayoutGroupConfig);\n      }\n    }\n    /**\n     * The mount order and hierarchy is specific to ensure our element ref\n     * is hydrated by the time features fire their effects.\n     */\n    return React.createElement(VisualElementHandler, {\n      visualElement: context.visualElement,\n      props: configAndProps\n    }, features, React.createElement(MotionContext.Provider, {\n      value: context\n    }, useRender(Component, props, projectionId, useMotionRef(visualState, context.visualElement, externalRef), visualState, isStatic, context.visualElement)));\n  }\n  const ForwardRefComponent = forwardRef(MotionComponent);\n  ForwardRefComponent[motionComponentSymbol] = Component;\n  return ForwardRefComponent;\n}\nfunction useLayoutId(_ref2) {\n  let {\n    layoutId\n  } = _ref2;\n  const layoutGroupId = useContext(LayoutGroupContext).id;\n  return layoutGroupId && layoutId !== undefined ? layoutGroupId + \"-\" + layoutId : layoutId;\n}\nexport { createMotionComponent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}