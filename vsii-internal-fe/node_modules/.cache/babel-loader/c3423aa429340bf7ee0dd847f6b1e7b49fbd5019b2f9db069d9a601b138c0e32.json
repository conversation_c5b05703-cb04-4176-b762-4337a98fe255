{"ast": null, "code": "// react\nimport React,{useEffect}from'react';// third party\nimport{useForm,useFieldArray,useWatch}from'react-hook-form';import{yupResolver}from'@hookform/resolvers/yup';import{FormattedMessage}from'react-intl';// material-ui\nimport{Grid,Stack,Button,Box}from'@mui/material';import{LoadingButton}from'@mui/lab';// project imports\nimport{overtimeReportSchema}from'pages/manage-ot-requests/Config';import{compensateTypeOptions,EApproveStatus,isApproved,isApprovedOrAwaiting,isAwaitingHR,isAwaitingQLKT,isAwaitingQLTT,isDeclined,MESSAGE_APPROVE_RESPONSE,overtimeReasonOptions,TEXT_CONFIG_SCREEN}from'constants/Common';import{closeConfirm,openConfirm}from'store/slice/confirmSlice';import{checkAllowedPermission}from'utils/authorization';import DirectApprover from'containers/search/DirectApprover';import DatePicker from'components/extended/Form/DatePicker';import{useAppSelector,useAppDispatch}from'app/hooks';import{openSnackbar}from'store/slice/snackbarSlice';import{FormProvider}from'components/extended/Form';import Select from'components/extended/Form/Select';import{authSelector}from'store/slice/authSlice';import OvertimeRecordRow from'./OvertimeRecordRow';import Input from'components/extended/Form/Input';import{PERMISSIONS}from'constants/Permission';import{checkIsHoliday}from'utils/common';import Modal from'components/extended/Modal';import sendRequest from'services/ApiService';import{Project}from'containers/search';import Api from'constants/Api';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const AddOrEditOvertimeReportModal=_ref=>{var _userInfo$role;let{isOpen,onClose,onSubmit,defaultValues,statusOt,isEdit,onReject,onSuccess,statusOverTimeTicket}=_ref;// Redux hooks\nconst dispatch=useAppDispatch();const{userInfo}=useAppSelector(authSelector);const{manageOt}=PERMISSIONS.workingCalendar;//Local state\nconst[loading,setLoading]=React.useState(false);const[holidays,setHolidays]=React.useState([]);const{manage_ot}=TEXT_CONFIG_SCREEN.workingCalendar;//Check\nconst userGroup=userInfo===null||userInfo===void 0?void 0:(_userInfo$role=userInfo.role)===null||_userInfo$role===void 0?void 0:_userInfo$role.map(item=>item.groupName);const isUserCreator=(userInfo===null||userInfo===void 0?void 0:userInfo.idHexString)===(defaultValues===null||defaultValues===void 0?void 0:defaultValues.userIdHexString);const isUserManager=(userInfo===null||userInfo===void 0?void 0:userInfo.idHexString)===(defaultValues===null||defaultValues===void 0?void 0:defaultValues.manager);const isUserNextManager=(userInfo===null||userInfo===void 0?void 0:userInfo.idHexString)===(defaultValues===null||defaultValues===void 0?void 0:defaultValues.nextManager);const isCBUser=userGroup===null||userGroup===void 0?void 0:userGroup.includes('C&B');const checkUserIsManagerApprove=()=>{if(isAwaitingQLTT(statusOverTimeTicket))return isUserManager;if(isAwaitingQLKT(statusOverTimeTicket))return isUserNextManager;if(isAwaitingHR(statusOverTimeTicket))return isCBUser;if(isApprovedOrAwaiting(statusOverTimeTicket)||isDeclined(statusOverTimeTicket)){return isUserManager||isUserNextManager||isCBUser;}return false;};const checkManager=()=>{if(!isEdit)return true;if(isUserCreator&&isApprovedOrAwaiting(statusOverTimeTicket))return true;if(checkUserIsManagerApprove()&&!isAwaitingQLTT(statusOverTimeTicket))return true;if(!checkUserIsManagerApprove()&&checkAllowedPermission(manageOt.view))return true;return false;};const isDisableStatus=isDeclined(statusOverTimeTicket)||isApprovedOrAwaiting(statusOverTimeTicket);const isCheckedApprove=()=>{return isEdit&&checkAllowedPermission(PERMISSIONS.workingCalendar.manageOt.approve)&&checkUserIsManagerApprove();};const showApproveButtons=isCheckedApprove()&&!isDeclined(statusOverTimeTicket)&&!isApproved(statusOverTimeTicket);const checkDisable=isEdit&&isUserCreator&&!(isAwaitingQLTT(statusOverTimeTicket)||isDeclined(statusOverTimeTicket))||!isUserCreator&&isDisableStatus;//Form setup\nconst methods=useForm({defaultValues,mode:'all',reValidateMode:'onChange',resolver:yupResolver(overtimeReportSchema),context:{currentUserId:(userInfo===null||userInfo===void 0?void 0:userInfo.idHexString)||''}});const{fields,remove,append}=useFieldArray({control:methods.control,name:'overtimeRecords'});const overtimeRecords=useWatch({control:methods.control,name:'overtimeRecords'});const watchCompensateType=methods.watch('compensateType');const watchovertimeReason=methods.watch('overtimeReason');const calculateEquivalentDays=totalCompensateHours=>{const rawDays=totalCompensateHours/4*0.5;const rounded=Math.round(rawDays*2)/2;return totalCompensateHours>0?Math.max(0.5,rounded):0;};//Reset form when defaultValues change\nuseEffect(()=>{if(isOpen){methods.reset(defaultValues);}// eslint-disable-next-line react-hooks/exhaustive-deps\n},[isOpen,defaultValues]);//Calculate total hours and compensation\nuseEffect(()=>{if(overtimeRecords){let totalCompensateHours=0;overtimeRecords.forEach(record=>{if(record!==null&&record!==void 0&&record.overtimeFrom&&record!==null&&record!==void 0&&record.overtimeHours){const fromDate=new Date(record.overtimeFrom);const toDate=record.overtimeTo?new Date(record.overtimeTo):fromDate;const hours=parseFloat(record.overtimeHours.toString())||0;// Tính cho từng bản ghi\nif(fromDate.toDateString()!==toDate.toDateString()){// làm thêm qua nhiều ngày\nconst daysDiff=Math.ceil((toDate.getTime()-fromDate.getTime())/(1000*60*60*24))+1;const hoursPerDay=hours/daysDiff;let currentDate=new Date(fromDate);while(currentDate<=toDate){const isWeekend=currentDate.getDay()===0||currentDate.getDay()===6;const isHoliday=checkIsHoliday(currentDate,holidays);const coefficient=isWeekend||isHoliday?2.2:1.5;totalCompensateHours+=hoursPerDay*coefficient;currentDate.setDate(currentDate.getDate()+1);}}else{// làm thêm trong 1 ngày\nconst isWeekend=fromDate.getDay()===0||fromDate.getDay()===6;const isHoliday=checkIsHoliday(fromDate,holidays);const coefficient=isWeekend||isHoliday?2.2:1.5;totalCompensateHours+=hours*coefficient;}}});const totalHours=overtimeRecords.reduce((sum,record)=>{var _record$overtimeHours;return sum+(parseFloat(((_record$overtimeHours=record.overtimeHours)===null||_record$overtimeHours===void 0?void 0:_record$overtimeHours.toString())||'0')||0);},0);const equivalentDays=calculateEquivalentDays(totalCompensateHours);methods.setValue('totalHours',totalHours.toString());methods.setValue('totalCompensateHours',totalCompensateHours.toFixed(2));methods.setValue('equivalentDays',equivalentDays.toFixed(1));}// eslint-disable-next-line react-hooks/exhaustive-deps\n},[overtimeRecords,holidays]);useEffect(()=>{fetchHolidayList();},[]);useEffect(()=>{if(overtimeRecords&&overtimeRecords.length>0){const earliestFrom=new Date(Math.min(...overtimeRecords.map(rec=>{var _rec$overtimeFrom;return new Date((_rec$overtimeFrom=rec.overtimeFrom)!==null&&_rec$overtimeFrom!==void 0?_rec$overtimeFrom:new Date()).getTime();})));const latestTo=new Date(Math.max(...overtimeRecords.map(rec=>{var _ref2,_rec$overtimeTo;return new Date((_ref2=(_rec$overtimeTo=rec.overtimeTo)!==null&&_rec$overtimeTo!==void 0?_rec$overtimeTo:rec.overtimeFrom)!==null&&_ref2!==void 0?_ref2:new Date()).getTime();})));methods.setValue('fromDate',earliestFrom);methods.setValue('toDate',latestTo);}// eslint-disable-next-line react-hooks/exhaustive-deps\n},[overtimeRecords]);//Event handlers\nconst handleSubmit=async formData=>{setLoading(true);try{var _formData$overtimeRec;await overtimeReportSchema.validate(formData,{context:{currentUserId:userInfo===null||userInfo===void 0?void 0:userInfo.idHexString}});const projectId=typeof formData.projectName==='object'&&formData.projectName!==null?formData.projectName.value:0;const apiPayload={...formData,idHexString:isEdit?defaultValues.idHexString:null,userIdHexString:isEdit?defaultValues.userIdHexString:(userInfo===null||userInfo===void 0?void 0:userInfo.idHexString)||'',projectId,projectName:formData.projectName,managerIdHexString:formData.manager,nextManagerIdHexString:formData.nextManager,overTimeType:formData.overtimeReason,detailReason:formData.detail,otherReason:formData.otherReason,hoursCompensation:formData.totalCompensateHours||0,daysCompensation:formData.equivalentDays||0,compensationLeaveType:formData.compensateType,leaveFrom:formData.compensateFrom,leaveTo:formData.compensateTo,status:'new',totalHours:formData.totalHours||0,overTimeHistory:((_formData$overtimeRec=formData.overtimeRecords)===null||_formData$overtimeRec===void 0?void 0:_formData$overtimeRec.map(rec=>({fromDate:rec.overtimeFrom,toDate:rec.overtimeTo,hours:rec.overtimeHours})))||[]};const apiConfig=isEdit?Api.overtime_report.updateDetailOvertimeTicket(defaultValues.idHexString):Api.overtime_report.postOvertimeTicket;const response=await sendRequest(apiConfig,apiPayload);dispatch(openSnackbar({open:true,message:(response===null||response===void 0?void 0:response.status)===true?(response===null||response===void 0?void 0:response.result.content.message)||'add-success':(response===null||response===void 0?void 0:response.result.content.message)||'overtime-request-error',variant:'alert',alert:{color:(response===null||response===void 0?void 0:response.status)===true?'success':'error'}}));if(response!==null&&response!==void 0&&response.status){await onSubmit(formData);onClose();}if(onSuccess)onSuccess();}catch(error){dispatch(openSnackbar({open:true,message:'overtime-request-error',variant:'alert',alert:{color:'error'}}));}finally{setLoading(false);}};const handleAddOvertimeRecord=()=>{append({overtimeFrom:new Date(),overtimeTo:new Date(),overtimeHours:''});};const fetchHolidayList=async()=>{try{const response=await sendRequest(Api.holiday.getAll,{page:1,size:100});if(response!==null&&response!==void 0&&response.status){const holidayDates=[];response.result.content.forEach(item=>{const from=new Date(item.fromDate);const to=item.toDate?new Date(item.toDate):from;let current=new Date(from);while(current<=to){const dateStr=\"\".concat(current.getDate().toString().padStart(2,'0'),\"/\").concat((current.getMonth()+1).toString().padStart(2,'0'),\"/\").concat(current.getFullYear());if(!holidayDates.includes(dateStr))holidayDates.push(dateStr);current.setDate(current.getDate()+1);}});setHolidays(holidayDates);}}catch(error){setHolidays([]);}};const handleOpenApproveConfirm=async()=>{const isValid=await methods.trigger();if(!isValid){return;}dispatch(openConfirm({title:/*#__PURE__*/_jsx(FormattedMessage,{id:\"confirm-information\"}),content:/*#__PURE__*/_jsx(FormattedMessage,{id:\"confirm-approve-overtime\"}),handleConfirm:async()=>{await handleApprove(EApproveStatus.APPROVED);dispatch(closeConfirm());}}));};const handleApprove=async status=>{try{const formValues=methods.getValues();const response=await sendRequest(Api.overtime_report.approvedOvertimeTicket(defaultValues.idHexString),{status:defaultValues.status,nextApproverIdHexString:defaultValues.status===EApproveStatus.AWAITING_QLTT?formValues.nextManager:null});dispatch(openSnackbar({open:true,message:(response===null||response===void 0?void 0:response.status)===true?(response===null||response===void 0?void 0:response.result.content.message)||MESSAGE_APPROVE_RESPONSE.SUCCESS:(response===null||response===void 0?void 0:response.result.content.message)||MESSAGE_APPROVE_RESPONSE.ERROR,variant:'alert',alert:{color:'success'}}));onClose();if(onSubmit){await onSubmit(methods.getValues());}if(onSuccess)onSuccess();}catch(error){dispatch(openSnackbar({open:true,message:'overtime-approve-error',variant:'alert',alert:{color:'error'}}));}};// Get reject leave data\nconst getRejectOtData=()=>{const overtimeRecords=methods.getValues('overtimeRecords');// Đảm bảo lấy đúng fromDate từ phần tử đầu tiên\nconst fromDate=overtimeRecords&&overtimeRecords.length>0?overtimeRecords[0].overtimeFrom:new Date();// Đảm bảo lấy đúng toDate từ phần tử cuối cùng\nconst toDate=overtimeRecords&&overtimeRecords.length>0?overtimeRecords[overtimeRecords.length-1].overtimeTo:new Date();// Tính tổng số giờ từ tất cả các bản ghi\nlet totalHours=0;if(overtimeRecords&&overtimeRecords.length>0){overtimeRecords.forEach(record=>{if(record!==null&&record!==void 0&&record.overtimeHours){const hoursValue=String(record.overtimeHours||'0');totalHours+=parseFloat(hoursValue)||0;}});}return{fromDate:fromDate,toDate:toDate,fullName:methods.getValues('fullName'),totalDays:totalHours.toString()};};// Handle open reject modal\nconst handleOpenRejectModal=()=>{if(onReject){const rejectData={...getRejectOtData(),leaveIdHexString:defaultValues.idHexString};onReject(rejectData);}onClose();};return/*#__PURE__*/_jsx(Modal,{isOpen:isOpen,onClose:onClose,title:isEdit&&isUserCreator?manage_ot+'edit-overtime-request':isEdit&&checkUserIsManagerApprove()?manage_ot+'approve-overtime-request':manage_ot+'overtime-modal-title',footer:/*#__PURE__*/_jsxs(Stack,{direction:\"row\",spacing:1,justifyContent:\"flex-end\",width:\"100%\",pl:2,children:[/*#__PURE__*/_jsx(Button,{onClick:onClose,color:\"error\",children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_ot+'cancel'})}),showApproveButtons&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Button,{variant:\"contained\",sx:{bgcolor:'rgba(161, 0, 0, 1)'},onClick:handleOpenRejectModal,children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_ot+'declines'})}),/*#__PURE__*/_jsx(LoadingButton,{loading:loading,variant:\"contained\",onClick:handleOpenApproveConfirm,children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_ot+'approve'})})]}),(!isEdit||isUserCreator&&(isAwaitingQLTT(statusOverTimeTicket)||isDeclined(statusOverTimeTicket)))&&/*#__PURE__*/_jsx(Button,{type:\"submit\",form:\"overtime-form\",variant:\"contained\",color:\"primary\",disabled:loading,onClick:async e=>{e.preventDefault();const isValid=await methods.trigger();if(!isValid){return;}methods.handleSubmit(handleSubmit)();},children:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_ot+'send-request'})})]}),children:/*#__PURE__*/_jsx(FormProvider,{formReturn:methods,children:/*#__PURE__*/_jsx(Box,{display:\"flex\",flexDirection:\"column\",children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,md:6,xs:12,children:/*#__PURE__*/_jsx(Input,{name:\"fullName\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_ot+'full-name'}),required:true,disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,md:6,xs:12,children:/*#__PURE__*/_jsx(Input,{name:\"department\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_ot+'department-overtime'}),required:true,disabled:true})}),checkManager()&&/*#__PURE__*/_jsx(Grid,{item:true,md:6,xs:12,children:/*#__PURE__*/_jsx(DirectApprover,{name:\"manager\",required:true,label:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_ot+'direct-manager-overtime'}),onChange:value=>methods.setValue('manager',value),disabled:checkDisable,idHexString:!isEdit?userInfo===null||userInfo===void 0?void 0:userInfo.idHexString:undefined})}),isEdit&&(!isUserCreator||isUserCreator&&!isAwaitingQLTT(statusOverTimeTicket)&&!isDeclined(statusOverTimeTicket))&&/*#__PURE__*/_jsx(Grid,{item:true,md:6,xs:12,children:/*#__PURE__*/_jsx(DirectApprover,{name:\"nextManager\",required:true,label:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_ot+'next-manager-overtime'}),onChange:value=>methods.setValue('nextManager',value),disabled:!isAwaitingQLTT(statusOverTimeTicket),idHexString:defaultValues.userIdHexString})}),(!isEdit||isAwaitingQLTT(statusOverTimeTicket)&&(isUserManager||isUserCreator)||isUserCreator&&isDeclined(statusOverTimeTicket))&&/*#__PURE__*/_jsx(Grid,{item:true,md:6,xs:12}),/*#__PURE__*/_jsx(Grid,{item:true,md:6,xs:12,children:/*#__PURE__*/_jsx(Select,{name:\"overtimeReason\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_ot+'overtime-reason'}),required:true,selects:overtimeReasonOptions,isMultipleLanguage:true,disabled:checkDisable})}),/*#__PURE__*/_jsx(Grid,{item:true,md:6,xs:12,children:watchovertimeReason==='overtime-reason-1'&&/*#__PURE__*/_jsx(Project,{name:\"projectName\",projectAuthorization:\"false\",isDefaultAll:true,required:true,handleChange:value=>methods.setValue('projectName',value,{shouldValidate:true}),disabled:checkDisable})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Input,{name:\"detail\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_ot+'overtime-detail'}),disabled:checkDisable})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Input,{name:\"otherReason\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_ot+'overtime-other-reason'}),disabled:checkDisable})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:fields.map((field,index)=>/*#__PURE__*/_jsx(OvertimeRecordRow,{field:field,index:index,remove:remove,onAdd:handleAddOvertimeRecord,totalRecords:fields.length,disabled:checkDisable},field.id))}),/*#__PURE__*/_jsx(Grid,{item:true,md:4,xs:12,children:/*#__PURE__*/_jsx(Input,{name:\"totalHours\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_ot+'overtime-total-hours'}),required:true,disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,md:4,xs:12,children:/*#__PURE__*/_jsx(Input,{name:\"totalCompensateHours\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_ot+'overtime-total-compensate-hours'}),required:true,disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,md:4,xs:12,children:/*#__PURE__*/_jsx(Input,{name:\"equivalentDays\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_ot+'overtime-equivalent-days'}),required:true,disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Select,{name:\"compensateType\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_ot+'compensate-type'}),required:true,selects:compensateTypeOptions,isMultipleLanguage:true,disabled:checkDisable})}),watchCompensateType==='overtime-compensate-1'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Grid,{item:true,md:6,xs:12,children:/*#__PURE__*/_jsx(DatePicker,{name:\"compensateFrom\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_ot+'compensate-from'}),disabled:checkDisable,required:true})}),/*#__PURE__*/_jsx(Grid,{item:true,md:6,xs:12,children:/*#__PURE__*/_jsx(DatePicker,{name:\"compensateTo\",label:/*#__PURE__*/_jsx(FormattedMessage,{id:manage_ot+'compensate-to'}),disabled:checkDisable,required:true})})]})]})})})});};export default AddOrEditOvertimeReportModal;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}