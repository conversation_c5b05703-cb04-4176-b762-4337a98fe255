{"ast": null, "code": "var e,\n  n,\n  t,\n  i,\n  r,\n  a = -1,\n  o = function (e) {\n    addEventListener(\"pageshow\", function (n) {\n      n.persisted && (a = n.timeStamp, e(n));\n    }, !0);\n  },\n  c = function () {\n    return window.performance && performance.getEntriesByType && performance.getEntriesByType(\"navigation\")[0];\n  },\n  u = function () {\n    var e = c();\n    return e && e.activationStart || 0;\n  },\n  f = function (e, n) {\n    var t = c(),\n      i = \"navigate\";\n    a >= 0 ? i = \"back-forward-cache\" : t && (document.prerendering || u() > 0 ? i = \"prerender\" : document.wasDiscarded ? i = \"restore\" : t.type && (i = t.type.replace(/_/g, \"-\")));\n    return {\n      name: e,\n      value: void 0 === n ? -1 : n,\n      rating: \"good\",\n      delta: 0,\n      entries: [],\n      id: \"v3-\".concat(Date.now(), \"-\").concat(Math.floor(8999999999999 * Math.random()) + 1e12),\n      navigationType: i\n    };\n  },\n  s = function (e, n, t) {\n    try {\n      if (PerformanceObserver.supportedEntryTypes.includes(e)) {\n        var i = new PerformanceObserver(function (e) {\n          Promise.resolve().then(function () {\n            n(e.getEntries());\n          });\n        });\n        return i.observe(Object.assign({\n          type: e,\n          buffered: !0\n        }, t || {})), i;\n      }\n    } catch (e) {}\n  },\n  d = function (e, n, t, i) {\n    var r, a;\n    return function (o) {\n      n.value >= 0 && (o || i) && ((a = n.value - (r || 0)) || void 0 === r) && (r = n.value, n.delta = a, n.rating = function (e, n) {\n        return e > n[1] ? \"poor\" : e > n[0] ? \"needs-improvement\" : \"good\";\n      }(n.value, t), e(n));\n    };\n  },\n  l = function (e) {\n    requestAnimationFrame(function () {\n      return requestAnimationFrame(function () {\n        return e();\n      });\n    });\n  },\n  p = function (e) {\n    var n = function (n) {\n      \"pagehide\" !== n.type && \"hidden\" !== document.visibilityState || e(n);\n    };\n    addEventListener(\"visibilitychange\", n, !0), addEventListener(\"pagehide\", n, !0);\n  },\n  v = function (e) {\n    var n = !1;\n    return function (t) {\n      n || (e(t), n = !0);\n    };\n  },\n  m = -1,\n  h = function () {\n    return \"hidden\" !== document.visibilityState || document.prerendering ? 1 / 0 : 0;\n  },\n  g = function (e) {\n    \"hidden\" === document.visibilityState && m > -1 && (m = \"visibilitychange\" === e.type ? e.timeStamp : 0, T());\n  },\n  y = function () {\n    addEventListener(\"visibilitychange\", g, !0), addEventListener(\"prerenderingchange\", g, !0);\n  },\n  T = function () {\n    removeEventListener(\"visibilitychange\", g, !0), removeEventListener(\"prerenderingchange\", g, !0);\n  },\n  E = function () {\n    return m < 0 && (m = h(), y(), o(function () {\n      setTimeout(function () {\n        m = h(), y();\n      }, 0);\n    })), {\n      get firstHiddenTime() {\n        return m;\n      }\n    };\n  },\n  C = function (e) {\n    document.prerendering ? addEventListener(\"prerenderingchange\", function () {\n      return e();\n    }, !0) : e();\n  },\n  L = [1800, 3e3],\n  w = function (e, n) {\n    n = n || {}, C(function () {\n      var t,\n        i = E(),\n        r = f(\"FCP\"),\n        a = s(\"paint\", function (e) {\n          e.forEach(function (e) {\n            \"first-contentful-paint\" === e.name && (a.disconnect(), e.startTime < i.firstHiddenTime && (r.value = Math.max(e.startTime - u(), 0), r.entries.push(e), t(!0)));\n          });\n        });\n      a && (t = d(e, r, L, n.reportAllChanges), o(function (i) {\n        r = f(\"FCP\"), t = d(e, r, L, n.reportAllChanges), l(function () {\n          r.value = performance.now() - i.timeStamp, t(!0);\n        });\n      }));\n    });\n  },\n  b = [.1, .25],\n  S = function (e, n) {\n    n = n || {}, w(v(function () {\n      var t,\n        i = f(\"CLS\", 0),\n        r = 0,\n        a = [],\n        c = function (e) {\n          e.forEach(function (e) {\n            if (!e.hadRecentInput) {\n              var n = a[0],\n                t = a[a.length - 1];\n              r && e.startTime - t.startTime < 1e3 && e.startTime - n.startTime < 5e3 ? (r += e.value, a.push(e)) : (r = e.value, a = [e]);\n            }\n          }), r > i.value && (i.value = r, i.entries = a, t());\n        },\n        u = s(\"layout-shift\", c);\n      u && (t = d(e, i, b, n.reportAllChanges), p(function () {\n        c(u.takeRecords()), t(!0);\n      }), o(function () {\n        r = 0, i = f(\"CLS\", 0), t = d(e, i, b, n.reportAllChanges), l(function () {\n          return t();\n        });\n      }), setTimeout(t, 0));\n    }));\n  },\n  A = {\n    passive: !0,\n    capture: !0\n  },\n  I = new Date(),\n  P = function (i, r) {\n    e || (e = r, n = i, t = new Date(), k(removeEventListener), F());\n  },\n  F = function () {\n    if (n >= 0 && n < t - I) {\n      var r = {\n        entryType: \"first-input\",\n        name: e.type,\n        target: e.target,\n        cancelable: e.cancelable,\n        startTime: e.timeStamp,\n        processingStart: e.timeStamp + n\n      };\n      i.forEach(function (e) {\n        e(r);\n      }), i = [];\n    }\n  },\n  M = function (e) {\n    if (e.cancelable) {\n      var n = (e.timeStamp > 1e12 ? new Date() : performance.now()) - e.timeStamp;\n      \"pointerdown\" == e.type ? function (e, n) {\n        var t = function () {\n            P(e, n), r();\n          },\n          i = function () {\n            r();\n          },\n          r = function () {\n            removeEventListener(\"pointerup\", t, A), removeEventListener(\"pointercancel\", i, A);\n          };\n        addEventListener(\"pointerup\", t, A), addEventListener(\"pointercancel\", i, A);\n      }(n, e) : P(n, e);\n    }\n  },\n  k = function (e) {\n    [\"mousedown\", \"keydown\", \"touchstart\", \"pointerdown\"].forEach(function (n) {\n      return e(n, M, A);\n    });\n  },\n  D = [100, 300],\n  x = function (t, r) {\n    r = r || {}, C(function () {\n      var a,\n        c = E(),\n        u = f(\"FID\"),\n        l = function (e) {\n          e.startTime < c.firstHiddenTime && (u.value = e.processingStart - e.startTime, u.entries.push(e), a(!0));\n        },\n        m = function (e) {\n          e.forEach(l);\n        },\n        h = s(\"first-input\", m);\n      a = d(t, u, D, r.reportAllChanges), h && p(v(function () {\n        m(h.takeRecords()), h.disconnect();\n      })), h && o(function () {\n        var o;\n        u = f(\"FID\"), a = d(t, u, D, r.reportAllChanges), i = [], n = -1, e = null, k(addEventListener), o = l, i.push(o), F();\n      });\n    });\n  },\n  B = 0,\n  R = 1 / 0,\n  H = 0,\n  N = function (e) {\n    e.forEach(function (e) {\n      e.interactionId && (R = Math.min(R, e.interactionId), H = Math.max(H, e.interactionId), B = H ? (H - R) / 7 + 1 : 0);\n    });\n  },\n  O = function () {\n    return r ? B : performance.interactionCount || 0;\n  },\n  q = function () {\n    \"interactionCount\" in performance || r || (r = s(\"event\", N, {\n      type: \"event\",\n      buffered: !0,\n      durationThreshold: 0\n    }));\n  },\n  j = [200, 500],\n  _ = 0,\n  z = function () {\n    return O() - _;\n  },\n  G = [],\n  J = {},\n  K = function (e) {\n    var n = G[G.length - 1],\n      t = J[e.interactionId];\n    if (t || G.length < 10 || e.duration > n.latency) {\n      if (t) t.entries.push(e), t.latency = Math.max(t.latency, e.duration);else {\n        var i = {\n          id: e.interactionId,\n          latency: e.duration,\n          entries: [e]\n        };\n        J[i.id] = i, G.push(i);\n      }\n      G.sort(function (e, n) {\n        return n.latency - e.latency;\n      }), G.splice(10).forEach(function (e) {\n        delete J[e.id];\n      });\n    }\n  },\n  Q = function (e, n) {\n    n = n || {}, C(function () {\n      var t;\n      q();\n      var i,\n        r = f(\"INP\"),\n        a = function (e) {\n          e.forEach(function (e) {\n            (e.interactionId && K(e), \"first-input\" === e.entryType) && !G.some(function (n) {\n              return n.entries.some(function (n) {\n                return e.duration === n.duration && e.startTime === n.startTime;\n              });\n            }) && K(e);\n          });\n          var n,\n            t = (n = Math.min(G.length - 1, Math.floor(z() / 50)), G[n]);\n          t && t.latency !== r.value && (r.value = t.latency, r.entries = t.entries, i());\n        },\n        c = s(\"event\", a, {\n          durationThreshold: null !== (t = n.durationThreshold) && void 0 !== t ? t : 40\n        });\n      i = d(e, r, j, n.reportAllChanges), c && (\"PerformanceEventTiming\" in window && \"interactionId\" in PerformanceEventTiming.prototype && c.observe({\n        type: \"first-input\",\n        buffered: !0\n      }), p(function () {\n        a(c.takeRecords()), r.value < 0 && z() > 0 && (r.value = 0, r.entries = []), i(!0);\n      }), o(function () {\n        G = [], _ = O(), r = f(\"INP\"), i = d(e, r, j, n.reportAllChanges);\n      }));\n    });\n  },\n  U = [2500, 4e3],\n  V = {},\n  W = function (e, n) {\n    n = n || {}, C(function () {\n      var t,\n        i = E(),\n        r = f(\"LCP\"),\n        a = function (e) {\n          var n = e[e.length - 1];\n          n && n.startTime < i.firstHiddenTime && (r.value = Math.max(n.startTime - u(), 0), r.entries = [n], t());\n        },\n        c = s(\"largest-contentful-paint\", a);\n      if (c) {\n        t = d(e, r, U, n.reportAllChanges);\n        var m = v(function () {\n          V[r.id] || (a(c.takeRecords()), c.disconnect(), V[r.id] = !0, t(!0));\n        });\n        [\"keydown\", \"click\"].forEach(function (e) {\n          addEventListener(e, function () {\n            return setTimeout(m, 0);\n          }, !0);\n        }), p(m), o(function (i) {\n          r = f(\"LCP\"), t = d(e, r, U, n.reportAllChanges), l(function () {\n            r.value = performance.now() - i.timeStamp, V[r.id] = !0, t(!0);\n          });\n        });\n      }\n    });\n  },\n  X = [800, 1800],\n  Y = function e(n) {\n    document.prerendering ? C(function () {\n      return e(n);\n    }) : \"complete\" !== document.readyState ? addEventListener(\"load\", function () {\n      return e(n);\n    }, !0) : setTimeout(n, 0);\n  },\n  Z = function (e, n) {\n    n = n || {};\n    var t = f(\"TTFB\"),\n      i = d(e, t, X, n.reportAllChanges);\n    Y(function () {\n      var r = c();\n      if (r) {\n        var a = r.responseStart;\n        if (a <= 0 || a > performance.now()) return;\n        t.value = Math.max(a - u(), 0), t.entries = [r], i(!0), o(function () {\n          t = f(\"TTFB\", 0), (i = d(e, t, X, n.reportAllChanges))(!0);\n        });\n      }\n    });\n  };\nexport { b as CLSThresholds, L as FCPThresholds, D as FIDThresholds, j as INPThresholds, U as LCPThresholds, X as TTFBThresholds, S as getCLS, w as getFCP, x as getFID, Q as getINP, W as getLCP, Z as getTTFB, S as onCLS, w as onFCP, x as onFID, Q as onINP, W as onLCP, Z as onTTFB };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}