{"ast": null, "code": "/* eslint-disable @typescript-eslint/no-unused-vars */\nimport * as React from 'react';\nlet warnedOnce = false;\nconst warn = () => {\n  if (!warnedOnce) {\n    console.warn(['MUI: The MobileDateRangePicker component was moved from `@mui/lab` to `@mui/x-date-pickers-pro`', '', \"You should use `import { MobileDateRangePicker } from '@mui/x-date-pickers-pro'`\", \"or `import { MobileDateRangePicker } from '@mui/x-date-pickers-pro/MobileDateRangePicker'`\", '', 'More information about this migration on our blog: https://mui.com/blog/lab-date-pickers-to-mui-x/.'].join('\\n'));\n    warnedOnce = true;\n  }\n};\n\n/**\n * @ignore - do not document.\n */\nconst MobileDateRangePicker = /*#__PURE__*/React.forwardRef(function DeprecatedMobileDateRangePicker() {\n  warn();\n  return null;\n});\nexport default MobileDateRangePicker;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}