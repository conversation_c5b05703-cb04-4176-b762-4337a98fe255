{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '@mui/material';\nexport const getDayPickerUtilityClass = slot => generateUtilityClass('MuiDayPicker', slot);\nexport const dayPickerClasses = generateUtilityClasses('MuiDayPicker', ['header', 'weekDayLabel', 'loadingContainer', 'slideTransition', 'monthContainer', 'weekContainer']);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}