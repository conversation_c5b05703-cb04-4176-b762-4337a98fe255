{"ast": null, "code": "import { AnimationType } from '../render/utils/types.mjs';\nimport { useDomEvent } from '../events/use-dom-event.mjs';\n\n/**\n *\n * @param props\n * @param ref\n * @internal\n */\nfunction useFocusGesture(_ref) {\n  let {\n    whileFocus,\n    visualElement\n  } = _ref;\n  const {\n    animationState\n  } = visualElement;\n  const onFocus = () => {\n    animationState && animationState.setActive(AnimationType.Focus, true);\n  };\n  const onBlur = () => {\n    animationState && animationState.setActive(AnimationType.Focus, false);\n  };\n  useDomEvent(visualElement, \"focus\", whileFocus ? onFocus : undefined);\n  useDomEvent(visualElement, \"blur\", whileFocus ? onBlur : undefined);\n}\nexport { useFocusGesture };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}