{"ast": null, "code": "var warning = function () {};\nvar invariant = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  warning = function (check, message) {\n    if (!check && typeof console !== 'undefined') {\n      console.warn(message);\n    }\n  };\n  invariant = function (check, message) {\n    if (!check) {\n      throw new Error(message);\n    }\n  };\n}\nexport { invariant, warning };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}