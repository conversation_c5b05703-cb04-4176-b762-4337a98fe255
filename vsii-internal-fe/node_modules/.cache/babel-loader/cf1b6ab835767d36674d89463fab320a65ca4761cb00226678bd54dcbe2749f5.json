{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nlet isCallingCanDrop = false;\nexport class DropTargetMonitorImpl {\n  receiveHandlerId(targetId) {\n    this.targetId = targetId;\n  }\n  getHandlerId() {\n    return this.targetId;\n  }\n  subscribeToStateChange(listener, options) {\n    return this.internalMonitor.subscribeToStateChange(listener, options);\n  }\n  canDrop() {\n    // Cut out early if the target id has not been set. This should prevent errors\n    // where the user has an older version of dnd-core like in\n    // https://github.com/react-dnd/react-dnd/issues/1310\n    if (!this.targetId) {\n      return false;\n    }\n    invariant(!isCallingCanDrop, 'You may not call monitor.canDrop() inside your canDrop() implementation. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor');\n    try {\n      isCallingCanDrop = true;\n      return this.internalMonitor.canDropOnTarget(this.targetId);\n    } finally {\n      isCallingCanDrop = false;\n    }\n  }\n  isOver(options) {\n    if (!this.targetId) {\n      return false;\n    }\n    return this.internalMonitor.isOverTarget(this.targetId, options);\n  }\n  getItemType() {\n    return this.internalMonitor.getItemType();\n  }\n  getItem() {\n    return this.internalMonitor.getItem();\n  }\n  getDropResult() {\n    return this.internalMonitor.getDropResult();\n  }\n  didDrop() {\n    return this.internalMonitor.didDrop();\n  }\n  getInitialClientOffset() {\n    return this.internalMonitor.getInitialClientOffset();\n  }\n  getInitialSourceClientOffset() {\n    return this.internalMonitor.getInitialSourceClientOffset();\n  }\n  getSourceClientOffset() {\n    return this.internalMonitor.getSourceClientOffset();\n  }\n  getClientOffset() {\n    return this.internalMonitor.getClientOffset();\n  }\n  getDifferenceFromInitialOffset() {\n    return this.internalMonitor.getDifferenceFromInitialOffset();\n  }\n  constructor(manager) {\n    this.targetId = null;\n    this.internalMonitor = manager.getMonitor();\n  }\n}\n\n//# sourceMappingURL=DropTargetMonitorImpl.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}