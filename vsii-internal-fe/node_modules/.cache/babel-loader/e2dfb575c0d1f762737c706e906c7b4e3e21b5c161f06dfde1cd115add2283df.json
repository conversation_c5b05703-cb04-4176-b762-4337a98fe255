{"ast": null, "code": "import { useState, useRef, useEffect } from 'react';\nfunction areInputsEqual(newInputs, lastInputs) {\n  if (newInputs.length !== lastInputs.length) {\n    return false;\n  }\n  for (var i = 0; i < newInputs.length; i++) {\n    if (newInputs[i] !== lastInputs[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction useMemoOne(getResult, inputs) {\n  var initial = useState(function () {\n    return {\n      inputs: inputs,\n      result: getResult()\n    };\n  })[0];\n  var isFirstRun = useRef(true);\n  var committed = useRef(initial);\n  var useCache = isFirstRun.current || Boolean(inputs && committed.current.inputs && areInputsEqual(inputs, committed.current.inputs));\n  var cache = useCache ? committed.current : {\n    inputs: inputs,\n    result: getResult()\n  };\n  useEffect(function () {\n    isFirstRun.current = false;\n    committed.current = cache;\n  }, [cache]);\n  return cache.result;\n}\nfunction useCallbackOne(callback, inputs) {\n  return useMemoOne(function () {\n    return callback;\n  }, inputs);\n}\nvar useMemo = useMemoOne;\nvar useCallback = useCallbackOne;\nexport { useCallback, useCallbackOne, useMemo, useMemoOne };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}