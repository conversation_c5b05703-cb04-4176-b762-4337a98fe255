{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useControlled as useControlled, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { useButton } from '../ButtonUnstyled';\nimport { useListbox, defaultListboxReducer, ActionTypes } from '../ListboxUnstyled';\nimport defaultOptionStringifier from './defaultOptionStringifier';\nfunction useSelect(props) {\n  const {\n    buttonRef: buttonRefProp,\n    defaultValue,\n    disabled = false,\n    listboxId,\n    listboxRef: listboxRefProp,\n    multiple = false,\n    onChange,\n    onOpenChange,\n    open = false,\n    options,\n    optionStringifier = defaultOptionStringifier,\n    value: valueProp\n  } = props;\n  const buttonRef = React.useRef(null);\n  const handleButtonRef = useForkRef(buttonRefProp, buttonRef);\n  const listboxRef = React.useRef(null);\n  const [value, setValue] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'SelectUnstyled',\n    state: 'value'\n  }); // prevents closing the listbox on keyUp right after opening it\n\n  const ignoreEnterKeyUp = React.useRef(false); // prevents reopening the listbox when button is clicked\n  // (listbox closes on lost focus, then immediately reopens on click)\n\n  const ignoreClick = React.useRef(false); // Ensure the listbox is focused after opening\n\n  const [listboxFocusRequested, requestListboxFocus] = React.useState(false);\n  const focusListboxIfRequested = React.useCallback(() => {\n    if (listboxFocusRequested && listboxRef.current != null) {\n      listboxRef.current.focus();\n      requestListboxFocus(false);\n    }\n  }, [listboxFocusRequested]);\n  const updateListboxRef = listboxElement => {\n    listboxRef.current = listboxElement;\n    focusListboxIfRequested();\n  };\n  const handleListboxRef = useForkRef(useForkRef(listboxRefProp, listboxRef), updateListboxRef);\n  React.useEffect(() => {\n    focusListboxIfRequested();\n  }, [focusListboxIfRequested]);\n  React.useEffect(() => {\n    requestListboxFocus(open);\n  }, [open]);\n  const createHandleMouseDown = otherHandlers => event => {\n    var _otherHandlers$onMous;\n    otherHandlers == null ? void 0 : (_otherHandlers$onMous = otherHandlers.onMouseDown) == null ? void 0 : _otherHandlers$onMous.call(otherHandlers, event);\n    if (!event.defaultPrevented && open) {\n      ignoreClick.current = true;\n    }\n  };\n  const createHandleButtonClick = otherHandlers => event => {\n    var _otherHandlers$onClic;\n    otherHandlers == null ? void 0 : (_otherHandlers$onClic = otherHandlers.onClick) == null ? void 0 : _otherHandlers$onClic.call(otherHandlers, event);\n    if (!event.defaultPrevented && !ignoreClick.current) {\n      onOpenChange == null ? void 0 : onOpenChange(!open);\n    }\n    ignoreClick.current = false;\n  };\n  const createHandleButtonKeyDown = otherHandlers => event => {\n    var _otherHandlers$onKeyD;\n    otherHandlers == null ? void 0 : (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null ? void 0 : _otherHandlers$onKeyD.call(otherHandlers, event);\n    if (event.defaultPrevented) {\n      return;\n    }\n    if (event.key === 'Enter') {\n      ignoreEnterKeyUp.current = true;\n    }\n    if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {\n      event.preventDefault();\n      onOpenChange == null ? void 0 : onOpenChange(true);\n    }\n  };\n  const createHandleListboxKeyUp = otherHandlers => event => {\n    var _otherHandlers$onKeyU;\n    otherHandlers == null ? void 0 : (_otherHandlers$onKeyU = otherHandlers.onKeyUp) == null ? void 0 : _otherHandlers$onKeyU.call(otherHandlers, event);\n    if (event.defaultPrevented) {\n      return;\n    }\n    const closingKeys = multiple ? ['Escape'] : ['Escape', 'Enter', ' '];\n    if (open && !ignoreEnterKeyUp.current && closingKeys.includes(event.key)) {\n      var _buttonRef$current;\n      buttonRef == null ? void 0 : (_buttonRef$current = buttonRef.current) == null ? void 0 : _buttonRef$current.focus();\n    }\n    ignoreEnterKeyUp.current = false;\n  };\n  const createHandleListboxItemClick = otherHandlers => event => {\n    var _otherHandlers$onClic2;\n    otherHandlers == null ? void 0 : (_otherHandlers$onClic2 = otherHandlers.onClick) == null ? void 0 : _otherHandlers$onClic2.call(otherHandlers, event);\n    if (event.defaultPrevented) {\n      return;\n    }\n    if (!multiple) {\n      onOpenChange == null ? void 0 : onOpenChange(false);\n    }\n  };\n  const createHandleListboxBlur = otherHandlers => event => {\n    var _otherHandlers$onBlur;\n    otherHandlers == null ? void 0 : (_otherHandlers$onBlur = otherHandlers.onBlur) == null ? void 0 : _otherHandlers$onBlur.call(otherHandlers, event);\n    if (!event.defaultPrevented) {\n      onOpenChange == null ? void 0 : onOpenChange(false);\n    }\n  };\n  const listboxReducer = (state, action) => {\n    const newState = defaultListboxReducer(state, action); // change selection when listbox is closed\n\n    if (action.type === ActionTypes.keyDown && !open && (action.event.key === 'ArrowUp' || action.event.key === 'ArrowDown')) {\n      return _extends({}, newState, {\n        selectedValue: newState.highlightedValue\n      });\n    }\n    if (action.type === ActionTypes.blur || action.type === ActionTypes.setValue || action.type === ActionTypes.optionsChange) {\n      return _extends({}, newState, {\n        highlightedValue: newState.selectedValue\n      });\n    }\n    return newState;\n  };\n  const {\n    getRootProps: getButtonRootProps,\n    active: buttonActive,\n    focusVisible: buttonFocusVisible\n  } = useButton({\n    disabled,\n    ref: handleButtonRef\n  });\n  const selectedOption = React.useMemo(() => {\n    var _props$options$find;\n    return props.multiple ? props.options.filter(o => value.includes(o.value)) : (_props$options$find = props.options.find(o => o.value === value)) != null ? _props$options$find : null;\n  }, [props.multiple, props.options, value]);\n  let useListboxParameters;\n  if (props.multiple) {\n    useListboxParameters = {\n      id: listboxId,\n      isOptionDisabled: o => {\n        var _o$disabled;\n        return (_o$disabled = o == null ? void 0 : o.disabled) != null ? _o$disabled : false;\n      },\n      optionComparer: (o, v) => (o == null ? void 0 : o.value) === (v == null ? void 0 : v.value),\n      listboxRef: handleListboxRef,\n      multiple: true,\n      onChange: newOptions => {\n        const newValues = newOptions.map(o => o.value);\n        setValue(newValues);\n        onChange == null ? void 0 : onChange(newValues);\n      },\n      options,\n      optionStringifier,\n      value: selectedOption\n    };\n  } else {\n    useListboxParameters = {\n      id: listboxId,\n      isOptionDisabled: o => {\n        var _o$disabled2;\n        return (_o$disabled2 = o == null ? void 0 : o.disabled) != null ? _o$disabled2 : false;\n      },\n      optionComparer: (o, v) => (o == null ? void 0 : o.value) === (v == null ? void 0 : v.value),\n      listboxRef: handleListboxRef,\n      multiple: false,\n      onChange: option => {\n        var _option$value, _option$value2;\n        setValue((_option$value = option == null ? void 0 : option.value) != null ? _option$value : null);\n        onChange == null ? void 0 : onChange((_option$value2 = option == null ? void 0 : option.value) != null ? _option$value2 : null);\n      },\n      options,\n      optionStringifier,\n      stateReducer: listboxReducer,\n      value: selectedOption\n    };\n  }\n  const {\n    getRootProps: getListboxRootProps,\n    getOptionProps: getListboxOptionProps,\n    getOptionState,\n    highlightedOption,\n    selectedOption: listboxSelectedOption\n  } = useListbox(useListboxParameters);\n  const getButtonProps = function () {\n    let otherHandlers = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    return _extends({}, getButtonRootProps(_extends({}, otherHandlers, {\n      onClick: createHandleButtonClick(otherHandlers),\n      onMouseDown: createHandleMouseDown(otherHandlers),\n      onKeyDown: createHandleButtonKeyDown(otherHandlers)\n    })), {\n      'aria-expanded': open,\n      'aria-haspopup': 'listbox'\n    });\n  };\n  const getListboxProps = function () {\n    let otherHandlers = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    return getListboxRootProps(_extends({}, otherHandlers, {\n      onBlur: createHandleListboxBlur(otherHandlers),\n      onKeyUp: createHandleListboxKeyUp(otherHandlers)\n    }));\n  };\n  const getOptionProps = function (option) {\n    let otherHandlers = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    return getListboxOptionProps(option, _extends({}, otherHandlers, {\n      onClick: createHandleListboxItemClick(otherHandlers)\n    }));\n  };\n  React.useDebugValue({\n    selectedOption: listboxSelectedOption,\n    highlightedOption,\n    open\n  });\n  return {\n    buttonActive,\n    buttonFocusVisible,\n    disabled,\n    getButtonProps,\n    getListboxProps,\n    getOptionProps,\n    getOptionState,\n    open,\n    value\n  };\n}\nexport default useSelect;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}