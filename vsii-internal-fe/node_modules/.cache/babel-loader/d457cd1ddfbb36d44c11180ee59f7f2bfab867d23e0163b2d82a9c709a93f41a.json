{"ast": null, "code": "import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getTabUnstyledUtilityClass(slot) {\n  return generateUtilityClass('TabUnstyled', slot);\n}\nconst tabUnstyledClasses = generateUtilityClasses('TabUnstyled', ['root', 'selected', 'disabled']);\nexport default tabUnstyledClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}