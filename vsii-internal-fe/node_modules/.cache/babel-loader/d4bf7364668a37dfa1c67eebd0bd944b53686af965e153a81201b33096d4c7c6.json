{"ast": null, "code": "import { getNamedFormat, filterProps } from './utils';\nimport { FormatError, ErrorCode } from 'intl-messageformat';\nimport { IntlFormatError } from './error';\nvar RELATIVE_TIME_FORMAT_OPTIONS = ['numeric', 'style'];\nfunction getFormatter(_a, getRelativeTimeFormat, options) {\n  var locale = _a.locale,\n    formats = _a.formats,\n    onError = _a.onError;\n  if (options === void 0) {\n    options = {};\n  }\n  var format = options.format;\n  var defaults = !!format && getNamedFormat(formats, 'relative', format, onError) || {};\n  var filteredOptions = filterProps(options, RELATIVE_TIME_FORMAT_OPTIONS, defaults);\n  return getRelativeTimeFormat(locale, filteredOptions);\n}\nexport function formatRelativeTime(config, getRelativeTimeFormat, value, unit, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  if (!unit) {\n    unit = 'second';\n  }\n  var RelativeTimeFormat = Intl.RelativeTimeFormat;\n  if (!RelativeTimeFormat) {\n    config.onError(new FormatError(\"Intl.RelativeTimeFormat is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-relativetimeformat\\\"\\n\", ErrorCode.MISSING_INTL_API));\n  }\n  try {\n    return getFormatter(config, getRelativeTimeFormat, options).format(value, unit);\n  } catch (e) {\n    config.onError(new IntlFormatError('Error formatting relative time.', config.locale, e));\n  }\n  return String(value);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}