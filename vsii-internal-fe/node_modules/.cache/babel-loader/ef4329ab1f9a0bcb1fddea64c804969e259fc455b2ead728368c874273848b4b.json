{"ast": null, "code": "import { PartitionNumberPattern } from './PartitionNumberPattern';\nimport { ArrayCreate } from '../262';\nexport function FormatNumericToParts(nf, x, implDetails) {\n  var parts = PartitionNumberPattern(nf, x, implDetails);\n  var result = ArrayCreate(0);\n  for (var _i = 0, parts_1 = parts; _i < parts_1.length; _i++) {\n    var part = parts_1[_i];\n    result.push({\n      type: part.type,\n      value: part.value\n    });\n  }\n  return result;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}