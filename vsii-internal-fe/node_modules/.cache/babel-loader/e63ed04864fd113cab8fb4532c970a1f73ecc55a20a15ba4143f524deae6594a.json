{"ast": null, "code": "import{useEffect,useState}from'react';import{useSearchParams}from'react-router-dom';// material-ui\n// project imports\nimport MainCard from'components/cards/MainCard';import{SEARCH_PARAM_KEY}from'constants/Common';import{getSearchParam,transformObject}from'utils/common';import{budgetingPlanSearchConfig}from'./Config';import{Table}from'components/extended/Table';import Api from'constants/Api';import{BudgetingPlanSearch,BudgetingPlanTBody,BudgetingPlanThead,OnGoingTotal}from'containers/sales';import EditBudgetingPlan from'containers/sales/EditBudgetingPlan';import{FilterCollapse}from'containers/search';import sendRequest from'services/ApiService';import{useAppDispatch}from'app/hooks';import{openSnackbar}from'store/slice/snackbarSlice';import{closeConfirm}from'store/slice/confirmSlice';import useConfig from'hooks/useConfig';import{PERMISSIONS}from'constants/Permission';import{checkAllowedPermission}from'utils/authorization';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const BudgetingPlan=()=>{const[loading,setLoading]=useState(false);const[searchParams,setSearchParams]=useSearchParams();const keyParams=[SEARCH_PARAM_KEY.year,SEARCH_PARAM_KEY.type,SEARCH_PARAM_KEY.pipelineType];const params=getSearchParam(keyParams,searchParams);transformObject(params);const dispatch=useAppDispatch();const{locale}=useConfig();const defaultConditions={...budgetingPlanSearchConfig,...params,language:locale};const[formReset]=useState(defaultConditions);const[conditions,setConditions]=useState(defaultConditions);const[budgetingPlans,setBudgetingPlans]=useState([]);const[budgetingPlan,setBudgetingPlan]=useState();const[total,setTotal]=useState([]);const[open,setOpen]=useState(false);const[editLoading,setEditLoading]=useState(false);const[isEdit,setIsEdit]=useState(false);const[isEditedTotalList,setIsEditedTotalList]=useState(false);const{budgetingPermission}=PERMISSIONS.sale.salePipeline;const getDataTable=async()=>{setLoading(true);const response=await sendRequest(Api.budgeting_plan.getAll,{...conditions});if(response){const{status,result}=response;if(status&&result){setLoading(false);const{data}=result.content;setBudgetingPlans(data);}else{setDataEmpty();}}else{setDataEmpty();}};const getTotal=async()=>{setLoading(true);const response=await sendRequest(Api.budgeting_plan.getTotal,{...conditions});if(response){const{status,result}=response;if(status&&result){const{total}=result.content;setTotal(total.filter(item=>item.show));setLoading(false);}else{setTotal([]);setLoading(false);}}else{setTotal([]);setLoading(false);}};const setDataEmpty=()=>{setBudgetingPlans([]);setLoading(false);};// Event\nconst handleOpenDialog=item=>{setIsEdit(item?true:false);setBudgetingPlan(item);setOpen(true);};const handleCloseDialog=()=>{setOpen(false);};// Handle submit\nconst handleSearch=value=>{transformObject(value);setSearchParams(value);setConditions({...value});};const handleEditBudgetingPlan=async payload=>{setEditLoading(true);const response=await sendRequest(Api.budgeting_plan.editBudgetingPlan,payload);if(response){if(response.status){setEditLoading(false);setOpen(false);getDataTable();dispatch(openSnackbar({open:true,message:'update-success',variant:'alert',alert:{color:'success'}}));}else{setEditLoading(false);}}else{setEditLoading(false);}dispatch(closeConfirm());};const hanldeConfirmEditList=async list=>{setLoading(true);const res=await sendRequest(Api.flexible_report.editArrangement,list);dispatch(openSnackbar({open:true,message:res.status?'update-success':'update-fail',variant:'alert',alert:{color:res.status?'success':'error'}}));if(res.status){setTotal(list);setIsEditedTotalList(false);}setLoading(false);};// Effects\nuseEffect(()=>{getDataTable();getTotal();// eslint-disable-next-line react-hooks/exhaustive-deps\n},[conditions]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(FilterCollapse,{children:/*#__PURE__*/_jsx(BudgetingPlanSearch,{formReset:formReset,handleSearch:handleSearch})}),(total===null||total===void 0?void 0:total.length)>0&&/*#__PURE__*/_jsx(OnGoingTotal,{total:total,loading:loading,isEdited:isEditedTotalList,setIsEdited:setIsEditedTotalList,handleConFirmEdit:checkAllowedPermission(budgetingPermission.editRows)?hanldeConfirmEditList:undefined}),/*#__PURE__*/_jsx(MainCard,{children:/*#__PURE__*/_jsx(Table,{heads:/*#__PURE__*/_jsx(BudgetingPlanThead,{}),isLoading:loading,data:budgetingPlans,children:/*#__PURE__*/_jsx(BudgetingPlanTBody,{handleOpen:handleOpenDialog,data:budgetingPlans})})}),open&&/*#__PURE__*/_jsx(EditBudgetingPlan,{open:open,isEdit:isEdit,budgetingPlan:budgetingPlan,handleClose:handleCloseDialog,loading:editLoading,editBudgetingPlan:handleEditBudgetingPlan})]});};export default BudgetingPlan;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}