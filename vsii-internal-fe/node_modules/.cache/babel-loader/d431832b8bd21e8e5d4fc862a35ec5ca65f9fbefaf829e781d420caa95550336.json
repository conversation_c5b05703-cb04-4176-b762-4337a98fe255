{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport Tab from '@mui/material/Tab';\nimport Tabs, { tabsClasses } from '@mui/material/Tabs';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses } from '@mui/material';\nimport { Time, DateRange } from '../internals/components/icons';\nimport { WrapperVariantContext } from '../internals/components/wrappers/WrapperVariantContext';\nimport { useLocaleText } from '../internals/hooks/useUtils';\nimport { getDateTimePickerTabsUtilityClass } from './dateTimePickerTabsClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst viewToTab = openView => {\n  if (['day', 'month', 'year'].includes(openView)) {\n    return 'date';\n  }\n  return 'time';\n};\nconst tabToView = tab => {\n  if (tab === 'date') {\n    return 'day';\n  }\n  return 'hours';\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getDateTimePickerTabsUtilityClass, classes);\n};\nconst DateTimePickerTabsRoot = styled(Tabs, {\n  name: 'MuiDateTimePickerTabs',\n  slot: 'Root',\n  overridesResolver: (_, styles) => styles.root\n})(_ref => {\n  let {\n    ownerState,\n    theme\n  } = _ref;\n  return _extends({\n    boxShadow: \"0 -1px 0 0 inset \".concat(theme.palette.divider)\n  }, ownerState.wrapperVariant === 'desktop' && {\n    order: 1,\n    boxShadow: \"0 1px 0 0 inset \".concat(theme.palette.divider),\n    [\"& .\".concat(tabsClasses.indicator)]: {\n      bottom: 'auto',\n      top: 0\n    }\n  });\n});\nconst DateTimePickerTabs = function DateTimePickerTabs(inProps) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiDateTimePickerTabs'\n  });\n  const {\n    dateRangeIcon = /*#__PURE__*/_jsx(DateRange, {}),\n    onChange,\n    timeIcon = /*#__PURE__*/_jsx(Time, {}),\n    view\n  } = props;\n  const localeText = useLocaleText();\n  const wrapperVariant = React.useContext(WrapperVariantContext);\n  const ownerState = _extends({}, props, {\n    wrapperVariant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const handleChange = (event, value) => {\n    onChange(tabToView(value));\n  };\n  return /*#__PURE__*/_jsxs(DateTimePickerTabsRoot, {\n    ownerState: ownerState,\n    variant: \"fullWidth\",\n    value: viewToTab(view),\n    onChange: handleChange,\n    className: classes.root,\n    children: [/*#__PURE__*/_jsx(Tab, {\n      value: \"date\",\n      \"aria-label\": localeText.dateTableLabel,\n      icon: /*#__PURE__*/_jsx(React.Fragment, {\n        children: dateRangeIcon\n      })\n    }), /*#__PURE__*/_jsx(Tab, {\n      value: \"time\",\n      \"aria-label\": localeText.timeTableLabel,\n      icon: /*#__PURE__*/_jsx(React.Fragment, {\n        children: timeIcon\n      })\n    })]\n  });\n};\nprocess.env.NODE_ENV !== \"production\" ? DateTimePickerTabs.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * Date tab icon.\n   * @default DateRange\n   */\n  dateRangeIcon: PropTypes.node,\n  /**\n   * Callback called when tab is clicked\n   * @param {CalendarOrClockPickerView} view Picker view that was clicked\n   */\n  onChange: PropTypes.func.isRequired,\n  /**\n   * Time tab icon.\n   * @default Time\n   */\n  timeIcon: PropTypes.node,\n  /**\n   * Open picker view\n   */\n  view: PropTypes.oneOf(['day', 'hours', 'minutes', 'month', 'seconds', 'year']).isRequired\n} : void 0;\nexport { DateTimePickerTabs };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}