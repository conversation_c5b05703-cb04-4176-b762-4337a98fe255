{"ast": null, "code": "import { defaults, noop, time } from '@motionone/utils';\nimport { stopAnimation } from './stop-animation.es.js';\nconst createAnimation = factory => factory();\nconst withControls = function (animationFactory, options) {\n  let duration = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaults.duration;\n  return new Proxy({\n    animations: animationFactory.map(createAnimation).filter(Boolean),\n    duration,\n    options\n  }, controls);\n};\n/**\n * TODO:\n * Currently this returns the first animation, ideally it would return\n * the first active animation.\n */\nconst getActiveAnimation = state => state.animations[0];\nconst controls = {\n  get: (target, key) => {\n    const activeAnimation = getActiveAnimation(target);\n    switch (key) {\n      case \"duration\":\n        return target.duration;\n      case \"currentTime\":\n        return time.s((activeAnimation === null || activeAnimation === void 0 ? void 0 : activeAnimation[key]) || 0);\n      case \"playbackRate\":\n      case \"playState\":\n        return activeAnimation === null || activeAnimation === void 0 ? void 0 : activeAnimation[key];\n      case \"finished\":\n        if (!target.finished) {\n          target.finished = Promise.all(target.animations.map(selectFinished)).catch(noop);\n        }\n        return target.finished;\n      case \"stop\":\n        return () => {\n          target.animations.forEach(animation => stopAnimation(animation));\n        };\n      case \"forEachNative\":\n        /**\n         * This is for internal use only, fire a callback for each\n         * underlying animation.\n         */\n        return callback => {\n          target.animations.forEach(animation => callback(animation, target));\n        };\n      default:\n        return typeof (activeAnimation === null || activeAnimation === void 0 ? void 0 : activeAnimation[key]) === \"undefined\" ? undefined : () => target.animations.forEach(animation => animation[key]());\n    }\n  },\n  set: (target, key, value) => {\n    switch (key) {\n      case \"currentTime\":\n        value = time.ms(value);\n      // Fall-through\n      case \"playbackRate\":\n        for (let i = 0; i < target.animations.length; i++) {\n          target.animations[i][key] = value;\n        }\n        return true;\n    }\n    return false;\n  }\n};\nconst selectFinished = animation => animation.finished;\nexport { controls, withControls };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}