{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/extended/Form/FormProvider.tsx\",\n  _s = $RefreshSig$();\n/* eslint-disable prettier/prettier */\nimport React from 'react';\n\n// react-hook-form\nimport { FormProvider as RHFProvider, useForm } from 'react-hook-form';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction FormProvider(props) {\n  _s();\n  var _React$Children;\n  const {\n    children,\n    onSubmit,\n    form,\n    formReset,\n    formReturn,\n    ...other\n  } = props;\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  const methods = formReturn ? {\n    ...formReturn\n  } : useForm({\n    ...form,\n    mode: 'all'\n  });\n  React.useEffect(() => {\n    methods.reset(formReset);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [formReset]);\n  return /*#__PURE__*/_jsxDEV(RHFProvider, {\n    ...methods,\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: methods.handleSubmit(onSubmit),\n      ...other,\n      children: (_React$Children = React.Children) === null || _React$Children === void 0 ? void 0 : _React$Children.map(children, child => {\n        var _child$props;\n        return child !== null && child !== void 0 && (_child$props = child.props) !== null && _child$props !== void 0 && _child$props.name ? /*#__PURE__*/React.createElement(child.type, {\n          ...{\n            key: child.props.name,\n            ...methods.register,\n            ...child.props\n          }\n        }) : child;\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 9\n  }, this);\n}\n_s(FormProvider, \"6V6hAMD+n60WiqwTS4Q1skgSnyM=\", false, function () {\n  return [useForm];\n});\n_c = FormProvider;\nexport default FormProvider;\nvar _c;\n$RefreshReg$(_c, \"FormProvider\");", "map": {"version": 3, "names": ["React", "FormProvider", "RHFProvider", "useForm", "jsxDEV", "_jsxDEV", "props", "_s", "_React$Children", "children", "onSubmit", "form", "formReset", "formReturn", "other", "methods", "mode", "useEffect", "reset", "handleSubmit", "Children", "map", "child", "_child$props", "name", "createElement", "type", "key", "register", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/extended/Form/FormProvider.tsx"], "sourcesContent": ["/* eslint-disable prettier/prettier */\nimport React from 'react';\n\n// react-hook-form\nimport { FieldValues, FormProvider as R<PERSON><PERSON><PERSON><PERSON>, SubmitHandler, useForm, UseFormProps, UseFormReturn } from 'react-hook-form';\n\ninterface IFormProviderProps<T extends FieldValues> extends Omit<React.FormHTMLAttributes<HTMLFormElement>, 'onSubmit'> {\n    form?: UseFormProps<T>;\n    formReturn?: UseFormReturn<T>;\n    onSubmit?: SubmitHandler<T>;\n    children: React.ReactElement | React.ReactElement[] | any;\n    formReset?: T;\n}\nfunction FormProvider<T extends FieldValues>(props: IFormProviderProps<T>): JSX.Element {\n    const { children, onSubmit, form, formReset, formReturn, ...other } = props;\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const methods = formReturn ? { ...formReturn } : useForm<T>({ ...form, mode: 'all' });\n\n    React.useEffect(() => {\n        methods.reset(formReset);\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [formReset]);\n\n    return (\n        <RHFProvider {...methods}>\n            <form onSubmit={methods.handleSubmit(onSubmit!)} {...other}>\n                {React.Children?.map(children, (child) => {\n                    return child?.props?.name\n                        ? React.createElement<T>(child.type, {\n                              ...{\n                                  key: child.props.name,\n                                  ...methods.register,\n                                  ...child.props\n                              }\n                          })\n                        : child;\n                })}\n            </form>\n        </RHFProvider>\n    );\n}\n\nexport default FormProvider;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,SAAsBC,YAAY,IAAIC,WAAW,EAAiBC,OAAO,QAAqC,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAShI,SAASJ,YAAYA,CAAwBK,KAA4B,EAAe;EAAAC,EAAA;EAAA,IAAAC,eAAA;EACpF,MAAM;IAAEC,QAAQ;IAAEC,QAAQ;IAAEC,IAAI;IAAEC,SAAS;IAAEC,UAAU;IAAE,GAAGC;EAAM,CAAC,GAAGR,KAAK;EAC3E;EACA,MAAMS,OAAO,GAAGF,UAAU,GAAG;IAAE,GAAGA;EAAW,CAAC,GAAGV,OAAO,CAAI;IAAE,GAAGQ,IAAI;IAAEK,IAAI,EAAE;EAAM,CAAC,CAAC;EAErFhB,KAAK,CAACiB,SAAS,CAAC,MAAM;IAClBF,OAAO,CAACG,KAAK,CAACN,SAAS,CAAC;IACxB;EACJ,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAEf,oBACIP,OAAA,CAACH,WAAW;IAAA,GAAKa,OAAO;IAAAN,QAAA,eACpBJ,OAAA;MAAMK,QAAQ,EAAEK,OAAO,CAACI,YAAY,CAACT,QAAS,CAAE;MAAA,GAAKI,KAAK;MAAAL,QAAA,GAAAD,eAAA,GACrDR,KAAK,CAACoB,QAAQ,cAAAZ,eAAA,uBAAdA,eAAA,CAAgBa,GAAG,CAACZ,QAAQ,EAAGa,KAAK,IAAK;QAAA,IAAAC,YAAA;QACtC,OAAOD,KAAK,aAALA,KAAK,gBAAAC,YAAA,GAALD,KAAK,CAAEhB,KAAK,cAAAiB,YAAA,eAAZA,YAAA,CAAcC,IAAI,gBACnBxB,KAAK,CAACyB,aAAa,CAAIH,KAAK,CAACI,IAAI,EAAE;UAC/B,GAAG;YACCC,GAAG,EAAEL,KAAK,CAAChB,KAAK,CAACkB,IAAI;YACrB,GAAGT,OAAO,CAACa,QAAQ;YACnB,GAAGN,KAAK,CAAChB;UACb;QACJ,CAAC,CAAC,GACFgB,KAAK;MACf,CAAC;IAAC;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEtB;AAACzB,EAAA,CA3BQN,YAAY;EAAA,QAGgCE,OAAO;AAAA;AAAA8B,EAAA,GAHnDhC,YAAY;AA6BrB,eAAeA,YAAY;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}