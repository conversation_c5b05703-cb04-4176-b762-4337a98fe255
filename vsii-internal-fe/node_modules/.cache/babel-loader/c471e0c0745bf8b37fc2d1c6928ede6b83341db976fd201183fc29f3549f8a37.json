{"ast": null, "code": "// material-ui\nimport{<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,TableCell,TableRow}from'@mui/material';// project imports\nimport{Checkbox}from'components/extended/Form';import InputTable from'./InputTable';// assets\nimport Visibility from'@mui/icons-material/Visibility';import VisibilityOff from'@mui/icons-material/VisibilityOff';import{DeleteTwoToneIcon}from'assets/images/icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FieldsEducationHistory=props=>{const{index,handleRemove,idHexString}=props;return/*#__PURE__*/_jsxs(TableRow,{sx:{position:'relative'},children:[/*#__PURE__*/_jsxs(TableCell,{className:\"from-to-date-col vertical-align-top\",children:[/*#__PURE__*/_jsx(InputTable,{name:\"educationHistory.\".concat(index,\".fromDate\"),placeholder:\"Fill from\",label:\"From\",required:true}),/*#__PURE__*/_jsx(InputTable,{name:\"educationHistory.\".concat(index,\".toDate\"),placeholder:\"Fill to\",label:\"To\",required:true})]}),/*#__PURE__*/_jsxs(TableCell,{className:\"vertical-align-top\",children:[/*#__PURE__*/_jsx(InputTable,{name:\"educationHistory.\".concat(index,\".school\"),label:\"University/School\",required:true}),/*#__PURE__*/_jsx(InputTable,{name:\"educationHistory.\".concat(index,\".qualification\"),placeholder:\"Enter\",label:\"Degree/Qualifications\"}),/*#__PURE__*/_jsxs(Stack,{sx:{position:'absolute',top:'50%',right:'-100px',transform:'translateY(-50%)','& .Mui-checked':{color:'#9e9e9e !important'}},direction:\"row\",justifyContent:\"space-between\",spacing:2,children:[/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleRemove(index,idHexString),children:/*#__PURE__*/_jsx(DeleteTwoToneIcon,{fontSize:\"small\"})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Checkbox,{name:\"educationHistory.\".concat(index,\".visible\"),checkboxProps:{icon:/*#__PURE__*/_jsx(VisibilityOff,{fontSize:\"small\"}),checkedIcon:/*#__PURE__*/_jsx(Visibility,{fontSize:\"small\"})}})})]})]})]},index);};export default FieldsEducationHistory;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}