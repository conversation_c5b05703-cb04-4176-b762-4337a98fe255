{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/Unit.tsx\";\n// project imports\nimport { Select } from 'components/extended/Form';\nimport { DEFAULT_VALUE_OPTION, UNIT_SELECT_OPTION } from 'constants/Common';\nimport { searchFormConfig } from './Config';\n\n// third party\nimport { FormattedMessage } from 'react-intl';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Unit = props => {\n  const {\n    disabled,\n    required,\n    isShowAll,\n    name,\n    handleChange,\n    label\n  } = props;\n  return /*#__PURE__*/_jsxDEV(Select, {\n    required: required,\n    disabled: disabled,\n    name: name,\n    handleChange: handleChange,\n    selects: isShowAll ? [DEFAULT_VALUE_OPTION, ...UNIT_SELECT_OPTION] : [...UNIT_SELECT_OPTION],\n    label: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n      id: label || searchFormConfig.unit.label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 20\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 9\n  }, this);\n};\n_c = Unit;\nUnit.defaultProps = {\n  isShowAll: false,\n  name: searchFormConfig.unit.name\n};\nexport default Unit;\nvar _c;\n$RefreshReg$(_c, \"Unit\");", "map": {"version": 3, "names": ["Select", "DEFAULT_VALUE_OPTION", "UNIT_SELECT_OPTION", "searchFormConfig", "FormattedMessage", "jsxDEV", "_jsxDEV", "Unit", "props", "disabled", "required", "isShowAll", "name", "handleChange", "label", "selects", "id", "unit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "defaultProps", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/search/Unit.tsx"], "sourcesContent": ["import { SelectChangeEvent } from '@mui/material';\r\n\r\n// project imports\r\nimport { Select } from 'components/extended/Form';\r\nimport { DEFAULT_VALUE_OPTION, UNIT_SELECT_OPTION } from 'constants/Common';\r\nimport { searchFormConfig } from './Config';\r\n\r\n// third party\r\nimport { FormattedMessage } from 'react-intl';\r\n\r\ninterface IUnitProps {\r\n    name: string;\r\n    disabled?: boolean;\r\n    required?: boolean;\r\n    isShowAll?: boolean;\r\n    handleChange?: (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => void;\r\n    label?: string;\r\n}\r\n\r\nconst Unit = (props: IUnitProps) => {\r\n    const { disabled, required, isShowAll, name, handleChange, label } = props;\r\n\r\n    return (\r\n        <Select\r\n            required={required}\r\n            disabled={disabled}\r\n            name={name}\r\n            handleChange={handleChange}\r\n            selects={isShowAll ? [DEFAULT_VALUE_OPTION, ...UNIT_SELECT_OPTION] : [...UNIT_SELECT_OPTION]}\r\n            label={<FormattedMessage id={label || searchFormConfig.unit.label} />}\r\n        />\r\n    );\r\n};\r\n\r\nUnit.defaultProps = {\r\n    isShowAll: false,\r\n    name: searchFormConfig.unit.name\r\n};\r\n\r\nexport default Unit;\r\n"], "mappings": ";AAEA;AACA,SAASA,MAAM,QAAQ,0BAA0B;AACjD,SAASC,oBAAoB,EAAEC,kBAAkB,QAAQ,kBAAkB;AAC3E,SAASC,gBAAgB,QAAQ,UAAU;;AAE3C;AACA,SAASC,gBAAgB,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAW9C,MAAMC,IAAI,GAAIC,KAAiB,IAAK;EAChC,MAAM;IAAEC,QAAQ;IAAEC,QAAQ;IAAEC,SAAS;IAAEC,IAAI;IAAEC,YAAY;IAAEC;EAAM,CAAC,GAAGN,KAAK;EAE1E,oBACIF,OAAA,CAACN,MAAM;IACHU,QAAQ,EAAEA,QAAS;IACnBD,QAAQ,EAAEA,QAAS;IACnBG,IAAI,EAAEA,IAAK;IACXC,YAAY,EAAEA,YAAa;IAC3BE,OAAO,EAAEJ,SAAS,GAAG,CAACV,oBAAoB,EAAE,GAAGC,kBAAkB,CAAC,GAAG,CAAC,GAAGA,kBAAkB,CAAE;IAC7FY,KAAK,eAAER,OAAA,CAACF,gBAAgB;MAACY,EAAE,EAAEF,KAAK,IAAIX,gBAAgB,CAACc,IAAI,CAACH;IAAM;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzE,CAAC;AAEV,CAAC;AAACC,EAAA,GAbIf,IAAI;AAeVA,IAAI,CAACgB,YAAY,GAAG;EAChBZ,SAAS,EAAE,KAAK;EAChBC,IAAI,EAAET,gBAAgB,CAACc,IAAI,CAACL;AAChC,CAAC;AAED,eAAeL,IAAI;AAAC,IAAAe,EAAA;AAAAE,YAAA,CAAAF,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}