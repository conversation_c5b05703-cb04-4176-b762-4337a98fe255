{"ast": null, "code": "import ControlPointOutlinedIcon from'@mui/icons-material/ControlPointOutlined';import HighlightOffOutlinedIcon from'@mui/icons-material/HighlightOffOutlined';import{useFieldArray}from'react-hook-form';import TableContainer from'@mui/material/TableContainer';import{ButtonBase,Typography}from'@mui/material';import TableBody from'@mui/material/TableBody';import TableCell from'@mui/material/TableCell';import TableHead from'@mui/material/TableHead';import TableRow from'@mui/material/TableRow';import{FormattedMessage}from'react-intl';import Table from'@mui/material/Table';import Paper from'@mui/material/Paper';import{Box}from'@mui/system';import{DatePicker,Input}from'components/extended/Form';import{TEXT_CONFIG_SCREEN}from'constants/Common';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export default function NextPlanTable(_ref){let{methods,disableEdit}=_ref;const{project_report}=TEXT_CONFIG_SCREEN.generalReport;const{fields:milestoneValues,append,remove}=useFieldArray({control:methods.control,name:'monthlyReport.nextPlan'});const handleAddRows=()=>{const rows={task:'',comment:'',startDate:null,dueDate:null};append(rows);};const handleRemoveMilestone=index=>{remove(index);};return/*#__PURE__*/_jsxs(Box,{className:\"sale-table\",children:[/*#__PURE__*/_jsx(ButtonBase,{onClick:handleAddRows,sx:{display:disableEdit?'none':'block'},children:/*#__PURE__*/_jsx(ControlPointOutlinedIcon,{})}),/*#__PURE__*/_jsx(TableContainer,{component:Paper,children:/*#__PURE__*/_jsxs(Table,{sx:{minWidth:650},\"aria-label\":\"simple table\",children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsxs(Typography,{display:\"flex\",justifyContent:\"center\",gap:1,sx:theme=>({color:theme.palette.primary.main}),children:[/*#__PURE__*/_jsx(FormattedMessage,{id:project_report+'task-name'}),\" \",/*#__PURE__*/_jsx(Typography,{color:\"#e53935\",children:\" *\"})]})}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(Typography,{display:\"flex\",justifyContent:\"center\",gap:1,sx:theme=>({color:theme.palette.primary.main}),children:/*#__PURE__*/_jsx(FormattedMessage,{id:project_report+'tentative-start-date'})})}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(Typography,{display:\"flex\",justifyContent:\"center\",gap:1,sx:theme=>({color:theme.palette.primary.main}),children:/*#__PURE__*/_jsx(FormattedMessage,{id:project_report+'comment'})})}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(Typography,{display:\"flex\",justifyContent:\"center\",gap:1,sx:theme=>({color:theme.palette.primary.main}),children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"rp-wer-member-comment\"})})}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(Typography,{display:\"flex\",justifyContent:\"center\",gap:1,sx:theme=>({color:theme.palette.primary.main}),children:/*#__PURE__*/_jsx(FormattedMessage,{id:project_report+'actions'})})})]})}),/*#__PURE__*/_jsx(TableBody,{children:milestoneValues.map((item,index)=>/*#__PURE__*/_jsxs(TableRow,{sx:{'&:last-child td, &:last-child th':{border:0}},children:[/*#__PURE__*/_jsx(TableCell,{align:\"center\",component:\"th\",scope:\"row\",children:/*#__PURE__*/_jsx(Input,{name:\"monthlyReport.nextPlan.\".concat(index,\".task\"),required:true,disabled:disableEdit})}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(DatePicker,{name:\"monthlyReport.nextPlan.\".concat(index,\".startDate\"),disabled:disableEdit})}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(DatePicker,{name:\"monthlyReport.nextPlan.\".concat(index,\".dueDate\"),disabled:disableEdit})}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(Input,{name:\"monthlyReport.nextPlan.\".concat(index,\".comment\"),textFieldProps:{multiline:true},disabled:disableEdit})}),/*#__PURE__*/_jsx(TableCell,{align:\"center\",children:/*#__PURE__*/_jsx(ButtonBase,{onClick:()=>handleRemoveMilestone(index),sx:{display:disableEdit?'none':'block'},children:/*#__PURE__*/_jsx(HighlightOffOutlinedIcon,{})})})]},item.id))})]})})]});}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}