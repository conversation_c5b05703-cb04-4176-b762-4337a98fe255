{"ast": null, "code": "import { getBatch } from './batch'; // encapsulates the subscription logic for connecting a component to the redux store, as\n// well as nesting subscriptions of descendant components, so that we can ensure the\n// ancestor components re-render before descendants\n\nfunction createListenerCollection() {\n  var batch = getBatch();\n  var first = null;\n  var last = null;\n  return {\n    clear: function clear() {\n      first = null;\n      last = null;\n    },\n    notify: function notify() {\n      batch(function () {\n        var listener = first;\n        while (listener) {\n          listener.callback();\n          listener = listener.next;\n        }\n      });\n    },\n    get: function get() {\n      var listeners = [];\n      var listener = first;\n      while (listener) {\n        listeners.push(listener);\n        listener = listener.next;\n      }\n      return listeners;\n    },\n    subscribe: function subscribe(callback) {\n      var isSubscribed = true;\n      var listener = last = {\n        callback: callback,\n        next: null,\n        prev: last\n      };\n      if (listener.prev) {\n        listener.prev.next = listener;\n      } else {\n        first = listener;\n      }\n      return function unsubscribe() {\n        if (!isSubscribed || first === null) return;\n        isSubscribed = false;\n        if (listener.next) {\n          listener.next.prev = listener.prev;\n        } else {\n          last = listener.prev;\n        }\n        if (listener.prev) {\n          listener.prev.next = listener.next;\n        } else {\n          first = listener.next;\n        }\n      };\n    }\n  };\n}\nvar nullListeners = {\n  notify: function notify() {},\n  get: function get() {\n    return [];\n  }\n};\nexport function createSubscription(store, parentSub) {\n  var unsubscribe;\n  var listeners = nullListeners;\n  function addNestedSub(listener) {\n    trySubscribe();\n    return listeners.subscribe(listener);\n  }\n  function notifyNestedSubs() {\n    listeners.notify();\n  }\n  function handleChangeWrapper() {\n    if (subscription.onStateChange) {\n      subscription.onStateChange();\n    }\n  }\n  function isSubscribed() {\n    return Boolean(unsubscribe);\n  }\n  function trySubscribe() {\n    if (!unsubscribe) {\n      unsubscribe = parentSub ? parentSub.addNestedSub(handleChangeWrapper) : store.subscribe(handleChangeWrapper);\n      listeners = createListenerCollection();\n    }\n  }\n  function tryUnsubscribe() {\n    if (unsubscribe) {\n      unsubscribe();\n      unsubscribe = undefined;\n      listeners.clear();\n      listeners = nullListeners;\n    }\n  }\n  var subscription = {\n    addNestedSub: addNestedSub,\n    notifyNestedSubs: notifyNestedSubs,\n    handleChangeWrapper: handleChangeWrapper,\n    isSubscribed: isSubscribed,\n    trySubscribe: trySubscribe,\n    tryUnsubscribe: tryUnsubscribe,\n    getListeners: function getListeners() {\n      return listeners;\n    }\n  };\n  return subscription;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}