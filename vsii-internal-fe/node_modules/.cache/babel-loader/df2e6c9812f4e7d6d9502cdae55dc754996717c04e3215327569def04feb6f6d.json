{"ast": null, "code": "import { INIT_COORDS } from '../types.js';\nexport function setClientOffset(clientOffset, sourceClientOffset) {\n  return {\n    type: INIT_COORDS,\n    payload: {\n      sourceClientOffset: sourceClientOffset || null,\n      clientOffset: clientOffset || null\n    }\n  };\n}\n\n//# sourceMappingURL=setClientOffset.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}