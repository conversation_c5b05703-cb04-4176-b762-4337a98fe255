{"ast": null, "code": "// react\nimport React from'react';import{FormattedMessage}from'react-intl';import{useFormContext}from'react-hook-form';// material-ui\nimport{TableCell,TableBody,Stack,Tooltip,IconButton,TableRow}from'@mui/material';import EditTwoToneIcon from'@mui/icons-material/EditTwoTone';// project imports\nimport{Input,NumericFormatCustom}from'components/extended/Form';import{formatPrice}from'utils/common';import{E_BIDDING_STATUS}from'constants/Common';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const BiddingHCTBody=props=>{const{monthlyHCList,handleOpen,status}=props;const{watch}=useFormContext();const contractDurationFrom=watch('project.contractDurationFrom');const contractDurationTo=watch('project.contractDurationTo');return/*#__PURE__*/_jsx(TableBody,{children:/*#__PURE__*/_jsxs(TableRow,{children:[contractDurationFrom&&contractDurationTo?/*#__PURE__*/_jsx(TableCell,{sx:{position:'sticky',left:0,zIndex:2,backgroundColor:'white'},children:/*#__PURE__*/_jsx(Stack,{direction:\"row\",justifyContent:\"center\",alignItems:\"center\",children:/*#__PURE__*/_jsx(Tooltip,{placement:\"top\",title:/*#__PURE__*/_jsx(FormattedMessage,{id:\"edit\"}),onClick:()=>handleOpen(),children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"edit\",size:\"small\",children:/*#__PURE__*/_jsx(EditTwoToneIcon,{sx:{fontSize:'1.1rem'}})})})})}):/*#__PURE__*/_jsx(_Fragment,{}),monthlyHCList===null||monthlyHCList===void 0?void 0:monthlyHCList.map((field,index)=>/*#__PURE__*/_jsxs(React.Fragment,{children:[/*#__PURE__*/_jsx(TableCell,{sx:{'& .MuiFormControl-root':{width:'100px'}},children:/*#__PURE__*/_jsx(Input,{textFieldProps:{InputProps:{inputComponent:NumericFormatCustom},defaultValue:field.hcMonthly},name:\"hcInfo.monthlyHCList.\".concat(index,\".hcMonthly\"),disabled:status===E_BIDDING_STATUS.CONTRACT})}),/*#__PURE__*/_jsx(TableCell,{children:formatPrice(monthlyHCList[index].billableDay)}),/*#__PURE__*/_jsx(TableCell,{children:formatPrice(monthlyHCList[index].billable)})]},field.id))]})});};export default BiddingHCTBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}