{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/monthly-effort/SettingProjectTypeByDepartment.tsx\",\n  _s = $RefreshSig$();\nimport { Button, DialogActions, Grid, Typography } from '@mui/material';\nimport { FormattedMessage } from 'react-intl';\nimport { useForm } from 'react-hook-form';\nimport { CheckboxGroup, FormProvider } from 'components/extended/Form';\nimport Modal from 'components/extended/Modal';\nimport { gridSpacing } from 'store/constant';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SettingProjectTypeByDepartment = ({\n  open,\n  data,\n  handleClose,\n  filterDepartment,\n  handleChangeFilterDepartment\n}) => {\n  _s();\n  var _methods$watch;\n  const methods = useForm({\n    defaultValues: {\n      departments: filterDepartment\n    }\n  });\n  const handleSubmit = values => {\n    handleChangeFilterDepartment(values.departments);\n    handleClose();\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    isOpen: open,\n    title: \"setting-project-type-by-department\",\n    onClose: handleClose,\n    maxWidth: \"xs\",\n    children: /*#__PURE__*/_jsxDEV(FormProvider, {\n      formReturn: methods,\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: gridSpacing,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: '#000000',\n              fontSize: 14\n            },\n            children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n              id: \"select-project-type-by-department\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sx: {\n            minHeight: '10vh',\n            maxHeight: '50vh',\n            overflowX: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(CheckboxGroup, {\n            name: \"departments\",\n            labelPlacement: \"start\",\n            labelOptionSx: {\n              color: '#000000',\n              fontWeight: 500\n            },\n            options: data.map(item => ({\n              label: item.department,\n              value: item.department\n            })),\n            config: {\n              value: 'value',\n              label: 'label'\n            },\n            sx: {\n              display: 'flex',\n              ml: 0\n            },\n            loadMore: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          mt: 5\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          color: \"error\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: \"cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          disabled: ((_methods$watch = methods.watch('departments')) === null || _methods$watch === void 0 ? void 0 : _methods$watch.length) < 1,\n          variant: \"contained\",\n          type: \"submit\",\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: \"submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 9\n  }, this);\n};\n_s(SettingProjectTypeByDepartment, \"cLnp+zYVfnD49CnXGGXF3NvR6q8=\", false, function () {\n  return [useForm];\n});\n_c = SettingProjectTypeByDepartment;\nexport default SettingProjectTypeByDepartment;\nvar _c;\n$RefreshReg$(_c, \"SettingProjectTypeByDepartment\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "DialogActions", "Grid", "Typography", "FormattedMessage", "useForm", "CheckboxGroup", "FormProvider", "Modal", "gridSpacing", "jsxDEV", "_jsxDEV", "SettingProjectTypeByDepartment", "open", "data", "handleClose", "filterDepartment", "handleChangeFilterDepartment", "_s", "_methods$watch", "methods", "defaultValues", "departments", "handleSubmit", "values", "isOpen", "title", "onClose", "max<PERSON><PERSON><PERSON>", "children", "formReturn", "onSubmit", "container", "spacing", "item", "xs", "sx", "color", "fontSize", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "minHeight", "maxHeight", "overflowX", "name", "labelPlacement", "labelOptionSx", "fontWeight", "options", "map", "label", "department", "value", "config", "display", "ml", "loadMore", "mt", "onClick", "disabled", "watch", "length", "variant", "type", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/monthly-effort/SettingProjectTypeByDepartment.tsx"], "sourcesContent": ["import { Button, <PERSON>alogActions, Grid, Typography } from '@mui/material';\nimport { FormattedMessage } from 'react-intl';\nimport { useForm } from 'react-hook-form';\n\nimport { CheckboxGroup, FormProvider } from 'components/extended/Form';\nimport { IProjectTypeByDepartment } from 'types';\nimport Modal from 'components/extended/Modal';\nimport { gridSpacing } from 'store/constant';\n\ninterface ISettingProjectTypeByDepartmentProps {\n    open: boolean;\n    handleClose: () => void;\n    data: IProjectTypeByDepartment[];\n    filterDepartment: { value: string; label: string }[];\n    handleChangeFilterDepartment: (departments: { value: string; label: string }[]) => void;\n}\n\nconst SettingProjectTypeByDepartment = ({\n    open,\n    data,\n    handleClose,\n    filterDepartment,\n    handleChangeFilterDepartment\n}: ISettingProjectTypeByDepartmentProps) => {\n    const methods = useForm({\n        defaultValues: {\n            departments: filterDepartment\n        }\n    });\n\n    const handleSubmit = (values: { departments: { value: string; label: string }[] }) => {\n        handleChangeFilterDepartment(values.departments);\n        handleClose();\n    };\n\n    return (\n        <Modal isOpen={open} title=\"setting-project-type-by-department\" onClose={handleClose} maxWidth=\"xs\">\n            <FormProvider formReturn={methods} onSubmit={handleSubmit}>\n                <Grid container spacing={gridSpacing}>\n                    <Grid item xs={12}>\n                        <Typography\n                            sx={{\n                                color: '#000000',\n                                fontSize: 14\n                            }}\n                        >\n                            <FormattedMessage id=\"select-project-type-by-department\" />\n                        </Typography>\n                    </Grid>\n                    <Grid\n                        item\n                        xs={12}\n                        sx={{\n                            minHeight: '10vh',\n                            maxHeight: '50vh',\n                            overflowX: 'auto'\n                        }}\n                    >\n                        <CheckboxGroup\n                            name=\"departments\"\n                            labelPlacement=\"start\"\n                            labelOptionSx={{\n                                color: '#000000',\n                                fontWeight: 500\n                            }}\n                            options={data.map((item) => ({ label: item.department, value: item.department }))}\n                            config={{ value: 'value', label: 'label' }}\n                            sx={{\n                                display: 'flex',\n                                ml: 0\n                            }}\n                            loadMore={false}\n                        />\n                    </Grid>\n                </Grid>\n\n                <DialogActions sx={{ mt: 5 }}>\n                    <Button color=\"error\" onClick={handleClose}>\n                        <FormattedMessage id=\"cancel\" />\n                    </Button>\n                    <Button disabled={methods.watch('departments')?.length < 1} variant=\"contained\" type=\"submit\">\n                        <FormattedMessage id=\"submit\" />\n                    </Button>\n                </DialogActions>\n            </FormProvider>\n        </Modal>\n    );\n};\n\nexport default SettingProjectTypeByDepartment;\n"], "mappings": ";;AAAA,SAASA,MAAM,EAAEC,aAAa,EAAEC,IAAI,EAAEC,UAAU,QAAQ,eAAe;AACvE,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,OAAO,QAAQ,iBAAiB;AAEzC,SAASC,aAAa,EAAEC,YAAY,QAAQ,0BAA0B;AAEtE,OAAOC,KAAK,MAAM,2BAA2B;AAC7C,SAASC,WAAW,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAU7C,MAAMC,8BAA8B,GAAGA,CAAC;EACpCC,IAAI;EACJC,IAAI;EACJC,WAAW;EACXC,gBAAgB;EAChBC;AACkC,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,cAAA;EACxC,MAAMC,OAAO,GAAGf,OAAO,CAAC;IACpBgB,aAAa,EAAE;MACXC,WAAW,EAAEN;IACjB;EACJ,CAAC,CAAC;EAEF,MAAMO,YAAY,GAAIC,MAA2D,IAAK;IAClFP,4BAA4B,CAACO,MAAM,CAACF,WAAW,CAAC;IAChDP,WAAW,CAAC,CAAC;EACjB,CAAC;EAED,oBACIJ,OAAA,CAACH,KAAK;IAACiB,MAAM,EAAEZ,IAAK;IAACa,KAAK,EAAC,oCAAoC;IAACC,OAAO,EAAEZ,WAAY;IAACa,QAAQ,EAAC,IAAI;IAAAC,QAAA,eAC/FlB,OAAA,CAACJ,YAAY;MAACuB,UAAU,EAAEV,OAAQ;MAACW,QAAQ,EAAER,YAAa;MAAAM,QAAA,gBACtDlB,OAAA,CAACT,IAAI;QAAC8B,SAAS;QAACC,OAAO,EAAExB,WAAY;QAAAoB,QAAA,gBACjClB,OAAA,CAACT,IAAI;UAACgC,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAN,QAAA,eACdlB,OAAA,CAACR,UAAU;YACPiC,EAAE,EAAE;cACAC,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE;YACd,CAAE;YAAAT,QAAA,eAEFlB,OAAA,CAACP,gBAAgB;cAACmC,EAAE,EAAC;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACPhC,OAAA,CAACT,IAAI;UACDgC,IAAI;UACJC,EAAE,EAAE,EAAG;UACPC,EAAE,EAAE;YACAQ,SAAS,EAAE,MAAM;YACjBC,SAAS,EAAE,MAAM;YACjBC,SAAS,EAAE;UACf,CAAE;UAAAjB,QAAA,eAEFlB,OAAA,CAACL,aAAa;YACVyC,IAAI,EAAC,aAAa;YAClBC,cAAc,EAAC,OAAO;YACtBC,aAAa,EAAE;cACXZ,KAAK,EAAE,SAAS;cAChBa,UAAU,EAAE;YAChB,CAAE;YACFC,OAAO,EAAErC,IAAI,CAACsC,GAAG,CAAElB,IAAI,KAAM;cAAEmB,KAAK,EAAEnB,IAAI,CAACoB,UAAU;cAAEC,KAAK,EAAErB,IAAI,CAACoB;YAAW,CAAC,CAAC,CAAE;YAClFE,MAAM,EAAE;cAAED,KAAK,EAAE,OAAO;cAAEF,KAAK,EAAE;YAAQ,CAAE;YAC3CjB,EAAE,EAAE;cACAqB,OAAO,EAAE,MAAM;cACfC,EAAE,EAAE;YACR,CAAE;YACFC,QAAQ,EAAE;UAAM;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPhC,OAAA,CAACV,aAAa;QAACmC,EAAE,EAAE;UAAEwB,EAAE,EAAE;QAAE,CAAE;QAAA/B,QAAA,gBACzBlB,OAAA,CAACX,MAAM;UAACqC,KAAK,EAAC,OAAO;UAACwB,OAAO,EAAE9C,WAAY;UAAAc,QAAA,eACvClB,OAAA,CAACP,gBAAgB;YAACmC,EAAE,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACThC,OAAA,CAACX,MAAM;UAAC8D,QAAQ,EAAE,EAAA3C,cAAA,GAAAC,OAAO,CAAC2C,KAAK,CAAC,aAAa,CAAC,cAAA5C,cAAA,uBAA5BA,cAAA,CAA8B6C,MAAM,IAAG,CAAE;UAACC,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,QAAQ;UAAArC,QAAA,eACzFlB,OAAA,CAACP,gBAAgB;YAACmC,EAAE,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEhB,CAAC;AAACzB,EAAA,CAtEIN,8BAA8B;EAAA,QAOhBP,OAAO;AAAA;AAAA8D,EAAA,GAPrBvD,8BAA8B;AAwEpC,eAAeA,8BAA8B;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}