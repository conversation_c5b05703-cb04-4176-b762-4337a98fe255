{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"date\", \"disabled\", \"disableFuture\", \"disablePast\", \"maxDate\", \"minDate\", \"onChange\", \"shouldDisableMonth\", \"readOnly\", \"disableHighlightToday\", \"autoFocus\", \"onMonthFocus\", \"hasFocus\", \"onFocusedViewChange\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { useTheme } from '@mui/system';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_composeClasses as composeClasses, useControlled, useEventCallback } from '@mui/material';\nimport { PickersMonth } from './PickersMonth';\nimport { useUtils, useNow, useDefaultDates } from '../internals/hooks/useUtils';\nimport { getMonthPickerUtilityClass } from './monthPickerClasses';\nimport { parseNonNullablePickerDate } from '../internals/utils/date-utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getMonthPickerUtilityClass, classes);\n};\nexport function useMonthPickerDefaultizedProps(props, name) {\n  const utils = useUtils();\n  const defaultDates = useDefaultDates();\n  const themeProps = useThemeProps({\n    props,\n    name\n  });\n  return _extends({\n    disableFuture: false,\n    disablePast: false\n  }, themeProps, {\n    minDate: parseNonNullablePickerDate(utils, themeProps.minDate, defaultDates.minDate),\n    maxDate: parseNonNullablePickerDate(utils, themeProps.maxDate, defaultDates.maxDate)\n  });\n}\nconst MonthPickerRoot = styled('div', {\n  name: 'MuiMonthPicker',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  width: 310,\n  display: 'flex',\n  flexWrap: 'wrap',\n  alignContent: 'stretch',\n  margin: '0 4px'\n});\nexport const MonthPicker = /*#__PURE__*/React.forwardRef(function MonthPicker(inProps, ref) {\n  const utils = useUtils();\n  const now = useNow();\n  const props = useMonthPickerDefaultizedProps(inProps, 'MuiMonthPicker');\n  const {\n      className,\n      date,\n      disabled,\n      disableFuture,\n      disablePast,\n      maxDate,\n      minDate,\n      onChange,\n      shouldDisableMonth,\n      readOnly,\n      disableHighlightToday,\n      autoFocus = false,\n      onMonthFocus,\n      hasFocus,\n      onFocusedViewChange\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  const theme = useTheme();\n  const selectedDateOrStartOfMonth = React.useMemo(() => date != null ? date : utils.startOfMonth(now), [now, utils, date]);\n  const selectedMonth = React.useMemo(() => {\n    if (date != null) {\n      return utils.getMonth(date);\n    }\n    if (disableHighlightToday) {\n      return null;\n    }\n    return utils.getMonth(now);\n  }, [now, date, utils, disableHighlightToday]);\n  const [focusedMonth, setFocusedMonth] = React.useState(() => selectedMonth || utils.getMonth(now));\n  const isMonthDisabled = React.useCallback(month => {\n    const firstEnabledMonth = utils.startOfMonth(disablePast && utils.isAfter(now, minDate) ? now : minDate);\n    const lastEnabledMonth = utils.startOfMonth(disableFuture && utils.isBefore(now, maxDate) ? now : maxDate);\n    if (utils.isBefore(month, firstEnabledMonth)) {\n      return true;\n    }\n    if (utils.isAfter(month, lastEnabledMonth)) {\n      return true;\n    }\n    if (!shouldDisableMonth) {\n      return false;\n    }\n    return shouldDisableMonth(month);\n  }, [disableFuture, disablePast, maxDate, minDate, now, shouldDisableMonth, utils]);\n  const onMonthSelect = month => {\n    if (readOnly) {\n      return;\n    }\n    const newDate = utils.setMonth(selectedDateOrStartOfMonth, month);\n    onChange(newDate, 'finish');\n  };\n  const [internalHasFocus, setInternalHasFocus] = useControlled({\n    name: 'MonthPicker',\n    state: 'hasFocus',\n    controlled: hasFocus,\n    default: autoFocus\n  });\n  const changeHasFocus = React.useCallback(newHasFocus => {\n    setInternalHasFocus(newHasFocus);\n    if (onFocusedViewChange) {\n      onFocusedViewChange(newHasFocus);\n    }\n  }, [setInternalHasFocus, onFocusedViewChange]);\n  const focusMonth = React.useCallback(month => {\n    if (!isMonthDisabled(utils.setMonth(selectedDateOrStartOfMonth, month))) {\n      setFocusedMonth(month);\n      changeHasFocus(true);\n      if (onMonthFocus) {\n        onMonthFocus(month);\n      }\n    }\n  }, [isMonthDisabled, utils, selectedDateOrStartOfMonth, changeHasFocus, onMonthFocus]);\n  React.useEffect(() => {\n    setFocusedMonth(prevFocusedMonth => selectedMonth !== null && prevFocusedMonth !== selectedMonth ? selectedMonth : prevFocusedMonth);\n  }, [selectedMonth]);\n  const handleKeyDown = useEventCallback(event => {\n    const monthsInYear = 12;\n    const monthsInRow = 3;\n    switch (event.key) {\n      case 'ArrowUp':\n        focusMonth((monthsInYear + focusedMonth - monthsInRow) % monthsInYear);\n        event.preventDefault();\n        break;\n      case 'ArrowDown':\n        focusMonth((monthsInYear + focusedMonth + monthsInRow) % monthsInYear);\n        event.preventDefault();\n        break;\n      case 'ArrowLeft':\n        focusMonth((monthsInYear + focusedMonth + (theme.direction === 'ltr' ? -1 : 1)) % monthsInYear);\n        event.preventDefault();\n        break;\n      case 'ArrowRight':\n        focusMonth((monthsInYear + focusedMonth + (theme.direction === 'ltr' ? 1 : -1)) % monthsInYear);\n        event.preventDefault();\n        break;\n      default:\n        break;\n    }\n  });\n  const handleMonthFocus = React.useCallback((event, month) => {\n    focusMonth(month);\n  }, [focusMonth]);\n  const handleMonthBlur = React.useCallback(() => {\n    changeHasFocus(false);\n  }, [changeHasFocus]);\n  const currentMonthNumber = utils.getMonth(now);\n  return /*#__PURE__*/_jsx(MonthPickerRoot, _extends({\n    ref: ref,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    onKeyDown: handleKeyDown\n  }, other, {\n    children: utils.getMonthArray(selectedDateOrStartOfMonth).map(month => {\n      const monthNumber = utils.getMonth(month);\n      const monthText = utils.format(month, 'monthShort');\n      const isDisabled = disabled || isMonthDisabled(month);\n      return /*#__PURE__*/_jsx(PickersMonth, {\n        value: monthNumber,\n        selected: monthNumber === selectedMonth,\n        tabIndex: monthNumber === focusedMonth && !isDisabled ? 0 : -1,\n        hasFocus: internalHasFocus && monthNumber === focusedMonth,\n        onSelect: onMonthSelect,\n        onFocus: handleMonthFocus,\n        onBlur: handleMonthBlur,\n        disabled: isDisabled,\n        \"aria-current\": currentMonthNumber === monthNumber ? 'date' : undefined,\n        children: monthText\n      }, monthText);\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? MonthPicker.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  autoFocus: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * className applied to the root element.\n   */\n  className: PropTypes.string,\n  /**\n   * Date value for the MonthPicker\n   */\n  date: PropTypes.any,\n  /**\n   * If `true` picker is disabled\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true` future days are disabled.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * If `true`, today's date is rendering without highlighting with circle.\n   * @default false\n   */\n  disableHighlightToday: PropTypes.bool,\n  /**\n   * If `true` past days are disabled.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  hasFocus: PropTypes.bool,\n  /**\n   * Maximal selectable date. @DateIOType\n   */\n  maxDate: PropTypes.any,\n  /**\n   * Minimal selectable date. @DateIOType\n   */\n  minDate: PropTypes.any,\n  /**\n   * Callback fired on date change.\n   */\n  onChange: PropTypes.func.isRequired,\n  onFocusedViewChange: PropTypes.func,\n  onMonthFocus: PropTypes.func,\n  /**\n   * If `true` picker is readonly\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * Disable specific months dynamically.\n   * Works like `shouldDisableDate` but for month selection view @DateIOType.\n   * @template TDate\n   * @param {TDate} month The month to check.\n   * @returns {boolean} If `true` the month will be disabled.\n   */\n  shouldDisableMonth: PropTypes.func,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}