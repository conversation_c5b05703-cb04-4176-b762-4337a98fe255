{"ast": null, "code": "/**\n * If `componentProps` is a function, calls it with the provided `ownerState`.\n * Otherwise, just returns `componentProps`.\n */\nexport default function resolveComponentProps(componentProps, ownerState) {\n  if (typeof componentProps === 'function') {\n    return componentProps(ownerState);\n  }\n  return componentProps;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}