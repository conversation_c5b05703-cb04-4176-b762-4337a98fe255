{"ast": null, "code": "function _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport has from 'lodash/has';\nimport snakeCase from 'lodash/snakeCase';\nimport camelCase from 'lodash/camelCase';\nimport mapKeys from 'lodash/mapKeys';\nimport mapValues from 'lodash/mapValues';\nimport { getter } from 'property-expr';\nimport { object as locale } from './locale';\nimport sortFields from './util/sortFields';\nimport sortByKeyOrder from './util/sortByKeyOrder';\nimport runTests from './util/runTests';\nimport ValidationError from './ValidationError';\nimport BaseSchema from './schema';\nlet isObject = obj => Object.prototype.toString.call(obj) === '[object Object]';\nfunction unknown(ctx, value) {\n  let known = Object.keys(ctx.fields);\n  return Object.keys(value).filter(key => known.indexOf(key) === -1);\n}\nconst defaultSort = sortByKeyOrder([]);\nexport default class ObjectSchema extends BaseSchema {\n  constructor(spec) {\n    super({\n      type: 'object'\n    });\n    this.fields = Object.create(null);\n    this._sortErrors = defaultSort;\n    this._nodes = [];\n    this._excludedEdges = [];\n    this.withMutation(() => {\n      this.transform(function coerce(value) {\n        if (typeof value === 'string') {\n          try {\n            value = JSON.parse(value);\n          } catch (err) {\n            value = null;\n          }\n        }\n        if (this.isType(value)) return value;\n        return null;\n      });\n      if (spec) {\n        this.shape(spec);\n      }\n    });\n  }\n  _typeCheck(value) {\n    return isObject(value) || typeof value === 'function';\n  }\n  _cast(_value) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var _options$stripUnknown;\n    let value = super._cast(_value, options); //should ignore nulls here\n\n    if (value === undefined) return this.getDefault();\n    if (!this._typeCheck(value)) return value;\n    let fields = this.fields;\n    let strip = (_options$stripUnknown = options.stripUnknown) != null ? _options$stripUnknown : this.spec.noUnknown;\n    let props = this._nodes.concat(Object.keys(value).filter(v => this._nodes.indexOf(v) === -1));\n    let intermediateValue = {}; // is filled during the transform below\n\n    let innerOptions = _extends({}, options, {\n      parent: intermediateValue,\n      __validating: options.__validating || false\n    });\n    let isChanged = false;\n    for (const prop of props) {\n      let field = fields[prop];\n      let exists = has(value, prop);\n      if (field) {\n        let fieldValue;\n        let inputValue = value[prop]; // safe to mutate since this is fired in sequence\n\n        innerOptions.path = (options.path ? \"\".concat(options.path, \".\") : '') + prop; // innerOptions.value = value[prop];\n\n        field = field.resolve({\n          value: inputValue,\n          context: options.context,\n          parent: intermediateValue\n        });\n        let fieldSpec = 'spec' in field ? field.spec : undefined;\n        let strict = fieldSpec == null ? void 0 : fieldSpec.strict;\n        if (fieldSpec == null ? void 0 : fieldSpec.strip) {\n          isChanged = isChanged || prop in value;\n          continue;\n        }\n        fieldValue = !options.__validating || !strict ?\n        // TODO: use _cast, this is double resolving\n        field.cast(value[prop], innerOptions) : value[prop];\n        if (fieldValue !== undefined) {\n          intermediateValue[prop] = fieldValue;\n        }\n      } else if (exists && !strip) {\n        intermediateValue[prop] = value[prop];\n      }\n      if (intermediateValue[prop] !== value[prop]) {\n        isChanged = true;\n      }\n    }\n    return isChanged ? intermediateValue : value;\n  }\n  _validate(_value) {\n    let opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let callback = arguments.length > 2 ? arguments[2] : undefined;\n    let errors = [];\n    let {\n      sync,\n      from = [],\n      originalValue = _value,\n      abortEarly = this.spec.abortEarly,\n      recursive = this.spec.recursive\n    } = opts;\n    from = [{\n      schema: this,\n      value: originalValue\n    }, ...from]; // this flag is needed for handling `strict` correctly in the context of\n    // validation vs just casting. e.g strict() on a field is only used when validating\n\n    opts.__validating = true;\n    opts.originalValue = originalValue;\n    opts.from = from;\n    super._validate(_value, opts, (err, value) => {\n      if (err) {\n        if (!ValidationError.isError(err) || abortEarly) {\n          return void callback(err, value);\n        }\n        errors.push(err);\n      }\n      if (!recursive || !isObject(value)) {\n        callback(errors[0] || null, value);\n        return;\n      }\n      originalValue = originalValue || value;\n      let tests = this._nodes.map(key => (_, cb) => {\n        let path = key.indexOf('.') === -1 ? (opts.path ? \"\".concat(opts.path, \".\") : '') + key : \"\".concat(opts.path || '', \"[\\\"\").concat(key, \"\\\"]\");\n        let field = this.fields[key];\n        if (field && 'validate' in field) {\n          field.validate(value[key], _extends({}, opts, {\n            // @ts-ignore\n            path,\n            from,\n            // inner fields are always strict:\n            // 1. this isn't strict so the casting will also have cast inner values\n            // 2. this is strict in which case the nested values weren't cast either\n            strict: true,\n            parent: value,\n            originalValue: originalValue[key]\n          }), cb);\n          return;\n        }\n        cb(null);\n      });\n      runTests({\n        sync,\n        tests,\n        value,\n        errors,\n        endEarly: abortEarly,\n        sort: this._sortErrors,\n        path: opts.path\n      }, callback);\n    });\n  }\n  clone(spec) {\n    const next = super.clone(spec);\n    next.fields = _extends({}, this.fields);\n    next._nodes = this._nodes;\n    next._excludedEdges = this._excludedEdges;\n    next._sortErrors = this._sortErrors;\n    return next;\n  }\n  concat(schema) {\n    let next = super.concat(schema);\n    let nextFields = next.fields;\n    for (let [field, schemaOrRef] of Object.entries(this.fields)) {\n      const target = nextFields[field];\n      if (target === undefined) {\n        nextFields[field] = schemaOrRef;\n      } else if (target instanceof BaseSchema && schemaOrRef instanceof BaseSchema) {\n        nextFields[field] = schemaOrRef.concat(target);\n      }\n    }\n    return next.withMutation(() => next.shape(nextFields, this._excludedEdges));\n  }\n  getDefaultFromShape() {\n    let dft = {};\n    this._nodes.forEach(key => {\n      const field = this.fields[key];\n      dft[key] = 'default' in field ? field.getDefault() : undefined;\n    });\n    return dft;\n  }\n  _getDefault() {\n    if ('default' in this.spec) {\n      return super._getDefault();\n    } // if there is no default set invent one\n\n    if (!this._nodes.length) {\n      return undefined;\n    }\n    return this.getDefaultFromShape();\n  }\n  shape(additions) {\n    let excludes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n    let next = this.clone();\n    let fields = Object.assign(next.fields, additions);\n    next.fields = fields;\n    next._sortErrors = sortByKeyOrder(Object.keys(fields));\n    if (excludes.length) {\n      // this is a convenience for when users only supply a single pair\n      if (!Array.isArray(excludes[0])) excludes = [excludes];\n      next._excludedEdges = [...next._excludedEdges, ...excludes];\n    }\n    next._nodes = sortFields(fields, next._excludedEdges);\n    return next;\n  }\n  pick(keys) {\n    const picked = {};\n    for (const key of keys) {\n      if (this.fields[key]) picked[key] = this.fields[key];\n    }\n    return this.clone().withMutation(next => {\n      next.fields = {};\n      return next.shape(picked);\n    });\n  }\n  omit(keys) {\n    const next = this.clone();\n    const fields = next.fields;\n    next.fields = {};\n    for (const key of keys) {\n      delete fields[key];\n    }\n    return next.withMutation(() => next.shape(fields));\n  }\n  from(from, to, alias) {\n    let fromGetter = getter(from, true);\n    return this.transform(obj => {\n      if (obj == null) return obj;\n      let newObj = obj;\n      if (has(obj, from)) {\n        newObj = _extends({}, obj);\n        if (!alias) delete newObj[from];\n        newObj[to] = fromGetter(obj);\n      }\n      return newObj;\n    });\n  }\n  noUnknown() {\n    let noAllow = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n    let message = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : locale.noUnknown;\n    if (typeof noAllow === 'string') {\n      message = noAllow;\n      noAllow = true;\n    }\n    let next = this.test({\n      name: 'noUnknown',\n      exclusive: true,\n      message: message,\n      test(value) {\n        if (value == null) return true;\n        const unknownKeys = unknown(this.schema, value);\n        return !noAllow || unknownKeys.length === 0 || this.createError({\n          params: {\n            unknown: unknownKeys.join(', ')\n          }\n        });\n      }\n    });\n    next.spec.noUnknown = noAllow;\n    return next;\n  }\n  unknown() {\n    let allow = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n    let message = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : locale.noUnknown;\n    return this.noUnknown(!allow, message);\n  }\n  transformKeys(fn) {\n    return this.transform(obj => obj && mapKeys(obj, (_, key) => fn(key)));\n  }\n  camelCase() {\n    return this.transformKeys(camelCase);\n  }\n  snakeCase() {\n    return this.transformKeys(snakeCase);\n  }\n  constantCase() {\n    return this.transformKeys(key => snakeCase(key).toUpperCase());\n  }\n  describe() {\n    let base = super.describe();\n    base.fields = mapValues(this.fields, value => value.describe());\n    return base;\n  }\n}\nexport function create(spec) {\n  return new ObjectSchema(spec);\n}\ncreate.prototype = ObjectSchema.prototype;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}