{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/monthly-effort/MonthlyEffortORMReportThead.tsx\",\n  _s = $RefreshSig$();\n// material-ui\nimport { TableCell, TableHead, TableRow, Typography } from '@mui/material';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme } from '@mui/material/styles';\nimport { FormattedMessage } from 'react-intl';\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction MonthlyEffortORMReportThead({\n  currentYear,\n  projectLength\n}) {\n  _s();\n  const theme = useTheme();\n  const matches = useMediaQuery(theme.breakpoints.up('md'));\n  const {\n    ORMReport\n  } = TEXT_CONFIG_SCREEN.generalReport;\n  return /*#__PURE__*/_jsxDEV(TableHead, {\n    children: /*#__PURE__*/_jsxDEV(TableRow, {\n      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n        rowSpan: 2,\n        sx: {\n          left: !!matches ? 0 : 'unset',\n          zIndex: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: theme => ({\n            fontWeight: 'bold'\n          }),\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: ORMReport + 'no'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        rowSpan: 2,\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: theme => ({\n            fontWeight: 'bold'\n          }),\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: ORMReport + 'report-name'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        rowSpan: 2,\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: theme => ({\n            fontWeight: 'bold'\n          }),\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: ORMReport + 'month'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        rowSpan: 2,\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: theme => ({\n            fontWeight: 'bold'\n          }),\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: ORMReport + 'department'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        rowSpan: 2,\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: theme => ({\n            fontWeight: 'bold'\n          }),\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: ORMReport + 'upload-user'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        rowSpan: 2,\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: theme => ({\n            fontWeight: 'bold'\n          }),\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: ORMReport + 'last-update'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n        rowSpan: 2\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 9\n  }, this);\n}\n_s(MonthlyEffortORMReportThead, \"W0qUR68KjTkJDAHnizbTku3qehE=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c = MonthlyEffortORMReportThead;\nexport default MonthlyEffortORMReportThead;\nvar _c;\n$RefreshReg$(_c, \"MonthlyEffortORMReportThead\");", "map": {"version": 3, "names": ["TableCell", "TableHead", "TableRow", "Typography", "useMediaQuery", "useTheme", "FormattedMessage", "TEXT_CONFIG_SCREEN", "jsxDEV", "_jsxDEV", "MonthlyEffortORMReportThead", "currentYear", "projectLength", "_s", "theme", "matches", "breakpoints", "up", "ORMReport", "general<PERSON><PERSON><PERSON>", "children", "rowSpan", "sx", "left", "zIndex", "fontWeight", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/containers/monthly-effort/MonthlyEffortORMReportThead.tsx"], "sourcesContent": ["// material-ui\r\nimport { TableCell, TableHead, TableRow, Typography } from '@mui/material';\r\nimport useMediaQuery from '@mui/material/useMediaQuery';\r\nimport { useTheme } from '@mui/material/styles';\r\nimport { FormattedMessage } from 'react-intl';\r\nimport { TEXT_CONFIG_SCREEN } from 'constants/Common';\r\n\r\ninterface IMonthlyEffortORMReportTheadProps {\r\n    currentYear: number;\r\n    projectLength: number;\r\n}\r\n\r\nfunction MonthlyEffortORMReportThead({ currentYear, projectLength }: IMonthlyEffortORMReportTheadProps) {\r\n    const theme = useTheme();\r\n\r\n    const matches = useMediaQuery(theme.breakpoints.up('md'));\r\n\r\n    const { ORMReport } = TEXT_CONFIG_SCREEN.generalReport;\r\n    return (\r\n        <TableHead>\r\n            <TableRow>\r\n                <TableCell rowSpan={2} sx={{ left: !!matches ? 0 : 'unset', zIndex: 3 }}>\r\n                    <Typography\r\n                        sx={(theme) => ({\r\n                            fontWeight: 'bold'\r\n                        })}\r\n                    >\r\n                        <FormattedMessage id={ORMReport + 'no'} />\r\n                    </Typography>\r\n                </TableCell>\r\n                <TableCell rowSpan={2}>\r\n                    <Typography\r\n                        sx={(theme) => ({\r\n                            fontWeight: 'bold'\r\n                        })}\r\n                    >\r\n                        <FormattedMessage id={ORMReport + 'report-name'} />\r\n                    </Typography>\r\n                </TableCell>\r\n\r\n                <TableCell rowSpan={2}>\r\n                    <Typography\r\n                        sx={(theme) => ({\r\n                            fontWeight: 'bold'\r\n                        })}\r\n                    >\r\n                        <FormattedMessage id={ORMReport + 'month'} />\r\n                    </Typography>\r\n                </TableCell>\r\n                <TableCell rowSpan={2}>\r\n                    <Typography\r\n                        sx={(theme) => ({\r\n                            fontWeight: 'bold'\r\n                        })}\r\n                    >\r\n                        <FormattedMessage id={ORMReport + 'department'} />\r\n                    </Typography>\r\n                </TableCell>\r\n                <TableCell rowSpan={2}>\r\n                    <Typography\r\n                        sx={(theme) => ({\r\n                            fontWeight: 'bold'\r\n                        })}\r\n                    >\r\n                        <FormattedMessage id={ORMReport + 'upload-user'} />\r\n                    </Typography>\r\n                </TableCell>\r\n\r\n                <TableCell rowSpan={2}>\r\n                    <Typography\r\n                        sx={(theme) => ({\r\n                            fontWeight: 'bold'\r\n                        })}\r\n                    >\r\n                        <FormattedMessage id={ORMReport + 'last-update'} />\r\n                    </Typography>\r\n                </TableCell>\r\n                <TableCell rowSpan={2}></TableCell>\r\n            </TableRow>\r\n        </TableHead>\r\n    );\r\n}\r\nexport default MonthlyEffortORMReportThead;\r\n"], "mappings": ";;AAAA;AACA,SAASA,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,eAAe;AAC1E,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,kBAAkB,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOtD,SAASC,2BAA2BA,CAAC;EAAEC,WAAW;EAAEC;AAAiD,CAAC,EAAE;EAAAC,EAAA;EACpG,MAAMC,KAAK,GAAGT,QAAQ,CAAC,CAAC;EAExB,MAAMU,OAAO,GAAGX,aAAa,CAACU,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,CAAC;EAEzD,MAAM;IAAEC;EAAU,CAAC,GAAGX,kBAAkB,CAACY,aAAa;EACtD,oBACIV,OAAA,CAACR,SAAS;IAAAmB,QAAA,eACNX,OAAA,CAACP,QAAQ;MAAAkB,QAAA,gBACLX,OAAA,CAACT,SAAS;QAACqB,OAAO,EAAE,CAAE;QAACC,EAAE,EAAE;UAAEC,IAAI,EAAE,CAAC,CAACR,OAAO,GAAG,CAAC,GAAG,OAAO;UAAES,MAAM,EAAE;QAAE,CAAE;QAAAJ,QAAA,eACpEX,OAAA,CAACN,UAAU;UACPmB,EAAE,EAAGR,KAAK,KAAM;YACZW,UAAU,EAAE;UAChB,CAAC,CAAE;UAAAL,QAAA,eAEHX,OAAA,CAACH,gBAAgB;YAACoB,EAAE,EAAER,SAAS,GAAG;UAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACZrB,OAAA,CAACT,SAAS;QAACqB,OAAO,EAAE,CAAE;QAAAD,QAAA,eAClBX,OAAA,CAACN,UAAU;UACPmB,EAAE,EAAGR,KAAK,KAAM;YACZW,UAAU,EAAE;UAChB,CAAC,CAAE;UAAAL,QAAA,eAEHX,OAAA,CAACH,gBAAgB;YAACoB,EAAE,EAAER,SAAS,GAAG;UAAc;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEZrB,OAAA,CAACT,SAAS;QAACqB,OAAO,EAAE,CAAE;QAAAD,QAAA,eAClBX,OAAA,CAACN,UAAU;UACPmB,EAAE,EAAGR,KAAK,KAAM;YACZW,UAAU,EAAE;UAChB,CAAC,CAAE;UAAAL,QAAA,eAEHX,OAAA,CAACH,gBAAgB;YAACoB,EAAE,EAAER,SAAS,GAAG;UAAQ;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACZrB,OAAA,CAACT,SAAS;QAACqB,OAAO,EAAE,CAAE;QAAAD,QAAA,eAClBX,OAAA,CAACN,UAAU;UACPmB,EAAE,EAAGR,KAAK,KAAM;YACZW,UAAU,EAAE;UAChB,CAAC,CAAE;UAAAL,QAAA,eAEHX,OAAA,CAACH,gBAAgB;YAACoB,EAAE,EAAER,SAAS,GAAG;UAAa;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACZrB,OAAA,CAACT,SAAS;QAACqB,OAAO,EAAE,CAAE;QAAAD,QAAA,eAClBX,OAAA,CAACN,UAAU;UACPmB,EAAE,EAAGR,KAAK,KAAM;YACZW,UAAU,EAAE;UAChB,CAAC,CAAE;UAAAL,QAAA,eAEHX,OAAA,CAACH,gBAAgB;YAACoB,EAAE,EAAER,SAAS,GAAG;UAAc;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEZrB,OAAA,CAACT,SAAS;QAACqB,OAAO,EAAE,CAAE;QAAAD,QAAA,eAClBX,OAAA,CAACN,UAAU;UACPmB,EAAE,EAAGR,KAAK,KAAM;YACZW,UAAU,EAAE;UAChB,CAAC,CAAE;UAAAL,QAAA,eAEHX,OAAA,CAACH,gBAAgB;YAACoB,EAAE,EAAER,SAAS,GAAG;UAAc;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACZrB,OAAA,CAACT,SAAS;QAACqB,OAAO,EAAE;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEpB;AAACjB,EAAA,CArEQH,2BAA2B;EAAA,QAClBL,QAAQ,EAEND,aAAa;AAAA;AAAA2B,EAAA,GAHxBrB,2BAA2B;AAsEpC,eAAeA,2BAA2B;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}