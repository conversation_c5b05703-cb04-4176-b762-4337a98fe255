{"ast": null, "code": "import { isNumber, isFunction } from '@motionone/utils';\nimport { getEasingFunction } from '@motionone/animation';\nfunction stagger() {\n  let duration = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0.1;\n  let {\n    start = 0,\n    from = 0,\n    easing\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return (i, total) => {\n    const fromIndex = isNumber(from) ? from : getFromIndex(from, total);\n    const distance = Math.abs(fromIndex - i);\n    let delay = duration * distance;\n    if (easing) {\n      const maxDelay = total * duration;\n      const easingFunction = getEasingFunction(easing);\n      delay = easingFunction(delay / maxDelay) * maxDelay;\n    }\n    return start + delay;\n  };\n}\nfunction getFromIndex(from, total) {\n  if (from === \"first\") {\n    return 0;\n  } else {\n    const lastIndex = total - 1;\n    return from === \"last\" ? lastIndex : lastIndex / 2;\n  }\n}\nfunction resolveOption(option, i, total) {\n  return isFunction(option) ? option(i, total) : option;\n}\nexport { getFromIndex, resolveOption, stagger };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}