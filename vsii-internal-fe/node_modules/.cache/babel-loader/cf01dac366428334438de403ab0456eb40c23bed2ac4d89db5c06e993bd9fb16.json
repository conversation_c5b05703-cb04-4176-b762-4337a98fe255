{"ast": null, "code": "import { __rest } from \"tslib\";\nimport * as React from 'react';\nimport useIntl from './useIntl';\nvar DisplayName;\n(function (DisplayName) {\n  DisplayName[\"formatDate\"] = \"FormattedDate\";\n  DisplayName[\"formatTime\"] = \"FormattedTime\";\n  DisplayName[\"formatNumber\"] = \"FormattedNumber\";\n  DisplayName[\"formatList\"] = \"FormattedList\";\n  // Note that this DisplayName is the locale display name, not to be confused with\n  // the name of the enum, which is for React component display name in dev tools.\n  DisplayName[\"formatDisplayName\"] = \"FormattedDisplayName\";\n})(DisplayName || (DisplayName = {}));\nvar DisplayNameParts;\n(function (DisplayNameParts) {\n  DisplayNameParts[\"formatDate\"] = \"FormattedDateParts\";\n  DisplayNameParts[\"formatTime\"] = \"FormattedTimeParts\";\n  DisplayNameParts[\"formatNumber\"] = \"FormattedNumberParts\";\n  DisplayNameParts[\"formatList\"] = \"FormattedListParts\";\n})(DisplayNameParts || (DisplayNameParts = {}));\nexport var FormattedNumberParts = function (props) {\n  var intl = useIntl();\n  var value = props.value,\n    children = props.children,\n    formatProps = __rest(props, [\"value\", \"children\"]);\n  return children(intl.formatNumberToParts(value, formatProps));\n};\nFormattedNumberParts.displayName = 'FormattedNumberParts';\nexport var FormattedListParts = function (props) {\n  var intl = useIntl();\n  var value = props.value,\n    children = props.children,\n    formatProps = __rest(props, [\"value\", \"children\"]);\n  return children(intl.formatListToParts(value, formatProps));\n};\nFormattedNumberParts.displayName = 'FormattedNumberParts';\nexport function createFormattedDateTimePartsComponent(name) {\n  var ComponentParts = function (props) {\n    var intl = useIntl();\n    var value = props.value,\n      children = props.children,\n      formatProps = __rest(props, [\"value\", \"children\"]);\n    var date = typeof value === 'string' ? new Date(value || 0) : value;\n    var formattedParts = name === 'formatDate' ? intl.formatDateToParts(date, formatProps) : intl.formatTimeToParts(date, formatProps);\n    return children(formattedParts);\n  };\n  ComponentParts.displayName = DisplayNameParts[name];\n  return ComponentParts;\n}\nexport function createFormattedComponent(name) {\n  var Component = function (props) {\n    var intl = useIntl();\n    var value = props.value,\n      children = props.children,\n      formatProps = __rest(props\n      // TODO: fix TS type definition for localeMatcher upstream\n      , [\"value\", \"children\"]);\n    // TODO: fix TS type definition for localeMatcher upstream\n    var formattedValue = intl[name](value, formatProps);\n    if (typeof children === 'function') {\n      return children(formattedValue);\n    }\n    var Text = intl.textComponent || React.Fragment;\n    return React.createElement(Text, null, formattedValue);\n  };\n  Component.displayName = DisplayName[name];\n  return Component;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}