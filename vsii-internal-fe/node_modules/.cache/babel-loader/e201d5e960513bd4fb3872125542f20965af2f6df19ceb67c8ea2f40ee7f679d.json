{"ast": null, "code": "import{Box,Button,Typography}from'@mui/material';import{useNavigate}from'react-router-dom';import{FormattedMessage}from'react-intl';import{closeDeniedPermission}from'store/slice/deniedPermissionSlice';import{useAppDispatch,useAppSelector}from'app/hooks';import{PUBLIC_URL}from'constants/Common';import{ROUTER}from'constants/Routers';import{Crown}from'./icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DeniedPermissionScreen=_ref=>{let{children}=_ref;const{show,isTabWrap}=useAppSelector(state=>state.deniedPermission);const dispatch=useAppDispatch();const navigate=useNavigate();const handleClose=()=>{navigate(ROUTER.home.index);dispatch(closeDeniedPermission());};return children&&!show&&!isTabWrap?children:/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',alignItems:'center',width:'100%',height:'100%',background:\"url(\\\"\".concat(PUBLIC_URL,\"background-dashboard.svg\\\") no-repeat center\")},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'center',alignItems:'center',width:'50%',flexDirection:'column',gap:5,py:10},children:[/*#__PURE__*/_jsx(Box,{sx:{padding:2,border:'2px solid #f99e2185',borderRadius:30},children:/*#__PURE__*/_jsx(Crown,{})}),/*#__PURE__*/_jsx(Typography,{variant:\"h1\",sx:{fontSize:24,textAlign:'center'},children:\"Upgrade to Access the full feature of InstantView\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{fontSize:16,textAlign:'center'},children:\"Unlock limitless content possibilities. Upgrade now to exceed your credit limit and access valuable content that fuels creativity. Get more credit and unleash your full potential with our paid plans.\"}),/*#__PURE__*/_jsx(Button,{size:\"large\",variant:\"contained\",onClick:handleClose,children:/*#__PURE__*/_jsx(FormattedMessage,{id:\"upgrade-btn\"})})]})});};export default DeniedPermissionScreen;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}