{"ast": null, "code": "/**\n * The base implementation of `_.unary` without support for storing metadata.\n *\n * @private\n * @param {Function} func The function to cap arguments for.\n * @returns {Function} Returns the new capped function.\n */\nfunction baseUnary(func) {\n  return function (value) {\n    return func(value);\n  };\n}\nmodule.exports = baseUnary;", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}