{"ast": null, "code": "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    var ownKeys = Object.keys(source);\n    if (typeof Object.getOwnPropertySymbols === 'function') {\n      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n      }));\n    }\n    ownKeys.forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    });\n  }\n  return target;\n}\nimport { BEGIN_DRAG, DROP, END_DRAG, HOVER, PUBLISH_DRAG_SOURCE } from '../actions/dragDrop/index.js';\nimport { REMOVE_TARGET } from '../actions/registry.js';\nimport { without } from '../utils/js_utils.js';\nconst initialState = {\n  itemType: null,\n  item: null,\n  sourceId: null,\n  targetIds: [],\n  dropResult: null,\n  didDrop: false,\n  isSourcePublic: null\n};\nexport function reduce(state = initialState, action) {\n  const {\n    payload\n  } = action;\n  switch (action.type) {\n    case BEGIN_DRAG:\n      return _objectSpread({}, state, {\n        itemType: payload.itemType,\n        item: payload.item,\n        sourceId: payload.sourceId,\n        isSourcePublic: payload.isSourcePublic,\n        dropResult: null,\n        didDrop: false\n      });\n    case PUBLISH_DRAG_SOURCE:\n      return _objectSpread({}, state, {\n        isSourcePublic: true\n      });\n    case HOVER:\n      return _objectSpread({}, state, {\n        targetIds: payload.targetIds\n      });\n    case REMOVE_TARGET:\n      if (state.targetIds.indexOf(payload.targetId) === -1) {\n        return state;\n      }\n      return _objectSpread({}, state, {\n        targetIds: without(state.targetIds, payload.targetId)\n      });\n    case DROP:\n      return _objectSpread({}, state, {\n        dropResult: payload.dropResult,\n        didDrop: true,\n        targetIds: []\n      });\n    case END_DRAG:\n      return _objectSpread({}, state, {\n        itemType: null,\n        item: null,\n        sourceId: null,\n        dropResult: null,\n        didDrop: false,\n        isSourcePublic: null,\n        targetIds: []\n      });\n    default:\n      return state;\n  }\n}\n\n//# sourceMappingURL=dragOperation.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}