{"ast": null, "code": "import { frameData } from '../frameloop/data.mjs';\nimport { sync } from '../frameloop/index.mjs';\nimport { SubscriptionManager } from '../utils/subscription-manager.mjs';\nimport { velocityPerSecond } from '../utils/velocity-per-second.mjs';\nconst isFloat = value => {\n  return !isNaN(parseFloat(value));\n};\n/**\n * `MotionValue` is used to track the state and velocity of motion values.\n *\n * @public\n */\nclass MotionValue {\n  /**\n   * @param init - The initiating value\n   * @param config - Optional configuration options\n   *\n   * -  `transformer`: A function to transform incoming values with.\n   *\n   * @internal\n   */\n  constructor(init) {\n    var _this = this;\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    /**\n     * This will be replaced by the build step with the latest version number.\n     * When MotionValues are provided to motion components, warn if versions are mixed.\n     */\n    this.version = \"7.10.3\";\n    /**\n     * Duration, in milliseconds, since last updating frame.\n     *\n     * @internal\n     */\n    this.timeDelta = 0;\n    /**\n     * Timestamp of the last time this `MotionValue` was updated.\n     *\n     * @internal\n     */\n    this.lastUpdated = 0;\n    /**\n     * Tracks whether this value can output a velocity. Currently this is only true\n     * if the value is numerical, but we might be able to widen the scope here and support\n     * other value types.\n     *\n     * @internal\n     */\n    this.canTrackVelocity = false;\n    /**\n     * An object containing a SubscriptionManager for each active event.\n     */\n    this.events = {};\n    this.updateAndNotify = function (v) {\n      let render = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n      _this.prev = _this.current;\n      _this.current = v;\n      // Update timestamp\n      const {\n        delta,\n        timestamp\n      } = frameData;\n      if (_this.lastUpdated !== timestamp) {\n        _this.timeDelta = delta;\n        _this.lastUpdated = timestamp;\n        sync.postRender(_this.scheduleVelocityCheck);\n      }\n      // Update update subscribers\n      if (_this.prev !== _this.current && _this.events.change) {\n        _this.events.change.notify(_this.current);\n      }\n      // Update velocity subscribers\n      if (_this.events.velocityChange) {\n        _this.events.velocityChange.notify(_this.getVelocity());\n      }\n      // Update render subscribers\n      if (render && _this.events.renderRequest) {\n        _this.events.renderRequest.notify(_this.current);\n      }\n    };\n    /**\n     * Schedule a velocity check for the next frame.\n     *\n     * This is an instanced and bound function to prevent generating a new\n     * function once per frame.\n     *\n     * @internal\n     */\n    this.scheduleVelocityCheck = () => sync.postRender(this.velocityCheck);\n    /**\n     * Updates `prev` with `current` if the value hasn't been updated this frame.\n     * This ensures velocity calculations return `0`.\n     *\n     * This is an instanced and bound function to prevent generating a new\n     * function once per frame.\n     *\n     * @internal\n     */\n    this.velocityCheck = _ref => {\n      let {\n        timestamp\n      } = _ref;\n      if (timestamp !== this.lastUpdated) {\n        this.prev = this.current;\n        if (this.events.velocityChange) {\n          this.events.velocityChange.notify(this.getVelocity());\n        }\n      }\n    };\n    this.hasAnimated = false;\n    this.prev = this.current = init;\n    this.canTrackVelocity = isFloat(this.current);\n    this.owner = options.owner;\n  }\n  /**\n   * Adds a function that will be notified when the `MotionValue` is updated.\n   *\n   * It returns a function that, when called, will cancel the subscription.\n   *\n   * When calling `onChange` inside a React component, it should be wrapped with the\n   * `useEffect` hook. As it returns an unsubscribe function, this should be returned\n   * from the `useEffect` function to ensure you don't add duplicate subscribers..\n   *\n   * ```jsx\n   * export const MyComponent = () => {\n   *   const x = useMotionValue(0)\n   *   const y = useMotionValue(0)\n   *   const opacity = useMotionValue(1)\n   *\n   *   useEffect(() => {\n   *     function updateOpacity() {\n   *       const maxXY = Math.max(x.get(), y.get())\n   *       const newOpacity = transform(maxXY, [0, 100], [1, 0])\n   *       opacity.set(newOpacity)\n   *     }\n   *\n   *     const unsubscribeX = x.on(\"change\", updateOpacity)\n   *     const unsubscribeY = y.on(\"change\", updateOpacity)\n   *\n   *     return () => {\n   *       unsubscribeX()\n   *       unsubscribeY()\n   *     }\n   *   }, [])\n   *\n   *   return <motion.div style={{ x }} />\n   * }\n   * ```\n   *\n   * @privateRemarks\n   *\n   * We could look into a `useOnChange` hook if the above lifecycle management proves confusing.\n   *\n   * ```jsx\n   * useOnChange(x, () => {})\n   * ```\n   *\n   * @param subscriber - A function that receives the latest value.\n   * @returns A function that, when called, will cancel this subscription.\n   *\n   * @deprecated\n   */\n  onChange(subscription) {\n    return this.on(\"change\", subscription);\n  }\n  on(eventName, callback) {\n    if (!this.events[eventName]) {\n      this.events[eventName] = new SubscriptionManager();\n    }\n    return this.events[eventName].add(callback);\n  }\n  clearListeners() {\n    for (const eventManagers in this.events) {\n      this.events[eventManagers].clear();\n    }\n  }\n  /**\n   * Attaches a passive effect to the `MotionValue`.\n   *\n   * @internal\n   */\n  attach(passiveEffect) {\n    this.passiveEffect = passiveEffect;\n  }\n  /**\n   * Sets the state of the `MotionValue`.\n   *\n   * @remarks\n   *\n   * ```jsx\n   * const x = useMotionValue(0)\n   * x.set(10)\n   * ```\n   *\n   * @param latest - Latest value to set.\n   * @param render - Whether to notify render subscribers. Defaults to `true`\n   *\n   * @public\n   */\n  set(v) {\n    let render = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    if (!render || !this.passiveEffect) {\n      this.updateAndNotify(v, render);\n    } else {\n      this.passiveEffect(v, this.updateAndNotify);\n    }\n  }\n  setWithVelocity(prev, current, delta) {\n    this.set(current);\n    this.prev = prev;\n    this.timeDelta = delta;\n  }\n  /**\n   * Returns the latest state of `MotionValue`\n   *\n   * @returns - The latest state of `MotionValue`\n   *\n   * @public\n   */\n  get() {\n    return this.current;\n  }\n  /**\n   * @public\n   */\n  getPrevious() {\n    return this.prev;\n  }\n  /**\n   * Returns the latest velocity of `MotionValue`\n   *\n   * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.\n   *\n   * @public\n   */\n  getVelocity() {\n    // This could be isFloat(this.prev) && isFloat(this.current), but that would be wasteful\n    return this.canTrackVelocity ?\n    // These casts could be avoided if parseFloat would be typed better\n    velocityPerSecond(parseFloat(this.current) - parseFloat(this.prev), this.timeDelta) : 0;\n  }\n  /**\n   * Registers a new animation to control this `MotionValue`. Only one\n   * animation can drive a `MotionValue` at one time.\n   *\n   * ```jsx\n   * value.start()\n   * ```\n   *\n   * @param animation - A function that starts the provided animation\n   *\n   * @internal\n   */\n  start(animation) {\n    this.stop();\n    return new Promise(resolve => {\n      this.hasAnimated = true;\n      this.stopAnimation = animation(resolve);\n      if (this.events.animationStart) {\n        this.events.animationStart.notify();\n      }\n    }).then(() => {\n      if (this.events.animationComplete) {\n        this.events.animationComplete.notify();\n      }\n      this.clearAnimation();\n    });\n  }\n  /**\n   * Stop the currently active animation.\n   *\n   * @public\n   */\n  stop() {\n    if (this.stopAnimation) {\n      this.stopAnimation();\n      if (this.events.animationCancel) {\n        this.events.animationCancel.notify();\n      }\n    }\n    this.clearAnimation();\n  }\n  /**\n   * Returns `true` if this value is currently animating.\n   *\n   * @public\n   */\n  isAnimating() {\n    return !!this.stopAnimation;\n  }\n  clearAnimation() {\n    this.stopAnimation = null;\n  }\n  /**\n   * Destroy and clean up subscribers to this `MotionValue`.\n   *\n   * The `MotionValue` hooks like `useMotionValue` and `useTransform` automatically\n   * handle the lifecycle of the returned `MotionValue`, so this method is only necessary if you've manually\n   * created a `MotionValue` via the `motionValue` function.\n   *\n   * @public\n   */\n  destroy() {\n    this.clearListeners();\n    this.stop();\n  }\n}\nfunction motionValue(init, options) {\n  return new MotionValue(init, options);\n}\nexport { MotionValue, motionValue };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}