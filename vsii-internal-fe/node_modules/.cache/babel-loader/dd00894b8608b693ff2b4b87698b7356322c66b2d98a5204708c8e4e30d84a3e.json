{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/extended/Form/Select.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\nimport { isEmpty } from 'lodash';\n\n// material-ui\nimport { FormHelperText, MenuItem, Select as MuiSelect, styled, Typography } from '@mui/material';\n\n// react-hook-form\nimport { Controller, useFormContext } from 'react-hook-form';\n\n// project imports\nimport Label from './Label';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SelectWrapper = styled('div')({\n  position: 'relative',\n  width: '100%'\n});\n_c = SelectWrapper;\nconst Select = props => {\n  _s();\n  const {\n    name,\n    label,\n    handleChange,\n    handleChangeFullOption,\n    selects,\n    disabled,\n    isMultipleLanguage,\n    required,\n    isControl = true,\n    valueSelect,\n    inRow,\n    selectWidth,\n    sx,\n    placeholder,\n    ...other\n  } = props;\n  const methods = useFormContext();\n\n  // Events\n  const handleChangeSelect = event => {\n    handleChange && handleChange(event);\n  };\n  return isControl ? /*#__PURE__*/_jsxDEV(Controller, {\n    name: name,\n    control: methods.control,\n    render: ({\n      field: {\n        value,\n        ref,\n        onChange,\n        ...field\n      },\n      fieldState: {\n        error\n      }\n    }) => /*#__PURE__*/_jsxDEV(SelectWrapper, {\n      sx: inRow ? {\n        display: 'flex',\n        flexDirection: 'row',\n        alignItems: 'center',\n        gap: 5\n      } : undefined,\n      children: [/*#__PURE__*/_jsxDEV(Label, {\n        name: name,\n        label: label,\n        required: required\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(MuiSelectStyle, {\n        ...field,\n        ...other,\n        disabled: disabled,\n        displayEmpty: true,\n        size: \"small\",\n        onChange: event => {\n          handleChangeSelect(event);\n          onChange(event.target.value);\n        },\n        error: !!error,\n        fullWidth: true,\n        value: value,\n        MenuProps: MenuProps,\n        renderValue: isEmpty(value) && placeholder ? () => /*#__PURE__*/_jsxDEV(Typography, {\n          children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: placeholder\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 39\n        }, this) : undefined,\n        ref: ref,\n        sx: selectWidth ? {\n          width: selectWidth,\n          ...sx\n        } : sx,\n        children: selects === null || selects === void 0 ? void 0 : selects.map((option, key) => /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: option.value,\n          disabled: option === null || option === void 0 ? void 0 : option.disabled,\n          onClick: () => handleChangeFullOption && handleChangeFullOption(option),\n          children: isMultipleLanguage || !option.value ? /*#__PURE__*/_jsxDEV(FormattedMessage, {\n            id: option.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 72\n          }, this) : option.label\n        }, key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 29\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n        sx: {\n          color: '#f44336'\n        },\n        children: error && /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: error.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 73\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 9\n  }, this) : /*#__PURE__*/_jsxDEV(SelectWrapper, {\n    children: [/*#__PURE__*/_jsxDEV(Label, {\n      name: name,\n      label: label,\n      required: required\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(MuiSelectStyle, {\n      ...other,\n      disabled: disabled,\n      displayEmpty: true,\n      size: \"small\",\n      onChange: handleChangeSelect,\n      fullWidth: true,\n      value: valueSelect,\n      MenuProps: MenuProps,\n      sx: sx,\n      renderValue: isEmpty(valueSelect) && placeholder ? () => /*#__PURE__*/_jsxDEV(Typography, {\n        children: /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: placeholder\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 35\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 31\n      }, this) : undefined,\n      children: selects === null || selects === void 0 ? void 0 : selects.map((option, key) => /*#__PURE__*/_jsxDEV(MenuItem, {\n        value: option.value,\n        disabled: option === null || option === void 0 ? void 0 : option.disabled,\n        onClick: () => handleChangeFullOption && handleChangeFullOption(option),\n        children: isMultipleLanguage || !option.value ? /*#__PURE__*/_jsxDEV(FormattedMessage, {\n          id: option.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 64\n        }, this) : option.label\n      }, key, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 9\n  }, this);\n};\n_s(Select, \"u7sAMcQCpiWJQxXuJ6yWLOYZ4cg=\", false, function () {\n  return [useFormContext];\n});\n_c2 = Select;\nconst MenuProps = {\n  PaperProps: {\n    style: {\n      maxHeight: 250\n    }\n  }\n};\nconst MuiSelectStyle = styled(MuiSelect)({});\n_c3 = MuiSelectStyle;\nexport default Select;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"SelectWrapper\");\n$RefreshReg$(_c2, \"Select\");\n$RefreshReg$(_c3, \"MuiSelectStyle\");", "map": {"version": 3, "names": ["React", "FormattedMessage", "isEmpty", "FormHelperText", "MenuItem", "Select", "MuiSelect", "styled", "Typography", "Controller", "useFormContext", "Label", "jsxDEV", "_jsxDEV", "SelectWrapper", "position", "width", "_c", "props", "_s", "name", "label", "handleChange", "handleChangeFullOption", "selects", "disabled", "isMultipleLanguage", "required", "isControl", "valueSelect", "inRow", "selectWidth", "sx", "placeholder", "other", "methods", "handleChangeSelect", "event", "control", "render", "field", "value", "ref", "onChange", "fieldState", "error", "display", "flexDirection", "alignItems", "gap", "undefined", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "MuiSelectStyle", "displayEmpty", "size", "target", "fullWidth", "MenuProps", "renderValue", "id", "map", "option", "key", "onClick", "color", "message", "_c2", "PaperProps", "style", "maxHeight", "_c3", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/VSII/InstantView/3.SourceCode/vsii-internal-fe/src/components/extended/Form/Select.tsx"], "sourcesContent": ["import React, { ReactNode } from 'react';\nimport { FormattedMessage } from 'react-intl';\nimport { isEmpty } from 'lodash';\n\n// material-ui\nimport { FormHelperText, MenuItem, Select as MuiSelect, SelectChangeEvent, SelectProps, styled, SxProps, Typography } from '@mui/material';\n\n// react-hook-form\nimport { Controller, useFormContext } from 'react-hook-form';\n\n// project imports\nimport Label from './Label';\nimport { IOption } from 'types';\n\ninterface ISelectProps {\n    name: string;\n    label?: string | ReactNode;\n    disabled?: boolean;\n    handleChange?: (e: SelectChangeEvent<unknown>) => void;\n    selects: IOption[];\n    other?: SelectProps;\n    handleChangeFullOption?: (option: IOption) => void;\n    isMultipleLanguage?: boolean;\n    required?: boolean;\n    isControl?: boolean;\n    valueSelect?: string;\n    defaultValue?: string;\n    inRow?: boolean;\n    selectWidth?: number;\n    sx?: SxProps;\n    placeholder?: string;\n}\n\nconst SelectWrapper = styled('div')({\n    position: 'relative',\n    width: '100%'\n});\n\nconst Select = (props: ISelectProps) => {\n    const {\n        name,\n        label,\n        handleChange,\n        handleChangeFullOption,\n        selects,\n        disabled,\n        isMultipleLanguage,\n        required,\n        isControl = true,\n        valueSelect,\n        inRow,\n        selectWidth,\n        sx,\n        placeholder,\n        ...other\n    } = props;\n    const methods = useFormContext();\n\n    // Events\n    const handleChangeSelect = (event: SelectChangeEvent<unknown>) => {\n        handleChange && handleChange(event);\n    };\n\n    return isControl ? (\n        <Controller\n            name={name}\n            control={methods.control}\n            render={({ field: { value, ref, onChange, ...field }, fieldState: { error } }) => (\n                <SelectWrapper\n                    sx={\n                        inRow\n                            ? {\n                                  display: 'flex',\n                                  flexDirection: 'row',\n                                  alignItems: 'center',\n                                  gap: 5\n                              }\n                            : undefined\n                    }\n                >\n                    <Label name={name} label={label} required={required} />\n                    <MuiSelectStyle\n                        {...field}\n                        {...other}\n                        disabled={disabled}\n                        displayEmpty\n                        size=\"small\"\n                        onChange={(event) => {\n                            handleChangeSelect(event);\n                            onChange(event.target.value);\n                        }}\n                        error={!!error}\n                        fullWidth\n                        value={value}\n                        MenuProps={MenuProps}\n                        renderValue={\n                            isEmpty(value) && placeholder\n                                ? () => (\n                                      <Typography>\n                                          <FormattedMessage id={placeholder} />\n                                      </Typography>\n                                  )\n                                : undefined\n                        }\n                        ref={ref}\n                        sx={\n                            selectWidth\n                                ? {\n                                      width: selectWidth,\n                                      ...sx\n                                  }\n                                : sx\n                        }\n                    >\n                        {selects?.map((option: IOption, key) => (\n                            <MenuItem\n                                key={key}\n                                value={option.value}\n                                disabled={option?.disabled}\n                                onClick={() => handleChangeFullOption && handleChangeFullOption(option)}\n                            >\n                                {isMultipleLanguage || !option.value ? <FormattedMessage id={option.label} /> : option.label}\n                            </MenuItem>\n                        ))}\n                    </MuiSelectStyle>\n                    <FormHelperText sx={{ color: '#f44336' }}>{error && <FormattedMessage id={error.message} />}</FormHelperText>\n                </SelectWrapper>\n            )}\n        />\n    ) : (\n        <SelectWrapper>\n            <Label name={name} label={label} required={required} />\n            <MuiSelectStyle\n                {...other}\n                disabled={disabled}\n                displayEmpty\n                size=\"small\"\n                onChange={handleChangeSelect}\n                fullWidth\n                value={valueSelect}\n                MenuProps={MenuProps}\n                sx={sx}\n                renderValue={\n                    isEmpty(valueSelect) && placeholder\n                        ? () => (\n                              <Typography>\n                                  <FormattedMessage id={placeholder} />\n                              </Typography>\n                          )\n                        : undefined\n                }\n            >\n                {selects?.map((option: IOption, key) => (\n                    <MenuItem\n                        key={key}\n                        value={option.value}\n                        disabled={option?.disabled}\n                        onClick={() => handleChangeFullOption && handleChangeFullOption(option)}\n                    >\n                        {isMultipleLanguage || !option.value ? <FormattedMessage id={option.label} /> : option.label}\n                    </MenuItem>\n                ))}\n            </MuiSelectStyle>\n        </SelectWrapper>\n    );\n};\n\nconst MenuProps = {\n    PaperProps: {\n        style: {\n            maxHeight: 250\n        }\n    }\n};\n\nconst MuiSelectStyle = styled(MuiSelect)({});\n\nexport default Select;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAqB,OAAO;AACxC,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,OAAO,QAAQ,QAAQ;;AAEhC;AACA,SAASC,cAAc,EAAEC,QAAQ,EAAEC,MAAM,IAAIC,SAAS,EAAkCC,MAAM,EAAWC,UAAU,QAAQ,eAAe;;AAE1I;AACA,SAASC,UAAU,EAAEC,cAAc,QAAQ,iBAAiB;;AAE5D;AACA,OAAOC,KAAK,MAAM,SAAS;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsB5B,MAAMC,aAAa,GAAGP,MAAM,CAAC,KAAK,CAAC,CAAC;EAChCQ,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE;AACX,CAAC,CAAC;AAACC,EAAA,GAHGH,aAAa;AAKnB,MAAMT,MAAM,GAAIa,KAAmB,IAAK;EAAAC,EAAA;EACpC,MAAM;IACFC,IAAI;IACJC,KAAK;IACLC,YAAY;IACZC,sBAAsB;IACtBC,OAAO;IACPC,QAAQ;IACRC,kBAAkB;IAClBC,QAAQ;IACRC,SAAS,GAAG,IAAI;IAChBC,WAAW;IACXC,KAAK;IACLC,WAAW;IACXC,EAAE;IACFC,WAAW;IACX,GAAGC;EACP,CAAC,GAAGhB,KAAK;EACT,MAAMiB,OAAO,GAAGzB,cAAc,CAAC,CAAC;;EAEhC;EACA,MAAM0B,kBAAkB,GAAIC,KAAiC,IAAK;IAC9Df,YAAY,IAAIA,YAAY,CAACe,KAAK,CAAC;EACvC,CAAC;EAED,OAAOT,SAAS,gBACZf,OAAA,CAACJ,UAAU;IACPW,IAAI,EAAEA,IAAK;IACXkB,OAAO,EAAEH,OAAO,CAACG,OAAQ;IACzBC,MAAM,EAAEA,CAAC;MAAEC,KAAK,EAAE;QAAEC,KAAK;QAAEC,GAAG;QAAEC,QAAQ;QAAE,GAAGH;MAAM,CAAC;MAAEI,UAAU,EAAE;QAAEC;MAAM;IAAE,CAAC,kBACzEhC,OAAA,CAACC,aAAa;MACVkB,EAAE,EACEF,KAAK,GACC;QACIgB,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,KAAK;QACpBC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACT,CAAC,GACDC,SACT;MAAAC,QAAA,gBAEDtC,OAAA,CAACF,KAAK;QAACS,IAAI,EAAEA,IAAK;QAACC,KAAK,EAAEA,KAAM;QAACM,QAAQ,EAAEA;MAAS;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvD1C,OAAA,CAAC2C,cAAc;QAAA,GACPhB,KAAK;QAAA,GACLN,KAAK;QACTT,QAAQ,EAAEA,QAAS;QACnBgC,YAAY;QACZC,IAAI,EAAC,OAAO;QACZf,QAAQ,EAAGN,KAAK,IAAK;UACjBD,kBAAkB,CAACC,KAAK,CAAC;UACzBM,QAAQ,CAACN,KAAK,CAACsB,MAAM,CAAClB,KAAK,CAAC;QAChC,CAAE;QACFI,KAAK,EAAE,CAAC,CAACA,KAAM;QACfe,SAAS;QACTnB,KAAK,EAAEA,KAAM;QACboB,SAAS,EAAEA,SAAU;QACrBC,WAAW,EACP5D,OAAO,CAACuC,KAAK,CAAC,IAAIR,WAAW,GACvB,mBACIpB,OAAA,CAACL,UAAU;UAAA2C,QAAA,eACPtC,OAAA,CAACZ,gBAAgB;YAAC8D,EAAE,EAAE9B;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACf,GACDL,SACT;QACDR,GAAG,EAAEA,GAAI;QACTV,EAAE,EACED,WAAW,GACL;UACIf,KAAK,EAAEe,WAAW;UAClB,GAAGC;QACP,CAAC,GACDA,EACT;QAAAmB,QAAA,EAEA3B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwC,GAAG,CAAC,CAACC,MAAe,EAAEC,GAAG,kBAC/BrD,OAAA,CAACT,QAAQ;UAELqC,KAAK,EAAEwB,MAAM,CAACxB,KAAM;UACpBhB,QAAQ,EAAEwC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAExC,QAAS;UAC3B0C,OAAO,EAAEA,CAAA,KAAM5C,sBAAsB,IAAIA,sBAAsB,CAAC0C,MAAM,CAAE;UAAAd,QAAA,EAEvEzB,kBAAkB,IAAI,CAACuC,MAAM,CAACxB,KAAK,gBAAG5B,OAAA,CAACZ,gBAAgB;YAAC8D,EAAE,EAAEE,MAAM,CAAC5C;UAAM;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAGU,MAAM,CAAC5C;QAAK,GALvF6C,GAAG;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMF,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC,eACjB1C,OAAA,CAACV,cAAc;QAAC6B,EAAE,EAAE;UAAEoC,KAAK,EAAE;QAAU,CAAE;QAAAjB,QAAA,EAAEN,KAAK,iBAAIhC,OAAA,CAACZ,gBAAgB;UAAC8D,EAAE,EAAElB,KAAK,CAACwB;QAAQ;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClG;EACjB;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC,gBAEF1C,OAAA,CAACC,aAAa;IAAAqC,QAAA,gBACVtC,OAAA,CAACF,KAAK;MAACS,IAAI,EAAEA,IAAK;MAACC,KAAK,EAAEA,KAAM;MAACM,QAAQ,EAAEA;IAAS;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACvD1C,OAAA,CAAC2C,cAAc;MAAA,GACPtB,KAAK;MACTT,QAAQ,EAAEA,QAAS;MACnBgC,YAAY;MACZC,IAAI,EAAC,OAAO;MACZf,QAAQ,EAAEP,kBAAmB;MAC7BwB,SAAS;MACTnB,KAAK,EAAEZ,WAAY;MACnBgC,SAAS,EAAEA,SAAU;MACrB7B,EAAE,EAAEA,EAAG;MACP8B,WAAW,EACP5D,OAAO,CAAC2B,WAAW,CAAC,IAAII,WAAW,GAC7B,mBACIpB,OAAA,CAACL,UAAU;QAAA2C,QAAA,eACPtC,OAAA,CAACZ,gBAAgB;UAAC8D,EAAE,EAAE9B;QAAY;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CACf,GACDL,SACT;MAAAC,QAAA,EAEA3B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwC,GAAG,CAAC,CAACC,MAAe,EAAEC,GAAG,kBAC/BrD,OAAA,CAACT,QAAQ;QAELqC,KAAK,EAAEwB,MAAM,CAACxB,KAAM;QACpBhB,QAAQ,EAAEwC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAExC,QAAS;QAC3B0C,OAAO,EAAEA,CAAA,KAAM5C,sBAAsB,IAAIA,sBAAsB,CAAC0C,MAAM,CAAE;QAAAd,QAAA,EAEvEzB,kBAAkB,IAAI,CAACuC,MAAM,CAACxB,KAAK,gBAAG5B,OAAA,CAACZ,gBAAgB;UAAC8D,EAAE,EAAEE,MAAM,CAAC5C;QAAM;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAAGU,MAAM,CAAC5C;MAAK,GALvF6C,GAAG;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAMF,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAClB;AACL,CAAC;AAACpC,EAAA,CA/HId,MAAM;EAAA,QAkBQK,cAAc;AAAA;AAAA4D,GAAA,GAlB5BjE,MAAM;AAiIZ,MAAMwD,SAAS,GAAG;EACdU,UAAU,EAAE;IACRC,KAAK,EAAE;MACHC,SAAS,EAAE;IACf;EACJ;AACJ,CAAC;AAED,MAAMjB,cAAc,GAAGjD,MAAM,CAACD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AAACoE,GAAA,GAAvClB,cAAc;AAEpB,eAAenD,MAAM;AAAC,IAAAY,EAAA,EAAAqD,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAA1D,EAAA;AAAA0D,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}