// API client and utilities
export { apiClient, handleApiError, createApiHeaders } from "./client";
export { API_ENDPOINTS } from "./apiEndpoints";

// Feature API exports
export { authApi } from "../features/auth/api";
export { companiesApi } from "../features/companies/api";
export { jobsApi } from "../features/jobs/api";

// Common API types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}
